@echo off
echo Setting up D: drive cache directories...
echo =========================================

REM Create all necessary cache directories on D: drive
echo Creating cache directories on D: drive...
if not exist "D:\gradle-cache" mkdir "D:\gradle-cache" && echo Created: D:\gradle-cache
if not exist "D:\android-build-cache" mkdir "D:\android-build-cache" && echo Created: D:\android-build-cache
if not exist "D:\metro-cache" mkdir "D:\metro-cache" && echo Created: D:\metro-cache
if not exist "D:\npm-cache" mkdir "D:\npm-cache" && echo Created: D:\npm-cache
if not exist "D:\temp" mkdir "D:\temp" && echo Created: D:\temp
if not exist "D:\expo-cache" mkdir "D:\expo-cache" && echo Created: D:\expo-cache
if not exist "D:\react-native-cache" mkdir "D:\react-native-cache" && echo Created: D:\react-native-cache

echo.
echo Setting environment variables for current session...
set GRADLE_USER_HOME=D:\gradle-cache
set TMPDIR=D:\temp
set TEMP=D:\temp
set TMP=D:\temp
set NPM_CONFIG_CACHE=D:\npm-cache
set NPM_CONFIG_TMP=D:\temp
set EXPO_CACHE_DIR=D:\expo-cache
set REACT_NATIVE_CACHE_DIR=D:\react-native-cache
set ANDROID_USER_HOME=D:\android-cache

echo.
echo Environment Variables Set:
echo GRADLE_USER_HOME=%GRADLE_USER_HOME%
echo TMPDIR=%TMPDIR%
echo TEMP=%TEMP%
echo TMP=%TMP%
echo NPM_CONFIG_CACHE=%NPM_CONFIG_CACHE%
echo EXPO_CACHE_DIR=%EXPO_CACHE_DIR%

echo.
echo =========================================
echo D: drive cache setup complete!
echo =========================================
echo.
echo Next Steps:
echo 1. Run: set-env.bat
echo 2. Run: npx expo start
echo.
echo All build cache will now go to D: drive!
