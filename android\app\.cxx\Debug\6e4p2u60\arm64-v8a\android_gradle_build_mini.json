{"buildFiles": ["D:\\IraChat\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\IraChat\\node_modules\\@react-native-community\\slider\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\IraChat\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\IraChat\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\IraChat\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\IraChat\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\IraChat\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\IraChat\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\IraChat\\android\\app\\.cxx\\Debug\\6e4p2u60\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\IraChat\\android\\app\\.cxx\\Debug\\6e4p2u60\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNCSlider::@4898bc4726ecf1751b6a": {"artifactName": "react_codegen_RNCSlider", "abi": "arm64-v8a", "output": "D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libreact_codegen_RNCSlider.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"artifactName": "react_codegen_rngesturehandler_codegen", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": ["D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libreact_codegen_RNCSlider.so", "D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"artifactName": "react_codegen_rnsvg", "abi": "arm64-v8a", "output": "D:\\IraChat\\android\\app\\build\\intermediates\\cxx\\Debug\\6e4p2u60\\obj\\arm64-v8a\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\reactnative\\libs\\android.arm64-v8a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\prefab\\modules\\jsi\\libs\\android.arm64-v8a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e80cc6deab05b24bdfe1060903f43f89\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.arm64-v8a\\libfbjni.so"]}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "arm64-v8a", "runtimeFiles": []}}}