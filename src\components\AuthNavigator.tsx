import React from "react";

import { navigationService } from "../services/navigationService";

/**
 * 🔐 IRACHAT AUTHENTICATION MODEL:
 * - NO LOGIN SYSTEM: Users remain signed in until they uninstall the app or delete their account
 * - PRIVACY LOCK: Instead of logout, users can lock the app with PIN/password/biometric
 * - REGISTRATION ONLY: New users go through registration flow once
 * - PERSISTENT SESSION: Authentication state persists across app launches
 *
 * 📱 EXPLICIT TAB NAVIGATION:
 * - CHATS TAB: /(tabs)/index - Main chat list (default tab)
 * - GROUPS TAB: /(tabs)/groups - Group chat management
 * - BUSINESS TAB: /(tabs)/business - Facebook Marketplace style business posts
 * - CALLS TAB: /(tabs)/calls - Call history and contacts
 * - STORIES TAB: /(tabs)/updates - Video stories/updates feed
 */

interface AuthNavigatorProps {
  children: React.ReactNode;
}

export const AuthNavigator: React.FC<AuthNavigatorProps> = ({ children }) => {
  // 🚫 COMPLETELY DISABLED: This component has been disabled to prevent navigation conflicts
  // The main AuthNavigator in src/navigation/AuthNavigator.tsx now handles all auth navigation
  console.log('🔐 AuthNavigator (components): COMPLETELY DISABLED - using consolidated navigation system');

  // Simply return children without any auth logic to prevent conflicts
  return <>{children}</>;
};

/**
 * 📱 TAB NAVIGATION HELPERS
 * Explicit navigation methods for all five main tabs
 */
export const TabNavigationHelpers = {
  /**
   * Navigate to Chats tab (default/home tab)
   */
  navigateToChats: () => {
    console.log('🔄 Navigating to Chats tab');
    try {
      navigationService.navigate('/(tabs)/index');
      console.log('✅ Chats tab navigation successful');
    } catch (error) {
      console.error('❌ Chats tab navigation failed:', error);
    }
  },

  /**
   * Navigate to Groups tab
   */
  navigateToGroups: () => {
    console.log('🔄 Navigating to Groups tab');
    try {
      navigationService.navigate('/(tabs)/groups');
      console.log('✅ Groups tab navigation successful');
    } catch (error) {
      console.error('❌ Groups tab navigation failed:', error);
    }
  },

  /**
   * Navigate to Business tab (Facebook Marketplace style)
   */
  navigateToBusiness: () => {
    console.log('🔄 Navigating to Business tab');
    try {
      navigationService.navigate('/(tabs)/business');
      console.log('✅ Business tab navigation successful');
    } catch (error) {
      console.error('❌ Business tab navigation failed:', error);
    }
  },

  /**
   * Navigate to Calls tab
   */
  navigateToCalls: () => {
    console.log('🔄 Navigating to Calls tab');
    try {
      navigationService.navigate('/(tabs)/calls');
      console.log('✅ Calls tab navigation successful');
    } catch (error) {
      console.error('❌ Calls tab navigation failed:', error);
    }
  },

  /**
   * Navigate to Stories/Updates tab
   */
  navigateToStories: () => {
    console.log('🔄 Navigating to Stories tab');
    try {
      navigationService.navigate('/(tabs)/updates');
      console.log('✅ Stories tab navigation successful');
    } catch (error) {
      console.error('❌ Stories tab navigation failed:', error);
    }
  },

  /**
   * Get current active tab from segments
   */
  getCurrentTab: (segments: string[]): string => {
    const tabSegment = segments[segments.length - 1];
    switch (tabSegment) {
      case 'index':
        return 'chats';
      case 'groups':
        return 'groups';
      case 'business':
        return 'business';
      case 'calls':
        return 'calls';
      case 'updates':
        return 'stories';
      default:
        return 'chats'; // Default to chats
    }
  },

  /**
   * Check if current route is a valid tab
   */
  isValidTab: (segments: string[]): boolean => {
    const validTabs = ['index', 'groups', 'business', 'calls', 'updates'];
    return segments.some(segment => validTabs.includes(segment));
  },

  /**
   * Navigate to specific tab by name
   */
  navigateToTab: (tabName: 'chats' | 'groups' | 'business' | 'calls' | 'stories') => {
    console.log(`🔄 Navigating to ${tabName} tab`);
    switch (tabName) {
      case 'chats':
        TabNavigationHelpers.navigateToChats();
        break;
      case 'groups':
        TabNavigationHelpers.navigateToGroups();
        break;
      case 'business':
        TabNavigationHelpers.navigateToBusiness();
        break;
      case 'calls':
        TabNavigationHelpers.navigateToCalls();
        break;
      case 'stories':
        TabNavigationHelpers.navigateToStories();
        break;
      
      default:
        console.warn(`⚠️ Unknown tab: ${tabName}, defaulting to chats`);
        TabNavigationHelpers.navigateToChats();
    }
  }
};

export { AuthNavigator as default };
