import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  Modal,
  Alert,
  TextInput,
  Image,
  Share,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  BackHandler,
  TouchableWithoutFeedback,
  Dimensions,
  Linking,
  ToastAndroid,
  Animated,
} from 'react-native';

// Helper function to format video duration properly
const formatVideoDuration = (duration: number): string => {
  if (!duration || duration <= 0) return '0:00';

  // Convert milliseconds to seconds if the number is too large (likely milliseconds)
  const seconds = duration > 1000 ? Math.floor(duration / 1000) : Math.floor(duration);

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Helper function to show styled alerts with border radius
const showStyledAlert = (title: string, message: string, buttons: any[], options: any = {}) => {
  Alert.alert(title, message, buttons, {
    cancelable: true,
    userInterfaceStyle: 'dark',
    ...options
  });
};

import * as ImagePicker from 'expo-image-picker';
import { VideoView, useVideoPlayer } from 'expo-video';
import { Ionicons } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFocusEffect } from 'expo-router';
import { RootState } from '../../src/redux/store';
import { BusinessPost, BusinessProfile, ProductCategory, BusinessType } from '../../src/types/Business';
import { businessService } from '../../src/services/businessService';
import { postStorageService, PendingPost } from '../../src/services/postStorageService';
import mediaService from '../../src/services/mediaService';
import { navigationService } from '../../src/services/navigationService';
import { BusinessContactActions } from '../../src/components/business/BusinessContactActions';
import { BusinessHeader } from '../../src/components/business/BusinessHeader';
import { BusinessCategoryFilter } from '../../src/components/business/BusinessCategoryFilter';
import { BusinessProfilePage } from '../../src/components/business/BusinessProfilePage';
import { PhotoCropModal } from '../../src/components/business/PhotoCropModal';

import { SyncIndicator } from '../../src/components/business/SyncIndicator';
import { EnhancedMediaViewer } from '../../src/components/business/EnhancedMediaViewer';
import { OptimizedProductDetailModal } from '../../src/components/business/OptimizedProductDetailModal';
import { PriceAdjustmentModal } from '../../src/components/business/PriceAdjustmentModal';
import { BusinessSearch, SearchFilters } from '../../src/components/business/BusinessSearch';
import { parsePrice, formatPriceWithCurrency, getCurrencySymbol } from '../../src/utils/priceUtils';
import { useTheme } from '../../src/contexts/ThemeContext';
// Media editing components (available for future use)
// import { FreeFormImageCropper } from '../../src/components/media/FreeFormImageCropper';
// import { EnhancedVideoTrimmer } from '../../src/components/media/EnhancedVideoTrimmer';
// import { DraggableTextOverlay } from '../../src/components/media/DraggableTextOverlay';

// COLORS will be replaced by theme context

// Business Registration Modal Component
interface BusinessRegistrationModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (profile: BusinessProfile) => void;
  currentUserId: string;
  modalStyles: any;
  COLORS: any;
  editingBusiness?: BusinessProfile; // For editing existing business
  // State setters for updating business profiles throughout the app
  selectedBusinessProfile?: BusinessProfile | null;
  setSelectedBusinessProfile?: (profile: BusinessProfile | null) => void;
  activeBusinessProfile?: BusinessProfile | null;
  setActiveBusinessProfile?: (profile: BusinessProfile | null) => void;
  setUserBusinessProfiles?: (updater: (prev: BusinessProfile[]) => BusinessProfile[]) => void;
}

const BusinessRegistrationModal: React.FC<BusinessRegistrationModalProps> = React.memo(({
  visible,
  onClose,
  onSuccess,
  currentUserId,
  modalStyles,
  COLORS,
  editingBusiness,
  selectedBusinessProfile,
  setSelectedBusinessProfile,
  activeBusinessProfile,
  setActiveBusinessProfile,
  setUserBusinessProfiles,
}) => {
  // Simplified registration - direct to business form
  const [isLoading, setIsLoading] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  const [inputFocused, setInputFocused] = useState(false);
  const [businessForm, setBusinessForm] = useState({
    businessName: '',
    businessType: 'retail' as BusinessType,
    description: '',
    email: '',
    phone: '',
    secondaryPhone: '',
    whatsappNumber: '',
    alternativeEmail: '',
    address: '',
    city: '',
    district: '',
    website: '',
    facebook: '',
    instagram: '',
    twitter: '',
    linkedin: '',
    logo: '', // Business logo
    profilePhoto: '', // Business profile photo
    coverPhoto: '', // Business cover photo
  });

  // Refs for input focus management
  const emailInputRef = useRef<TextInput>(null);
  const phoneInputRef = useRef<TextInput>(null);
  const secondaryPhoneInputRef = useRef<TextInput>(null);
  const whatsappInputRef = useRef<TextInput>(null);
  const alternativeEmailInputRef = useRef<TextInput>(null);
  const descriptionInputRef = useRef<TextInput>(null);
  const addressInputRef = useRef<TextInput>(null);
  const cityInputRef = useRef<TextInput>(null);
  const websiteInputRef = useRef<TextInput>(null);
  const facebookInputRef = useRef<TextInput>(null);
  const instagramInputRef = useRef<TextInput>(null);
  const twitterInputRef = useRef<TextInput>(null);
  const linkedinInputRef = useRef<TextInput>(null);



  // Handle keyboard events specifically for this modal
  useEffect(() => {
    if (!visible) return;

    let keyboardVisibleRef = false;

    // Set global flag to prevent tab layout interference
    (global as any).businessModalOpen = true;

    const keyboardDidShow = () => {
      keyboardVisibleRef = true;
      setIsKeyboardVisible(true);
    };

    const keyboardDidHide = () => {
      keyboardVisibleRef = false;
      setIsKeyboardVisible(false);
    };

    // Handle Android back button to prevent modal from closing unexpectedly
    const handleBackPress = () => {
      if (keyboardVisibleRef) {
        // If keyboard is visible, just dismiss it instead of closing modal
        Keyboard.dismiss();
        return true;
      }
      // Allow normal back behavior (close modal)
      return false;
    };

    const keyboardShowListener = Keyboard.addListener('keyboardDidShow', keyboardDidShow);
    const keyboardHideListener = Keyboard.addListener('keyboardDidHide', keyboardDidHide);
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      // Clear global flag
      (global as any).businessModalOpen = false;
      keyboardShowListener?.remove();
      keyboardHideListener?.remove();
      backHandler?.remove();
    };
  }, [visible]); // Remove isKeyboardVisible from dependencies

  // Initialize form when modal opens
  useEffect(() => {
    if (visible && editingBusiness) {
      // Pre-fill form with existing business data for editing
      setBusinessForm({
        businessName: editingBusiness.businessName,
        businessType: editingBusiness.businessType,
        description: editingBusiness.description,
        email: editingBusiness.contactInfo.email,
        phone: editingBusiness.contactInfo.primaryPhone,
        secondaryPhone: editingBusiness.contactInfo.secondaryPhone || '',
        whatsappNumber: editingBusiness.contactInfo.whatsappNumber || '',
        alternativeEmail: editingBusiness.contactInfo.alternativeEmail || '',
        address: editingBusiness.location.address,
        city: editingBusiness.location.city,
        district: editingBusiness.location.district,
        website: editingBusiness.website || '',
        facebook: editingBusiness.socialMedia?.facebook || '',
        instagram: editingBusiness.socialMedia?.instagram || '',
        twitter: editingBusiness.socialMedia?.twitter || '',
        linkedin: editingBusiness.socialMedia?.linkedin || '',
        logo: editingBusiness.logo || '',
        profilePhoto: editingBusiness.profilePhoto || '',
        coverPhoto: editingBusiness.coverPhoto || '',
      });
    } else if (visible && !editingBusiness) {
      // Reset form for new business registration
      setBusinessForm({
        businessName: '',
        businessType: 'retail' as BusinessType,
        description: '',
        email: '',
        phone: '',
        secondaryPhone: '',
        whatsappNumber: '',
        alternativeEmail: '',
        address: '',
        city: '',
        district: '',
        website: '',
        facebook: '',
        instagram: '',
        twitter: '',
        linkedin: '',
        logo: '',
        profilePhoto: '',
        coverPhoto: '',
      });
    }

    if (!visible) {
      setIsKeyboardVisible(false);
      setInputFocused(false);
      Keyboard.dismiss();
    }
  }, [visible, editingBusiness]);

  const handleRegistration = async () => {
    if (!businessForm.businessName || !businessForm.email || !businessForm.phone) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setIsLoading(true);
    const isEditing = !!editingBusiness;
    console.log(isEditing ? '🏢 Starting business profile update...' : '🏢 Starting business profile creation...');
    try {
      // Upload images first if they are local URIs
      let logoUrl = businessForm.logo;
      let profilePhotoUrl = businessForm.profilePhoto;
      let coverPhotoUrl = businessForm.coverPhoto;

      // Upload logo if it's a local URI
      if (businessForm.logo && businessForm.logo.startsWith('file://')) {
        console.log('📤 Uploading business logo...');
        try {
          const logoResult = await mediaService.uploadMedia(
            businessForm.logo,
            `business_logos/${currentUserId}_${Date.now()}.jpg`,
            'business_logos'
          );
          logoUrl = logoResult.url || businessForm.logo;
          console.log('✅ Logo uploaded successfully:', logoUrl);
        } catch (error) {
          console.error('❌ Failed to upload logo:', error);
          // Continue with local URI if upload fails
        }
      }

      // Upload profile photo if it's a local URI
      if (businessForm.profilePhoto && businessForm.profilePhoto.startsWith('file://')) {
        console.log('📤 Uploading business profile photo...');
        try {
          const profileResult = await mediaService.uploadMedia(
            businessForm.profilePhoto,
            `business_profiles/${currentUserId}_${Date.now()}.jpg`,
            'business_profiles'
          );
          profilePhotoUrl = profileResult.url || businessForm.profilePhoto;
          console.log('✅ Profile photo uploaded successfully:', profilePhotoUrl);
        } catch (error) {
          console.error('❌ Failed to upload profile photo:', error);
          // Continue with local URI if upload fails
        }
      }

      // Upload cover photo if it's a local URI
      if (businessForm.coverPhoto && businessForm.coverPhoto.startsWith('file://')) {
        console.log('📤 Uploading business cover photo...');
        try {
          const coverResult = await mediaService.uploadMedia(
            businessForm.coverPhoto,
            `business_covers/${currentUserId}_${Date.now()}.jpg`,
            'business_covers'
          );
          coverPhotoUrl = coverResult.url || businessForm.coverPhoto;
          console.log('✅ Cover photo uploaded successfully:', coverPhotoUrl);
        } catch (error) {
          console.error('❌ Failed to upload cover photo:', error);
          // Continue with local URI if upload fails
        }
      }

      const profileData = {
        userId: currentUserId,
        businessName: businessForm.businessName,
        businessType: businessForm.businessType,
        description: businessForm.description,
        logo: logoUrl,
        profilePhoto: profilePhotoUrl,
        coverPhoto: coverPhotoUrl,
        contactInfo: {
          email: businessForm.email,
          primaryPhone: businessForm.phone,
          secondaryPhone: businessForm.secondaryPhone,
          whatsappNumber: businessForm.whatsappNumber,
          alternativeEmail: businessForm.alternativeEmail,
        },
        location: {
          address: businessForm.address,
          city: businessForm.city,
          district: businessForm.district,
          region: 'Central',
          country: 'Uganda',
          coordinates: { latitude: 0.3476, longitude: 32.5825 }
        },
        website: businessForm.website,
        socialMedia: {
          facebook: businessForm.facebook,
          instagram: businessForm.instagram,
          twitter: businessForm.twitter,
          linkedin: businessForm.linkedin,
        },
        isVerified: false,
        verificationDocuments: [],
        totalPosts: 0,
        totalViews: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
        totalDownloads: 0,
        allowDirectMessages: true,
        allowPhoneCalls: true,
        businessHours: [
          { dayOfWeek: 1, isOpen: true, openTime: '08:00', closeTime: '18:00' },
          { dayOfWeek: 2, isOpen: true, openTime: '08:00', closeTime: '18:00' },
          { dayOfWeek: 3, isOpen: true, openTime: '08:00', closeTime: '18:00' },
          { dayOfWeek: 4, isOpen: true, openTime: '08:00', closeTime: '18:00' },
          { dayOfWeek: 5, isOpen: true, openTime: '08:00', closeTime: '18:00' },
          { dayOfWeek: 6, isOpen: true, openTime: '08:00', closeTime: '16:00' },
          { dayOfWeek: 0, isOpen: false },
        ],
      };

      console.log('🏢 Creating business profile with data:', {
        businessName: profileData.businessName,
        businessType: profileData.businessType,
        userId: profileData.userId
      });

      let result;
      if (isEditing && editingBusiness) {
        // Update existing business profile
        result = await businessService.updateBusinessProfile(editingBusiness.id, profileData);
      } else {
        // Create new business profile
        result = await businessService.createBusinessProfile(profileData);
      }

      console.log(`🏢 Business profile ${isEditing ? 'update' : 'creation'} result:`, {
        success: result.success,
        hasData: !!result.data,
        error: result.error
      });

      if (result.success && result.data) {
        console.log(`✅ Business profile ${isEditing ? 'updated' : 'created'} successfully:`, result.data.id);
        Alert.alert(
          'Success!',
          `Your business profile has been ${isEditing ? 'updated' : 'created'} successfully!`,
          [{
            text: 'Continue',
            onPress: () => {
              onSuccess(result.data!);

              if (isEditing && editingBusiness) {
                // EDITING: Update existing business profile
                // Update the selected business profile if it's the same one being edited
                if (selectedBusinessProfile?.id === editingBusiness.id && setSelectedBusinessProfile) {
                  setSelectedBusinessProfile(result.data!);
                }
                // Update the active business profile if it's the same one being edited
                if (activeBusinessProfile?.id === editingBusiness.id && setActiveBusinessProfile) {
                  setActiveBusinessProfile(result.data!);
                }
                // Update the business profiles list
                if (setUserBusinessProfiles) {
                  setUserBusinessProfiles(prev =>
                    prev.map(profile =>
                      profile.id === editingBusiness.id ? result.data! : profile
                    )
                  );
                }
              } else {
                // CREATING: Add new business profile to the list
                if (setUserBusinessProfiles) {
                  setUserBusinessProfiles(prev => [...prev, result.data!]);
                }
                // Set as active business if it's the first one or no active business
                if (setActiveBusinessProfile && !activeBusinessProfile) {
                  setActiveBusinessProfile(result.data!);
                }
              }
            }
          }]
        );
      } else {
        console.error(`❌ Business profile ${isEditing ? 'update' : 'creation'} failed:`, result.error);
        Alert.alert(
          `${isEditing ? 'Update' : 'Registration'} Failed`,
          result.error || `Failed to ${isEditing ? 'update' : 'create'} business profile`
        );
      }
    } catch (error) {
      console.error('❌ Business profile creation error:', error);
      Alert.alert('Registration Failed', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderBusinessForm = () => (
    <ScrollView
      style={modalStyles.content}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
      nestedScrollEnabled={true}
      contentContainerStyle={[modalStyles.contentContainer, { paddingBottom: 50 }]}
      scrollEventThrottle={16}
    >
      <Text style={modalStyles.sectionTitle}>Business Information</Text>

      <TextInput
        style={modalStyles.input}
        placeholder="Business Name *"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.businessName}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, businessName: text }))}
        returnKeyType="next"
        onSubmitEditing={() => emailInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
        autoCapitalize="words"
      />

      {/* Business Photos Section */}
      <Text style={modalStyles.sectionTitle}>Business Photos</Text>

      {/* Business Logo Upload */}
      <View style={modalStyles.photoSection}>
        <Text style={modalStyles.fieldLabel}>Business Logo</Text>
        <TouchableOpacity
          style={modalStyles.logoUploadButton}
          onPress={() => {
            Alert.alert(
              'Select Business Logo',
              'Choose how you want to add your business logo',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Camera',
                  onPress: async () => {
                    try {
                      const result = await ImagePicker.launchCameraAsync({
                        mediaTypes: ['images'],
                        allowsEditing: true,
                        aspect: [1, 1], // Square aspect ratio for logo
                        quality: 0.8,
                      });

                      if (!result.canceled && result.assets[0]) {
                        setBusinessForm(prev => ({ ...prev, logo: result.assets[0].uri }));
                      }
                    } catch (error) {
                      console.error('Error taking logo photo:', error);
                      Alert.alert('Error', 'Failed to take logo photo');
                    }
                  }
                },
                {
                  text: 'Gallery',
                  onPress: async () => {
                    try {
                      const result = await ImagePicker.launchImageLibraryAsync({
                        mediaTypes: ['images'],
                        allowsEditing: true,
                        aspect: [1, 1], // Square aspect ratio for logo
                        quality: 0.8,
                      });

                      if (!result.canceled && result.assets[0]) {
                        setBusinessForm(prev => ({ ...prev, logo: result.assets[0].uri }));
                      }
                    } catch (error) {
                      console.error('Error picking logo:', error);
                      Alert.alert('Error', 'Failed to select logo image');
                    }
                  }
                }
              ]
            );
          }}
        >
          {businessForm.logo ? (
            <Image source={{ uri: businessForm.logo }} style={modalStyles.logoPreview} />
          ) : (
            <View style={modalStyles.logoPlaceholder}>
              <Ionicons name="business" size={32} color={COLORS.textSecondary} />
              <Text style={modalStyles.logoPlaceholderText}>Add Logo</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>



      {/* Cover Photo Upload */}
      <View style={modalStyles.photoSection}>
        <Text style={modalStyles.fieldLabel}>Cover Photo</Text>
        <TouchableOpacity
          style={modalStyles.coverPhotoUploadButton}
          onPress={() => {
            Alert.alert(
              'Select Cover Photo',
              'Choose how you want to add your business cover photo',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Camera',
                  onPress: async () => {
                    try {
                      const result = await ImagePicker.launchCameraAsync({
                        mediaTypes: ['images'],
                        allowsEditing: true,
                        aspect: [16, 9], // Wide aspect ratio for cover
                        quality: 0.8,
                      });

                      if (!result.canceled && result.assets[0]) {
                        setBusinessForm(prev => ({ ...prev, coverPhoto: result.assets[0].uri }));
                      }
                    } catch (error) {
                      console.error('Error taking cover photo:', error);
                      Alert.alert('Error', 'Failed to take cover photo');
                    }
                  }
                },
                {
                  text: 'Gallery',
                  onPress: async () => {
                    try {
                      const result = await ImagePicker.launchImageLibraryAsync({
                        mediaTypes: ['images'],
                        allowsEditing: true,
                        aspect: [16, 9], // Wide aspect ratio for cover
                        quality: 0.8,
                      });

                      if (!result.canceled && result.assets[0]) {
                        setBusinessForm(prev => ({ ...prev, coverPhoto: result.assets[0].uri }));
                      }
                    } catch (error) {
                      console.error('Error picking cover photo:', error);
                      Alert.alert('Error', 'Failed to select cover photo');
                    }
                  }
                }
              ]
            );
          }}
        >
          {businessForm.coverPhoto ? (
            <Image source={{ uri: businessForm.coverPhoto }} style={modalStyles.coverPhotoPreview} />
          ) : (
            <View style={modalStyles.coverPhotoPlaceholder}>
              <Ionicons name="image" size={32} color={COLORS.textSecondary} />
              <Text style={modalStyles.logoPlaceholderText}>Add Cover Photo</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Business Type Selector */}
      <Text style={modalStyles.sectionTitle}>Business Type *</Text>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={{ marginBottom: 16 }}
        contentContainerStyle={{ paddingHorizontal: 4 }}
      >
        {[
          { id: 'retail', label: 'Retail Store', icon: 'storefront-outline' },
          { id: 'wholesale', label: 'Wholesale', icon: 'business-outline' },
          { id: 'restaurant', label: 'Restaurant', icon: 'restaurant-outline' },
          { id: 'fast_food', label: 'Fast Food', icon: 'fast-food-outline' },
          { id: 'cafe', label: 'Cafe', icon: 'cafe-outline' },
          { id: 'bar', label: 'Bar', icon: 'wine-outline' },
          { id: 'hotel', label: 'Hotel', icon: 'bed-outline' },
          { id: 'school', label: 'School', icon: 'school-outline' },
          { id: 'university', label: 'University', icon: 'library-outline' },
          { id: 'church', label: 'Church', icon: 'home-outline' },
          { id: 'mosque', label: 'Mosque', icon: 'home-outline' },
          { id: 'hospital', label: 'Hospital', icon: 'medical-outline' },
          { id: 'clinic', label: 'Clinic', icon: 'medical-outline' },
          { id: 'pharmacy', label: 'Pharmacy', icon: 'medical-outline' },
          { id: 'supermarket', label: 'Supermarket', icon: 'storefront-outline' },
          { id: 'convenience_store', label: 'Convenience Store', icon: 'storefront-outline' },
          { id: 'law_firm', label: 'Law Firm', icon: 'document-text-outline' },
          { id: 'accounting', label: 'Accounting', icon: 'calculator-outline' },
          { id: 'real_estate_agency', label: 'Real Estate', icon: 'business-outline' },
          { id: 'construction', label: 'Construction', icon: 'hammer-outline' },
          { id: 'manufacturing', label: 'Manufacturing', icon: 'settings-outline' },
          { id: 'software_company', label: 'Software Company', icon: 'code-outline' },
          { id: 'agriculture', label: 'Agriculture', icon: 'leaf-outline' },
          { id: 'beauty_salon', label: 'Beauty Salon', icon: 'cut-outline' },
          { id: 'barbershop', label: 'Barbershop', icon: 'cut-outline' },
          { id: 'fitness_center', label: 'Fitness Center', icon: 'fitness-outline' },
          { id: 'spa', label: 'Spa', icon: 'flower-outline' },
          { id: 'other', label: 'Other', icon: 'ellipsis-horizontal-outline' },
        ].map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              modalStyles.businessTypeOption,
              businessForm.businessType === type.id && modalStyles.businessTypeSelected
            ]}
            onPress={() => setBusinessForm(prev => ({ ...prev, businessType: type.id as BusinessType }))}
          >
            <Ionicons
              name={type.icon as any}
              size={20}
              color={businessForm.businessType === type.id ? COLORS.primary : COLORS.textSecondary}
            />
            <Text style={[
              modalStyles.businessTypeLabel,
              businessForm.businessType === type.id && modalStyles.businessTypeLabelSelected
            ]}>
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <TextInput
        ref={emailInputRef}
        style={modalStyles.input}
        placeholder="Email *"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.email}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, email: text }))}
        keyboardType="email-address"
        autoCapitalize="none"
        returnKeyType="next"
        onSubmitEditing={() => phoneInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={phoneInputRef}
        style={modalStyles.input}
        placeholder="Phone Number *"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.phone}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, phone: text }))}
        keyboardType="phone-pad"
        returnKeyType="next"
        onSubmitEditing={() => secondaryPhoneInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
      />

      <TextInput
        ref={secondaryPhoneInputRef}
        style={modalStyles.input}
        placeholder="Secondary Phone (optional)"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.secondaryPhone}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, secondaryPhone: text }))}
        keyboardType="phone-pad"
        returnKeyType="next"
        onSubmitEditing={() => whatsappInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={whatsappInputRef}
        style={modalStyles.input}
        placeholder="WhatsApp Number (optional)"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.whatsappNumber}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, whatsappNumber: text }))}
        keyboardType="phone-pad"
        returnKeyType="next"
        onSubmitEditing={() => alternativeEmailInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={alternativeEmailInputRef}
        style={modalStyles.input}
        placeholder="Alternative Email (optional)"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.alternativeEmail}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, alternativeEmail: text }))}
        keyboardType="email-address"
        autoCapitalize="none"
        returnKeyType="next"
        onSubmitEditing={() => descriptionInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={descriptionInputRef}
        style={[modalStyles.input, { height: 80 }]}
        placeholder="Business Description"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.description}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, description: text }))}
        multiline
        textAlignVertical="top"
        returnKeyType="next"
        onSubmitEditing={() => addressInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
      />

      <TextInput
        ref={addressInputRef}
        style={modalStyles.input}
        placeholder="Address"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.address}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, address: text }))}
        returnKeyType="next"
        onSubmitEditing={() => cityInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={cityInputRef}
        style={modalStyles.input}
        placeholder="City"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.city}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, city: text }))}
        returnKeyType="next"
        onSubmitEditing={() => websiteInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={websiteInputRef}
        style={modalStyles.input}
        placeholder="Website (optional)"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.website}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, website: text }))}
        keyboardType="url"
        autoCapitalize="none"
        returnKeyType="next"
        onSubmitEditing={() => facebookInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      {/* Social Media Section */}
      <View style={modalStyles.sectionHeader}>
        <Text style={modalStyles.socialSectionTitle}>Social Media (Optional)</Text>
      </View>

      <TextInput
        ref={facebookInputRef}
        style={modalStyles.input}
        placeholder="Facebook Page URL"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.facebook}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, facebook: text }))}
        keyboardType="url"
        autoCapitalize="none"
        returnKeyType="next"
        onSubmitEditing={() => instagramInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={instagramInputRef}
        style={modalStyles.input}
        placeholder="Instagram Profile URL"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.instagram}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, instagram: text }))}
        keyboardType="url"
        autoCapitalize="none"
        returnKeyType="next"
        onSubmitEditing={() => twitterInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={twitterInputRef}
        style={modalStyles.input}
        placeholder="Twitter Profile URL"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.twitter}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, twitter: text }))}
        keyboardType="url"
        autoCapitalize="none"
        returnKeyType="next"
        onSubmitEditing={() => linkedinInputRef.current?.focus()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TextInput
        ref={linkedinInputRef}
        style={modalStyles.input}
        placeholder="LinkedIn Profile URL"
        placeholderTextColor={COLORS.textSecondary}
        value={businessForm.linkedin}
        onChangeText={(text) => setBusinessForm(prev => ({ ...prev, linkedin: text }))}
        keyboardType="url"
        autoCapitalize="none"
        returnKeyType="done"
        onSubmitEditing={() => Keyboard.dismiss()}
        onFocus={() => setInputFocused(true)}
        onBlur={() => setInputFocused(false)}
        autoCorrect={false}
      />

      <TouchableOpacity
        style={[modalStyles.button, isLoading && { opacity: 0.6 }]}
        onPress={handleRegistration}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator size="small" color="#FFFFFF" />
        ) : (
          <Text style={modalStyles.buttonText}>
            {editingBusiness ? 'Update Business Profile' : 'Create Business Profile'}
          </Text>
        )}
      </TouchableOpacity>
    </ScrollView>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={() => {
        // Prevent closing if input is focused or keyboard is visible
        if (isKeyboardVisible || inputFocused) {
          Keyboard.dismiss();
        } else {
          onClose();
        }
      }}
      supportedOrientations={['portrait']}
      statusBarTranslucent={false}
    >
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
        enabled={Platform.OS === 'ios'}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.background }}>
          <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
            <View style={{ flex: 1 }}>
              <View style={modalStyles.header}>
                <TouchableOpacity
                  onPress={() => {
                    // Prevent closing if input is focused or keyboard is visible
                    if (isKeyboardVisible || inputFocused) {
                      Keyboard.dismiss();
                    } else {
                      onClose();
                    }
                  }}
                  hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
                >
                  <Ionicons name="close" size={24} color={COLORS.text} />
                </TouchableOpacity>
                <Text style={modalStyles.title}>
                  {editingBusiness ? 'Edit Business Profile' : 'Business Registration'}
                </Text>
                <View style={{ width: 24 }} />
              </View>
              {renderBusinessForm()}
            </View>
          </TouchableWithoutFeedback>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </Modal>
  );
});

// Add Business Post Modal Component
interface AddBusinessPostModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (post: BusinessPost) => void;
  businessProfile: BusinessProfile;
  modalStyles: any;
  COLORS: any;
}

// Media item interface
interface MediaItem {
  id: string;
  uri: string;
  type: 'photo' | 'video';
  duration?: number; // for videos in seconds
  thumbnail?: string; // for videos
  textOverlay?: string; // text overlay for photos and videos
  overlayPosition?: 'top' | 'center' | 'bottom'; // position of text overlay
  textColor?: string; // text color for overlay
  backgroundColor?: string; // background color for overlay
  textX?: number; // X position for draggable text (0-1 relative)
  textY?: number; // Y position for draggable text (0-1 relative)
  textBackgroundStyle?: 'none' | 'solid' | 'semi-transparent' | 'outline' | 'shadow';
  fontSize?: number; // font size for text overlay
  filter?: string; // photo filter
  emojis?: Array<{
    id: string;
    emoji: string;
    x: number;
    y: number;
    size: number;
  }>; // emoji stickers
}

const AddBusinessPostModal: React.FC<AddBusinessPostModalProps> = ({
  visible,
  onClose,
  onSuccess,
  businessProfile,
  modalStyles,
  COLORS,
}) => {
  const [isLoading, setIsLoading] = useState(false);




  const [postForm, setPostForm] = useState({
    title: '',
    description: '',
    category: 'other' as ProductCategory,
    price: '',
    currency: 'UGX',
    isNegotiable: false,
    availability: 'available' as 'available' | 'out_of_stock' | 'discontinued',
    statusTags: [] as string[],
    tags: '',
    address: '',
    city: '',
    district: '',
  });

  // Media state
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [showMediaViewer, setShowMediaViewer] = useState(false);
  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);
  const [showMediaPreview, setShowMediaPreview] = useState(false);
  const [selectedMediaForPreview, setSelectedMediaForPreview] = useState<MediaItem | null>(null);
  const [showMediaOptions, setShowMediaOptions] = useState(false);
  const [showPhotoCrop, setShowPhotoCrop] = useState(false);
  const [photoCropUri, setPhotoCropUri] = useState('');

  // Multi-media preview state removed - media added directly

  // Prevent multiple simultaneous picker calls
  const [isPickerActive, setIsPickerActive] = useState(false);

  // Media handling functions
  const requestPermissions = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant photo library permissions to add photos and videos.');
        return false;
      }
      return true;
    } catch (error) {
      console.error('❌ Error requesting permissions:', error);
      Alert.alert('Error', 'Failed to request permissions. Please try again.');
      return false;
    }
  };



  const validateMediaCount = (): boolean => {
    const maxMedia = 10; // Maximum 10 media items
    if (mediaItems.length >= maxMedia) {
      Alert.alert('Media Limit Reached', `You can only add up to ${maxMedia} photos and videos.`);
      return false;
    }
    return true;
  };

  const addMedia = async () => {
    if (!validateMediaCount()) return;

    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    setShowMediaOptions(true);
  };

  const openCamera = async (mediaType: 'photo' | 'video' = 'photo') => {
    // Prevent multiple simultaneous camera calls to avoid "Already resumed" crash
    if (isPickerActive) {
      console.log('🚫 Camera picker already active, ignoring call');
      return;
    }

    try {
      setIsPickerActive(true);
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera permissions to take photos and videos.');
        setIsPickerActive(false);
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: mediaType === 'photo' ? ['images'] : ['videos'],
        allowsEditing: true, 
        quality: 1.0, // Maximum quality - no compression
        videoQuality: 1.0, // Maximum video quality
        videoMaxDuration: 180, // 3 minutes
        exif: false, // Don't include EXIF data for privacy
      });

      if (!result.canceled && result.assets[0]) {
        await processMediaAsset(result.assets[0]);
      }
    } catch (error) {
      console.error('Camera error:', error);
      Alert.alert('Error', 'Failed to open camera. Please try again.');
    } finally {
      setIsPickerActive(false);
    }
  };

  const openGallery = async () => {
    // Prevent multiple simultaneous picker calls to avoid "Already resumed" crash
    if (isPickerActive) {
      console.log('🚫 Image picker already active, ignoring call');
      return;
    }

    try {
      setIsPickerActive(true);
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: false, // Disabled because allowsMultipleSelection is enabled
        quality: 1.0, // Maximum quality - no compression
        videoQuality: 1.0, // Maximum video quality
        videoMaxDuration: 180, // 3 minutes
        allowsMultipleSelection: true, // Allow multiple selection
        selectionLimit: Math.max(1, 10 - mediaItems.length), // Limit based on current media count
        exif: false, // Don't include EXIF data for privacy
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Create media items from all selected assets
        const newMediaItems: MediaItem[] = [];
        for (const asset of result.assets) {
          const isVideo = asset.type === 'video';

          const newMediaItem: MediaItem = {
            id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
            uri: asset.uri,
            type: isVideo ? 'video' : 'photo',
            duration: asset.duration || undefined,
            textOverlay: '',
            overlayPosition: 'bottom',
            textColor: '#FFFFFF',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
            textX: 0.5, // Center horizontally
            textY: 0.5, 
            textBackgroundStyle: 'semi-transparent',
          };

          newMediaItems.push(newMediaItem);
        }

        // Add media items directly without preview modal
        setMediaItems(prev => [...prev, ...newMediaItems]);
      }
    } catch (error) {
      console.error('Gallery error:', error);
      Alert.alert('Error', 'Failed to open gallery. Please try again.');
    } finally {
      setIsPickerActive(false);
    }
  };

  const processMediaAsset = async (asset: any) => {
    const isVideo = asset.type === 'video';

    // For videos longer than 3 minutes, automatically show trimming
    if (isVideo && asset.duration && asset.duration > 180) {
      Alert.alert(
        'Video Too Long',
        `This video is ${Math.round(asset.duration / 60)} minutes long. Videos must be 3 minutes or less. Please trim it to continue.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Trim Video',
            onPress: () => {
              // Create media item and immediately show trimmer
              const newMediaItem: MediaItem = {
                id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
                uri: asset.uri,
                type: 'video',
                duration: asset.duration,
                textOverlay: '',
                overlayPosition: 'bottom',
                textColor: '#FFFFFF',
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                textX: 0.5,
                textY: 0.5,
                textBackgroundStyle: 'semi-transparent',
                fontSize: 16,
              };

              // Add video directly to media items
              setMediaItems(prev => [...prev, newMediaItem]);
            }
          },
        ]
      );
    } else {
      createMediaItem(asset);
    }
  };

  const createMediaItem = (asset: any) => {
    const isVideo = asset.type === 'video';

    const newMediaItem: MediaItem = {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      uri: asset.uri,
      type: isVideo ? 'video' : 'photo',
      duration: asset.duration,
      textOverlay: '',
      overlayPosition: 'bottom',
      textColor: '#FFFFFF',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      textX: 0.5, // Center horizontally
      textY: 0.5, 
      textBackgroundStyle: 'semi-transparent',
    };

    // Show preview modal for text overlay and cropping
    setSelectedMediaForPreview(newMediaItem);
    setShowMediaPreview(true);
  };

  const removeMedia = (id: string) => {
    setMediaItems(prev => prev.filter(item => item.id !== id));
  };

  const reorderMedia = (fromIndex: number, toIndex: number) => {
    setMediaItems(prev => {
      const newItems = [...prev];
      const [removed] = newItems.splice(fromIndex, 1);
      newItems.splice(toIndex, 0, removed);
      return newItems;
    });
  };

  const openMediaViewer = (index: number) => {
    setSelectedMediaIndex(index);
    setShowMediaViewer(true);
  };

  const resetForm = () => {
    setPostForm({
      title: '',
      description: '',
      category: 'other' as ProductCategory,
      price: '',
      currency: 'UGX',
      isNegotiable: false,
      availability: 'available' as 'available' | 'out_of_stock' | 'discontinued',
      statusTags: [],
      tags: '',
      address: '',
      city: '',
      district: '',
    });
    setMediaItems([]);
    setShowMediaViewer(false);
    setSelectedMediaIndex(0);
    setShowMediaPreview(false);
    setSelectedMediaForPreview(null);
    // Multi-media preview state removed
  };

  // Reset form when modal closes
  useEffect(() => {
    if (!visible) {
      resetForm();
    }
  }, [visible]);

  const handleSubmit = async () => {
    if (!postForm.title || !postForm.description) {
      Alert.alert('Error', 'Please fill in title and description');
      return;
    }

    setIsLoading(true);
    try {
      // Convert media items to the format expected by BusinessPost
      const mediaData = mediaItems.map((item, index) => ({
        id: item.id,
        type: item.type === 'photo' ? 'image' as const : 'video' as const,
        url: item.uri, // This will be updated after upload
        thumbnailUrl: item.thumbnail,
        duration: item.duration,
        width: 800, // Default values, will be updated during upload
        height: 600,
        fileSize: 0, // Will be calculated during upload
        order: index,
      }));

      const postData = {
        businessId: businessProfile.id,
        businessName: businessProfile.businessName,
        businessLogo: businessProfile.logo, // Add business logo from profile
        businessType: businessProfile.businessType,
        isVerified: businessProfile.isVerified,
        title: postForm.title,
        description: postForm.description,
        media: mediaData,
        tags: postForm.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        category: postForm.category,
        price: postForm.price ? parseFloat(postForm.price) : undefined,
        currency: postForm.currency,
        isNegotiable: postForm.isNegotiable,
        availability: postForm.availability,
        status: 'available' as const, // Default status
        location: {
          address: postForm.address || businessProfile.location.address,
          city: postForm.city || businessProfile.location.city,
          district: postForm.district || businessProfile.location.district,
          region: businessProfile.location.region,
          country: businessProfile.location.country,
          coordinates: businessProfile.location.coordinates,
        },
        contact: {
          phone: businessProfile.contactInfo.primaryPhone,
          email: businessProfile.contactInfo.email,
        },
        views: 0,
        likes: [],
        comments: [],
        shares: 0,
        downloads: 0,
        isActive: true,
        isPinned: false,
        isPromoted: false,
      };

      console.log('📦 Creating business post with data:', {
        title: postData.title,
        businessName: postData.businessName,
        category: postData.category,
        price: postData.price
      });

      // Save post locally first (for offline support)
      const pendingPost = await postStorageService.savePostLocally(postData);

      console.log('📱 Post saved locally with temp ID:', pendingPost.tempId);

      // Show success message immediately
      Alert.alert('Success', 'Your post has been saved and will be published when you\'re online!');

      // Create a temporary BusinessPost for immediate display
      const tempBusinessPost: BusinessPost = {
        ...postData,
        id: pendingPost.tempId,
        createdAt: pendingPost.localCreatedAt,
        updatedAt: pendingPost.localCreatedAt,
        // Add sync indicator
        _syncStatus: pendingPost.syncStatus,
        _tempId: pendingPost.tempId,
      } as BusinessPost & { _syncStatus?: string; _tempId?: string };

      // Call onSuccess to close modal and refresh the list
      onSuccess(tempBusinessPost);

      // The post storage service will automatically attempt to sync when online

    } catch (error) {
      console.error('❌ Business post creation error:', error);
      Alert.alert('Error', 'Failed to save post. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={onClose}
      >
      <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.background }}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        >
          <View style={modalStyles.header}>
            <TouchableOpacity onPress={onClose}>
              <Text style={modalStyles.cancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={modalStyles.title}>Add Product</Text>
            <TouchableOpacity
              style={[!postForm.title || !postForm.description || isLoading ? { opacity: 0.6 } : {}]}
              onPress={handleSubmit}
              disabled={!postForm.title || !postForm.description || isLoading}
            >
              {isLoading ? (
                <ActivityIndicator size="small" color={COLORS.primary} />
              ) : (
                <Text style={modalStyles.submitText}>Post</Text>
              )}
            </TouchableOpacity>
          </View>

          <ScrollView
            style={modalStyles.content}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={[modalStyles.contentContainer, { paddingBottom: 100 }]}
            keyboardShouldPersistTaps="handled"
            scrollEventThrottle={16}
          >
          {/* Media Upload Section - MOVED TO TOP */}
          <Text style={modalStyles.sectionTitle}>Product Photos & Videos</Text>

          {/* Enhanced Media Section with Integrated Add Button */}
          <View style={modalStyles.mediaSection}>
            <View style={modalStyles.mediaGrid}>
              {/* Add Media Button - Always First */}
              <TouchableOpacity
                style={modalStyles.addMediaCard}
                onPress={addMedia}
              >
                <Ionicons name="add" size={32} color={COLORS.primary} />
                <Text style={modalStyles.addMediaCardText}>Add Media</Text>
                <Text style={modalStyles.addMediaCardSubtext}>Photos & Videos</Text>
              </TouchableOpacity>

              {/* Existing Media Items */}
              {mediaItems.map((item, index) => (
                <TouchableOpacity
                  key={item.id}
                  style={modalStyles.mediaItem}
                  onPress={() => openMediaViewer(index)}
                  onLongPress={() => {
                    Alert.alert(
                      'Reorder Media',
                      'Choose an action for this media item',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Move to Front', onPress: () => reorderMedia(index, 0) },
                        { text: 'Move to Back', onPress: () => reorderMedia(index, mediaItems.length - 1) },
                        { text: 'Remove', style: 'destructive', onPress: () => removeMedia(item.id) },
                      ]
                    );
                  }}
                >
                  <Image
                    source={{ uri: item.uri }}
                    style={modalStyles.mediaImage}
                    resizeMode="cover"
                  />
                  {item.type === 'video' && (
                    <View style={modalStyles.videoOverlay}>
                      <Ionicons name="play-circle" size={32} color="white" />
                      {item.duration && (
                        <Text style={modalStyles.videoDuration}>
                          {formatVideoDuration(item.duration)}
                        </Text>
                      )}
                    </View>
                  )}
                  {/* Text Overlay Indicator */}
                  {item.textOverlay && (
                    <View style={modalStyles.textOverlayIndicator}>
                      <Ionicons name="text" size={16} color="white" />
                    </View>
                  )}
                  <TouchableOpacity
                    style={modalStyles.removeMediaButton}
                    onPress={() => removeMedia(item.id)}
                  >
                    <Ionicons name="close-circle" size={20} color="#EF4444" />
                  </TouchableOpacity>
                  {/* Order indicator */}
                  <View style={modalStyles.orderIndicator}>
                    <Text style={modalStyles.orderText}>{index + 1}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>

            {/* Media Hint */}
            {mediaItems.length > 0 && (
              <Text style={modalStyles.mediaHint}>
                Tap to view • Long press to reorder • First image will be the main photo
              </Text>
            )}
          </View>

          {/* Product Information Section */}
          <Text style={modalStyles.sectionTitle}>Product Information</Text>

          <TextInput
            style={modalStyles.input}
            placeholder="Product Title *"
            placeholderTextColor={COLORS.textSecondary}
            value={postForm.title}
            onChangeText={(text) => setPostForm(prev => ({ ...prev, title: text }))}
          />

          <TextInput
            style={[modalStyles.input, { height: 100, textAlign: 'left' }]}
            placeholder="Product Description *"
            placeholderTextColor={COLORS.textSecondary}
            value={postForm.description}
            onChangeText={(text) => setPostForm(prev => ({ ...prev, description: text }))}
            multiline
            textAlignVertical="top"
          />

          {/* Product Status Selection - Multiple Selection */}
          <Text style={modalStyles.sectionTitle}>Product Status (Select all that apply)</Text>
          <View style={modalStyles.statusGrid}>
            {[
              { id: 'available', label: 'Available', color: COLORS.success, icon: 'checkmark-circle' },
              { id: 'out_of_stock', label: 'Out of Stock', color: COLORS.error, icon: 'close-circle' },
              { id: 'new', label: 'New', color: COLORS.primary, icon: 'sparkles' },
              { id: 'refurbished', label: 'Refurbished', color: COLORS.warning, icon: 'construct' },
              { id: 'second_hand', label: 'Second Hand', color: '#8B5CF6', icon: 'repeat' },
              { id: 'limited', label: 'Limited Edition', color: '#F59E0B', icon: 'star' },
              { id: 'trending', label: 'Trending', color: '#EF4444', icon: 'trending-up' },
              { id: 'high_quality', label: 'High Quality', color: '#10B981', icon: 'diamond' },
            ].map((status) => {
              const isSelected = postForm.statusTags?.includes(status.id) || false;
              return (
                <TouchableOpacity
                  key={status.id}
                  style={[
                    modalStyles.statusOption,
                    { borderColor: status.color },
                    isSelected && { backgroundColor: status.color + '20' }
                  ]}
                  onPress={() => {
                    setPostForm(prev => {
                      const currentTags = prev.statusTags || [];
                      const newTags = isSelected
                        ? currentTags.filter(tag => tag !== status.id)
                        : [...currentTags, status.id];
                      return { ...prev, statusTags: newTags };
                    });
                  }}
                >
                  <Ionicons
                    name={status.icon as any}
                    size={20}
                    color={isSelected ? status.color : COLORS.textSecondary}
                  />
                  <Text style={[
                    modalStyles.statusOptionText,
                    { color: isSelected ? status.color : COLORS.textSecondary }
                  ]}>
                    {status.label}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Enhanced Price Section */}
          <Text style={modalStyles.sectionTitle}>Price</Text>
          <View style={modalStyles.priceContainer}>
            {/* Currency Selection */}
            <View style={modalStyles.currencySelector}>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {[
                  { code: 'UGX', symbol: 'UGX', name: 'Ugandan Shilling' },
                  { code: 'USD', symbol: '$', name: 'US Dollar' },
                  { code: 'EUR', symbol: '€', name: 'Euro' },
                  { code: 'GBP', symbol: '£', name: 'British Pound' },
                  { code: 'KES', symbol: 'KSh', name: 'Kenyan Shilling' },
                  { code: 'TZS', symbol: 'TSh', name: 'Tanzanian Shilling' },
                ].map((currency) => (
                  <TouchableOpacity
                    key={currency.code}
                    style={[
                      modalStyles.currencyOption,
                      postForm.currency === currency.code && modalStyles.currencyOptionActive
                    ]}
                    onPress={() => setPostForm(prev => ({ ...prev, currency: currency.code }))}
                  >
                    <Text style={[
                      modalStyles.currencyText,
                      postForm.currency === currency.code && modalStyles.currencyTextActive
                    ]}>
                      {currency.symbol}
                    </Text>
                    <Text style={[
                      modalStyles.currencyCode,
                      postForm.currency === currency.code && modalStyles.currencyCodeActive
                    ]}>
                      {currency.code}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Price Input with Smart Parsing */}
            <View style={modalStyles.priceInputContainer}>
              <Text style={modalStyles.currencyPrefix}>
                {getCurrencySymbol(postForm.currency)}
              </Text>
              <TextInput
                style={modalStyles.priceInput}
                placeholder="Price"
                placeholderTextColor={COLORS.textSecondary}
                value={postForm.price}
                onChangeText={(text) => setPostForm(prev => ({ ...prev, price: text }))}
                keyboardType="default" // Allow letters for K, M, etc.
              />
            </View>

            {/* Price Preview */}
            {postForm.price && (
              <View style={modalStyles.pricePreview}>
                {(() => {
                  const parsed = parsePrice(postForm.price);
                  if (parsed.isValid && parsed.value > 0) {
                    return (
                      <Text style={modalStyles.pricePreviewText}>
                        = {formatPriceWithCurrency(parsed.value, postForm.currency)}
                      </Text>
                    );
                  } else if (postForm.price.trim()) {
                    return (
                      <Text style={modalStyles.pricePreviewError}>
                        Invalid price format
                      </Text>
                    );
                  }
                  return null;
                })()}
              </View>
            )}


          </View>

          <TextInput
            style={modalStyles.input}
            placeholder="Tags (comma separated)"
            placeholderTextColor={COLORS.textSecondary}
            value={postForm.tags}
            onChangeText={(text) => setPostForm(prev => ({ ...prev, tags: text }))}
          />

          <View style={modalStyles.checkboxContainer}>
            <TouchableOpacity
              style={modalStyles.checkbox}
              onPress={() => setPostForm(prev => ({ ...prev, isNegotiable: !prev.isNegotiable }))}
            >
              <Ionicons
                name={postForm.isNegotiable ? "checkbox" : "square-outline"}
                size={20}
                color={COLORS.primary}
              />
              <Text style={modalStyles.checkboxText}>Price is negotiable</Text>
            </TouchableOpacity>
          </View>

          <Text style={modalStyles.sectionTitle}>Location (Optional)</Text>

          <TextInput
            style={modalStyles.input}
            placeholder="Specific Address"
            placeholderTextColor={COLORS.textSecondary}
            value={postForm.address}
            onChangeText={(text) => setPostForm(prev => ({ ...prev, address: text }))}
          />

          <TextInput
            style={modalStyles.input}
            placeholder="City"
            placeholderTextColor={COLORS.textSecondary}
            value={postForm.city}
            onChangeText={(text) => setPostForm(prev => ({ ...prev, city: text }))}
          />
        </ScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>

    {/* Media Viewer - Only render when there are media items */}
    {mediaItems && mediaItems.length > 0 && (
      <MediaViewer
        visible={showMediaViewer}
        mediaItems={mediaItems}
        initialIndex={selectedMediaIndex}
        onClose={() => setShowMediaViewer(false)}
      />
    )}

    {/* Media Preview Modal */}
    <MediaPreviewModal
      visible={showMediaPreview}
      mediaItem={selectedMediaForPreview}
      onClose={() => {
        setShowMediaPreview(false);
        setSelectedMediaForPreview(null);
      }}
      onSave={(mediaItem) => {
        setMediaItems(prev => [...prev, mediaItem]);
        setShowMediaPreview(false);
        setSelectedMediaForPreview(null);
      }}
      onCropPhoto={(imageUri) => {
        setPhotoCropUri(imageUri);
        setShowPhotoCrop(true);
      }}
      COLORS={COLORS}
    />

    {/* Multi-Media Preview Modal removed - media added directly */}

    {/* Photo Crop Modal */}
    <PhotoCropModal
      visible={showPhotoCrop}
      imageUri={photoCropUri}
      onClose={() => setShowPhotoCrop(false)}
      onSave={(croppedImageUri) => {
        // Update the media item with the cropped image
        if (selectedMediaForPreview) {
          const updatedMediaItem = {
            ...selectedMediaForPreview,
            uri: croppedImageUri,
          };
          setSelectedMediaForPreview(updatedMediaItem);
        }
        setShowPhotoCrop(false);
      }}
    />

    {/* Media Options Modal */}
    <Modal
      visible={showMediaOptions}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowMediaOptions(false)}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.background }}>
        <View style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          paddingHorizontal: 16,
          paddingVertical: 12,
          borderBottomWidth: 1,
          borderBottomColor: COLORS.border,
        }}>
          <TouchableOpacity onPress={() => setShowMediaOptions(false)}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={{
            fontSize: 18,
            fontWeight: 'bold',
            color: COLORS.text,
          }}>Add Photos & Videos</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={{ flex: 1, padding: 20 }}>
          <Text style={{ fontSize: 16, color: COLORS.textSecondary, textAlign: 'center', marginBottom: 30 }}>
            Choose how to add media to your product
          </Text>

          {/* Take Photo Option */}
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: COLORS.surface,
              padding: 20,
              borderRadius: 12,
              marginBottom: 16,
            }}
            onPress={() => {
              setShowMediaOptions(false);
              openCamera('photo');
            }}
          >
            <Ionicons name="camera" size={24} color={COLORS.primary} />
            <Text style={{ fontSize: 16, fontWeight: '600', color: COLORS.text, marginLeft: 16 }}>
              📷 Take Photo
            </Text>
          </TouchableOpacity>

          {/* Record Video Option */}
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: COLORS.surface,
              padding: 20,
              borderRadius: 12,
              marginBottom: 16,
            }}
            onPress={() => {
              setShowMediaOptions(false);
              openCamera('video');
            }}
          >
            <Ionicons name="videocam" size={24} color={COLORS.primary} />
            <Text style={{ fontSize: 16, fontWeight: '600', color: COLORS.text, marginLeft: 16 }}>
              🎥 Record Video
            </Text>
          </TouchableOpacity>

          {/* Choose from Gallery Option */}
          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              backgroundColor: COLORS.surface,
              padding: 20,
              borderRadius: 12,
              marginBottom: 16,
            }}
            onPress={() => {
              setShowMediaOptions(false);
              openGallery();
            }}
          >
            <Ionicons name="images" size={24} color={COLORS.primary} />
            <Text style={{ fontSize: 16, fontWeight: '600', color: COLORS.text, marginLeft: 16 }}>
              🖼️ Choose from Gallery
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
    </>
  );
};

// Advanced Media Viewer Component
interface MediaViewerProps {
  visible: boolean;
  mediaItems: MediaItem[];
  initialIndex: number;
  onClose: () => void;
}

const MediaViewer: React.FC<MediaViewerProps> = ({
  visible,
  mediaItems,
  initialIndex,
  onClose,
}) => {
  // ALL HOOKS MUST BE DECLARED FIRST - BEFORE ANY EARLY RETURNS OR CONDITIONS
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoPosition, setVideoPosition] = useState(0);
  const [videoDuration, setVideoDuration] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const [imageLoading, setImageLoading] = useState<Record<string, boolean>>({});
  const [gestureStart, setGestureStart] = useState<{ x: number; y: number } | null>(null);
  const flatListRef = useRef<FlatList>(null);
  const videoRef = useRef<any>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // ALL useEffect hooks must also be declared before early return
  useEffect(() => {
    if (visible) {
      setCurrentIndex(initialIndex);
      flatListRef.current?.scrollToIndex({ index: initialIndex, animated: false });
    }
  }, [visible, initialIndex]);

  useEffect(() => {
    // Auto-hide controls after 3 seconds
    if (showControls) {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls]);

  // Early return AFTER all hooks are declared
  if (!mediaItems || mediaItems.length === 0) {
    console.warn('⚠️ MediaViewer: No media items provided');
    return (
      <Modal visible={visible} animationType="fade" presentationStyle="fullScreen" onRequestClose={onClose}>
        <View style={{ flex: 1, backgroundColor: '#000000' }}>
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 16,
            paddingTop: 50,
            paddingBottom: 16,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            zIndex: 1000,
          }}>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={28} color="white" />
            </TouchableOpacity>
            <Text style={{ color: 'white', fontSize: 16, fontWeight: '600' }}>
              No Media
            </Text>
            <View style={{ width: 28 }} />
          </View>
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Text style={{ color: 'white', fontSize: 18, textAlign: 'center' }}>
              No media items to display
            </Text>
          </View>
        </View>
      </Modal>
    );
  }

  // Debug logging
  console.log('🎬 MediaViewer rendered:', {
    visible,
    mediaItemsCount: mediaItems?.length || 0,
    initialIndex,
    currentIndex,
    firstItem: mediaItems?.[0] ? {
      id: mediaItems[0].id,
      type: mediaItems[0].type,
      uri: mediaItems[0].uri?.substring(0, 50) + '...'
    } : null
  });

  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;

  // Define media viewer styles
  const mediaViewerStyles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000000',
    },
    header: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingTop: 50,
      paddingBottom: 16,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      zIndex: 10,
    },
    closeButton: {
      padding: 8,
    },
    headerTitle: {
      color: 'white',
      fontSize: 16,
      fontWeight: '500',
    },
    mediaContainer: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    mediaWrapper: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    video: {
      width: '100%',
      height: '100%',
    },
    image: {
      width: screenWidth,
      height: screenHeight,
      backgroundColor: 'transparent', // Add background color for debugging
    },
    videoControls: {
      position: 'absolute',
      bottom: 100,
      left: 0,
      right: 0,
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      paddingVertical: 20,
    },
    playButton: {
      marginBottom: 20,
    },
    progressContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      width: '100%',
    },
    timeText: {
      color: 'white',
      fontSize: 12,
      minWidth: 40,
      textAlign: 'center',
    },
    progressBar: {
      flex: 1,
      height: 4,
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
      borderRadius: 2,
      marginHorizontal: 10,
      position: 'relative',
    },
    progressFill: {
      height: '100%',
      backgroundColor: 'white',
      borderRadius: 2,
    },
    progressThumb: {
      position: 'absolute',
      top: -6,
      width: 16,
      height: 16,
      backgroundColor: 'white',
      borderRadius: 8,
      marginLeft: -8,
    },
    indicators: {
      position: 'absolute',
      bottom: 50,
      left: 0,
      right: 0,
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 8,
    },
    indicator: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: 'white',
    },
  });

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  const togglePlayPause = () => {
    if (videoRef.current) {
      if (isVideoPlaying) {
        videoRef.current.pauseAsync();
      } else {
        videoRef.current.playAsync();
      }
      setIsVideoPlaying(!isVideoPlaying);
    }
  };

  const seekTo = (position: number) => {
    if (videoRef.current) {
      videoRef.current.setPositionAsync(position * videoDuration);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Gesture handling for vertical swipe to close
  const handleTouchStart = (event: any) => {
    const { pageX, pageY } = event.nativeEvent;
    setGestureStart({ x: pageX, y: pageY });
  };

  const handleTouchEnd = (event: any) => {
    if (!gestureStart) return;

    const { pageX, pageY } = event.nativeEvent;
    const deltaX = pageX - gestureStart.x;
    const deltaY = pageY - gestureStart.y;

    // Check if it's a vertical swipe (more vertical than horizontal)
    if (Math.abs(deltaY) > Math.abs(deltaX) && Math.abs(deltaY) > 100) {
      // Swipe down to close
      if (deltaY > 0) {
        onClose();
      }
    }

    setGestureStart(null);
  };

  const renderMediaItem = ({ item, index }: { item: MediaItem; index: number }) => {
    const isVideo = item.type === 'video';

    console.log('🖼️ Rendering media item:', {
      id: item.id,
      type: item.type,
      uri: item.uri?.substring(0, 50) + '...',
      isVideo
    });

    return (
      <View
        style={[mediaViewerStyles.mediaContainer, { width: screenWidth, height: screenHeight }]}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
      >
        <TouchableWithoutFeedback onPress={toggleControls}>
          <View style={mediaViewerStyles.mediaWrapper}>
            {isVideo ? (
              <VideoComponent uri={item.uri} style={mediaViewerStyles.video} />
            ) : (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <Image
                  source={{ uri: item.uri }}
                  style={mediaViewerStyles.image}
                  resizeMode="contain"
                  onLoad={() => {
                    console.log('✅ Image loaded successfully for:', item.uri);
                    setImageLoading(prev => ({ ...prev, [item.id]: false }));
                  }}
                  onError={(error) => {
                    console.error('❌ Image load error for:', item.uri, error);
                    setImageLoading(prev => ({ ...prev, [item.id]: false }));
                  }}
                  onLoadStart={() => {
                    console.log('🔄 Image loading started for:', item.uri);
                    setImageLoading(prev => ({ ...prev, [item.id]: true }));
                  }}
                />
                {/* Loading indicator overlay - only show when loading */}
                {imageLoading[item.id] && (
                  <View style={{
                    position: 'absolute',
                    justifyContent: 'center',
                    alignItems: 'center',
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    borderRadius: 8,
                    padding: 16,
                  }}>
                    <ActivityIndicator size="large" color="white" />
                    <Text style={{ color: 'white', marginTop: 8, fontSize: 14 }}>Loading image...</Text>
                  </View>
                )}
              </View>
            )}
          </View>
        </TouchableWithoutFeedback>

        {/* Video Controls Overlay */}
        {isVideo && showControls && index === currentIndex && (
          <View style={mediaViewerStyles.videoControls}>
            <TouchableOpacity
              style={mediaViewerStyles.playButton}
              onPress={togglePlayPause}
            >
              <Ionicons
                name={isVideoPlaying ? 'pause' : 'play'}
                size={48}
                color="white"
              />
            </TouchableOpacity>

            <View style={mediaViewerStyles.progressContainer}>
              <Text style={mediaViewerStyles.timeText}>
                {formatTime(videoPosition)}
              </Text>
              <View style={mediaViewerStyles.progressBar}>
                <View
                  style={[
                    mediaViewerStyles.progressFill,
                    { width: `${(videoPosition / videoDuration) * 100}%` }
                  ]}
                />
                <TouchableOpacity
                  style={[
                    mediaViewerStyles.progressThumb,
                    { left: `${(videoPosition / videoDuration) * 100}%` }
                  ]}
                  onPress={(e) => {
                    const x = e.nativeEvent.locationX;
                    const progress = x / (screenWidth - 40);
                    seekTo(progress);
                  }}
                />
              </View>
              <Text style={mediaViewerStyles.timeText}>
                {formatTime(videoDuration)}
              </Text>
            </View>
          </View>
        )}
      </View>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <View style={mediaViewerStyles.container}>
        {/* Header */}
        {showControls && (
          <View style={mediaViewerStyles.header}>
            <TouchableOpacity onPress={onClose} style={mediaViewerStyles.closeButton}>
              <Ionicons name="close" size={28} color="white" />
            </TouchableOpacity>
            <Text style={mediaViewerStyles.headerTitle}>
              {currentIndex + 1} of {mediaItems.length}
            </Text>
            <View style={{ width: 28 }} />
          </View>
        )}

        {/* Media Carousel */}
        <FlatList
          ref={flatListRef}
          data={mediaItems}
          renderItem={renderMediaItem}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          initialScrollIndex={initialIndex}
          getItemLayout={(_, index) => ({
            length: screenWidth,
            offset: screenWidth * index,
            index,
          })}
          onScrollToIndexFailed={(info) => {
            console.warn('⚠️ ScrollToIndex failed in MediaViewer:', info);
            // Fallback: scroll to the closest valid offset
            const offset = Math.min(info.index * screenWidth, (mediaItems.length - 1) * screenWidth);
            flatListRef.current?.scrollToOffset({ offset, animated: true });
          }}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
            setCurrentIndex(index);
            setIsVideoPlaying(false); // Pause video when switching
          }}
        />

        {/* Media Indicators */}
        {showControls && mediaItems.length > 1 && (
          <View style={mediaViewerStyles.indicators}>
            {mediaItems.map((_, index) => (
              <View
                key={index}
                style={[
                  mediaViewerStyles.indicator,
                  { opacity: index === currentIndex ? 1 : 0.5 }
                ]}
              />
            ))}
          </View>
        )}
      </View>
    </Modal>
  );
};

// Media Preview Modal with Text Overlay Component
interface MediaPreviewModalProps {
  visible: boolean;
  mediaItem: MediaItem | null;
  onClose: () => void;
  onSave: (mediaItem: MediaItem) => void;
  onCropPhoto?: (imageUri: string) => void;
  COLORS: any;
}

const MediaPreviewModal: React.FC<MediaPreviewModalProps> = ({
  visible,
  mediaItem,
  onClose,
  onSave,
  onCropPhoto,
  COLORS,
}) => {
  const [textOverlay, setTextOverlay] = useState('');
  const [overlayPosition, setOverlayPosition] = useState<'top' | 'center' | 'bottom'>('bottom');
  const [textColor, setTextColor] = useState('#FFFFFF');
  const [backgroundColor, setBackgroundColor] = useState('rgba(0, 0, 0, 0.7)');
  const [showTextInput, setShowTextInput] = useState(false);



  // Define media preview styles
  const mediaPreviewStyles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000000',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingTop: 50,
      paddingBottom: 16,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
    },
    headerButton: {
      padding: 8,
    },
    headerTitle: {
      color: 'white',
      fontSize: 18,
      fontWeight: '600',
    },
    skipText: {
      color: COLORS.primary,
      fontSize: 16,
      fontWeight: '500',
    },
    mediaContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    },
    media: {
      width: '100%',
      height: '70%',
    },
    textOverlay: {
      position: 'absolute',
      left: 20,
      right: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
    },
    overlayTop: {
      top: 50,
    },
    overlayCenter: {
      top: '50%',
      transform: [{ translateY: -20 }],
    },
    overlayBottom: {
      bottom: 50,
    },
    overlayText: {
      fontSize: 16,
      fontWeight: '600',
      textAlign: 'center',
    },
    controls: {
      backgroundColor: COLORS.background,
      paddingHorizontal: 16,
      paddingVertical: 20,
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.text,
      marginBottom: 16,
    },
    textInputButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      backgroundColor: COLORS.surface,
      borderRadius: 8,
      marginBottom: 12,
    },
    textInputButtonText: {
      fontSize: 16,
      color: COLORS.text,
      marginLeft: 8,
    },
    positionLabel: {
      fontSize: 14,
      fontWeight: '500',
      color: COLORS.text,
      marginBottom: 8,
      marginTop: 16,
    },
    positionButtons: {
      flexDirection: 'row',
      gap: 8,
      marginBottom: 16,
    },
    positionButton: {
      flex: 1,
      paddingVertical: 8,
      paddingHorizontal: 12,
      backgroundColor: COLORS.surface,
      borderRadius: 6,
      alignItems: 'center',
    },
    positionButtonActive: {
      backgroundColor: COLORS.primary,
    },
    positionButtonText: {
      fontSize: 14,
      color: COLORS.text,
    },
    positionButtonTextActive: {
      color: 'white',
    },
    colorGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    colorButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      borderWidth: 2,
      borderColor: 'transparent',
    },
    colorButtonActive: {
      borderColor: 'white',
    },
    saveButton: {
      backgroundColor: COLORS.primary,
      paddingVertical: 16,
      borderRadius: 8,
      alignItems: 'center',
      marginTop: 20,
    },
    saveButtonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: '600',
    },
    textInputModal: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    textInputContainer: {
      backgroundColor: COLORS.background,
      margin: 20,
      padding: 20,
      borderRadius: 12,
      width: '90%',
    },
    textInputTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: COLORS.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    textInput: {
      borderWidth: 1,
      borderColor: COLORS.border,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
      color: COLORS.text,
      backgroundColor: COLORS.background,
      minHeight: 80,
      textAlignVertical: 'top',
    },
    characterCount: {
      fontSize: 12,
      color: COLORS.textSecondary,
      textAlign: 'right',
      marginTop: 8,
    },
    textInputButtons: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 16,
    },
    cancelButton: {
      backgroundColor: COLORS.surface,
    },
    confirmButton: {
      backgroundColor: COLORS.primary,
    },
    cancelButtonText: {
      color: COLORS.text,
    },
    confirmButtonText: {
      color: 'white',
    },
  });

  // Color options for text and background
  const textColors = ['#FFFFFF', '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
  const backgroundColors = [
    'rgba(0, 0, 0, 0.7)',
    'rgba(255, 255, 255, 0.7)',
    'rgba(255, 0, 0, 0.7)',
    'rgba(0, 255, 0, 0.7)',
    'rgba(0, 0, 255, 0.7)',
    'rgba(255, 255, 0, 0.7)',
    'rgba(255, 0, 255, 0.7)',
    'rgba(0, 255, 255, 0.7)',
    'transparent'
  ];

  useEffect(() => {
    if (mediaItem) {
      setTextOverlay(mediaItem.textOverlay || '');
      setOverlayPosition(mediaItem.overlayPosition || 'bottom');
      setTextColor(mediaItem.textColor || '#FFFFFF');
      setBackgroundColor(mediaItem.backgroundColor || 'rgba(0, 0, 0, 0.7)');
    }
  }, [mediaItem]);

  const handleSave = () => {
    if (mediaItem) {
      const updatedMediaItem: MediaItem = {
        ...mediaItem,
        textOverlay,
        overlayPosition,
        textColor,
        backgroundColor,
      };
      onSave(updatedMediaItem);
    }
    onClose();
  };

  const handleSkip = () => {
    if (mediaItem) {
      const updatedMediaItem: MediaItem = {
        ...mediaItem,
        textOverlay: '',
        overlayPosition: 'bottom',
        textColor: '#FFFFFF',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
      };
      onSave(updatedMediaItem);
    }
    onClose();
  };

  if (!visible || !mediaItem) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView style={mediaPreviewStyles.container}>
        {/* Header */}
        <View style={mediaPreviewStyles.header}>
          <TouchableOpacity onPress={onClose} style={mediaPreviewStyles.headerButton}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={mediaPreviewStyles.headerTitle}>Preview & Customize</Text>
          <TouchableOpacity onPress={handleSkip} style={mediaPreviewStyles.headerButton}>
            <Text style={mediaPreviewStyles.skipText}>Skip</Text>
          </TouchableOpacity>
        </View>

        {/* Media Preview */}
        <View style={mediaPreviewStyles.mediaContainer}>
          {mediaItem.type === 'video' ? (
            <VideoComponent uri={mediaItem.uri} style={mediaPreviewStyles.media} />
          ) : (
            <Image
              source={{ uri: mediaItem.uri }}
              style={mediaPreviewStyles.media}
              resizeMode="contain"
            />
          )}

          {/* Text Overlay Preview */}
          {textOverlay.length > 0 && (
            <View style={[
              mediaPreviewStyles.textOverlay,
              { backgroundColor },
              overlayPosition === 'top' && mediaPreviewStyles.overlayTop,
              overlayPosition === 'center' && mediaPreviewStyles.overlayCenter,
              overlayPosition === 'bottom' && mediaPreviewStyles.overlayBottom,
            ]}>
              <Text style={[mediaPreviewStyles.overlayText, { color: textColor }]}>{textOverlay}</Text>
            </View>
          )}
        </View>

        {/* Controls */}
        <View style={mediaPreviewStyles.controls}>
          <Text style={mediaPreviewStyles.sectionTitle}>Add Text Overlay (Optional)</Text>

          <TouchableOpacity
            style={mediaPreviewStyles.textInputButton}
            onPress={() => setShowTextInput(true)}
          >
            <Ionicons name="text-outline" size={20} color={COLORS.primary} />
            <Text style={mediaPreviewStyles.textInputButtonText}>
              {textOverlay ? 'Edit Text' : 'Add Text'}
            </Text>
          </TouchableOpacity>

          {/* Crop Button for Photos */}
          {mediaItem?.type === 'photo' && onCropPhoto && (
            <TouchableOpacity
              style={mediaPreviewStyles.textInputButton}
              onPress={() => onCropPhoto(mediaItem.uri)}
            >
              <Ionicons name="crop-outline" size={20} color={COLORS.primary} />
              <Text style={mediaPreviewStyles.textInputButtonText}>
                Crop Photo
              </Text>
            </TouchableOpacity>
          )}

          {textOverlay.length > 0 && (
            <>
              <Text style={mediaPreviewStyles.positionLabel}>Text Position:</Text>
              <View style={mediaPreviewStyles.positionButtons}>
                {(['top', 'center', 'bottom'] as const).map((position) => (
                  <TouchableOpacity
                    key={position}
                    style={[
                      mediaPreviewStyles.positionButton,
                      overlayPosition === position && mediaPreviewStyles.positionButtonActive
                    ]}
                    onPress={() => setOverlayPosition(position)}
                  >
                    <Text style={[
                      mediaPreviewStyles.positionButtonText,
                      overlayPosition === position && mediaPreviewStyles.positionButtonTextActive
                    ]}>
                      {position.charAt(0).toUpperCase() + position.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Color Customization */}
              <Text style={mediaPreviewStyles.positionLabel}>Text Color:</Text>
              <View style={mediaPreviewStyles.colorGrid}>
                {textColors.map((color, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      mediaPreviewStyles.colorButton,
                      { backgroundColor: color },
                      textColor === color && mediaPreviewStyles.colorButtonActive
                    ]}
                    onPress={() => setTextColor(color)}
                  />
                ))}
              </View>

              <Text style={mediaPreviewStyles.positionLabel}>Background:</Text>
              <View style={mediaPreviewStyles.colorGrid}>
                {backgroundColors.map((color, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[
                      mediaPreviewStyles.colorButton,
                      {
                        backgroundColor: color === 'transparent' ? '#F0F0F0' : color,
                        borderWidth: color === 'transparent' ? 2 : 0,
                        borderColor: color === 'transparent' ? '#999' : 'transparent',
                      },
                      backgroundColor === color && mediaPreviewStyles.colorButtonActive
                    ]}
                    onPress={() => setBackgroundColor(color)}
                  >
                    {color === 'transparent' && (
                      <Text style={{ fontSize: 10, color: '#999' }}>None</Text>
                    )}
                  </TouchableOpacity>
                ))}
              </View>
            </>
          )}

          <TouchableOpacity
            style={mediaPreviewStyles.saveButton}
            onPress={handleSave}
          >
            <Text style={mediaPreviewStyles.saveButtonText}>Add to Product</Text>
          </TouchableOpacity>
        </View>

        {/* Text Input Modal */}
        <Modal
          visible={showTextInput}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowTextInput(false)}
        >
          <View style={mediaPreviewStyles.textInputModal}>
            <View style={mediaPreviewStyles.textInputContainer}>
              <Text style={mediaPreviewStyles.textInputTitle}>Add Text Overlay</Text>
              <TextInput
                style={mediaPreviewStyles.textInput}
                placeholder="Enter text to overlay on media..."
                placeholderTextColor={COLORS.textSecondary}
                value={textOverlay}
                onChangeText={setTextOverlay}
                multiline
                maxLength={100}
                autoFocus
              />
              <Text style={mediaPreviewStyles.characterCount}>
                {textOverlay.length}/100 characters
              </Text>
              <View style={mediaPreviewStyles.textInputButtons}>
                <TouchableOpacity
                  style={[mediaPreviewStyles.textInputButton, mediaPreviewStyles.cancelButton]}
                  onPress={() => setShowTextInput(false)}
                >
                  <Text style={mediaPreviewStyles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[mediaPreviewStyles.textInputButton, mediaPreviewStyles.confirmButton]}
                  onPress={() => setShowTextInput(false)}
                >
                  <Text style={mediaPreviewStyles.confirmButtonText}>Done</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </SafeAreaView>
    </Modal>
  );
};

// Video Component to handle video player hook internally
interface VideoComponentProps {
  uri: string;
  style: any;
}

// Bubbling Emoji Component
interface BubblingEmojiProps {
  emoji: string;
  x: number;
  y: number;
  size: number;
  containerWidth: number;
  containerHeight: number;
  isVisible: boolean;
}

const BubblingEmoji: React.FC<BubblingEmojiProps> = React.memo(({
  emoji,
  x,
  y,
  size,
  containerWidth,
  containerHeight,
  isVisible,
}) => {
  const bubbleAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (isVisible) {
      // Start bubbling animation
      const bubbleAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(bubbleAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(bubbleAnim, {
            toValue: 0,
            duration: 0,
            useNativeDriver: true,
          }),
        ])
      );

      const scaleAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );

      bubbleAnimation.start();
      scaleAnimation.start();

      return () => {
        bubbleAnimation.stop();
        scaleAnimation.stop();
      };
    }
  }, [isVisible, bubbleAnim, scaleAnim]);

  const bubbleTranslateY = bubbleAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -50],
  });

  const bubbleOpacity = bubbleAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [1, 0.7, 0],
  });

  return (
    <View
      style={{
        position: 'absolute',
        left: x * containerWidth - size / 2,
        top: y * containerHeight - size / 2,
        zIndex: 5,
      }}
    >
      <Animated.View
        style={{
          transform: [
            { scale: scaleAnim },
            { translateY: bubbleTranslateY },
          ],
          opacity: bubbleOpacity,
        }}
      >
        <Text style={{ fontSize: size }}>{emoji}</Text>
      </Animated.View>
    </View>
  );
});

// Enhanced VideoComponent with pause control
interface EnhancedVideoComponentProps {
  uri: string;
  style: any;
  itemId: string;
  isActive?: boolean; // Whether this video is currently visible
  onPlayerReady?: (player: any) => void; // Callback to pass player reference to parent
}

const EnhancedVideoComponent: React.FC<EnhancedVideoComponentProps> = React.memo(({
  uri,
  style,
  itemId,
  isActive = true,
  onPlayerReady
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  console.log('🎥 EnhancedVideoComponent rendering:', {
    itemId,
    uri: uri?.substring(0, 50) + '...',
    isActive
  });

  const player = useVideoPlayer(uri, (player) => {
    player.loop = false;
    player.muted = false;
    // Don't autoplay - require manual play

    // Register player with parent component
    if (onPlayerReady) {
      onPlayerReady(player);
    }
  });

  // Auto-pause when not active
  useEffect(() => {
    if (!isActive && player) {
      try {
        player.pause();
        console.log('🔇 Auto-paused video due to inactivity:', itemId);
      } catch (error) {
        console.warn('⚠️ Failed to auto-pause video:', itemId, error);
      }
    }
  }, [isActive, player, itemId]);

  useEffect(() => {
    setIsLoading(true);
    setHasError(false);

    const loadingTimeout = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(loadingTimeout);
  }, [uri]);

  return (
    <View style={[style, { backgroundColor: 'black', justifyContent: 'center', alignItems: 'center' }]}>
      <VideoView
        style={style}
        player={player}
        allowsFullscreen={false}
        allowsPictureInPicture={false}
        contentFit="contain"
        nativeControls={true}
      />

      {/* Loading indicator */}
      {isLoading && (
        <View style={{
          position: 'absolute',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          borderRadius: 8,
          padding: 16,
        }}>
          <ActivityIndicator size="large" color="white" />
          <Text style={{ color: 'white', marginTop: 8, fontSize: 14 }}>Loading video...</Text>
        </View>
      )}

      {/* Error indicator */}
      {hasError && (
        <View style={{
          position: 'absolute',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255, 0, 0, 0.7)',
          borderRadius: 8,
          padding: 16,
        }}>
          <Ionicons name="alert-circle" size={32} color="white" />
          <Text style={{ color: 'white', marginTop: 8, fontSize: 14, textAlign: 'center' }}>
            Video failed to load
          </Text>
        </View>
      )}
    </View>
  );
});

const VideoComponent: React.FC<VideoComponentProps> = React.memo(({ uri, style }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  console.log('🎥 VideoComponent rendering:', { uri: uri?.substring(0, 50) + '...' });

  const player = useVideoPlayer(uri, (player) => {
    player.loop = false;
    player.muted = false;
    // Don't autoplay - require manual play
  });

  // Use useEffect to handle loading states
  useEffect(() => {
    setIsLoading(true);
    setHasError(false);

    // Set a timeout to hide loading after a reasonable time
    const loadingTimeout = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(loadingTimeout);
  }, [uri]);

  return (
    <View style={[style, { backgroundColor: 'black', justifyContent: 'center', alignItems: 'center' }]}>
      <VideoView
        style={style}
        player={player}
        allowsFullscreen={false}
        allowsPictureInPicture={false}
        contentFit="contain"
        nativeControls={true}
      />

      {/* Loading indicator */}
      {isLoading && (
        <View style={{
          position: 'absolute',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          borderRadius: 8,
          padding: 16,
        }}>
          <ActivityIndicator size="large" color="white" />
          <Text style={{ color: 'white', marginTop: 8, fontSize: 14 }}>Loading video...</Text>
        </View>
      )}

      {/* Error indicator */}
      {hasError && (
        <View style={{
          position: 'absolute',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255, 0, 0, 0.7)',
          borderRadius: 8,
          padding: 16,
        }}>
          <Ionicons name="alert-circle" size={32} color="white" />
          <Text style={{ color: 'white', marginTop: 8, fontSize: 14, textAlign: 'center' }}>
            Video failed to load
          </Text>
        </View>
      )}
    </View>
  );
});

// MultiMediaPreviewModal component removed - simplified media handling

// Removed MultiMediaPreviewModal component entirely



  // Filter style functions
  const getFilterStyle = (filter: string) => {
    switch (filter) {
      case 'sepia':
        return { tintColor: '#d2b48c' };
      case 'grayscale':
        return { tintColor: '#808080' };
      default:
        return {};
    }
  };

  const getFilterOverlayStyle = (filter: string) => {
    switch (filter) {
      case 'vintage':
      case 'sepia':
        return { backgroundColor: 'rgba(210, 180, 140, 0.3)' };
      case 'brightness':
        return { backgroundColor: 'rgba(255, 255, 255, 0.2)' };
      case 'contrast':
        return { backgroundColor: 'rgba(0, 0, 0, 0.2)' };
      case 'warm':
        return { backgroundColor: 'rgba(255, 153, 102, 0.2)' };
      case 'cool':
        return { backgroundColor: 'rgba(102, 204, 255, 0.2)' };
      case 'dramatic':
        return { backgroundColor: 'rgba(102, 0, 102, 0.3)' };
      case 'soft':
        return { backgroundColor: 'rgba(255, 204, 255, 0.2)' };
      case 'vivid':
        return { backgroundColor: 'rgba(255, 51, 102, 0.2)' };
      case 'retro':
        return { backgroundColor: 'rgba(204, 153, 102, 0.3)' };
      case 'fade':
        return { backgroundColor: 'rgba(204, 204, 204, 0.2)' };
      default:
        return { backgroundColor: 'transparent' };
    }
  };



// All MultiMediaPreviewModal code removed - media added directly

// BusinessPostCard Component - moved outside to prevent hooks issues









// BusinessPostCard Component - moved outside to prevent hooks issues
const BusinessPostCard = React.memo(({
  post,
  currentUser,
  onPostPress,
  onBusinessNamePress,
  COLORS
}: {
  post: BusinessPost;
  currentUser: any;
  onPostPress: (post: BusinessPost, viewCount: number, likeCount: number, shareCount: number, isLiked: boolean) => void;
  onBusinessNamePress: (post: BusinessPost) => void;
  COLORS: any;
}) => {
  const [isLiked, setIsLiked] = useState(post.likes.includes(currentUser?.id || ''));
  const [likeCount, setLikeCount] = useState(post.likes.length);
  const [shareCount, setShareCount] = useState(post.shares || 0);
  const [viewCount, setViewCount] = useState(post.views || 0);

  // Update view count when post is pressed
  const handleCardPress = async () => {
    const newViewCount = viewCount + 1;
    setViewCount(newViewCount);
    onPostPress(post, newViewCount, likeCount, shareCount, isLiked);

    // Update post view count in Firebase
    try {
      await businessService.posts.updatePost(post.id, { views: newViewCount });
      console.log('✅ Post view count updated in Firebase:', newViewCount);
    } catch (error) {
      console.error('❌ Error updating post view count:', error);
    }

    // Update business profile statistics
    try {
      await businessService.updateBusinessStats(post.businessId, { views: 1 });
      console.log('✅ Business view stats updated');
    } catch (error) {
      console.error('❌ Error updating business view stats:', error);
    }
  };

  const handleLike = async (event: any) => {
    event.stopPropagation();
    if (!currentUser?.id) {
      Alert.alert('Login Required', 'Please login to like posts');
      return;
    }

    // Prevent actions on pending posts
    if ((post as any)._syncStatus === 'pending') {
      Alert.alert('Post Pending', 'Please wait for the post to sync before interacting with it.');
      return;
    }

    try {
      const newIsLiked = !isLiked;
      const newLikeCount = newIsLiked ? likeCount + 1 : likeCount - 1;

      setIsLiked(newIsLiked);
      setLikeCount(newLikeCount);

      // Update the post in the backend
      const updatedLikes = newIsLiked
        ? [...post.likes, currentUser.id]
        : post.likes.filter(id => id !== currentUser.id);

      await businessService.posts.updatePost(post.id, { likes: updatedLikes });

      // Update business profile statistics
      const likeChange = newIsLiked ? 1 : -1;
      await businessService.updateBusinessStats(post.businessId, { likes: likeChange });
    } catch (error) {
      console.error('Error updating like:', error);
      // Revert on error
      setIsLiked(!isLiked);
      setLikeCount(likeCount);
    }
  };

  const handleShare = async (event: any) => {
    event.stopPropagation();

    // Prevent actions on pending posts
    if ((post as any)._syncStatus === 'pending') {
      Alert.alert('Post Pending', 'Please wait for the post to sync before sharing.');
      return;
    }

    try {
      const newShareCount = shareCount + 1;
      setShareCount(newShareCount);

      const result = await Share.share({
        message: `Check out this product: ${post.title}\n\n${post.description}\n\nPrice: ${post.currency} ${post.price?.toLocaleString()}\n\nShared via IraChat Business`,
        title: post.title,
      });

      if (result.action === Share.sharedAction) {
        // Update the post in the backend
        await businessService.posts.updatePost(post.id, { shares: newShareCount });

        // Update business profile statistics
        await businessService.updateBusinessStats(post.businessId, { shares: 1 });
      } else {
        // Revert if not shared
        setShareCount(shareCount);
      }
    } catch (error) {
      console.error('Error sharing post:', error);
      setShareCount(shareCount);
    }
  };

  const handleChat = (event: any) => {
    event.stopPropagation();
    try {
      navigationService.openChat(post.businessId, false);
    } catch (error) {
      console.error('❌ Error opening chat:', error);
      Alert.alert('Error', 'Failed to open chat. Please try again.');
    }
  };

  const businessPostStyles = StyleSheet.create({
    container: {
      backgroundColor: COLORS.background,
      marginHorizontal: 16,
      marginVertical: 8,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: COLORS.border,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    businessInfo: {
      flex: 1,
    },
    businessRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    businessLogo: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: COLORS.surface,
    },
    businessLogoPlaceholder: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: COLORS.surface,
      justifyContent: 'center',
      alignItems: 'center',
    },
    businessTextContainer: {
      flex: 1,
    },
    businessNameRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    businessName: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.primary,
    },
    businessType: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginTop: 2,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    promotionBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#F59E0B20',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      gap: 4,
    },
    promotionText: {
      fontSize: 10,
      color: '#F59E0B',
      fontWeight: '600',
    },
    mediaContainer: {
      marginBottom: 12,
      position: 'relative',
    },
    mediaScrollView: {
      height: 200,
      width: '100%',
    },
    mediaItem: {
      width: Dimensions.get('window').width - 32,
      height: 200,
      borderRadius: 8,
      overflow: 'hidden',
    },
    videoContainer: {
      position: 'relative',
      width: '100%',
      height: '100%',
    },
    mediaImage: {
      width: '100%',
      height: '100%',
    },
    videoOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    videoDuration: {
      position: 'absolute',
      bottom: 8,
      right: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      color: 'white',
      fontSize: 12,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
    },
    mediaIndexIndicator: {
      position: 'absolute',
      top: 8,
      right: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    mediaIndexText: {
      color: 'white',
      fontSize: 12,
      fontWeight: '600',
    },
    zoomHint: {
      position: 'absolute',
      bottom: 8,
      left: 8,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      gap: 4,
    },
    zoomHintText: {
      color: 'white',
      fontSize: 12,
    },
    content: {
      flex: 1,
    },
    title: {
      fontSize: 18,
      fontWeight: '700',
      color: COLORS.text,
      marginBottom: 8,
    },
    description: {
      fontSize: 14,
      color: COLORS.textSecondary,
      marginBottom: 12,
      lineHeight: 20,
    },
    priceRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    priceContainer: {
      flex: 1,
    },
    price: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#10B981', // Green color for price
    },
    negotiable: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginTop: 2,
    },
    availabilityBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    availabilityText: {
      fontSize: 12,
      color: 'white',
      fontWeight: '600',
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
      gap: 4,
    },
    locationText: {
      fontSize: 12,
      color: COLORS.textSecondary,
    },
    statsRow: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
      marginBottom: 12,
      gap: 16,
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    statText: {
      fontSize: 12,
      color: COLORS.textSecondary,
    },
    actionsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: 8,
    },
    actionButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: COLORS.surface,
      justifyContent: 'center',
      alignItems: 'center',
    },
    timestamp: {
      fontSize: 11,
      color: COLORS.textSecondary,
      textAlign: 'center',
      marginTop: 8,
    },
  });

  return (
    <TouchableOpacity style={businessPostStyles.container} onPress={handleCardPress}>
      {/* Header */}
      <View style={businessPostStyles.header}>
        <View style={businessPostStyles.businessInfo}>
          {/* Logo, Business Name, and Type on Same Line */}
          <View style={businessPostStyles.businessRow}>
            {/* Business Logo - Clickable */}
            <TouchableOpacity onPress={() => onBusinessNamePress(post)}>
              {post.businessLogo ? (
                <Image
                  source={{ uri: post.businessLogo }}
                  style={businessPostStyles.businessLogo}
                  onError={() => {
                    console.log('❌ Business logo failed to load for:', post.businessName);
                  }}
                />
              ) : (
                <View style={businessPostStyles.businessLogoPlaceholder}>
                  <Ionicons name="business" size={16} color={COLORS.textSecondary} />
                </View>
              )}
            </TouchableOpacity>

            {/* Business Name and Type */}
            <View style={businessPostStyles.businessTextContainer}>
              <View style={businessPostStyles.businessNameRow}>
                <TouchableOpacity onPress={() => onBusinessNamePress(post)}>
                  <Text style={businessPostStyles.businessName}>{post.businessName}</Text>
                </TouchableOpacity>
                {post.isVerified && (
                  <Ionicons name="checkmark-circle" size={16} color="#3B82F6" />
                )}
              </View>
              <Text style={businessPostStyles.businessType}>{post.businessType}</Text>
            </View>
          </View>
        </View>

        {post.isPromoted && (
          <View style={businessPostStyles.promotionBadge}>
            <Ionicons name="flash" size={12} color="#F59E0B" />
            <Text style={businessPostStyles.promotionText}>PROMOTED</Text>
          </View>
        )}

        {/* Sync Indicator */}
        <SyncIndicator
          syncStatus={(post as any)._syncStatus}
          size="medium"
        />
      </View>

      {/* Enhanced Media with Horizontal Scroll */}
      {post.media && post.media.length > 0 && (
        <View style={businessPostStyles.mediaContainer}>
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            style={businessPostStyles.mediaScrollView}
          >
            {post.media.map((media, index) => (
              <View key={index} style={businessPostStyles.mediaItem}>
                {media.type === 'video' ? (
                  <View style={businessPostStyles.videoContainer}>
                    <Image
                      source={{ uri: media.thumbnailUrl || media.url }}
                      style={businessPostStyles.mediaImage}
                      resizeMode="cover"
                    />
                    <View style={businessPostStyles.videoOverlay}>
                      <Ionicons name="play-circle" size={48} color="white" />
                    </View>
                    {media.duration && (
                      <Text style={businessPostStyles.videoDuration}>
                        {formatVideoDuration(media.duration)}
                      </Text>
                    )}
                  </View>
                ) : (
                  <Image
                    source={{ uri: media.url }}
                    style={businessPostStyles.mediaImage}
                    resizeMode="cover"
                  />
                )}

                {/* Media Index Indicator */}
                {post.media.length > 1 && (
                  <View style={businessPostStyles.mediaIndexIndicator}>
                    <Text style={businessPostStyles.mediaIndexText}>
                      {index + 1}/{post.media.length}
                    </Text>
                  </View>
                )}

                {/* Zoom Hint */}
                <View style={businessPostStyles.zoomHint}>
                  <Ionicons name="expand-outline" size={12} color="white" />
                  <Text style={businessPostStyles.zoomHintText}>Tap to expand</Text>
                </View>
              </View>
            ))}
          </ScrollView>
        </View>
      )}

      <View style={businessPostStyles.content}>
        <Text style={businessPostStyles.title} numberOfLines={2}>
          {post.title}
        </Text>
        <Text style={businessPostStyles.description} numberOfLines={3}>
          {post.description}
        </Text>

        {/* Price and Availability */}
        <View style={businessPostStyles.priceRow}>
          {post.price && (
            <View style={businessPostStyles.priceContainer}>
              <Text style={businessPostStyles.price}>
                {post.currency} {post.price.toLocaleString()}
              </Text>
              {post.isNegotiable && (
                <Text style={businessPostStyles.negotiable}>Negotiable</Text>
              )}
            </View>
          )}

          <View style={[businessPostStyles.availabilityBadge, {
            backgroundColor: post.availability === 'available' ? '#10B981' : '#F59E0B'
          }]}>
            <Text style={businessPostStyles.availabilityText}>
              {post.availability === 'available' ? 'Available' : 'Out of Stock'}
            </Text>
          </View>
        </View>

        {/* Location */}
        {post.location && (
          <View style={businessPostStyles.locationContainer}>
            <Ionicons name="location-outline" size={14} color="#9CA3AF" />
            <Text style={businessPostStyles.locationText}>
              {post.location.city}, {post.location.district}
            </Text>
          </View>
        )}

        {/* Statistics Row */}
        <View style={businessPostStyles.statsRow}>
          <TouchableOpacity style={businessPostStyles.statItem} onPress={handleCardPress}>
            <Ionicons name="eye-outline" size={20} color="#9CA3AF" />
            <Text style={businessPostStyles.statText}>{viewCount}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={businessPostStyles.statItem} onPress={handleLike}>
            <Ionicons
              name={isLiked ? "heart" : "heart-outline"}
              size={20}
              color={isLiked ? "#FF3040" : "#9CA3AF"}
            />
            <Text style={businessPostStyles.statText}>{likeCount}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={businessPostStyles.statItem} onPress={handleShare}>
            <Ionicons name="share-outline" size={20} color="#9CA3AF" />
            <Text style={businessPostStyles.statText}>{shareCount}</Text>
          </TouchableOpacity>
        </View>

        {/* Action Buttons Row */}
        <View style={businessPostStyles.actionsRow}>
          <TouchableOpacity style={businessPostStyles.actionButton} onPress={handleChat}>
            <Ionicons name="chatbubble-outline" size={24} color={COLORS.primary} />
          </TouchableOpacity>

          {/* Contact Actions */}
          <BusinessContactActions
            businessName={post.businessName}
            phone={post.contact?.phone || "0700123456"} // Fallback for testing
            email={post.contact?.email || "<EMAIL>"} // Fallback for testing
            size="medium"
            style={{ flex: 1 }}
          />
        </View>

        {/* Timestamp */}
        <Text style={businessPostStyles.timestamp}>
          {post.createdAt.toLocaleDateString()} • {post.createdAt.toLocaleTimeString()}
        </Text>
      </View>
    </TouchableOpacity>
  );
});

export default function Business() {
  // Basic hooks
  const currentUser = useSelector((state: RootState) => state.user?.currentUser);
  const insets = useSafeAreaInsets();
  const { colors: COLORS } = useTheme();

  // Create modal styles with theme colors
  const modalStyles = StyleSheet.create({
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
    },
    title: {
      fontSize: 18,
      fontWeight: 'bold',
      color: COLORS.text,
    },
    productTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: COLORS.text,
      marginBottom: 8,
      lineHeight: 26,
    },
    cancelText: {
      fontSize: 16,
      color: COLORS.textSecondary,
    },
    submitText: {
      fontSize: 16,
      color: COLORS.primary,
      fontWeight: '600',
    },
    content: {
      flex: 1,
      // Removed padding - should be in contentContainerStyle
    },
    contentContainer: {
      padding: 16,
    },
    description: {
      fontSize: 16,
      color: COLORS.textSecondary,
      marginBottom: 20,
      textAlign: 'left',
      lineHeight: 24,
    },
    button: {
      backgroundColor: COLORS.primary,
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 8,
      alignItems: 'center',
    },
    buttonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    input: {
      borderWidth: 1,
      borderColor: COLORS.border,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
      color: COLORS.text,
      marginBottom: 16,
      backgroundColor: COLORS.background,
    },

    businessName: {
      fontSize: 16,
      color: COLORS.primary,
      marginBottom: 12,
    },
    price: {
      fontSize: 20,
      fontWeight: 'bold',
      color: COLORS.primary,
      marginTop: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: COLORS.text,
      marginBottom: 16,
      marginTop: 8,
    },
    checkboxContainer: {
      marginVertical: 16,
    },
    checkbox: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    checkboxText: {
      fontSize: 16,
      color: COLORS.text,
      marginLeft: 8,
    },
    // Business Type Selection Styles
    businessTypeOption: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 20,
      borderWidth: 1,
      borderColor: COLORS.border,
      marginRight: 8,
      marginBottom: 8,
      backgroundColor: COLORS.surface,
      minWidth: 100,
    },
    businessTypeSelected: {
      borderColor: COLORS.primary,
      backgroundColor: COLORS.primary + '20',
    },
    businessTypeLabel: {
      marginLeft: 6,
      fontSize: 14,
      fontWeight: '500',
      color: COLORS.text,
    },
    businessTypeLabelSelected: {
      color: COLORS.primary,
      fontWeight: '600',
    },
    // Media upload styles
    addMediaButton: {
      borderWidth: 2,
      borderColor: COLORS.primary,
      borderStyle: 'dashed',
      borderRadius: 12,
      paddingVertical: 24,
      paddingHorizontal: 16,
      alignItems: 'center',
      marginBottom: 16,
      backgroundColor: COLORS.background,
    },
    addMediaText: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.primary,
      marginTop: 8,
    },
    addMediaSubtext: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginTop: 4,
    },
    mediaGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 16,
      gap: 8,
    },
    mediaItem: {
      width: 100,
      height: 100,
      borderRadius: 8,
      overflow: 'hidden',
      position: 'relative',
    },
    mediaImage: {
      width: '100%',
      height: '100%',
    },
    videoOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    videoDuration: {
      position: 'absolute',
      bottom: 4,
      right: 4,
      color: 'white',
      fontSize: 10,
      fontWeight: '600',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      paddingHorizontal: 4,
      paddingVertical: 2,
      borderRadius: 4,
    },
    removeMediaButton: {
      position: 'absolute',
      top: 4,
      right: 4,
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderRadius: 10,
    },
    orderIndicator: {
      position: 'absolute',
      bottom: 4,
      left: 4,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderRadius: 8,
      paddingHorizontal: 6,
      paddingVertical: 2,
    },
    orderText: {
      color: 'white',
      fontSize: 10,
      fontWeight: '600',
    },
    mediaHint: {
      fontSize: 12,
      color: COLORS.textSecondary,
      textAlign: 'center',
      marginBottom: 16,
      fontStyle: 'italic',
    },
    textOverlayIndicator: {
      position: 'absolute',
      top: 4,
      left: 4,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderRadius: 8,
      padding: 2,
    },
    // Product Details Modal Styles
    businessHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    businessInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    businessType: {
      fontSize: 16,
      color: COLORS.text,
      marginTop: 4,
      fontWeight: '600',
      opacity: 0.9,
      textTransform: 'capitalize',
    },
    statusBadge: {
      alignSelf: 'flex-start',
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 16,
      marginBottom: 16,
    },
    statusText: {
      fontSize: 12,
      fontWeight: '600',
    },
    detailMediaSection: {
      marginBottom: 16,
    },
    detailMediaItem: {
      width: 120,
      height: 120,
      marginRight: 8,
      borderRadius: 8,
      overflow: 'hidden',
      position: 'relative',
    },
    detailMediaImage: {
      width: '100%',
      height: '100%',
    },
    detailVideoOverlay: {
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: [{ translateX: -16 }, { translateY: -16 }],
    },
    priceSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    negotiable: {
      fontSize: 12,
      color: COLORS.textSecondary,
      fontStyle: 'italic',
    },
    negotiableRight: {
      fontSize: 12,
      color: COLORS.textSecondary,
      fontStyle: 'italic',
      textAlign: 'right',
    },
    locationSection: {
      marginBottom: 16,
    },
    locationItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
      marginBottom: 8,
    },
    locationText: {
      fontSize: 14,
      color: COLORS.primary,
      fontWeight: '500',
    },
    additionalLocations: {
      marginTop: 8,
    },
    additionalLocationsLabel: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginBottom: 4,
    },
    additionalLocationText: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginLeft: 8,
    },
    productDetailsSection: {
      marginBottom: 16,
    },
    detailRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 4,
    },
    detailLabel: {
      fontSize: 14,
      color: COLORS.textSecondary,
      fontWeight: '500',
    },
    detailValue: {
      fontSize: 14,
      color: COLORS.text,
    },
    contactSection: {
      marginBottom: 16,
    },
    statsSection: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: COLORS.border,
      marginBottom: 16,
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    statText: {
      fontSize: 12,
      color: COLORS.textSecondary,
    },
    timestamp: {
      fontSize: 11,
      color: COLORS.textSecondary,
      textAlign: 'center',
      marginBottom: 16,
    },
    // Product Status Styles
    statusGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
      marginBottom: 16,
    },
    statusOption: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 20,
      borderWidth: 1,
      marginBottom: 8,
      minWidth: '45%',
    },
    statusOptionText: {
      marginLeft: 6,
      fontSize: 14,
      fontWeight: '500',
    },
    // Enhanced Media Section Styles
    mediaSection: {
      marginBottom: 16,
    },
    addMediaCard: {
      width: 100,
      height: 100,
      borderRadius: 8,
      borderWidth: 2,
      borderColor: COLORS.primary,
      borderStyle: 'dashed',
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: COLORS.primary + '10',
      marginRight: 8,
      marginBottom: 8,
    },
    addMediaCardText: {
      fontSize: 12,
      fontWeight: '600',
      color: COLORS.primary,
      marginTop: 4,
    },
    addMediaCardSubtext: {
      fontSize: 10,
      color: COLORS.textSecondary,
      textAlign: 'center',
    },
    // Enhanced Price Styles
    priceContainer: {
      marginBottom: 16,
    },
    currencySelector: {
      marginBottom: 12,
    },
    currencyOption: {
      alignItems: 'center',
      paddingHorizontal: 12,
      paddingVertical: 8,
      marginRight: 8,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: COLORS.border,
      backgroundColor: COLORS.surface,
      minWidth: 60,
    },
    currencyOptionActive: {
      borderColor: COLORS.primary,
      backgroundColor: COLORS.primary + '20',
    },
    currencyText: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.text,
    },
    currencyTextActive: {
      color: COLORS.primary,
    },
    currencyCode: {
      fontSize: 10,
      color: COLORS.textSecondary,
      marginTop: 2,
    },
    currencyCodeActive: {
      color: COLORS.primary,
    },
    priceInputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: COLORS.border,
      borderRadius: 8,
      backgroundColor: COLORS.background,
      marginBottom: 12,
    },
    currencyPrefix: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.text,
      paddingHorizontal: 12,
      paddingVertical: 12,
      backgroundColor: COLORS.surface,
      borderTopLeftRadius: 8,
      borderBottomLeftRadius: 8,
    },
    priceInput: {
      flex: 1,
      fontSize: 16,
      color: COLORS.text,
      paddingHorizontal: 12,
      paddingVertical: 12,
    },

    pricePreview: {
      marginTop: 8,
      alignItems: 'center',
    },
    pricePreviewText: {
      fontSize: 14,
      fontWeight: '600',
      color: COLORS.success,
    },
    pricePreviewError: {
      fontSize: 12,
      color: COLORS.error,
      fontStyle: 'italic',
    },
    // Business photos upload styles
    photoSection: {
      marginBottom: 20,
    },
    fieldLabel: {
      fontSize: 14,
      fontWeight: '600',
      color: COLORS.text,
      marginBottom: 8,
    },
    logoUploadButton: {
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: COLORS.border,
      borderStyle: 'dashed',
      borderRadius: 12,
      padding: 16,
      backgroundColor: COLORS.surface,
    },
    logoPreview: {
      width: 80,
      height: 80,
      borderRadius: 40,
    },
    logoPlaceholder: {
      alignItems: 'center',
      justifyContent: 'center',
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: COLORS.background,
    },
    logoPlaceholderText: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginTop: 4,
      fontWeight: '500',
    },

    // Cover photo styles
    coverPhotoUploadButton: {
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: COLORS.border,
      borderStyle: 'dashed',
      borderRadius: 12,
      padding: 16,
      backgroundColor: COLORS.surface,
      height: 120,
    },
    coverPhotoPreview: {
      width: '100%',
      height: 100,
      borderRadius: 8,
    },
    coverPhotoPlaceholder: {
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: 100,
      borderRadius: 8,
      backgroundColor: COLORS.background,
    },
    sectionHeader: {
      marginTop: 20,
      marginBottom: 12,
    },
    socialSectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.text,
      textAlign: 'center',
    },
  });

  // Main component styles
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: COLORS.background,
    },
    listContent: {
      paddingBottom: 100,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 60,
    },
    emptyTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: COLORS.text,
      marginTop: 16,
      marginBottom: 8,
    },
    emptySubtitle: {
      fontSize: 14,
      color: COLORS.textSecondary,
      textAlign: 'center',
      paddingHorizontal: 32,
    },
    postCard: {
      backgroundColor: COLORS.background,
      marginHorizontal: 16,
      marginVertical: 8,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: COLORS.border,
    },
    postHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    businessInfo: {
      flex: 1,
    },
    businessName: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.primary,
    },
    businessType: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginTop: 2,
    },
    postActions: {
      flexDirection: 'row',
      gap: 8,
    },
    actionButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: COLORS.surface,
      justifyContent: 'center',
      alignItems: 'center',
    },
    postTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: COLORS.text,
      marginBottom: 8,
    },
    postDescription: {
      fontSize: 14,
      color: COLORS.textSecondary,
      marginBottom: 12,
      lineHeight: 20,
    },
    postMedia: {
      height: 200,
      borderRadius: 8,
      marginBottom: 12,
      overflow: 'hidden',
    },
    postPrice: {
      fontSize: 20,
      fontWeight: 'bold',
      color: COLORS.primary,
      marginBottom: 8,
    },
    postLocation: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginBottom: 8,
    },
    postStats: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: COLORS.border,
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    statText: {
      fontSize: 12,
      color: COLORS.textSecondary,
    },
    timestamp: {
      fontSize: 11,
      color: COLORS.textSecondary,
    },
    modalActionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: COLORS.border,
      marginTop: 16,
    },
    modalActionButton: {
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
      minWidth: 80,
    },
    modalActionButtonActive: {
      backgroundColor: COLORS.primaryLight,
    },
    modalActionButtonText: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginTop: 4,
      fontWeight: '500',
    },
    modalActionButtonTextActive: {
      color: COLORS.primary,
    },
    scrollButtonsContainer: {
      position: 'absolute',
      right: 16,
      bottom: 100, // Above the bottom tab bar
      flexDirection: 'column',
      gap: 8,
    },
    scrollButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: COLORS.primary,
      justifyContent: 'center',
      alignItems: 'center',
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
    },
    scrollToTopButton: {
      // Additional styles for top button if needed
    },
    scrollToBottomButton: {
      // Additional styles for bottom button if needed
    },
  });

  // Business Post Card Styles
  const businessPostStyles = StyleSheet.create({
    container: {
      backgroundColor: COLORS.background,
      marginHorizontal: 16,
      marginVertical: 8,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: COLORS.border,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    businessInfo: {
      flex: 1,
    },
    businessNameRow: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    businessName: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.primary,
    },
    businessType: {
      fontSize: 14,
      color: COLORS.primary,
      marginTop: 2,
      fontWeight: '600',
      textTransform: 'uppercase',
    },
    promotionBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#F59E0B20',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      gap: 4,
    },
    promotionText: {
      fontSize: 10,
      color: '#F59E0B',
      fontWeight: '600',
    },
    mediaContainer: {
      marginBottom: 12,
      position: 'relative',
    },
    mediaScrollView: {
      height: 200,
      width: '100%', // Use full container width
    },
    mediaItem: {
      width: Dimensions.get('window').width - 32, // Full container width minus padding
      height: 200,
      borderRadius: 8,
      overflow: 'hidden',
    },
    videoContainer: {
      position: 'relative',
      width: '100%',
      height: '100%',
    },
    mediaImage: {
      width: '100%',
      height: '100%',
    },
    videoOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
    },
    videoDuration: {
      position: 'absolute',
      bottom: 8,
      right: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      color: 'white',
      fontSize: 12,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 4,
    },
    mediaIndexIndicator: {
      position: 'absolute',
      top: 8,
      right: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    mediaIndexText: {
      color: 'white',
      fontSize: 12,
      fontWeight: '600',
    },
    zoomHint: {
      position: 'absolute',
      bottom: 8,
      left: 8,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      gap: 4,
    },
    zoomHintText: {
      color: 'white',
      fontSize: 12,
    },
    content: {
      flex: 1,
    },
    title: {
      fontSize: 18,
      fontWeight: '700',
      color: COLORS.primary,
      marginBottom: 8,
    },
    description: {
      fontSize: 14,
      color: COLORS.textSecondary,
      marginBottom: 12,
      lineHeight: 20,
    },
    priceRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 8,
    },
    priceContainer: {
      flex: 1,
    },
    price: {
      fontSize: 20,
      fontWeight: 'bold',
      color: COLORS.primary,
    },
    negotiable: {
      fontSize: 12,
      color: COLORS.textSecondary,
      fontStyle: 'italic',
    },
    availabilityBadge: {
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
    },
    availabilityText: {
      fontSize: 12,
      color: 'white',
      fontWeight: '600',
    },
    locationContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
      marginBottom: 12,
    },
    locationText: {
      fontSize: 12,
      color: COLORS.textSecondary,
    },
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: COLORS.border,
    },
    stats: {
      flexDirection: 'row',
      gap: 16,
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    statText: {
      fontSize: 12,
      color: COLORS.textSecondary,
    },
    actions: {
      flexDirection: 'row',
      gap: 8,
    },
    actionButton: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: COLORS.surface,
      justifyContent: 'center',
      alignItems: 'center',
    },
    timestamp: {
      fontSize: 11,
      color: COLORS.textSecondary,
      textAlign: 'center',
      marginTop: 8,
    },
    footerLoading: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingVertical: 20,
      gap: 8,
    },
    footerLoadingText: {
      fontSize: 14,
      color: COLORS.textSecondary,
    },
    downloadIndicator: {
      position: 'absolute',
      top: 8,
      left: 8,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      borderRadius: 16,
      padding: 4,
      minWidth: 32,
      minHeight: 32,
      justifyContent: 'center',
      alignItems: 'center',
    },
    downloadProgress: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 4,
    },
    downloadProgressText: {
      color: '#FFFFFF',
      fontSize: 10,
      fontWeight: '600',
    },
    statsRow: {
      flexDirection: 'row',
      justifyContent: 'flex-start',
      alignItems: 'center',
      marginBottom: 12,
      gap: 16,
    },
    actionsRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: 8,
    },
  });

  // State Management
  const [businessPosts, setBusinessPosts] = useState<BusinessPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showAddPost, setShowAddPost] = useState(false);
  const [showRegistration, setShowRegistration] = useState(false);
  const [editingBusiness, setEditingBusiness] = useState<BusinessProfile | null>(null);
  const [registrationForced, setRegistrationForced] = useState(false);
  const [modalStable, setModalStable] = useState(false);
  const [selectedPost, setSelectedPost] = useState<BusinessPost | null>(null);
  const [userBusinessProfiles, setUserBusinessProfiles] = useState<BusinessProfile[]>([]);
  const [activeBusinessProfile, setActiveBusinessProfile] = useState<BusinessProfile | null>(null);
  const [showBusinessSelector, setShowBusinessSelector] = useState(false);
  const [businessSelectorForAddPost, setBusinessSelectorForAddPost] = useState(false);
  const [showBusinessProfile, setShowBusinessProfile] = useState(false);
  const [selectedBusinessProfile, setSelectedBusinessProfile] = useState<BusinessProfile | null>(null);

  // Scroll detection states
  const [isScrolling, setIsScrolling] = useState(false);
  const [showFilterBar, setShowFilterBar] = useState(true);
  const [currentScrollY, setCurrentScrollY] = useState(0);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastScrollYRef = useRef(0);
  const scrollVelocityRef = useRef(0);
  const touchStartYRef = useRef(0);
  const flatListRef = useRef<FlatList>(null);
  const [addPostDebounce, setAddPostDebounce] = useState(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);



  // Media viewer state for product details modal
  const [showMediaViewer, setShowMediaViewer] = useState(false);
  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);

  // Enhanced media viewer for business posts
  const [showEnhancedMediaViewer, setShowEnhancedMediaViewer] = useState(false);
  const [selectedPostMedia, setSelectedPostMedia] = useState<any[]>([]);
  const [selectedPostMediaIndex, setSelectedPostMediaIndex] = useState(0);

  // Debug: Track component re-renders
  console.log('🏢 Business component rendering, showRegistration:', showRegistration);
  // Analytics and manager functionality handled via alerts

  // Search and Filter State
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    searchText: '',
    category: undefined,
    businessType: undefined,
    priceRange: { min: 0, max: 1000000 },
    location: undefined,
    availability: undefined,
    isNegotiable: undefined,
    sortBy: 'newest',
  });
  const [filteredPosts, setFilteredPosts] = useState<BusinessPost[]>([]);
  const [searchResultsCount, setSearchResultsCount] = useState<number | undefined>(undefined);
  const [showSearchBar, setShowSearchBar] = useState(false);
  const [showFiltersModal, setShowFiltersModal] = useState(false);

  // Product management states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingPost, setEditingPost] = useState<BusinessPost | null>(null);
  const [showPriceAdjustmentModal, setShowPriceAdjustmentModal] = useState(false);
  const [priceAdjustmentPost, setPriceAdjustmentPost] = useState<BusinessPost | null>(null);






  // Use refs to avoid stale closure issues
  const filtersRef = useRef(searchFilters);
  const isLoadingRef = useRef(isLoading);
  const isRefreshingRef = useRef(isRefreshing);
  const hasMoreRef = useRef(hasMore);
  const businessPostsRef = useRef(businessPosts);



  // Update refs when state changes
  useEffect(() => {
    filtersRef.current = searchFilters;
  }, [searchFilters]);

  useEffect(() => {
    isLoadingRef.current = isLoading;
  }, [isLoading]);

  useEffect(() => {
    isRefreshingRef.current = isRefreshing;
  }, [isRefreshing]);

  useEffect(() => {
    hasMoreRef.current = hasMore;
  }, [hasMore]);

  useEffect(() => {
    businessPostsRef.current = businessPosts;
  }, [businessPosts]);

  // Circuit breaker to prevent infinite loops
  const lastLoadTimeRef = useRef(0);
  const loadAttemptsRef = useRef(0);
  const LOAD_COOLDOWN = 1000; // 1 second cooldown
  const MAX_LOAD_ATTEMPTS = 3; // Max attempts before backing off

  // Periodic sync check
  useEffect(() => {
    const syncInterval = setInterval(async () => {
      try {
        // Check if there are any pending or failed posts to sync
        const stats = await postStorageService.getSyncStats();
        if (stats.pending > 0 || stats.failed > 0) {
          console.log(`🔄 Background sync check: ${stats.pending} pending, ${stats.failed} failed posts`);
          await postStorageService.attemptSync();
        }
      } catch (error) {
        console.error('❌ Background sync check error:', error);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(syncInterval);
  }, []);

  // Load business data
  const loadBusinessPosts = useCallback(async (refresh: boolean = false) => {
    const now = Date.now();

    // Circuit breaker: prevent too frequent calls
    if (now - lastLoadTimeRef.current < LOAD_COOLDOWN) {
      console.log('🔄 Business posts load skipped - cooldown active');
      return;
    }

    // Circuit breaker: prevent too many attempts
    if (loadAttemptsRef.current >= MAX_LOAD_ATTEMPTS && !refresh) {
      console.log('🔄 Business posts load skipped - max attempts reached');
      return;
    }

    // Use refs to avoid stale closure issues
    if (!refresh && (isLoadingRef.current || isRefreshingRef.current)) {
      console.log('🔄 Business posts load skipped - already loading');
      return;
    }
    if (!refresh && businessPostsRef.current.length > 0 && !hasMoreRef.current) {
      console.log('🔄 Business posts load skipped - no more data');
      return;
    }

    lastLoadTimeRef.current = now;
    loadAttemptsRef.current += 1;

    console.log(`🔄 Loading business posts (refresh: ${refresh}, attempt: ${loadAttemptsRef.current})`);

    try {
      if (refresh) {
        setIsRefreshing(true);
        loadAttemptsRef.current = 0; // Reset attempts on manual refresh

        // Retry failed syncs when refreshing
        console.log('🔄 Retrying failed syncs during refresh...');
        try {
          // Get current sync stats before retry
          const statsBefore = await postStorageService.getSyncStats();
          console.log('📊 Sync stats before retry:', statsBefore);

          if (statsBefore.failed > 0 || statsBefore.pending > 0) {
            console.log(`🔄 Retrying ${statsBefore.failed} failed and ${statsBefore.pending} pending posts...`);

            // Retry failed posts
            await postStorageService.retryFailedPosts();

            // Also attempt sync for any pending posts
            await postStorageService.attemptSync();

            // Get stats after retry
            const statsAfter = await postStorageService.getSyncStats();
            console.log('📊 Sync stats after retry:', statsAfter);

            // Show user feedback if there were posts to retry
            if (statsBefore.failed > 0) {
              console.log(`✅ Retried ${statsBefore.failed} failed posts`);
            }
          } else {
            console.log('ℹ️ No failed or pending posts to retry');
          }

          console.log('✅ Sync retry process completed');
        } catch (syncError) {
          console.error('❌ Error retrying failed syncs:', syncError);
        }
      } else {
        setIsLoading(true);
      }

      // Use the business service to get marketplace posts with filters (prioritize Firebase)
      const convertedFilters = convertSearchFilters(filtersRef.current);
      console.log('📡 Fetching marketplace posts with filters:', convertedFilters);
      const serverPosts = await businessService.getMarketplacePosts(convertedFilters);
      console.log(`✅ Loaded ${serverPosts.length} server posts`);

      let allPosts = [...serverPosts];

      // Always load local posts (including pending and failed ones) to ensure they're visible
      const localPosts = await postStorageService.getAllPosts();
      console.log(`📱 Loaded ${localPosts.length} local posts`);

      // Add local posts that don't exist on server (pending, syncing, or failed posts)
      localPosts.forEach(localPost => {
        // Check if this is a local post (has _tempId) or if it doesn't exist on server
        const isLocalPost = '_tempId' in localPost && localPost._tempId;
        const existsOnServer = allPosts.find(p => p.id === localPost.id);

        if (isLocalPost || !existsOnServer) {
          // Log sync status for debugging
          if ('_syncStatus' in localPost) {
            console.log(`📱 Adding local post: ${localPost.id} (${localPost.title}) - Status: ${localPost._syncStatus}`);
          }
          allPosts.push(localPost);
        }
      });

      if (refresh) {
        console.log('🔄 Refresh completed - local posts preserved');
      }

      // Sort by creation date
      allPosts.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      if (refresh) {
        setBusinessPosts(allPosts);
      } else {
        setBusinessPosts(prev => {
          const merged = [...prev, ...allPosts];
          // Remove duplicates
          const unique = merged.filter((post, index, self) =>
            index === self.findIndex(p => p.id === post.id)
          );
          return unique;
        });
      }

      setHasMore(serverPosts.length >= 20);
      loadAttemptsRef.current = 0; // Reset attempts on success

      // Sync stats updated automatically by the service

    } catch (error) {
      console.error('❌ Error loading business posts:', error);
      // Handle offline mode gracefully
      if (refresh) {
        // Try to get cached posts from offline storage
        try {
          console.log('🔄 Trying to load cached posts from offline storage');
          const convertedFilters = convertSearchFilters(filtersRef.current);
          const result = await businessService.posts.getPosts(convertedFilters, 1, 20);
          console.log(`📱 Loaded ${result.success ? result.data?.length || 0 : 0} cached posts`);
          setBusinessPosts(result.success ? result.data || [] : []);
        } catch (offlineError) {
          console.error('❌ Error loading cached posts:', offlineError);
          setBusinessPosts([]);
        }
      }
      setHasMore(false);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  // Load user business profiles (support multiple profiles)
  const loadUserBusinessProfiles = useCallback(async () => {
    if (!currentUser) {
      console.log('👤 No current user - skipping business profiles load');
      return;
    }

    console.log(`👤 Loading ALL business profiles for user: ${currentUser.id}`);
    try {
      const result = await businessService.getUserBusinessProfiles(currentUser.id);
      console.log('👤 Business profiles result:', result);

      if (result.success && result.data) {
        console.log(`👤 Found ${result.data.length} business profiles:`, result.data.map(p => p.businessName));
        setUserBusinessProfiles(result.data);

        // Set active business profile if none is set
        if (!activeBusinessProfile && result.data.length > 0) {
          setActiveBusinessProfile(result.data[0]);
          console.log('👤 Set active business profile:', result.data[0].businessName);
        }
      } else {
        console.log('👤 No business profiles found or error:', result.error);
        setUserBusinessProfiles([]);
        setActiveBusinessProfile(null);
      }
    } catch (error) {
      console.error('❌ Error loading business profiles:', error);
      setUserBusinessProfiles([]);
      setActiveBusinessProfile(null);
    }
  }, [currentUser, activeBusinessProfile]);

  // Handle business deletion
  const handleDeleteBusiness = useCallback(async (businessId: string) => {
    try {
      console.log('🗑️ Deleting business:', businessId);

      const result = await businessService.deleteBusinessProfile(businessId);

      if (result.success) {
        // Remove from local state
        setUserBusinessProfiles(prev => prev.filter(profile => profile.id !== businessId));

        // Clear selected business if it was the deleted one
        if (selectedBusinessProfile?.id === businessId) {
          setSelectedBusinessProfile(null);
          setShowBusinessProfile(false);
        }

        // Clear active business if it was the deleted one
        if (activeBusinessProfile?.id === businessId) {
          setActiveBusinessProfile(null);
        }

        // Refresh business posts
        await loadBusinessPosts();

        Alert.alert(
          'Success',
          'Business profile and all related data have been permanently deleted.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Error',
          result.error || 'Failed to delete business profile. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('❌ Error deleting business:', error);
      Alert.alert(
        'Error',
        'Failed to delete business profile. Please check your internet connection and try again.',
        [{ text: 'OK' }]
      );
    }
  }, [selectedBusinessProfile, activeBusinessProfile, loadBusinessPosts]);

  // Initialize data
  useEffect(() => {
    let isMounted = true;

    const initializeData = async () => {
      if (isMounted) {
        await loadBusinessPosts(true);
        await loadUserBusinessProfiles();
      }
    };

    initializeData();

    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array for initialization only

  // Reload business profiles when user changes (for login/logout scenarios)
  useEffect(() => {
    if (currentUser) {
      console.log('👤 User changed, reloading business profiles for:', currentUser.id);
      loadUserBusinessProfiles();
    } else {
      console.log('👤 No user, clearing business profiles');
      setUserBusinessProfiles([]);
      setActiveBusinessProfile(null);
    }
  }, [currentUser?.id, loadUserBusinessProfiles]);

  // Handle filter changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      loadBusinessPosts(true);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [selectedCategory]); // Only depend on selectedCategory

  // Prevent modal from closing when stable
  useEffect(() => {
    if (modalStable && showRegistration) {
      // Keep modal open regardless of userBusinessProfile changes
      const timer = setTimeout(() => {
        setModalStable(false);
      }, 1000); // Stabilize for 1 second
      return () => clearTimeout(timer);
    }
  }, [modalStable, showRegistration]);

  // Add focus effect to prevent component re-mounting
  useFocusEffect(
    useCallback(() => {
      console.log('🏢 Business tab focused');
      return () => {
        console.log('🏢 Business tab unfocused');
      };
    }, [])
  );



  // State for tracking modal statistics
  const [modalViewCount, setModalViewCount] = useState<number>(0);
  const [modalLikeCount, setModalLikeCount] = useState<number>(0);
  const [modalShareCount, setModalShareCount] = useState<number>(0);
  const [modalIsLiked, setModalIsLiked] = useState<boolean>(false);

  // Function to update post statistics in the main posts array
  const updatePostStats = useCallback((postId: string, updates: { views?: number; likes?: string[]; shares?: number }) => {
    setBusinessPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, ...updates }
          : post
      )
    );
    setFilteredPosts(prevPosts =>
      prevPosts.map(post =>
        post.id === postId
          ? { ...post, ...updates }
          : post
      )
    );
  }, []);

  const handlePostPress = async (post: BusinessPost, currentViewCount?: number, currentLikeCount?: number, currentShareCount?: number, currentIsLiked?: boolean) => {
    setSelectedPost(post);

    // Initialize modal statistics with current values from card or post data
    const viewCount = currentViewCount || (post.views || 0) + 1;
    const likeCount = currentLikeCount !== undefined ? currentLikeCount : post.likes.length;
    const shareCount = currentShareCount !== undefined ? currentShareCount : (post.shares || 0);
    const isLiked = currentIsLiked !== undefined ? currentIsLiked : post.likes.includes(currentUser?.id || '');

    setModalViewCount(viewCount);
    setModalLikeCount(likeCount);
    setModalShareCount(shareCount);
    setModalIsLiked(isLiked);

    // Update the post in the main array
    updatePostStats(post.id, { views: viewCount });

    // Increment view count in backend
    try {
      // Note: incrementViews method needs to be added to businessService
      console.log('📊 Post viewed:', post.id, 'View count:', viewCount);
      // TODO: await businessService.incrementViews(post.id);
    } catch (error) {
      console.error('❌ Error incrementing views:', error);
    }
  };

  const handleAddPost = () => {
    if (addPostDebounce) return; // Prevent multiple calls

    if (!currentUser) {
      Alert.alert('Authentication Required', 'Please sign in to add a business post.');
      return;
    }

    if (!activeBusinessProfile && !registrationForced) {
      setAddPostDebounce(true);
      setRegistrationForced(true);
      setModalStable(true);
      setShowRegistration(true);

      // Clear any existing timeout
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // Reset debounce after 2 seconds
      debounceTimeoutRef.current = setTimeout(() => {
        setAddPostDebounce(false);
        debounceTimeoutRef.current = null;
      }, 2000);
    } else if (activeBusinessProfile) {
      // If user has multiple businesses, show business selector first
      if (userBusinessProfiles.length > 1) {
        setBusinessSelectorForAddPost(true);
        setShowBusinessSelector(true);
      } else {
        setShowAddPost(true);
      }
    }
  };

  const handleChatPress = (businessId: string) => {
    try {
      navigationService.openChat(businessId, false);
    } catch (error) {
      console.error('❌ Error opening chat:', error);
      Alert.alert('Error', 'Failed to open chat. Please try again.');
    }
  };

  const handleBusinessNamePress = async (post: BusinessPost) => {
    try {
      // Find the business profile for this post
      const businessProfile = await businessService.getBusinessProfileById(post.businessId);
      if (businessProfile) {
        setSelectedBusinessProfile(businessProfile);
        setShowBusinessProfile(true);
      }
    } catch (error) {
      console.error('Error loading business profile:', error);
      Alert.alert('Error', 'Failed to load business profile. Please try again.');
    }
  };

  const handleBusinessRegistration = useCallback((profile: BusinessProfile) => {
    console.log('🏢 Business registration successful, closing modal');

    // Clear any pending debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
      debounceTimeoutRef.current = null;
    }

    // Close modal immediately
    setShowRegistration(false);
    setRegistrationForced(false);
    setModalStable(false);
    setAddPostDebounce(false);

    // Clear the global flag
    (global as any).businessModalOpen = false;

    // Set profile after a small delay to prevent race conditions
    setTimeout(() => {
      setUserBusinessProfiles(prev => [...prev, profile]);
      setActiveBusinessProfile(profile);
      // Refresh business profile data from storage
      loadUserBusinessProfiles();
      Alert.alert('Success', 'Business profile created successfully!');
    }, 100);
  }, [loadUserBusinessProfiles]);

  // Handle modal like action
  const handleModalLike = useCallback(async () => {
    if (!currentUser?.id || !selectedPost) {
      showStyledAlert('Login Required', 'Please login to like posts', [{ text: 'OK' }]);
      return;
    }

    try {
      const result = await businessService.toggleLike(selectedPost.id, currentUser.id);
      if (result.success) {
        const newIsLiked = !modalIsLiked;
        const newLikeCount = modalIsLiked ? modalLikeCount - 1 : modalLikeCount + 1;

        setModalIsLiked(newIsLiked);
        setModalLikeCount(newLikeCount);

        // Update the main posts array
        const updatedLikes = newIsLiked
          ? [...selectedPost.likes, currentUser.id]
          : selectedPost.likes.filter(id => id !== currentUser.id);

        updatePostStats(selectedPost.id, { likes: updatedLikes });
      }
    } catch (error) {
      console.error('❌ Error toggling like:', error);
      showStyledAlert('Error', 'Failed to update like. Please try again.', [{ text: 'OK' }]);
    }
  }, [currentUser, selectedPost, modalIsLiked, modalLikeCount, updatePostStats]);

  // Handle modal share action
  const handleModalShare = useCallback(async () => {
    if (!selectedPost) return;

    try {
      const result = await Share.share({
        message: `Check out this ${selectedPost.businessType} post: ${selectedPost.title}\n\n${selectedPost.description}`,
        title: selectedPost.title,
      });

      if (result.action === Share.sharedAction) {
        const newShareCount = modalShareCount + 1;
        setModalShareCount(newShareCount);
        updatePostStats(selectedPost.id, { shares: newShareCount });

        try {
          await businessService.incrementShares(selectedPost.id);
        } catch (error) {
          console.error('❌ Error updating share count:', error);
        }
      }
    } catch (error) {
      console.error('❌ Error sharing post:', error);
      showStyledAlert('Error', 'Failed to share post. Please try again.', [{ text: 'OK' }]);
    }
  }, [selectedPost, modalShareCount, updatePostStats]);

  // Handle price update
  const handlePriceUpdate = useCallback(async (newPrice: number, oldPrice: number) => {
    if (!priceAdjustmentPost) return;

    try {
      // Update the post with new price and price history
      const priceHistory = priceAdjustmentPost.priceHistory || [];
      const updatedPriceHistory = [
        ...priceHistory,
        {
          price: oldPrice,
          date: new Date(),
          reason: newPrice > oldPrice ? 'Price increased' : 'Price reduced'
        }
      ];

      const updateData = {
        price: newPrice,
        priceHistory: updatedPriceHistory,
        oldPrice: oldPrice, // Store old price for strikethrough display
      };

      // Update in backend
      const result = await businessService.posts.updatePost(priceAdjustmentPost.id, updateData);

      if (result.success) {
        // Update local state
        const updatedPost = {
          ...priceAdjustmentPost,
          ...updateData
        };

        setBusinessPosts(prev =>
          prev.map(p => p.id === priceAdjustmentPost.id ? updatedPost : p)
        );

        // Update selected post if it's the same
        if (selectedPost?.id === priceAdjustmentPost.id) {
          setSelectedPost(updatedPost);
        }

        showStyledAlert('Success', 'Price updated successfully!', [{ text: 'OK' }]);
      } else {
        showStyledAlert('Error', 'Failed to update price. Please try again.', [{ text: 'OK' }]);
      }
    } catch (error) {
      console.error('❌ Error updating price:', error);
      showStyledAlert('Error', 'Failed to update price. Please try again.', [{ text: 'OK' }]);
    }
  }, [priceAdjustmentPost, selectedPost, businessService]);



  // Type guard to check if a post is pending
  const isPendingPost = (post: BusinessPost): post is BusinessPost & { _syncStatus?: string; _tempId?: string } => {
    return '_syncStatus' in post && '_tempId' in post;
  };

  // Get pending posts for display
  const getPendingPosts = useCallback(async (): Promise<BusinessPost[]> => {
    try {
      const pendingPosts = await postStorageService.getPendingPosts();
      return pendingPosts.map((pendingPost: PendingPost) => ({
        ...pendingPost,
        id: pendingPost.tempId,
        createdAt: pendingPost.localCreatedAt,
        updatedAt: pendingPost.localCreatedAt,
        _syncStatus: pendingPost.syncStatus,
        _tempId: pendingPost.tempId,
      } as BusinessPost & { _syncStatus?: string; _tempId?: string }));
    } catch (error) {
      console.error('❌ Error getting pending posts:', error);
      return [];
    }
  }, []);

  // Simple toast function
  const showToast = (message: string) => {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    } else {
      Alert.alert('Info', message, [{ text: 'OK' }]);
    }
  };

  // Download state management
  const [downloadingMedia, setDownloadingMedia] = useState<Set<string>>(new Set());
  const [downloadedMedia, setDownloadedMedia] = useState<Set<string>>(new Set());
  const [downloadProgress, setDownloadProgress] = useState<Record<string, number>>({});

  // Download media function
  const downloadMedia = useCallback(async (mediaUrl: string, mediaId: string) => {
    if (downloadingMedia.has(mediaId) || downloadedMedia.has(mediaId)) {
      return; // Already downloading or downloaded
    }

    setDownloadingMedia(prev => new Set(prev).add(mediaId));
    setDownloadProgress(prev => ({ ...prev, [mediaId]: 0 }));

    try {
      // Simulate download progress (replace with actual download logic)
      for (let progress = 0; progress <= 100; progress += 10) {
        setDownloadProgress(prev => ({ ...prev, [mediaId]: progress }));
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Mark as downloaded
      setDownloadedMedia(prev => new Set(prev).add(mediaId));
      showToast('Media downloaded successfully');
    } catch (error) {
      console.error('Download failed:', error);
      showToast('Download failed. Please try again.');
    } finally {
      setDownloadingMedia(prev => {
        const newSet = new Set(prev);
        newSet.delete(mediaId);
        return newSet;
      });
      setDownloadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[mediaId];
        return newProgress;
      });
    }
  }, [downloadingMedia, downloadedMedia, showToast]);





  // Convert search filters to business service filters
  const convertSearchFilters = useCallback((filters: SearchFilters) => {
    // Map sortBy values to match business service expectations
    let sortBy: 'newest' | 'oldest' | 'price_low' | 'price_high' | 'most_liked' | 'most_viewed' | undefined;
    switch (filters.sortBy) {
      case 'popular':
        sortBy = 'most_liked';
        break;
      default:
        sortBy = filters.sortBy as any;
        break;
    }

    return {
      category: filters.category,
      businessType: filters.businessType,
      priceRange: filters.priceRange,
      location: filters.location ? { city: filters.location } : undefined,
      sortBy,
    };
  }, []);

  // Search and filter posts
  const searchAndFilterPosts = useCallback((posts: BusinessPost[], filters: SearchFilters): BusinessPost[] => {
    let filtered = [...posts];

    // Text search
    if (filters.searchText.trim()) {
      const searchText = filters.searchText.toLowerCase().trim();
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchText) ||
        post.description.toLowerCase().includes(searchText) ||
        post.businessName.toLowerCase().includes(searchText) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchText))
      );
    }

    // Category filter
    if (filters.category) {
      filtered = filtered.filter(post => post.category === filters.category);
    }

    // Business type filter
    if (filters.businessType) {
      filtered = filtered.filter(post => post.businessType === filters.businessType);
    }

    // Price range filter
    if (filters.priceRange.min > 0 || filters.priceRange.max < 1000000) {
      filtered = filtered.filter(post => {
        if (!post.price) return filters.priceRange.min === 0;
        return post.price >= filters.priceRange.min && post.price <= filters.priceRange.max;
      });
    }

    // Location filter
    if (filters.location) {
      const locationText = filters.location.toLowerCase();
      filtered = filtered.filter(post =>
        post.location.city?.toLowerCase().includes(locationText) ||
        post.location.district?.toLowerCase().includes(locationText) ||
        post.location.address?.toLowerCase().includes(locationText)
      );
    }

    // Availability filter
    if (filters.availability) {
      filtered = filtered.filter(post => post.availability === filters.availability);
    }

    // Negotiable filter
    if (filters.isNegotiable !== undefined) {
      filtered = filtered.filter(post => post.isNegotiable === filters.isNegotiable);
    }

    // Sort posts
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'newest':
          return b.createdAt.getTime() - a.createdAt.getTime();
        case 'oldest':
          return a.createdAt.getTime() - b.createdAt.getTime();
        case 'price_low':
          return (a.price || 0) - (b.price || 0);
        case 'price_high':
          return (b.price || 0) - (a.price || 0);
        case 'popular':
          return (b.likes.length + b.views) - (a.likes.length + a.views);
        default:
          return b.createdAt.getTime() - a.createdAt.getTime();
      }
    });

    return filtered;
  }, []);

  // Handle search
  const handleSearch = useCallback((filters: SearchFilters) => {
    console.log('🔍 Searching with filters:', filters);
    const filtered = searchAndFilterPosts(businessPosts, filters);
    setFilteredPosts(filtered);
    setSearchResultsCount(filtered.length);
  }, [businessPosts, searchAndFilterPosts]);

  // Handle filter changes
  const handleFiltersChange = useCallback((filters: SearchFilters) => {
    setSearchFilters(filters);
    filtersRef.current = filters;
  }, []);

  // Handle search icon press
  const handleSearchPress = useCallback(() => {
    setShowSearchBar(!showSearchBar);
  }, [showSearchBar]);

  // Handle filter icon press
  const handleFilterPress = useCallback(() => {
    setShowFiltersModal(true);
  }, []);

  // Check if there are active filters
  const hasActiveFilters = useCallback(() => {
    const filters = searchFilters;
    return !!(
      filters.searchText ||
      filters.category ||
      filters.businessType ||
      filters.location ||
      filters.availability ||
      filters.isNegotiable !== undefined ||
      filters.priceRange.min > 0 ||
      filters.priceRange.max < 1000000
    );
  }, [searchFilters]);

  // Delete post function
  const handleDeletePost = useCallback(async (post: BusinessPost) => {
    Alert.alert(
      'Delete Product',
      `Are you sure you want to delete "${post.title}"? This action cannot be undone.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete from backend
              const result = await businessService.posts.deletePost(post.id);

              if (!result.success) {
                throw new Error(result.error || 'Failed to delete post');
              }

              // Remove from local state
              setBusinessPosts(prev => prev.filter(p => p.id !== post.id));
              setFilteredPosts(prev => prev.filter(p => p.id !== post.id));

              // Close modal if this post was selected
              if (selectedPost?.id === post.id) {
                setSelectedPost(null);
              }

              Alert.alert('Success', 'Product deleted successfully');
            } catch (error) {
              console.error('❌ Error deleting post:', error);
              Alert.alert('Error', 'Failed to delete product. Please try again.');
            }
          },
        },
      ]
    );
  }, [selectedPost, businessService]);

  // Update post function
  const handleUpdatePost = useCallback(async () => {
    if (!editingPost) return;

    try {
      // Update in backend
      const updateData = {
        title: editingPost.title,
        description: editingPost.description,
        price: editingPost.price,
        isNegotiable: editingPost.isNegotiable,
        updatedAt: new Date(),
      };

      const result = await businessService.posts.updatePost(editingPost.id, updateData);

      if (!result.success) {
        throw new Error(result.error || 'Failed to update post');
      }

      // Update local state
      setBusinessPosts(prev =>
        prev.map(post =>
          post.id === editingPost.id
            ? { ...post, ...editingPost, updatedAt: new Date() }
            : post
        )
      );
      setFilteredPosts(prev =>
        prev.map(post =>
          post.id === editingPost.id
            ? { ...post, ...editingPost, updatedAt: new Date() }
            : post
        )
      );

      setShowEditModal(false);
      setEditingPost(null);
      Alert.alert('Success', 'Product updated successfully');
    } catch (error) {
      console.error('❌ Error updating post:', error);
      Alert.alert('Error', 'Failed to update product. Please try again.');
    }
  }, [editingPost, businessService]);





  // Handle scroll events for scroll to top/bottom functionality
  const handleScroll = useCallback((event: any) => {
    const scrollY = event.nativeEvent.contentOffset.y;

    // Update refs for scroll position tracking
    lastScrollYRef.current = scrollY;

    // Keep bottom bar always visible
    (global as any).hideTabBarOnScroll = false;

    // Update scroll position for scroll to top/bottom buttons
    setCurrentScrollY(scrollY);

    // Show scroll buttons when user has scrolled more than 100px
    setShowScrollButtons(scrollY > 100);
  }, []);

  // Scroll to top function
  const scrollToTop = useCallback(() => {
    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
  }, []);

  // Scroll to bottom function
  const scrollToBottom = useCallback(() => {
    flatListRef.current?.scrollToEnd({ animated: true });
  }, []);

  // Simplified touch handling - no longer needed for filter bar
  const handleTouchStart = useCallback(() => {
    // Keep bottom bar always visible
    (global as any).hideTabBarOnScroll = false;
  }, []);

  // Initialize scroll tracking
  useEffect(() => {
    lastScrollYRef.current = 0;
    scrollVelocityRef.current = 0;
    touchStartYRef.current = 0;
  }, []);

  // Update filtered posts when business posts change
  useEffect(() => {
    if (businessPosts.length > 0) {
      const filtered = searchAndFilterPosts(businessPosts, searchFilters);
      setFilteredPosts(filtered);
      setSearchResultsCount(filtered.length);
    }
  }, [businessPosts, searchFilters, searchAndFilterPosts]);

  // Determine which posts to display
  const postsToDisplay = searchFilters.searchText.trim() ||
                        searchFilters.category ||
                        searchFilters.businessType ||
                        searchFilters.location ||
                        searchFilters.availability ||
                        searchFilters.isNegotiable !== undefined ||
                        searchFilters.sortBy !== 'newest' ||
                        searchFilters.priceRange.min > 0 ||
                        searchFilters.priceRange.max < 1000000
                        ? filteredPosts : businessPosts;



  // Render function for FlatList
  const renderBusinessPost = ({ item }: { item: BusinessPost }) => (
    <BusinessPostCard
      post={item}
      currentUser={currentUser}
      onPostPress={handlePostPress}
      onBusinessNamePress={handleBusinessNamePress}
      COLORS={COLORS}
    />
  );

  // Component is ready to render

  // Smart rendering: Always show the UI, handle loading states inline
  // Removed the full-screen loading state that was causing modal reopening issues

  return (
    <SafeAreaView style={styles.container}>
      <BusinessHeader
        userBusinessProfile={activeBusinessProfile || undefined}
        userBusinessProfiles={userBusinessProfiles}
        paddingTop={insets.top}
        onShowRegistration={() => {
          console.log('🏢 Register business button pressed');
          setEditingBusiness(null); // Clear editing state for new registration
          setShowRegistration(true);
        }}
        onBusinessProfilePress={(business) => {
          console.log('🏢 Business profile pressed for:', business.businessName);
          // Navigate to existing business profile page
          setSelectedBusinessProfile(business);
          setShowBusinessProfile(true);
        }}
        onBusinessSelect={() => setShowBusinessSelector(true)}
        onSearchPress={handleSearchPress}
        onFilterPress={handleFilterPress}
        hasActiveFilters={hasActiveFilters()}
      />


      {/* Business Registration Banner - Show when no business profile */}
      {userBusinessProfiles.length === 0 && (
        <View style={{
          backgroundColor: COLORS.primary,
          margin: 16,
          padding: 16,
          borderRadius: 12,
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          <View style={{ flex: 1 }}>
            <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
              Start Your Business Journey
            </Text>
            <Text style={{ color: 'white', fontSize: 14, opacity: 0.9, marginTop: 4 }}>
              Register your business to start posting products and services
            </Text>
          </View>
          <TouchableOpacity
            style={{
              backgroundColor: 'white',
              paddingHorizontal: 16,
              paddingVertical: 8,
              borderRadius: 20,
              marginLeft: 12,
            }}
            onPress={() => {
              console.log('🏢 Register business banner pressed');
              setShowRegistration(true);
            }}
          >
            <Text style={{ color: COLORS.primary, fontWeight: 'bold' }}>Register</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Expandable Search Component */}
      {showSearchBar && (
        <BusinessSearch
          filters={searchFilters}
          onFiltersChange={handleFiltersChange}
          onSearch={handleSearch}
          totalResults={searchResultsCount}
          hideFilterButton={true}
          onClose={() => setShowSearchBar(false)}
        />
      )}
      <FlatList
        ref={flatListRef}
        data={postsToDisplay}
        renderItem={renderBusinessPost}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={() => (
          <BusinessCategoryFilter
            selectedCategory={selectedCategory as ProductCategory | 'all'}
            onCategoryChange={(category) => {
              setSelectedCategory(category as string);
              // Update search filters to include category
              const newFilters = {
                ...searchFilters,
                category: category === 'all' ? undefined : category as ProductCategory
              };
              setSearchFilters(newFilters);
              filtersRef.current = newFilters;
              handleSearch(newFilters);
            }}
            postCounts={{}} // TODO: Calculate post counts by category
          />
        )}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={() => loadBusinessPosts(true)}
            colors={[COLORS.primary]}
          />
        }
        onEndReached={() => loadBusinessPosts(false)}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        onTouchStart={handleTouchStart}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            {isLoading && businessPosts.length === 0 ? (
              // Inline loading state instead of full-screen
              <>
                <ActivityIndicator size="large" color={COLORS.primary} />
                <Text style={styles.emptySubtitle}>Loading marketplace...</Text>
              </>
            ) : (
              // Normal empty state
              <>
                <Ionicons name="storefront-outline" size={64} color={COLORS.textSecondary} />
                <Text style={styles.emptyTitle}>No products found</Text>
                <Text style={styles.emptySubtitle}>
                  Be the first to list a product!
                </Text>

                {/* Show register button if user doesn't have business profile */}
                {userBusinessProfiles.length === 0 && (
                  <TouchableOpacity
                    style={{
                      backgroundColor: COLORS.primary,
                      paddingHorizontal: 24,
                      paddingVertical: 12,
                      borderRadius: 25,
                      marginTop: 20,
                      flexDirection: 'row',
                      alignItems: 'center',
                      gap: 8,
                    }}
                    onPress={() => {
                      console.log('🏢 Empty state register button pressed');
                      setShowRegistration(true);
                    }}
                  >
                    <Ionicons name="business" size={20} color="white" />
                    <Text style={{ color: 'white', fontSize: 16, fontWeight: 'bold' }}>
                      Register Your Business
                    </Text>
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>
        )}
        ListFooterComponent={() => (
          // Show loading indicator at bottom when loading more
          isLoading && businessPosts.length > 0 ? (
            <View style={businessPostStyles.footerLoading}>
              <ActivityIndicator size="small" color={COLORS.primary} />
              <Text style={businessPostStyles.footerLoadingText}>Loading more...</Text>
            </View>
          ) : null
        )}
      />

      {/* Business Registration Modal */}
      <BusinessRegistrationModal
        visible={showRegistration}
        onClose={() => {
          console.log('🏢 Business registration modal closed by user');

          // Clear any pending debounce timeout
          if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
            debounceTimeoutRef.current = null;
          }

          setShowRegistration(false);
          setEditingBusiness(null); // Clear editing state
          setRegistrationForced(false);
          setModalStable(false);
          setAddPostDebounce(false);
          // Clear the global flag
          (global as any).businessModalOpen = false;
        }}
        onSuccess={handleBusinessRegistration}
        currentUserId={currentUser?.id || ''}
        modalStyles={modalStyles}
        COLORS={COLORS}
        editingBusiness={editingBusiness || undefined}
        selectedBusinessProfile={selectedBusinessProfile}
        setSelectedBusinessProfile={setSelectedBusinessProfile}
        activeBusinessProfile={activeBusinessProfile}
        setActiveBusinessProfile={setActiveBusinessProfile}
        setUserBusinessProfiles={setUserBusinessProfiles}
      />

      {/* Add Business Post Modal */}
      {activeBusinessProfile && (
        <AddBusinessPostModal
          visible={showAddPost}
          onClose={() => setShowAddPost(false)}
          onSuccess={(post: BusinessPost) => {
            console.log('📦 Product added successfully, refreshing data');
            setShowAddPost(false);
            setBusinessPosts(prev => [post, ...prev]);
            // Refresh the business posts to ensure data consistency
            loadBusinessPosts(true);
            Alert.alert('Success', 'Product added successfully!');
          }}
          businessProfile={activeBusinessProfile}
          modalStyles={modalStyles}
          COLORS={COLORS}
        />
      )}

      {/* Optimized Product Detail Modal */}
      <OptimizedProductDetailModal
        visible={!!selectedPost}
        post={selectedPost}
        onClose={() => setSelectedPost(null)}
        onMediaPress={(mediaIndex) => {
          setSelectedMediaIndex(mediaIndex);
          setShowMediaViewer(true);
        }}
        onEditPress={() => {
          if (selectedPost) {
            setEditingPost(selectedPost);
            setShowAddPost(true);
            setSelectedPost(null);
          }
        }}
        onDeletePress={() => selectedPost && handleDeletePost(selectedPost)}
        onDeleteMedia={(mediaId) => {
          if (selectedPost) {
            showStyledAlert(
              'Delete Media',
              'Are you sure you want to delete this media item?',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Delete',
                  style: 'destructive',
                  onPress: async () => {
                    try {
                      // Remove media from the post
                      const updatedMedia = selectedPost.media.filter(m => m.id !== mediaId);
                      const updatedPost = { ...selectedPost, media: updatedMedia };

                      // If no media left, delete the entire post
                      if (updatedMedia.length === 0) {
                        await handleDeletePost(selectedPost);
                        return;
                      }

                      // Update the post with remaining media in backend
                      const result = await businessService.posts.updatePost(selectedPost.id, { media: updatedMedia });

                      if (!result.success) {
                        throw new Error(result.error || 'Failed to update post');
                      }

                      // Update local state
                      setSelectedPost(updatedPost);
                      setBusinessPosts(prev =>
                        prev.map(p => p.id === selectedPost.id ? updatedPost : p)
                      );

                      showStyledAlert('Success', 'Media deleted successfully', [{ text: 'OK' }]);
                    } catch (error) {
                      console.error('❌ Error deleting media:', error);
                      showStyledAlert('Error', 'Failed to delete media. Please try again.', [{ text: 'OK' }]);
                    }
                  }
                }
              ],
              {
                cancelable: true,
                userInterfaceStyle: 'dark' // Add border radius to alert
              }
            );
          }
        }}
        onPriceReductionPress={() => {
          if (selectedPost) {
            setPriceAdjustmentPost(selectedPost);
            setShowPriceAdjustmentModal(true);
            setSelectedPost(null);
          }
        }}
        isOwner={selectedPost?.businessId === activeBusinessProfile?.id}
        modalViewCount={modalViewCount}
        modalLikeCount={modalLikeCount}
        modalShareCount={modalShareCount}
        modalIsLiked={modalIsLiked}
        onLikePress={handleModalLike}
        onSharePress={handleModalShare}
        onBusinessNamePress={(post) => {
          // Close the product detail modal
          setSelectedPost(null);
          // Find and show the business profile
          const businessProfile = userBusinessProfiles.find(profile => profile.businessName === post.businessName);
          if (businessProfile) {
            setSelectedBusinessProfile(businessProfile);
            setShowBusinessProfile(true);
          } else {
            // If not found in user's businesses, create a basic profile for viewing
            const basicProfile: BusinessProfile = {
              id: post.businessId,
              userId: '', // Unknown for external businesses
              businessName: post.businessName,
              businessType: post.businessType,
              description: '',
              logo: post.businessLogo,
              contactInfo: {
                email: post.contact?.email || '',
                primaryPhone: post.contact?.phone || '',
                secondaryPhone: '',
              },
              location: post.location,
              isVerified: post.isVerified,
              verificationDocuments: [],
              totalPosts: 0,
              totalViews: 0,
              totalLikes: 0,
              totalComments: 0,
              totalShares: 0,
              totalDownloads: 0,
              allowDirectMessages: true,
              allowPhoneCalls: true,
              businessHours: [],
              createdAt: new Date(),
              updatedAt: new Date(),
            };
            setSelectedBusinessProfile(basicProfile);
            setShowBusinessProfile(true);
          }
        }}
      />




      {/* Enhanced Media Viewer for Product Details */}
      <EnhancedMediaViewer
        visible={selectedPost && showMediaViewer ? true : false}
        mediaItems={selectedPost ? selectedPost.media.map(media => ({
          id: media.id,
          uri: media.url,
          type: media.type === 'image' ? 'photo' : 'video',
          duration: media.duration,
        })) : []}
        initialIndex={selectedMediaIndex}
        onClose={() => setShowMediaViewer(false)}
      />

      {/* Enhanced Media Viewer for Business Posts */}
      <EnhancedMediaViewer
        visible={showEnhancedMediaViewer}
        mediaItems={selectedPostMedia}
        initialIndex={selectedPostMediaIndex}
        onClose={() => setShowEnhancedMediaViewer(false)}
      />

      {/* Custom Filter Modal */}
      <Modal
        visible={showFiltersModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFiltersModal(false)}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.background }}>
          <View style={modalStyles.header}>
            <TouchableOpacity onPress={() => setShowFiltersModal(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={modalStyles.title}>Filters</Text>
            <TouchableOpacity onPress={() => {
              // Reset filters
              const resetFilters: SearchFilters = {
                searchText: '',
                category: undefined,
                businessType: undefined,
                priceRange: { min: 0, max: 1000000 },
                location: undefined,
                availability: undefined,
                isNegotiable: undefined,
                sortBy: 'newest',
              };
              setSearchFilters(resetFilters);
              filtersRef.current = resetFilters;
              handleSearch(resetFilters);
              setShowFiltersModal(false);
            }}>
              <Text style={{ color: COLORS.primary, fontSize: 16 }}>Reset</Text>
            </TouchableOpacity>
          </View>
          <BusinessSearch
            filters={searchFilters}
            onFiltersChange={handleFiltersChange}
            onSearch={(filters) => {
              handleSearch(filters);
              // Don't close modal automatically - let user close it manually
            }}
            totalResults={searchResultsCount}
          />
        </SafeAreaView>
      </Modal>

      {/* Edit Product Modal */}
      {editingPost && (
        <Modal
          visible={showEditModal}
          animationType="slide"
          presentationStyle="fullScreen"
          onRequestClose={() => setShowEditModal(false)}
        >
          <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.background }}>
            <View style={modalStyles.header}>
              <TouchableOpacity onPress={() => setShowEditModal(false)}>
                <Ionicons name="close" size={24} color={COLORS.text} />
              </TouchableOpacity>
              <Text style={modalStyles.title}>Edit Product</Text>
              <TouchableOpacity onPress={() => handleUpdatePost()}>
                <Text style={{ color: COLORS.primary, fontSize: 16, fontWeight: '600' }}>Save</Text>
              </TouchableOpacity>
            </View>

            <ScrollView
              style={{ flex: 1 }}
              contentContainerStyle={{ padding: 16 }}
            >
              <Text style={modalStyles.sectionTitle}>Product Title</Text>
              <TextInput
                style={modalStyles.input}
                value={editingPost.title}
                onChangeText={(text) => setEditingPost(prev => prev ? { ...prev, title: text } : null)}
                placeholder="Enter product title"
                placeholderTextColor={COLORS.textSecondary}
              />

              <Text style={modalStyles.sectionTitle}>Description</Text>
              <TextInput
                style={[modalStyles.input, { height: 100, textAlignVertical: 'top' }]}
                value={editingPost.description}
                onChangeText={(text) => setEditingPost(prev => prev ? { ...prev, description: text } : null)}
                placeholder="Enter product description"
                placeholderTextColor={COLORS.textSecondary}
                multiline
              />

              <Text style={modalStyles.sectionTitle}>Price</Text>
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
                <TextInput
                  style={[modalStyles.input, { flex: 1 }]}
                  value={editingPost.price?.toString() || ''}
                  onChangeText={(text) => setEditingPost(prev => prev ? { ...prev, price: parseFloat(text) || 0 } : null)}
                  placeholder="Enter price"
                  placeholderTextColor={COLORS.textSecondary}
                  keyboardType="numeric"
                />
                <Text style={{ color: COLORS.text, fontSize: 16 }}>{editingPost.currency}</Text>
              </View>

              <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 12 }}>
                <TouchableOpacity
                  style={{ flexDirection: 'row', alignItems: 'center' }}
                  onPress={() => setEditingPost(prev => prev ? { ...prev, isNegotiable: !prev.isNegotiable } : null)}
                >
                  <Ionicons
                    name={editingPost.isNegotiable ? "checkbox" : "square-outline"}
                    size={20}
                    color={COLORS.primary}
                  />
                  <Text style={{ marginLeft: 8, color: COLORS.text }}>Price is negotiable</Text>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </SafeAreaView>
        </Modal>
      )}

      {/* Price Adjustment Modal */}
      <PriceAdjustmentModal
        visible={showPriceAdjustmentModal}
        post={priceAdjustmentPost}
        onClose={() => {
          setShowPriceAdjustmentModal(false);
          setPriceAdjustmentPost(null);
        }}
        onPriceUpdate={handlePriceUpdate}
      />

      {/* Business Selector Modal */}
      <Modal
        visible={showBusinessSelector}
        animationType="slide"
        presentationStyle="formSheet"
        onRequestClose={() => {
          setShowBusinessSelector(false);
          setBusinessSelectorForAddPost(false);
        }}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.background, width: '100%', maxWidth: 500, alignSelf: 'center' }}>
          <View style={[modalStyles.header, { paddingHorizontal: 32 }]}>
            <TouchableOpacity onPress={() => {
              setShowBusinessSelector(false);
              setBusinessSelectorForAddPost(false);
            }}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={modalStyles.title}>Select Business</Text>
            <TouchableOpacity onPress={() => {
              setShowBusinessSelector(false);
              setShowRegistration(true);
            }}>
              <Text style={{ color: COLORS.primary, fontSize: 16, fontWeight: '600' }}>+ Add New</Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            style={{ flex: 1, maxHeight: '80%', width: '100%' }}
            contentContainerStyle={{ paddingHorizontal: 32, paddingVertical: 24, paddingBottom: 40, width: '100%' }}
            showsVerticalScrollIndicator={true}
            bounces={true}
          >
            <Text style={[modalStyles.sectionTitle, { marginLeft: 0, marginBottom: 20, fontSize: 18, fontWeight: 'bold' }]}>Your Businesses</Text>

            {userBusinessProfiles.map((profile) => (
              <TouchableOpacity
                key={profile.id}
                style={{
                  backgroundColor: activeBusinessProfile?.id === profile.id ? COLORS.primaryLight : COLORS.surface,
                  paddingHorizontal: 20,
                  paddingVertical: 18,
                  borderRadius: 16,
                  marginBottom: 16,
                  borderWidth: activeBusinessProfile?.id === profile.id ? 2 : 1,
                  borderColor: activeBusinessProfile?.id === profile.id ? COLORS.primary : COLORS.border,
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 2 },
                  shadowOpacity: 0.1,
                  shadowRadius: 4,
                  elevation: 3,
                  width: '100%',
                }}
                onPress={() => {
                  setActiveBusinessProfile(profile);
                  setShowBusinessSelector(false);
                  setBusinessSelectorForAddPost(false);
                  // If this was for add post, open add post modal
                  if (businessSelectorForAddPost) {
                    setShowAddPost(true);
                  }
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                  {/* Business Logo */}
                  <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
                    <View style={{
                      width: 48,
                      height: 48,
                      borderRadius: 24,
                      backgroundColor: COLORS.primary + '20',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginRight: 16,
                    }}>
                      {profile.logo ? (
                        <Image
                          source={{ uri: profile.logo }}
                          style={{ width: 48, height: 48, borderRadius: 24 }}
                        />
                      ) : (
                        <Ionicons name="business-outline" size={24} color={COLORS.primary} />
                      )}
                    </View>

                    {/* Business Info */}
                    <View style={{ flex: 1, minWidth: 0 }}>
                      <Text
                        style={{
                          fontSize: 18,
                          fontWeight: 'bold',
                          color: activeBusinessProfile?.id === profile.id ? COLORS.primary : COLORS.text,
                          marginBottom: 4,
                        }}
                        numberOfLines={2}
                        ellipsizeMode="tail"
                      >
                        {profile.businessName}
                      </Text>
                      <Text style={{
                        fontSize: 14,
                        color: COLORS.textSecondary,
                        textTransform: 'uppercase',
                        fontWeight: '600',
                        marginBottom: 4,
                      }}>
                        {profile.businessType}
                      </Text>
                      <Text style={{
                        fontSize: 12,
                        color: COLORS.textSecondary,
                      }}>
                        {profile.totalPosts} posts • {profile.totalViews} views
                      </Text>
                    </View>
                  </View>

                  {/* Active Indicator */}
                  {activeBusinessProfile?.id === profile.id && (
                    <Ionicons name="checkmark-circle" size={24} color={COLORS.primary} />
                  )}
                </View>
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              style={{
                backgroundColor: COLORS.surface,
                paddingHorizontal: 20,
                paddingVertical: 20,
                borderRadius: 16,
                marginTop: 16,
                borderWidth: 2,
                borderColor: COLORS.primary,
                borderStyle: 'dashed',
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 3,
              }}
              onPress={() => {
                setShowBusinessSelector(false);
                setShowRegistration(true);
              }}
            >
              <Ionicons name="add-circle" size={32} color={COLORS.primary} />
              <Text style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: COLORS.primary,
                marginTop: 8
              }}>
                Register New Business
              </Text>
              <Text style={{
                fontSize: 14,
                color: COLORS.textSecondary,
                marginTop: 4,
                textAlign: 'center'
              }}>
                Expand your business portfolio with different types of businesses
              </Text>
            </TouchableOpacity>
          </ScrollView>
        </SafeAreaView>
      </Modal>

      {/* Business Profile Page */}
      {selectedBusinessProfile && (
        <BusinessProfilePage
          visible={showBusinessProfile}
          businessProfile={selectedBusinessProfile}
          onClose={() => {
            setShowBusinessProfile(false);
            setSelectedBusinessProfile(null);
          }}
          onPostPress={(post) => {
            setShowBusinessProfile(false);
            handlePostPress(post);
          }}
          isOwner={selectedBusinessProfile.userId === currentUser?.id}
          onEditBusiness={() => {
            setShowBusinessProfile(false);
            setEditingBusiness(selectedBusinessProfile);
            setShowRegistration(true);
          }}
          onDeleteBusiness={handleDeleteBusiness}
          onAddProduct={() => {
            setShowBusinessProfile(false);
            setShowAddPost(true);
          }}
        />
      )}

      {/* Scroll to Top/Bottom Buttons */}
      {showScrollButtons && (
        <View style={styles.scrollButtonsContainer}>
          <TouchableOpacity
            style={[styles.scrollButton, styles.scrollToTopButton]}
            onPress={scrollToTop}
            activeOpacity={0.8}
          >
            <Ionicons name="chevron-up" size={24} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.scrollButton, styles.scrollToBottomButton]}
            onPress={scrollToBottom}
            activeOpacity={0.8}
          >
            <Ionicons name="chevron-down" size={24} color="white" />
          </TouchableOpacity>
        </View>
      )}

    </SafeAreaView>
  );
}