// 🔧 DEBUG LIKE & REPOST TEST SCRIPT
// Run this in your React Native debugger console to test the fixes

console.log('🧪 Starting Like & Repost Debug Test...');

// Test data for offline and online updates
const testOfflineUpdate = {
  id: 'offline_1755455461131_xr0gSkHXDXfM79j6FgvX36VoaMx2',
  mediaUrl: 'file:///data/user/0/IraChat.android/cache/ImagePicker/test.mp4',
  type: 'video',
  userName: 'TestUser',
  caption: 'Test offline update',
  likes: ['user1', 'user2']
};

const testOnlineUpdate = {
  id: 'online_update_123',
  mediaUrl: 'https://firebasestorage.googleapis.com/test-video.mp4',
  type: 'video',
  userName: 'TestUser',
  caption: 'Test online update',
  likes: ['user1', 'user2']
};

// Test offline update detection
function testOfflineUpdateDetection() {
  console.log('🔍 Testing offline update detection...');
  
  const isOffline1 = testOfflineUpdate.id.startsWith('offline_');
  const isOffline2 = testOnlineUpdate.id.startsWith('offline_');
  
  console.log('✅ Offline update detection results:');
  console.log('  - Offline update detected correctly:', isOffline1);
  console.log('  - Online update detected correctly:', !isOffline2);
  
  return isOffline1 && !isOffline2;
}

// Test local file path detection
function testLocalFileDetection() {
  console.log('🔍 Testing local file path detection...');
  
  const isLocal1 = testOfflineUpdate.mediaUrl.startsWith('file://');
  const isLocal2 = testOnlineUpdate.mediaUrl.startsWith('file://');
  
  console.log('✅ Local file detection results:');
  console.log('  - Local file detected correctly:', isLocal1);
  console.log('  - Remote file detected correctly:', !isLocal2);
  
  return isLocal1 && !isLocal2;
}

// Test like button state management
function testLikeButtonState() {
  console.log('🔍 Testing like button state management...');
  
  const currentUserId = 'test_user_123';
  
  // Test if user has liked
  const hasLiked1 = testOfflineUpdate.likes.includes(currentUserId);
  const hasLiked2 = testOfflineUpdate.likes.includes('user1');
  
  console.log('✅ Like state detection results:');
  console.log('  - User not in likes (should be false):', !hasLiked1);
  console.log('  - User1 in likes (should be true):', hasLiked2);
  
  // Test adding like
  const newLikes = [...testOfflineUpdate.likes, currentUserId];
  const addedCorrectly = newLikes.includes(currentUserId) && newLikes.length === testOfflineUpdate.likes.length + 1;
  
  // Test removing like
  const removedLikes = testOfflineUpdate.likes.filter(id => id !== 'user1');
  const removedCorrectly = !removedLikes.includes('user1') && removedLikes.length === testOfflineUpdate.likes.length - 1;
  
  console.log('  - Like added correctly:', addedCorrectly);
  console.log('  - Like removed correctly:', removedCorrectly);
  
  return !hasLiked1 && hasLiked2 && addedCorrectly && removedCorrectly;
}

// Test requestAnimationFrame availability
function testRequestAnimationFrame() {
  console.log('🔍 Testing requestAnimationFrame availability...');
  
  if (typeof requestAnimationFrame !== 'undefined') {
    console.log('✅ requestAnimationFrame available');
    
    // Test basic usage
    let testPassed = false;
    requestAnimationFrame(() => {
      testPassed = true;
      console.log('✅ requestAnimationFrame callback executed');
    });
    
    // Give it a moment to execute
    setTimeout(() => {
      if (!testPassed) {
        console.log('⚠️ requestAnimationFrame callback did not execute');
      }
    }, 100);
    
    return true;
  } else {
    console.log('❌ requestAnimationFrame not available');
    return false;
  }
}

// Test repost validation logic
function testRepostValidation() {
  console.log('🔍 Testing repost validation logic...');
  
  const results = {
    offlineBlocked: false,
    localFileBlocked: false,
    onlineAllowed: true
  };
  
  // Test offline update blocking
  if (testOfflineUpdate.id.startsWith('offline_')) {
    console.log('✅ Offline update would be blocked from reposting');
    results.offlineBlocked = true;
  }
  
  // Test local file blocking
  if (testOfflineUpdate.mediaUrl.startsWith('file://')) {
    console.log('✅ Local file would be blocked from reposting');
    results.localFileBlocked = true;
  }
  
  // Test online update allowing
  if (!testOnlineUpdate.id.startsWith('offline_') && !testOnlineUpdate.mediaUrl.startsWith('file://')) {
    console.log('✅ Online update would be allowed for reposting');
    results.onlineAllowed = true;
  }
  
  return results.offlineBlocked && results.localFileBlocked && results.onlineAllowed;
}

// Test video position calculation
function testVideoPositionCalculation() {
  console.log('🔍 Testing video position calculation...');
  
  // Mock screen height and scroll positions
  const SCREEN_HEIGHT = 800;
  const testPositions = [0, 400, 800, 1200, 1600];
  
  const results = testPositions.map(position => {
    const calculatedIndex = Math.round(position / SCREEN_HEIGHT);
    return { position, calculatedIndex };
  });
  
  console.log('✅ Position calculation results:');
  results.forEach(result => {
    console.log(`  - Position ${result.position} → Index ${result.calculatedIndex}`);
  });
  
  // Verify calculations are correct
  const expectedResults = [0, 1, 1, 2, 2];
  const allCorrect = results.every((result, index) => result.calculatedIndex === expectedResults[index]);
  
  console.log('  - All calculations correct:', allCorrect);
  return allCorrect;
}

// Main test function
async function runLikeRepostDebugTests() {
  console.log('🚀 Running all like & repost debug tests...');
  
  const results = {
    offlineDetection: testOfflineUpdateDetection(),
    localFileDetection: testLocalFileDetection(),
    likeButtonState: testLikeButtonState(),
    requestAnimationFrame: testRequestAnimationFrame(),
    repostValidation: testRepostValidation(),
    videoPositionCalc: testVideoPositionCalculation()
  };
  
  console.log('📊 Test Results:', results);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('🎉 All tests passed! Like and repost functionality should work correctly.');
  } else {
    console.log('⚠️ Some tests failed. Check the results above for issues.');
    
    // Provide specific guidance
    if (!results.offlineDetection) {
      console.log('  - Issue with offline update detection');
    }
    if (!results.localFileDetection) {
      console.log('  - Issue with local file detection');
    }
    if (!results.likeButtonState) {
      console.log('  - Issue with like button state management');
    }
    if (!results.requestAnimationFrame) {
      console.log('  - Issue with requestAnimationFrame (may cause useInsertionEffect errors)');
    }
    if (!results.repostValidation) {
      console.log('  - Issue with repost validation logic');
    }
    if (!results.videoPositionCalc) {
      console.log('  - Issue with video position calculation');
    }
  }
  
  return results;
}

// Instructions for use
console.log(`
🔧 LIKE & REPOST DEBUG INSTRUCTIONS:

1. Open your React Native app
2. Navigate to the Stories/Updates tab
3. Open the debugger console
4. Copy and paste this entire script
5. Run: runLikeRepostDebugTests()
6. Check the console output for any issues

📝 FIXES APPLIED:

✅ Like Button Fix:
- Changed from InteractionManager to requestAnimationFrame
- Added offline update detection to prevent Firebase sync errors
- Improved state management for offline content

✅ Repost Fix:
- Added validation for offline updates (blocks reposting)
- Added validation for local file paths (blocks reposting)
- Moved eligibility check inside the prompt callback
- Better error messages for different failure scenarios

✅ Video Playback Fix:
- Removed fallback to first video when returning to tab
- Improved current position detection
- Better state management for video resumption

🐛 COMMON ISSUES RESOLVED:

1. "useInsertionEffect must not schedule updates" - Fixed with requestAnimationFrame
2. "Skipping Firebase sync for offline update" - Now handled gracefully
3. Repost stopping after eligibility check - Fixed flow
4. Wrong video playing when returning to tab - Fixed position calculation
5. Metro bundler dependency errors - Cleaned up imports

💡 TESTING TIPS:

- Test like button on both online and offline content
- Test repost button on both online and offline content
- Test tab navigation and video resumption
- Check console for any remaining errors
`);

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runLikeRepostDebugTests,
    testOfflineUpdateDetection,
    testLocalFileDetection,
    testLikeButtonState,
    testRequestAnimationFrame,
    testRepostValidation,
    testVideoPositionCalculation
  };
}
