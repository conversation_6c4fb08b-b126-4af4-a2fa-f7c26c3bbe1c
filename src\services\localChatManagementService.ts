import AsyncStorage from '@react-native-async-storage/async-storage';
// Firebase imports - will be configured later
// import { getFirestore, collection, doc, setDoc, getDoc, updateDoc, deleteDoc, query, where, orderBy, onSnapshot } from 'firebase/firestore';
// import { getAuth } from 'firebase/auth';

export interface LocalChatData {
  id: string;
  name: string;
  avatar?: string;
  lastMessage: string;
  timestamp: Date;
  messageCount: number;
  mediaCount: number;
  isGroup: boolean;
  participants: string[];
  
  // Local management flags
  isArchived: boolean;
  isMuted: boolean;
  isPinned: boolean;
  isLocked: boolean;
  isHidden: boolean;
  isDeleted: boolean;
  
  // Local timestamps
  archivedAt?: Date;
  mutedAt?: Date;
  pinnedAt?: Date;
  lockedAt?: Date;
  hiddenAt?: Date;
  deletedAt?: Date;
  
  // Local settings
  lockPin?: string;
  customName?: string;
  
  // Sync status
  needsSync: boolean;
  lastSyncAt?: Date;
}

export interface LocalChatMessage {
  id: string;
  chatId: string;
  text: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  type: 'text' | 'image' | 'video' | 'audio' | 'document';
  fileName?: string;
  fileUrl?: string;
  
  // Local flags
  isDeleted: boolean;
  deletedAt?: Date;
  
  // Sync status
  needsSync: boolean;
}

export interface SearchFilters {
  searchType: 'all' | 'chats' | 'messages' | 'contacts' | 'media';
  chatType: 'all' | 'individual' | 'groups';
  timeRange: 'all' | 'today' | 'week' | 'month' | 'year';
  messageType: 'all' | 'text' | 'images' | 'videos' | 'documents' | 'audio';
  sortBy: 'relevance' | 'date' | 'name';
  includeArchived: boolean;
  includeMuted: boolean;
  includeHidden: boolean;
  includeDeleted: boolean;
}

export interface LocalSearchResult {
  id: string;
  type: 'chat' | 'message' | 'media';
  title: string;
  subtitle?: string;
  content?: string;
  chatId?: string;
  messageId?: string;
  timestamp?: Date;
  relevanceScore: number;
}

export interface ChatSettings {
  defaultSearchFilters: SearchFilters;
  autoArchiveAfterDays: number;
  maxCacheSize: number;
  enableOfflineMode: boolean;
  syncInterval: number;
  notificationSettings: {
    enableNotifications: boolean;
    muteAll: boolean;
    soundEnabled: boolean;
    vibrationEnabled: boolean;
  };
  privacySettings: {
    hideLastSeen: boolean;
    hideReadReceipts: boolean;
    requirePinForLockedChats: boolean;
  };
  displaySettings: {
    theme: 'light' | 'dark' | 'auto';
    fontSize: 'small' | 'medium' | 'large';
    showAvatars: boolean;
    compactMode: boolean;
  };
}

export interface SyncResult {
  success: boolean;
  syncedChats: number;
  syncedMessages: number;
  errors: string[];
  lastSyncTime: Date;
}

export interface ConflictResolution {
  strategy: 'local_wins' | 'remote_wins' | 'merge' | 'manual';
  resolvedConflicts: number;
}

export interface FirebaseConfig {
  isConfigured: boolean;
  isOnline: boolean;
  isAuthenticated: boolean;
  userId?: string;
}

class LocalChatManagementService {
  private readonly CHATS_KEY = 'local_chats';
  private readonly MESSAGES_KEY = 'local_messages';
  private readonly SETTINGS_KEY = 'chat_settings';
  private readonly SYNC_STATUS_KEY = 'sync_status';

  private chatsCache: LocalChatData[] = [];
  private messagesCache: Map<string, LocalChatMessage[]> = new Map();
  private settingsCache: ChatSettings | null = null;
  private isInitialized = false;

  // Firebase sync properties
  private firebaseConfig: FirebaseConfig = {
    isConfigured: false,
    isOnline: false,
    isAuthenticated: false,
  };
  private syncInProgress = false;
  private lastSyncAttempt: Date | null = null;
  private syncListeners: Map<string, () => void> = new Map();

  /**
   * Initialize the service and load cached data
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadChatsFromStorage();
      await this.loadMessagesFromStorage();
      await this.loadSettingsFromStorage();

      // If no settings exist, create default settings
      if (!this.settingsCache) {
        await this.createDefaultSettings();
      }

      this.isInitialized = true;
      console.log('✅ Local chat management service initialized');
    } catch (error) {
      console.error('❌ Error initializing local chat management:', error);
    }
  }



  /**
   * LOCAL SEARCH FUNCTIONALITY
   */
  async searchLocal(query: string, filters: SearchFilters): Promise<LocalSearchResult[]> {
    await this.initialize();
    
    if (!query.trim()) return [];

    const results: LocalSearchResult[] = [];
    const searchTerm = query.toLowerCase();

    // Search chats
    if (filters.searchType === 'all' || filters.searchType === 'chats') {
      const chatResults = this.searchChatsLocal(searchTerm, filters);
      results.push(...chatResults);
    }

    // Search messages
    if (filters.searchType === 'all' || filters.searchType === 'messages') {
      const messageResults = this.searchMessagesLocal(searchTerm, filters);
      results.push(...messageResults);
    }

    // Search media
    if (filters.searchType === 'all' || filters.searchType === 'media') {
      const mediaResults = this.searchMediaLocal(searchTerm, filters);
      results.push(...mediaResults);
    }

    // Apply time filter
    const filteredResults = this.applyTimeFilter(results, filters.timeRange);

    // Sort results
    return this.sortSearchResults(filteredResults, filters.sortBy, searchTerm);
  }

  private searchChatsLocal(searchTerm: string, filters: SearchFilters): LocalSearchResult[] {
    return this.chatsCache
      .filter(chat => {
        // Apply visibility filters
        if (!filters.includeArchived && chat.isArchived) return false;
        if (!filters.includeMuted && chat.isMuted) return false;
        if (!filters.includeHidden && chat.isHidden) return false;
        if (!filters.includeDeleted && chat.isDeleted) return false;
        
        // Apply chat type filter
        if (filters.chatType === 'individual' && chat.isGroup) return false;
        if (filters.chatType === 'groups' && !chat.isGroup) return false;
        
        // Search in name and custom name
        const name = (chat.customName || chat.name).toLowerCase();
        return name.includes(searchTerm);
      })
      .map(chat => ({
        id: chat.id,
        type: 'chat' as const,
        title: chat.customName || chat.name,
        subtitle: chat.isGroup ? `${chat.participants.length} members` : 'Individual chat',
        chatId: chat.id,
        timestamp: chat.timestamp,
        relevanceScore: this.calculateRelevance(chat.customName || chat.name, searchTerm),
      }));
  }

  private searchMessagesLocal(searchTerm: string, filters: SearchFilters): LocalSearchResult[] {
    const results: LocalSearchResult[] = [];

    for (const [chatId, messages] of this.messagesCache) {
      const chat = this.chatsCache.find(c => c.id === chatId);
      if (!chat) continue;

      // Apply chat visibility filters
      if (!filters.includeArchived && chat.isArchived) continue;
      if (!filters.includeHidden && chat.isHidden) continue;
      if (!filters.includeDeleted && chat.isDeleted) continue;

      messages
        .filter(message => {
          if (message.isDeleted && !filters.includeDeleted) return false;
          if (filters.messageType !== 'all' && message.type !== filters.messageType) return false;
          return message.text.toLowerCase().includes(searchTerm);
        })
        .forEach(message => {
          results.push({
            id: message.id,
            type: 'message',
            title: message.text.substring(0, 100) + (message.text.length > 100 ? '...' : ''),
            subtitle: `In ${chat.customName || chat.name}`,
            content: message.text,
            chatId: chatId,
            messageId: message.id,
            timestamp: message.timestamp,
            relevanceScore: this.calculateRelevance(message.text, searchTerm),
          });
        });
    }

    return results;
  }

  private searchMediaLocal(searchTerm: string, filters: SearchFilters): LocalSearchResult[] {
    const results: LocalSearchResult[] = [];

    for (const [chatId, messages] of this.messagesCache) {
      const chat = this.chatsCache.find(c => c.id === chatId);
      if (!chat) continue;

      if (!filters.includeArchived && chat.isArchived) continue;
      if (!filters.includeHidden && chat.isHidden) continue;
      if (!filters.includeDeleted && chat.isDeleted) continue;

      messages
        .filter(message => {
          if (message.isDeleted && !filters.includeDeleted) return false;
          if (!['image', 'video', 'audio', 'document'].includes(message.type)) return false;
          return message.fileName?.toLowerCase().includes(searchTerm) || false;
        })
        .forEach(message => {
          results.push({
            id: message.id,
            type: 'media',
            title: message.fileName || 'Media file',
            subtitle: `${message.type} in ${chat.customName || chat.name}`,
            chatId: chatId,
            messageId: message.id,
            timestamp: message.timestamp,
            relevanceScore: this.calculateRelevance(message.fileName || '', searchTerm),
          });
        });
    }

    return results;
  }

  /**
   * LOCAL CHAT MANAGEMENT OPERATIONS
   */
  async archiveChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isArchived = true;
        this.chatsCache[chatIndex].archivedAt = new Date();
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Archived ${chatIds.length} chats locally`);
  }

  async unarchiveChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isArchived = false;
        this.chatsCache[chatIndex].archivedAt = undefined;
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Unarchived ${chatIds.length} chats locally`);
  }

  async muteChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isMuted = true;
        this.chatsCache[chatIndex].mutedAt = new Date();
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Muted ${chatIds.length} chats locally`);
  }

  async unmuteChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isMuted = false;
        this.chatsCache[chatIndex].mutedAt = undefined;
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Unmuted ${chatIds.length} chats locally`);
  }

  async pinChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isPinned = true;
        this.chatsCache[chatIndex].pinnedAt = new Date();
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Pinned ${chatIds.length} chats locally`);
  }

  async unpinChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isPinned = false;
        this.chatsCache[chatIndex].pinnedAt = undefined;
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Unpinned ${chatIds.length} chats locally`);
  }

  async hideChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isHidden = true;
        this.chatsCache[chatIndex].hiddenAt = new Date();
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Hidden ${chatIds.length} chats locally`);
  }

  async unhideChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isHidden = false;
        this.chatsCache[chatIndex].hiddenAt = undefined;
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Unhidden ${chatIds.length} chats locally`);
  }

  async lockChats(chatIds: string[], pin: string): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isLocked = true;
        this.chatsCache[chatIndex].lockPin = pin;
        this.chatsCache[chatIndex].lockedAt = new Date();
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Locked ${chatIds.length} chats locally`);
  }

  async unlockChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isLocked = false;
        this.chatsCache[chatIndex].lockPin = undefined;
        this.chatsCache[chatIndex].lockedAt = undefined;
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Unlocked ${chatIds.length} chats locally`);
  }

  async deleteChats(chatIds: string[]): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const chatIndex = this.chatsCache.findIndex(c => c.id === chatId);
      if (chatIndex !== -1) {
        this.chatsCache[chatIndex].isDeleted = true;
        this.chatsCache[chatIndex].deletedAt = new Date();
        this.chatsCache[chatIndex].needsSync = true;
      }
    }
    
    await this.saveChatsToStorage();
    console.log(`✅ Deleted ${chatIds.length} chats locally`);
  }

  async clearChatHistory(chatIds: string[], clearType: 'messages' | 'media' | 'all'): Promise<void> {
    await this.initialize();
    
    for (const chatId of chatIds) {
      const messages = this.messagesCache.get(chatId) || [];
      
      messages.forEach(message => {
        if (clearType === 'all') {
          message.isDeleted = true;
          message.deletedAt = new Date();
          message.needsSync = true;
        } else if (clearType === 'messages' && message.type === 'text') {
          message.isDeleted = true;
          message.deletedAt = new Date();
          message.needsSync = true;
        } else if (clearType === 'media' && ['image', 'video', 'audio', 'document'].includes(message.type)) {
          message.isDeleted = true;
          message.deletedAt = new Date();
          message.needsSync = true;
        }
      });
      
      this.messagesCache.set(chatId, messages);
    }
    
    await this.saveMessagesToStorage();
    console.log(`✅ Cleared ${clearType} from ${chatIds.length} chats locally`);
  }

  /**
   * GET FILTERED CHATS
   */
  async getFilteredChats(filters: {
    includeArchived?: boolean;
    includeMuted?: boolean;
    includeHidden?: boolean;
    includeDeleted?: boolean;
    includeLocked?: boolean;
    chatType?: 'all' | 'individual' | 'groups';
    sortBy?: 'name' | 'date' | 'pinned';
  } = {}): Promise<LocalChatData[]> {
    await this.initialize();
    
    let filtered = this.chatsCache.filter(chat => {
      if (!filters.includeArchived && chat.isArchived) return false;
      if (!filters.includeMuted && chat.isMuted) return false;
      if (!filters.includeHidden && chat.isHidden) return false;
      if (!filters.includeDeleted && chat.isDeleted) return false;
      if (!filters.includeLocked && chat.isLocked) return false;
      
      if (filters.chatType === 'individual' && chat.isGroup) return false;
      if (filters.chatType === 'groups' && !chat.isGroup) return false;
      
      return true;
    });

    // Sort
    if (filters.sortBy === 'name') {
      filtered.sort((a, b) => (a.customName || a.name).localeCompare(b.customName || b.name));
    } else if (filters.sortBy === 'pinned') {
      filtered.sort((a, b) => {
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;
        return b.timestamp.getTime() - a.timestamp.getTime();
      });
    } else {
      filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    }

    return filtered;
  }

  /**
   * STORAGE OPERATIONS
   */
  private async loadChatsFromStorage(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.CHATS_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.chatsCache = parsed.map((chat: any) => ({
          ...chat,
          timestamp: new Date(chat.timestamp),
          archivedAt: chat.archivedAt ? new Date(chat.archivedAt) : undefined,
          mutedAt: chat.mutedAt ? new Date(chat.mutedAt) : undefined,
          pinnedAt: chat.pinnedAt ? new Date(chat.pinnedAt) : undefined,
          lockedAt: chat.lockedAt ? new Date(chat.lockedAt) : undefined,
          hiddenAt: chat.hiddenAt ? new Date(chat.hiddenAt) : undefined,
          deletedAt: chat.deletedAt ? new Date(chat.deletedAt) : undefined,
          lastSyncAt: chat.lastSyncAt ? new Date(chat.lastSyncAt) : undefined,
        }));
      }
    } catch (error) {
      console.error('❌ Error loading chats from storage:', error);
    }
  }

  private async saveChatsToStorage(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.CHATS_KEY, JSON.stringify(this.chatsCache));
    } catch (error) {
      console.error('❌ Error saving chats to storage:', error);
    }
  }

  private async loadMessagesFromStorage(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.MESSAGES_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.messagesCache = new Map();
        
        for (const [chatId, messages] of Object.entries(parsed)) {
          this.messagesCache.set(chatId, (messages as any[]).map(msg => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
            deletedAt: msg.deletedAt ? new Date(msg.deletedAt) : undefined,
          })));
        }
      }
    } catch (error) {
      console.error('❌ Error loading messages from storage:', error);
    }
  }

  private async saveMessagesToStorage(): Promise<void> {
    try {
      const toSave = Object.fromEntries(this.messagesCache);
      await AsyncStorage.setItem(this.MESSAGES_KEY, JSON.stringify(toSave));
    } catch (error) {
      console.error('❌ Error saving messages to storage:', error);
    }
  }

  private async loadSettingsFromStorage(): Promise<void> {
    try {
      const stored = await AsyncStorage.getItem(this.SETTINGS_KEY);
      if (stored) {
        this.settingsCache = JSON.parse(stored);
      }
    } catch (error) {
      console.error('❌ Error loading settings from storage:', error);
    }
  }

  private async saveSettingsToStorage(): Promise<void> {
    try {
      if (this.settingsCache) {
        await AsyncStorage.setItem(this.SETTINGS_KEY, JSON.stringify(this.settingsCache));
      }
    } catch (error) {
      console.error('❌ Error saving settings to storage:', error);
    }
  }

  private async createDefaultSettings(): Promise<void> {
    this.settingsCache = {
      defaultSearchFilters: {
        searchType: 'all',
        chatType: 'all',
        timeRange: 'all',
        messageType: 'all',
        sortBy: 'relevance',
        includeArchived: false,
        includeMuted: true,
        includeHidden: false,
        includeDeleted: false,
      },
      autoArchiveAfterDays: 30,
      maxCacheSize: 1000,
      enableOfflineMode: true,
      syncInterval: 300000, // 5 minutes
      notificationSettings: {
        enableNotifications: true,
        muteAll: false,
        soundEnabled: true,
        vibrationEnabled: true,
      },
      privacySettings: {
        hideLastSeen: false,
        hideReadReceipts: false,
        requirePinForLockedChats: true,
      },
      displaySettings: {
        theme: 'auto',
        fontSize: 'medium',
        showAvatars: true,
        compactMode: false,
      },
    };

    await this.saveSettingsToStorage();
    console.log('✅ Default settings created');
  }

  /**
   * UTILITY METHODS
   */
  private calculateRelevance(text: string, searchTerm: string): number {
    const lowerText = text.toLowerCase();
    const lowerTerm = searchTerm.toLowerCase();
    
    if (lowerText === lowerTerm) return 100;
    if (lowerText.startsWith(lowerTerm)) return 80;
    if (lowerText.includes(` ${lowerTerm}`)) return 60;
    if (lowerText.includes(lowerTerm)) return 40;
    
    const words = lowerTerm.split(' ');
    const matchedWords = words.filter(word => lowerText.includes(word));
    return (matchedWords.length / words.length) * 20;
  }

  private applyTimeFilter(results: LocalSearchResult[], timeRange: string): LocalSearchResult[] {
    if (timeRange === 'all') return results;

    const now = new Date();
    let cutoffDate: Date;

    switch (timeRange) {
      case 'today':
        cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        cutoffDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        cutoffDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        return results;
    }

    return results.filter(result => 
      result.timestamp && result.timestamp >= cutoffDate
    );
  }

  private sortSearchResults(results: LocalSearchResult[], sortBy: string, searchTerm: string): LocalSearchResult[] {
    return results.sort((a, b) => {
      switch (sortBy) {
        case 'relevance':
          // Use searchTerm to boost exact matches in relevance sorting
          const aExactMatch = a.title.toLowerCase() === searchTerm.toLowerCase();
          const bExactMatch = b.title.toLowerCase() === searchTerm.toLowerCase();

          if (aExactMatch && !bExactMatch) return -1;
          if (!aExactMatch && bExactMatch) return 1;

          return b.relevanceScore - a.relevanceScore;
        case 'date':
          if (!a.timestamp && !b.timestamp) return 0;
          if (!a.timestamp) return 1;
          if (!b.timestamp) return -1;
          return b.timestamp.getTime() - a.timestamp.getTime();
        case 'name':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });
  }

  /**
   * SYNC METHODS (for when online)
   */
  async syncWithFirebase(): Promise<SyncResult> {
    console.log('🔄 Starting Firebase sync...');

    if (this.syncInProgress) {
      console.log('⚠️ Sync already in progress, skipping...');
      throw new Error('Sync already in progress');
    }

    this.syncInProgress = true;
    this.lastSyncAttempt = new Date();

    const result: SyncResult = {
      success: false,
      syncedChats: 0,
      syncedMessages: 0,
      errors: [],
      lastSyncTime: new Date(),
    };

    try {
      await this.initialize();

      // Check Firebase configuration and connectivity
      if (!this.firebaseConfig.isConfigured) {
        throw new Error('Firebase not configured. Please configure Firebase first.');
      }

      if (!this.firebaseConfig.isOnline) {
        throw new Error('No internet connection. Sync will retry when online.');
      }

      if (!this.firebaseConfig.isAuthenticated) {
        throw new Error('User not authenticated. Please sign in first.');
      }

      // Get unsynced changes
      const { chats: unsyncedChats, messages: unsyncedMessages } = await this.getUnsyncedChanges();

      console.log(`📊 Found ${unsyncedChats.length} unsynced chats and ${unsyncedMessages.length} unsynced messages`);

      // Sync chats to Firebase
      result.syncedChats = await this.syncChatsToFirebase(unsyncedChats);

      // Sync messages to Firebase
      result.syncedMessages = await this.syncMessagesToFirebase(unsyncedMessages);

      // Pull latest changes from Firebase
      await this.pullChangesFromFirebase();

      // Save sync status
      await this.saveSyncStatus(result.lastSyncTime);

      result.success = true;
      console.log(`✅ Sync completed successfully: ${result.syncedChats} chats, ${result.syncedMessages} messages`);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown sync error';
      result.errors.push(errorMessage);
      console.error('❌ Sync failed:', errorMessage);
    } finally {
      this.syncInProgress = false;
    }

    return result;
  }

  private async syncChatsToFirebase(chats: LocalChatData[]): Promise<number> {
    let syncedCount = 0;

    for (const chat of chats) {
      try {
        // TODO: Replace with actual Firebase operations when configured
        // const db = getFirestore();
        // const chatRef = doc(db, 'chats', chat.id);
        // const firebaseData = this.chatToFirebaseFormat(chat);
        // await setDoc(chatRef, firebaseData, { merge: true });

        // For now, simulate Firebase operation with data transformation
        const firebaseData = this.chatToFirebaseFormat(chat);
        console.log(`📤 Syncing chat to Firebase: ${chat.name}`, { id: firebaseData.id });
        await this.simulateFirebaseOperation();

        // Mark as synced
        const chatIndex = this.chatsCache.findIndex(c => c.id === chat.id);
        if (chatIndex !== -1) {
          this.chatsCache[chatIndex].needsSync = false;
          this.chatsCache[chatIndex].lastSyncAt = new Date();
        }

        syncedCount++;
      } catch (error) {
        console.error(`❌ Failed to sync chat ${chat.id}:`, error);
      }
    }

    if (syncedCount > 0) {
      await this.saveChatsToStorage();
    }

    return syncedCount;
  }

  private async syncMessagesToFirebase(messages: LocalChatMessage[]): Promise<number> {
    let syncedCount = 0;

    for (const message of messages) {
      try {
        // TODO: Replace with actual Firebase operations when configured
        // const db = getFirestore();
        // const messageRef = doc(db, 'messages', message.id);
        // const firebaseData = this.messageToFirebaseFormat(message);
        // await setDoc(messageRef, firebaseData, { merge: true });

        // For now, simulate Firebase operation with data transformation
        const firebaseData = this.messageToFirebaseFormat(message);
        console.log(`📤 Syncing message to Firebase: ${message.id}`, { chatId: firebaseData.chatId });
        await this.simulateFirebaseOperation();

        // Mark as synced
        const chatMessages = this.messagesCache.get(message.chatId);
        if (chatMessages) {
          const messageIndex = chatMessages.findIndex(m => m.id === message.id);
          if (messageIndex !== -1) {
            chatMessages[messageIndex].needsSync = false;
          }
        }

        syncedCount++;
      } catch (error) {
        console.error(`❌ Failed to sync message ${message.id}:`, error);
      }
    }

    if (syncedCount > 0) {
      await this.saveMessagesToStorage();
    }

    return syncedCount;
  }

  private async pullChangesFromFirebase(): Promise<void> {
    try {
      // TODO: Replace with actual Firebase operations when configured
      // const db = getFirestore();
      // const auth = getAuth();
      // const userId = auth.currentUser?.uid;

      // Pull latest chats
      // const chatsQuery = query(
      //   collection(db, 'chats'),
      //   where('participants', 'array-contains', userId),
      //   orderBy('timestamp', 'desc')
      // );

      // Pull latest messages for each chat
      // const messagesQuery = query(
      //   collection(db, 'messages'),
      //   where('chatId', 'in', chatIds),
      //   orderBy('timestamp', 'desc')
      // );

      console.log('📥 Pulling latest changes from Firebase...');
      await this.simulateFirebaseOperation();

      // TODO: Process pulled data and merge with local cache
      // const remoteChats = await this.fetchRemoteChats();
      // const remoteMessages = await this.fetchRemoteMessages();
      // await this.mergeRemoteChanges(remoteChats, remoteMessages);

      // For now, simulate with empty arrays
      await this.mergeRemoteChanges([], []);

    } catch (error) {
      console.error('❌ Failed to pull changes from Firebase:', error);
      throw error;
    }
  }

  private async saveSyncStatus(lastSyncTime: Date): Promise<void> {
    try {
      const syncStatus = {
        lastSyncTime: lastSyncTime.toISOString(),
        lastSyncAttempt: this.lastSyncAttempt?.toISOString(),
        syncInProgress: false,
      };

      await AsyncStorage.setItem(this.SYNC_STATUS_KEY, JSON.stringify(syncStatus));
    } catch (error) {
      console.error('❌ Failed to save sync status:', error);
    }
  }

  private async simulateFirebaseOperation(): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
  }

  async getUnsyncedChanges(): Promise<{ chats: LocalChatData[]; messages: LocalChatMessage[] }> {
    await this.initialize();

    const unsyncedChats = this.chatsCache.filter(chat => chat.needsSync);
    const unsyncedMessages: LocalChatMessage[] = [];

    for (const messages of this.messagesCache.values()) {
      unsyncedMessages.push(...messages.filter(msg => msg.needsSync));
    }

    return { chats: unsyncedChats, messages: unsyncedMessages };
  }

  /**
   * Get all chats from local storage
   */
  async getAllChats(): Promise<LocalChatData[]> {
    await this.initialize();
    return [...this.chatsCache];
  }

  /**
   * Get a specific chat by ID
   */
  async getChatById(chatId: string): Promise<LocalChatData | null> {
    await this.initialize();
    return this.chatsCache.find(chat => chat.id === chatId) || null;
  }

  /**
   * Add or update a chat in local storage
   */
  async saveChat(chatData: Partial<LocalChatData> & { id: string }): Promise<void> {
    await this.initialize();

    const existingIndex = this.chatsCache.findIndex(chat => chat.id === chatData.id);

    if (existingIndex !== -1) {
      // Update existing chat
      this.chatsCache[existingIndex] = {
        ...this.chatsCache[existingIndex],
        ...chatData,
        needsSync: true,
      };
    } else {
      // Add new chat
      const newChat: LocalChatData = {
        name: chatData.name || 'Unknown Chat',
        avatar: chatData.avatar,
        lastMessage: chatData.lastMessage || '',
        timestamp: chatData.timestamp || new Date(),
        messageCount: chatData.messageCount || 0,
        mediaCount: chatData.mediaCount || 0,
        isGroup: chatData.isGroup || false,
        participants: chatData.participants || [],
        isArchived: chatData.isArchived || false,
        isMuted: chatData.isMuted || false,
        isPinned: chatData.isPinned || false,
        isLocked: chatData.isLocked || false,
        isHidden: chatData.isHidden || false,
        isDeleted: chatData.isDeleted || false,
        needsSync: true,
        ...chatData,
      };

      this.chatsCache.push(newChat);
    }

    await this.saveChatsToStorage();
    console.log('✅ Chat saved to local storage:', chatData.id);
  }

  /**
   * SETTINGS MANAGEMENT
   */
  async getSettings(): Promise<ChatSettings> {
    await this.initialize();
    return this.settingsCache!;
  }

  async updateSettings(settings: Partial<ChatSettings>): Promise<void> {
    await this.initialize();

    if (this.settingsCache) {
      this.settingsCache = {
        ...this.settingsCache,
        ...settings,
      };

      await this.saveSettingsToStorage();
      console.log('✅ Settings updated');
    }
  }

  async resetSettings(): Promise<void> {
    await this.initialize();
    await this.createDefaultSettings();
    console.log('✅ Settings reset to defaults');
  }

  /**
   * FIREBASE CONFIGURATION METHODS
   */
  setFirebaseConfig(config: Partial<FirebaseConfig>): void {
    this.firebaseConfig = {
      ...this.firebaseConfig,
      ...config,
    };
    console.log('🔧 Firebase config updated:', this.firebaseConfig);
  }

  getFirebaseConfig(): FirebaseConfig {
    return { ...this.firebaseConfig };
  }

  async checkConnectivity(): Promise<boolean> {
    try {
      // TODO: Replace with actual network check when available
      // For now, assume online if Firebase is configured
      this.firebaseConfig.isOnline = this.firebaseConfig.isConfigured;
      return this.firebaseConfig.isOnline;
    } catch (error) {
      this.firebaseConfig.isOnline = false;
      return false;
    }
  }

  async enableAutoSync(intervalMs: number = 300000): Promise<void> {
    // Auto-sync every 5 minutes by default
    setInterval(async () => {
      if (!this.syncInProgress && this.firebaseConfig.isConfigured && this.firebaseConfig.isOnline) {
        try {
          await this.syncWithFirebase();
        } catch (error) {
          console.log('🔄 Auto-sync failed, will retry next interval');
        }
      }
    }, intervalMs);

    console.log(`🔄 Auto-sync enabled with ${intervalMs / 1000}s interval`);
  }

  async forceSyncNow(): Promise<SyncResult> {
    console.log('🚀 Force sync requested...');
    return await this.syncWithFirebase();
  }

  getSyncStatus(): { inProgress: boolean; lastAttempt: Date | null; lastSuccess: Date | null } {
    return {
      inProgress: this.syncInProgress,
      lastAttempt: this.lastSyncAttempt,
      lastSuccess: null, // TODO: Track from sync status storage
    };
  }

  /**
   * DATA TRANSFORMATION METHODS
   */
  private chatToFirebaseFormat(chat: LocalChatData): any {
    return {
      id: chat.id,
      name: chat.name,
      avatar: chat.avatar,
      lastMessage: chat.lastMessage,
      timestamp: chat.timestamp.toISOString(),
      messageCount: chat.messageCount,
      mediaCount: chat.mediaCount,
      isGroup: chat.isGroup,
      participants: chat.participants,

      // Local management flags
      isArchived: chat.isArchived,
      isMuted: chat.isMuted,
      isPinned: chat.isPinned,
      isLocked: chat.isLocked,
      isHidden: chat.isHidden,
      isDeleted: chat.isDeleted,

      // Timestamps
      archivedAt: chat.archivedAt?.toISOString(),
      mutedAt: chat.mutedAt?.toISOString(),
      pinnedAt: chat.pinnedAt?.toISOString(),
      lockedAt: chat.lockedAt?.toISOString(),
      hiddenAt: chat.hiddenAt?.toISOString(),
      deletedAt: chat.deletedAt?.toISOString(),

      // Settings (exclude sensitive data like lockPin)
      customName: chat.customName,

      // Sync metadata
      lastSyncAt: chat.lastSyncAt?.toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private messageToFirebaseFormat(message: LocalChatMessage): any {
    return {
      id: message.id,
      chatId: message.chatId,
      text: message.text,
      senderId: message.senderId,
      senderName: message.senderName,
      timestamp: message.timestamp.toISOString(),
      type: message.type,
      fileName: message.fileName,
      fileUrl: message.fileUrl,

      // Local flags
      isDeleted: message.isDeleted,
      deletedAt: message.deletedAt?.toISOString(),

      // Sync metadata
      updatedAt: new Date().toISOString(),
    };
  }

  private firebaseToLocalChatFormat(firebaseChat: any): LocalChatData {
    return {
      id: firebaseChat.id,
      name: firebaseChat.name,
      avatar: firebaseChat.avatar || '',
      lastMessage: firebaseChat.lastMessage,
      timestamp: new Date(firebaseChat.timestamp),
      messageCount: firebaseChat.messageCount || 0,
      mediaCount: firebaseChat.mediaCount || 0,
      isGroup: firebaseChat.isGroup || false,
      participants: firebaseChat.participants || [],

      // Local management flags
      isArchived: firebaseChat.isArchived || false,
      isMuted: firebaseChat.isMuted || false,
      isPinned: firebaseChat.isPinned || false,
      isLocked: firebaseChat.isLocked || false,
      isHidden: firebaseChat.isHidden || false,
      isDeleted: firebaseChat.isDeleted || false,

      // Timestamps
      archivedAt: firebaseChat.archivedAt ? new Date(firebaseChat.archivedAt) : undefined,
      mutedAt: firebaseChat.mutedAt ? new Date(firebaseChat.mutedAt) : undefined,
      pinnedAt: firebaseChat.pinnedAt ? new Date(firebaseChat.pinnedAt) : undefined,
      lockedAt: firebaseChat.lockedAt ? new Date(firebaseChat.lockedAt) : undefined,
      hiddenAt: firebaseChat.hiddenAt ? new Date(firebaseChat.hiddenAt) : undefined,
      deletedAt: firebaseChat.deletedAt ? new Date(firebaseChat.deletedAt) : undefined,

      // Settings
      customName: firebaseChat.customName,

      // Sync status
      needsSync: false, // Just synced from Firebase
      lastSyncAt: new Date(),
    };
  }

  private firebaseToLocalMessageFormat(firebaseMessage: any): LocalChatMessage {
    return {
      id: firebaseMessage.id,
      chatId: firebaseMessage.chatId,
      text: firebaseMessage.text,
      senderId: firebaseMessage.senderId,
      senderName: firebaseMessage.senderName,
      timestamp: new Date(firebaseMessage.timestamp),
      type: firebaseMessage.type,
      fileName: firebaseMessage.fileName,
      fileUrl: firebaseMessage.fileUrl,

      // Local flags
      isDeleted: firebaseMessage.isDeleted || false,
      deletedAt: firebaseMessage.deletedAt ? new Date(firebaseMessage.deletedAt) : undefined,

      // Sync status
      needsSync: false, // Just synced from Firebase
    };
  }

  /**
   * MERGE REMOTE CHANGES (for future Firebase integration)
   */
  private async mergeRemoteChanges(remoteChats: any[], remoteMessages: any[]): Promise<void> {
    // Convert Firebase data to local format and merge
    for (const firebaseChat of remoteChats) {
      const localChat = this.firebaseToLocalChatFormat(firebaseChat);
      await this.mergeRemoteChat(localChat);
    }

    for (const firebaseMessage of remoteMessages) {
      const localMessage = this.firebaseToLocalMessageFormat(firebaseMessage);
      await this.mergeRemoteMessage(localMessage);
    }

    console.log(`🔄 Merged ${remoteChats.length} chats and ${remoteMessages.length} messages from Firebase`);
  }

  private async mergeRemoteChat(remoteChat: LocalChatData): Promise<void> {
    const existingIndex = this.chatsCache.findIndex(c => c.id === remoteChat.id);

    if (existingIndex !== -1) {
      // Merge with existing chat (remote wins for now)
      this.chatsCache[existingIndex] = {
        ...this.chatsCache[existingIndex],
        ...remoteChat,
        needsSync: false,
      };
    } else {
      // Add new chat from remote
      this.chatsCache.push(remoteChat);
    }
  }

  private async mergeRemoteMessage(remoteMessage: LocalChatMessage): Promise<void> {
    const chatMessages = this.messagesCache.get(remoteMessage.chatId) || [];
    const existingIndex = chatMessages.findIndex(m => m.id === remoteMessage.id);

    if (existingIndex !== -1) {
      // Merge with existing message (remote wins for now)
      chatMessages[existingIndex] = {
        ...chatMessages[existingIndex],
        ...remoteMessage,
        needsSync: false,
      };
    } else {
      // Add new message from remote
      chatMessages.push(remoteMessage);
    }

    this.messagesCache.set(remoteMessage.chatId, chatMessages);
  }
}

export const localChatManagementService = new LocalChatManagementService();
