import { Ionicons } from "@expo/vector-icons";
import * as MediaLibrary from "expo-media-library";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useEffect, useState } from "react";
import {
    Alert,
    Dimensions,
    FlatList,
    Image,
    Text,
    TouchableOpacity,
    View,
    StyleSheet,
    ActivityIndicator,
} from "react-native";
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useSelector } from "react-redux";
import { RootState } from "../src/redux/store";
import { navigationService } from "../src/services/navigationService";
import { FloatingActionButton, QuickNavActions } from "../src/components/NavigationHelper";
import { shareService } from "../src/services/shareService";
import { realTimeMessagingService } from "../src/services/realTimeMessagingService";
import { realMediaUploadService } from "../src/services/realMediaUploadService";
import { ResponsiveContainer , ResponsiveGrid } from "../src/components/ui/ResponsiveContainer";
import { ResponsiveCard } from "../src/components/ui/ResponsiveCard";
import { ResponsiveHeader } from "../src/components/ui/ResponsiveHeader";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from "../src/styles/iraChatDesignSystem";
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo, ResponsiveUtils } from "../src/utils/responsiveUtils";

interface MediaItem {
  id: string;
  uri: string;
  type: "photo" | "video";
  creationTime: number;
  filename: string;
  duration?: number;
}

const { width } = Dimensions.get("window");
const columns = ResponsiveUtils.getOptimalColumns(120, ResponsiveSpacing.md);
const itemSize = (width - (ResponsiveSpacing.screenPadding * 2) - (ResponsiveSpacing.md * (columns - 1))) / columns;

export default function MediaGalleryScreen() {
  const router = useRouter();
  const params = useLocalSearchParams();
  const chatId = params.chatId as string;
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [selectedTab, setSelectedTab] = useState<"all" | "photos" | "videos">(
    "all",
  );
  const [loading, setLoading] = useState(true);
  const [permissionGranted, setPermissionGranted] = useState(false);

  useEffect(() => {
    requestPermissionAndLoadMedia();
  }, []);

  const requestPermissionAndLoadMedia = async () => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status === "granted") {
        setPermissionGranted(true);
        await loadMediaItems();
      } else {
        Alert.alert(
          "Permission Required",
          "Please grant media library access to view photos and videos.",
          [
            { text: "Cancel", style: "cancel" },
            {
              text: "Settings",
              onPress: () => MediaLibrary.requestPermissionsAsync(),
            },
          ],
        );
      }
    } catch (error) {
      console.error("Error requesting permission:", error);
    } finally {
      setLoading(false);
    }
  };

  const loadMediaItems = async () => {
    try {
      const media = await MediaLibrary.getAssetsAsync({
        mediaType: "photo",
        first: 100,
        sortBy: "creationTime",
      });

      const videos = await MediaLibrary.getAssetsAsync({
        mediaType: "video",
        first: 50,
        sortBy: "creationTime",
      });

      const allMedia: MediaItem[] = [
        ...media.assets.map((asset) => ({
          id: asset.id,
          uri: asset.uri,
          type: "photo" as const,
          creationTime: asset.creationTime,
          filename: asset.filename,
        })),
        ...videos.assets.map((asset) => ({
          id: asset.id,
          uri: asset.uri,
          type: "video" as const,
          creationTime: asset.creationTime,
          filename: asset.filename,
          duration: asset.duration,
        })),
      ];

      // Sort by creation time (newest first)
      allMedia.sort((a, b) => b.creationTime - a.creationTime);
      setMediaItems(allMedia);
    } catch (error) {
      console.error("Error loading media:", error);
      Alert.alert("Error", "Failed to load media items");
    }
  };

  const getFilteredMedia = () => {
    switch (selectedTab) {
      case "photos":
        return mediaItems.filter((item) => item.type === "photo");
      case "videos":
        return mediaItems.filter((item) => item.type === "video");
      default:
        return mediaItems;
    }
  };

  const handleMediaSelect = (item: MediaItem) => {
    if (chatId) {
      // If opened from a chat, send the media
      Alert.alert("Send Media", `Send this ${item.type} to the chat?`, [
        { text: "Cancel", style: "cancel" },
        {
          text: "Send",
          onPress: async () => {
            try {
              if (!currentUser?.id) {
                Alert.alert("Error", "User not authenticated");
                return;
              }

              console.log('🔥 Sending media to chat...', { chatId, mediaType: item.type });

              // Send media message using offline-capable service
              const { iraChatOfflineEngine } = await import('../src/services/iraChatOfflineEngine');
              await iraChatOfflineEngine.initialize();

              await iraChatOfflineEngine.sendMessage(
                chatId,
                `Shared from gallery`,
                currentUser.id,
                item.type === 'photo' ? 'image' : 'video',
                {
                  mediaUri: item.uri,
                  type: item.type === 'photo' ? 'image' : 'video',
                  caption: 'Shared from gallery'
                }
              );

              const result = { success: true };

              if (result.success) {
                Alert.alert("Success", `${item.type} sent to chat successfully!`, [
                  { text: "OK", onPress: () => navigationService.goBack() }
                ]);
                console.log('✅ Media sent successfully');
              } else {
                Alert.alert("Error", "Failed to send media");
                console.error('❌ Failed to send media');
              }
            } catch (error) {
              console.error('❌ Error sending media:', error);
              Alert.alert("Error", "Failed to send media. Please try again.");
            }
          },
        },
      ]);
    } else {
      // Just view the media - implement full-screen viewer
      console.log("Viewing media:", item.uri);

      // Navigate to full-screen media viewer
      navigationService.navigate('/media-viewer', {
        mediaUri: item.uri,
        mediaType: item.type,
        mediaId: item.id
      });
    }
  };

  const TabButton = ({
    title,
    value,
    count,
  }: {
    title: string;
    value: typeof selectedTab;
    count: number;
  }) => (
    <TouchableOpacity
      onPress={() => setSelectedTab(value)}
      className={`flex-1 py-3 px-4 rounded-lg mx-1 ${
        selectedTab === value ? "bg-blue-500" : "bg-gray-100"
      }`}
    >
      <Text
        className={`text-center font-medium ${
          selectedTab === value ? "text-white" : "text-gray-600"
        }`}
      >
        {title}
      </Text>
      <Text
        className={`text-center text-xs mt-1 ${
          selectedTab === value ? "text-blue-100" : "text-gray-400"
        }`}
      >
        {count}
      </Text>
    </TouchableOpacity>
  );

  const handleShareMedia = async (item: MediaItem) => {
    const mediaType = item.type === 'photo' ? 'image' : item.type === 'video' ? 'video' : 'document';
    await shareService.shareMedia(item.uri, mediaType);
  };

  const MediaItemComponent = ({ item }: { item: MediaItem }) => (
    <TouchableOpacity
      onPress={() => handleMediaSelect(item)}
      onLongPress={() => {
        Alert.alert(
          'Media Options',
          'What would you like to do with this media?',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Share', onPress: () => handleShareMedia(item) },
            { text: 'Select', onPress: () => handleMediaSelect(item) },
          ]
        );
      }}
      className="relative"
      style={{ width: itemSize, height: itemSize, margin: 2 }}
    >
      <Image
        source={{ uri: item.uri }}
        style={{ width: itemSize, height: itemSize }}
        className="rounded-lg"
        resizeMode="cover"
      />

      {item.type === "video" && (
        <View className="absolute inset-0 items-center justify-center">
          <View className="bg-black bg-opacity-50 rounded-full p-2">
            <Ionicons name="play" size={20} color="white" />
          </View>
        </View>
      )}

      {item.duration && (
        <View className="absolute bottom-1 right-1 bg-black bg-opacity-70 px-2 py-1 rounded">
          <Text className="text-white text-xs">
            {Math.floor(item.duration / 60)}:
            {(item.duration % 60).toString().padStart(2, "0")}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View className="flex-1 bg-white items-center justify-center">
        <Text className="text-gray-500">Loading media...</Text>
      </View>
    );
  }

  if (!permissionGranted) {
    return (
      <View className="flex-1 bg-white">
        {/* Header */}
        <View className="bg-white px-4 py-4 border-b border-gray-200">
          <View className="flex-row items-center">
            <TouchableOpacity onPress={() => navigationService.goBack()} className="mr-4">
              <Ionicons name="arrow-back" size={24} color="#374151" />
            </TouchableOpacity>
            <Text className="text-xl font-bold text-gray-800">
              Media Gallery
            </Text>
          </View>
        </View>

        <View className="flex-1 items-center justify-center px-6">
          <Ionicons name="images" size={64} color="#9CA3AF" />
          <Text className="text-gray-800 text-lg font-medium mt-4 text-center">
            Permission Required
          </Text>
          <Text className="text-gray-500 text-center mt-2 mb-6">
            We need access to your media library to show photos and videos.
          </Text>
          <TouchableOpacity
            onPress={requestPermissionAndLoadMedia}
            className="bg-blue-500 py-3 px-6 rounded-lg"
          >
            <Text className="text-white font-medium">Grant Permission</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  const filteredMedia = getFilteredMedia();
  const photoCount = mediaItems.filter((item) => item.type === "photo").length;
  const videoCount = mediaItems.filter((item) => item.type === "video").length;

  return (
    <View className="flex-1 bg-white">
      {/* Header */}
      <View className="bg-white px-4 py-4 border-b border-gray-200">
        <View className="flex-row items-center">
          <TouchableOpacity onPress={() => navigationService.goBack()} className="mr-4">
            <Ionicons name="arrow-back" size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-xl font-bold text-gray-800">Media Gallery</Text>
        </View>
      </View>

      {/* Tabs */}
      <View className="flex-row px-4 py-3 bg-gray-50">
        <TabButton title="All" value="all" count={mediaItems.length} />
        <TabButton title="Photos" value="photos" count={photoCount} />
        <TabButton title="Videos" value="videos" count={videoCount} />
      </View>

      {/* Media Grid */}
      {filteredMedia.length > 0 ? (
        <FlatList
          data={filteredMedia}
          renderItem={({ item }) => <MediaItemComponent item={item} />}
          keyExtractor={(item) => item.id}
          numColumns={3}
          getItemLayout={(_data, index) => ({
            length: 120, // Approximate height of each media item in grid
            offset: 120 * Math.floor(index / 3),
            index,
          })}
          contentContainerStyle={{ padding: 16 }}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          maxToRenderPerBatch={15}
          windowSize={10}
        />
      ) : (
        <View className="flex-1 items-center justify-center">
          <Ionicons name="images-outline" size={64} color="#9CA3AF" />
          <Text className="text-gray-500 text-lg mt-4">No media found</Text>
          <Text className="text-gray-400 text-sm mt-2">
            {selectedTab === "photos"
              ? "No photos available"
              : selectedTab === "videos"
                ? "No videos available"
                : "No media files available"}
          </Text>
        </View>
      )}

      {/* Floating Action Button for Media Actions */}
      <FloatingActionButton
        actions={[
          QuickNavActions.camera,
          {
            icon: 'download-outline',
            label: 'Downloads',
            onPress: () => navigationService.navigate('/downloaded-media'),
            color: '#10B981',
          },
          {
            icon: 'share-outline',
            label: 'Share App',
            onPress: () => shareService.shareApp(),
            color: '#3B82F6',
          },
        ]}
        mainAction={{
          icon: 'add-outline',
          onPress: () => navigationService.openCamera(),
          backgroundColor: '#667eea',
        }}
      />
    </View>
  );
}
