// Firebase service for vertical media updates functionality
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  increment,
  limit,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  startAfter,
  updateDoc,
  where,
} from "firebase/firestore";
import {
  deleteObject,
  getDownloadURL,
  ref,
  uploadBytes,
} from "firebase/storage";
import { Comment, Update } from "../types";
import { db, storage } from "./firebaseSimple";
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

// Collections
const UPDATES_COLLECTION = "updates";
const COMMENTS_COLLECTION = "comments";
const LIKES_COLLECTION = "likes";

// Offline support functions
const cacheUpdateOffline = async (update: Update): Promise<void> => {
  try {
    const database = offlineDatabaseService.getDatabase();
    await database.runAsync(`
      INSERT OR REPLACE INTO cached_vertical_updates (
        id, userId, username, userAvatar, mediaUrl, mediaType, caption,
        musicUrl, musicTitle, timestamp, createdAt, expiresAt, likes,
        comments, shares, views
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      update.id, update.user.id, update.user.name, update.user.avatar || '',
      update.mediaUrl, update.mediaType, update.caption || '',
      update.musicTitle || '', update.musicAuthor || '', update.createdAt,
      new Date().toISOString(), update.createdAt + (7 * 24 * 60 * 60 * 1000),
      update.likeCount, update.commentCount, update.shareCount, update.viewCount
    ]);
  } catch (error) {
    // Cache failed - continue without caching
  }
};

const getOfflineUpdates = async (limitCount: number = 10): Promise<Update[]> => {
  try {
    const database = offlineDatabaseService.getDatabase();
    const now = Date.now();
    const result = await database.getAllAsync(`
      SELECT * FROM cached_vertical_updates
      WHERE expiresAt > ?
      ORDER BY timestamp DESC
      LIMIT ?
    `, [now, limitCount]);

    return result.map((row: any) => ({
      id: row.id,
      user: {
        id: row.userId,
        name: row.username,
        username: row.username,
        displayName: row.username,
        avatar: row.userAvatar || undefined,
        phoneNumber: '',
        authMethod: 'phone' as const,
        phoneVerified: true,
        followersCount: 0,
        followingCount: 0,
        likesCount: 0,
      },
      mediaUrl: row.mediaUrl,
      mediaType: row.mediaType,
      caption: row.caption || undefined,
      musicTitle: row.musicTitle || undefined,
      musicAuthor: row.musicUrl || undefined,
      location: undefined,
      createdAt: row.createdAt,
      likeCount: row.likes,
      commentCount: row.comments,
      shareCount: row.shares,
      viewCount: row.views,
      downloadCount: 0,
      isLiked: false,
      isFollowing: false,
      hashtags: [],
      mentions: [],
      comments: [],
    }));
  } catch (error) {
    return [];
  }
};

// ===== UPDATE OPERATIONS =====

export const createUpdate = async (updateData: {
  userId: string;
  username: string;
  userAvatar?: string;
  mediaFile: any; // File or blob
  mediaType: "photo" | "video";
  caption?: string;
  musicUrl?: string;
  musicTitle?: string;
}): Promise<string> => {
  try {
    let mediaUrl: string;

    if (networkStateManager.isOnline()) {
      // Upload media file to Firebase Storage
      const mediaRef = ref(storage, `updates/${updateData.userId}/${Date.now()}`);
      const uploadResult = await uploadBytes(mediaRef, updateData.mediaFile);
      mediaUrl = await getDownloadURL(uploadResult.ref);
    } else {
      // Store offline placeholder
      mediaUrl = `offline_${Date.now()}_${updateData.userId}`;
    }

    // Calculate expiry date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create update document
    const updateDoc = {
      userId: updateData.userId,
      username: updateData.username,
      userAvatar: updateData.userAvatar || "",
      mediaUrl,
      mediaType: updateData.mediaType,
      caption: updateData.caption || "",
      musicUrl: updateData.musicUrl || "",
      musicTitle: updateData.musicTitle || "",
      timestamp: serverTimestamp(),
      createdAt: new Date().toISOString(),
      expiresAt: serverTimestamp(),
      likes: 0,
      comments: 0,
      shares: 0,
      views: 0,
    };

    let docId: string;

    if (networkStateManager.isOnline()) {
      const docRef = await addDoc(collection(db, UPDATES_COLLECTION), updateDoc);
      docId = docRef.id;
    } else {
      // Generate offline ID
      docId = `offline_${Date.now()}_${updateData.userId}`;
    }

    // Cache update offline
    const fullUpdate: Update = {
      id: docId,
      user: {
        id: updateData.userId,
        name: updateData.username,
        username: updateData.username,
        displayName: updateData.username,
        avatar: updateData.userAvatar || '',
        phoneNumber: '',
        authMethod: 'phone' as const,
        phoneVerified: true,
        followersCount: 0,
        followingCount: 0,
        likesCount: 0,
      },
      mediaUrl,
      mediaType: updateData.mediaType as "video" | "image",
      caption: updateData.caption,
      musicTitle: updateData.musicTitle,
      musicAuthor: updateData.musicUrl,
      location: undefined,
      createdAt: Date.now(),
      likeCount: 0,
      commentCount: 0,
      shareCount: 0,
      viewCount: 0,
      downloadCount: 0,
      isLiked: false,
      isFollowing: false,
      hashtags: [],
      mentions: [],
      comments: [],
    };

    await cacheUpdateOffline(fullUpdate);

    return docId;
  } catch (error) {
    throw error;
  }
};

export const getUpdates = async (
  limitCount: number = 10,
  lastDoc?: any,
): Promise<{ updates: Update[]; lastVisible: any }> => {
  try {
    if (!networkStateManager.isOnline()) {
      // Return offline cached updates
      const offlineUpdates = await getOfflineUpdates(limitCount);
      return { updates: offlineUpdates, lastVisible: null };
    }

    // Add timeout to prevent hanging
    const queryPromise = (async () => {
      let q = query(
        collection(db, UPDATES_COLLECTION),
        where("expiresAt", ">", new Date()), // Only get non-expired updates
        orderBy("timestamp", "desc"),
        limit(Math.min(limitCount, 20)), // Limit to max 20 for performance
      );

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const querySnapshot = await getDocs(q);
      const updates: Update[] = [];
      let lastVisible = null;

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        const update = {
          id: doc.id,
          ...data,
          timestamp: data.timestamp,
          expiresAt: data.expiresAt,
        } as Update;
        updates.push(update);
        lastVisible = doc;
      });

      return { updates, lastVisible };
    })();

    const timeoutPromise = new Promise<{ updates: Update[]; lastVisible: any }>((_, reject) =>
      setTimeout(() => reject(new Error('Updates query timeout')), 5000)
    );

    const result = await Promise.race([queryPromise, timeoutPromise]);

    // Cache updates offline (async, don't wait)
    result.updates.forEach(update => {
      cacheUpdateOffline(update).catch(error =>
        console.error('Failed to cache update:', error)
      );
    });

    return result;
  } catch (error) {
    console.error('❌ Error fetching updates:', error);
    // Fallback to offline data
    const offlineUpdates = await getOfflineUpdates(limitCount);
    return { updates: offlineUpdates, lastVisible: null };
  }
};

export const incrementUpdateViews = async (updateId: string): Promise<void> => {
  try {
    const updateRef = doc(db, UPDATES_COLLECTION, updateId);
    await updateDoc(updateRef, {
      views: increment(1),
    });
  } catch (error) {
    // Error incrementing views - continue silently
  }
};

export const deleteUpdate = async (
  updateId: string,
  mediaUrl: string,
): Promise<void> => {
  try {
    // Delete media from storage
    const mediaRef = ref(storage, mediaUrl);
    await deleteObject(mediaRef);

    // Delete update document
    await deleteDoc(doc(db, UPDATES_COLLECTION, updateId));

    // Delete associated comments and likes
    const commentsQuery = query(
      collection(db, COMMENTS_COLLECTION),
      where("updateId", "==", updateId),
    );
    const likesQuery = query(
      collection(db, LIKES_COLLECTION),
      where("updateId", "==", updateId),
    );

    const [commentsSnapshot, likesSnapshot] = await Promise.all([
      getDocs(commentsQuery),
      getDocs(likesQuery),
    ]);

    const deletePromises: Promise<void>[] = [];

    commentsSnapshot.forEach((doc) => {
      deletePromises.push(deleteDoc(doc.ref));
    });

    likesSnapshot.forEach((doc) => {
      deletePromises.push(deleteDoc(doc.ref));
    });

    await Promise.all(deletePromises);
  } catch (error) {
    throw error;
  }
};

// ===== LIKE OPERATIONS =====

export const toggleLike = async (
  updateId: string,
  userId: string,
): Promise<boolean> => {
  try {
    const likesQuery = query(
      collection(db, LIKES_COLLECTION),
      where("updateId", "==", updateId),
      where("userId", "==", userId),
    );

    const querySnapshot = await getDocs(likesQuery);
    const updateRef = doc(db, UPDATES_COLLECTION, updateId);

    if (querySnapshot.empty) {
      // Add like
      await addDoc(collection(db, LIKES_COLLECTION), {
        updateId,
        userId,
        timestamp: serverTimestamp(),
      });

      await updateDoc(updateRef, {
        likes: increment(1),
      });

      return true; // Liked
    } else {
      // Remove like
      const likeDoc = querySnapshot.docs[0];
      await deleteDoc(likeDoc.ref);

      await updateDoc(updateRef, {
        likes: increment(-1),
      });

      return false; // Unliked
    }
  } catch (error) {
    throw error;
  }
};

export const checkIfLiked = async (
  updateId: string,
  userId: string,
): Promise<boolean> => {
  try {
    const likesQuery = query(
      collection(db, LIKES_COLLECTION),
      where("updateId", "==", updateId),
      where("userId", "==", userId),
    );

    const querySnapshot = await getDocs(likesQuery);
    return !querySnapshot.empty;
  } catch (error) {
    return false;
  }
};

// ===== COMMENT OPERATIONS =====

export const addComment = async (commentData: {
  updateId: string;
  userId: string;
  username: string;
  userAvatar?: string;
  text: string;
}): Promise<string> => {
  try {
    const commentDoc = {
      updateId: commentData.updateId,
      userId: commentData.userId,
      username: commentData.username,
      userAvatar: commentData.userAvatar || "",
      text: commentData.text,
      timestamp: serverTimestamp(),
      createdAt: new Date().toISOString(),
      likes: 0,
    };

    const docRef = await addDoc(
      collection(db, COMMENTS_COLLECTION),
      commentDoc,
    );

    // Increment comment count on update
    const updateRef = doc(db, UPDATES_COLLECTION, commentData.updateId);
    await updateDoc(updateRef, {
      comments: increment(1),
    });

    return docRef.id;
  } catch (error) {
    throw error;
  }
};

export const getComments = async (updateId: string): Promise<Comment[]> => {
  try {
    const commentsQuery = query(
      collection(db, COMMENTS_COLLECTION),
      where("updateId", "==", updateId),
      orderBy("timestamp", "desc"),
    );

    const querySnapshot = await getDocs(commentsQuery);
    const comments: Comment[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      comments.push({
        id: doc.id,
        ...data,
        timestamp: data.timestamp,
      } as Comment);
    });

    return comments;
  } catch (error) {
    throw error;
  }
};

// ===== REAL-TIME LISTENERS =====

export const subscribeToComments = (
  updateId: string,
  callback: (comments: Comment[]) => void,
): (() => void) => {
  const commentsQuery = query(
    collection(db, COMMENTS_COLLECTION),
    where("updateId", "==", updateId),
    orderBy("timestamp", "desc"),
  );

  return onSnapshot(commentsQuery, (querySnapshot) => {
    const comments: Comment[] = [];
    querySnapshot.forEach((doc) => {
      const data = doc.data();
      comments.push({
        id: doc.id,
        ...data,
        timestamp: data.timestamp,
      } as Comment);
    });
    callback(comments);
  });
};

// ===== CLEANUP OPERATIONS =====

export const cleanupExpiredUpdates = async (): Promise<void> => {
  try {
    const expiredQuery = query(
      collection(db, UPDATES_COLLECTION),
      where("expiresAt", "<=", new Date()),
    );

    const querySnapshot = await getDocs(expiredQuery);
    const deletePromises: Promise<void>[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      deletePromises.push(deleteUpdate(doc.id, data.mediaUrl));
    });

    await Promise.all(deletePromises);
  } catch (error) {
    // Error cleaning up - continue silently
  }
};
