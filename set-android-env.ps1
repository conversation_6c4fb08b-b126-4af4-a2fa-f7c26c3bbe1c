# PowerShell script to set Android environment variables
Write-Host "Setting Android Environment Variables..." -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Common Android SDK paths
$possibleSdkPaths = @(
    "$env:LOCALAPPDATA\Android\Sdk",
    "$env:USERPROFILE\AppData\Local\Android\Sdk",
    "C:\Android\Sdk",
    "$env:USERPROFILE\Android\Sdk",
    "C:\Users\<USER>\AppData\Local\Android\Sdk"
)

$sdkPath = $null
Write-Host "Searching for Android SDK..." -ForegroundColor Yellow

foreach ($path in $possibleSdkPaths) {
    Write-Host "Checking: $path" -ForegroundColor Gray
    if (Test-Path $path) {
        $sdkPath = $path
        Write-Host "✅ Found Android SDK at: $sdkPath" -ForegroundColor Green
        break
    }
}

if (-not $sdkPath) {
    Write-Host "❌ Android SDK not found in common locations." -ForegroundColor Red
    Write-Host "Please enter the path to your Android SDK manually:" -ForegroundColor Yellow
    $manualPath = Read-Host "Android SDK Path"
    
    if (Test-Path $manualPath) {
        $sdkPath = $manualPath
        Write-Host "✅ Using manual path: $sdkPath" -ForegroundColor Green
    } else {
        Write-Host "❌ Invalid path. Exiting." -ForegroundColor Red
        exit 1
    }
}

# Verify essential components
Write-Host "`nVerifying SDK components..." -ForegroundColor Yellow
$platformTools = "$sdkPath\platform-tools"
$adbPath = "$platformTools\adb.exe"

if (Test-Path $adbPath) {
    Write-Host "✅ ADB found: $adbPath" -ForegroundColor Green
} else {
    Write-Host "⚠️  ADB not found. You may need to install platform-tools." -ForegroundColor Yellow
}

# Set environment variables
Write-Host "`nSetting environment variables..." -ForegroundColor Cyan

try {
    # Set ANDROID_HOME
    [Environment]::SetEnvironmentVariable("ANDROID_HOME", $sdkPath, [EnvironmentVariableTarget]::User)
    Write-Host "✅ Set ANDROID_HOME = $sdkPath" -ForegroundColor Green
    
    # Set ANDROID_SDK_ROOT (alternative name)
    [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $sdkPath, [EnvironmentVariableTarget]::User)
    Write-Host "✅ Set ANDROID_SDK_ROOT = $sdkPath" -ForegroundColor Green
    
    # Update PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)
    $pathsToAdd = @(
        "$sdkPath\platform-tools",
        "$sdkPath\tools",
        "$sdkPath\tools\bin",
        "$sdkPath\emulator"
    )
    
    $pathUpdated = $false
    foreach ($pathToAdd in $pathsToAdd) {
        if ($currentPath -notlike "*$pathToAdd*") {
            $currentPath = "$currentPath;$pathToAdd"
            $pathUpdated = $true
            Write-Host "✅ Added to PATH: $pathToAdd" -ForegroundColor Green
        } else {
            Write-Host "✓ Already in PATH: $pathToAdd" -ForegroundColor Gray
        }
    }
    
    if ($pathUpdated) {
        [Environment]::SetEnvironmentVariable("PATH", $currentPath, [EnvironmentVariableTarget]::User)
        Write-Host "✅ PATH updated successfully!" -ForegroundColor Green
    } else {
        Write-Host "✓ PATH already contains all necessary Android paths" -ForegroundColor Gray
    }

} catch {
    Write-Host "❌ Error setting environment variables: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`nEnvironment variables set successfully!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host "ANDROID_HOME = $sdkPath" -ForegroundColor White
Write-Host "ANDROID_SDK_ROOT = $sdkPath" -ForegroundColor White

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Close and restart your terminal/PowerShell" -ForegroundColor White
Write-Host "2. Run: npx expo start" -ForegroundColor White
Write-Host "3. Press 'a' to open Android" -ForegroundColor White

Write-Host "`nTo verify the setup works:" -ForegroundColor Yellow
Write-Host "   adb version" -ForegroundColor White
Write-Host "   echo `$env:ANDROID_HOME" -ForegroundColor White

Write-Host "`nImportant: You must restart your terminal for changes to take effect!" -ForegroundColor Red
