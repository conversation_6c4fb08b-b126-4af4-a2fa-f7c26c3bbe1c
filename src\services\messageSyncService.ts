import { addDoc, collection, serverTimestamp } from 'firebase/firestore';
import { db } from './firebaseSimple';
import { localMessageStorage, LocalMessage } from './localMessageStorage';
import { Message } from '../types';

class MessageSyncService {
  private syncInProgress = false;
  private syncQueue: Set<string> = new Set();

  async syncPendingMessages(): Promise<void> {
    if (this.syncInProgress) {
      console.log('⏳ Sync already in progress, skipping...');
      return;
    }

    this.syncInProgress = true;
    console.log('🔄 Starting message sync...');

    try {
      const pendingMessages = await localMessageStorage.getPendingMessages();
      console.log(`📤 Found ${pendingMessages.length} pending messages to sync`);

      for (const message of pendingMessages) {
        if (this.syncQueue.has(message.localId!)) {
          continue; // Skip if already being processed
        }

        this.syncQueue.add(message.localId!);
        await this.syncSingleMessage(message);
        this.syncQueue.delete(message.localId!);
      }

      console.log('✅ Message sync completed');
    } catch (error) {
      console.error('❌ Message sync failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncSingleMessage(localMessage: LocalMessage): Promise<void> {
    try {
      console.log('📤 Syncing message:', localMessage.localId);

      // Prepare message data for Firebase
      const messageData: Partial<Message> = {
        text: localMessage.text,
        senderId: localMessage.senderId,
        timestamp: serverTimestamp(),
        status: localMessage.status,
        type: localMessage.type,
        mediaUrl: localMessage.mediaUrl,
        duration: localMessage.duration,
        fileName: localMessage.fileName,
        fileSize: localMessage.fileSize,
        isPinned: localMessage.isPinned,
        pinnedAt: localMessage.pinnedAt,
        pinnedBy: localMessage.pinnedBy,
        replyTo: localMessage.replyTo,
        reactions: localMessage.reactions,
      };

      // Add media object if we have media-related data
      if (localMessage.mediaUrl && (localMessage.type === 'image' || localMessage.type === 'video')) {
        messageData.media = {
          type: localMessage.type,
          url: localMessage.mediaUrl,
          thumbnail: localMessage.mediaThumbnail,
          caption: localMessage.text,
        };
      }

      // Add file object if we have file-related data
      if (localMessage.mediaUrl && localMessage.type === 'document' && localMessage.fileName) {
        messageData.file = {
          type: 'document',
          name: localMessage.fileName,
          size: localMessage.fileSize ? `${localMessage.fileSize}` : '0',
          url: localMessage.mediaUrl,
          caption: localMessage.text,
        };
      }

      // Remove undefined values
      Object.keys(messageData).forEach(key => {
        if (messageData[key as keyof typeof messageData] === undefined) {
          delete messageData[key as keyof typeof messageData];
        }
      });

      // Send to Firebase
      const docRef = await addDoc(
        collection(db, `chats/${localMessage.chatId}/messages`),
        messageData
      );

      // Update local message with Firebase ID and sync status
      await localMessageStorage.updateMessageSyncStatus(
        localMessage.localId!,
        'synced',
        docRef.id
      );

      console.log('✅ Message synced successfully:', localMessage.localId, '→', docRef.id);
    } catch (error) {
      console.error('❌ Failed to sync message:', localMessage.localId, error);
      
      // Mark as failed
      await localMessageStorage.updateMessageSyncStatus(
        localMessage.localId!,
        'failed'
      );
    }
  }

  async saveMessageLocally(
    chatId: string,
    text: string,
    senderId: string,
    type: Message['type'] = 'text',
    additionalData?: Partial<LocalMessage>
  ): Promise<string> {
    const now = Date.now();
    const messageLocalId = `local_${now}_${Math.random().toString(36).substring(2, 11)}`;
    const localMessage: LocalMessage = {
      id: messageLocalId, // Required by Message interface
      localId: messageLocalId,
      chatId,
      text,
      senderId,
      timestamp: new Date(now),
      status: 'sent',
      type,
      syncStatus: 'pending',
      createdAt: now,
      updatedAt: now,
      ...additionalData,
    };

    const savedLocalId = await localMessageStorage.saveMessage(localMessage);

    // Trigger sync in background
    this.syncPendingMessages().catch(error => {
      console.error('❌ Background sync failed:', error);
    });

    return savedLocalId;
  }

  async saveMediaMessageLocally(
    chatId: string,
    senderId: string,
    type: 'image' | 'video' | 'audio' | 'document' | 'voice',
    mediaUrl: string,
    additionalData?: {
      fileName?: string;
      fileSize?: number;
      duration?: number;
      mediaThumbnail?: string;
      text?: string; // caption
      replyTo?: Message['replyTo'];
    }
  ): Promise<string> {
    // Map 'voice' type to 'audio' for Message compatibility
    const messageType: Message['type'] = type === 'voice' ? 'audio' : type;

    return this.saveMessageLocally(chatId, additionalData?.text || '', senderId, messageType, {
      mediaUrl,
      fileName: additionalData?.fileName,
      fileSize: additionalData?.fileSize,
      duration: additionalData?.duration,
      mediaThumbnail: additionalData?.mediaThumbnail,
      replyTo: additionalData?.replyTo,
    });
  }

  async loadMessagesFromLocal(chatId: string, limit: number = 50, offset: number = 0): Promise<Message[]> {
    const localMessages = await localMessageStorage.getMessages(chatId, limit, offset);

    // Convert LocalMessage to Message format
    return localMessages.map(localMsg => {
      const message: Message = {
        id: localMsg.id || localMsg.localId!,
        text: localMsg.text,
        senderId: localMsg.senderId,
        timestamp: localMsg.timestamp,
        status: localMsg.status,
        type: localMsg.type,
        mediaUrl: localMsg.mediaUrl,
        duration: localMsg.duration,
        fileName: localMsg.fileName,
        fileSize: localMsg.fileSize,
        isPinned: localMsg.isPinned,
        pinnedAt: localMsg.pinnedAt,
        pinnedBy: localMsg.pinnedBy,
        replyTo: localMsg.replyTo,
        reactions: localMsg.reactions,
        isEdited: localMsg.isEdited,
        editedAt: localMsg.editedAt,
      };

      // Add media object if we have media-related data
      if (localMsg.mediaUrl && (localMsg.type === 'image' || localMsg.type === 'video')) {
        message.media = {
          type: localMsg.type,
          url: localMsg.mediaUrl,
          thumbnail: localMsg.mediaThumbnail,
          caption: localMsg.text,
        };
      }

      // Add file object if we have file-related data
      if (localMsg.mediaUrl && localMsg.type === 'document' && localMsg.fileName) {
        message.file = {
          type: 'document',
          name: localMsg.fileName,
          size: localMsg.fileSize ? `${localMsg.fileSize}` : '0',
          url: localMsg.mediaUrl,
          caption: localMsg.text,
        };
      }

      return message;
    });
  }

  async updateMessageStatus(localId: string, status: Message['status']): Promise<void> {
    await localMessageStorage.updateMessage(localId, { status });
  }

  async deleteMessageLocally(localId: string): Promise<void> {
    await localMessageStorage.deleteMessage(localId);
  }

  async clearChatLocally(chatId: string): Promise<void> {
    await localMessageStorage.clearChat(chatId);
  }

  async retryFailedMessages(): Promise<void> {
    console.log('🔄 Retrying failed messages...');
    
    try {
      // Get failed messages and mark them as pending for retry
      const failedMessages = await localMessageStorage.getPendingMessages();
      const actuallyFailedMessages = failedMessages.filter(msg => msg.syncStatus === 'failed');
      
      for (const message of actuallyFailedMessages) {
        await localMessageStorage.updateMessageSyncStatus(message.localId!, 'pending');
      }

      // Trigger sync
      await this.syncPendingMessages();
    } catch (error) {
      console.error('❌ Failed to retry messages:', error);
    }
  }

  // Initialize sync service
  async initialize(): Promise<void> {
    await localMessageStorage.initialize();
    
    // Start periodic sync every 30 seconds
    setInterval(() => {
      this.syncPendingMessages().catch(error => {
        console.error('❌ Periodic sync failed:', error);
      });
    }, 30000);

    console.log('✅ Message sync service initialized');
  }

  // Get sync status
  getSyncStatus(): { inProgress: boolean; queueSize: number } {
    return {
      inProgress: this.syncInProgress,
      queueSize: this.syncQueue.size,
    };
  }
}

export const messageSyncService = new MessageSyncService();
