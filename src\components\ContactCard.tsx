/**
 * Beautiful Contact Card Component for IraChat
 * Responsive design with animations and sky blue branding
 */

import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity, Image } from 'react-native';

import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS, ANIMATIONS } from '../styles/iraChatDesignSystem';
import { ResponsiveScale, ComponentSizes, ResponsiveSpacing, ResponsiveTypography } from '../utils/responsiveUtils';
import { Avatar } from './Avatar';

interface ContactCardProps {
  contactId: string;
  name: string;
  phoneNumber?: string;
  email?: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: string;
  status?: string;
  onPress: (contactId: string) => void;
  onCallPress?: (contactId: string) => void;
  onVideoCallPress?: (contactId: string) => void;
  onMessagePress?: (contactId: string) => void;
  animated?: boolean;
  index?: number;
  variant?: 'default' | 'compact' | 'detailed';
  showActions?: boolean;
}

export const ContactCard: React.FC<ContactCardProps> = ({
  contactId,
  name,
  phoneNumber,
  email,
  avatar,
  isOnline = false,
  lastSeen,
  status,
  onPress,
  onCallPress,
  onVideoCallPress,
  onMessagePress,
  animated = true,
  index = 0,
  variant = 'default',
  showActions = true,
}) => {
  // Beautiful animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(20)).current;
  const scaleAnimation = useRef(new Animated.Value(1)).current;

  // Entrance animation with stagger
  useEffect(() => {
    if (animated) {
      Animated.sequence([
        Animated.delay(index * 50),
        Animated.parallel([
          Animated.timing(fadeAnimation, {
            toValue: 1,
            duration: ANIMATIONS.fast,
            useNativeDriver: true,
          }),
          Animated.spring(slideAnimation, {
            toValue: 0,
            tension: 80,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      fadeAnimation.setValue(1);
      slideAnimation.setValue(0);
    }
  }, [animated, index]);

  // Press animations
  const handlePressIn = () => {
    if (animated) {
      Animated.spring(scaleAnimation, {
        toValue: 0.98,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated) {
      Animated.spring(scaleAnimation, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  const renderContactInfo = () => (
    <View style={styles.contactInfo}>
      <Text style={styles.contactName} numberOfLines={1}>
        {name}
      </Text>
      
      {status && variant === 'detailed' && (
        <Text style={styles.contactStatus} numberOfLines={1}>
          {status}
        </Text>
      )}
      
      <View style={styles.contactDetails}>
        {phoneNumber && (
          <Text style={styles.contactPhone} numberOfLines={1}>
            {phoneNumber}
          </Text>
        )}
        
        {email && variant === 'detailed' && (
          <Text style={styles.contactEmail} numberOfLines={1}>
            {email}
          </Text>
        )}
      </View>
      
      <View style={styles.onlineStatus}>
        {isOnline ? (
          <View style={styles.onlineIndicator}>
            <View style={styles.onlineDot} />
            <Text style={styles.onlineText}>Online</Text>
          </View>
        ) : lastSeen ? (
          <Text style={styles.lastSeenText}>Last seen {lastSeen}</Text>
        ) : null}
      </View>
    </View>
  );

  const renderActionButtons = () => {
    if (!showActions || variant === 'compact') return null;

    return (
      <View style={styles.actionButtons}>
        {onMessagePress && (
          <TouchableOpacity
            style={[styles.actionButton, styles.messageButton]}
            onPress={() => onMessagePress(contactId)}
          >
            <Image
              source={require('../../assets/images/LOGO.png')}
              style={{
                width: ResponsiveScale.iconSize(18),
                height: ResponsiveScale.iconSize(18),
                tintColor: IRACHAT_COLORS.textOnPrimary
              }}
              resizeMode="contain"
            />
          </TouchableOpacity>
        )}

        {onCallPress && (
          <TouchableOpacity
            style={[styles.actionButton, styles.callButton]}
            onPress={() => onCallPress(contactId)}
          >
            <Ionicons
              name="call"
              size={ResponsiveScale.iconSize(18)}
              color={IRACHAT_COLORS.success}
            />
          </TouchableOpacity>
        )}

        {onVideoCallPress && (
          <TouchableOpacity
            style={[styles.actionButton, styles.videoButton]}
            onPress={() => onVideoCallPress(contactId)}
          >
            <Ionicons
              name="videocam"
              size={ResponsiveScale.iconSize(18)}
              color={IRACHAT_COLORS.primary}
            />
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const animatedStyle = {
    opacity: fadeAnimation,
    transform: [
      { translateX: slideAnimation },
      { scale: scaleAnimation }
    ],
  };

  if (variant === 'compact') {
    return (
      <Animated.View style={[styles.compactContainer, animatedStyle]}>
        <TouchableOpacity
          onPress={() => onPress(contactId)}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.compactCard}
          activeOpacity={1}
        >
          <Avatar
            imageUrl={avatar}
            name={name}
            size="medium"
            showOnlineStatus={true}
            isOnline={isOnline}
            animated={animated}
          />
          <View style={styles.compactInfo}>
            <Text style={styles.compactName} numberOfLines={1}>
              {name}
            </Text>
            {phoneNumber && (
              <Text style={styles.compactPhone} numberOfLines={1}>
                {phoneNumber}
              </Text>
            )}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <TouchableOpacity
        onPress={() => onPress(contactId)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={styles.card}
        activeOpacity={1}
      >
        <View style={styles.cardContent}>
          <Avatar
            imageUrl={avatar}
            name={name}
            size="large"
            showOnlineStatus={true}
            isOnline={isOnline}
            animated={animated}
            variant="gradient"
          />

          {renderContactInfo()}
        </View>

        {renderActionButtons()}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: ResponsiveSpacing.screenPadding,
    marginVertical: ResponsiveSpacing.sm,
  },
  card: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: ResponsiveScale.borderRadius(16),
    padding: ResponsiveSpacing.md,
    ...SHADOWS.md,
  },
  compactContainer: {
    marginHorizontal: ResponsiveSpacing.sm,
    marginVertical: ResponsiveSpacing.xs,
  },
  compactCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: ResponsiveScale.borderRadius(12),
    padding: ResponsiveSpacing.sm,
    minHeight: ComponentSizes.buttonHeight.large,
    ...SHADOWS.sm,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: ResponsiveSpacing.md,
  },
  contactInfo: {
    flex: 1,
    marginLeft: ResponsiveSpacing.md,
  },
  compactInfo: {
    flex: 1,
    marginLeft: ResponsiveSpacing.sm,
  },
  contactName: {
    fontSize: ResponsiveTypography.fontSize.lg,
    fontWeight: '500' as const,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
    marginBottom: ResponsiveSpacing.xs,
  },
  compactName: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '500' as const,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  contactStatus: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    marginBottom: ResponsiveSpacing.xs,
    fontStyle: 'italic',
  },
  contactDetails: {
    marginBottom: ResponsiveSpacing.sm,
  },
  contactPhone: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    marginBottom: ResponsiveSpacing.xs / 2,
  },
  compactPhone: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  contactEmail: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  onlineStatus: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  onlineDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: IRACHAT_COLORS.online,
    marginRight: ResponsiveSpacing.xs,
  },
  onlineText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.online,
    fontFamily: TYPOGRAPHY.fontFamily,
    fontWeight: '500' as const,
  },
  lastSeenText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  actionButton: {
    width: ComponentSizes.buttonHeight.medium,
    height: ComponentSizes.buttonHeight.medium,
    borderRadius: ComponentSizes.buttonHeight.medium / 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: ResponsiveSpacing.sm,
    ...SHADOWS.sm,
  },
  messageButton: {
    backgroundColor: IRACHAT_COLORS.primary,
  },
  callButton: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.success,
  },
  videoButton: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.primary,
  },
});

export default ContactCard;
