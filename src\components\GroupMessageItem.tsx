import { Ionicons } from "@expo/vector-icons";
import React, { useCallback, useRef, useState } from "react";
import {
  Animated,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS } from "../styles/iraChatDesignSystem";
import { GroupMessage } from "../types/groupChat";
import { formatTimeAgo } from "../utils/dateUtils";
import { handleDelete } from "../utils/deleteHandler";
import { ResponsiveScale, ResponsiveTypography, ResponsiveSpacing } from "../utils/responsiveUtils";
import { db } from "../services/firebaseSimple";
import { doc, updateDoc, serverTimestamp } from "../config/firebase";

// Define Media type for compatibility
interface Media {
  id: string;
  type: "image" | "video" | "document";
  url: string;
  caption?: string;
}

// Define MediaPreview component for compatibility
const MediaPreview: React.FC<{
  media: Media;
  onPress: () => void;
  onLongPress: () => void;
}> = ({ media, onPress, onLongPress }) => (
  <TouchableOpacity onPress={onPress} onLongPress={onLongPress}>
    <Image
      source={{ uri: media.url }}
      style={{
        width: ResponsiveScale.wp(50),
        height: ResponsiveScale.wp(50),
        borderRadius: ResponsiveScale.borderRadius(8)
      }}
    />
  </TouchableOpacity>
);

interface GroupMessageItemProps {
  message: GroupMessage;
  isCurrentUser: boolean;
  groupId?: string;
  onDelete?: (_messageId: string) => Promise<void>;
  onReaction?: (_messageId: string, _reaction: string) => void;
  onReply?: (_message: GroupMessage) => void;
  onMention?: (_userId: string) => void;
  onMediaPress?: (_media: Media) => void;
  onLongPress?: () => void;
  onMediaDelete?: (_messageId: string, _mediaId: string) => Promise<void>;
}

export const GroupMessageItem: React.FC<GroupMessageItemProps> = ({
  message,
  isCurrentUser,
  groupId,
  onDelete,
  onReaction,
  onReply,
  onMention,
  onMediaPress,
  onLongPress,
  onMediaDelete,
}) => {
  const [showReactions, setShowReactions] = useState(false);
  const [_showReplies, _setShowReplies] = useState(false);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handlePressIn = useCallback(() => {
    Animated.spring(scaleAnim, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  const handlePressOut = useCallback(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  }, [scaleAnim]);

  const handleDeleteMessage = async () => {
    if (!onDelete) return;

    handleDelete({
      type: "message",
      onDelete: async () => {
        await onDelete(message.id);
      },
    });
  };

  const handleDeleteMedia = async (mediaId: string) => {
    handleDelete({
      type: "media",
      onDelete: async () => {
        try {
          // If we have a custom media delete handler, use it
          if (onMediaDelete) {
            await onMediaDelete(message.id, mediaId);
            return;
          }

          // Otherwise, implement direct Firebase update for selective media deletion
          if (groupId && message.media && message.media.length > 1) {
            // Filter out the specific media item
            const updatedMedia = message.media.filter((m) => m.id !== mediaId);
            const updatedMediaUrls = message.mediaUrls?.filter((_, index) => {
              const mediaItem = message.media?.[index];
              return mediaItem?.id !== mediaId;
            });

            // Update the message in Firebase
            const messageRef = doc(db, `groups/${groupId}/messages`, message.id);
            await updateDoc(messageRef, {
              media: updatedMedia,
              mediaUrls: updatedMediaUrls || [],
              updatedAt: serverTimestamp(),
              isEdited: true,
            });

            console.log(`✅ Media ${mediaId} deleted from message ${message.id}`);
          } else {
            // If it's the only media or no groupId, delete the entire message
            if (onDelete) {
              await onDelete(message.id);
            }
          }
        } catch (error) {
          console.error("❌ Error deleting media:", error);
          throw error;
        }
      },
    });
  };

  const renderMedia = () => {
    const mediaUrls = message.mediaUrls || message.media || [];
    if (!mediaUrls?.length) return null;

    return (
      <View style={styles.mediaContainer}>
        {mediaUrls.map((media, index) => {
          const mediaItem =
            typeof media === "string"
              ? { id: `media_${index}`, type: "image" as const, url: media }
              : media;

          return (
            <TouchableOpacity
              key={mediaItem.id || `media_${index}`}
              style={styles.mediaItem}
              onPress={() => onMediaPress?.(mediaItem)}
              onLongPress={() =>
                handleDeleteMedia(mediaItem.id || `media_${index}`)
              }
            >
              <MediaPreview
                media={mediaItem}
                onPress={() => onMediaPress?.(mediaItem)}
                onLongPress={() =>
                  handleDeleteMedia(mediaItem.id || `media_${index}`)
                }
              />
            </TouchableOpacity>
          );
        })}
      </View>
    );
  };

  const renderReactions = useCallback(() => {
    const reactions = Object.entries(message.reactions);
    if (!reactions.length) return null;

    return (
      <View style={styles.reactionsContainer}>
        {reactions.map(([emoji, userIds]) => (
          <TouchableOpacity
            key={emoji}
            style={styles.reactionButton}
            onPress={() => onReaction?.(message.id, emoji)}
          >
            <Text style={styles.reactionEmoji}>{emoji}</Text>
            <Text style={styles.reactionCount}>{userIds.length}</Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  }, [message.reactions, onReaction]);

  const renderReplies = useCallback(() => {
    if (!message.replies.length) return null;

    return (
      <View style={styles.repliesContainer}>
        {message.replies.map((reply) => (
          <View key={reply.messageId} style={styles.replyItem}>
            <Text style={styles.replySender}>{reply.senderName}</Text>
            <Text style={styles.replyContent}>{reply.content}</Text>
          </View>
        ))}
      </View>
    );
  }, [message.replies]);

  return (
    <Animated.View
      style={[
        styles.container,
        isCurrentUser ? styles.currentUserContainer : styles.otherUserContainer,
        { transform: [{ scale: scaleAnim }] },
      ]}
    >
      {!isCurrentUser && (
        <Image
          source={{ uri: message.senderProfilePic }}
          style={styles.avatar}
        />
      )}

      <View style={styles.messageContent}>
        {!isCurrentUser && (
          <Text style={styles.senderName}>{message.senderName}</Text>
        )}

        <TouchableOpacity
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          onLongPress={onLongPress}
          delayLongPress={500}
          style={[
            styles.messageBubble,
            isCurrentUser ? styles.currentUserBubble : styles.otherUserBubble
          ]}
        >
          <Text style={[
            styles.messageText,
            isCurrentUser ? styles.currentUserText : styles.otherUserText
          ]}>{message.content}</Text>
          {renderMedia()}
          <Text style={styles.timestamp}>
            {formatTimeAgo(message.timestamp)}
            {message.isEdited && " (edited)"}
          </Text>
        </TouchableOpacity>

        {renderReactions()}
        {renderReplies()}

        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => setShowReactions(!showReactions)}
          >
            <Ionicons name="happy-outline" size={20} color={IRACHAT_COLORS.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onReply?.(message)}
          >
            <Ionicons name="chatbubble-outline" size={20} color={IRACHAT_COLORS.textSecondary} />
          </TouchableOpacity>
        </View>

        {isCurrentUser && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDeleteMessage}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="trash-outline" size={20} color={IRACHAT_COLORS.error} />
          </TouchableOpacity>
        )}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    marginVertical: ResponsiveSpacing.xs,
    paddingHorizontal: ResponsiveSpacing.sm,
    minHeight: 44, // Ensure minimum touch target for mobile
  },
  currentUserContainer: {
    justifyContent: "flex-end",
  },
  otherUserContainer: {
    justifyContent: "flex-start",
  },
  avatar: {
    width: ResponsiveScale.wp(8),
    height: ResponsiveScale.wp(8),
    borderRadius: ResponsiveScale.wp(4),
    marginRight: ResponsiveSpacing.sm,
  },
  messageContent: {
    maxWidth: ResponsiveScale.wp(70),
  },
  senderName: {
    color: IRACHAT_COLORS.textSecondary,
    fontSize: ResponsiveTypography.fontSize.xs,
    marginBottom: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  messageBubble: {
    borderRadius: ResponsiveScale.borderRadius(16),
    padding: ResponsiveSpacing.sm,
    ...SHADOWS.sm,
  },
  currentUserBubble: {
    backgroundColor: IRACHAT_COLORS.sentMessage,
  },
  otherUserBubble: {
    backgroundColor: IRACHAT_COLORS.receivedMessage,
  },
  messageText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    lineHeight: ResponsiveTypography.fontSize.sm * 1.5,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  currentUserText: {
    color: IRACHAT_COLORS.textOnPrimary,
  },
  otherUserText: {
    color: IRACHAT_COLORS.text,
  },
  timestamp: {
    color: IRACHAT_COLORS.textMuted,
    fontSize: ResponsiveTypography.fontSize.xs,
    marginTop: ResponsiveSpacing.xs,
    alignSelf: "flex-end",
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  mediaContainer: {
    marginTop: ResponsiveSpacing.sm,
  },
  mediaItem: {
    marginBottom: ResponsiveSpacing.xs,
  },
  media: {
    width: ResponsiveScale.wp(50),
    height: ResponsiveScale.wp(50),
    borderRadius: ResponsiveScale.borderRadius(8),
  },
  mediaCaption: {
    fontSize: ResponsiveTypography.fontSize.xs,
    marginTop: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  reactionsContainer: {
    flexDirection: "row",
    marginTop: ResponsiveSpacing.xs,
  },
  reactionButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: IRACHAT_COLORS.surfaceElevated,
    borderRadius: ResponsiveScale.borderRadius(12),
    paddingHorizontal: ResponsiveSpacing.sm,
    paddingVertical: ResponsiveSpacing.xs,
    marginRight: ResponsiveSpacing.xs,
    ...SHADOWS.sm,
  },
  reactionEmoji: {
    fontSize: ResponsiveTypography.fontSize.sm,
    marginRight: ResponsiveSpacing.xs,
  },
  reactionCount: {
    color: IRACHAT_COLORS.text,
    fontSize: ResponsiveTypography.fontSize.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  repliesContainer: {
    marginTop: ResponsiveSpacing.sm,
    paddingLeft: ResponsiveSpacing.md,
    borderLeftWidth: 2,
    borderLeftColor: IRACHAT_COLORS.border,
  },
  replyItem: {
    marginBottom: ResponsiveSpacing.xs,
  },
  replySender: {
    color: IRACHAT_COLORS.textSecondary,
    fontSize: ResponsiveTypography.fontSize.xs,
    marginBottom: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  replyContent: {
    color: IRACHAT_COLORS.text,
    fontSize: ResponsiveTypography.fontSize.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  actionsContainer: {
    flexDirection: "row",
    marginTop: ResponsiveSpacing.xs,
  },
  actionButton: {
    padding: ResponsiveSpacing.xs,
    marginRight: ResponsiveSpacing.sm,
    minWidth: 44, // Mobile touch target
    minHeight: 44,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: ResponsiveScale.borderRadius(8),
  },
  deleteButton: {
    padding: ResponsiveSpacing.xs,
    minWidth: 44, // Mobile touch target
    minHeight: 44,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: ResponsiveScale.borderRadius(8),
  },
});
