// ⚙️ TAB SETTINGS PAGE - DRAMATICALLY REDESIGNED WITH BRIGHT ORANGE HEADER
// This is the TAB settings page with the same styling as the main settings
// Features: Bright orange header, no bottom tab bar, edge-to-edge design
// Real profile editing, privacy settings, notifications, and app preferences

import { Ionicons } from "@expo/vector-icons";

import React, { useCallback, useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Image,
  Modal,
  ScrollView,
  StyleSheet,
  Switch,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Animated,
} from "react-native";
import { useSelector } from "react-redux";
import { RootState } from "../../src/redux/store";
import { realSettingsService, UserSettings } from "../../src/services/realSettingsService";
import { navigationService, ROUTES } from "../../src/services/navigationService";
// import PrivacySettings from "../../src/components/privacy/PrivacySettings";
// import { usePrivacyLock } from "../../src/contexts/PrivacyLockContext";
import { ResponsiveListCard } from "../../src/components/ui/ResponsiveCard";
import { AnimatedInput } from "../../src/components/ui/AnimatedInput";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from "../../src/styles/iraChatDesignSystem";
import { ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../../src/utils/responsiveUtils";

export default function SettingsScreen() {
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showEditProfile, setShowEditProfile] = useState(false);
  // Privacy lock functionality (temporarily disabled)
  // const { lockApp } = usePrivacyLock();

  // Edit profile form
  const [editName, setEditName] = useState("");
  const [editBio, setEditBio] = useState("");

  // Animation and responsive values
  const fadeAnim = useState(new Animated.Value(0))[0];
  const slideAnim = useState(new Animated.Value(50))[0];
  const responsiveTypography = ResponsiveTypography;
  const responsiveSpacing = ResponsiveSpacing;
  


  // Load settings on component mount
  // Load user settings
  const loadSettings = useCallback(async () => {
    if (!currentUser?.id) {
      console.log('⚠️ No current user ID, creating default settings');
      // Create default settings if no user
      const defaultSettings: UserSettings = {
        displayName: currentUser?.displayName || currentUser?.name || 'User',
        bio: currentUser?.bio || '',
        avatar: currentUser?.avatar || null,
        phoneNumber: currentUser?.phoneNumber || '',
        profileVisibility: 'everyone',
        lastSeenVisibility: 'contacts',
        profilePhotoVisibility: 'everyone',
        statusVisibility: 'everyone',
        readReceiptsEnabled: true,
        messageNotifications: true,
        callNotifications: true,
        groupNotifications: true,
        soundEnabled: true,
        vibrationEnabled: true,
        notificationPreview: true,
        enterToSend: false,
        fontSize: 'medium',
        chatWallpaper: 'default',
        autoDownloadMedia: 'wifi',
        twoFactorEnabled: false,
        fingerprintLockEnabled: false,
        autoLockTime: 5,
        theme: 'dark',
        language: 'en',
        autoBackup: true,
        backupFrequency: 'weekly',
        includeVideos: false,
        keepChatArchived: true,
        showSecurityNotifications: true,
        updatedAt: new Date(),
      };
      setSettings(defaultSettings);
      setEditName(defaultSettings.displayName);
      setEditBio(defaultSettings.bio || "");
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      console.log('🔄 Loading settings for user:', currentUser.id);
      const userSettings = await realSettingsService.getUserSettings(currentUser.id);

      if (userSettings) {
        console.log('✅ Settings loaded successfully');
        setSettings(userSettings);
        setEditName(userSettings.displayName);
        setEditBio(userSettings.bio || "");
      } else {
        console.log('⚠️ No settings found, creating default settings');
        // Create default settings from current user data
        const defaultSettings: UserSettings = {
          displayName: currentUser.displayName || currentUser.name || 'User',
          bio: currentUser.bio || '',
          avatar: currentUser?.avatar || null,
          phoneNumber: currentUser.phoneNumber || '',
          profileVisibility: 'everyone',
          lastSeenVisibility: 'contacts',
          profilePhotoVisibility: 'everyone',
          statusVisibility: 'everyone',
          readReceiptsEnabled: true,
          messageNotifications: true,
          callNotifications: true,
          groupNotifications: true,
          soundEnabled: true,
          vibrationEnabled: true,
          notificationPreview: true,
          enterToSend: false,
          fontSize: 'medium',
          chatWallpaper: 'default',
          autoDownloadMedia: 'wifi',
          twoFactorEnabled: false,
          fingerprintLockEnabled: false,
          autoLockTime: 5,
          theme: 'dark',
          language: 'en',
          autoBackup: true,
          backupFrequency: 'weekly',
          includeVideos: false,
          keepChatArchived: true,
          showSecurityNotifications: true,
          updatedAt: new Date(),
        };
        setSettings(defaultSettings);
        setEditName(defaultSettings.displayName);
        setEditBio(defaultSettings.bio || "");

        // Save default settings
        try {
          await realSettingsService.updateUserSettings(currentUser.id, defaultSettings);
          console.log('✅ Default settings saved');
        } catch (saveError) {
          console.log('⚠️ Could not save default settings, continuing with local defaults');
        }
      }
      console.log('✅ Settings loading completed');
    } catch (error) {
      console.error('❌ Error loading settings:', error);
      // Create fallback settings even on error
      const fallbackSettings: UserSettings = {
        displayName: currentUser?.displayName || currentUser?.name || 'User',
        bio: currentUser?.bio || '',
        avatar: currentUser?.avatar || null,
        phoneNumber: currentUser?.phoneNumber || '',
        profileVisibility: 'everyone',
        lastSeenVisibility: 'contacts',
        profilePhotoVisibility: 'everyone',
        statusVisibility: 'everyone',
        readReceiptsEnabled: true,
        messageNotifications: true,
        callNotifications: true,
        groupNotifications: true,
        soundEnabled: true,
        vibrationEnabled: true,
        notificationPreview: true,
        enterToSend: false,
        fontSize: 'medium',
        chatWallpaper: 'default',
        autoDownloadMedia: 'wifi',
        twoFactorEnabled: false,
        fingerprintLockEnabled: false,
        autoLockTime: 5,
        theme: 'dark',
        language: 'en',
        autoBackup: true,
        backupFrequency: 'weekly',
        includeVideos: false,
        keepChatArchived: true,
        showSecurityNotifications: true,
        updatedAt: new Date(),
      };
      setSettings(fallbackSettings);
      setEditName(fallbackSettings.displayName);
      setEditBio(fallbackSettings.bio || "");
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id]);

  useEffect(() => {
    if (currentUser?.id) {
      loadSettings();
    }
  }, [currentUser?.id, loadSettings]);

  // Animation effects
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim]);

  // Update setting
  const updateSetting = async (key: keyof UserSettings, value: any) => {
    if (!currentUser?.id || !settings) return;
    
    try {
      const result = await realSettingsService.updateUserSettings(currentUser.id, {
        [key]: value,
      });
      
      if (result.success) {
        setSettings(prev => prev ? { ...prev, [key]: value } : null);
      } else {
        Alert.alert('Error', result.error || 'Failed to update setting');
      }
    } catch (error) {
      console.error('❌ Error updating setting:', error);
      Alert.alert('Error', 'Failed to update setting');
    }
  };

  // Handle profile picture change
  const handleChangeProfilePicture = async () => {
    if (!currentUser?.id) return;
    
    try {
      const result = await realSettingsService.changeProfilePicture(currentUser.id);
      if (result.success) {
        Alert.alert('Success!', 'Profile picture updated');
        // Refresh settings to get new avatar
        await loadSettings();
      } else {
        Alert.alert('Error', result.error || 'Failed to update profile picture');
      }
    } catch (error) {
      console.error('❌ Error changing profile picture:', error);
      Alert.alert('Error', 'Failed to update profile picture');
    }
  };

  // Handle save profile
  const handleSaveProfile = async () => {
    if (!currentUser?.id) return;
    
    try {
      const result = await realSettingsService.updateUserProfile(currentUser.id, {
        displayName: editName.trim(),
        bio: editBio.trim(),
      });
      
      if (result.success) {
        Alert.alert('Success!', 'Profile updated successfully');
        setShowEditProfile(false);
        await loadSettings();
      } else {
        Alert.alert('Error', result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('❌ Error saving profile:', error);
      Alert.alert('Error', 'Failed to update profile');
    }
  };



  // Handle privacy lock settings (temporarily disabled)
  const handlePrivacyLockSettings = () => {
    Alert.alert("Privacy Lock", "Privacy lock feature is being set up. Please check back soon!");
  };

  // Handle lock app (temporarily disabled)
  const handleLockApp = async () => {
    try {
      Alert.alert(
        "Lock App",
        "Privacy lock feature is being set up. Please check back soon!",
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error("❌ Error locking app:", error);
      Alert.alert("Error", "Failed to lock app. Please try again.");
    }
  };

  // Render setting item
  const renderSettingItem = (
    icon: string,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightElement?: React.ReactNode
  ) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon as any} size={24} color={IRACHAT_COLORS.primary} style={styles.settingIcon} />
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      {rightElement || <Ionicons name="chevron-forward" size={20} color={IRACHAT_COLORS.textSecondary} />}
    </TouchableOpacity>
  );

  // Render switch setting
  const renderSwitchSetting = (
    icon: string,
    title: string,
    subtitle: string,
    value: boolean,
    onValueChange: (_value: boolean) => void
  ) => (
    <View style={styles.settingItem}>
      <View style={styles.settingLeft}>
        <Ionicons name={icon as any} size={24} color={IRACHAT_COLORS.primary} style={styles.settingIcon} />
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          <Text style={styles.settingSubtitle}>{subtitle}</Text>
        </View>
      </View>
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: IRACHAT_COLORS.border, true: IRACHAT_COLORS.primary }}
        thumbColor={IRACHAT_COLORS.surface}
      />
    </View>
  );

  return (
    <View style={styles.fullScreenContainer}>
      {/* Status bar background to match header */}
      <View style={styles.statusBarBackground} />

      {/* COMPLETELY NEW DRAMATIC HEADER DESIGN - MATCHING MAIN SETTINGS */}
      <View style={styles.dramaticHeader}>
        {/* Decorative background pattern */}
        <View style={styles.headerPattern} />

        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => navigationService.goBack()}
            style={styles.newBackButton}
          >
            <Ionicons
              name="arrow-back"
              size={28}
              color="white"
            />
          </TouchableOpacity>
          <View style={styles.titleContainer}>
            <Animated.Text style={[styles.dramaticTitle, { opacity: fadeAnim }]}>
              ⚙️ SETTINGS
            </Animated.Text>
            <Text style={styles.subtitle}>Customize your experience</Text>
            <View style={styles.titleUnderline} />
          </View>
          <View style={styles.rightSpacer} />
        </View>

        {/* Bottom wave decoration */}
        <View style={styles.waveDecoration} />
      </View>

      <Animated.ScrollView
        style={[styles.content, {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }]}
        showsVerticalScrollIndicator={false}
      >

        {/* Show loading state or content */}
        {isLoading || !settings ? (
          <View style={styles.contentLoadingContainer}>
            <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
            <Text style={styles.loadingText}>
              {isLoading ? 'Loading settings...' : 'Initializing settings...'}
            </Text>
          </View>
        ) : (
          <>
            {/* Notifications Section */}
            <ResponsiveListCard
              style={{
                marginTop: responsiveSpacing.md,
                borderRadius: BORDER_RADIUS.lg,
                ...SHADOWS.sm,
                backgroundColor: IRACHAT_COLORS.surface
              }}
            >
              <Text style={{
                fontSize: responsiveTypography.fontSize.sm,
                fontWeight: '600' as const,
                color: IRACHAT_COLORS.textSecondary,
                paddingHorizontal: responsiveSpacing.md, // Add back padding for content readability
                paddingVertical: responsiveSpacing.sm,
                backgroundColor: IRACHAT_COLORS.backgroundDark,
                textTransform: 'uppercase',
                letterSpacing: 0.5,
                fontFamily: TYPOGRAPHY.fontFamily,
              }}>Notifications</Text>

              {renderSwitchSetting(
                "notifications-outline",
                "Message Notifications",
                "Get notified about new messages",
                settings.messageNotifications,
                (value) => updateSetting('messageNotifications', value)
              )}

          {renderSwitchSetting(
            "call-outline",
            "Call Notifications",
            "Get notified about incoming calls",
            settings.callNotifications,
            (value) => updateSetting('callNotifications', value)
          )}

          {renderSwitchSetting(
            "people-outline",
            "Group Notifications",
            "Get notified about group messages",
            settings.groupNotifications,
            (value) => updateSetting('groupNotifications', value)
          )}

          {renderSwitchSetting(
            "volume-high-outline",
            "Sound",
            "Play notification sounds",
            settings.soundEnabled,
            (value) => updateSetting('soundEnabled', value)
          )}

          {renderSwitchSetting(
            "phone-portrait-outline",
            "Vibration",
            "Vibrate for notifications",
            settings.vibrationEnabled,
            (value) => updateSetting('vibrationEnabled', value)
          )}
        </ResponsiveListCard>

        {/* Chat Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Chat Settings</Text>
          
          {renderSwitchSetting(
            "checkmark-done-outline",
            "Read Receipts",
            "Let others know when you've read their messages",
            settings.readReceiptsEnabled,
            (value) => updateSetting('readReceiptsEnabled', value)
          )}
          
          {renderSettingItem(
            "text-outline",
            "Font Size",
            `Currently: ${settings.fontSize}`,
            () => {
              // Show font size picker
              Alert.alert(
                'Font Size',
                'Choose your preferred font size',
                [
                  { text: 'Small', onPress: () => updateSetting('fontSize', 'small') },
                  { text: 'Medium', onPress: () => updateSetting('fontSize', 'medium') },
                  { text: 'Large', onPress: () => updateSetting('fontSize', 'large') },
                  { text: 'Cancel', style: 'cancel' },
                ]
              );
            }
          )}
          
          {renderSettingItem(
            "color-palette-outline",
            "Theme",
            `Currently: ${settings.theme}`,
            () => {
              // Show theme picker
              Alert.alert(
                'Theme',
                'Choose your preferred theme',
                [
                  { text: 'Light', onPress: () => updateSetting('theme', 'light') },
                  { text: 'Dark', onPress: () => updateSetting('theme', 'dark') },
                  { text: 'Auto', onPress: () => updateSetting('theme', 'auto') },
                  { text: 'Cancel', style: 'cancel' },
                ]
              );
            }
          )}
        </View>

        {/* Storage & Data Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Storage & Data</Text>
          
          {renderSwitchSetting(
            "cloud-upload-outline",
            "Auto Backup",
            "Automatically backup your chats",
            settings.autoBackup,
            (value) => updateSetting('autoBackup', value)
          )}
          
          {renderSettingItem(
            "download-outline",
            "Auto Download Media",
            `Currently: ${settings.autoDownloadMedia}`,
            () => {
              Alert.alert(
                'Auto Download Media',
                'When should media be downloaded automatically?',
                [
                  { text: 'Never', onPress: () => updateSetting('autoDownloadMedia', 'never') },
                  { text: 'Wi-Fi Only', onPress: () => updateSetting('autoDownloadMedia', 'wifi') },
                  { text: 'Always', onPress: () => updateSetting('autoDownloadMedia', 'always') },
                  { text: 'Cancel', style: 'cancel' },
                ]
              );
            }
          )}
          
          {renderSettingItem(
            "archive-outline",
            "Export Chat History",
            "Download your chat history",
            () => navigationService.navigate(ROUTES.SETTINGS.EXPORT_DATA)
          )}
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          
          {renderSettingItem(
            "help-circle-outline",
            "Help & Support",
            "Get help and contact support",
            () => navigationService.navigate(ROUTES.HELP.HELP)
          )}

          {renderSettingItem(
            "information-circle-outline",
            "About",
            "App version and information",
            () => navigationService.navigate(ROUTES.HELP.ABOUT)
          )}
        </View>

        {/* Privacy & Security Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Security</Text>

          {renderSettingItem(
            "shield-checkmark-outline",
            "Privacy Lock Settings",
            "Set up PIN/password to protect your app",
            handlePrivacyLockSettings,
            <Ionicons name="shield-checkmark-outline" size={20} color="#667eea" />
          )}

          {renderSettingItem(
            "lock-closed-outline",
            "Lock App Now",
            "Immediately lock the app for privacy",
            handleLockApp,
            <Ionicons name="lock-closed-outline" size={20} color="#6B7280" />
          )}
        </View>
        </>
        )}
      </Animated.ScrollView>

      {/* Edit Profile Modal */}
      <Modal
        visible={showEditProfile}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowEditProfile(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowEditProfile(false)}>
              <Text style={styles.modalCancel}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Edit Profile</Text>
            <TouchableOpacity onPress={handleSaveProfile}>
              <Text style={styles.modalSave}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.profilePictureSection}>
              <Image
                source={{
                  uri: settings?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(settings?.displayName || 'User')}&background=667eea&color=fff`
                }}
                style={styles.editProfileAvatar}
              />
              <TouchableOpacity
                style={styles.changePhotoButton}
                onPress={handleChangeProfilePicture}
              >
                <Text style={styles.changePhotoText}>Change Photo</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { fontSize: responsiveTypography.fontSize.base }]}>Name</Text>
              <AnimatedInput
                value={editName}
                onChangeText={setEditName}
                placeholder="Enter your name"
                maxLength={50}
                style={{
                  ...styles.textInput,
                  borderRadius: BORDER_RADIUS.md,
                  ...SHADOWS.sm
                }}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={[styles.inputLabel, { fontSize: responsiveTypography.fontSize.base }]}>Bio</Text>
              <AnimatedInput
                value={editBio}
                onChangeText={setEditBio}
                placeholder="Tell us about yourself"
                multiline
                maxLength={150}
                style={{
                  ...styles.textInput,
                  ...styles.textArea,
                  borderRadius: BORDER_RADIUS.md,
                  ...SHADOWS.sm
                }}
              />

              {/* Fallback TextInput for compatibility */}
              {editBio.length > 140 && (
                <TextInput
                  style={{
                    marginTop: SPACING.xs,
                    fontSize: responsiveTypography.fontSize.xs,
                    color: IRACHAT_COLORS.textMuted,
                    textAlign: 'right'
                  }}
                  value={`${editBio.length}/150`}
                  editable={false}
                />
              )}
            </View>

            <View style={{ marginTop: responsiveSpacing.lg }}>
              <TouchableOpacity
                onPress={handleSaveProfile}
                style={{
                  backgroundColor: IRACHAT_COLORS.primary,
                  paddingVertical: ResponsiveSpacing.md,
                  paddingHorizontal: ResponsiveSpacing.lg,
                  borderRadius: BORDER_RADIUS.lg,
                  alignItems: 'center',
                  ...SHADOWS.md
                }}
              >
                <Text style={{
                  color: 'white',
                  fontSize: ResponsiveTypography.fontSize.base,
                  fontWeight: '600',
                }}>
                  Save Changes
                </Text>
              </TouchableOpacity>
            </View>


          </ScrollView>
        </View>
      </Modal>

      {/* Privacy Settings Modal - Temporarily Disabled */}
      {/* {showPrivacySettings && (
        <Modal
          visible={showPrivacySettings}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowPrivacySettings(false)}
        >
          <PrivacySettings
            onClose={() => setShowPrivacySettings(false)}
            onLockEnabled={() => {
              setShowPrivacySettings(false);
              Alert.alert(
                "Privacy Lock Enabled",
                "Your app is now protected with privacy lock. You can lock it anytime from the settings.",
                [{ text: "OK" }]
              );
            }}
          />
        </Modal>
      )} */}

    </View>
  );
}

const styles = StyleSheet.create({
  fullScreenContainer: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999, // Ensure it's above any tab navigation
  },
  statusBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: DeviceInfo.statusBarHeight,
    backgroundColor: IRACHAT_COLORS.primary, // Use app's primary color
    zIndex: 10000,
  },
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  // COMPLETELY NEW DRAMATIC HEADER STYLES - MATCHING MAIN SETTINGS
  dramaticHeader: {
    backgroundColor: IRACHAT_COLORS.primary, // Match status bar color
    paddingTop: DeviceInfo.statusBarHeight,
    paddingBottom: ResponsiveSpacing.lg,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 15,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ResponsiveSpacing.lg,
    paddingTop: ResponsiveSpacing.md,
  },
  newBackButton: {
    width: 50,
    height: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: ResponsiveSpacing.md,
  },
  dramaticTitle: {
    fontSize: ResponsiveTypography.fontSize['2xl'],
    fontWeight: '900',
    color: 'white',
    textAlign: 'center',
    letterSpacing: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  subtitle: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '500',
  },
  titleUnderline: {
    width: 60,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 2,
    marginTop: 8,
    alignSelf: 'center',
  },
  rightSpacer: {
    width: 50, // Same as back button to center the title
  },
  headerPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    opacity: 0.3,
  },
  waveDecoration: {
    position: 'absolute',
    bottom: -1,
    left: 0,
    right: 0,
    height: 20,
    backgroundColor: IRACHAT_COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  header: {
    paddingHorizontal: 0, // Remove horizontal padding to match chat management
    paddingVertical: 0,   // Remove vertical padding to match chat management
    paddingTop: DeviceInfo.statusBarHeight + ResponsiveSpacing.md, // Use responsive status bar height
    paddingBottom: ResponsiveSpacing.md, // Add proper bottom padding
    backgroundColor: 'transparent', // Make transparent since we're using gradient
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.xl * 2,
    marginTop: ResponsiveSpacing.lg,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 18,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: IRACHAT_COLORS.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: IRACHAT_COLORS.surface,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: IRACHAT_COLORS.textSecondary,
    paddingHorizontal: ResponsiveSpacing.md, // Add back padding for content readability
    paddingVertical: 12,
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },

  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ResponsiveSpacing.md, // Add back padding for content readability
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    marginRight: 16,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: IRACHAT_COLORS.text,
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: IRACHAT_COLORS.textSecondary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 60,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  modalCancel: {
    fontSize: 16,
    color: '#6B7280',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  modalSave: {
    fontSize: 16,
    color: '#667eea',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  profilePictureSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  editProfileAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  changePhotoButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: IRACHAT_COLORS.primary,
  },
  changePhotoText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: IRACHAT_COLORS.text,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.border,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
});
