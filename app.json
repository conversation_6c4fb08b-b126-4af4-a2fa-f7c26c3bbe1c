{"expo": {"name": "IraC<PERSON>", "slug": "irachat", "version": "1.0.2", "scheme": "irachat", "orientation": "portrait", "icon": "./assets/images/LOGO.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/images/LOGO.png", "resizeMode": "contain", "backgroundColor": "#667eea"}, "optimization": {"minify": true, "bundleInBinary": true}, "platforms": ["ios", "android"], "ios": {"supportsTablet": true, "bundleIdentifier": "IraChat.ios", "infoPlist": {"NSCameraUsageDescription": "IraChat needs camera access for video calls and sharing photos/videos in chats.", "NSMicrophoneUsageDescription": "IraChat needs microphone access for voice calls, video calls, and voice messages.", "NSPhotoLibraryUsageDescription": "IraChat needs photo library access to share images and videos in chats.", "NSContactsUsageDescription": "IraChat needs contacts access to help you find friends who are already using the app.", "NSLocationWhenInUseUsageDescription": "IraChat needs location access to share your location with friends when you choose to.", "UIBackgroundModes": ["voip", "audio", "background-processing", "background-fetch"], "NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true}, "RTCAppGroupIdentifier": "group.com.irachat.webrtc", "RTCScreenSharingExtension": "IraChatScreenShare"}}, "android": {"icon": "./assets/images/LOGO.png", "adaptiveIcon": {"foregroundImage": "./assets/images/LOGO.png", "backgroundColor": "#87CEEB"}, "package": "IraChat.android", "compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 24, "edgeToEdgeEnabled": false, "permissions": ["android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.WAKE_LOCK", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_CONTACTS", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.VIBRATE", "android.permission.SYSTEM_ALERT_WINDOW", "android.permission.USE_FULL_SCREEN_INTENT", "android.permission.FOREGROUND_SERVICE", "android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_CONNECT", "android.permission.CHANGE_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE", "android.permission.CAPTURE_AUDIO_OUTPUT", "android.permission.BIND_TELECOM_CONNECTION_SERVICE"], "enableProguardInReleaseBuilds": true, "enableSeparateBuildPerCPUArchitecture": true, "universalApk": false}, "experiments": {"typedRoutes": true}, "updates": {"enabled": false, "checkAutomatically": "ON_LOAD", "fallbackToCacheTimeout": 0}, "plugins": ["expo-router", ["expo-dev-client", {"addGeneratedScheme": false}]], "owner": "irachat"}}