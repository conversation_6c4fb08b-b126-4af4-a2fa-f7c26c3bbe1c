/**
 * Chat Export Component for IraChat
 * Provides interface for exporting chat conversations
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { 
  chatExportService, 
  ExportOptions, 
  getExportFormatOptions, 
  getExportDateRangePresets 
} from '../services/chatExportService';

interface ChatExportProps {
  visible: boolean;
  onClose: () => void;
  chatId: string;
  chatName: string;
  isGroupChat?: boolean;
  messages?: any[]; // Fixed: Added messages prop to avoid database dependency
}

export const ChatExport: React.FC<ChatExportProps> = ({
  visible,
  onClose,
  chatId,
  chatName,
  isGroupChat = false,
  messages = [], // Fixed: Added messages prop with default empty array
}) => {
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'txt',
    includeMedia: false,
    includeSystemMessages: false,
  });
  const [selectedDateRange, setSelectedDateRange] = useState<string>('All Messages');
  const [isExporting, setIsExporting] = useState(false);

  const formatOptions = getExportFormatOptions();
  const dateRangePresets = getExportDateRangePresets();

  const handleDateRangeSelect = (preset: any) => {
    setSelectedDateRange(preset.label);
    if (preset.startDate && preset.endDate) {
      setExportOptions(prev => ({
        ...prev,
        dateRange: {
          startDate: preset.startDate,
          endDate: preset.endDate,
        },
      }));
    } else {
      setExportOptions(prev => ({
        ...prev,
        dateRange: undefined,
      }));
    }
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      // Fixed: Use messages prop instead of database
      if (messages.length === 0) {
        Alert.alert('Export Failed', 'No messages found to export');
        return;
      }

      const result = await chatExportService.exportChatFromMessages(
        messages,
        chatName,
        exportOptions
      );
      
      if (result.success && result.filePath) {
        Alert.alert(
          'Export Successful',
          `Chat exported successfully!\n\nMessages: ${result.messageCount}\nFile size: ${formatFileSize(result.fileSize || 0)}`,
          [
            { text: 'Share', onPress: () => handleShare(result.filePath!) },
            { text: 'Save to Device', onPress: () => handleSaveToDevice(result.filePath!) },
            { text: 'OK', style: 'default' },
          ]
        );
      } else {
        Alert.alert('Export Failed', result.error || 'Unknown error occurred');
      }
    } catch (error) {
      Alert.alert('Export Failed', 'An error occurred while exporting the chat');
    } finally {
      setIsExporting(false);
    }
  };

  const handleShare = async (filePath: string) => {
    const success = await chatExportService.shareExportedChat(filePath);
    if (success) {
      onClose();
    }
  };

  const handleSaveToDevice = async (filePath: string) => {
    const success = await chatExportService.saveToDevice(filePath);
    if (success) {
      onClose();
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#87CEEB" />
          </TouchableOpacity>
          <Text style={styles.title}>Export Chat</Text>
          <TouchableOpacity onPress={handleExport} disabled={isExporting}>
            {isExporting ? (
              <ActivityIndicator size="small" color="#87CEEB" />
            ) : (
              <Text style={styles.exportButton}>Export</Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Chat Information</Text>
            <View style={styles.infoCard}>
              <Text style={styles.chatName}>{chatName}</Text>
              <Text style={styles.chatType}>
                {isGroupChat ? 'Group Chat' : 'Individual Chat'}
              </Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Export Format</Text>
            {formatOptions.map((format) => (
              <TouchableOpacity
                key={format.value}
                style={[
                  styles.optionCard,
                  exportOptions.format === format.value && styles.selectedOption
                ]}
                onPress={() => setExportOptions(prev => ({ ...prev, format: format.value as any }))}
              >
                <View style={styles.optionContent}>
                  <Text style={[
                    styles.optionTitle,
                    exportOptions.format === format.value && styles.selectedOptionText
                  ]}>
                    {format.label}
                  </Text>
                  <Text style={[
                    styles.optionDescription,
                    exportOptions.format === format.value && styles.selectedOptionText
                  ]}>
                    {format.description}
                  </Text>
                </View>
                {exportOptions.format === format.value && (
                  <Ionicons name="checkmark-circle" size={24} color="#87CEEB" />
                )}
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Date Range</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.dateRangeRow}>
                {dateRangePresets.map((preset) => (
                  <TouchableOpacity
                    key={preset.label}
                    style={[
                      styles.dateRangeChip,
                      selectedDateRange === preset.label && styles.selectedDateRangeChip
                    ]}
                    onPress={() => handleDateRangeSelect(preset)}
                  >
                    <Text style={[
                      styles.dateRangeChipText,
                      selectedDateRange === preset.label && styles.selectedDateRangeChipText
                    ]}>
                      {preset.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Export Options</Text>
            
            <View style={styles.switchOption}>
              <View style={styles.switchContent}>
                <Text style={styles.switchTitle}>Include Media Files</Text>
                <Text style={styles.switchDescription}>
                  Export images, videos, and other media files
                </Text>
              </View>
              <Switch
                value={exportOptions.includeMedia}
                onValueChange={(value) => setExportOptions(prev => ({ ...prev, includeMedia: value }))}
                trackColor={{ false: '#2A2A2A', true: '#87CEEB' }}
                thumbColor={exportOptions.includeMedia ? '#FFFFFF' : '#888888'}
              />
            </View>

            <View style={styles.switchOption}>
              <View style={styles.switchContent}>
                <Text style={styles.switchTitle}>Include System Messages</Text>
                <Text style={styles.switchDescription}>
                  Include join/leave notifications and other system messages
                </Text>
              </View>
              <Switch
                value={exportOptions.includeSystemMessages}
                onValueChange={(value) => setExportOptions(prev => ({ ...prev, includeSystemMessages: value }))}
                trackColor={{ false: '#2A2A2A', true: '#87CEEB' }}
                thumbColor={exportOptions.includeSystemMessages ? '#FFFFFF' : '#888888'}
              />
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Export Preview</Text>
            <View style={styles.previewCard}>
              <Text style={styles.previewText}>
                Format: {formatOptions.find(f => f.value === exportOptions.format)?.label}
              </Text>
              <Text style={styles.previewText}>
                Date Range: {selectedDateRange}
              </Text>
              <Text style={styles.previewText}>
                Include Media: {exportOptions.includeMedia ? 'Yes' : 'No'}
              </Text>
              <Text style={styles.previewText}>
                Include System Messages: {exportOptions.includeSystemMessages ? 'Yes' : 'No'}
              </Text>
            </View>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  exportButton: {
    fontSize: 16,
    fontWeight: '600',
    color: '#87CEEB',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  infoCard: {
    backgroundColor: '#2A2A2A',
    padding: 16,
    borderRadius: 12,
  },
  chatName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#87CEEB',
    marginBottom: 4,
  },
  chatType: {
    fontSize: 14,
    color: '#888888',
  },
  optionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedOption: {
    borderColor: '#87CEEB',
    backgroundColor: '#1A3A4A',
  },
  optionContent: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  optionDescription: {
    fontSize: 14,
    color: '#888888',
  },
  selectedOptionText: {
    color: '#87CEEB',
  },
  dateRangeRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateRangeChip: {
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#87CEEB',
  },
  selectedDateRangeChip: {
    backgroundColor: '#87CEEB',
  },
  dateRangeChipText: {
    fontSize: 14,
    color: '#87CEEB',
  },
  selectedDateRangeChipText: {
    color: '#000000',
  },
  switchOption: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  switchContent: {
    flex: 1,
  },
  switchTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  switchDescription: {
    fontSize: 14,
    color: '#888888',
  },
  previewCard: {
    backgroundColor: '#2A2A2A',
    padding: 16,
    borderRadius: 12,
  },
  previewText: {
    fontSize: 14,
    color: '#FFFFFF',
    marginBottom: 4,
  },
});
