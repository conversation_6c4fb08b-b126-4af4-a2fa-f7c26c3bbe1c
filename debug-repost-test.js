// 🔧 DEBUG REPOST TEST SCRIPT
// Run this in your React Native debugger console to test repost functionality

console.log('🧪 Starting Repost Debug Test...');

// Test data - replace with actual values from your app
const testRepostData = {
  originalMediaId: 'test_media_123',
  originalMediaUrl: 'https://example.com/test-video.mp4', // Replace with actual URL
  originalMediaType: 'video',
  originalAuthor: 'testuser',
  originalCaption: 'Test caption',
  newCaption: 'Reposted test video',
  isStory: true,
  privacy: 'public'
};

const testUserId = 'test_user_123'; // Replace with actual user ID
const testUserName = 'Test User';

// Function to test repost eligibility
async function testRepostEligibility() {
  try {
    console.log('🔍 Testing repost eligibility...');
    
    // This would be called from your app context
    // const eligibility = await repostService.canUserRepost(testUserId);
    // console.log('📊 Eligibility result:', eligibility);
    
    console.log('✅ Eligibility test completed (replace with actual service call)');
    return { canRepost: true };
  } catch (error) {
    console.error('❌ Eligibility test failed:', error);
    return { canRepost: false, reason: error.message };
  }
}

// Function to test media copying
async function testMediaCopy() {
  try {
    console.log('📋 Testing media copy...');
    
    // Check if FileSystem is available
    if (typeof FileSystem !== 'undefined') {
      console.log('✅ FileSystem available');
      
      // Test document directory access
      console.log('📁 Document directory:', FileSystem.documentDirectory);
      
      // This would test the actual copy function
      // const tempUri = await copyMediaToTemp(testRepostData.originalMediaUrl);
      // console.log('✅ Media copied to:', tempUri);
      
    } else {
      console.log('⚠️ FileSystem not available in this context');
    }
    
    console.log('✅ Media copy test completed');
    return true;
  } catch (error) {
    console.error('❌ Media copy test failed:', error);
    return false;
  }
}

// Function to test Firebase connectivity
async function testFirebaseConnection() {
  try {
    console.log('☁️ Testing Firebase connection...');
    
    // Check if Firebase services are available
    if (typeof db !== 'undefined') {
      console.log('✅ Firestore available');
    } else {
      console.log('⚠️ Firestore not available in this context');
    }
    
    if (typeof storage !== 'undefined') {
      console.log('✅ Firebase Storage available');
    } else {
      console.log('⚠️ Firebase Storage not available in this context');
    }
    
    console.log('✅ Firebase connection test completed');
    return true;
  } catch (error) {
    console.error('❌ Firebase connection test failed:', error);
    return false;
  }
}

// Function to test InteractionManager
function testInteractionManager() {
  try {
    console.log('⚡ Testing InteractionManager...');
    
    if (typeof InteractionManager !== 'undefined') {
      console.log('✅ InteractionManager available');
      
      // Test runAfterInteractions
      InteractionManager.runAfterInteractions(() => {
        console.log('✅ InteractionManager.runAfterInteractions working');
      });
      
      return true;
    } else {
      console.log('⚠️ InteractionManager not available in this context');
      return false;
    }
  } catch (error) {
    console.error('❌ InteractionManager test failed:', error);
    return false;
  }
}

// Main test function
async function runDebugTests() {
  console.log('🚀 Running all debug tests...');
  
  const results = {
    eligibility: await testRepostEligibility(),
    mediaCopy: await testMediaCopy(),
    firebase: await testFirebaseConnection(),
    interactionManager: testInteractionManager()
  };
  
  console.log('📊 Test Results:', results);
  
  const allPassed = Object.values(results).every(result => 
    typeof result === 'boolean' ? result : result.canRepost
  );
  
  if (allPassed) {
    console.log('🎉 All tests passed! Repost functionality should work.');
  } else {
    console.log('⚠️ Some tests failed. Check the results above for issues.');
  }
  
  return results;
}

// Instructions for use
console.log(`
🔧 REPOST DEBUG INSTRUCTIONS:

1. Open your React Native app
2. Open the debugger console
3. Copy and paste this entire script
4. Run: runDebugTests()
5. Check the console output for any issues

📝 COMMON ISSUES TO CHECK:

1. Network connectivity
2. Firebase configuration
3. File system permissions
4. User authentication status
5. Media URL accessibility

🐛 IF REPOST STILL FAILS:

1. Check the detailed console logs during repost
2. Verify the media URL is accessible
3. Check Firebase Storage rules
4. Ensure user has proper permissions
5. Test with a smaller media file first

💡 LIKE BUTTON FIX:
The useInsertionEffect error should now be fixed by wrapping
state updates in InteractionManager.runAfterInteractions()
`);

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runDebugTests,
    testRepostEligibility,
    testMediaCopy,
    testFirebaseConnection,
    testInteractionManager
  };
}
