// 👥 COMPREHENSIVE GROUPS SCREEN
// Beautiful groups main screen with creation, management, and discovery
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  RefreshControl,
  TextInput,
  Modal,
  Animated,
  ActivityIndicator,
} from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { generatePlaceholderAvatar } from '../utils/avatarUtils';
import { useRouter } from 'expo-router';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { Group } from '../types/Group';
import { realGroupService, RealGroup } from '../services/realGroupService';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
};

interface ComprehensiveGroupsScreenProps {
  onGroupPress?: (_groupId: string) => void;
  onCreateGroup?: () => void;
}

export const ComprehensiveGroupsScreen: React.FC<ComprehensiveGroupsScreenProps> = ({
  onGroupPress,
  onCreateGroup,
}) => {
  const router = useRouter();
  const insets = useSafeAreaInsets();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  
  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================
  
  const [groups, setGroups] = useState<Group[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [activeTab, setActiveTab] = useState<'my_groups' | 'discover' | 'recent'>('my_groups');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [isOffline, setIsOffline] = useState(false);
  const [filteredGroups, setFilteredGroups] = useState<Group[]>([]);

  // Animation refs
  const searchAnim = useRef(new Animated.Value(0)).current;
  const fabAnim = useRef(new Animated.Value(1)).current;

  // ==================== OFFLINE & CACHING METHODS ====================

  // Network state monitoring
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      const offline = !state.isConnected;
      setIsOffline(offline);

      if (offline) {
        console.log('📱 Device is offline - using cached data');
        loadCachedGroups();
      } else {
        console.log('🌐 Device is online - syncing data');
        loadGroups();
      }
    });

    return () => unsubscribe();
  }, []);

  // Load cached groups from AsyncStorage
  const loadCachedGroups = useCallback(async () => {
    try {
      const cached = await AsyncStorage.getItem(`groups_${currentUser?.id}`);
      if (cached) {
        const parsedGroups = JSON.parse(cached);
        setGroups(parsedGroups);
        console.log('✅ Loaded cached groups:', parsedGroups.length);
      }
    } catch (error) {
      console.error('❌ Error loading cached groups:', error);
    }
  }, [currentUser?.id]);

  // Cache groups to AsyncStorage
  const cacheGroups = useCallback(async (groupsToCache: Group[]) => {
    try {
      await AsyncStorage.setItem(`groups_${currentUser?.id}`, JSON.stringify(groupsToCache));
      console.log('💾 Groups cached successfully');
    } catch (error) {
      console.error('❌ Error caching groups:', error);
    }
  }, [currentUser?.id]);

  // ==================== LIFECYCLE METHODS ====================

  const loadGroups = useCallback(async () => {
    setIsLoading(true);
    try {
      if (!currentUser?.id) {
        console.log('❌ No current user ID available');
        setGroups([]);
        return;
      }

      console.log('👥 Loading groups for user:', currentUser.id);

      // Load real groups from Firebase
      const realGroups = await realGroupService.getUserGroups(currentUser.id);

      // Convert RealGroup to Group format for UI compatibility
      const convertedGroups: Group[] = realGroups.map((realGroup: RealGroup) => ({
        id: realGroup.id,
        name: realGroup.name,
        description: realGroup.description || '',
        avatar: realGroup.avatar,
        type: realGroup.privacy as 'public' | 'private',
        isVerified: false, // Can be enhanced later
        ownerId: realGroup.createdBy,
        ownerName: realGroup.memberNames[realGroup.createdBy] || 'Unknown',
        createdAt: realGroup.createdAt,
        createdBy: realGroup.createdBy,
        // Convert string[] members to GroupMember[] format
        members: realGroup.members.map(memberId => ({
          id: memberId,
          userId: memberId,
          userName: realGroup.memberNames[memberId] || 'Unknown',
          userAvatar: realGroup.memberAvatars[memberId],
          role: realGroup.memberRoles[memberId] || 'member',
          joinedAt: realGroup.memberJoinedAt[memberId] || realGroup.createdAt,
          isOnline: false, // Will be updated by real-time listeners
          permissions: [], // Default permissions
        })),
        memberCount: realGroup.members.length,
        onlineCount: 0, // Can be calculated from online status
        admins: Object.keys(realGroup.memberRoles).filter(userId =>
          realGroup.memberRoles[userId] === 'admin' || realGroup.memberRoles[userId] === 'owner'
        ),
        settings: {
          onlyAdminsCanSendMessages: !realGroup.allowMemberMessages,
          onlyAdminsCanAddMembers: !realGroup.allowMemberInvites,
          allowMemberInvites: realGroup.allowMemberInvites,
          requireApprovalToJoin: realGroup.requireApproval,
          showMemberList: true,
          allowForwarding: true,
          allowScreenshots: true,
          muteNotifications: false,
          mentionNotifications: true,
          disappearingMessages: false,
          disappearingMessagesDuration: 24,
          readReceipts: true,
          typingIndicators: true,
          allowPhotos: true,
          allowVideos: true,
          allowDocuments: true,
          allowVoiceMessages: true,
          allowStickers: true,
          allowGifs: true,
          slowMode: false,
          slowModeDelay: 0,
          maxMembers: realGroup.maxMembers,
          autoDeleteMessages: false,
          autoDeleteDuration: 30,
        },
        lastActivity: realGroup.lastActivity,
        lastMessage: realGroup.lastMessage ? {
          id: realGroup.lastMessage.id,
          text: realGroup.lastMessage.content,
          senderId: realGroup.lastMessage.senderId,
          senderName: realGroup.lastMessage.senderName,
          timestamp: realGroup.lastMessage.timestamp,
          type: (realGroup.lastMessage.type as 'text' | 'image' | 'video' | 'audio' | 'document') || 'text',
        } : undefined,
        stats: {
          totalMembers: realGroup.members.length,
          onlineMembers: 0, // Can be calculated from online status
          totalMessages: realGroup.messageCount,
          messagesThisWeek: 0, // Can be calculated
          mediaShared: 0, // Can be calculated
          mostActiveMembers: [],
          peakOnlineTime: { hour: 14, count: 0 },
          joinRate: 0,
          leaveRate: 0,
        },
        invites: [],
        pinnedMessages: [],
        mutedMembers: [],
        bannedMembers: [],
        currentUserRole: realGroup.memberRoles[currentUser.id] || 'member',
        currentUserPermissions: realGroup.memberRoles[currentUser.id] === 'owner' || realGroup.memberRoles[currentUser.id] === 'admin'
          ? ['send_messages', 'add_members', 'remove_members', 'edit_info']
          : ['send_messages'],
        isMuted: false,
        isPinned: false,
        unreadCount: 0, // Can be calculated from unread messages
        mentionCount: 0, // Can be calculated from mentions
        hasBot: false,
        tags: [],
        trustScore: 95,
        reportCount: 0,
        isReported: false,
        isFlagged: false,
      }));

      console.log('✅ Loaded groups:', convertedGroups.length);
      setGroups(convertedGroups);

      // Cache groups for offline use
      if (!isOffline) {
        await cacheGroups(convertedGroups);
      }
    } catch (error) {
      console.error('❌ Error loading groups:', error);

      // If online request fails, try to load cached data
      if (!isOffline) {
        console.log('🔄 Falling back to cached data');
        await loadCachedGroups();
      }

      Alert.alert('Error', isOffline ? 'No cached data available' : 'Failed to load groups');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id, isOffline, cacheGroups, loadCachedGroups]);

  useEffect(() => {
    if (currentUser?.id) {
      loadGroups();
    } else {
      // Clear groups if no user
      setGroups([]);
      setFilteredGroups([]);
      setIsLoading(false);
    }
  }, [currentUser?.id, activeTab, loadGroups]);

  useEffect(() => {
    // Animate search bar
    Animated.timing(searchAnim, {
      toValue: showSearch ? 1 : 0,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [showSearch, searchAnim]);

  // ==================== DATA METHODS ====================

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadGroups();
    setIsRefreshing(false);
  }, [loadGroups]);

  const handleGroupPress = (groupId: string) => {
    onGroupPress?.(groupId);
    // Navigate to group chat
    router.push(`/group/${groupId}`);
  };

  const handleCreateGroup = () => {
    setShowCreateModal(true);
    onCreateGroup?.();
  };

  const handleCloseCreateModal = () => {
    setShowCreateModal(false);
  };

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);

    if (!query.trim()) {
      setFilteredGroups(groups);
      return;
    }

    const filtered = groups.filter(group =>
      group.name.toLowerCase().includes(query.toLowerCase()) ||
      (group.description || '').toLowerCase().includes(query.toLowerCase()) ||
      group.ownerName.toLowerCase().includes(query.toLowerCase())
    );

    setFilteredGroups(filtered);
    console.log(`🔍 Search "${query}" found ${filtered.length} groups`);
  }, [groups]);

  // Update filtered groups when groups change
  useEffect(() => {
    if (searchQuery.trim()) {
      handleSearch(searchQuery);
    } else {
      setFilteredGroups(groups);
    }
  }, [groups, searchQuery, handleSearch]);

  const getLastMessagePreview = (group: Group) => {
    if (!group.lastMessage) return 'No messages yet';
    
    const { text, type, senderName } = group.lastMessage;
    const preview = type === 'text' ? text : `${type.charAt(0).toUpperCase() + type.slice(1)}`;
    return `${senderName}: ${preview}`;
  };

  const getTimeAgo = (timestamp: Date) => {
    const now = Date.now();
    const diff = now - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}d`;
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    return 'now';
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top, minHeight: headerHeight }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={[styles.headerContent, isTablet && styles.headerContentTablet]}>
          <View style={styles.headerLeft}>
            <View style={styles.headerTitleContainer}>
              <Text style={[styles.headerTitle, isTablet && styles.headerTitleTablet]}>Groups</Text>
              {isOffline && (
                <View style={styles.offlineBadge}>
                  <Ionicons name="cloud-offline" size={16} color={COLORS.warning} />
                  <Text style={styles.offlineText}>Offline</Text>
                </View>
              )}
            </View>
            <Text style={[styles.headerSubtitle, isTablet && styles.headerSubtitleTablet]}>
              {filteredGroups.length} {filteredGroups.length === 1 ? 'group' : 'groups'}
              {searchQuery && ` (filtered from ${groups.length})`}
            </Text>
          </View>

          <View style={styles.headerRight}>
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => setShowSearch(!showSearch)}
            >
              <Ionicons name="search" size={24} color={COLORS.text} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={handleCreateGroup}
            >
              <Ionicons name="add" size={24} color={COLORS.text} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <Animated.View 
          style={[
            styles.searchContainer,
            {
              height: searchAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 50],
              }),
              opacity: searchAnim,
            },
          ]}
        >
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color={COLORS.textMuted} />
            <TextInput
              style={styles.searchInput}
              placeholder="Search groups..."
              placeholderTextColor={COLORS.textMuted}
              value={searchQuery}
              onChangeText={handleSearch}
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color={COLORS.textMuted} />
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>

        {/* Tab Bar */}
        <View style={styles.tabBar}>
          {(['my_groups', 'discover', 'recent'] as const).map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
              onPress={() => setActiveTab(tab)}
            >
              <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
                {tab === 'my_groups' ? 'My Groups' : 
                 tab === 'discover' ? 'Discover' : 'Recent'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </LinearGradient>
    </View>
  );

  const renderGroupItem = ({ item: group, index }: { item: Group; index: number }) => (
    <TouchableOpacity
      style={[
        styles.groupItem,
        isTablet && styles.groupItemTablet,
        isTablet && index % 2 === 1 && styles.groupItemTabletRight
      ]}
      onPress={() => handleGroupPress(group.id)}
      activeOpacity={0.7}
    >
      <View style={styles.groupLeft}>
        <View style={styles.groupAvatarContainer}>
          <Image
            source={{ uri: group.avatar || generatePlaceholderAvatar(group.name, 50) }}
            style={styles.groupAvatar}
          />
          {group.isVerified && (
            <View style={styles.verifiedBadge}>
              <Ionicons name="checkmark" size={10} color={COLORS.text} />
            </View>
          )}
          {group.onlineCount > 0 && (
            <View style={styles.onlineBadge}>
              <Text style={styles.onlineText}>{group.onlineCount}</Text>
            </View>
          )}
        </View>

        <View style={styles.groupInfo}>
          <View style={styles.groupHeader}>
            <Text style={styles.groupName} numberOfLines={1}>
              {group.name}
            </Text>
            {group.isPinned && (
              <Ionicons name="pin" size={14} color={COLORS.primary} />
            )}
          </View>
          
          <Text style={styles.groupLastMessage} numberOfLines={1}>
            {getLastMessagePreview(group)}
          </Text>
          
          <View style={styles.groupMeta}>
            <Text style={styles.groupMembers}>
              {group.memberCount} members
            </Text>
            <Text style={styles.metaSeparator}>•</Text>
            <Text style={styles.groupTime}>
              {getTimeAgo(group.lastActivity)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.groupRight}>
        {group.unreadCount > 0 && (
          <View style={styles.unreadBadge}>
            <Text style={styles.unreadText}>
              {group.unreadCount > 99 ? '99+' : group.unreadCount}
            </Text>
          </View>
        )}
        {group.mentionCount > 0 && (
          <View style={styles.mentionBadge}>
            <Text style={styles.mentionText}>@</Text>
          </View>
        )}
        {group.isMuted && (
          <Ionicons name="volume-mute" size={16} color={COLORS.textMuted} />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {renderHeader()}

      {/* Loading Indicator */}
      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>
            {isOffline ? 'Loading cached groups...' : 'Loading groups...'}
          </Text>
        </View>
      )}

      {/* Groups List */}
      {!isLoading && (
        <FlatList
          data={filteredGroups}
          renderItem={renderGroupItem}
          keyExtractor={(item) => item.id}
          style={[styles.groupsList, isTablet && styles.groupsListTablet]}
          contentContainerStyle={[styles.groupsListContent, isTablet && styles.groupsListContentTablet]}
          showsVerticalScrollIndicator={false}
          numColumns={isTablet ? 2 : 1}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              tintColor={COLORS.primary}
              colors={[COLORS.primary]}
              enabled={!isOffline}
            />
          }
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Ionicons name="people-outline" size={64} color={COLORS.textMuted} />
            <Text style={styles.emptyText}>No groups yet</Text>
            <Text style={styles.emptySubtext}>
              {activeTab === 'my_groups' 
                ? 'Create or join a group to get started!'
                : 'Discover new groups to join!'}
            </Text>
            <TouchableOpacity style={styles.emptyButton} onPress={handleCreateGroup}>
              <Text style={styles.emptyButtonText}>Create Group</Text>
            </TouchableOpacity>
          </View>
        )}
        />
      )}

      {/* Floating Action Button */}
      <Animated.View
        style={[
          styles.fab,
          {
            transform: [{ scale: fabAnim }],
          },
        ]}
      >
        <TouchableOpacity style={styles.fabButton} onPress={handleCreateGroup}>
          <Ionicons name="add" size={24} color={COLORS.text} />
        </TouchableOpacity>
      </Animated.View>

      {/* Create Group Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCloseCreateModal}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={handleCloseCreateModal}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Create Group</Text>
            <View style={{ width: 24 }} />
          </View>

          <View style={styles.modalContent}>
            <Text style={styles.modalText}>
              Group creation functionality will be implemented here.
            </Text>
            <TouchableOpacity
              style={styles.modalButton}
              onPress={handleCloseCreateModal}
            >
              <Text style={styles.modalButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  headerGradient: {
    paddingBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
  },
  headerContentTablet: {
    paddingHorizontal: 40,
    paddingTop: 24,
    paddingBottom: 16,
  },
  headerLeft: {
    flex: 1,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    gap: 12,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
  },
  headerTitleTablet: {
    fontSize: 32,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  headerSubtitleTablet: {
    fontSize: 16,
  },
  offlineBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  offlineText: {
    fontSize: 12,
    color: COLORS.warning,
    fontWeight: '600',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 8,
    overflow: 'hidden',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.inputBackground,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    paddingVertical: 4,
  },
  tabBar: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 8,
    gap: 8,
  },
  tabItem: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  activeTabItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  tabText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: COLORS.text,
    fontWeight: '700',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginTop: 16,
    fontWeight: '500',
  },
  groupsList: {
    flex: 1,
  },
  groupsListTablet: {
    paddingHorizontal: 20,
  },
  groupsListContent: {
    paddingVertical: 8,
  },
  groupsListContentTablet: {
    paddingVertical: 16,
  },
  groupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 2,
    borderRadius: 16,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  groupItemTablet: {
    flex: 1,
    marginHorizontal: 8,
    marginVertical: 4,
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  groupItemTabletRight: {
    marginRight: 16,
  },
  groupLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  groupAvatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  groupAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  verifiedBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: COLORS.success,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: COLORS.surface,
  },
  onlineBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
    borderWidth: 1.5,
    borderColor: COLORS.surface,
  },
  onlineText: {
    fontSize: 10,
    color: COLORS.text,
    fontWeight: '700',
  },
  groupInfo: {
    flex: 1,
  },
  groupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 6,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    flex: 1,
  },
  groupLastMessage: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  groupMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  groupMembers: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  metaSeparator: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginHorizontal: 6,
  },
  groupTime: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  groupRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  unreadBadge: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
  },
  unreadText: {
    fontSize: 12,
    color: COLORS.background,
    fontWeight: '700',
  },
  mentionBadge: {
    backgroundColor: COLORS.error,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mentionText: {
    fontSize: 10,
    color: COLORS.text,
    fontWeight: '700',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
    paddingHorizontal: 40,
  },
  emptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    color: COLORS.textMuted,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 22,
  },
  emptyButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    paddingHorizontal: 24,
    paddingVertical: 12,
    marginTop: 24,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  emptyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.background,
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
  },
  fabButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 28,
    width: 56,
    height: 56,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 10,
    borderWidth: 3,
    borderColor: COLORS.background,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  modalContent: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  modalButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.background,
  },
});
