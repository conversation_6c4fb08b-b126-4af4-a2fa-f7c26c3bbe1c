import { useRouter, useSegments } from "expo-router";
import { onAuthStateChanged } from "firebase/auth";
import { useEffect, useRef } from "react";
import { auth } from "../services/firebaseSimple";


export function AuthNavigator() {
  const segments = useSegments();
  const router = useRouter();
  const navigationTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastNavigationRef = useRef<string>("");
  const isNavigatingRef = useRef(false);

  useEffect(() => {
    if (!auth) {
      console.warn("⚠️ Auth instance not available in AuthNavigator");
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      // Prevent multiple simultaneous navigation attempts
      if (isNavigatingRef.current) {
        console.log("🔐 Navigation already in progress, skipping");
        return;
      }

      // Clear any pending navigation
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current);
      }

      // Debounce navigation to prevent rapid-fire redirects
      navigationTimeoutRef.current = setTimeout(() => {
        const inAuthGroup = segments[0] === "(auth)";
        const inTabsGroup = segments[0] === "(tabs)";

        console.log("🔐 AuthNavigator: Firebase auth state changed");
        console.log("🔐 User:", user ? "authenticated" : "not authenticated");
        console.log("🔐 Current segments:", segments);
        console.log("🔐 In auth group:", inAuthGroup);
        console.log("🔐 In tabs group:", inTabsGroup);

        // More lenient navigation logic - only redirect when absolutely necessary
        if (!user && !inAuthGroup) {
          // User not authenticated and not on auth screen - go to welcome
          const targetRoute = "/(auth)/welcome";
          if (lastNavigationRef.current !== targetRoute) {
            console.log("🔐 Redirecting unauthenticated user to welcome");
            isNavigatingRef.current = true;
            lastNavigationRef.current = targetRoute;
            router.replace(targetRoute);
            setTimeout(() => { isNavigatingRef.current = false; }, 1000);
          }
        } else if (user && inAuthGroup) {
          // User authenticated but on auth screen - go to main app
          const targetRoute = "/(tabs)";
          if (lastNavigationRef.current !== targetRoute) {
            console.log("🔐 Redirecting authenticated user to main app");
            isNavigatingRef.current = true;
            lastNavigationRef.current = targetRoute;
            router.replace(targetRoute);
            setTimeout(() => { isNavigatingRef.current = false; }, 1000);
          }
        } else {
          console.log("🔐 No navigation needed - user in correct location");
        }
      }, 300); // Increased debounce to 300ms
    });

    return () => {
      unsubscribe();
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current);
      }
    };
  }, [segments, router]);

  return null;
}
