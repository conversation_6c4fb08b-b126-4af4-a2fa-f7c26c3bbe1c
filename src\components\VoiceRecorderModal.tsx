/**
 * Voice Recorder Modal Component
 * WhatsApp-style voice recording for story narration
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { voiceRecorderService, VoiceRecording, RecordingStatus } from '../services/voiceRecorderService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface VoiceRecorderModalProps {
  visible: boolean;
  onClose: () => void;
  onRecordingComplete: (recording: VoiceRecording) => void;
}

export const VoiceRecorderModal: React.FC<VoiceRecorderModalProps> = ({
  visible,
  onClose,
  onRecordingComplete,
}) => {
  const [recordingStatus, setRecordingStatus] = useState<RecordingStatus>({
    isRecording: false,
    duration: 0,
  });
  const [currentRecording, setCurrentRecording] = useState<VoiceRecording | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  
  // Animation values
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const waveformAnims = useRef(Array.from({ length: 20 }, () => new Animated.Value(0.3))).current;
  
  // Status update interval
  const statusInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (visible) {
      // Reset state when modal opens
      setRecordingStatus({ isRecording: false, duration: 0 });
      setCurrentRecording(null);
      setIsPlaying(false);
    } else {
      // Clean up when modal closes
      cleanup();
    }
  }, [visible]);

  useEffect(() => {
    if (recordingStatus.isRecording) {
      startStatusUpdates();
      startPulseAnimation();
      startWaveformAnimation();
    } else {
      stopStatusUpdates();
      stopPulseAnimation();
      stopWaveformAnimation();
    }

    return () => {
      stopStatusUpdates();
    };
  }, [recordingStatus.isRecording]);

  const startStatusUpdates = () => {
    statusInterval.current = setInterval(async () => {
      const status = await voiceRecorderService.getRecordingStatus();
      setRecordingStatus(status);
    }, 100);
  };

  const stopStatusUpdates = () => {
    if (statusInterval.current) {
      clearInterval(statusInterval.current);
      statusInterval.current = null;
    }
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.2,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopPulseAnimation = () => {
    pulseAnim.stopAnimation();
    pulseAnim.setValue(1);
  };

  const startWaveformAnimation = () => {
    const animateWave = () => {
      const animations = waveformAnims.map((anim, index) => {
        return Animated.timing(anim, {
          toValue: 0.3 + Math.random() * 0.7,
          duration: 150 + Math.random() * 100,
          useNativeDriver: false,
        });
      });

      Animated.parallel(animations).start(() => {
        if (recordingStatus.isRecording) {
          setTimeout(animateWave, 50);
        }
      });
    };
    animateWave();
  };

  const stopWaveformAnimation = () => {
    waveformAnims.forEach(anim => {
      anim.stopAnimation();
      anim.setValue(0.3);
    });
  };

  const handleStartRecording = async () => {
    const success = await voiceRecorderService.startRecording();
    if (success) {
      setRecordingStatus({ isRecording: true, duration: 0 });
    }
  };

  const handleStopRecording = async () => {
    const recording = await voiceRecorderService.stopRecording();
    if (recording) {
      setCurrentRecording(recording);
      setRecordingStatus({ isRecording: false, duration: recording.duration });
    }
  };

  const handleCancelRecording = async () => {
    await voiceRecorderService.cancelRecording();
    setRecordingStatus({ isRecording: false, duration: 0 });
    setCurrentRecording(null);
  };

  const handlePlayRecording = async () => {
    if (!currentRecording) return;
    
    if (isPlaying) {
      await voiceRecorderService.stopPlayback();
      setIsPlaying(false);
    } else {
      const success = await voiceRecorderService.playRecording(currentRecording.uri);
      if (success) {
        setIsPlaying(true);
        // Auto-stop after duration
        setTimeout(() => {
          setIsPlaying(false);
        }, currentRecording.duration);
      }
    }
  };

  const handleUseRecording = () => {
    if (currentRecording) {
      onRecordingComplete(currentRecording);
      onClose();
    }
  };

  const cleanup = async () => {
    stopStatusUpdates();
    await voiceRecorderService.cleanup();
  };

  const renderWaveform = () => (
    <View style={styles.waveformContainer}>
      {waveformAnims.map((anim, index) => (
        <Animated.View
          key={index}
          style={[
            styles.waveformBar,
            {
              height: anim.interpolate({
                inputRange: [0, 1],
                outputRange: [4, 40],
              }),
            },
          ]}
        />
      ))}
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#000000" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Voice Note</Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Recording Area */}
        <View style={styles.recordingArea}>
          {/* Duration Display */}
          <Text style={styles.durationText}>
            {voiceRecorderService.formatDuration(recordingStatus.duration)}
          </Text>

          {/* Waveform Visualization */}
          {recordingStatus.isRecording && renderWaveform()}

          {/* Recording Button */}
          <TouchableOpacity
            style={[
              styles.recordButton,
              recordingStatus.isRecording && styles.recordingButton,
            ]}
            onPress={recordingStatus.isRecording ? handleStopRecording : handleStartRecording}
          >
            <Animated.View
              style={[
                styles.recordButtonInner,
                { transform: [{ scale: recordingStatus.isRecording ? pulseAnim : 1 }] },
              ]}
            >
              <Ionicons
                name={recordingStatus.isRecording ? 'stop' : 'mic'}
                size={32}
                color="#FFFFFF"
              />
            </Animated.View>
          </TouchableOpacity>

          {/* Status Text */}
          <Text style={styles.statusText}>
            {recordingStatus.isRecording
              ? 'Recording... Tap to stop'
              : currentRecording
              ? 'Recording complete'
              : 'Tap to start recording'
            }
          </Text>
        </View>

        {/* Playback Controls */}
        {currentRecording && (
          <View style={styles.playbackControls}>
            <TouchableOpacity
              style={styles.playButton}
              onPress={handlePlayRecording}
            >
              <Ionicons
                name={isPlaying ? 'pause' : 'play'}
                size={24}
                color="#25D366"
              />
              <Text style={styles.playButtonText}>
                {isPlaying ? 'Pause' : 'Play'}
              </Text>
            </TouchableOpacity>

            <View style={styles.recordingInfo}>
              <Text style={styles.recordingInfoText}>
                Duration: {voiceRecorderService.formatDuration(currentRecording.duration)}
              </Text>
              <Text style={styles.recordingInfoText}>
                Size: {(currentRecording.size / 1024).toFixed(1)} KB
              </Text>
            </View>
          </View>
        )}

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          {recordingStatus.isRecording ? (
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={handleCancelRecording}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          ) : currentRecording ? (
            <>
              <TouchableOpacity
                style={styles.retryButton}
                onPress={() => {
                  setCurrentRecording(null);
                  setRecordingStatus({ isRecording: false, duration: 0 });
                }}
              >
                <Text style={styles.retryButtonText}>Record Again</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.useButton}
                onPress={handleUseRecording}
              >
                <Text style={styles.useButtonText}>Use Recording</Text>
              </TouchableOpacity>
            </>
          ) : null}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  closeButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  recordingArea: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  durationText: {
    fontSize: 48,
    fontWeight: '300',
    color: '#000000',
    marginBottom: 32,
    fontVariant: ['tabular-nums'],
  },
  waveformContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 60,
    marginBottom: 32,
    gap: 2,
  },
  waveformBar: {
    width: 3,
    backgroundColor: '#25D366',
    borderRadius: 1.5,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#25D366',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  recordingButton: {
    backgroundColor: '#FF3040',
  },
  recordButtonInner: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  playbackControls: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  playButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    marginBottom: 12,
  },
  playButtonText: {
    fontSize: 16,
    color: '#25D366',
    marginLeft: 8,
    fontWeight: '500',
  },
  recordingInfo: {
    alignItems: 'center',
  },
  recordingInfoText: {
    fontSize: 14,
    color: '#999999',
    marginBottom: 2,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 12,
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  retryButton: {
    flex: 1,
    paddingVertical: 12,
    backgroundColor: '#F0F0F0',
    borderRadius: 8,
    alignItems: 'center',
  },
  retryButtonText: {
    fontSize: 16,
    color: '#666666',
    fontWeight: '500',
  },
  useButton: {
    flex: 1,
    paddingVertical: 12,
    backgroundColor: '#25D366',
    borderRadius: 8,
    alignItems: 'center',
  },
  useButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
});
