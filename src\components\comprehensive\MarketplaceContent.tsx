import React, { useState, useCallback } from 'react';
import {
  View,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BusinessPost } from '../../types/Business';
import { BusinessPostCard } from '../business/BusinessPostCard';
import { COLORS } from '../../constants/theme';

interface MarketplaceContentProps {
  businessPosts: BusinessPost[];
  currentUserId?: string;
  isLoading: boolean;
  isRefreshing: boolean;
  onRefresh: () => void;
  onLoadMore: () => void;
  onPostPress: (post: BusinessPost) => void;
  onChatPress: (businessId: string) => void;
  onFilterPress: () => void;
  onCreatePostPress: () => void;
  isOnline: boolean;
  hasBusinessProfile: boolean;
}

export const MarketplaceContent: React.FC<MarketplaceContentProps> = ({
  businessPosts,
  currentUserId,
  isLoading,
  isRefreshing,
  onRefresh,
  onLoadMore,
  onPostPress,
  onChatPress,
  onFilterPress,
  onCreatePostPress,
  isOnline,
  hasBusinessProfile,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { key: 'all', label: 'All', icon: 'grid-outline' },
    { key: 'retail', label: 'Retail', icon: 'storefront-outline' },
    { key: 'food', label: 'Food', icon: 'restaurant-outline' },
    { key: 'technology', label: 'Tech', icon: 'laptop-outline' },
    { key: 'services', label: 'Services', icon: 'construct-outline' },
    { key: 'other', label: 'Other', icon: 'ellipsis-horizontal-outline' },
  ];

  const filteredPosts = selectedCategory === 'all' 
    ? businessPosts 
    : businessPosts.filter(post => post.businessType === selectedCategory);

  const renderCategoryFilter = () => (
    <View style={styles.categoryContainer}>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={categories}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryChip,
              selectedCategory === item.key && styles.activeCategoryChip
            ]}
            onPress={() => setSelectedCategory(item.key)}
          >
            <Ionicons
              name={item.icon as any}
              size={16}
              color={selectedCategory === item.key ? '#FFFFFF' : COLORS.textSecondary}
              style={styles.categoryIcon}
            />
            <Text style={[
              styles.categoryText,
              selectedCategory === item.key && styles.activeCategoryText
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.key}
        contentContainerStyle={styles.categoryContent}
      />
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.headerTitle}>Marketplace</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerButton} onPress={onFilterPress}>
            <Ionicons name="filter-outline" size={20} color={COLORS.textSecondary} />
          </TouchableOpacity>
          
          {hasBusinessProfile && (
            <TouchableOpacity 
              style={[styles.headerButton, styles.createButton]} 
              onPress={onCreatePostPress}
            >
              <Ionicons name="add" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </View>
      </View>
      
      {renderCategoryFilter()}
      
      {!isOnline && (
        <View style={styles.offlineNotice}>
          <Ionicons name="cloud-offline" size={16} color="#EF4444" />
          <Text style={styles.offlineText}>
            Showing cached content. Connect to see latest posts.
          </Text>
        </View>
      )}
    </View>
  );

  const renderBusinessPost = ({ item }: { item: BusinessPost }) => (
    <BusinessPostCard
      post={item}
      currentUserId={currentUserId || ''}
      onPress={onPostPress}
      onChatPress={onChatPress}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="storefront-outline" size={64} color={COLORS.textSecondary} />
      <Text style={styles.emptyTitle}>
        {selectedCategory === 'all' ? 'No Business Posts' : `No ${selectedCategory} Posts`}
      </Text>
      <Text style={styles.emptySubtitle}>
        {isOnline 
          ? hasBusinessProfile 
            ? 'Be the first to post in this category!'
            : 'Register your business to start posting'
          : 'Connect to internet to see business posts'
        }
      </Text>
      
      {isOnline && hasBusinessProfile && (
        <TouchableOpacity style={styles.emptyAction} onPress={onCreatePostPress}>
          <Ionicons name="add" size={20} color="#FFFFFF" />
          <Text style={styles.emptyActionText}>Create Post</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderFooter = () => {
    if (!isLoading) return null;
    
    return (
      <View style={styles.footer}>
        <Text style={styles.loadingText}>Loading more posts...</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredPosts}
        renderItem={renderBusinessPost}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={!isLoading ? renderEmptyState : null}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            tintColor={COLORS.primary}
            colors={[COLORS.primary]}
          />
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={filteredPosts.length === 0 ? styles.emptyContainer : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    backgroundColor: '#1F2937',
    paddingBottom: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    color: COLORS.text,
    fontSize: 20,
    fontWeight: '700',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#374151',
  },
  createButton: {
    backgroundColor: COLORS.primary,
  },
  categoryContainer: {
    paddingBottom: 8,
  },
  categoryContent: {
    paddingHorizontal: 16,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#374151',
    marginRight: 8,
  },
  activeCategoryChip: {
    backgroundColor: COLORS.primary,
  },
  categoryIcon: {
    marginRight: 6,
  },
  categoryText: {
    color: COLORS.textSecondary,
    fontSize: 12,
    fontWeight: '500',
  },
  activeCategoryText: {
    color: '#FFFFFF',
  },
  offlineNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  offlineText: {
    color: '#92400E',
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyTitle: {
    color: COLORS.text,
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    color: COLORS.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 20,
  },
  emptyAction: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 24,
    marginTop: 24,
  },
  emptyActionText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  footer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  emptyContainer: {
    flexGrow: 1,
  },
});
