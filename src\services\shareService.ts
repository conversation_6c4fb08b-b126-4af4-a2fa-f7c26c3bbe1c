/**
 * Share Service for IraChat
 * Provides real sharing functionality for media, text, and app content
 */

import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { Alert, Platform } from 'react-native';

export interface ShareOptions {
  title?: string;
  message?: string;
  url?: string;
  mimeType?: string;
  dialogTitle?: string;
}

class ShareService {
  /**
   * Check if sharing is available on the device
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await Sharing.isAvailableAsync();
    } catch (error) {
      console.error('❌ Error checking sharing availability:', error);
      return false;
    }
  }

  /**
   * Share a file from a URL
   */
  async shareFile(url: string, options: ShareOptions = {}): Promise<boolean> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        Alert.alert('Error', 'Sharing is not available on this device');
        return false;
      }

      // Download file to local storage first if it's a remote URL
      let localUri = url;
      if (url.startsWith('http')) {
        const filename = url.split('/').pop() || 'shared_file';
        const fileUri = `${FileSystem.documentDirectory}${filename}`;
        
        try {
          const downloadResult = await FileSystem.downloadAsync(url, fileUri);
          localUri = downloadResult.uri;
        } catch (downloadError) {
          console.error('❌ Error downloading file for sharing:', downloadError);
          Alert.alert('Error', 'Failed to prepare file for sharing');
          return false;
        }
      }

      await Sharing.shareAsync(localUri, {
        mimeType: options.mimeType,
        dialogTitle: options.dialogTitle || 'Share',
      });

      return true;
    } catch (error) {
      console.error('❌ Error sharing file:', error);
      Alert.alert('Error', 'Failed to share file');
      return false;
    }
  }

  /**
   * Share text content
   */
  async shareText(text: string, options: ShareOptions = {}): Promise<boolean> {
    try {
      const isAvailable = await this.isAvailable();
      if (!isAvailable) {
        Alert.alert('Error', 'Sharing is not available on this device');
        return false;
      }

      // Create a temporary text file
      const filename = `shared_text_${Date.now()}.txt`;
      const fileUri = `${FileSystem.documentDirectory}${filename}`;
      
      await FileSystem.writeAsStringAsync(fileUri, text);
      
      await Sharing.shareAsync(fileUri, {
        mimeType: 'text/plain',
        dialogTitle: options.dialogTitle || 'Share Text',
      });

      // Clean up temporary file
      try {
        await FileSystem.deleteAsync(fileUri);
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up temporary file:', cleanupError);
      }

      return true;
    } catch (error) {
      console.error('❌ Error sharing text:', error);
      Alert.alert('Error', 'Failed to share text');
      return false;
    }
  }

  /**
   * Share app download link
   */
  async shareApp(): Promise<boolean> {
    const appText = `Check out IraChat - A secure messaging app!\n\nDownload: https://irachat.app/download\n\nFeatures:\n• Secure messaging\n• Voice & video calls\n• Media sharing\n• Group chats\n• Privacy focused`;
    
    return await this.shareText(appText, {
      dialogTitle: 'Share IraChat App',
    });
  }

  /**
   * Share media with proper MIME type detection
   */
  async shareMedia(url: string, mediaType: 'image' | 'video' | 'audio' | 'document'): Promise<boolean> {
    const mimeTypeMap = {
      image: 'image/*',
      video: 'video/*',
      audio: 'audio/*',
      document: 'application/*',
    };

    return await this.shareFile(url, {
      mimeType: mimeTypeMap[mediaType],
      dialogTitle: `Share ${mediaType}`,
    });
  }

  /**
   * Share multiple files (if supported by platform)
   */
  async shareMultipleFiles(urls: string[], mediaType: 'image' | 'video' | 'audio' | 'document'): Promise<boolean> {
    try {
      // For now, share the first file and show info about multiple files
      if (urls.length === 0) {
        Alert.alert('Error', 'No files to share');
        return false;
      }

      if (urls.length === 1) {
        return await this.shareMedia(urls[0], mediaType);
      }

      // For multiple files, create a text summary and share the first file
      const summary = `Sharing ${urls.length} ${mediaType} files from IraChat`;
      Alert.alert(
        'Multiple Files',
        `${summary}\n\nSharing the first file. To share all files, please select them individually.`,
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Share First', 
            onPress: () => this.shareMedia(urls[0], mediaType)
          }
        ]
      );

      return true;
    } catch (error) {
      console.error('❌ Error sharing multiple files:', error);
      Alert.alert('Error', 'Failed to share files');
      return false;
    }
  }

  /**
   * Share chat export data
   */
  async shareChatExport(chatData: string, chatName: string): Promise<boolean> {
    const filename = `${chatName}_export_${new Date().toISOString().split('T')[0]}.txt`;
    const fileUri = `${FileSystem.documentDirectory}${filename}`;
    
    try {
      await FileSystem.writeAsStringAsync(fileUri, chatData);
      
      const success = await this.shareFile(fileUri, {
        mimeType: 'text/plain',
        dialogTitle: `Share ${chatName} Chat Export`,
      });

      // Clean up temporary file
      try {
        await FileSystem.deleteAsync(fileUri);
      } catch (cleanupError) {
        console.warn('⚠️ Failed to clean up chat export file:', cleanupError);
      }

      return success;
    } catch (error) {
      console.error('❌ Error sharing chat export:', error);
      Alert.alert('Error', 'Failed to share chat export');
      return false;
    }
  }
}

export const shareService = new ShareService();
export default shareService;
