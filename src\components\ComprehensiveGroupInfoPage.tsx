// 📋 COMPREHENSIVE GROUP INFO PAGE
// Complete group information, settings, members, and management
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  RefreshControl,
  TextInput,
  Modal,
  ScrollView,
  Animated,
  Share,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Group, GroupMember, GroupInvite } from '../types/Group';
import { MostActiveMemberSystem } from './MostActiveMemberSystem';
import { realGroupService } from '../services/realGroupService';
import { generatePlaceholderAvatar } from '../utils/avatarUtils';
import * as ImagePicker from 'expo-image-picker';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
  border: '#3A3A3A',       // Border color
};

interface ComprehensiveGroupInfoPageProps {
  visible: boolean;
  group: Group;
  currentUserId: string;
  onClose: () => void;
  onEditGroup?: () => void;
  onLeaveGroup?: () => void;
  onDeleteGroup?: () => void;
  onManageMembers?: () => void;
  onGroupSettings?: () => void;
  onUserPress?: (userId: string) => void;
}

export const ComprehensiveGroupInfoPage: React.FC<ComprehensiveGroupInfoPageProps> = ({
  visible,
  group,
  currentUserId,
  onClose,
  onEditGroup,
  onLeaveGroup,
  onDeleteGroup,
  onManageMembers,
  onGroupSettings,
  onUserPress,
}) => {
  const insets = useSafeAreaInsets();
  
  // ==================== STATE MANAGEMENT ====================
  
  const [activeTab, setActiveTab] = useState<'info' | 'members' | 'media' | 'activity'>('info');
  const [showMostActive, setShowMostActive] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showCoverZoom, setShowCoverZoom] = useState(false);
  const [showAvatarZoom, setShowAvatarZoom] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [groupMembers, setGroupMembers] = useState<GroupMember[]>([]);

  // Edit group state
  const [editGroupName, setEditGroupName] = useState(group.name);
  const [editGroupDescription, setEditGroupDescription] = useState(group.description || '');
  const [editGroupAvatar, setEditGroupAvatar] = useState(group.avatar || '');
  const [editGroupCover, setEditGroupCover] = useState(group.coverImage || '');
  const [editGroupPrivacy, setEditGroupPrivacy] = useState<'public' | 'private'>((group as any).privacy || 'private');

  // New state for media and activity
  const [groupMedia, setGroupMedia] = useState<any[]>([]);
  const [groupStats, setGroupStats] = useState<any>({});
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [isUploadingCover, setIsUploadingCover] = useState(false);

  // Animation refs
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;

  // Upload image to Firebase Storage
  const uploadImageToStorage = async (localUri: string, folder: string, type: 'avatar' | 'cover' = 'avatar'): Promise<string> => {
    try {
      console.log(`📤 [DEBUG] Uploading ${type} image to ${folder}:`, localUri);
      console.log(`📤 [DEBUG] Current user ID:`, currentUserId);
      console.log(`📤 [DEBUG] Group ID:`, group.id);

      // Import Firebase Storage functions
      const { ref, uploadBytes, getDownloadURL } = await import('firebase/storage');
      const { storage, auth } = await import('../services/firebaseSimple');

      // Check if user is authenticated
      if (!auth.currentUser) {
        throw new Error('User not authenticated');
      }

      console.log(`📤 [DEBUG] Firebase auth user:`, auth.currentUser.uid);
      console.log(`📤 [DEBUG] Storage bucket:`, storage.app.options.storageBucket);
      console.log(`📤 [DEBUG] Storage app name:`, storage.app.name);

      // Convert local URI to blob
      console.log(`📤 [DEBUG] Fetching image from URI...`);
      const response = await fetch(localUri);

      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }

      console.log(`📤 [DEBUG] Converting to blob...`);
      const blob = await response.blob();
      console.log(`📤 [DEBUG] Blob created, size:`, blob.size, 'bytes, type:', blob.type);

      // Create unique filename with type prefix
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(7);
      const filename = `${type}_${timestamp}_${randomId}.jpg`;

      // Try the provided folder first, with fallback to more permissive paths
      let storageRef = ref(storage, `${folder}/${filename}`);
      let uploadPath = `${folder}/${filename}`;

      // If the folder is a group media path, we'll try it first
      // If it fails, we'll try the fallback paths

      console.log(`📤 [DEBUG] Uploading to path: ${uploadPath}`);

      // Upload the blob with metadata
      const metadata = {
        contentType: blob.type || 'image/jpeg',
        customMetadata: {
          'uploadedBy': currentUserId,
          'groupId': group.id,
          'imageType': type,
          'uploadedAt': new Date().toISOString()
        }
      };

      console.log(`📤 [DEBUG] Upload metadata:`, metadata);

      // Upload directly (using the same pattern as working examples)
      const snapshot = await uploadBytes(storageRef, blob, metadata);
      console.log('✅ [DEBUG] Upload completed:', snapshot.metadata.fullPath);

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);
      console.log('✅ [DEBUG] Download URL obtained:', downloadURL);

      return downloadURL;
    } catch (error) {
      console.error('❌ [DEBUG] Error uploading image:', error);
      console.error('❌ [DEBUG] Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        code: (error as any)?.code,
        serverResponse: (error as any)?.serverResponse
      });
      throw error;
    }
  };

  // Handle saving group changes
  const handleSaveGroupChanges = async () => {
    if (isSaving) return;

    setIsSaving(true);
    try {
      console.log('💾 [DEBUG] Saving group changes:', {
        name: editGroupName,
        description: editGroupDescription,
        avatar: editGroupAvatar,
        cover: editGroupCover,
        privacy: editGroupPrivacy
      });

      // Prepare updates object
      const updates: any = {
        name: editGroupName.trim(),
        description: editGroupDescription.trim(),
        privacy: editGroupPrivacy,
      };

      // Add avatar if changed
      if (editGroupAvatar !== group.avatar) {
        updates.avatar = editGroupAvatar;
      }

      // Add cover image if changed (note: this might need to be added to the group schema)
      if (editGroupCover !== (group as any).coverImage) {
        updates.coverImage = editGroupCover;
      }

      console.log('💾 [DEBUG] Calling realGroupService.updateGroupSettings...');

      // Use the real group service to update
      const result = await realGroupService.updateGroupSettings(
        group.id,
        currentUserId,
        updates
      );

      if (result.success) {
        console.log('✅ [DEBUG] Group updated successfully');
        Alert.alert('Success', 'Group updated successfully!');
        setShowEditModal(false);

        // Refresh the group data to reflect changes
        if (onEditGroup) {
          onEditGroup();
        }
      } else {
        console.error('❌ [DEBUG] Failed to update group:', result.error);
        Alert.alert('Error', result.error || 'Failed to update group');
      }
    } catch (error) {
      console.error('❌ [DEBUG] Error saving group changes:', error);
      Alert.alert('Error', 'Failed to update group. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible) {
      loadGroupData();
      showModal();
    } else {
      hideModal();
    }
  }, [visible]);

  // ==================== ANIMATION METHODS ====================

  const showModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // ==================== DATA METHODS ====================

  const loadGroupData = async () => {
    try {
      console.log('📋 Loading group data for:', group.id);

      // Load real group data from Firebase
      const realGroupData = await realGroupService.getGroupById(group.id);

      if (realGroupData && realGroupData.members) {
        // Convert real group members to GroupMember format
        const realMembers: GroupMember[] = realGroupData.members.map((memberId: string) => {
          const role = realGroupData.memberRoles[memberId] || 'member';

          return {
            id: memberId,
            userId: memberId,
            userName: realGroupData.memberNames[memberId] || 'Unknown User',
            userAvatar: realGroupData.memberAvatars[memberId] || undefined,
            role: role as 'owner' | 'admin' | 'member',
            joinedAt: (() => {
              const joinedAtData = realGroupData.memberJoinedAt[memberId];
              if (!joinedAtData) return new Date();

              // Handle Firebase Timestamp objects
              if (joinedAtData.toDate) return joinedAtData.toDate();

              // Handle string or number timestamps
              const date = new Date(joinedAtData);
              return isNaN(date.getTime()) ? new Date() : date;
            })(),
            isOnline: false, // Can be enhanced with online status service
            lastSeen: undefined, // Can be enhanced with last seen service
            permissions: role === 'owner' || role === 'admin'
              ? ['send_messages', 'add_members', 'remove_members', 'edit_info', 'pin_messages', 'delete_messages']
              : ['send_messages'],
            customTitle: role === 'owner' ? 'Owner' : role === 'admin' ? 'Admin' : undefined,
            invitedBy: undefined, // Can be enhanced if needed
          };
        });

        console.log('✅ Group members loaded:', realMembers.length);
        setGroupMembers(realMembers);
      } else {
        console.log('❌ No group data found');
        setGroupMembers([]);
      }

      // Load additional data
      await Promise.all([
        loadGroupMedia(),
        loadGroupStats(),
        loadRecentActivity(),
      ]);
    } catch (error) {
      console.error('❌ Error loading group data:', error);
    }
  };

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadGroupData();
    setIsRefreshing(false);
  }, []);

  const handleShareGroup = async () => {
    try {
      const shareContent = {
        message: `Join "${group.name}" on IraChat!`,
        url: group.primaryInviteLink || `https://irachat.app/group/${group.id}`,
      };
      
      await Share.share(shareContent);
    } catch (error) {
      console.error('❌ Error sharing group:', error);
    }
  };

  const handleInviteMembers = () => {
    setShowInviteModal(true);
  };

  // New functions for media and activity
  const handleMediaPress = (mediaItem: any) => {
    // Open media viewer
    console.log('Opening media:', mediaItem);
  };

  const loadGroupMedia = async () => {
    try {
      const { collection, query, where, orderBy, getDocs } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      const mediaQuery = query(
        collection(db, 'group_messages'),
        where('groupId', '==', group.id),
        where('type', 'in', ['image', 'video', 'file']),
        orderBy('timestamp', 'desc')
      );

      const snapshot = await getDocs(mediaQuery);
      const media = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      setGroupMedia(media);
    } catch (error) {
      console.error('❌ Error loading group media:', error);
    }
  };

  const loadGroupStats = async () => {
    try {
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      // Get total messages count
      const messagesQuery = query(
        collection(db, 'group_messages'),
        where('groupId', '==', group.id)
      );
      const messagesSnapshot = await getDocs(messagesQuery);

      // Get media count
      const mediaQuery = query(
        collection(db, 'group_messages'),
        where('groupId', '==', group.id),
        where('type', 'in', ['image', 'video', 'file'])
      );
      const mediaSnapshot = await getDocs(mediaQuery);

      const stats = {
        totalMessages: messagesSnapshot.size,
        mediaCount: mediaSnapshot.size,
        activeMembers: Math.max(1, groupMembers.filter(m => m.isOnline).length), // At least 1 (creator)
      };

      console.log('📊 Group stats calculated:', stats);
      setGroupStats(stats);
    } catch (error) {
      console.error('❌ Error loading group stats:', error);
    }
  };

  const loadRecentActivity = async () => {
    try {
      const { collection, query, where, orderBy, limit, getDocs } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      const activityQuery = query(
        collection(db, 'group_activity'),
        where('groupId', '==', group.id),
        orderBy('timestamp', 'desc'),
        limit(10)
      );

      const snapshot = await getDocs(activityQuery);
      const activities = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      }));

      setRecentActivity(activities);
    } catch (error) {
      console.error('❌ Error loading recent activity:', error);
    }
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';

    try {
      // Handle Firebase Timestamp objects
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);

      // Validate the date
      if (isNaN(date.getTime())) return '';

      return date.toLocaleString();
    } catch (error) {
      console.error('Error formatting time:', error);
      return '';
    }
  };

  const isCurrentUserAdmin = () => {
    return group.currentUserRole === 'owner' || group.currentUserRole === 'admin';
  };

  // Image picker functions
  const pickGroupAvatar = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to select group avatar.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        console.log('📸 [DEBUG] Selected group avatar:', result.assets[0].uri);

        // Upload image to Firebase Storage
        setIsUploadingAvatar(true);
        try {
          const uploadedUrl = await uploadImageToStorage(result.assets[0].uri, `business_posts/${group.id}`, 'avatar');
          console.log('✅ [DEBUG] Group avatar uploaded:', uploadedUrl);
          setEditGroupAvatar(uploadedUrl);
        } catch (uploadError) {
          console.error('❌ [DEBUG] Failed to upload group avatar:', uploadError);
          Alert.alert('Error', 'Failed to upload image. Please try again.');
        } finally {
          setIsUploadingAvatar(false);
        }
      }
    } catch (error) {
      console.error('❌ Error picking group avatar:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const pickGroupCover = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to select group cover.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        console.log('🖼️ [DEBUG] Selected group cover:', result.assets[0].uri);

        // Upload image to Firebase Storage
        setIsUploadingCover(true);
        try {
          const uploadedUrl = await uploadImageToStorage(result.assets[0].uri, `business_posts/${group.id}`, 'cover');
          console.log('✅ [DEBUG] Group cover uploaded:', uploadedUrl);
          setEditGroupCover(uploadedUrl);
        } catch (uploadError) {
          console.error('❌ [DEBUG] Failed to upload group cover:', uploadError);
          Alert.alert('Error', 'Failed to upload cover image. Please try again.');
        } finally {
          setIsUploadingCover(false);
        }
      }
    } catch (error) {
      console.error('❌ Error picking group cover:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const getTimeAgo = (date: Date | undefined | null) => {
    if (!date) return 'Unknown';

    // Ensure we have a proper Date object
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return 'Unknown';

    const now = Date.now();
    const diff = now - dateObj.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const months = Math.floor(days / 30);
    
    if (months > 0) return `${months} month${months > 1 ? 's' : ''} ago`;
    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    return 'Today';
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner': return COLORS.warning;
      case 'admin': return COLORS.primary;
      default: return COLORS.textSecondary;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return 'crown';
      case 'admin': return 'shield';
      default: return 'person';
    }
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="chevron-down" size={28} color={COLORS.text} />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Group Info</Text>

          <TouchableOpacity 
            style={styles.headerButton}
            onPress={handleShareGroup}
          >
            <Ionicons name="share-outline" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );

  const renderGroupHeader = () => (
    <View style={styles.groupHeader}>
      <LinearGradient
        colors={[COLORS.surface, COLORS.surfaceLight]}
        style={styles.groupHeaderGradient}
      >
        {/* Cover Image */}
        <TouchableOpacity
          onPress={() => setShowCoverZoom(true)}
          activeOpacity={0.9}
        >
          {group?.coverImage ? (
            <Image
              source={{ uri: group.coverImage }}
              style={styles.coverImage}
              onError={() => console.log('Cover image failed to load')}
            />
          ) : (
            <LinearGradient
              colors={[COLORS.primary, COLORS.primaryDark]}
              style={styles.coverImage}
            />
          )}
        </TouchableOpacity>

        {/* Group Avatar & Info */}
        <View style={styles.groupAvatarSection}>
          <TouchableOpacity
            style={styles.groupAvatarContainer}
            onPress={() => setShowAvatarZoom(true)}
            activeOpacity={0.8}
          >
            <Image
              source={{
                uri: group?.avatar || generatePlaceholderAvatar(group?.name || 'Group', 80)
              }}
              style={styles.groupAvatar}
              onError={() => console.log('Avatar failed to load')}
            />
            {group?.isVerified && (
              <View style={styles.verifiedBadge}>
                <Ionicons name="checkmark" size={16} color={COLORS.text} />
              </View>
            )}
          </TouchableOpacity>

          <View style={styles.groupInfo}>
            <Text style={styles.groupName}>{group?.name || 'Unknown Group'}</Text>
            {group?.description && (
              <Text style={styles.groupDescription}>{group.description}</Text>
            )}
            
            <View style={styles.groupStats}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{group.memberCount}</Text>
                <Text style={styles.statLabel}>Members</Text>
              </View>
              <View style={styles.statSeparator} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{group.onlineCount}</Text>
                <Text style={styles.statLabel}>Online</Text>
              </View>
              <View style={styles.statSeparator} />
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{group.stats?.totalMessages || 0}</Text>
                <Text style={styles.statLabel}>Messages</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton} onPress={handleInviteMembers}>
            <Ionicons name="person-add-outline" size={20} color={COLORS.primary} />
            <Text style={styles.actionButtonText}>Invite</Text>
          </TouchableOpacity>

          {isCurrentUserAdmin() && (
            <TouchableOpacity style={styles.actionButton} onPress={() => setShowEditModal(true)}>
              <Ionicons name="create-outline" size={20} color={COLORS.primary} />
              <Text style={styles.actionButtonText}>Edit</Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity style={styles.actionButton} onPress={() => setShowMostActive(true)}>
            <Ionicons name="trophy-outline" size={20} color={COLORS.primary} />
            <Text style={styles.actionButtonText}>Activity</Text>
          </TouchableOpacity>
          
          {isCurrentUserAdmin() && (
            <TouchableOpacity style={styles.actionButton} onPress={() => setShowSettingsModal(true)}>
              <Ionicons name="settings-outline" size={20} color={COLORS.primary} />
              <Text style={styles.actionButtonText}>Settings</Text>
            </TouchableOpacity>
          )}
        </View>
      </LinearGradient>
    </View>
  );

  const renderTabBar = () => (
    <View style={styles.tabBar}>
      {(['info', 'members', 'media', 'activity'] as const).map((tab) => (
        <TouchableOpacity
          key={tab}
          style={[styles.tabItem, activeTab === tab && styles.activeTabItem]}
          onPress={() => setActiveTab(tab)}
        >
          <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderInfoTab = () => (
    <View style={styles.tabContent}>
      {/* Group Details */}
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Group Details</Text>
        
        <View style={styles.infoItem}>
          <Ionicons name="calendar-outline" size={20} color={COLORS.primary} />
          <View style={styles.infoText}>
            <Text style={styles.infoLabel}>Created</Text>
            <Text style={styles.infoValue}>
              {group.createdAt?.toLocaleDateString() || 'Unknown'} by {group.ownerName || 'Unknown'}
            </Text>
          </View>
        </View>
        
        <View style={styles.infoItem}>
          <Ionicons name="globe-outline" size={20} color={COLORS.primary} />
          <View style={styles.infoText}>
            <Text style={styles.infoLabel}>Type</Text>
            <Text style={styles.infoValue}>
              {group.type ? (group.type.charAt(0).toUpperCase() + group.type.slice(1)) : 'Public'} Group
            </Text>
          </View>
        </View>
        
        {group.category && (
          <View style={styles.infoItem}>
            <Ionicons name="pricetag-outline" size={20} color={COLORS.primary} />
            <View style={styles.infoText}>
              <Text style={styles.infoLabel}>Category</Text>
              <Text style={styles.infoValue}>{group.category}</Text>
            </View>
          </View>
        )}
        
        {group.tags && group.tags.length > 0 && (
          <View style={styles.infoItem}>
            <Ionicons name="bookmark-outline" size={20} color={COLORS.primary} />
            <View style={styles.infoText}>
              <Text style={styles.infoLabel}>Tags</Text>
              <View style={styles.tagsContainer}>
                {(group.tags || []).map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>#{tag}</Text>
                  </View>
                ))}
              </View>
            </View>
          </View>
        )}
      </View>

      {/* Edit Group Section - Admin Only */}
      {isCurrentUserAdmin() && (
        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Edit Group</Text>

          <TouchableOpacity
            style={styles.editGroupButton}
            onPress={() => setShowEditModal(true)}
          >
            <Ionicons name="create-outline" size={20} color={COLORS.primary} />
            <Text style={styles.editGroupButtonText}>Edit Group Name & Description</Text>
            <Ionicons name="chevron-forward" size={20} color={COLORS.textMuted} />
          </TouchableOpacity>
        </View>
      )}

      {/* Group Settings - Interactive for Admins */}
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>Group Settings</Text>

        {/* Message Visibility Setting */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => {
            if (isCurrentUserAdmin()) {
              Alert.alert(
                'Message Visibility',
                'Who can see message history?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Everyone', onPress: () => console.log('Set visibility to everyone') },
                  { text: 'Admins Only', onPress: () => console.log('Set visibility to admins only') },
                ]
              );
            }
          }}
          disabled={!isCurrentUserAdmin()}
        >
          <Text style={styles.settingLabel}>Message History</Text>
          <View style={styles.settingRight}>
            <Text style={styles.settingValue}>
              {group.settings?.readReceipts ? 'Visible to All' : 'Admins Only'}
            </Text>
            {isCurrentUserAdmin() && (
              <Ionicons name="chevron-forward" size={16} color={COLORS.textMuted} />
            )}
          </View>
        </TouchableOpacity>

        {/* Add Members Setting */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => {
            if (isCurrentUserAdmin()) {
              Alert.alert(
                'Add Members Permission',
                'Who can add new members?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Everyone', onPress: () => console.log('Set add members to everyone') },
                  { text: 'Admins Only', onPress: () => console.log('Set add members to admins only') },
                ]
              );
            }
          }}
          disabled={!isCurrentUserAdmin()}
        >
          <Text style={styles.settingLabel}>Add Members</Text>
          <View style={styles.settingRight}>
            <Text style={styles.settingValue}>
              {group.settings?.onlyAdminsCanAddMembers ? 'Admins Only' : 'Everyone'}
            </Text>
            {isCurrentUserAdmin() && (
              <Ionicons name="chevron-forward" size={16} color={COLORS.textMuted} />
            )}
          </View>
        </TouchableOpacity>

        {/* Send Messages Setting */}
        <TouchableOpacity
          style={styles.settingItem}
          onPress={() => {
            if (isCurrentUserAdmin()) {
              Alert.alert(
                'Send Messages Permission',
                'Who can send messages?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Everyone', onPress: () => console.log('Set send messages to everyone') },
                  { text: 'Admins Only', onPress: () => console.log('Set send messages to admins only') },
                  { text: 'Selected Members', onPress: () => console.log('Set send messages to selected members') },
                ]
              );
            }
          }}
          disabled={!isCurrentUserAdmin()}
        >
          <Text style={styles.settingLabel}>Send Messages</Text>
          <View style={styles.settingRight}>
            <Text style={styles.settingValue}>
              {group.settings?.onlyAdminsCanSendMessages ? 'Admins Only' : 'Everyone'}
            </Text>
            {isCurrentUserAdmin() && (
              <Ionicons name="chevron-forward" size={16} color={COLORS.textMuted} />
            )}
          </View>
        </TouchableOpacity>
      </View>

      {/* Danger Zone */}
      <View style={styles.dangerSection}>
        <Text style={styles.sectionTitle}>Actions</Text>
        
        {group.currentUserRole === 'owner' ? (
          <TouchableOpacity style={styles.dangerButton} onPress={onDeleteGroup}>
            <Ionicons name="trash-outline" size={20} color={COLORS.error} />
            <Text style={styles.dangerButtonText}>Delete Group</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity style={styles.dangerButton} onPress={onLeaveGroup}>
            <Ionicons name="exit-outline" size={20} color={COLORS.error} />
            <Text style={styles.dangerButtonText}>Leave Group</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const renderMemberItem = ({ item: member }: { item: GroupMember }) => (
    <TouchableOpacity
      style={styles.memberItem}
      onPress={() => {
        console.log('👤 Member pressed:', member.userName, member.userId);
        console.log('🔗 onUserPress function:', !!onUserPress);
        onUserPress?.(member.userId);
      }}
      activeOpacity={0.7}
    >
      <View style={styles.memberLeft}>
        <View style={styles.memberAvatarContainer}>
          <Image 
            source={{ uri: member.userAvatar || 'https://via.placeholder.com/40' }} 
            style={styles.memberAvatar} 
          />
          {member.isOnline && (
            <View style={styles.onlineIndicator} />
          )}
        </View>

        <View style={styles.memberInfo}>
          <View style={styles.memberHeader}>
            <Text style={styles.memberName}>{member.userName}</Text>
            {member.customTitle && (
              <Text style={styles.customTitle}>{member.customTitle}</Text>
            )}
          </View>
          
          <View style={styles.memberMeta}>
            <View style={styles.roleContainer}>
              <Ionicons 
                name={getRoleIcon(member.role) as any} 
                size={12} 
                color={getRoleColor(member.role)} 
              />
              <Text style={[styles.roleText, { color: getRoleColor(member.role) }]}>
                {member.role.charAt(0).toUpperCase() + member.role.slice(1)}
              </Text>
            </View>
            <Text style={styles.joinedText}>
              Joined {getTimeAgo(member.joinedAt)}
            </Text>
          </View>
        </View>
      </View>

      <View style={styles.memberRight}>
        {member.userId === currentUserId && (
          <Text style={styles.youLabel}>You</Text>
        )}
        <TouchableOpacity style={styles.memberActionButton}>
          <Ionicons name="chevron-forward" size={16} color={COLORS.textMuted} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );

  const renderMembersTab = () => (
    <View style={styles.tabContent}>
      <FlatList
        data={groupMembers}
        renderItem={renderMemberItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={handleRefresh}
            tintColor={COLORS.primary}
            colors={[COLORS.primary]}
          />
        }
        ListHeaderComponent={() => (
          <View style={styles.membersHeader}>
            <Text style={styles.membersCount}>
              {group.memberCount} member{group.memberCount !== 1 ? 's' : ''}
            </Text>
            {isCurrentUserAdmin() && (
              <TouchableOpacity style={styles.manageButton} onPress={onManageMembers}>
                <Text style={styles.manageButtonText}>Manage</Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      />
    </View>
  );

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="none">
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <Animated.View 
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Back Button */}
          <View style={styles.backButtonContainer}>
            <TouchableOpacity style={styles.backButton} onPress={onClose}>
              <Ionicons name="chevron-back" size={24} color={COLORS.text} />
            </TouchableOpacity>
          </View>

          {activeTab === 'info' && (
            <ScrollView
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
              bounces={true}
            >
              {renderGroupHeader()}
              {renderTabBar()}
              {renderInfoTab()}
            </ScrollView>
          )}
          {activeTab === 'members' && (
            <View style={{ flex: 1 }}>
              <FlatList
                data={groupMembers}
                renderItem={renderMemberItem}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                ListHeaderComponent={() => (
                  <View>
                    {renderGroupHeader()}
                    {renderTabBar()}
                  </View>
                )}
                refreshControl={
                  <RefreshControl
                    refreshing={isRefreshing}
                    onRefresh={handleRefresh}
                    colors={[COLORS.primary]}
                    tintColor={COLORS.primary}
                  />
                }
                contentContainerStyle={styles.tabContent}
              />
            </View>
          )}
          {activeTab === 'media' && (
            <FlatList
              data={groupMedia}
              keyExtractor={(item) => item.id}
              numColumns={3}
              ListHeaderComponent={() => (
                <View>
                  {renderGroupHeader()}
                  {renderTabBar()}
                </View>
              )}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.mediaItem}
                  onPress={() => handleMediaPress(item)}
                >
                  <Image source={{ uri: item.thumbnail || item.url }} style={styles.mediaThumbnail} />
                  {item.type === 'video' && (
                    <View style={styles.videoOverlay}>
                      <Ionicons name="play" size={20} color="white" />
                    </View>
                  )}
                </TouchableOpacity>
              )}
              contentContainerStyle={styles.mediaGrid}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.emptyState}>
                  <Ionicons name="images" size={48} color="#ccc" />
                  <Text style={styles.emptyStateText}>No media shared yet</Text>
                </View>
              }
            />
          )}
          {activeTab === 'activity' && (
            <ScrollView
              style={{ flex: 1 }}
              showsVerticalScrollIndicator={false}
              bounces={true}
            >
              {renderGroupHeader()}
              {renderTabBar()}
              <View style={styles.activityContainer}>
                <View style={styles.activitySection}>
                  <Text style={styles.activitySectionTitle}>Group Statistics</Text>
                  <View style={styles.activityStatItem}>
                    <Text style={styles.activityStatLabel}>Total Messages:</Text>
                    <Text style={styles.activityStatValue}>{groupStats.totalMessages || 0}</Text>
                  </View>
                  <View style={styles.activityStatItem}>
                    <Text style={styles.activityStatLabel}>Active Members:</Text>
                    <Text style={styles.activityStatValue}>{groupStats.activeMembers || 0}</Text>
                  </View>
                  <View style={styles.activityStatItem}>
                    <Text style={styles.activityStatLabel}>Media Shared:</Text>
                    <Text style={styles.activityStatValue}>{groupStats.mediaCount || 0}</Text>
                  </View>
                </View>

                <View style={styles.activitySection}>
                  <Text style={styles.activitySectionTitle}>Recent Activity</Text>
                  {recentActivity.map((activity, index) => (
                    <View key={index} style={styles.activityItem}>
                      <Text style={styles.activityText}>{activity.description}</Text>
                      <Text style={styles.activityTime}>{formatTime(activity.timestamp)}</Text>
                    </View>
                  ))}
                </View>
              </View>
            </ScrollView>
          )}
        </Animated.View>
      </Animated.View>

      {/* Most Active Member Modal */}
      <MostActiveMemberSystem
        groupId={group.id}
        currentUserId={currentUserId}
        visible={showMostActive}
        onClose={() => setShowMostActive(false)}
        onUserPress={onUserPress}
      />

      {/* Invite Members Modal */}
      <Modal visible={showInviteModal} animationType="slide" transparent>
        <View style={styles.inviteModalBackdrop}>
          <View style={styles.inviteModal}>
            <View style={styles.inviteModalHeader}>
              <Text style={styles.inviteModalTitle}>Invite Members</Text>
              <TouchableOpacity onPress={() => setShowInviteModal(false)}>
                <Ionicons name="close" size={24} color={COLORS.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.inviteModalContent}>
              <TouchableOpacity
                style={styles.inviteOption}
                onPress={handleShareGroup}
              >
                <Ionicons name="share-outline" size={24} color={COLORS.primary} />
                <View style={styles.inviteOptionText}>
                  <Text style={styles.inviteOptionTitle}>Share Group Link</Text>
                  <Text style={styles.inviteOptionSubtitle}>Share via messages, social media, etc.</Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.inviteOption}
                onPress={async () => {
                  setShowInviteModal(false);
                  try {
                    // Generate QR code data
                    const qrData = {
                      type: 'group_invite',
                      groupId: group.id,
                      groupName: group.name,
                      inviteLink: group.primaryInviteLink || `https://irachat.app/group/${group.id}`,
                      timestamp: Date.now()
                    };

                    const qrString = JSON.stringify(qrData);
                    console.log('📱 QR Code data generated:', qrString);

                    // For now, copy the invite link to clipboard
                    if (Platform.OS === 'web' && navigator.clipboard) {
                      await navigator.clipboard.writeText(qrData.inviteLink);
                      Alert.alert('QR Code', 'Invite link copied to clipboard!\n\nQR code generation will be available soon.');
                    } else {
                      Alert.alert('QR Code', `Group: ${group.name}\nInvite Link: ${qrData.inviteLink}\n\nQR code generation will be available soon.`);
                    }
                  } catch (error) {
                    console.error('❌ Error generating QR code:', error);
                    Alert.alert('Error', 'Failed to generate QR code');
                  }
                }}
              >
                <Ionicons name="qr-code-outline" size={24} color={COLORS.primary} />
                <View style={styles.inviteOptionText}>
                  <Text style={styles.inviteOptionTitle}>QR Code</Text>
                  <Text style={styles.inviteOptionSubtitle}>Let others scan to join</Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.inviteOption}
                onPress={async () => {
                  setShowInviteModal(false);
                  try {
                    console.log('📞 Opening IraChat contacts for group invitations');

                    // Navigate to IraChat contacts page with group invite context
                    const inviteData = {
                      type: 'group_invite',
                      groupId: group.id,
                      groupName: group.name,
                      groupDescription: group.description || '',
                      inviteLink: group.primaryInviteLink || `https://irachat.app/group/${group.id}`,
                    };

                    // Store invite data in a way that the contacts page can access it
                    // This could be through navigation params, global state, or AsyncStorage
                    console.log('📱 Group invite data:', inviteData);

                    // Navigate to fast-contacts page (IraChat contacts)
                    // You would replace this with your actual navigation method
                    if (typeof window !== 'undefined' && window.location) {
                      // Web navigation
                      const params = new URLSearchParams({
                        mode: 'group_invite',
                        groupId: group.id,
                        groupName: group.name,
                        inviteLink: inviteData.inviteLink
                      });
                      window.location.href = `/fast-contacts?${params.toString()}`;
                    } else {
                      // Mobile navigation - you would use your navigation service here
                      Alert.alert(
                        'Navigate to Contacts',
                        'This would navigate to the IraChat contacts page where you can select contacts to send group invites to.',
                        [
                          { text: 'OK', onPress: () => console.log('Navigate to /fast-contacts with group invite context') }
                        ]
                      );
                    }
                  } catch (error) {
                    console.error('❌ Error opening contacts:', error);
                    Alert.alert('Error', 'Failed to open contacts');
                  }
                }}
              >
                <Ionicons name="people-outline" size={24} color={COLORS.primary} />
                <View style={styles.inviteOptionText}>
                  <Text style={styles.inviteOptionTitle}>From Contacts</Text>
                  <Text style={styles.inviteOptionSubtitle}>Invite from your phone contacts</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Edit Group Modal */}
      <Modal visible={showEditModal} animationType="slide" transparent>
        <View style={styles.inviteModalBackdrop}>
          <View style={styles.inviteModal}>
            <View style={styles.inviteModalHeader}>
              <Text style={styles.inviteModalTitle}>Edit Group</Text>
              <TouchableOpacity onPress={() => setShowEditModal(false)}>
                <Ionicons name="close" size={24} color={COLORS.text} />
              </TouchableOpacity>
            </View>

            <View style={styles.inviteModalContent}>
              <Text style={styles.editLabel}>Group Name</Text>
              <TextInput
                style={styles.editInput}
                value={editGroupName}
                onChangeText={setEditGroupName}
                placeholder="Enter group name"
                placeholderTextColor={COLORS.textMuted}
                maxLength={50}
              />

              <Text style={styles.editLabel}>Description</Text>
              <TextInput
                style={[styles.editInput, styles.editTextArea]}
                value={editGroupDescription}
                onChangeText={setEditGroupDescription}
                placeholder="Enter group description"
                placeholderTextColor={COLORS.textMuted}
                multiline
                numberOfLines={3}
                maxLength={200}
              />

              {/* Group Avatar Section */}
              <Text style={styles.editLabel}>Group Photo</Text>
              <TouchableOpacity
                style={[styles.photoSelector, isUploadingAvatar && { opacity: 0.6 }]}
                onPress={pickGroupAvatar}
                disabled={isUploadingAvatar}
              >
                {isUploadingAvatar ? (
                  <View style={styles.photoPlaceholder}>
                    <ActivityIndicator size="small" color={COLORS.primary} />
                    <Text style={styles.photoPlaceholderText}>Uploading...</Text>
                  </View>
                ) : editGroupAvatar ? (
                  <Image source={{ uri: editGroupAvatar }} style={styles.selectedPhoto} />
                ) : (
                  <View style={styles.photoPlaceholder}>
                    <Ionicons name="camera" size={24} color={COLORS.textMuted} />
                    <Text style={styles.photoPlaceholderText}>Select Group Photo</Text>
                  </View>
                )}
              </TouchableOpacity>

              {/* Group Cover Section */}
              <Text style={styles.editLabel}>Cover Photo</Text>
              <TouchableOpacity
                style={[styles.coverSelector, isUploadingCover && { opacity: 0.6 }]}
                onPress={pickGroupCover}
                disabled={isUploadingCover}
              >
                {isUploadingCover ? (
                  <View style={styles.coverPlaceholder}>
                    <ActivityIndicator size="small" color={COLORS.primary} />
                    <Text style={styles.photoPlaceholderText}>Uploading...</Text>
                  </View>
                ) : editGroupCover ? (
                  <Image source={{ uri: editGroupCover }} style={styles.selectedCover} />
                ) : (
                  <View style={styles.coverPlaceholder}>
                    <Ionicons name="image" size={24} color={COLORS.textMuted} />
                    <Text style={styles.photoPlaceholderText}>Select Cover Photo</Text>
                  </View>
                )}
              </TouchableOpacity>

              {/* Privacy Settings */}
              <Text style={styles.editLabel}>Privacy</Text>
              <View style={styles.privacySelector}>
                <TouchableOpacity
                  style={[
                    styles.privacyOption,
                    editGroupPrivacy === 'private' && styles.privacyOptionSelected
                  ]}
                  onPress={() => setEditGroupPrivacy('private')}
                >
                  <Ionicons
                    name="lock-closed"
                    size={20}
                    color={editGroupPrivacy === 'private' ? COLORS.primary : COLORS.textMuted}
                  />
                  <Text style={[
                    styles.privacyOptionText,
                    editGroupPrivacy === 'private' && styles.privacyOptionTextSelected
                  ]}>Private</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.privacyOption,
                    editGroupPrivacy === 'public' && styles.privacyOptionSelected
                  ]}
                  onPress={() => setEditGroupPrivacy('public')}
                >
                  <Ionicons
                    name="globe"
                    size={20}
                    color={editGroupPrivacy === 'public' ? COLORS.primary : COLORS.textMuted}
                  />
                  <Text style={[
                    styles.privacyOptionText,
                    editGroupPrivacy === 'public' && styles.privacyOptionTextSelected
                  ]}>Public</Text>
                </TouchableOpacity>
              </View>

              <View style={styles.editButtons}>
                <TouchableOpacity
                  style={[styles.editButton, styles.editButtonCancel]}
                  onPress={() => setShowEditModal(false)}
                >
                  <Text style={styles.editButtonTextCancel}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.editButton,
                    styles.editButtonSave,
                    isSaving && { opacity: 0.6 }
                  ]}
                  onPress={handleSaveGroupChanges}
                  disabled={isSaving}
                >
                  <Text style={styles.editButtonTextSave}>
                    {isSaving ? 'Saving...' : 'Save'}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Group Settings Modal */}
      <Modal visible={showSettingsModal} animationType="slide" transparent>
        <View style={styles.inviteModalBackdrop}>
          <View style={styles.inviteModal}>
            <View style={styles.inviteModalHeader}>
              <Text style={styles.inviteModalTitle}>Group Settings</Text>
              <TouchableOpacity onPress={() => setShowSettingsModal(false)}>
                <Ionicons name="close" size={24} color={COLORS.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.inviteModalContent} showsVerticalScrollIndicator={false}>
              {/* Message History Setting */}
              <View style={styles.settingSection}>
                <Text style={styles.settingSectionTitle}>Message Permissions</Text>

                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => {
                    Alert.alert(
                      'Message History',
                      'Who can see message history?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Everyone', onPress: () => console.log('Set visibility to everyone') },
                        { text: 'Admins Only', onPress: () => console.log('Set visibility to admins only') },
                      ]
                    );
                  }}
                >
                  <View style={styles.settingContent}>
                    <Ionicons name="eye-outline" size={20} color={COLORS.primary} />
                    <View style={styles.settingTextContainer}>
                      <Text style={styles.settingLabel}>Message History</Text>
                      <Text style={styles.settingDescription}>Control who can see past messages</Text>
                    </View>
                    <Text style={styles.settingValue}>
                      {group.settings?.readReceipts ? 'Everyone' : 'Admins Only'}
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => {
                    Alert.alert(
                      'Send Messages',
                      'Who can send messages in this group?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Everyone', onPress: () => console.log('Set send messages to everyone') },
                        { text: 'Admins Only', onPress: () => console.log('Set send messages to admins only') },
                      ]
                    );
                  }}
                >
                  <View style={styles.settingContent}>
                    <Ionicons name="chatbubble-outline" size={20} color={COLORS.primary} />
                    <View style={styles.settingTextContainer}>
                      <Text style={styles.settingLabel}>Send Messages</Text>
                      <Text style={styles.settingDescription}>Control who can send messages</Text>
                    </View>
                    <Text style={styles.settingValue}>
                      {group.settings?.onlyAdminsCanSendMessages ? 'Admins Only' : 'Everyone'}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              {/* Member Management */}
              <View style={styles.settingSection}>
                <Text style={styles.settingSectionTitle}>Member Management</Text>

                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => {
                    Alert.alert(
                      'Add Members',
                      'Who can add new members to this group?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Everyone', onPress: () => console.log('Set add members to everyone') },
                        { text: 'Admins Only', onPress: () => console.log('Set add members to admins only') },
                      ]
                    );
                  }}
                >
                  <View style={styles.settingContent}>
                    <Ionicons name="person-add-outline" size={20} color={COLORS.primary} />
                    <View style={styles.settingTextContainer}>
                      <Text style={styles.settingLabel}>Add Members</Text>
                      <Text style={styles.settingDescription}>Control who can invite new members</Text>
                    </View>
                    <Text style={styles.settingValue}>
                      {group.settings?.onlyAdminsCanAddMembers ? 'Admins Only' : 'Everyone'}
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => {
                    Alert.alert(
                      'Edit Group Info',
                      'Who can edit group name and description?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Everyone', onPress: () => console.log('Set edit info to everyone') },
                        { text: 'Admins Only', onPress: () => console.log('Set edit info to admins only') },
                      ]
                    );
                  }}
                >
                  <View style={styles.settingContent}>
                    <Ionicons name="create-outline" size={20} color={COLORS.primary} />
                    <View style={styles.settingTextContainer}>
                      <Text style={styles.settingLabel}>Edit Group Info</Text>
                      <Text style={styles.settingDescription}>Control who can edit group details</Text>
                    </View>
                    <Text style={styles.settingValue}>Admins Only</Text>
                  </View>
                </TouchableOpacity>
              </View>

              {/* Privacy Settings */}
              <View style={styles.settingSection}>
                <Text style={styles.settingSectionTitle}>Privacy & Security</Text>

                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => {
                    Alert.alert(
                      'Group Privacy',
                      'Change group privacy setting',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Public', onPress: () => console.log('Set to public') },
                        { text: 'Private', onPress: () => console.log('Set to private') },
                      ]
                    );
                  }}
                >
                  <View style={styles.settingContent}>
                    <Ionicons name="lock-closed-outline" size={20} color={COLORS.primary} />
                    <View style={styles.settingTextContainer}>
                      <Text style={styles.settingLabel}>Group Privacy</Text>
                      <Text style={styles.settingDescription}>Control group visibility</Text>
                    </View>
                    <Text style={styles.settingValue}>
                      {(group as any).privacy === 'public' ? 'Public' : 'Private'}
                    </Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.settingItem}
                  onPress={() => {
                    Alert.alert(
                      'Disappearing Messages',
                      'Set messages to disappear after a certain time',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        { text: 'Off', onPress: () => console.log('Turn off disappearing messages') },
                        { text: '24 Hours', onPress: () => console.log('Set to 24 hours') },
                        { text: '7 Days', onPress: () => console.log('Set to 7 days') },
                      ]
                    );
                  }}
                >
                  <View style={styles.settingContent}>
                    <Ionicons name="timer-outline" size={20} color={COLORS.primary} />
                    <View style={styles.settingTextContainer}>
                      <Text style={styles.settingLabel}>Disappearing Messages</Text>
                      <Text style={styles.settingDescription}>Auto-delete messages after time</Text>
                    </View>
                    <Text style={styles.settingValue}>Off</Text>
                  </View>
                </TouchableOpacity>
              </View>
            </ScrollView>
          </View>
        </View>
      </Modal>

      {/* Cover Photo Zoom Modal */}
      <Modal visible={showCoverZoom} animationType="fade" transparent>
        <View style={styles.zoomModalBackdrop}>
          <TouchableOpacity
            style={styles.zoomModalBackdrop}
            onPress={() => setShowCoverZoom(false)}
            activeOpacity={1}
          >
            <View style={styles.zoomModalContent}>
              <TouchableOpacity
                style={styles.zoomCloseButton}
                onPress={() => setShowCoverZoom(false)}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>

              {group?.coverImage ? (
                <Image
                  source={{ uri: group.coverImage }}
                  style={styles.zoomedCoverImage}
                  resizeMode="contain"
                />
              ) : (
                <LinearGradient
                  colors={[COLORS.primary, COLORS.primaryDark]}
                  style={styles.zoomedCoverImage}
                />
              )}

              <Text style={styles.zoomImageTitle}>Cover Photo</Text>
            </View>
          </TouchableOpacity>
        </View>
      </Modal>

      {/* Avatar Zoom Modal */}
      <Modal visible={showAvatarZoom} animationType="fade" transparent>
        <View style={styles.zoomModalBackdrop}>
          <TouchableOpacity
            style={styles.zoomModalBackdrop}
            onPress={() => setShowAvatarZoom(false)}
            activeOpacity={1}
          >
            <View style={styles.zoomModalContent}>
              <TouchableOpacity
                style={styles.zoomCloseButton}
                onPress={() => setShowAvatarZoom(false)}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>

              <Image
                source={{
                  uri: group?.avatar || generatePlaceholderAvatar(group?.name || 'Group', 300)
                }}
                style={styles.zoomedAvatarImage}
                resizeMode="contain"
              />

              <Text style={styles.zoomImageTitle}>{group?.name || 'Group'}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </Modal>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
  },
  headerButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  groupHeader: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  groupHeaderGradient: {
    paddingBottom: 20,
  },
  coverImage: {
    width: '100%',
    height: 120,
    position: 'absolute',
    top: 0,
  },
  groupAvatarSection: {
    paddingHorizontal: 20,
    paddingTop: 80,
    alignItems: 'center',
  },
  groupAvatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  groupAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: COLORS.background,
  },
  verifiedBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: COLORS.success,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.background,
  },
  groupInfo: {
    alignItems: 'center',
    marginBottom: 20,
  },
  groupName: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  groupDescription: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 16,
    paddingHorizontal: 20,
  },
  groupStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.primary,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginTop: 2,
  },
  statSeparator: {
    width: 1,
    height: 20,
    backgroundColor: COLORS.surfaceLight,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    gap: 8,
  },
  actionButton: {
    flex: 1,
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    paddingVertical: 12,
    alignItems: 'center',
    gap: 4,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  actionButtonText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  tabItem: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 16,
    marginHorizontal: 2,
  },
  activeTabItem: {
    backgroundColor: COLORS.primary,
  },
  tabText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  activeTabText: {
    color: COLORS.background,
    fontWeight: '700',
  },
  tabContent: {
    flex: 1,
  },
  infoSection: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
    gap: 12,
  },
  infoText: {
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 4,
  },
  tag: {
    backgroundColor: COLORS.primary + '20',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  tagText: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  settingLabel: {
    fontSize: 16,
    color: COLORS.text,
  },
  settingValue: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  dangerSection: {
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: COLORS.error + '30',
  },
  dangerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.error + '20',
    borderRadius: 12,
    paddingVertical: 12,
    gap: 8,
  },
  dangerButtonText: {
    fontSize: 16,
    color: COLORS.error,
    fontWeight: '600',
  },
  membersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  membersCount: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  manageButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 6,
  },
  manageButtonText: {
    fontSize: 14,
    color: COLORS.background,
    fontWeight: '600',
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  memberLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  memberAvatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1.5,
    borderColor: COLORS.primary,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: COLORS.success,
    borderWidth: 2,
    borderColor: COLORS.background,
  },
  memberInfo: {
    flex: 1,
  },
  memberHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
    gap: 8,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
  },
  customTitle: {
    fontSize: 12,
    color: COLORS.warning,
    backgroundColor: COLORS.warning + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    fontWeight: '600',
  },
  memberMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
  },
  joinedText: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  memberRight: {
    alignItems: 'flex-end',
    gap: 4,
  },
  youLabel: {
    fontSize: 12,
    color: COLORS.primary,
    backgroundColor: COLORS.primary + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
    fontWeight: '600',
  },
  memberActionButton: {
    padding: 4,
  },
  comingSoonText: {
    fontSize: 16,
    color: COLORS.textMuted,
    textAlign: 'center',
    marginTop: 40,
  },
  mediaItem: {
    width: 80,
    height: 80,
    margin: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mediaThumbnail: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  mediaGrid: {
    padding: 8,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: COLORS.textMuted,
    marginTop: 8,
  },
  activityContainer: {
    flex: 1,
  },
  activitySection: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  activitySectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
  },
  activityStatItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  activityStatLabel: {
    fontSize: 16,
    color: COLORS.text,
  },
  activityStatValue: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.primary,
  },
  activityItem: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  activityText: {
    fontSize: 14,
    color: COLORS.text,
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 12,
    color: COLORS.textMuted,
  },

  // Invite Modal Styles
  inviteModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  inviteModal: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    margin: 20,
    maxWidth: 400,
    width: '90%',
  },
  inviteModalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  inviteModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  inviteModalContent: {
    padding: 20,
  },
  inviteOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    borderRadius: 12,
    marginBottom: 12,
    backgroundColor: COLORS.surfaceLight,
  },
  inviteOptionText: {
    marginLeft: 16,
    flex: 1,
  },
  inviteOptionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 4,
  },
  inviteOptionSubtitle: {
    fontSize: 14,
    color: COLORS.textMuted,
  },

  // Edit Group Modal Styles
  editLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 8,
    marginTop: 16,
  },
  editInput: {
    backgroundColor: COLORS.inputBackground,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.text,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  editTextArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  editButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  editButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 12,
    alignItems: 'center',
  },
  editButtonCancel: {
    backgroundColor: COLORS.surfaceLight,
  },
  editButtonSave: {
    backgroundColor: COLORS.primary,
  },
  editButtonTextCancel: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text,
  },
  editButtonTextSave: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.background,
  },

  // Photo Selector Styles
  photoSelector: {
    height: 80,
    backgroundColor: COLORS.inputBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  selectedPhoto: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  photoPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  photoPlaceholderText: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
  },
  coverSelector: {
    height: 100,
    backgroundColor: COLORS.inputBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  selectedCover: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  coverPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Privacy Selector Styles
  privacySelector: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 8,
  },
  privacyOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: COLORS.inputBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
    gap: 8,
  },
  privacyOptionSelected: {
    borderColor: COLORS.primary,
    backgroundColor: `${COLORS.primary}20`,
  },
  privacyOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.textMuted,
  },
  privacyOptionTextSelected: {
    color: COLORS.primary,
  },

  // Settings Modal Styles
  settingSection: {
    marginBottom: 24,
  },
  settingSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  settingTextContainer: {
    flex: 1,
  },
  settingDescription: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginTop: 2,
  },

  // Back Button Styles
  backButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 16,
    zIndex: 1000,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Edit Group Button Styles
  editGroupButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 12,
    gap: 12,
  },
  editGroupButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text,
  },

  // Enhanced Setting Item Styles
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },

  // Zoom Modal Styles
  zoomModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomModalContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  zoomCloseButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1000,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  zoomedCoverImage: {
    width: SCREEN_WIDTH - 40,
    height: 250,
    borderRadius: 12,
  },
  zoomedAvatarImage: {
    width: 300,
    height: 300,
    borderRadius: 150,
    borderWidth: 4,
    borderColor: COLORS.primary,
  },
  zoomImageTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginTop: 20,
    textAlign: 'center',
  },
});
