Write-Host "Setting up F: Drive Development Tools..." -ForegroundColor Green

# Set Java
$javaPath = "F:\IraChat\java17\jdk-17.0.12+7"
if (Test-Path $javaPath) {
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath, [EnvironmentVariableTarget]::User)
    Write-Host "Set JAVA_HOME to: $javaPath" -ForegroundColor Green
}

# Set Android SDK
$androidPaths = @("F:\Android\Sdk", "F:\android\sdk", "F:\AndroidSDK")
foreach ($path in $androidPaths) {
    if (Test-Path $path) {
        [Environment]::SetEnvironmentVariable("ANDROID_HOME", $path, [EnvironmentVariableTarget]::User)
        [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $path, [EnvironmentVariableTarget]::User)
        Write-Host "Set ANDROID_HOME to: $path" -ForegroundColor Green
        break
    }
}

# Update PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)
$newPaths = @()

# Add Java to PATH
if (Test-Path "$javaPath\bin") {
    $newPaths += "$javaPath\bin"
}

# Add Android tools to PATH
$androidHome = [Environment]::GetEnvironmentVariable("ANDROID_HOME", [EnvironmentVariableTarget]::User)
if ($androidHome) {
    $androidToolPaths = @(
        "$androidHome\platform-tools",
        "$androidHome\cmdline-tools\latest\bin",
        "$androidHome\tools",
        "$androidHome\emulator"
    )
    foreach ($toolPath in $androidToolPaths) {
        if (Test-Path $toolPath) {
            $newPaths += $toolPath
        }
    }
}

# Add Node.js if found on F: drive
$nodePaths = @("F:\nodejs", "F:\Node", "F:\Tools\nodejs")
foreach ($nodePath in $nodePaths) {
    if (Test-Path "$nodePath\node.exe") {
        $newPaths += $nodePath
        break
    }
}

# Update PATH with new paths
foreach ($newPath in $newPaths) {
    if ($currentPath -notlike "*$newPath*") {
        $currentPath = "$currentPath;$newPath"
        Write-Host "Added to PATH: $newPath" -ForegroundColor Green
    }
}

[Environment]::SetEnvironmentVariable("PATH", $currentPath, [EnvironmentVariableTarget]::User)

Write-Host "`nSetup completed! Please restart your terminal." -ForegroundColor Yellow
Write-Host "Then run: npx expo start" -ForegroundColor Cyan

Read-Host "Press Enter to exit"
