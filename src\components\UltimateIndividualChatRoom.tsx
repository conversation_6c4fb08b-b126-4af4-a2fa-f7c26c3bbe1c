// 🚀 ULTIMATE INDIVIDUAL CHAT ROOM - WHATSAPP FUNCTIONALITY + TELEGRAM UI
// Complete WhatsApp-like functionality with beautiful Telegram design

import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Alert,
  StyleSheet,
  StatusBar,
  Dimensions,
  useColorScheme,
  Image,

  Animated,
  PanResponder,
  Vibration,

} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { realTimeMessagingService } from '../services/realTimeMessagingService';
import { IraChatWallpaper } from './ui/IraChatWallpaper';
import VoiceMessageRecorder from './VoiceMessageRecorder';
import { MediaPicker } from './MediaPicker';
import { wallpaperService } from '../services/wallpaperService';
import { WallpaperPicker } from './WallpaperPicker';
import { ChatExport } from './ChatExport';
import { contactService } from '../services/contactService';
import { MessageSearch } from './MessageSearch';
import ImageViewer from './ImageViewer';
import VideoPlayer from './VideoPlayer';
import AudioMessagePlayer from './AudioMessagePlayer';
import MediaGallery from './MediaGallery';
import FileViewer from './FileViewer';
import { offlineMessageService } from '../services/offlineMessageService';
import { networkStateManager } from '../services/networkStateManager';
import { userProfileService } from '../services/userProfileService';
// Legacy imports removed - functionality now handled through MediaPicker component
import { Audio } from 'expo-av';
import * as Sharing from 'expo-sharing';
import * as MediaLibrary from 'expo-media-library';
import { chatClearService } from '../services/chatClearService';

// Import new message selection and media components
import { MessageSelectionProvider } from '../contexts/MessageSelectionContext';
import MessageActionToolbar from './chat/MessageActionToolbar';
import { SelectableMessageBubble } from './chat/SelectableMessageBubble';
import FloatingEmojiBar, { useFloatingEmojiBar } from './chat/FloatingEmojiBar';
import MediaMessageBubble from './chat/MediaMessageBubble';
import MediaUploadModal, { MediaUploadData } from './chat/MediaUploadModal';
import MediaFullView from './chat/MediaFullView';
import { messageActionsService } from '../services/messageActionsService';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

// Telegram-style theme colors
const LIGHT_THEME = {
  background: '#FFFFFF',
  headerBackground: '#517DA2',
  headerText: '#FFFFFF',
  messageBackground: '#F1F1F1',
  ownMessageBackground: '#4A90E2',
  ownMessageText: '#FFFFFF',
  otherMessageText: '#000000',
  inputBackground: '#FFFFFF',
  inputBorder: '#E1E1E1',
  inputText: '#000000',
  placeholderText: '#999999',
  sendButtonActive: '#4A90E2',
  sendButtonInactive: '#C7C7CC',
  timeText: '#666666', // Darker for better visibility
  divider: '#E1E1E1',
};

const DARK_THEME = {
  background: '#0E1621',
  headerBackground: '#1C2733',
  headerText: '#FFFFFF',
  messageBackground: '#182533',
  ownMessageBackground: '#4A90E2',
  ownMessageText: '#FFFFFF',
  otherMessageText: '#FFFFFF',
  inputBackground: '#182533',
  inputBorder: '#2B3A4A',
  inputText: '#FFFFFF',
  placeholderText: '#8E8E93',
  sendButtonActive: '#4A90E2',
  sendButtonInactive: '#48484A',
  timeText: '#B0B0B0', // Lighter for dark theme but still visible
  divider: '#2B3A4A',
};

// Enhanced WhatsApp-style message interface
interface WhatsAppMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'video' | 'audio' | 'voice' | 'document' | 'file' | 'location' | 'contact' | 'deleted';
  mediaUrl?: string;
  thumbnailUrl?: string;
  fileName?: string;
  fileSize?: number;
  mimeType?: string;
  duration?: number; // For audio/video
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  contact?: {
    name: string;
    phoneNumber: string;
  };
  replyTo?: {
    messageId: string;
    content: string;
    senderName: string;
    type: string;
  };
  isForwarded?: boolean;
  forwardedFrom?: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  isStarred?: boolean;
  reactions?: { [emoji: string]: string[] }; // emoji -> array of user IDs
}

// Props interface
interface ChatRoomProps {
  chatId: string;
  partnerId: string;
  partnerName?: string;
  partnerAvatar?: string;
  currentUserId: string;
  isDarkMode?: boolean;
}

// Main component
export const UltimateIndividualChatRoom: React.FC<ChatRoomProps> = ({
  chatId,
  partnerId,
  partnerName = 'Unknown User',
  partnerAvatar = '',
  currentUserId,
  isDarkMode,
}) => {
  const router = useRouter();
  const systemColorScheme = useColorScheme();

  // Debug logging for currentUserId
  console.log('🔍 UltimateIndividualChatRoom Props:', {
    chatId,
    partnerId,
    currentUserId,
    partnerName,
    partnerAvatar
  });

  // Early validation of currentUserId
  if (!currentUserId || currentUserId === 'undefined' || currentUserId === 'null') {
    console.error('❌ Invalid currentUserId in UltimateIndividualChatRoom:', currentUserId);
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#000' }}>
        <Text style={{ color: '#fff', fontSize: 18, marginBottom: 10 }}>Authentication Error</Text>
        <Text style={{ color: '#ccc', fontSize: 14, textAlign: 'center', paddingHorizontal: 20 }}>
          User ID is not available. Please restart the app or log in again.
        </Text>
      </View>
    );
  }

  // Debug logging for currentUserId
  useEffect(() => {
    console.log('🔍 UltimateIndividualChatRoom Debug:', {
      chatId,
      partnerId,
      currentUserId,
      currentUserIdType: typeof currentUserId,
      currentUserIdLength: currentUserId?.length
    });
  }, [chatId, partnerId, currentUserId]);

  // Enhanced WhatsApp-style state management
  const [messages, setMessages] = useState<WhatsAppMessage[]>([]);
  const [messageText, setMessageText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [partnerTyping] = useState(false); // Keep for future typing indicator
  const [replyingTo, setReplyingTo] = useState<WhatsAppMessage | null>(null);
  const [editingMessage, setEditingMessage] = useState<WhatsAppMessage | null>(null);
  const [showMediaPicker, setShowMediaPicker] = useState(false);

  const [wallpaperConfig, setWallpaperConfig] = useState<any>(null);
  const [isOnline, setIsOnline] = useState(true);
  const [lastSeen, setLastSeen] = useState<Date | null>(null);
  const [unreadCount] = useState(0); // Keep for future unread count feature
  const [showThemeMenu, setShowThemeMenu] = useState(false);
  const [manualTheme, setManualTheme] = useState<boolean | null>(true); // Default to dark theme
  const [showWallpaperPicker, setShowWallpaperPicker] = useState(false);
  const [currentWallpaper, setCurrentWallpaper] = useState<any>(null);
  const [showChatExport, setShowChatExport] = useState(false);
  const [showMessageSearch, setShowMessageSearch] = useState(false);
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedImageUri] = useState<string>(''); // Keep for legacy compatibility
  const [videoPlayerVisible, setVideoPlayerVisible] = useState(false);
  const [selectedVideoUri] = useState<string>(''); // Keep for legacy compatibility
  const [mediaGalleryVisible, setMediaGalleryVisible] = useState(false);
  const [mediaGalleryItems, setMediaGalleryItems] = useState<any[]>([]);
  const [mediaGalleryIndex, setMediaGalleryIndex] = useState(0);

  // New state for enhanced message features
  const [showMediaUploadModal, setShowMediaUploadModal] = useState(false);
  const [mediaFullViewVisible, setMediaFullViewVisible] = useState(false);
  const [selectedMediaForFullView, setSelectedMediaForFullView] = useState<{
    type: 'image' | 'video';
    uri: string;
    caption?: string;
  } | null>(null);

  // Floating emoji bar state
  const { emojiBarState, showEmojiBar, hideEmojiBar } = useFloatingEmojiBar();

  // Determine theme - use manual override, then prop, then system preference
  const isUsingDarkMode = manualTheme !== null ? manualTheme : (isDarkMode !== undefined ? isDarkMode : systemColorScheme === 'dark');
  const theme = isUsingDarkMode ? DARK_THEME : LIGHT_THEME;

  const flatListRef = useRef<FlatList>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const recordingRef = useRef<Audio.Recording | null>(null);

  // Format last seen time
  const formatLastSeen = useCallback((lastSeenDate: Date) => {
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - lastSeenDate.getTime()) / (1000 * 60));

    if (diffMinutes < 1) {
      return 'just now';
    } else if (diffMinutes < 60) {
      return `${diffMinutes} minutes ago`;
    } else if (diffMinutes < 1440) {
      const hours = Math.floor(diffMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      return lastSeenDate.toLocaleDateString();
    }
  }, []);

  // SIMPLE MESSAGE LOADING - No complex fallbacks, just works!
  const loadMessages = useCallback(async () => {
    try {
      console.log('🔥 Loading messages for chat:', chatId);
      setError(null);

      // Set up real-time listener - this is the ONLY method we use
      const unsubscribe = realTimeMessagingService.subscribeToMessages(
        chatId,
        (firebaseMessages) => {
          console.log(`📨 Received ${firebaseMessages.length} messages`);
          
          if (firebaseMessages && firebaseMessages.length > 0) {
            // Convert to WhatsApp message format
            const convertedMessages: WhatsAppMessage[] = firebaseMessages.map(msg => ({
              id: msg.id,
              chatId: msg.chatId,
              senderId: msg.senderId,
              senderName: msg.senderName || 'Unknown',
              content: msg.content,
              timestamp: msg.timestamp instanceof Date ? msg.timestamp : new Date(msg.timestamp),
              type: msg.type === 'file' ? 'document' : (msg.type as any) || 'text',
              status: 'delivered',
              mediaUrl: msg.mediaUrl,
              thumbnailUrl: msg.thumbnailUrl,
              fileName: msg.fileName,
              fileSize: (msg as any).fileSize,
              duration: (msg as any).duration,
              location: (msg as any).location,
              contact: (msg as any).contact,
              replyTo: (msg as any).replyTo,
              isForwarded: (msg as any).isForwarded,
              forwardedFrom: (msg as any).forwardedFrom,
              isStarred: (msg as any).isStarred,
              reactions: (msg as any).reactions,
            }));
            
            setMessages(convertedMessages);
            setError(null);
          } else {
            // No messages found - show empty state
            setMessages([]);
          }
          
          setIsLoading(false);
        }
      );

      // Timeout to prevent infinite loading
      setTimeout(() => {
        setIsLoading(false);
        if (messages.length === 0) {
          console.log('📭 No messages loaded after timeout');
        }
      }, 5000);

      return unsubscribe;
    } catch (error) {
      console.error('❌ Failed to load messages:', error);
      setError('Failed to load messages');
      setIsLoading(false);
      setMessages([]);
    }
  }, [chatId, messages.length]);

  // ENHANCED MESSAGE SENDING WITH ERROR HANDLING
  const sendMessage = useCallback(async () => {
    if (!messageText.trim()) return;

    // Validate currentUserId
    if (!currentUserId) {
      console.error('❌ Cannot send message: currentUserId is undefined');
      Alert.alert('Error', 'User not properly authenticated. Please restart the app.');
      return;
    }

    const messageToSend = messageText.trim();
    setMessageText(''); // Clear input immediately for better UX

    // Clear reply state IMMEDIATELY when send button is pressed
    const replyToMessage = replyingTo;
    if (replyingTo) {
      console.log('🚀 INSTANTLY clearing reply modal');
      setReplyingTo(null);
    }

    try {
      // Check if we're editing a message
      if (editingMessage) {
        console.log('✏️ Editing message:', editingMessage.id);

        const result = await messageActionsService.editMessage(
          {
            id: editingMessage.id,
            chatId: editingMessage.chatId || chatId,
            senderId: editingMessage.senderId,
            content: editingMessage.content,
            type: editingMessage.type as any,
            timestamp: editingMessage.timestamp,
            isOwn: editingMessage.senderId === currentUserId,
          },
          messageToSend,
          currentUserId
        );

        if (result.success) {
          console.log('✅ Message edited successfully');
          setEditingMessage(null);
          await loadMessages();
        } else {
          console.error('❌ Failed to edit message:', result.error);
          setMessageText(messageToSend);
        }
        return;
      }

      console.log('🚀 Sending message:', { chatId, currentUserId, content: messageToSend, replyTo: replyToMessage?.id });

      // Send message with reply information if replying
      const result = await realTimeMessagingService.sendMessage(
        chatId,
        currentUserId,
        'You',
        messageToSend,
        'text',
        undefined, // mediaUrl
        undefined, // senderAvatar - will be handled by the service
        replyToMessage ? {
          messageId: replyToMessage.id,
          content: replyToMessage.content,
          senderName: replyToMessage.senderName || replyToMessage.senderId,
          type: replyToMessage.type
        } : undefined
      );

      console.log('📤 Send result:', result);

      if (result.success) {
        console.log('✅ Message sent successfully');

        // Reply state already cleared immediately when send button was pressed

        // Scroll to bottom after sending
        setTimeout(() => {
          flatListRef.current?.scrollToEnd({ animated: true });
        }, 100);
      } else {
        console.error('❌ Send failed:', result.error);
        Alert.alert('Error', result.error || 'Failed to send message');
        // Restore message text if send failed
        setMessageText(messageToSend);
      }
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', `Failed to send message: ${errorMessage}`);
      // Restore message text if send failed
      setMessageText(messageToSend);
    }
  }, [messageText, chatId, currentUserId, replyingTo, editingMessage]);

  // WHATSAPP-STYLE VOICE MESSAGE RECORDING
  const startVoiceRecording = useCallback(async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission needed', 'Please grant microphone permission to record voice messages');
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const recording = new Audio.Recording();
      await recording.prepareToRecordAsync({
        android: {
          extension: '.m4a',
          outputFormat: 2, // MPEG_4
          audioEncoder: 3, // AAC
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: 1, // MPEG4AAC
          audioQuality: 1, // HIGH
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
        web: {
          mimeType: 'audio/webm',
          bitsPerSecond: 128000,
        },
      });
      await recording.startAsync();

      recordingRef.current = recording;
      setIsRecording(true);
      setRecordingDuration(0);

      Vibration.vibrate(50);

      // Start duration timer
      const timer = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      (recording as any).timer = timer;
    } catch (error) {
      console.error('Failed to start recording:', error);
      Alert.alert('Error', 'Failed to start voice recording');
    }
  }, []);

  const stopVoiceRecording = useCallback(async () => {
    try {
      if (!recordingRef.current || !isRecording) {
        console.log('⚠️ No active recording to stop');
        return;
      }

      const recording = recordingRef.current;

      // Clear timer first
      if ((recording as any).timer) {
        clearInterval((recording as any).timer);
      }

      // Stop and unload recording
      await recording.stopAndUnloadAsync();

      // Update state immediately to prevent double calls
      setIsRecording(false);
      setRecordingDuration(0);
      recordingRef.current = null;

      const uri = recording.getURI();
      if (uri) {
        console.log('Voice recording saved to:', uri);

        // Send voice message
        const result = await realTimeMessagingService.sendMessage(
          chatId,
          currentUserId,
          'You',
          'Voice message',
          'audio',
          uri,
          undefined // senderAvatar
        );

        if (result.success) {
          console.log('Voice message sent successfully');
          Vibration.vibrate(50);
        } else {
          Alert.alert('Error', 'Failed to send voice message');
        }
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      // Reset state on error
      setIsRecording(false);
      setRecordingDuration(0);
      recordingRef.current = null;
      Alert.alert('Error', 'Failed to stop voice recording');
    }
  }, [chatId, currentUserId, isRecording]);

  const cancelVoiceRecording = useCallback(async () => {
    try {
      if (!recordingRef.current || !isRecording) {
        console.log('⚠️ No active recording to cancel');
        return;
      }

      const recording = recordingRef.current;

      // Clear timer first
      if ((recording as any).timer) {
        clearInterval((recording as any).timer);
      }

      // Stop and unload recording
      await recording.stopAndUnloadAsync();

      // Update state immediately
      setIsRecording(false);
      setRecordingDuration(0);
      recordingRef.current = null;

      console.log('Voice recording cancelled');
    } catch (error) {
      console.error('Failed to cancel recording:', error);
      // Reset state on error
      setIsRecording(false);
      setRecordingDuration(0);
      recordingRef.current = null;
    }
  }, [isRecording]);

  // ENHANCED MEDIA HANDLING WITH MULTIPLE SELECTION
  const handleMediaSelect = useCallback(async (media: any) => {
    try {
      // Handle multiple media items
      if (Array.isArray(media)) {
        for (const item of media) {
          await sendSingleMediaItem(item);
        }
      } else {
        // Handle single media item
        await sendSingleMediaItem(media);
      }
    } catch (error) {
      console.error('❌ Error handling media selection:', error);
      Alert.alert('Error', 'Failed to send media');
    }
  }, [chatId, currentUserId]);

  // Send single media item
  const sendSingleMediaItem = async (item: any) => {
    try {
      console.log('📤 Sending media item:', item);

      let messageType = item.type;
      let content = '';

      // Set content based on media type
      switch (item.type) {
        case 'audio':
          content = item.name || 'Audio message';
          messageType = 'audio';
          break;
        case 'document':
          content = item.name || 'Document';
          messageType = 'file';
          break;
        case 'image':
          content = item.caption || '';
          messageType = 'image';
          break;
        case 'video':
          content = item.caption || '';
          messageType = 'video';
          break;
        default:
          content = item.caption || '';
      }

      // Use the media upload service for proper media handling
      const sendResult = await realTimeMessagingService.sendMediaMessage(
        chatId,
        currentUserId,
        'You',
        undefined, // senderAvatar
        item.uri,
        messageType as 'image' | 'video' | 'audio' | 'file',
        content || undefined,
        undefined // replyTo
      );

      if (!sendResult.success) {
        throw new Error(sendResult.error || 'Failed to send media');
      }

      console.log('✅ Media sent successfully:', sendResult.messageId);
    } catch (error) {
      console.error('❌ Failed to send media item:', error);
      throw error;
    }
  };

  // Media handling is now done through MediaPicker component with caption support

  // Camera functionality is now handled through MediaPicker component

  // Document picker functionality is now handled through MediaPicker component

  // Location sharing functionality is now handled through MediaPicker component

  // Contact sharing functionality is now handled through MediaPicker component

  // WHATSAPP-STYLE TYPING INDICATOR
  const handleTextChange = useCallback((text: string) => {
    setMessageText(text);

    if (!isTyping && text.length > 0) {
      setIsTyping(true);
      // Send typing indicator to partner
      realTimeMessagingService.sendTypingIndicator(chatId, currentUserId, true);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      realTimeMessagingService.sendTypingIndicator(chatId, currentUserId, false);
    }, 2000);
  }, [chatId, currentUserId, isTyping]);

  // WHATSAPP-STYLE MESSAGE ACTIONS
  const handleMessageLongPress = useCallback((message: WhatsAppMessage) => {
    // This is now handled by the SelectableMessageBubble component
    // The message selection is managed by MessageSelectionContext
    console.log('Message long pressed:', message.id);
  }, []);

  // NETWORK STATE MANAGEMENT
  useEffect(() => {
    const unsubscribe = networkStateManager.subscribe((isConnected) => {
      setIsOnline(isConnected);
      if (isConnected) {
        // Sync offline messages when back online
        offlineMessageService.syncOfflineMessages(chatId);
      }
    });

    return unsubscribe;
  }, [chatId]);

  // WALLPAPER LOADING
  useEffect(() => {
    const loadWallpaper = async () => {
      try {
        const config = await wallpaperService.getWallpaperForChat(chatId);
        console.log('🎨 Loaded wallpaper config:', config);
        setWallpaperConfig(config);

        // If no wallpaper found, set a default dark theme wallpaper
        if (!config || Object.keys(config).length === 0) {
          const defaultConfig = {
            type: 'gradient',
            gradientId: 'dark',
          };
          console.log('🎨 Setting default wallpaper:', defaultConfig);
          setWallpaperConfig(defaultConfig);
        }
      } catch (error) {
        console.error('Failed to load wallpaper:', error);
        // Set default wallpaper on error
        const defaultConfig = {
          type: 'gradient',
          gradientId: 'dark',
        };
        setWallpaperConfig(defaultConfig);
      }
    };

    loadWallpaper();
  }, [chatId]);

  // REAL ONLINE STATUS TRACKING
  useEffect(() => {
    const unsubscribe = userProfileService.subscribeToUserStatus(partnerId, (profile) => {
      setIsOnline(profile.isOnline);
      setLastSeen(profile.lastSeen || null);
    });

    return unsubscribe;
  }, [partnerId]);

  // Load messages on mount
  useEffect(() => {
    let unsubscribe: (() => void) | undefined;
    
    const setupChat = async () => {
      try {
        unsubscribe = await loadMessages();
      } catch (error) {
        console.error('❌ Failed to setup chat:', error);
        setError('Failed to setup chat');
        setIsLoading(false);
      }
    };

    setupChat();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [loadMessages]);

  // Helper function to format date separators
  const formatDateSeparator = (date: Date): string => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDate = new Date(date);

    // Reset time to compare dates only
    today.setHours(0, 0, 0, 0);
    yesterday.setHours(0, 0, 0, 0);
    messageDate.setHours(0, 0, 0, 0);

    if (messageDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (messageDate.getTime() === yesterday.getTime()) {
      return 'Yesterday';
    } else {
      const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

      const dayOfWeek = dayNames[date.getDay()];
      const day = date.getDate();
      const month = monthNames[date.getMonth()];
      const year = date.getFullYear();

      // Show day name if within the last week
      const weekAgo = new Date(today);
      weekAgo.setDate(weekAgo.getDate() - 7);

      if (messageDate.getTime() > weekAgo.getTime()) {
        return dayOfWeek;
      } else {
        // Show full date for older messages
        const suffix = day === 1 || day === 21 || day === 31 ? 'st' :
                      day === 2 || day === 22 ? 'nd' :
                      day === 3 || day === 23 ? 'rd' : 'th';
        return `${day}${suffix} ${month}, ${year}`;
      }
    }
  };

  // Check if we need to show a date separator
  const shouldShowDateSeparator = (currentMessage: WhatsAppMessage, previousMessage?: WhatsAppMessage): boolean => {
    if (!previousMessage) return true; // Always show for first message

    const currentDate = new Date(currentMessage.timestamp);
    const previousDate = new Date(previousMessage.timestamp);

    // Reset time to compare dates only
    currentDate.setHours(0, 0, 0, 0);
    previousDate.setHours(0, 0, 0, 0);

    return currentDate.getTime() !== previousDate.getTime();
  };

  // WHATSAPP-STYLE MESSAGE RENDERING WITH TELEGRAM UI
  const renderMessage = ({ item, index }: { item: WhatsAppMessage; index: number }) => {
    const previousMessage = index > 0 ? messages[index - 1] : undefined;
    const showDateSeparator = shouldShowDateSeparator(item, previousMessage);
    const isOwnMessage = item.senderId === currentUserId;

    const renderMessageContent = () => {
      switch (item.type) {
        case 'image':
          return (
            <TouchableOpacity
              onPress={() => {
                // Get all media items from messages
                const mediaItems = messages
                  .filter(msg => msg.type === 'image' || msg.type === 'video')
                  .map(msg => ({
                    id: msg.id,
                    type: msg.type,
                    uri: msg.mediaUrl || '',
                    caption: msg.content || undefined,
                  }));

                // Find current item index
                const currentIndex = mediaItems.findIndex(media => media.id === item.id);

                setMediaGalleryItems(mediaItems);
                setMediaGalleryIndex(Math.max(0, currentIndex));
                setMediaGalleryVisible(true);
              }}
              style={styles.imageContainer}
            >
              <Image
                source={{ uri: item.mediaUrl }}
                style={styles.messageImage}
                resizeMode="cover"
              />
            </TouchableOpacity>
          );

        case 'video':
          return (
            <TouchableOpacity
              onPress={() => {
                // Get all media items from messages
                const mediaItems = messages
                  .filter(msg => msg.type === 'image' || msg.type === 'video')
                  .map(msg => ({
                    id: msg.id,
                    type: msg.type,
                    uri: msg.mediaUrl || '',
                    caption: msg.content || undefined,
                  }));

                // Find current item index
                const currentIndex = mediaItems.findIndex(media => media.id === item.id);

                setMediaGalleryItems(mediaItems);
                setMediaGalleryIndex(Math.max(0, currentIndex));
                setMediaGalleryVisible(true);
              }}
              style={styles.imageContainer}
            >
              <View style={styles.videoContainer}>
                <Image
                  source={{ uri: item.thumbnailUrl || item.mediaUrl }}
                  style={styles.messageImage}
                  resizeMode="cover"
                />
                <View style={styles.playButton}>
                  <Ionicons name="play" size={24} color="#FFFFFF" />
                </View>
              </View>
            </TouchableOpacity>
          );

        case 'audio':
        case 'voice':
          return (
            <AudioMessagePlayer
              audioUri={item.mediaUrl || ''}
              isOwnMessage={isOwnMessage}
              theme={theme}
              title={item.content || 'Audio message'}
              duration={item.duration}
            />
          );

        case 'document':
        case 'file':
          return (
            <FileViewer
              fileUri={item.mediaUrl || ''}
              fileName={item.fileName || item.content || 'Document'}
              fileSize={item.fileSize}
              mimeType={item.mimeType}
              isOwnMessage={isOwnMessage}
              theme={theme}
            />
          );

        default:
          return (
            <Text style={[
              styles.messageText,
              { color: isOwnMessage ? theme.ownMessageText : theme.otherMessageText }
            ]}>
              {item.content}
            </Text>
          );
      }
    };

    const getStatusIcon = () => {
      if (!isOwnMessage) return null;

      switch (item.status) {
        case 'sending':
          return <Ionicons name="time" size={16} color={theme.timeText} />;
        case 'sent':
          return <MaterialIcons name="done" size={16} color={isUsingDarkMode ? '#B0B0B0' : '#666666'} />;
        case 'delivered':
          return <MaterialIcons name="done-all" size={16} color={isUsingDarkMode ? '#B0B0B0' : '#666666'} />;
        case 'read':
          return <MaterialIcons name="done-all" size={16} color="#4FC3F7" />;
        case 'failed':
          return <Ionicons name="alert-circle" size={16} color="#F44336" />;
        default:
          return null;
      }
    };

    // Convert to SelectedMessage format
    const selectedMessage = {
      id: item.id,
      chatId: chatId,
      senderId: item.senderId,
      content: item.content,
      type: item.type as any,
      timestamp: item.timestamp,
      mediaUrl: item.mediaUrl,
      caption: (item as any).caption,
      fileName: item.fileName,
      fileSize: item.fileSize,
      isOwn: isOwnMessage,
    };

    return (
      <>
        {/* Date Separator */}
        {showDateSeparator && (
          <View style={styles.dateSeparatorContainer}>
            <View style={[styles.dateSeparatorLine, { backgroundColor: theme.divider }]} />
            <Text style={[styles.dateSeparatorText, { color: theme.timeText, backgroundColor: theme.background }]}>
              {formatDateSeparator(item.timestamp)}
            </Text>
            <View style={[styles.dateSeparatorLine, { backgroundColor: theme.divider }]} />
          </View>
        )}

        {/* Message */}
        <SelectableMessageBubble
          message={selectedMessage}
          isOwn={isOwnMessage}
          onLongPress={() => {
            handleMessageLongPress(item);
            // Show emoji bar for single selection
            showEmojiBar(item.id, { x: width / 2, y: 100 });
          }}
          onSwipeReply={() => setReplyingTo(item)}
          onSwipeDelete={async () => {
            console.log('🗑️ Deleting message:', item.id);
            try {
              const result = await realTimeMessagingService.deleteMessageForEveryone(item.id, currentUserId);

              if (result.success) {
                console.log('✅ Message deleted successfully');
                await loadMessages();
              } else {
                console.error('❌ Failed to delete message:', result.error);
                Alert.alert('Error', result.error || 'Failed to delete message');
              }
            } catch (error) {
              console.error('❌ Delete error:', error);
              Alert.alert('Error', 'Failed to delete message');
            }
          }}
          style={[
            styles.messageContainer,
            isOwnMessage ? styles.ownMessageContainer : styles.otherMessageContainer,
            // Attach to reply bubble if this is a reply
            item.replyTo ? {
              borderTopLeftRadius: 0,
              borderTopRightRadius: 0,
              marginTop: 0,
            } : {}
          ]}
        >
        {/* Reply indicator with threading - attached to message */}
        {item.replyTo && (
          <TouchableOpacity
            style={[styles.replyContainer, {
              backgroundColor: theme.divider,
              borderLeftWidth: 3,
              borderLeftColor: theme.sendButtonActive,
              marginBottom: 0, // NO SPACE between reply and message
              borderBottomLeftRadius: 0,
              borderBottomRightRadius: 0,
            }]}
            onPress={() => {
              // Scroll to the original message
              const originalMessageIndex = messages.findIndex(msg => msg.id === item.replyTo?.messageId);
              if (originalMessageIndex !== -1 && flatListRef.current) {
                flatListRef.current.scrollToIndex({
                  index: originalMessageIndex,
                  animated: true,
                  viewPosition: 0.5, // Center the message
                });
              }
            }}
          >
            <View style={styles.replyContent}>
              <Text style={[styles.replyName, { color: theme.sendButtonActive, fontWeight: 'bold' }]}>
                {item.replyTo.senderName}
              </Text>
              <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                {/* Media/File icon */}
                {item.replyTo.type !== 'text' && (
                  <Ionicons
                    name={
                      item.replyTo.type === 'image' ? 'image' :
                      item.replyTo.type === 'video' ? 'videocam' :
                      item.replyTo.type === 'audio' ? 'musical-notes' :
                      'document'
                    }
                    size={14}
                    color={theme.timeText}
                    style={{ marginRight: 4 }}
                  />
                )}
                <Text style={[styles.replyText, { color: theme.timeText, flex: 1 }]} numberOfLines={2}>
                  {item.replyTo.type === 'text'
                    ? item.replyTo.content
                    : item.replyTo.type === 'image'
                      ? '📷 Photo'
                      : item.replyTo.type === 'video'
                        ? '🎥 Video'
                        : item.replyTo.type === 'audio'
                          ? '🎵 Audio'
                          : '📄 Document'
                  }
                </Text>
              </View>
            </View>
            <View style={[styles.replyIndicator, { backgroundColor: theme.sendButtonActive }]} />
          </TouchableOpacity>
        )}

        {/* Forward indicator */}
        {item.isForwarded && (
          <View style={styles.forwardedContainer}>
            <Ionicons name="arrow-forward" size={14} color={theme.timeText} />
            <Text style={[styles.forwardedText, { color: theme.timeText }]}>
              Forwarded
            </Text>
          </View>
        )}

        <View style={[
          styles.messageBubble,
          isOwnMessage
            ? [styles.ownMessage, { backgroundColor: theme.ownMessageBackground }]
            : [styles.otherMessage, { backgroundColor: theme.messageBackground }]
        ]}>
          {renderMessageContent()}

          <View style={styles.messageFooter}>
            {item.isStarred && (
              <Ionicons name="star" size={12} color="#FFD700" style={styles.starIcon} />
            )}
            <Text style={[styles.messageTime, { color: theme.timeText }]}>
              {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </Text>
            {getStatusIcon()}
          </View>
        </View>
      </SelectableMessageBubble>
      </>
    );
  };

  // TELEGRAM-STYLE ERROR STATE
  if (error) {
    return (
      <>
        <StatusBar
          barStyle={isUsingDarkMode ? 'light-content' : 'dark-content'}
          backgroundColor={theme.headerBackground}
        />
        <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
          <View style={[styles.header, { backgroundColor: theme.headerBackground }]}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color={theme.headerText} />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, { color: theme.headerText }]}>{partnerName}</Text>
            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.headerActionButton}>
                <Ionicons name="ellipsis-vertical" size={22} color={theme.headerText} />
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: theme.otherMessageText }]}>{error}</Text>
            <TouchableOpacity onPress={loadMessages} style={[styles.retryButton, { backgroundColor: theme.sendButtonActive }]}>
              <Text style={styles.retryText}>Retry</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </>
    );
  }

  // TELEGRAM-STYLE LOADING STATE
  if (isLoading) {
    return (
      <>
        <StatusBar
          barStyle={isUsingDarkMode ? 'light-content' : 'dark-content'}
          backgroundColor={theme.headerBackground}
        />
        <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
          <View style={[styles.header, { backgroundColor: theme.headerBackground }]}>
            <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
              <Ionicons name="arrow-back" size={24} color={theme.headerText} />
            </TouchableOpacity>
            <Text style={[styles.headerTitle, { color: theme.headerText }]}>{partnerName}</Text>
            <View style={styles.headerActions}>
              <TouchableOpacity style={styles.headerActionButton}>
                <Ionicons name="ellipsis-vertical" size={22} color={theme.headerText} />
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.loadingContainer}>
            <Text style={[styles.loadingText, { color: theme.otherMessageText }]}>Loading messages...</Text>
          </View>
        </SafeAreaView>
      </>
    );
  }

  // TELEGRAM-STYLE MAIN INTERFACE - GUARANTEED TO RENDER!
  return (
    <MessageSelectionProvider chatId={chatId} currentUserId={currentUserId}>
      <StatusBar
        barStyle={isUsingDarkMode ? 'light-content' : 'dark-content'}
        backgroundColor={theme.headerBackground}
      />
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        {/* Telegram-style Header */}
        <View style={[styles.header, { backgroundColor: theme.headerBackground }]}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={theme.headerText} />
          </TouchableOpacity>

          {/* Partner Info - Clickable */}
          <TouchableOpacity
            style={styles.headerInfo}
            onPress={() => router.push(`/profile/${partnerId}`)}
          >
            <TouchableOpacity
              style={styles.avatar}
              onPress={() => router.push(`/profile/${partnerId}`)}
            >
              {partnerAvatar ? (
                <Image source={{ uri: partnerAvatar }} style={styles.avatarHeaderImage} />
              ) : (
                <Text style={[styles.avatarText, { color: theme.headerText }]}>
                  {partnerName.charAt(0).toUpperCase()}
                </Text>
              )}
            </TouchableOpacity>
            <View style={styles.headerTextContainer}>
              <Text style={[styles.headerTitle, { color: theme.headerText }]}>{partnerName}</Text>
              <Text style={[styles.headerSubtitle, { color: theme.headerText, opacity: 0.8 }]}>
                {isOnline ? 'Online' : lastSeen ? `Last seen ${formatLastSeen(lastSeen)}` : 'Last seen recently'}
              </Text>
            </View>
          </TouchableOpacity>

          {/* Header Actions */}
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => setShowThemeMenu(true)}
            >
              <Ionicons name="ellipsis-vertical" size={22} color={theme.headerText} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Messages with IraChat Wallpaper */}
        <View style={styles.messagesWrapper}>
          <View style={styles.wallpaper}>
            <IraChatWallpaper
              wallpaperConfig={wallpaperConfig}
              variant={isUsingDarkMode ? 'dark' : 'light'}
            />
          </View>
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            style={styles.messagesList}
            contentContainerStyle={styles.messagesContainer}
            showsVerticalScrollIndicator={false}
            onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: false })}
            inverted={false}
          />

          {/* Typing indicator */}
          {partnerTyping && (
            <View style={[styles.typingContainer, { backgroundColor: theme.messageBackground }]}>
              <Text style={[styles.typingText, { color: theme.otherMessageText }]}>
                {partnerName} is typing...
              </Text>
            </View>
          )}
        </View>



        {/* Reply Indicator */}
        {replyingTo && (
          <View style={[{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderTopWidth: 1,
            borderLeftWidth: 3,
            borderLeftColor: theme.sendButtonActive,
          }, { backgroundColor: theme.inputBackground, borderTopColor: theme.divider }]}>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, flex: 1 }}>
              <Ionicons name="arrow-undo" size={18} color={theme.sendButtonActive} />
              <View style={{ flex: 1 }}>
                <Text style={[{ fontSize: 12, fontWeight: '600' }, { color: theme.sendButtonActive }]}>
                  Replying to {replyingTo.senderId === currentUserId ? 'yourself' : (replyingTo.senderName || replyingTo.senderId)}
                </Text>
                <Text style={[{ fontSize: 14, marginTop: 2 }, { color: theme.otherMessageText }]} numberOfLines={1}>
                  {replyingTo.content}
                </Text>
              </View>
            </View>
            <TouchableOpacity
              onPress={() => setReplyingTo(null)}
              style={{ padding: 4 }}
            >
              <Ionicons name="close" size={16} color={theme.otherMessageText} />
            </TouchableOpacity>
          </View>
        )}

        {/* Editing Indicator */}
        {editingMessage && (
          <View style={[{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderTopWidth: 2,
          }, { backgroundColor: theme.inputBackground, borderTopColor: theme.headerText }]}>
            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
              <Ionicons name="create" size={16} color={theme.headerText} />
              <Text style={[{ fontSize: 14, fontWeight: '500' }, { color: theme.headerText }]}>
                Editing message
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => {
                setEditingMessage(null);
                setMessageText('');
              }}
              style={{ padding: 4 }}
            >
              <Ionicons name="close" size={16} color={theme.otherMessageText} />
            </TouchableOpacity>
          </View>
        )}

        {/* WhatsApp-style Input with Voice/Send Toggle */}
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={[styles.inputContainer, {
            backgroundColor: theme.background,
            borderTopColor: theme.divider
          }]}
        >
          <View style={styles.inputRow}>
            {/* Attach Button with Media Picker */}
            <TouchableOpacity
              style={styles.attachButton}
              onPress={() => setShowMediaUploadModal(true)}
              testID="attach-button"
            >
              <Ionicons name="attach" size={24} color={theme.placeholderText} />
            </TouchableOpacity>

            {/* Text Input Container */}
            <View style={[styles.textInputContainer, {
              backgroundColor: theme.inputBackground,
              borderColor: theme.inputBorder
            }]}>
              <TextInput
                style={[styles.textInput, {
                  color: theme.inputText,
                }]}
                value={messageText}
                onChangeText={handleTextChange}
                placeholder="Message"
                placeholderTextColor={theme.placeholderText}
                multiline
                maxLength={1000}
              />

            </View>

            {/* Voice/Send Button Toggle */}
            {messageText.trim() ? (
              <TouchableOpacity
                onPress={sendMessage}
                style={[styles.sendButton, { backgroundColor: theme.sendButtonActive }]}
                testID="send-button"
              >
                <Ionicons name="send" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                onPress={isRecording ? stopVoiceRecording : startVoiceRecording}
                style={[
                  styles.voiceButton,
                  { backgroundColor: isRecording ? '#FF5722' : theme.sendButtonActive }
                ]}
                testID="voice-button"
              >
                <Ionicons
                  name={isRecording ? "square" : "mic"}
                  size={20}
                  color="#FFFFFF"
                />
              </TouchableOpacity>
            )}
          </View>

          {/* Recording indicator */}
          {isRecording && (
            <View style={[styles.recordingIndicator, { backgroundColor: theme.inputBackground }]}>
              <View style={styles.recordingAnimation}>
                <View style={styles.recordingDot} />
              </View>
              <Text style={[styles.recordingText, { color: theme.otherMessageText }]}>
                Recording... {Math.floor(recordingDuration / 60)}:{(recordingDuration % 60).toString().padStart(2, '0')}
              </Text>
              <TouchableOpacity onPress={cancelVoiceRecording} style={styles.cancelRecording}>
                <Ionicons name="close" size={20} color="#FF5722" />
              </TouchableOpacity>
              <TouchableOpacity onPress={stopVoiceRecording} style={styles.sendRecording}>
                <Ionicons name="send" size={20} color="#4CAF50" />
              </TouchableOpacity>
            </View>
          )}
        </KeyboardAvoidingView>

        {/* Enhanced Media Picker with Multiple Selection */}
        <MediaPicker
          isVisible={showMediaPicker}
          onClose={() => setShowMediaPicker(false)}
          onMediaSelect={handleMediaSelect}
        />

        {/* Theme Menu Dropdown */}
        {showThemeMenu && (
          <TouchableOpacity
            style={styles.dropdownBackdrop}
            activeOpacity={1}
            onPress={() => setShowThemeMenu(false)}
          >
            <View style={[styles.dropdownMenu, { backgroundColor: theme.background }]}>
              <Text style={[styles.dropdownTitle, { color: theme.headerText }]}>
                Chat Options
              </Text>



              <TouchableOpacity
                style={[styles.themeOption, manualTheme === false && styles.activeThemeOption]}
                onPress={() => {
                  setManualTheme(false);
                  setShowThemeMenu(false);
                }}
              >
                <Ionicons name="sunny" size={20} color={theme.otherMessageText} />
                <Text style={[styles.themeOptionText, { color: theme.otherMessageText }]}>
                  Light Mode
                </Text>
                {manualTheme === false && (
                  <Ionicons name="checkmark" size={20} color={theme.sendButtonActive} />
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.themeOption, manualTheme === true && styles.activeThemeOption]}
                onPress={() => {
                  setManualTheme(true);
                  setShowThemeMenu(false);
                }}
              >
                <Ionicons name="moon" size={20} color={theme.otherMessageText} />
                <Text style={[styles.themeOptionText, { color: theme.otherMessageText }]}>
                  Dark Mode
                </Text>
                {manualTheme === true && (
                  <Ionicons name="checkmark" size={20} color={theme.sendButtonActive} />
                )}
              </TouchableOpacity>

              {/* Separator */}
              <View style={[{ height: 1, marginVertical: 8, marginHorizontal: 16 }, { backgroundColor: theme.otherMessageText + '20' }]} />

              {/* Search Messages */}
              <TouchableOpacity
                style={styles.themeOption}
                onPress={() => {
                  setShowThemeMenu(false);
                  setShowMessageSearch(true);
                }}
              >
                <Ionicons name="search" size={20} color={theme.otherMessageText} />
                <Text style={[styles.themeOptionText, { color: theme.otherMessageText }]}>
                  Search Messages
                </Text>
              </TouchableOpacity>

              {/* Change Wallpaper */}
              <TouchableOpacity
                style={styles.themeOption}
                onPress={() => {
                  setShowThemeMenu(false);
                  setShowWallpaperPicker(true);
                }}
              >
                <Ionicons name="image" size={20} color={theme.otherMessageText} />
                <Text style={[styles.themeOptionText, { color: theme.otherMessageText }]}>
                  Change Wallpaper
                </Text>
              </TouchableOpacity>

              {/* Export Chat */}
              <TouchableOpacity
                style={styles.themeOption}
                onPress={() => {
                  setShowThemeMenu(false);
                  setShowChatExport(true);
                }}
              >
                <Ionicons name="download" size={20} color={theme.otherMessageText} />
                <Text style={[styles.themeOptionText, { color: theme.otherMessageText }]}>
                  Export Chat
                </Text>
              </TouchableOpacity>

              {/* Add to Contacts */}
              <TouchableOpacity
                style={styles.themeOption}
                onPress={async () => {
                  setShowThemeMenu(false);
                  if (partnerName && partnerId) {
                    await contactService.showAddToContactsDialog({
                      name: partnerName,
                      phoneNumber: partnerId, // Using partnerId as phone number
                    });
                  }
                }}
              >
                <Ionicons name="person-add" size={20} color={theme.otherMessageText} />
                <Text style={[styles.themeOptionText, { color: theme.otherMessageText }]}>
                  Add to Contacts
                </Text>
              </TouchableOpacity>

              {/* Clear Chat */}
              <TouchableOpacity
                style={styles.themeOption}
                onPress={async () => {
                  setShowThemeMenu(false);
                  const result = await chatClearService.clearChatMessages(
                    chatId,
                    currentUserId,
                    { clearMessages: true, clearMedia: true, clearAll: false }
                  );
                  if (result.success) {
                    setMessages([]);
                  }
                }}
              >
                <Ionicons name="trash-outline" size={20} color="#FF6B6B" />
                <Text style={[styles.themeOptionText, { color: '#FF6B6B' }]}>
                  Clear Chat
                </Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        )}

        {/* Enhanced Media Gallery with Scroll */}
        <MediaGallery
          visible={mediaGalleryVisible}
          mediaItems={mediaGalleryItems}
          initialIndex={mediaGalleryIndex}
          onClose={() => setMediaGalleryVisible(false)}
          onCaptionUpdate={(id, caption) => {
            // Update message caption in the chat
            console.log('Caption updated for media:', id, caption);
          }}
        />

        {/* Legacy Image Viewer (fallback) */}
        <ImageViewer
          visible={imageViewerVisible}
          imageUri={selectedImageUri}
          onClose={() => setImageViewerVisible(false)}
        />

        {/* Legacy Video Player (fallback) */}
        <VideoPlayer
          visible={videoPlayerVisible}
          videoUri={selectedVideoUri}
          onClose={() => setVideoPlayerVisible(false)}
        />

        {/* Wallpaper Picker */}
        <WallpaperPicker
          visible={showWallpaperPicker}
          onClose={() => setShowWallpaperPicker(false)}
          chatId={chatId}
          isGroupChat={false}
          currentWallpaper={currentWallpaper}
          onWallpaperChange={(config) => {
            setCurrentWallpaper(config);
            setWallpaperConfig(config); // Update the wallpaper config to apply it
            console.log('🎨 Wallpaper changed:', config);
          }}
        />

        {/* Chat Export */}
        <ChatExport
          visible={showChatExport}
          onClose={() => setShowChatExport(false)}
          chatId={chatId}
          chatName={partnerName || 'Chat'}
          isGroupChat={false}
        />

        {/* Message Search */}
        <MessageSearch
          visible={showMessageSearch}
          onClose={() => setShowMessageSearch(false)}
          chatId={chatId}
          onMessageSelect={(messageId: string) => {
            // Scroll to message or highlight it
            console.log('Selected message:', messageId);
          }}
        />

        {/* Message Action Toolbar */}
        <MessageActionToolbar
          currentUserId={currentUserId}
          currentUserName={partnerName} // You might want to get the actual current user name
          onReply={(message) => {
            // Set reply state
            setReplyingTo(message);
            console.log('Reply to message:', message.id);
          }}
          onEdit={(message) => {
            // Set editing state and populate input
            setEditingMessage(message);
            setMessageText(message.content);
            console.log('Editing message:', message.id);
          }}
        />

        {/* Floating Emoji Bar */}
        <FloatingEmojiBar
          messageId={emojiBarState.messageId || ''}
          onEmojiSelect={async (emoji, messageId) => {
            try {
              console.log('😊 Adding emoji reaction:', emoji, 'to message:', messageId);

              // Add reaction using realTimeMessagingService
              const result = await realTimeMessagingService.addReaction(
                chatId,
                messageId,
                currentUserId,
                emoji
              );

              if (result.success) {
                console.log('✅ Emoji reaction added successfully');
                // Reload messages to show the reaction
                await loadMessages();
              } else {
                console.error('❌ Failed to add emoji reaction:', result.error);
                Alert.alert('Error', result.error || 'Failed to add reaction');
              }
            } catch (error) {
              console.error('❌ Error adding emoji reaction:', error);
              Alert.alert('Error', 'Failed to add reaction');
            } finally {
              hideEmojiBar();
            }
          }}
          position={emojiBarState.position}
          visible={emojiBarState.visible}
        />

        {/* Media Upload Modal */}
        <MediaUploadModal
          visible={showMediaUploadModal}
          onClose={() => setShowMediaUploadModal(false)}
          onSend={async (mediaData: MediaUploadData) => {
            try {
              console.log('📤 Sending media:', mediaData);

              // Send media message using realTimeMessagingService
              const result = await realTimeMessagingService.sendMediaMessage(
                chatId,
                currentUserId,
                partnerName, // You might want to get actual current user name
                undefined, // senderAvatar
                mediaData.uri,
                mediaData.type as any,
                mediaData.caption,
                replyingTo?.id
              );

              if (result.success) {
                console.log('✅ Media message sent successfully:', result.messageId);
                setShowMediaUploadModal(false);

                // Clear reply state if it was a reply
                if (replyingTo) {
                  setReplyingTo(null);
                }

                // Reload messages to show the new one
                await loadMessages();
              } else {
                console.error('❌ Failed to send media message:', result.error);
                Alert.alert('Error', result.error || 'Failed to send media');
              }
            } catch (error) {
              console.error('❌ Error sending media:', error);
              Alert.alert('Error', 'Failed to send media');
            }
          }}
          chatId={chatId}
          senderId={currentUserId}
        />

        {/* Media Full View */}
        <MediaFullView
          visible={mediaFullViewVisible}
          onClose={() => {
            setMediaFullViewVisible(false);
            setSelectedMediaForFullView(null);
          }}
          mediaType={selectedMediaForFullView?.type || 'image'}
          mediaUri={selectedMediaForFullView?.uri || ''}
          caption={selectedMediaForFullView?.caption}
          onShare={async () => {
            try {
              if (selectedMediaForFullView?.uri) {
                const isAvailable = await Sharing.isAvailableAsync();
                if (isAvailable) {
                  await Sharing.shareAsync(selectedMediaForFullView.uri);
                } else {
                  Alert.alert('Error', 'Sharing is not available on this device');
                }
              }
            } catch (error) {
              console.error('Error sharing media:', error);
              Alert.alert('Error', 'Failed to share media');
            }
          }}
          onSave={async () => {
            try {
              if (selectedMediaForFullView?.uri) {
                // Use expo-media-library to save to device gallery
                const { status } = await MediaLibrary.requestPermissionsAsync();
                if (status === 'granted') {
                  await MediaLibrary.saveToLibraryAsync(selectedMediaForFullView.uri);
                  Alert.alert('Success', 'Media saved to gallery');
                } else {
                  Alert.alert('Permission Required', 'Please grant permission to save media to gallery');
                }
              }
            } catch (error) {
              console.error('Error saving media:', error);
              Alert.alert('Error', 'Failed to save media');
            }
          }}
          onDelete={async () => {
            try {
              Alert.alert(
                'Delete Media',
                'Are you sure you want to delete this media?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: async () => {
                      // Close the full view first
                      setMediaFullViewVisible(false);
                      setSelectedMediaForFullView(null);

                      // You'd implement actual deletion logic here
                      Alert.alert('Info', 'Media deletion functionality coming soon');
                    }
                  },
                ]
              );
            } catch (error) {
              console.error('Error deleting media:', error);
              Alert.alert('Error', 'Failed to delete media');
            }
          }}
        />

      </SafeAreaView>
    </MessageSelectionProvider>
  );
};

// Telegram-style styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    marginRight: 12,
    padding: 4,
  },
  headerInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    fontSize: 18,
    fontWeight: '600',
  },
  avatarHeaderImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    lineHeight: 22,
  },
  headerSubtitle: {
    fontSize: 14,
    lineHeight: 16,
    marginTop: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerActionButton: {
    padding: 8,
    marginLeft: 4,
  },
  messagesList: {
    flex: 1,
    paddingHorizontal: 16,
  },
  messagesContainer: {
    paddingVertical: 16,
  },
  messageContainer: {
    marginVertical: 0.5,
  },
  ownMessageContainer: {
    alignItems: 'flex-end',
  },
  otherMessageContainer: {
    alignItems: 'flex-start',
  },
  messageBubble: {
    maxWidth: width * 0.75,
    paddingHorizontal: 8,
    paddingVertical: 6,
    borderRadius: 18,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  ownMessage: {
    borderBottomRightRadius: 4,
  },
  otherMessage: {
    borderBottomLeftRadius: 4,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 4,
  },
  messageTime: {
    fontSize: 13,
    lineHeight: 16,
    fontWeight: '500',
    opacity: 0.8,
  },
  readIcon: {
    marginLeft: 4,
  },
  inputContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  attachButton: {
    padding: 8,
    marginRight: 8,
  },
  textInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    borderRadius: 20,
    borderWidth: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    minHeight: 40,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    lineHeight: 20,
    maxHeight: 100,
    paddingVertical: 0,
  },
  emojiButton: {
    padding: 4,
    marginLeft: 8,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  retryButton: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  retryText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    lineHeight: 20,
  },

  // WhatsApp-style message styles
  messagesWrapper: {
    flex: 1,
    position: 'relative',
  },
  wallpaper: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  imageContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  messageImage: {
    width: 280,
    height: 210,
    borderRadius: 12,
  },
  videoContainer: {
    position: 'relative',
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -12 }, { translateY: -12 }],
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 8,
  },
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  audioPlayButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  audioWaveform: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    height: 20,
    marginRight: 8,
  },
  waveformBar: {
    width: 3,
    height: 12,
    borderRadius: 1.5,
    marginRight: 2,
  },
  audioDuration: {
    fontSize: 12,
    minWidth: 35,
  },
  documentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  documentSize: {
    fontSize: 12,
  },
  replyContainer: {
    padding: 10,
    borderRadius: 8,
    marginBottom: 4,
    borderLeftWidth: 3,
    borderLeftColor: '#4A90E2',
    flexDirection: 'row',
    alignItems: 'center',
  },
  replyContent: {
    flex: 1,
  },
  replyIndicator: {
    width: 4,
    height: 20,
    borderRadius: 2,
    marginLeft: 8,
  },
  replyName: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  replyText: {
    fontSize: 12,
  },
  forwardedContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  forwardedText: {
    fontSize: 12,
    fontStyle: 'italic',
    marginLeft: 4,
  },
  starIcon: {
    marginRight: 4,
  },
  typingContainer: {
    padding: 12,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 18,
    alignSelf: 'flex-start',
  },
  typingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },

  // Reply indicator styles (removed duplicates)
  replyToName: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 2,
  },
  replyToText: {
    fontSize: 12,
  },
  cancelReply: {
    padding: 4,
  },

  // Voice button and recording styles
  voiceButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  recordingAnimation: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#FF5722',
    marginRight: 8,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFFFFF',
    margin: 2,
  },
  recordingText: {
    flex: 1,
    fontSize: 14,
  },
  cancelRecording: {
    padding: 4,
  },
  sendRecording: {
    padding: 4,
    marginLeft: 8,
  },

  // Media picker modal styles
  mediaPickerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  mediaPickerContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area
  },
  mediaPickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  mediaPickerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  mediaOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 20,
  },
  mediaOption: {
    width: (width - 60) / 3,
    aspectRatio: 1,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    margin: 5,
  },
  mediaOptionText: {
    fontSize: 12,
    marginTop: 8,
    textAlign: 'center',
  },

  // Emoji picker styles
  emojiPickerOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  emojiPickerContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34,
    maxHeight: 400,
  },
  emojiPickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  emojiPickerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  emojiGrid: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  emojiRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 8,
  },
  emojiItem: {
    padding: 8,
    borderRadius: 8,
  },
  emojiText: {
    fontSize: 28,
  },

  // Dropdown menu styles
  dropdownBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 1000,
  },
  dropdownMenu: {
    position: 'absolute',
    top: 100, // Position below header
    right: 12,
    borderRadius: 12,
    paddingVertical: 8,
    minWidth: 200,
    maxWidth: 250,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  dropdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: 4,
  },
  dropdownOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  activeDropdownOption: {
    backgroundColor: 'rgba(74, 144, 226, 0.1)',
  },
  dropdownOptionText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  themeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  activeThemeOption: {
    backgroundColor: 'rgba(74, 144, 226, 0.1)',
  },
  themeOptionText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },

  // Date separator styles
  dateSeparatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
    paddingHorizontal: 20,
  },
  dateSeparatorLine: {
    flex: 1,
    height: 1,
  },
  dateSeparatorText: {
    fontSize: 12,
    fontWeight: '500',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    textAlign: 'center',
    minWidth: 80,
  },
});
