# IraChat Offline Functionality

IraChat now includes comprehensive offline functionality similar to WhatsApp, allowing users to send messages, create chats, and access their data even without an internet connection. All data is stored locally using SQLite and synced when the connection is restored.

## 🚀 Features

### Core Offline Capabilities
- ✅ **Send messages offline** - Messages are queued and sent when online
- ✅ **Create and manage chats** - Full chat functionality works offline
- ✅ **Local SQLite storage** - All data stored on device like WhatsApp
- ✅ **In-memory caching** - Instant access to frequently used data
- ✅ **Automatic sync** - Seamless sync when connection is restored
- ✅ **Network state management** - Real-time connectivity monitoring
- ✅ **Delivery status tracking** - Know when messages are sent/delivered/read
- ✅ **Search functionality** - Search messages and chats offline
- ✅ **Chat management** - Pin, archive, mute chats offline

### Advanced Features
- 🔄 **Background sync** - Automatic syncing in the background
- 📱 **Memory management** - Intelligent cache eviction and cleanup
- 🔍 **Full-text search** - Search across all messages and chats
- 📊 **Statistics and monitoring** - Track storage usage and sync status
- ⚡ **Performance optimized** - Fast loading with proper indexing
- 🛡️ **Conflict resolution** - Handle data conflicts intelligently

## 📁 Architecture

### Core Services

1. **Memory Cache Service** (`src/services/memoryCache.ts`)
   - LRU cache with automatic eviction
   - Separate caches for messages, chats, contacts, users, media
   - Memory pressure handling

2. **Offline Database Service** (`src/services/offlineDatabase.ts`)
   - SQLite database with comprehensive schema
   - Optimized indexes for fast queries
   - Transaction support and data integrity

3. **Network State Manager** (`src/services/networkStateManager.ts`)
   - Real-time connectivity monitoring
   - Connection quality detection
   - Retry mechanisms with exponential backoff

4. **Offline Message Service** (`src/services/offlineMessageService.ts`)
   - Complete message management offline
   - Message queuing and sync
   - Delivery status tracking

5. **Offline Chat Service** (`src/services/offlineChatService.ts`)
   - Chat creation and management
   - Participant management
   - Chat settings and metadata

6. **IraChat Offline Engine** (`src/services/iraChatOfflineEngine.ts`)
   - Main integration service
   - Unified API for all offline functionality
   - Sync coordination and conflict resolution

### React Integration

- **useIraChatOffline Hook** (`src/hooks/useIraChatOffline.ts`)
  - Easy React integration
  - Real-time state updates
  - Error handling and loading states

## 🛠️ Usage

### Basic Setup

```typescript
import { useIraChatOffline } from '../hooks/useIraChatOffline';

const MyComponent = () => {
  const {
    isOnline,
    isOffline,
    sendMessage,
    getMessages,
    getChats,
    createChat,
  } = useIraChatOffline({
    autoInitialize: true,
    enableBackgroundSync: true,
  });

  // Your component logic here
};
```

### Sending Messages

```typescript
// Works both online and offline
const handleSendMessage = async () => {
  try {
    const messageId = await sendMessage(
      chatId,
      'Hello, this works offline!',
      currentUserId,
      'text'
    );
    console.log('Message sent:', messageId);
  } catch (error) {
    console.error('Failed to send message:', error);
  }
};
```

### Creating Chats

```typescript
// Create a new chat (works offline)
const handleCreateChat = async () => {
  try {
    const chatId = await createChat(
      'My New Chat',
      currentUserId, // creator
      false, // isGroup
      ['user1', 'user2'] // participants
    );
    console.log('Chat created:', chatId);
  } catch (error) {
    console.error('Failed to create chat:', error);
  }
};
```

### Getting Data

```typescript
// Get chats (from cache or database)
const loadChats = async () => {
  try {
    const chats = await getChats();
    setChats(chats);
  } catch (error) {
    console.error('Failed to load chats:', error);
  }
};

// Get messages for a chat
const loadMessages = async (chatId: string) => {
  try {
    const messages = await getMessages(chatId, 50, 0);
    setMessages(messages);
  } catch (error) {
    console.error('Failed to load messages:', error);
  }
};
```

### Search Functionality

```typescript
// Search messages across all chats
const searchMessages = async (query: string) => {
  try {
    const results = await searchMessages(query);
    setSearchResults(results);
  } catch (error) {
    console.error('Search failed:', error);
  }
};

// Search chats
const searchChats = async (query: string) => {
  try {
    const results = await searchChats(query);
    setChatResults(results);
  } catch (error) {
    console.error('Chat search failed:', error);
  }
};
```

### Sync Management

```typescript
// Force sync when online
const handleSync = async () => {
  try {
    await forceSync();
    console.log('Sync completed');
  } catch (error) {
    console.error('Sync failed:', error);
  }
};

// Monitor sync progress
const {
  isSyncing,
  syncProgress,
} = useIraChatOffline();

if (isSyncing && syncProgress) {
  console.log(`Syncing ${syncProgress.phase}: ${syncProgress.progress}%`);
}
```

## 📊 Database Schema

### Core Tables

1. **messages** - All message data with sync status
2. **chats** - Chat information and metadata
3. **chat_participants** - Group chat membership
4. **users** - User profiles and online status
5. **contacts** - Contact information
6. **media_metadata** - Media file information
7. **app_settings** - Application settings
8. **sync_queue** - Pending sync operations

### Key Features

- **Foreign key constraints** for data integrity
- **Optimized indexes** for fast queries
- **Sync status tracking** for all records
- **Soft deletes** to maintain data consistency
- **Timestamps** for conflict resolution

## 🔄 Sync Strategy

### Automatic Sync
- Triggers when network connection is restored
- Background sync every 30 seconds when online
- App foreground sync when returning from background

### Manual Sync
- Force sync button for immediate synchronization
- Progress tracking with detailed status updates
- Error handling and retry mechanisms

### Conflict Resolution
- Last-write-wins for simple conflicts
- Merge strategies for complex data
- Manual resolution for critical conflicts

## 📱 Memory Management

### Cache Strategy
- **LRU eviction** - Least recently used items removed first
- **Size limits** - Maximum cache size per data type
- **TTL expiration** - Time-based cache invalidation
- **Memory pressure handling** - Automatic cleanup when needed

### Storage Optimization
- **Database vacuuming** - Regular cleanup of deleted data
- **Index optimization** - Analyze and optimize query performance
- **Media cleanup** - Remove old cached media files

## 🔧 Configuration

```typescript
const config = {
  enableOfflineMode: true,
  maxCacheSize: 200, // MB
  syncInterval: 30000, // 30 seconds
  retryAttempts: 3,
  enableBackgroundSync: true,
  autoDownloadMedia: true,
  maxMediaCacheSize: 500, // MB
  debugMode: false,
};

await iraChatOfflineEngine.initialize(config);
```

## 📈 Monitoring and Stats

```typescript
const stats = await iraChatOfflineEngine.getStats();

console.log('Database size:', stats.totalStorageUsed);
console.log('Pending sync items:', stats.pendingSyncItems);
console.log('Cache hit rate:', stats.cacheStats);
console.log('Network status:', stats.networkStats);
```

## 🧪 Demo Component

See `src/components/OfflineDemo.tsx` for a complete working example that demonstrates:
- Sending messages offline
- Creating chats
- Searching messages
- Monitoring sync status
- Handling network state changes

## 🚀 Getting Started

1. **Install dependencies** (already included in the project)
2. **Import the hook** in your components
3. **Initialize with options** that suit your needs
4. **Start using offline functionality** immediately

The system works seamlessly - users won't notice whether they're online or offline, messages will be delivered when connection is restored, and all data is safely stored locally.

## 🔒 Data Security

- All data is stored locally on the device
- No sensitive data is cached in memory longer than necessary
- Automatic cleanup of old data
- Secure sync protocols when online

This implementation provides WhatsApp-level offline functionality while maintaining excellent performance and user experience.
