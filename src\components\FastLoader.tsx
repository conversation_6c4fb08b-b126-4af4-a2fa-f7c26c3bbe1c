import React, { useEffect, useRef } from "react";
import { View, Text, StyleSheet, Animated, Easing } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface FastLoaderProps {
  text?: string;
  progress?: number; // 0-100
  showProgress?: boolean;
  color?: string;
  size?: "small" | "medium" | "large";
  animated?: boolean;
}

export const FastLoader = ({
  text = "IraChat",
  progress,
  showProgress = true,
  color = "#3B82F6",
  size = "medium",
  animated = true,
}: FastLoaderProps) => {
  const spinValue = useRef(new Animated.Value(0)).current;
  const progressValue = useRef(new Animated.Value(0)).current;
  const pulseValue = useRef(new Animated.Value(1)).current;

  // Icon sizes based on size prop
  const iconSizes = { small: 24, medium: 32, large: 40 };
  const textSizes = { small: 18, medium: 24, large: 28 };
  const progressWidths = { small: 80, medium: 120, large: 160 };

  useEffect(() => {
    if (!animated) return;

    // Spinning animation for the icon
    const spinAnimation = Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 2000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );

    // Pulsing animation for the text
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseValue, {
          toValue: 0.8,
          duration: 800,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(pulseValue, {
          toValue: 1,
          duration: 800,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );

    spinAnimation.start();
    pulseAnimation.start();

    return () => {
      spinAnimation.stop();
      pulseAnimation.stop();
    };
  }, [animated, spinValue, pulseValue]);

  useEffect(() => {
    if (progress !== undefined) {
      Animated.timing(progressValue, {
        toValue: progress / 100,
        duration: 300,
        easing: Easing.out(Easing.ease),
        useNativeDriver: false,
      }).start();
    }
  }, [progress, progressValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Animated.View
          style={[
            styles.iconContainer,
            animated && {
              transform: [{ rotate: spin }],
            },
          ]}
        >
          <Ionicons
            name="flash"
            size={iconSizes[size]}
            color={color}
          />
        </Animated.View>

        <Animated.Text
          style={[
            styles.text,
            {
              fontSize: textSizes[size],
              color: color === "#3B82F6" ? "#1f2937" : color,
            },
            animated && {
              transform: [{ scale: pulseValue }],
            },
          ]}
        >
          {text}
        </Animated.Text>

        {showProgress && (
          <View style={styles.progressContainer}>
            <View
              style={[
                styles.progressBar,
                { width: progressWidths[size] }
              ]}
            >
              <Animated.View
                style={[
                  styles.progress,
                  {
                    backgroundColor: color,
                    width: progress !== undefined
                      ? progressValue.interpolate({
                          inputRange: [0, 1],
                          outputRange: ["0%", "100%"],
                        })
                      : "100%",
                  },
                ]}
              />
            </View>
            {progress !== undefined && (
              <Text style={styles.progressText}>
                {Math.round(progress)}%
              </Text>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#ffffff",
  },
  content: {
    alignItems: "center",
  },
  iconContainer: {
    marginBottom: 8,
  },
  text: {
    fontWeight: "bold",
    marginTop: 12,
    marginBottom: 20,
    textAlign: "center",
  },
  progressContainer: {
    alignItems: "center",
    minHeight: 20,
  },
  progressBar: {
    height: 4,
    backgroundColor: "#e5e7eb",
    borderRadius: 2,
    overflow: "hidden",
    marginBottom: 8,
  },
  progress: {
    height: "100%",
    borderRadius: 2,
    minWidth: 2,
  },
  progressText: {
    fontSize: 12,
    color: "#6b7280",
    fontWeight: "500",
  },
});
