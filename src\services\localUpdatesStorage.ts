/**
 * Local Updates Storage Service
 * Handles offline-first storage of updates using SQLite
 * Similar to localMessageStorage but for updates/stories
 */

import * as SQLite from 'expo-sqlite';
import { Update } from '../types/Update';

export interface LocalUpdate extends Omit<Update, 'timestamp' | 'expiresAt' | 'createdAt' | 'updatedAt'> {
  localId: string;
  timestamp: number; // Store as timestamp for SQLite
  expiresAt?: number; // Store as timestamp for SQLite
  syncStatus: 'pending' | 'synced' | 'failed';
  createdAt: number; // Override to use number instead of Date
  updatedAt: number; // Override to use number instead of Date
  isDeleted: boolean;
  deletedAt?: number;
}

class LocalUpdatesStorageService {
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;
  private isInitializing = false;
  private readonly DB_NAME = 'irachat_updates.db';
  private initializationPromise: Promise<void> | null = null;


  async initialize(): Promise<void> {
    // Return existing promise if initialization is in progress
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    // Return immediately if already initialized
    if (this.isInitialized && this.db) {
      console.log('✅ Local updates storage already initialized');
      return;
    }

    // Create initialization promise to prevent concurrent initializations
    this.initializationPromise = this.performInitialization();

    try {
      await this.initializationPromise;
    } finally {
      this.initializationPromise = null;
    }
  }

  private async performInitialization(): Promise<void> {
    if (this.isInitializing) {
      throw new Error('Initialization already in progress');
    }

    this.isInitializing = true;
    this.isInitialized = false;

    try {
      console.log('🗄️ Initializing local updates storage...');

      // Close existing database if any
      if (this.db) {
        try {
          console.log('🔄 Closing existing database connection...');
          await this.db.closeAsync();
        } catch (closeError) {
          console.warn('⚠️ Warning closing existing database:', closeError);
        }
        this.db = null;
      }

      // Add delay to ensure proper cleanup
      await new Promise(resolve => setTimeout(resolve, 200));

      // Try to open database with retry logic
      console.log('📂 Opening database connection...');
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          this.db = await SQLite.openDatabaseAsync(this.DB_NAME);
          if (this.db) {
            console.log('✅ Database connection established');
            break;
          }
        } catch (openError) {
          retryCount++;
          console.warn(`⚠️ Database open attempt ${retryCount} failed:`, openError);

          if (retryCount >= maxRetries) {
            throw new Error(`Failed to open database after ${maxRetries} attempts: ${openError}`);
          }

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 500 * retryCount));
        }
      }

      if (!this.db) {
        throw new Error('Failed to open database - database instance is null');
      }

      // Configure database with safe PRAGMA commands
      console.log('⚙️ Configuring database settings safely...');

      // Only use essential PRAGMA commands that work on Android
      try {
        await this.db.execAsync('PRAGMA foreign_keys = ON;');
        console.log('✅ Foreign keys enabled');
      } catch (pragmaError) {
        console.warn('⚠️ Could not enable foreign keys (non-critical):', pragmaError);
      }

      console.log('🏗️ Creating database tables...');
      await this.createTablesWithRetry();

      this.isInitialized = true;
      console.log('✅ Local updates storage initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize local updates storage:', error);
      this.isInitialized = false;

      // Clean up on failure
      if (this.db) {
        try {
          await this.db.closeAsync();
        } catch (cleanupError) {
          console.warn('⚠️ Failed to cleanup database on error:', cleanupError);
        }
        this.db = null;
      }

      throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      this.isInitializing = false;
    }
  }

  private async ensureDatabaseReady(): Promise<void> {
    if (!this.db || !this.isInitialized) {
      console.log('🔄 Database not ready, initializing...');
      await this.initialize();
    }

    if (!this.db) {
      throw new Error('Database initialization failed - database is null');
    }
  }

  private async createTablesWithRetry(): Promise<void> {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        await this.createTables();
        return; // Success
      } catch (error) {
        retryCount++;
        console.warn(`⚠️ Table creation attempt ${retryCount} failed:`, error);

        if (retryCount >= maxRetries) {
          throw error;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
      }
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized - cannot create tables');
    }

    try {

    // Updates table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS updates (
        localId TEXT PRIMARY KEY,
        id TEXT,
        userId TEXT NOT NULL,
        userName TEXT NOT NULL,
        userAvatar TEXT,
        type TEXT NOT NULL,
        caption TEXT,
        timestamp INTEGER NOT NULL,
        isStory INTEGER DEFAULT 0,
        expiresAt INTEGER,
        privacy TEXT DEFAULT 'public',
        isVisible INTEGER DEFAULT 1,
        isArchived INTEGER DEFAULT 0,
        location TEXT,
        hashtags TEXT, -- JSON string
        mentions TEXT, -- JSON string
        groupTags TEXT, -- JSON string
        viewCount INTEGER DEFAULT 0,
        likeCount INTEGER DEFAULT 0,
        commentCount INTEGER DEFAULT 0,
        shareCount INTEGER DEFAULT 0,
        downloadCount INTEGER DEFAULT 0,
        isLikedByCurrentUser INTEGER DEFAULT 0,
        isViewedByCurrentUser INTEGER DEFAULT 0,
        isSharedByCurrentUser INTEGER DEFAULT 0,
        isDownloadedByCurrentUser INTEGER DEFAULT 0,
        isReported INTEGER DEFAULT 0,
        reportCount INTEGER DEFAULT 0,
        isFlagged INTEGER DEFAULT 0,
        isPinned INTEGER DEFAULT 0,
        isHighlight INTEGER DEFAULT 0,
        syncStatus TEXT DEFAULT 'pending',
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        isDeleted INTEGER DEFAULT 0,
        deletedAt INTEGER
      )
    `);

    // Media table for update media
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS update_media (
        id TEXT PRIMARY KEY,
        updateLocalId TEXT NOT NULL,
        url TEXT NOT NULL,
        type TEXT NOT NULL,
        thumbnailUrl TEXT,
        width INTEGER,
        height INTEGER,
        duration INTEGER,
        size INTEGER,
        FOREIGN KEY (updateLocalId) REFERENCES updates (localId) ON DELETE CASCADE
      )
    `);

    // Interactions table (likes, views, shares, etc.)
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS update_interactions (
        id TEXT PRIMARY KEY,
        updateLocalId TEXT NOT NULL,
        userId TEXT NOT NULL,
        type TEXT NOT NULL, -- 'like', 'view', 'share', 'download'
        timestamp INTEGER NOT NULL,
        FOREIGN KEY (updateLocalId) REFERENCES updates (localId) ON DELETE CASCADE
      )
    `);

    // Comments table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS update_comments (
        id TEXT PRIMARY KEY,
        updateLocalId TEXT NOT NULL,
        userId TEXT NOT NULL,
        userName TEXT NOT NULL,
        userAvatar TEXT,
        text TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        likesCount INTEGER DEFAULT 0,
        repliesCount INTEGER DEFAULT 0,
        isLiked INTEGER DEFAULT 0,
        isVisible INTEGER DEFAULT 1,
        syncStatus TEXT DEFAULT 'pending',
        FOREIGN KEY (updateLocalId) REFERENCES updates (localId) ON DELETE CASCADE
      )
    `);

    // Create indexes for better performance
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_updates_timestamp ON updates (timestamp DESC);
      CREATE INDEX IF NOT EXISTS idx_updates_user ON updates (userId);
      CREATE INDEX IF NOT EXISTS idx_updates_sync ON updates (syncStatus);
      CREATE INDEX IF NOT EXISTS idx_updates_story ON updates (isStory, expiresAt);
      CREATE INDEX IF NOT EXISTS idx_interactions_update ON update_interactions (updateLocalId);
      CREATE INDEX IF NOT EXISTS idx_comments_update ON update_comments (updateLocalId);
    `);

      console.log('✅ Updates database tables created');
    } catch (error) {
      console.error('❌ Failed to create database tables:', error);
      throw error;
    }
  }

  async saveUpdate(update: Omit<Update, 'id'> & { id?: string }): Promise<string> {
    await this.ensureDatabaseReady();

    const localId = `local_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const now = Date.now();

    try {
      // Convert Update to LocalUpdate format
      const localUpdate: LocalUpdate = {
        localId,
        id: update.id || localId,
        userId: update.userId,
        userName: update.userName,
        userAvatar: update.userAvatar,
        type: update.type,
        caption: update.caption,
        timestamp: typeof update.timestamp === 'object' ? update.timestamp.getTime() : now,
        isStory: update.isStory || false,
        expiresAt: update.expiresAt ? (typeof update.expiresAt === 'object' ? update.expiresAt.getTime() : update.expiresAt) : undefined,
        privacy: update.privacy || 'public',
        isVisible: update.isVisible !== false,
        isArchived: update.isArchived || false,
        location: update.location,
        hashtags: update.hashtags || [],
        mentions: update.mentions || [],
        groupTags: update.groupTags || [],
        media: update.media || [],
        likes: update.likes || [],
        views: update.views || [],
        shares: update.shares || [],
        downloads: update.downloads || [],
        reactions: update.reactions || [],
        comments: update.comments || [],
        viewCount: update.viewCount || 0,
        likeCount: update.likeCount || 0,
        commentCount: update.commentCount || 0,
        shareCount: update.shareCount || 0,
        downloadCount: update.downloadCount || 0,
        isLikedByCurrentUser: update.isLikedByCurrentUser || false,
        isViewedByCurrentUser: update.isViewedByCurrentUser || false,
        isSharedByCurrentUser: update.isSharedByCurrentUser || false,
        isDownloadedByCurrentUser: update.isDownloadedByCurrentUser || false,
        isReported: update.isReported || false,
        reportCount: update.reportCount || 0,
        isFlagged: update.isFlagged || false,
        isPinned: update.isPinned || false,
        isHighlight: update.isHighlight || false,
        musicTrack: update.musicTrack,
        syncStatus: update.id ? 'synced' : 'pending',
        createdAt: now,
        updatedAt: now,
        isDeleted: false,
      };

      // Insert update
      await this.db!.runAsync(`
        INSERT OR REPLACE INTO updates (
          localId, id, userId, userName, userAvatar, type, caption, timestamp,
          isStory, expiresAt, privacy, isVisible, isArchived, location,
          hashtags, mentions, groupTags, viewCount, likeCount, commentCount,
          shareCount, downloadCount, isLikedByCurrentUser, isViewedByCurrentUser,
          isSharedByCurrentUser, isDownloadedByCurrentUser, isReported, reportCount,
          isFlagged, isPinned, isHighlight, syncStatus, createdAt, updatedAt, isDeleted
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        localId, localUpdate.id || null, localUpdate.userId, localUpdate.userName,
        localUpdate.userAvatar || null, localUpdate.type, localUpdate.caption || null,
        localUpdate.timestamp, localUpdate.isStory ? 1 : 0, localUpdate.expiresAt || null,
        localUpdate.privacy, localUpdate.isVisible ? 1 : 0, localUpdate.isArchived ? 1 : 0,
        localUpdate.location ? JSON.stringify(localUpdate.location) : null, JSON.stringify(localUpdate.hashtags),
        JSON.stringify(localUpdate.mentions), JSON.stringify(localUpdate.groupTags),
        localUpdate.viewCount, localUpdate.likeCount, localUpdate.commentCount,
        localUpdate.shareCount, localUpdate.downloadCount, localUpdate.isLikedByCurrentUser ? 1 : 0,
        localUpdate.isViewedByCurrentUser ? 1 : 0, localUpdate.isSharedByCurrentUser ? 1 : 0,
        localUpdate.isDownloadedByCurrentUser ? 1 : 0, localUpdate.isReported ? 1 : 0,
        localUpdate.reportCount, localUpdate.isFlagged ? 1 : 0, localUpdate.isPinned ? 1 : 0,
        localUpdate.isHighlight ? 1 : 0, localUpdate.syncStatus, localUpdate.createdAt,
        localUpdate.updatedAt, localUpdate.isDeleted ? 1 : 0
      ]);

      // Save media
      if (localUpdate.media && localUpdate.media.length > 0) {
        for (const media of localUpdate.media) {
          await this.db!.runAsync(`
            INSERT OR REPLACE INTO update_media (
              id, updateLocalId, url, type, thumbnailUrl, width, height, duration, size
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            media.id, localId, media.url, media.type, media.thumbnailUrl || null,
            media.width || null, media.height || null, media.duration || null, media.size || null
          ]);
        }
      }

      console.log('✅ Update saved locally:', localId);
      return localId;
    } catch (error) {
      console.error('❌ Failed to save update locally:', error);
      throw error;
    }
  }

  async getUpdates(
    limit: number = 50,
    offset: number = 0,
    includeStories: boolean = false
  ): Promise<Update[]> {
    await this.ensureDatabaseReady();

    try {
      const now = Date.now();
      let whereClause = 'WHERE isDeleted = 0 AND isVisible = 1';

      if (!includeStories) {
        whereClause += ' AND isStory = 0';
      } else {
        // For stories, only include non-expired ones
        whereClause += ' AND (isStory = 0 OR (isStory = 1 AND (expiresAt IS NULL OR expiresAt > ?)))';
      }

      const params = includeStories ? [now, limit, offset] : [limit, offset];

      const result = await this.db!.getAllAsync(`
        SELECT * FROM updates
        ${whereClause}
        ORDER BY timestamp DESC
        LIMIT ? OFFSET ?
      `, params);

      const updates: Update[] = [];

      for (const row of result) {
        const update = await this.rowToUpdate(row as any);
        if (update) updates.push(update);
      }

      return updates;
    } catch (error) {
      console.error('❌ Failed to get updates from local storage:', error);
      return [];
    }
  }

  async getUpdateById(localId: string): Promise<Update | null> {
    if (!this.db) await this.initialize();

    try {
      const result = await this.db!.getFirstAsync(`
        SELECT * FROM updates WHERE localId = ? AND isDeleted = 0
      `, [localId]);

      if (!result) return null;
      return await this.rowToUpdate(result as any);
    } catch (error) {
      console.error('❌ Failed to get update by ID:', error);
      return null;
    }
  }

  async getStoriesForUser(userId: string): Promise<Update[]> {
    if (!this.db) await this.initialize();

    try {
      const now = Date.now();
      const result = await this.db!.getAllAsync(`
        SELECT * FROM updates
        WHERE userId = ? AND isStory = 1 AND isDeleted = 0 AND isVisible = 1
        AND (expiresAt IS NULL OR expiresAt > ?)
        ORDER BY timestamp DESC
      `, [userId, now]);

      const stories: Update[] = [];
      for (const row of result) {
        const story = await this.rowToUpdate(row as any);
        if (story) stories.push(story);
      }

      return stories;
    } catch (error) {
      console.error('❌ Failed to get stories for user:', error);
      return [];
    }
  }

  async getPendingSyncUpdates(): Promise<LocalUpdate[]> {
    if (!this.db) await this.initialize();

    try {
      const result = await this.db!.getAllAsync(`
        SELECT * FROM updates
        WHERE syncStatus = 'pending' AND isDeleted = 0
        ORDER BY createdAt ASC
      `);

      return result.map(row => this.rowToLocalUpdate(row as any));
    } catch (error) {
      console.error('❌ Failed to get pending sync updates:', error);
      return [];
    }
  }

  async getFailedSyncUpdates(): Promise<LocalUpdate[]> {
    if (!this.db) await this.initialize();

    try {
      const result = await this.db!.getAllAsync(`
        SELECT * FROM updates
        WHERE syncStatus = 'failed' AND isDeleted = 0
        ORDER BY createdAt ASC
      `);

      return result.map(row => this.rowToLocalUpdate(row as any));
    } catch (error) {
      console.error('❌ Failed to get failed sync updates:', error);
      return [];
    }
  }

  async updateSyncStatus(localId: string, status: 'pending' | 'synced' | 'failed', firebaseId?: string): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      const now = Date.now();
      await this.db!.runAsync(`
        UPDATE updates
        SET syncStatus = ?, id = COALESCE(?, id), updatedAt = ?
        WHERE localId = ?
      `, [status, firebaseId || null, now, localId]);

      console.log(`✅ Updated sync status for ${localId}: ${status}`);
    } catch (error) {
      console.error('❌ Failed to update sync status:', error);
      throw error;
    }
  }

  async deleteUpdate(localId: string): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      const now = Date.now();
      await this.db!.runAsync(`
        UPDATE updates
        SET isDeleted = 1, deletedAt = ?, updatedAt = ?
        WHERE localId = ?
      `, [now, now, localId]);

      console.log('✅ Update marked as deleted:', localId);
    } catch (error) {
      console.error('❌ Failed to delete update:', error);
      throw error;
    }
  }

  async clearExpiredStories(): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      const now = Date.now();
      const result = await this.db!.runAsync(`
        UPDATE updates
        SET isDeleted = 1, deletedAt = ?, updatedAt = ?
        WHERE isStory = 1 AND expiresAt IS NOT NULL AND expiresAt <= ?
      `, [now, now, now]);

      console.log(`✅ Expired stories cleared: ${result.changes} stories`);
    } catch (error) {
      console.error('❌ Failed to clear expired stories:', error);
    }
  }

  private async rowToUpdate(row: any): Promise<Update | null> {
    try {
      // Get media for this update
      const mediaResult = await this.db!.getAllAsync(`
        SELECT * FROM update_media WHERE updateLocalId = ?
      `, [row.localId]);

      const media = mediaResult.map((m: any) => ({
        id: m.id,
        url: m.url,
        type: m.type as 'image' | 'video',
        thumbnailUrl: m.thumbnailUrl,
        width: m.width,
        height: m.height,
        duration: m.duration,
        size: m.size,
      }));

      // Get comments for this update
      const commentsResult = await this.db!.getAllAsync(`
        SELECT * FROM update_comments WHERE updateLocalId = ? AND isVisible = 1
        ORDER BY timestamp ASC
      `, [row.localId]);

      const comments = commentsResult.map((c: any) => ({
        id: c.id,
        updateId: row.id || row.localId,
        userId: c.userId,
        userName: c.userName,
        user: {
          id: c.userId,
          name: c.userName,
          avatar: c.userAvatar,
        },
        text: c.text,
        timestamp: new Date(c.timestamp),
        likesCount: c.likesCount,
        repliesCount: c.repliesCount,
        replies: [], // Add missing required field
        mentions: [], // Add missing required field
        isLiked: c.isLiked === 1,
        isVisible: c.isVisible === 1,
        isEdited: false, // Add missing required field
        userAvatar: c.userAvatar,
        username: c.userName,
        likes: c.likesCount,
      }));

      const update: Update = {
        id: row.id || row.localId,
        userId: row.userId,
        userName: row.userName,
        userAvatar: row.userAvatar,
        type: row.type,
        caption: row.caption,
        timestamp: new Date(row.timestamp),
        isStory: row.isStory === 1,
        expiresAt: row.expiresAt ? new Date(row.expiresAt) : undefined,
        privacy: row.privacy,
        isVisible: row.isVisible === 1,
        isArchived: row.isArchived === 1,
        location: row.location,
        hashtags: row.hashtags ? JSON.parse(row.hashtags) : [],
        mentions: row.mentions ? JSON.parse(row.mentions) : [],
        groupTags: row.groupTags ? JSON.parse(row.groupTags) : [],
        media,
        likes: [], // Will be populated from interactions if needed
        views: [], // Will be populated from interactions if needed
        shares: [], // Will be populated from interactions if needed
        downloads: [], // Will be populated from interactions if needed
        reactions: [],
        comments,
        viewCount: row.viewCount,
        likeCount: row.likeCount,
        commentCount: row.commentCount,
        shareCount: row.shareCount,
        downloadCount: row.downloadCount,
        isLikedByCurrentUser: row.isLikedByCurrentUser === 1,
        isViewedByCurrentUser: row.isViewedByCurrentUser === 1,
        isSharedByCurrentUser: row.isSharedByCurrentUser === 1,
        isDownloadedByCurrentUser: row.isDownloadedByCurrentUser === 1,
        isReported: row.isReported === 1,
        reportCount: row.reportCount,
        isFlagged: row.isFlagged === 1,
        isPinned: row.isPinned === 1,
        isHighlight: row.isHighlight === 1,
        musicTrack: undefined, // TODO: Add music track support
      };

      return update;
    } catch (error) {
      console.error('❌ Failed to convert row to update:', error);
      return null;
    }
  }

  private rowToLocalUpdate(row: any): LocalUpdate {
    return {
      localId: row.localId,
      id: row.id,
      userId: row.userId,
      userName: row.userName,
      userAvatar: row.userAvatar,
      type: row.type,
      caption: row.caption,
      timestamp: row.timestamp,
      isStory: row.isStory === 1,
      expiresAt: row.expiresAt,
      privacy: row.privacy,
      isVisible: row.isVisible === 1,
      isArchived: row.isArchived === 1,
      location: row.location,
      hashtags: row.hashtags ? JSON.parse(row.hashtags) : [],
      mentions: row.mentions ? JSON.parse(row.mentions) : [],
      groupTags: row.groupTags ? JSON.parse(row.groupTags) : [],
      media: [], // Will be loaded separately if needed
      likes: [],
      views: [],
      shares: [],
      downloads: [],
      reactions: [],
      comments: [],
      viewCount: row.viewCount,
      likeCount: row.likeCount,
      commentCount: row.commentCount,
      shareCount: row.shareCount,
      downloadCount: row.downloadCount,
      isLikedByCurrentUser: row.isLikedByCurrentUser === 1,
      isViewedByCurrentUser: row.isViewedByCurrentUser === 1,
      isSharedByCurrentUser: row.isSharedByCurrentUser === 1,
      isDownloadedByCurrentUser: row.isDownloadedByCurrentUser === 1,
      isReported: row.isReported === 1,
      reportCount: row.reportCount,
      isFlagged: row.isFlagged === 1,
      isPinned: row.isPinned === 1,
      isHighlight: row.isHighlight === 1,
      musicTrack: undefined,
      syncStatus: row.syncStatus,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      isDeleted: row.isDeleted === 1,
      deletedAt: row.deletedAt,
    };
  }
}

export const localUpdatesStorage = new LocalUpdatesStorageService();

