/**
 * Message Selection Context for IraChat
 * Manages message selection state and actions
 */

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { Animated, Platform } from 'react-native';

export interface SelectedMessage {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice' | 'sticker' | 'location' | 'contact';
  timestamp: Date;
  mediaUrl?: string;
  caption?: string;
  fileName?: string;
  fileSize?: number;
  isOwn: boolean;
}

export interface MessageSelectionState {
  isSelectionMode: boolean;
  selectedMessages: Map<string, SelectedMessage>;
  lastSelectedMessageId: string | null;
  selectionStartedAt: Date | null;
}

export interface MessageSelectionActions {
  enterSelectionMode: (message: SelectedMessage) => void;
  exitSelectionMode: () => void;
  toggleMessageSelection: (message: SelectedMessage) => void;
  selectMessage: (message: SelectedMessage) => void;
  deselectMessage: (messageId: string) => void;
  selectAllMessages: (messages: SelectedMessage[]) => void;
  clearSelection: () => void;
  isMessageSelected: (messageId: string) => boolean;
  getSelectedCount: () => number;
  getSelectedMessages: () => SelectedMessage[];
  canReply: () => boolean;
  canCopy: () => boolean;
  canForward: () => boolean;
  canDelete: () => boolean;
  canStar: () => boolean;
  canPin: () => boolean;
  canEdit: () => boolean;
}

export interface MessageSelectionContextType extends MessageSelectionState, MessageSelectionActions {}

const MessageSelectionContext = createContext<MessageSelectionContextType | undefined>(undefined);

export const useMessageSelection = (): MessageSelectionContextType => {
  const context = useContext(MessageSelectionContext);
  if (!context) {
    throw new Error('useMessageSelection must be used within a MessageSelectionProvider');
  }
  return context;
};

interface MessageSelectionProviderProps {
  children: ReactNode;
  chatId: string;
  currentUserId: string;
}

export const MessageSelectionProvider: React.FC<MessageSelectionProviderProps> = ({
  children,
  chatId,
  currentUserId,
}) => {
  const [state, setState] = useState<MessageSelectionState>({
    isSelectionMode: false,
    selectedMessages: new Map(),
    lastSelectedMessageId: null,
    selectionStartedAt: null,
  });

  const enterSelectionMode = useCallback((message: SelectedMessage) => {
    console.log('📱 Entering selection mode with message:', message.id);
    
    setState(prevState => ({
      ...prevState,
      isSelectionMode: true,
      selectedMessages: new Map([[message.id, message]]),
      lastSelectedMessageId: message.id,
      selectionStartedAt: new Date(),
    }));
  }, []);

  const exitSelectionMode = useCallback(() => {
    console.log('📱 Exiting selection mode');
    
    setState({
      isSelectionMode: false,
      selectedMessages: new Map(),
      lastSelectedMessageId: null,
      selectionStartedAt: null,
    });
  }, []);

  const toggleMessageSelection = useCallback((message: SelectedMessage) => {
    setState(prevState => {
      const newSelectedMessages = new Map(prevState.selectedMessages);
      
      if (newSelectedMessages.has(message.id)) {
        // Deselect message
        newSelectedMessages.delete(message.id);
        
        // If no messages left, exit selection mode
        if (newSelectedMessages.size === 0) {
          return {
            isSelectionMode: false,
            selectedMessages: new Map(),
            lastSelectedMessageId: null,
            selectionStartedAt: null,
          };
        }
      } else {
        // Select message
        newSelectedMessages.set(message.id, message);
      }
      
      return {
        ...prevState,
        selectedMessages: newSelectedMessages,
        lastSelectedMessageId: message.id,
      };
    });
  }, []);

  const selectMessage = useCallback((message: SelectedMessage) => {
    setState(prevState => {
      const newSelectedMessages = new Map(prevState.selectedMessages);
      newSelectedMessages.set(message.id, message);
      
      return {
        ...prevState,
        selectedMessages: newSelectedMessages,
        lastSelectedMessageId: message.id,
      };
    });
  }, []);

  const deselectMessage = useCallback((messageId: string) => {
    setState(prevState => {
      const newSelectedMessages = new Map(prevState.selectedMessages);
      newSelectedMessages.delete(messageId);
      
      // If no messages left, exit selection mode
      if (newSelectedMessages.size === 0) {
        return {
          isSelectionMode: false,
          selectedMessages: new Map(),
          lastSelectedMessageId: null,
          selectionStartedAt: null,
        };
      }
      
      return {
        ...prevState,
        selectedMessages: newSelectedMessages,
      };
    });
  }, []);

  const selectAllMessages = useCallback((messages: SelectedMessage[]) => {
    const newSelectedMessages = new Map();
    messages.forEach(message => {
      newSelectedMessages.set(message.id, message);
    });
    
    setState(prevState => ({
      ...prevState,
      selectedMessages: newSelectedMessages,
      lastSelectedMessageId: messages[messages.length - 1]?.id || null,
    }));
  }, []);

  const clearSelection = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      selectedMessages: new Map(),
    }));
  }, []);

  const isMessageSelected = useCallback((messageId: string): boolean => {
    return state.selectedMessages.has(messageId);
  }, [state.selectedMessages]);

  const getSelectedCount = useCallback((): number => {
    return state.selectedMessages.size;
  }, [state.selectedMessages]);

  const getSelectedMessages = useCallback((): SelectedMessage[] => {
    return Array.from(state.selectedMessages.values());
  }, [state.selectedMessages]);

  // Action availability checks
  const canReply = useCallback((): boolean => {
    return state.selectedMessages.size === 1;
  }, [state.selectedMessages]);

  const canCopy = useCallback((): boolean => {
    if (state.selectedMessages.size === 0) return false;
    
    // Can copy if all selected messages are text or have captions
    const messages = Array.from(state.selectedMessages.values());
    return messages.every(msg => 
      msg.type === 'text' || 
      (msg.caption && msg.caption.trim().length > 0)
    );
  }, [state.selectedMessages]);

  const canForward = useCallback((): boolean => {
    return state.selectedMessages.size > 0;
  }, [state.selectedMessages]);

  const canDelete = useCallback((): boolean => {
    if (state.selectedMessages.size === 0) return false;
    
    // Can delete if all selected messages are own messages
    const messages = Array.from(state.selectedMessages.values());
    return messages.every(msg => msg.isOwn);
  }, [state.selectedMessages]);

  const canStar = useCallback((): boolean => {
    return state.selectedMessages.size > 0;
  }, [state.selectedMessages]);

  const canPin = useCallback((): boolean => {
    return state.selectedMessages.size === 1;
  }, [state.selectedMessages]);

  const canEdit = useCallback((): boolean => {
    if (state.selectedMessages.size !== 1) return false;
    
    const message = Array.from(state.selectedMessages.values())[0];
    return message.isOwn && message.type === 'text';
  }, [state.selectedMessages]);

  const contextValue: MessageSelectionContextType = {
    ...state,
    enterSelectionMode,
    exitSelectionMode,
    toggleMessageSelection,
    selectMessage,
    deselectMessage,
    selectAllMessages,
    clearSelection,
    isMessageSelected,
    getSelectedCount,
    getSelectedMessages,
    canReply,
    canCopy,
    canForward,
    canDelete,
    canStar,
    canPin,
    canEdit,
  };

  return (
    <MessageSelectionContext.Provider value={contextValue}>
      {children}
    </MessageSelectionContext.Provider>
  );
};

export default MessageSelectionContext;
