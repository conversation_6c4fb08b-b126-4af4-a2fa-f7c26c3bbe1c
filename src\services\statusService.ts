import {
  doc,
  onSnapshot,
  serverTimestamp,
  updateDoc,
} from "firebase/firestore";
import { AppState, AppStateStatus } from "react-native";
import { firestore } from "./firebaseSimple";
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface UserStatus {
  isOnline: boolean;
  lastSeen: Date;
  status?: string; // Custom status message
  isTyping?: { [chatId: string]: boolean };
}

class StatusService {
  private currentUserId: string | null = null;
  private statusUpdateInterval: ReturnType<typeof setInterval> | null = null;
  private appStateSubscription: any = null;
  private isInitialized = false;
  private offlineStatusQueue: Map<string, any[]> = new Map();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();

      // Set up network state listener for offline sync
      networkStateManager.addListener('statusService', (networkState) => {
        if (networkState.isConnected) {
          this.syncOfflineStatusUpdates();
        }
      });

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async syncOfflineStatusUpdates(): Promise<void> {
    try {
      // Sync any queued status updates when back online
      for (const [userId, queuedUpdates] of this.offlineStatusQueue.entries()) {
        for (const updateData of queuedUpdates) {
          try {
            await this.syncStatusUpdateToFirebase(updateData);
          } catch (error) {
            // Keep in queue for retry
            continue;
          }
        }
        // Clear successfully synced updates
        this.offlineStatusQueue.delete(userId);
      }
    } catch (error) {
      // Sync failed - will retry on next connection
    }
  }

  private async syncStatusUpdateToFirebase(updateData: any): Promise<void> {
    if (!firestore || !updateData.userId) return;

    const userRef = doc(firestore, "users", updateData.userId);
    await updateDoc(userRef, updateData.data);
  }

  private async storeOfflineStatusUpdate(userId: string, data: any): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO status_queue (
          userId, data, timestamp, synced
        ) VALUES (?, ?, ?, ?)
      `, [userId, JSON.stringify(data), Date.now(), 0]);

      // Also add to memory queue for immediate retry
      if (!this.offlineStatusQueue.has(userId)) {
        this.offlineStatusQueue.set(userId, []);
      }
      this.offlineStatusQueue.get(userId)!.push({ userId, data });
    } catch (error) {
      // Offline storage failed
    }
  }

  /**
   * Initialize status tracking for a user
   */
  async initializeStatus(userId: string): Promise<void> {
    try {
      if (!firestore) throw new Error("Firestore not initialized");

      this.currentUserId = userId;

      // Set user as online
      await this.setOnlineStatus(true);

      // Start periodic status updates
      this.startStatusUpdates();

      // Listen to app state changes
      this.setupAppStateListener();

    } catch (error) {
      // Error initializing status - continue silently
    }
  }

  /**
   * Set user online/offline status
   */
  async setOnlineStatus(isOnline: boolean): Promise<void> {
    try {
      if (!this.currentUserId) return;

      const statusData = {
        isOnline,
        lastSeen: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      if (networkStateManager.isOnline() && firestore) {
        // Check authentication before attempting update
        const { auth } = await import('./firebaseSimple');
        if (!auth?.currentUser) {
          console.warn("⚠️ User not authenticated for status update");
          return;
        }

        // Only allow users to update their own status
        if (auth.currentUser.uid !== this.currentUserId) {
          console.warn("⚠️ User can only update their own status");
          return;
        }

        const userRef = doc(firestore, "users", this.currentUserId);
        await updateDoc(userRef, statusData);
      } else {
        // Store offline for later sync
        await this.storeOfflineStatusUpdate(this.currentUserId, statusData);
      }

    } catch (error: any) {
      // Handle specific Firebase errors
      if (error.code === 'permission-denied') {
        console.warn("⚠️ Permission denied for status update - continuing silently");
      } else if (error.code === 'unavailable') {
        console.warn("⚠️ Firebase unavailable for status update - storing offline");
      } else {
        console.error("❌ Error updating online status:", error);
      }

      // If online update fails, try to store offline
      if (this.currentUserId) {
        try {
          await this.storeOfflineStatusUpdate(this.currentUserId, {
            isOnline,
            lastSeen: new Date(),
            updatedAt: new Date(),
          });
        } catch (offlineError) {
          // Both online and offline failed - continue silently
          console.warn("⚠️ Failed to store status update offline:", offlineError);
        }
      }
    }
  }

  /**
   * Update custom status message
   */
  async updateStatusMessage(status: string): Promise<void> {
    try {
      if (!this.currentUserId) return;

      const statusData = {
        status,
        updatedAt: serverTimestamp(),
      };

      if (networkStateManager.isOnline() && firestore) {
        const userRef = doc(firestore, "users", this.currentUserId);
        await updateDoc(userRef, statusData);
      } else {
        // Store offline for later sync
        await this.storeOfflineStatusUpdate(this.currentUserId, statusData);
      }

    } catch (error) {
      // Error updating status message - continue silently
    }
  }

  /**
   * Set typing indicator for a chat
   */
  async setTypingStatus(chatId: string, isTyping: boolean): Promise<void> {
    try {
      if (!this.currentUserId) return;

      const typingData = {
        [`isTyping.${chatId}`]: isTyping,
        updatedAt: serverTimestamp(),
      };

      if (networkStateManager.isOnline() && firestore) {
        const userRef = doc(firestore, "users", this.currentUserId);
        await updateDoc(userRef, typingData);
      } else {
        // Store offline for later sync
        await this.storeOfflineStatusUpdate(this.currentUserId, typingData);
      }

      // Auto-clear typing after 3 seconds
      if (isTyping) {
        setTimeout(() => {
          this.setTypingStatus(chatId, false);
        }, 3000);
      }
    } catch (error) {
      // Error updating typing status - continue silently
    }
  }

  /**
   * Listen to a user's status
   */
  listenToUserStatus(
    userId: string,
    callback: (_status: UserStatus) => void,
  ): () => void {
    try {
      if (!firestore) throw new Error("Firestore not initialized");

      const userRef = doc(firestore, "users", userId);

      return onSnapshot(userRef, (doc) => {
        if (doc.exists()) {
          const data = doc.data();
          const status: UserStatus = {
            isOnline: data.isOnline || false,
            lastSeen: data.lastSeen?.toDate ? data.lastSeen.toDate() :
                     (typeof data.lastSeen === 'number' ? new Date(data.lastSeen) : new Date()),
            status: data.status,
            isTyping: data.isTyping || {},
          };
          callback(status);
        }
      });
    } catch (error) {
      return () => {};
    }
  }

  /**
   * Start periodic status updates to maintain online presence
   */
  private startStatusUpdates(): void {
    // Update status every 30 seconds to maintain online presence
    this.statusUpdateInterval = setInterval(() => {
      if (this.currentUserId) {
        this.setOnlineStatus(true);
      }
    }, 30000);
  }

  /**
   * Setup app state listener to handle background/foreground
   */
  private setupAppStateListener(): void {
    this.appStateSubscription = AppState.addEventListener(
      "change",
      (nextAppState: AppStateStatus) => {
        if (nextAppState === "active") {
          // App came to foreground
          this.setOnlineStatus(true);
          this.startStatusUpdates();
        } else if (
          nextAppState === "background" ||
          nextAppState === "inactive"
        ) {
          // App went to background
          this.setOnlineStatus(false);
          this.stopStatusUpdates();
        }
      },
    );
  }

  /**
   * Stop status updates
   */
  private stopStatusUpdates(): void {
    if (this.statusUpdateInterval) {
      clearInterval(this.statusUpdateInterval);
      this.statusUpdateInterval = null;
    }
  }

  /**
   * Cleanup status tracking
   */
  async cleanup(): Promise<void> {
    try {
      // Set user offline
      if (this.currentUserId) {
        await this.setOnlineStatus(false);
      }

      // Stop updates
      this.stopStatusUpdates();

      // Remove app state listener
      if (this.appStateSubscription) {
        this.appStateSubscription.remove();
        this.appStateSubscription = null;
      }

      this.currentUserId = null;

    } catch (error) {
      // Error cleaning up status - continue silently
    }
  }

  /**
   * Format last seen time for display
   */
  formatLastSeen(lastSeen: Date, isOnline: boolean): string {
    if (isOnline) {
      return "Online";
    }

    const now = new Date();
    const diffMs = now.getTime() - lastSeen.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) {
      return "Just now";
    } else if (diffMins < 60) {
      return `${diffMins} min${diffMins > 1 ? "s" : ""} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours > 1 ? "s" : ""} ago`;
    } else if (diffDays === 1) {
      return "Yesterday";
    } else if (diffDays < 7) {
      return `${diffDays} days ago`;
    } else {
      return lastSeen.toLocaleDateString();
    }
  }

  /**
   * Get typing users in a chat
   */
  getTypingUsers(
    chatParticipants: string[],
    userStatuses: { [userId: string]: UserStatus },
    chatId: string,
  ): string[] {
    return chatParticipants.filter((userId) => {
      const status = userStatuses[userId];
      return status?.isTyping?.[chatId] === true;
    });
  }

  /**
   * Batch listen to multiple users' statuses
   */
  listenToMultipleUserStatuses(
    userIds: string[],
    callback: (_statuses: { [userId: string]: UserStatus }) => void,
  ): () => void {
    const unsubscribers: (() => void)[] = [];
    const statuses: { [userId: string]: UserStatus } = {};

    userIds.forEach((userId) => {
      const unsubscribe = this.listenToUserStatus(userId, (status) => {
        statuses[userId] = status;
        callback({ ...statuses });
      });
      unsubscribers.push(unsubscribe);
    });

    // Return cleanup function
    return () => {
      unsubscribers.forEach((unsubscribe) => unsubscribe());
    };
  }

  /**
   * Check if user was recently online (within last 5 minutes)
   */
  isRecentlyOnline(lastSeen: Date, isOnline: boolean): boolean {
    if (isOnline) return true;

    const now = new Date();
    const diffMs = now.getTime() - lastSeen.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    return diffMins <= 5;
  }

  /**
   * Get online status indicator color
   */
  getStatusColor(isOnline: boolean, lastSeen: Date): string {
    if (isOnline) {
      return "#4CAF50"; // Green for online
    } else if (this.isRecentlyOnline(lastSeen, false)) {
      return "#FF9800"; // Orange for recently online
    } else {
      return "#9E9E9E"; // Gray for offline
    }
  }
}

export const statusService = new StatusService();
export default statusService;
