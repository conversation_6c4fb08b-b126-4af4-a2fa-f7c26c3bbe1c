
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');
const fs = require('fs');

const config = getDefaultConfig(__dirname);

// Use default Metro cache (no custom cache override for now)
// This prevents path conflicts between D: drive cache and F: drive project

// ULTRA-STRICT MOBILE-ONLY CONFIGURATION WITH WEBRTC SUPPORT
config.resolver.platforms = ['ios', 'android', 'native'];

// WebRTC specific configuration
config.resolver.alias = {
  ...config.resolver.alias,
  'react-native-webrtc': 'react-native-webrtc',
};

// Ensure WebRTC native modules are properly resolved
config.resolver.sourceExts = [...config.resolver.sourceExts, 'jsx', 'ts', 'tsx'];
config.resolver.assetExts = [...config.resolver.assetExts, 'mp3', 'wav', 'aac', 'm4a'];

// Bundle optimization (less aggressive for development)
config.transformer.minifierConfig = {
  keep_fnames: true,
  mangle: {
    keep_fnames: true,
    toplevel: false,
  },
  compress: {
    drop_console: false, // Keep console.logs in development
    drop_debugger: false,
    pure_funcs: [],
  },
};

// Strict asset handling - only bundle what's explicitly listed
config.transformer.assetPlugins = ['expo-asset/tools/hashAssetFiles'];

// Exclude development files from bundling
config.resolver.blacklistRE = /.*\.(md|backup|log|tmp)$/;

// Tree shaking optimization
config.transformer.enableBabelRCLookup = true;

module.exports = config;
