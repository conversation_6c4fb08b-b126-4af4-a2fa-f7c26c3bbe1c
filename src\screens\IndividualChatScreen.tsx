import { useLocalSearchParams } from "expo-router";
import { useEffect, useState } from "react";
import { UltimateIndividualChatRoom } from "../components/UltimateIndividualChatRoom";
import { auth } from "../services/firebase";
import { User } from "../types";

const IndividualChatScreen = () => {
  const params = useLocalSearchParams();
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  useEffect(() => {
    // Ensure user is authenticated and create User object
    if (!auth?.currentUser) {
      console.warn("User not authenticated");
      // You might want to redirect to login here
      return;
    }

    // Create User object from Firebase auth user
    const firebaseUser = auth.currentUser;

    // Determine auth method and create appropriate user object
    const isEmailAuth = firebaseUser.email && !firebaseUser.phoneNumber;
    const isPhoneAuth = firebaseUser.phoneNumber && !firebaseUser.email;

    let userObject: User;

    if (isEmailAuth) {
      userObject = {
        id: firebaseUser.uid,
        username: firebaseUser.email!.split('@')[0],
        displayName: firebaseUser.displayName || firebaseUser.email!.split('@')[0],
        name: firebaseUser.displayName || firebaseUser.email!.split('@')[0],
        avatar: firebaseUser.photoURL || '',
        photoURL: firebaseUser.photoURL || '',
        followersCount: 0,
        followingCount: 0,
        likesCount: 0,
        isVerified: false,
        isOnline: true,
        email: firebaseUser.email!,
        authMethod: 'email' as const,
        emailVerified: firebaseUser.emailVerified,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
    } else if (isPhoneAuth) {
      userObject = {
        id: firebaseUser.uid,
        username: firebaseUser.phoneNumber!.replace(/\D/g, ''),
        displayName: firebaseUser.displayName || firebaseUser.phoneNumber!,
        name: firebaseUser.displayName || firebaseUser.phoneNumber!,
        avatar: firebaseUser.photoURL || '',
        photoURL: firebaseUser.photoURL || '',
        followersCount: 0,
        followingCount: 0,
        likesCount: 0,
        isVerified: false,
        isOnline: true,
        phoneNumber: firebaseUser.phoneNumber!,
        authMethod: 'phone' as const,
        phoneVerified: true, // Assume verified if they're logged in
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
    } else {
      // Fallback for mixed or unknown auth
      userObject = {
        id: firebaseUser.uid,
        username: firebaseUser.email?.split('@')[0] || firebaseUser.uid.substring(0, 8),
        displayName: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
        name: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
        avatar: firebaseUser.photoURL || '',
        photoURL: firebaseUser.photoURL || '',
        followersCount: 0,
        followingCount: 0,
        likesCount: 0,
        isVerified: false,
        isOnline: true,
        email: firebaseUser.email || '<EMAIL>',
        authMethod: 'email' as const,
        emailVerified: firebaseUser.emailVerified,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
    }

    setCurrentUser(userObject);
    console.log('🔍 Current user set:', userObject);
  }, []);

  // Parse contact data from params
  const contact = {
    id: params.contactId as string,
    name: params.contactName as string,
    avatar: params.contactAvatar as string,
    isOnline: params.contactIsOnline === "true",
  };

  // Don't render until we have current user
  if (!currentUser) {
    return null; // Or a loading spinner
  }

  return (
    <UltimateIndividualChatRoom
      chatId={contact.id}
      partnerName={contact.name}
      partnerAvatar={contact.avatar}

      partnerId={contact.id}
      currentUserId={currentUser.id}
    />
  );
};

export default IndividualChatScreen;
