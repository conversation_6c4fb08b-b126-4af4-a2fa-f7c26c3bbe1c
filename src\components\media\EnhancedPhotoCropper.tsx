/**
 * Enhanced Photo Cropper Component
 * Advanced photo cropping with multiple aspect ratios, filters, and smooth UI
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
  ScrollView,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { PanGestureHandler, PinchGestureHandler, State } from 'react-native-gesture-handler';
import { manipulateAsync, SaveFormat, FlipType } from 'expo-image-manipulator';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface AspectRatioOption {
  id: string;
  name: string;
  ratio: number | null; // null for free crop
  icon: string;
}

interface FilterOption {
  id: string;
  name: string;
  icon: string;
}

interface EnhancedPhotoCropperProps {
  imageUri: string;
  onCropComplete: (croppedUri: string) => void;
  onCancel: () => void;
  initialAspectRatio?: number;
}

export const EnhancedPhotoCropper: React.FC<EnhancedPhotoCropperProps> = ({
  imageUri,
  onCropComplete,
  onCancel,
  initialAspectRatio,
}) => {
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [cropArea, setCropArea] = useState({
    x: 50,
    y: 100,
    width: screenWidth - 100,
    height: screenWidth - 100,
  });
  const [scale, setScale] = useState(1);
  const [lastScale, setLastScale] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAspectRatio, setSelectedAspectRatio] = useState<string>('free');
  const [selectedFilter, setSelectedFilter] = useState<string>('none');
  const [rotation, setRotation] = useState(0);
  const [isFlippedHorizontally, setIsFlippedHorizontally] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  const aspectRatioOptions: AspectRatioOption[] = [
    { id: 'free', name: 'Free', ratio: null, icon: 'crop' },
    { id: 'square', name: '1:1', ratio: 1, icon: 'square' },
    { id: 'story', name: '9:16', ratio: 9/16, icon: 'phone-portrait' },
    { id: 'post', name: '4:5', ratio: 4/5, icon: 'image' },
    { id: 'landscape', name: '16:9', ratio: 16/9, icon: 'phone-landscape' },
  ];

  const filterOptions: FilterOption[] = [
    { id: 'none', name: 'Original', icon: 'image-outline' },
    { id: 'bw', name: 'B&W', icon: 'contrast' },
    { id: 'sepia', name: 'Sepia', icon: 'sunny' },
    { id: 'vintage', name: 'Vintage', icon: 'film' },
  ];

  const containerHeight = screenHeight - 300; // More space for controls
  const maxImageWidth = screenWidth - 40;
  const maxImageHeight = containerHeight - 100;

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();

    // Load image dimensions
    Image.getSize(imageUri, (width, height) => {
      const imageAspectRatio = width / height;
      let displayWidth = maxImageWidth;
      let displayHeight = maxImageHeight;

      if (imageAspectRatio > maxImageWidth / maxImageHeight) {
        displayHeight = maxImageWidth / imageAspectRatio;
      } else {
        displayWidth = maxImageHeight * imageAspectRatio;
      }

      setImageSize({ width: displayWidth, height: displayHeight });

      // Set initial crop area
      const cropSize = Math.min(displayWidth, displayHeight) * 0.8;
      setCropArea({
        x: (displayWidth - cropSize) / 2,
        y: (displayHeight - cropSize) / 2,
        width: cropSize,
        height: cropSize,
      });
    });

    // Set initial aspect ratio if provided
    if (initialAspectRatio) {
      const matchingRatio = aspectRatioOptions.find(option => 
        option.ratio === initialAspectRatio
      );
      if (matchingRatio) {
        setSelectedAspectRatio(matchingRatio.id);
      }
    }
  }, [imageUri, initialAspectRatio]);

  const handleAspectRatioChange = (optionId: string) => {
    setSelectedAspectRatio(optionId);
    const option = aspectRatioOptions.find(opt => opt.id === optionId);
    
    if (option && option.ratio) {
      // Adjust crop area to match aspect ratio
      const { width: imgWidth, height: imgHeight } = imageSize;
      const maxSize = Math.min(imgWidth, imgHeight) * 0.8;
      
      let newWidth, newHeight;
      if (option.ratio > 1) {
        // Landscape
        newWidth = maxSize;
        newHeight = maxSize / option.ratio;
      } else {
        // Portrait or square
        newHeight = maxSize;
        newWidth = maxSize * option.ratio;
      }

      setCropArea({
        x: (imgWidth - newWidth) / 2,
        y: (imgHeight - newHeight) / 2,
        width: newWidth,
        height: newHeight,
      });
    }
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const handleFlip = () => {
    setIsFlippedHorizontally(prev => !prev);
  };

  const handleCrop = async () => {
    if (!imageSize.width || !imageSize.height) return;
    
    setIsLoading(true);
    
    try {
      Image.getSize(imageUri, async (originalWidth, originalHeight) => {
        const scaleX = originalWidth / imageSize.width;
        const scaleY = originalHeight / imageSize.height;
        
        const cropParams = {
          originX: cropArea.x * scaleX,
          originY: cropArea.y * scaleY,
          width: cropArea.width * scaleX,
          height: cropArea.height * scaleY,
        };

        let manipulateActions: any[] = [{ crop: cropParams }];

        // Add rotation if needed
        if (rotation !== 0) {
          manipulateActions.push({ rotate: rotation });
        }

        // Add flip if needed
        if (isFlippedHorizontally) {
          manipulateActions.push({ flip: FlipType.Horizontal });
        }

        // Apply filter effects (simplified - in real app would use more sophisticated filters)
        let format = SaveFormat.JPEG;
        let compress = 0.8;

        if (selectedFilter === 'bw') {
          // For B&W, we'd typically use a more sophisticated filter
          // This is a simplified approach
          compress = 0.9;
        }

        const manipResult = await manipulateAsync(
          imageUri,
          manipulateActions,
          { compress, format }
        );
        
        onCropComplete(manipResult.uri);
      });
    } catch (error) {
      console.error('Crop failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderAspectRatioOptions = () => (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.optionsScrollView}
      contentContainerStyle={styles.optionsContainer}
    >
      {aspectRatioOptions.map((option) => (
        <TouchableOpacity
          key={option.id}
          style={[
            styles.optionButton,
            selectedAspectRatio === option.id && styles.selectedOptionButton
          ]}
          onPress={() => handleAspectRatioChange(option.id)}
        >
          <Ionicons 
            name={option.icon as any} 
            size={20} 
            color={selectedAspectRatio === option.id ? '#FFFFFF' : '#666666'} 
          />
          <Text style={[
            styles.optionText,
            selectedAspectRatio === option.id && styles.selectedOptionText
          ]}>
            {option.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  const renderFilterOptions = () => (
    <ScrollView 
      horizontal 
      showsHorizontalScrollIndicator={false}
      style={styles.optionsScrollView}
      contentContainerStyle={styles.optionsContainer}
    >
      {filterOptions.map((filter) => (
        <TouchableOpacity
          key={filter.id}
          style={[
            styles.optionButton,
            selectedFilter === filter.id && styles.selectedOptionButton
          ]}
          onPress={() => setSelectedFilter(filter.id)}
        >
          <Ionicons 
            name={filter.icon as any} 
            size={20} 
            color={selectedFilter === filter.id ? '#FFFFFF' : '#666666'} 
          />
          <Text style={[
            styles.optionText,
            selectedFilter === filter.id && styles.selectedOptionText
          ]}>
            {filter.name}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }]
        }
      ]}
    >
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={onCancel}>
          <Ionicons name="close" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Photo</Text>
        <TouchableOpacity 
          style={[styles.headerButton, styles.doneButton]} 
          onPress={handleCrop}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Ionicons name="checkmark" size={24} color="#FFFFFF" />
          )}
        </TouchableOpacity>
      </View>

      {/* Image and Crop Area */}
      <View style={styles.imageContainer}>
        {imageSize.width > 0 && (
          <View style={styles.imageWrapper}>
            <Image
              source={{ uri: imageUri }}
              style={[
                styles.image,
                {
                  width: imageSize.width,
                  height: imageSize.height,
                  transform: [
                    { rotate: `${rotation}deg` },
                    { scaleX: isFlippedHorizontally ? -1 : 1 }
                  ]
                }
              ]}
              resizeMode="contain"
            />
            
            {/* Crop Overlay */}
            <View
              style={[
                styles.cropOverlay,
                {
                  left: cropArea.x,
                  top: cropArea.y,
                  width: cropArea.width,
                  height: cropArea.height,
                }
              ]}
            >
              <View style={styles.cropBorder} />
              <View style={styles.cropCorners}>
                <View style={[styles.cropCorner, styles.topLeft]} />
                <View style={[styles.cropCorner, styles.topRight]} />
                <View style={[styles.cropCorner, styles.bottomLeft]} />
                <View style={[styles.cropCorner, styles.bottomRight]} />
              </View>
            </View>
          </View>
        )}
      </View>

      {/* Controls */}
      <View style={styles.controlsContainer}>
        {/* Transform Controls */}
        <View style={styles.transformControls}>
          <TouchableOpacity style={styles.transformButton} onPress={handleRotate}>
            <Ionicons name="refresh" size={20} color="#FFFFFF" />
            <Text style={styles.transformButtonText}>Rotate</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.transformButton} onPress={handleFlip}>
            <Ionicons name="swap-horizontal" size={20} color="#FFFFFF" />
            <Text style={styles.transformButtonText}>Flip</Text>
          </TouchableOpacity>
        </View>

        {/* Aspect Ratio Options */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Aspect Ratio</Text>
          {renderAspectRatioOptions()}
        </View>

        {/* Filter Options */}
        <View style={styles.sectionContainer}>
          <Text style={styles.sectionTitle}>Filters</Text>
          {renderFilterOptions()}
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50, // Account for status bar
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  doneButton: {
    backgroundColor: '#25D366',
    borderRadius: 20,
    paddingHorizontal: 16,
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#000000',
  },
  imageWrapper: {
    position: 'relative',
  },
  image: {
    backgroundColor: '#111111',
  },
  cropOverlay: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: '#25D366',
    borderStyle: 'dashed',
  },
  cropBorder: {
    position: 'absolute',
    top: -2,
    left: -2,
    right: -2,
    bottom: -2,
    borderWidth: 2,
    borderColor: '#25D366',
  },
  cropCorners: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  cropCorner: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderColor: '#25D366',
    borderWidth: 3,
  },
  topLeft: {
    top: -10,
    left: -10,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: -10,
    right: -10,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: -10,
    left: -10,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: -10,
    right: -10,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  controlsContainer: {
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    paddingBottom: 34, // Account for safe area
  },
  transformControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 32,
  },
  transformButton: {
    alignItems: 'center',
    gap: 4,
  },
  transformButtonText: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  sectionContainer: {
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  optionsScrollView: {
    maxHeight: 60,
  },
  optionsContainer: {
    paddingHorizontal: 16,
    gap: 8,
  },
  optionButton: {
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: 60,
    gap: 4,
  },
  selectedOptionButton: {
    backgroundColor: '#25D366',
  },
  optionText: {
    fontSize: 10,
    color: '#666666',
    fontWeight: '500',
  },
  selectedOptionText: {
    color: '#FFFFFF',
  },
});
