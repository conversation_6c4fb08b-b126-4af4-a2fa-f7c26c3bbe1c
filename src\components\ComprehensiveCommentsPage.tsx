// 💬 COMPREHENSIVE COMMENTS PAGE
// Beautiful threaded comments with reactions, mentions, and animations
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Modal,
  StatusBar,
  Animated,
  FlatList,
  TextInput,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { UpdateComment } from '../types/Update';
import { comprehensiveUpdatesService } from '../services/comprehensiveUpdatesService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
};

interface ComprehensiveCommentsPageProps {
  visible: boolean;
  updateId: string;
  updateTitle?: string;
  updateThumbnail?: string;
  currentUserId: string;
  currentUserName: string;
  currentUserAvatar?: string;
  onClose: () => void;
  onCommentAdded?: (comment: UpdateComment) => void;
}

export const ComprehensiveCommentsPage: React.FC<ComprehensiveCommentsPageProps> = ({
  visible,
  updateId,
  updateTitle,
  updateThumbnail,
  currentUserId,
  currentUserName,
  currentUserAvatar,
  onClose,
  onCommentAdded,
}) => {
  const insets = useSafeAreaInsets();
  
  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const commentInputHeight = isTablet ? 70 : 60;
  const headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================

  const [comments, setComments] = useState<UpdateComment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [replyingTo, setReplyingTo] = useState<UpdateComment | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [selectedComment, setSelectedComment] = useState<string | null>(null);

  // Animation refs
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const inputFocusAnim = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList>(null);

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible) {
      // Only load comments if not already loading
      if (!isLoading) {
        loadComments();
      }
      showModal();
    } else {
      hideModal();
    }
  }, [visible]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Reset state when component unmounts
      setComments([]);
      setCommentText('');
      setReplyingTo(null);
      setSelectedComment(null);
    };
  }, []);

  // ==================== ANIMATION METHODS ====================

  const showModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setCommentText('');
      setReplyingTo(null);
      setSelectedComment(null);
    });
  };

  const animateInputFocus = (focused: boolean) => {
    Animated.timing(inputFocusAnim, {
      toValue: focused ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  };

  // ==================== HELPER METHODS ====================

  const formatTimeAgo = (timestamp: Date | any): string => {
    try {
      let date: Date;

      if (timestamp instanceof Date) {
        date = timestamp;
      } else if (timestamp?.toDate && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp) {
        date = new Date(timestamp);
      } else {
        return 'now';
      }

      if (isNaN(date.getTime())) {
        return 'now';
      }

      const now = Date.now();
      const diffMs = now - date.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffMinutes < 1) return 'now';
      if (diffMinutes < 60) return `${diffMinutes}m`;
      if (diffHours < 24) return `${diffHours}h`;
      if (diffDays < 7) return `${diffDays}d`;

      return date.toLocaleDateString();
    } catch (error) {
      console.warn('⚠️ Error formatting timestamp:', error);
      return 'now';
    }
  };

  // ==================== DATA METHODS ====================

  const loadComments = async () => {
    setIsLoading(true);
    try {
      // Load comments from service - real implementation
      const result = await comprehensiveUpdatesService.getComments(updateId);

      if (result.success && result.comments) {
        // Process comments to ensure proper data structure and handle nested replies
        const processedComments = result.comments.map((comment: any) => ({
          ...comment,
          timestamp: comment.timestamp?.toDate ? comment.timestamp.toDate() : new Date(comment.timestamp),
          likes: Array.isArray(comment.likes) ? comment.likes : [],
          replies: Array.isArray(comment.replies) ? comment.replies.map((reply: any) => ({
            ...reply,
            timestamp: reply.timestamp?.toDate ? reply.timestamp.toDate() : new Date(reply.timestamp),
            likes: Array.isArray(reply.likes) ? reply.likes : [],
          })) : [],
          mentions: Array.isArray(comment.mentions) ? comment.mentions : [],
        }));

        // Separate top-level comments from replies
        const topLevelComments = processedComments.filter((comment: any) => !comment.parentCommentId);
        const replies = processedComments.filter((comment: any) => comment.parentCommentId);

        // Nest replies under their parent comments
        const commentsWithReplies = topLevelComments.map((comment: any) => ({
          ...comment,
          replies: replies.filter((reply: any) => reply.parentCommentId === comment.id),
        }));

        setComments(commentsWithReplies);
      } else {
        // Fallback to empty array if no comments
        setComments([]);
      }

      // Using real Firebase data only - no mock data
    } catch (error) {
      console.error('❌ Error loading comments:', error);
      Alert.alert('Error', 'Failed to load comments');
      setComments([]); // Ensure we set empty array on error
    } finally {
      setIsLoading(false);
    }
  };

  const submitComment = async () => {
    if (!commentText.trim() || isSubmitting) return;

    // Validate required fields
    if (!updateId || !currentUserId || !currentUserName) {
      Alert.alert('Error', 'Missing required information to post comment');
      return;
    }

    // Check comment length
    const trimmedText = commentText.trim();
    if (trimmedText.length > 500) {
      Alert.alert('Error', 'Comment is too long. Maximum 500 characters allowed.');
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await comprehensiveUpdatesService.addComment(
        updateId,
        currentUserId,
        currentUserName,
        currentUserAvatar,
        trimmedText,
        replyingTo?.id
      );

      if (result.success && result.data) {
        const newComment = result.data.comment;

        if (replyingTo) {
          // Add as reply - ensure proper data structure
          const processedReply = {
            ...newComment,
            timestamp: newComment.timestamp instanceof Date ? newComment.timestamp :
                      (newComment.timestamp as any)?.toDate ? (newComment.timestamp as any).toDate() :
                      new Date(newComment.timestamp),
            likes: Array.isArray(newComment.likes) ? newComment.likes : [],
            replies: [], // Replies don't have nested replies
            mentions: Array.isArray(newComment.mentions) ? newComment.mentions : [],
          };

          setComments(prev => prev.map(comment =>
            comment.id === replyingTo.id
              ? { ...comment, replies: [...comment.replies, processedReply] }
              : comment
          ));

          // Scroll to the parent comment to show the new reply
          setTimeout(() => {
            try {
              const parentIndex = comments.findIndex(c => c.id === replyingTo.id);
              if (parentIndex >= 0 && flatListRef.current) {
                flatListRef.current.scrollToIndex({
                  index: parentIndex,
                  animated: true,
                  viewPosition: 0.5 // Center the comment
                });
              }
            } catch (scrollError) {
              console.warn('⚠️ Could not scroll to parent comment:', scrollError);
              // Fallback to scroll to top
              flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
            }
          }, 150);
        } else {
          // Add as new top-level comment
          const processedComment = {
            ...newComment,
            timestamp: newComment.timestamp instanceof Date ? newComment.timestamp :
                      (newComment.timestamp as any)?.toDate ? (newComment.timestamp as any).toDate() :
                      new Date(newComment.timestamp),
            likes: Array.isArray(newComment.likes) ? newComment.likes : [],
            replies: Array.isArray(newComment.replies) ? newComment.replies : [],
            mentions: Array.isArray(newComment.mentions) ? newComment.mentions : [],
          };

          setComments(prev => [processedComment, ...prev]);

          // Scroll to top to show new comment
          setTimeout(() => {
            try {
              flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
            } catch (scrollError) {
              console.warn('⚠️ Could not scroll to top:', scrollError);
            }
          }, 150);
        }

        setCommentText('');
        setReplyingTo(null);
        onCommentAdded?.(newComment);
      } else {
        // Handle service error
        const errorMessage = result.error || 'Failed to post comment';
        console.error('❌ Service error:', errorMessage);
        Alert.alert('Error', errorMessage);
      }
    } catch (error) {
      console.error('❌ Error submitting comment:', error);
      Alert.alert('Error', 'Failed to post comment. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const likeComment = async (commentId: string, isReply: boolean = false, parentId?: string) => {
    try {
      // Store original state for rollback on error
      const originalComments = comments;

      // Toggle like locally first for immediate feedback
      if (isReply && parentId) {
        setComments(prev => prev.map(comment =>
          comment.id === parentId
            ? {
                ...comment,
                replies: comment.replies.map(reply =>
                  reply.id === commentId
                    ? {
                        ...reply,
                        likes: Array.isArray(reply.likes)
                          ? (reply.likes.includes(currentUserId)
                              ? reply.likes.filter(id => id !== currentUserId)
                              : [...reply.likes, currentUserId])
                          : [currentUserId]
                      }
                    : reply
                )
              }
            : comment
        ));
      } else {
        setComments(prev => prev.map(comment =>
          comment.id === commentId
            ? {
                ...comment,
                likes: Array.isArray(comment.likes)
                  ? (comment.likes.includes(currentUserId)
                      ? comment.likes.filter(id => id !== currentUserId)
                      : [...comment.likes, currentUserId])
                  : [currentUserId]
              }
            : comment
        ));
      }

      // Then sync with backend - implement when service method is available
      try {
        // await comprehensiveUpdatesService.likeComment(commentId, currentUserId);
        console.log('💖 Like toggled for comment:', commentId, 'by user:', currentUserId);
      } catch (backendError) {
        // Rollback on backend error
        console.error('❌ Backend error, rolling back like:', backendError);
        setComments(originalComments);
        Alert.alert('Error', 'Failed to update like. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error liking comment:', error);
      Alert.alert('Error', 'Failed to like comment');
    }
  };

  const replyToComment = (comment: UpdateComment) => {
    setReplyingTo(comment);
    setCommentText(`@${comment.userName} `);
  };

  const cancelReply = () => {
    setReplyingTo(null);
    setCommentText('');
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="chevron-down" size={28} color={COLORS.text} />
          </TouchableOpacity>

          <View style={styles.headerInfo}>
            <Text style={styles.headerTitle}>Comments</Text>
            <Text style={styles.headerSubtitle}>
              {comments.reduce((total, comment) => total + 1 + comment.replies.length, 0)} comments
            </Text>
            {updateTitle && (
              <Text style={styles.updateTitle} numberOfLines={1}>
                {updateTitle}
              </Text>
            )}
          </View>

          <TouchableOpacity style={styles.headerAction}>
            <Ionicons name="ellipsis-horizontal" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        {/* Update Preview Row */}
        {(updateTitle || updateThumbnail) && (
          <View style={styles.updatePreview}>
            {updateThumbnail && (
              <Image
                source={{ uri: updateThumbnail }}
                style={styles.updateThumbnail}
                resizeMode="cover"
              />
            )}
            {updateTitle && (
              <View style={styles.updatePreviewText}>
                <Text style={styles.updatePreviewTitle} numberOfLines={2}>
                  {updateTitle}
                </Text>
              </View>
            )}
          </View>
        )}
      </LinearGradient>
    </View>
  );

  const renderCommentItem = ({ item: comment }: { item: UpdateComment; index: number }) => (
    <Animated.View
      style={[
        styles.commentContainer,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [20, 0],
            }),
          }],
        },
      ]}
    >
      {/* Main Comment */}
      <TouchableOpacity
        style={styles.commentItem}
        onPress={() => setSelectedComment(selectedComment === comment.id ? null : comment.id)}
        activeOpacity={0.7}
      >
        <TouchableOpacity style={styles.commentAvatar}>
          <Image
            source={{ uri: comment.userAvatar || 'https://via.placeholder.com/40' }}
            style={styles.avatarImage}
          />
        </TouchableOpacity>

        <View style={styles.commentContent}>
          <View style={[
            styles.commentBubble,
            selectedComment === comment.id && styles.commentBubbleSelected
          ]}>
            <Text style={styles.commentUserName}>{comment.userName}</Text>
            <Text style={styles.commentText}>{comment.text}</Text>
            
            <View style={styles.commentMeta}>
              <Text style={styles.commentTime}>
                {formatTimeAgo(comment.timestamp)}
              </Text>

              <TouchableOpacity
                style={styles.commentAction}
                onPress={() => likeComment(comment.id)}
              >
                <Ionicons
                  name={Array.isArray(comment.likes) && comment.likes.includes(currentUserId) ? "heart" : "heart-outline"}
                  size={16}
                  color={Array.isArray(comment.likes) && comment.likes.includes(currentUserId) ? COLORS.error : COLORS.textMuted}
                />
                <Text style={styles.commentActionText}>
                  {Array.isArray(comment.likes) ? comment.likes.length : 0}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity 
                style={styles.commentAction}
                onPress={() => replyToComment(comment)}
              >
                <Text style={styles.commentActionText}>Reply</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </TouchableOpacity>

      {/* Replies */}
      {comment.replies.length > 0 && (
        <View style={styles.repliesContainer}>
          {comment.replies.map((reply) => (
            <View key={reply.id} style={styles.replyItem}>
              <TouchableOpacity style={styles.replyAvatar}>
                <Image 
                  source={{ uri: reply.userAvatar || 'https://via.placeholder.com/32' }} 
                  style={styles.replyAvatarImage} 
                />
              </TouchableOpacity>

              <View style={styles.replyContent}>
                <View style={styles.replyBubble}>
                  <Text style={styles.replyUserName}>{reply.userName}</Text>
                  <Text style={styles.replyText}>{reply.text}</Text>
                  
                  <View style={styles.replyMeta}>
                    <Text style={styles.replyTime}>
                      {formatTimeAgo(reply.timestamp)}
                    </Text>

                    <TouchableOpacity
                      style={styles.replyAction}
                      onPress={() => likeComment(reply.id, true, comment.id)}
                    >
                      <Ionicons
                        name={Array.isArray(reply.likes) && reply.likes.includes(currentUserId) ? "heart" : "heart-outline"}
                        size={14}
                        color={Array.isArray(reply.likes) && reply.likes.includes(currentUserId) ? COLORS.error : COLORS.textMuted}
                      />
                      <Text style={styles.replyActionText}>
                        {Array.isArray(reply.likes) ? reply.likes.length : 0}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </View>
          ))}
        </View>
      )}
    </Animated.View>
  );

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="none" statusBarTranslucent>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <Animated.View
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
              marginTop: headerHeight,
            },
          ]}
        >
          {renderHeader()}

          {/* Comments List */}
          <FlatList
            ref={flatListRef}
            data={comments}
            renderItem={renderCommentItem}
            keyExtractor={(item) => item.id}
            style={styles.commentsList}
            contentContainerStyle={styles.commentsListContent}
            showsVerticalScrollIndicator={false}
            inverted={false}
            ListEmptyComponent={() => (
              <View style={styles.emptyContainer}>
                <Ionicons name="chatbubbles-outline" size={64} color={COLORS.textMuted} />
                <Text style={styles.emptyText}>No comments yet</Text>
                <Text style={styles.emptySubtext}>Be the first to comment!</Text>
              </View>
            )}
          />

          {/* Reply Banner */}
          {replyingTo && (
            <Animated.View style={styles.replyBanner}>
              <View style={styles.replyBannerContent}>
                <Text style={styles.replyBannerText}>
                  Replying to {replyingTo.userName}
                </Text>
                <TouchableOpacity onPress={cancelReply}>
                  <Ionicons name="close" size={20} color={COLORS.textMuted} />
                </TouchableOpacity>
              </View>
            </Animated.View>
          )}

          {/* Comment Input */}
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.inputContainer, { minHeight: commentInputHeight }]}
          >
            <Animated.View
              style={[
                styles.inputWrapper,
                {
                  borderColor: inputFocusAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [COLORS.surfaceLight, COLORS.primary],
                  }),
                  minHeight: isTablet ? 50 : 44,
                },
              ]}
            >
              <TouchableOpacity style={styles.inputAvatar}>
                <Image
                  source={{ uri: currentUserAvatar || 'https://via.placeholder.com/32' }}
                  style={styles.inputAvatarImage}
                />
              </TouchableOpacity>

              <TextInput
                style={[styles.textInput, { fontSize: isTablet ? 18 : 16 }]}
                placeholder="Add a comment..."
                placeholderTextColor={COLORS.textMuted}
                value={commentText}
                onChangeText={setCommentText}
                multiline
                maxLength={500}
                onFocus={() => animateInputFocus(true)}
                onBlur={() => animateInputFocus(false)}
              />

              <TouchableOpacity
                style={styles.emojiButton}
                onPress={() => setShowEmojiPicker(!showEmojiPicker)}
              >
                <Ionicons name="happy-outline" size={24} color={COLORS.textMuted} />
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.sendButton,
                  { opacity: commentText.trim() ? 1 : 0.5 }
                ]}
                onPress={submitComment}
                disabled={!commentText.trim() || isSubmitting}
              >
                {isSubmitting ? (
                  <Animated.View style={styles.sendingIndicator}>
                    <Ionicons name="hourglass" size={20} color={COLORS.text} />
                  </Animated.View>
                ) : (
                  <Ionicons name="send" size={20} color={COLORS.text} />
                )}
              </TouchableOpacity>
            </Animated.View>

            {/* Emoji Picker Placeholder */}
            {showEmojiPicker && (
              <View style={styles.emojiPickerContainer}>
                <Text style={styles.emojiPickerText}>😀 😂 😍 😢 😡 👍 👎 ❤️ 🔥 💯</Text>
                <TouchableOpacity
                  style={styles.emojiPickerClose}
                  onPress={() => setShowEmojiPicker(false)}
                >
                  <Ionicons name="close" size={20} color={COLORS.textMuted} />
                </TouchableOpacity>
              </View>
            )}
          </KeyboardAvoidingView>
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.overlay,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  header: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 2,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  headerAction: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  updateTitle: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontWeight: '400',
    marginTop: 2,
  },
  updatePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 12,
    paddingBottom: 8,
  },
  updateThumbnail: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 12,
    borderWidth: 1,
    borderColor: COLORS.primaryLight,
  },
  updatePreviewText: {
    flex: 1,
  },
  updatePreviewTitle: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
    lineHeight: 18,
  },
  commentsList: {
    flex: 1,
  },
  commentsListContent: {
    paddingVertical: 16,
  },
  commentContainer: {
    marginBottom: 16,
  },
  commentItem: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    alignItems: 'flex-start',
  },
  commentAvatar: {
    marginRight: 12,
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  commentContent: {
    flex: 1,
  },
  commentBubble: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    borderTopLeftRadius: 4,
    padding: 12,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  commentBubbleSelected: {
    backgroundColor: COLORS.surfaceLight,
    borderWidth: 2,
    borderColor: COLORS.primary,
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  commentUserName: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 4,
  },
  commentText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 20,
    marginBottom: 8,
  },
  commentMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  commentTime: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginRight: 16,
  },
  commentAction: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    backgroundColor: 'rgba(135, 206, 235, 0.1)',
  },
  commentActionText: {
    fontSize: 12,
    color: COLORS.textMuted,
    fontWeight: '500',
    marginLeft: 4,
  },
  repliesContainer: {
    marginTop: 8,
    marginLeft: 52,
    borderLeftWidth: 2,
    borderLeftColor: COLORS.surfaceLight,
    paddingLeft: 16,
  },
  replyItem: {
    flexDirection: 'row',
    marginBottom: 12,
    alignItems: 'flex-start',
  },
  replyAvatar: {
    marginRight: 8,
  },
  replyAvatarImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1.5,
    borderColor: COLORS.primaryLight,
  },
  replyContent: {
    flex: 1,
  },
  replyBubble: {
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 12,
    borderTopLeftRadius: 4,
    padding: 10,
  },
  replyUserName: {
    fontSize: 13,
    fontWeight: '600',
    color: COLORS.primaryLight,
    marginBottom: 3,
  },
  replyText: {
    fontSize: 14,
    color: COLORS.text,
    lineHeight: 18,
    marginBottom: 6,
  },
  replyMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  replyTime: {
    fontSize: 11,
    color: COLORS.textMuted,
    marginRight: 12,
  },
  replyAction: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 8,
    backgroundColor: 'rgba(135, 206, 235, 0.05)',
  },
  replyActionText: {
    fontSize: 11,
    color: COLORS.textMuted,
    marginLeft: 3,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
  },
  replyBanner: {
    backgroundColor: COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  replyBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  replyBannerText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500',
  },
  inputContainer: {
    backgroundColor: COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderRadius: 25,
    margin: 12,
    backgroundColor: COLORS.inputBackground,
  },
  inputAvatar: {
    marginRight: 12,
    marginBottom: 4,
  },
  inputAvatarImage: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1.5,
    borderColor: COLORS.primary,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    maxHeight: 100,
    paddingVertical: 8,
    paddingHorizontal: 4,
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  sendingIndicator: {
    transform: [{ rotate: '45deg' }],
  },
  emojiButton: {
    padding: 8,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: 'rgba(135, 206, 235, 0.1)',
  },
  emojiPickerContainer: {
    backgroundColor: COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  emojiPickerText: {
    fontSize: 24,
    color: COLORS.text,
    flex: 1,
  },
  emojiPickerClose: {
    padding: 8,
    borderRadius: 16,
    backgroundColor: COLORS.surfaceLight,
  },
});
