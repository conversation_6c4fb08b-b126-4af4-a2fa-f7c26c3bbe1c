// Authentication Utilities - Real Implementation
import {
    clearAuthData,
    getStoredAuthData,
    isAuthenticated,
} from "../services/authStorageSimple";
import { auth } from "../services/firebaseSimple";
import { offlineDatabaseService } from "../services/offlineDatabase";
import { networkStateManager } from "../services/networkStateManager";

// Authentication test results interface
export interface AuthTestResult {
  success: boolean;
  message: string;
  details?: any;
}

// Authentication status interface
export interface AuthStatus {
  isAuthenticated: boolean;
  hasFirebaseUser: boolean;
  hasLocalAuth: boolean;
  user?: any;
}

/**
 * Check current authentication status with offline support
 */
export const checkAuthStatus = async (): Promise<boolean> => {
  try {
    // Check Firebase auth state
    const firebaseUser = auth?.currentUser;
    const localAuth = await isAuthenticated();

    // If offline, rely on local authentication
    if (!networkStateManager.isOnline()) {
      return localAuth;
    }

    return !!(firebaseUser && localAuth);
  } catch (error) {
    return false;
  }
};

/**
 * Get detailed authentication status
 */
export const getDetailedAuthStatus = async (): Promise<AuthStatus> => {
  try {
    const firebaseUser = auth?.currentUser;
    const localAuth = await isAuthenticated();
    const userData = await getCurrentUserData();

    return {
      isAuthenticated: !!(firebaseUser && localAuth),
      hasFirebaseUser: !!firebaseUser,
      hasLocalAuth: localAuth,
      user: userData,
    };
  } catch (error) {
    return {
      isAuthenticated: false,
      hasFirebaseUser: false,
      hasLocalAuth: false,
    };
  }
};

/**
 * Get current user data from storage with offline support
 */
export const getCurrentUserData = async () => {
  try {
    const authData = await getStoredAuthData();
    return authData?.user || null;
  } catch (error) {
    return null;
  }
};

/**
 * Clear all authentication data
 */
export const clearUserSession = async (): Promise<AuthTestResult> => {
  try {
    await clearAuthData();

    // Clear offline auth cache if available
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync('DELETE FROM auth_cache');
    } catch (offlineError) {
      // Offline cache clear failed - continue
    }

    return {
      success: true,
      message: "User session cleared successfully"
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to clear user session",
      details: error
    };
  }
};

/**
 * Validate authentication state with offline support
 */
export const validateAuthState = async (): Promise<{
  isValid: boolean;
  user: any | null;
  message: string;
  isOffline?: boolean;
}> => {
  try {
    const isAuth = await isAuthenticated();
    const userData = await getCurrentUserData();
    const firebaseUser = auth?.currentUser;
    const isOffline = !networkStateManager.isOnline();

    // If offline, only check local authentication
    if (isOffline) {
      if (!isAuth || !userData) {
        return {
          isValid: false,
          user: null,
          message: "Local authentication required",
          isOffline: true
        };
      }

      return {
        isValid: true,
        user: userData,
        message: "Authentication valid (offline mode)",
        isOffline: true
      };
    }

    // Online validation requires both Firebase and local auth
    if (!isAuth || !userData || !firebaseUser) {
      return {
        isValid: false,
        user: null,
        message: "Authentication required",
        isOffline: false
      };
    }

    return {
      isValid: true,
      user: userData,
      message: "Authentication valid",
      isOffline: false
    };
  } catch (error) {
    return {
      isValid: false,
      user: null,
      message: "Authentication error",
      isOffline: !networkStateManager.isOnline()
    };
  }
};

/**
 * Test app launch scenarios
 */
export const testAppLaunchScenarios = async (): Promise<AuthTestResult> => {
  try {
    const authStatus = await checkAuthStatus();
    const detailedStatus = await getDetailedAuthStatus();

    if (authStatus) {
      const userData = await getCurrentUserData();
      return {
        success: true,
        message: "App launch test passed - user authenticated",
        details: {
          authStatus,
          userData: !!userData,
          detailedStatus
        }
      };
    } else {
      return {
        success: true,
        message: "App launch test passed - user not authenticated",
        details: {
          authStatus,
          detailedStatus
        }
      };
    }
  } catch (error) {
    return {
      success: false,
      message: "App launch test failed",
      details: error
    };
  }
};

/**
 * Test authentication persistence with offline support
 */
export const testAuthPersistence = async (): Promise<AuthTestResult> => {
  try {
    const storedData = await getStoredAuthData();
    const firebaseUser = auth?.currentUser;
    const isOffline = !networkStateManager.isOnline();

    // Test offline persistence
    let offlinePersistence = false;
    try {
      const database = offlineDatabaseService.getDatabase();
      const result = await database.getAllAsync('SELECT * FROM auth_cache LIMIT 1');
      offlinePersistence = result.length > 0;
    } catch (offlineError) {
      // Offline persistence not available
    }

    return {
      success: true,
      message: "Authentication persistence test completed",
      details: {
        storedData: !!storedData,
        firebaseUser: !!firebaseUser,
        offlinePersistence,
        isOffline,
        persistenceStatus: {
          local: !!storedData,
          firebase: !!firebaseUser,
          offline: offlinePersistence
        }
      }
    };
  } catch (error) {
    return {
      success: false,
      message: "Authentication persistence test failed",
      details: error
    };
  }
};

/**
 * Test user registration flow
 */
export const testUserRegistration = async (): Promise<AuthTestResult> => {
  try {
    // Import registration services
    const { createUserAccount } = await import("../services/authService");
    const { usernameService } = await import("../services/usernameService");

    // Test data for registration
    const testPhoneNumber = "+**********"; // Test phone number
    const testUserData = {
      name: "Test User",
      username: "testuser_" + Date.now(),
      bio: "Test bio for registration",
      avatar: ""
    };

    // Test username availability
    const usernameCheck = await usernameService.checkUsernameAvailability(testUserData.username);

    // Test phone number validation
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    const isValidPhone = phoneRegex.test(testPhoneNumber);

    // Test registration data validation
    const validations = {
      name: testUserData.name.trim().length >= 2,
      username: testUserData.username.trim().length >= 3,
      phone: isValidPhone
    };

    // Test that createUserAccount function is available
    const isCreateAccountFunction = typeof createUserAccount === 'function';

    if (validations.name && validations.username && validations.phone) {
      return {
        success: true,
        message: "Registration flow test completed successfully",
        details: {
          validations,
          usernameAvailable: usernameCheck.available,
          phoneValid: isValidPhone,
          createAccountFunctionAvailable: isCreateAccountFunction,
          testData: {
            phone: testPhoneNumber,
            name: testUserData.name,
            username: testUserData.username,
            bio: testUserData.bio
          },
          note: "Actual account creation skipped to avoid test data"
        }
      };
    } else {
      return {
        success: false,
        message: "Registration validation failed",
        details: {
          validations,
          usernameAvailable: usernameCheck.available,
          phoneValid: isValidPhone,
          createAccountFunctionAvailable: isCreateAccountFunction
        }
      };
    }

  } catch (error) {
    return {
      success: false,
      message: "Registration test failed",
      details: {
        error: error instanceof Error ? error.message : "Unknown error",
        errorHandling: {
          errorCaught: true,
          errorMessageAvailable: error instanceof Error
        }
      }
    };
  }
};

// Comprehensive authentication test suite
export const runAuthTestSuite = async (): Promise<{
  overall: AuthTestResult;
  tests: {
    authStatus: AuthTestResult;
    persistence: AuthTestResult;
    appLaunch: AuthTestResult;
    registration: AuthTestResult;
  };
}> => {
  try {
    const authStatusTest = await testAppLaunchScenarios();
    const persistenceTest = await testAuthPersistence();
    const appLaunchTest = await testAppLaunchScenarios();
    const registrationTest = await testUserRegistration();

    const allTestsPassed = authStatusTest.success &&
                          persistenceTest.success &&
                          appLaunchTest.success &&
                          registrationTest.success;

    return {
      overall: {
        success: allTestsPassed,
        message: allTestsPassed ? "All authentication tests passed" : "Some authentication tests failed",
        details: {
          totalTests: 4,
          passedTests: [authStatusTest, persistenceTest, appLaunchTest, registrationTest]
            .filter(test => test.success).length
        }
      },
      tests: {
        authStatus: authStatusTest,
        persistence: persistenceTest,
        appLaunch: appLaunchTest,
        registration: registrationTest
      }
    };
  } catch (error) {
    return {
      overall: {
        success: false,
        message: "Authentication test suite failed",
        details: error
      },
      tests: {
        authStatus: { success: false, message: "Not run" },
        persistence: { success: false, message: "Not run" },
        appLaunch: { success: false, message: "Not run" },
        registration: { success: false, message: "Not run" }
      }
    };
  }
};
