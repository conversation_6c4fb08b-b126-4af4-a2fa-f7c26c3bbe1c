/**
 * IraChat Wallpaper Management Service
 * Handles wallpaper storage, caching, and optimization
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as FileSystem from 'expo-file-system';
import { Image } from 'react-native';
import { offlineDatabaseService } from './offlineDatabase';
import { db } from './firebase';
import {
  doc,
  setDoc,
  getDoc,
  collection,
  query,
  where,
  getDocs,
  serverTimestamp,
  updateDoc
} from 'firebase/firestore';

export interface WallpaperConfig {
  type: 'default' | 'gradient' | 'pattern' | 'custom' | 'solid';
  gradientId?: string;
  solidColor?: string;
  patternId?: string;
  customUri?: string;
  customBase64?: string;
  lightModeConfig?: WallpaperConfig;
  darkModeConfig?: WallpaperConfig;
}

export interface ChatWallpaperSettings {
  chatId: string;
  isGroupChat: boolean;
  wallpaperConfig: WallpaperConfig;
  lastUpdated: number;
}

class WallpaperService {
  private static instance: WallpaperService;
  private cache: Map<string, string> = new Map();
  private readonly STORAGE_KEY = 'irachat_wallpapers';
  private readonly CACHE_DIR = `${FileSystem.documentDirectory}wallpapers/`;

  static getInstance(): WallpaperService {
    if (!WallpaperService.instance) {
      WallpaperService.instance = new WallpaperService();
    }
    return WallpaperService.instance;
  }

  constructor() {
    this.initializeCacheDirectory();
  }

  // Initialize cache directory
  private async initializeCacheDirectory() {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.CACHE_DIR);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.CACHE_DIR, { intermediates: true });
      }
    } catch (error) {
      console.error('Failed to initialize wallpaper cache directory:', error);
    }
  }

  // Save wallpaper settings for a specific chat
  async saveWallpaperForChat(chatId: string, isGroupChat: boolean, config: WallpaperConfig): Promise<void> {
    try {
      // Prevent wallpaper functionality for group chats
      if (isGroupChat) {
        console.log('🚫 Wallpaper functionality is disabled for group chats');
        return;
      }
      const settings: ChatWallpaperSettings = {
        chatId,
        isGroupChat,
        wallpaperConfig: config,
        lastUpdated: Date.now(),
      };

      // Get existing settings
      const existingSettings = await this.getAllWallpaperSettings();
      
      // Update or add new settings
      const updatedSettings = existingSettings.filter(s => s.chatId !== chatId);
      updatedSettings.push(settings);

      // Save to AsyncStorage for immediate access
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedSettings));

      // Also save to SQLite for offline persistence (with proper error handling)
      try {
        // Check if offline database service is available and ready
        if (offlineDatabaseService && offlineDatabaseService.isReady()) {
          const db = offlineDatabaseService.getDatabase();

          // Only proceed if database is properly initialized
          if (db) {
            const wallpaperKey = `wallpaper_${chatId}`;
            await db.runAsync(`
              INSERT OR REPLACE INTO app_settings (key, value, type, userId, syncStatus, lastSyncAttempt, retryCount)
              VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
              wallpaperKey,
              JSON.stringify(settings),
              'wallpaper',
              null, // Global setting, not user-specific
              'synced',
              Date.now(),
              0
            ]);

            console.log(`✅ Wallpaper saved to SQLite for chat ${chatId}`);
          }
        } else {
          console.log('📱 SQLite database not ready, wallpaper saved to AsyncStorage only');
        }
      } catch (sqliteError) {
        console.warn('⚠️ Failed to save wallpaper to SQLite:', sqliteError);
        // Continue with AsyncStorage only - this is not a critical error
      }

      // Cache custom wallpaper if needed
      if (config.type === 'custom') {
        if (config.customBase64) {
          await this.cacheCustomWallpaper(chatId, config.customBase64);
        } else if (config.customUri) {
          // Convert URI to base64 and cache it
          try {
            console.log('🎨 Converting custom URI to base64 for caching...');
            const base64 = await FileSystem.readAsStringAsync(config.customUri, {
              encoding: FileSystem.EncodingType.Base64,
            });
            await this.cacheCustomWallpaper(chatId, base64);
            console.log('✅ Custom wallpaper cached successfully');
          } catch (cacheError) {
            console.warn('⚠️ Failed to cache custom wallpaper:', cacheError);
            // Don't throw error, just continue without caching
          }
        }
      }

      // Note: Wallpapers are kept local only as per user requirements
      // No Firebase/Firestore sync for wallpapers

      console.log(`Wallpaper saved locally for chat ${chatId}`);
    } catch (error) {
      console.error('Failed to save wallpaper settings:', error);
      throw error;
    }
  }

  // Get wallpaper settings for a specific chat
  async getWallpaperForChat(chatId: string, isGroupChat: boolean = false): Promise<WallpaperConfig | null> {
    try {
      // Prevent wallpaper functionality for group chats
      if (isGroupChat) {
        console.log('🚫 Wallpaper functionality is disabled for group chats');
        return null;
      }
      // First try AsyncStorage
      const allSettings = await this.getAllWallpaperSettings();
      let chatSettings = allSettings.find(s => s.chatId === chatId);

      // If not found in AsyncStorage, try SQLite (with proper error handling)
      if (!chatSettings) {
        try {
          // Check if offline database service is available and ready
          if (offlineDatabaseService && offlineDatabaseService.isReady()) {
            const db = offlineDatabaseService.getDatabase();

            // Only proceed if database is properly initialized
            if (db) {
              const wallpaperKey = `wallpaper_${chatId}`;
              const result = await db.getFirstAsync<{ value: string }>(`
                SELECT value FROM app_settings
                WHERE key = ? AND type = 'wallpaper'
              `, [wallpaperKey]);

              if (result?.value) {
                chatSettings = JSON.parse(result.value);
                console.log(`✅ Wallpaper loaded from SQLite for chat ${chatId}`);
              }
            }
          } else {
            console.log('📱 SQLite database not ready, skipping wallpaper load from SQLite');
          }
        } catch (sqliteError) {
          console.warn('⚠️ Failed to load wallpaper from SQLite:', sqliteError);
          // Continue without SQLite wallpaper - this is not a critical error
        }
      }

      if (chatSettings) {
        // Check if custom wallpaper is cached and validate file existence
        if (chatSettings.wallpaperConfig.type === 'custom') {
          const cachedUri = await this.getCachedWallpaperUri(chatId);
          if (cachedUri) {
            // Validate that the file actually exists
            try {
              const fileInfo = await FileSystem.getInfoAsync(cachedUri);
              if (fileInfo.exists) {
                console.log(`✅ Custom wallpaper file validated for chat ${chatId}`);

                // Try to load base64 data from the cached file
                try {
                  const base64Data = await FileSystem.readAsStringAsync(cachedUri, {
                    encoding: FileSystem.EncodingType.Base64,
                  });

                  console.log(`🎨 Loaded base64 data for chat ${chatId} (${base64Data.length} chars)`);

                  return {
                    ...chatSettings.wallpaperConfig,
                    customUri: cachedUri, // Keep URI as fallback
                    customBase64: base64Data, // Provide base64 for reliable display
                  };
                } catch (base64Error) {
                  console.warn(`⚠️ Failed to read base64 from cached file for chat ${chatId}:`, base64Error);
                  // Fallback to URI only
                  return {
                    ...chatSettings.wallpaperConfig,
                    customUri: cachedUri,
                  };
                }
              } else {
                console.warn(`⚠️ Custom wallpaper file missing for chat ${chatId}, removing from cache and storage`);
                // Remove from cache since file doesn't exist
                this.cache.delete(chatId);
                // Also remove from AsyncStorage to prevent future attempts
                await this.clearWallpaperForChat(chatId);
                // Return default wallpaper instead
                return { type: 'default' };
              }
            } catch (error) {
              console.error(`❌ Error validating custom wallpaper file for chat ${chatId}:`, error);
              // Remove from cache on error
              this.cache.delete(chatId);
              // Return default wallpaper instead
              return { type: 'default' };
            }
          } else {
            console.warn(`⚠️ Custom wallpaper config found but no cached file for chat ${chatId}`);
            // Return default wallpaper if no cached file
            return { type: 'default' };
          }
        }

        return chatSettings.wallpaperConfig;
      }

      // No Firestore fallback - wallpapers are local only
      // Return default dark theme wallpaper if nothing found
      console.log(`📱 No wallpaper found for chat ${chatId}, using default dark theme`);
      return {
        type: 'default',
        // Default dark theme wallpaper
      };

      return null;
    } catch (error) {
      console.error('Failed to get wallpaper settings:', error);
      return null;
    }
  }

  // Get all wallpaper settings
  private async getAllWallpaperSettings(): Promise<ChatWallpaperSettings[]> {
    try {
      const settingsJson = await AsyncStorage.getItem(this.STORAGE_KEY);
      return settingsJson ? JSON.parse(settingsJson) : [];
    } catch (error) {
      console.error('Failed to get wallpaper settings:', error);
      return [];
    }
  }

  // Cache custom wallpaper image
  private async cacheCustomWallpaper(chatId: string, base64Data: string): Promise<string> {
    try {
      const fileName = `wallpaper_${chatId}_${Date.now()}.jpg`;
      const filePath = `${this.CACHE_DIR}${fileName}`;

      // Write base64 data to file
      await FileSystem.writeAsStringAsync(filePath, base64Data, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Update cache map
      this.cache.set(chatId, filePath);

      return filePath;
    } catch (error) {
      console.error('Failed to cache wallpaper:', error);
      throw error;
    }
  }

  // Get cached wallpaper URI
  private async getCachedWallpaperUri(chatId: string): Promise<string | null> {
    try {
      // Check memory cache first
      if (this.cache.has(chatId)) {
        const cachedPath = this.cache.get(chatId)!;
        const fileInfo = await FileSystem.getInfoAsync(cachedPath);
        if (fileInfo.exists) {
          return cachedPath;
        } else {
          this.cache.delete(chatId);
        }
      }

      // Check file system
      const files = await FileSystem.readDirectoryAsync(this.CACHE_DIR);
      const wallpaperFile = files.find(file => file.startsWith(`wallpaper_${chatId}_`));
      
      if (wallpaperFile) {
        const filePath = `${this.CACHE_DIR}${wallpaperFile}`;
        this.cache.set(chatId, filePath);
        return filePath;
      }

      return null;
    } catch (error) {
      console.error('Failed to get cached wallpaper:', error);
      return null;
    }
  }

  // Optimize image for wallpaper use
  async optimizeWallpaperImage(uri: string, quality: number = 0.8): Promise<string> {
    try {
      // Get image dimensions
      const { width, height } = await this.getImageDimensions(uri);
      
      // Calculate optimal dimensions (max 1080p for performance)
      const maxWidth = 1080;
      const maxHeight = 1920;
      
      let newWidth = width;
      let newHeight = height;
      
      if (width > maxWidth || height > maxHeight) {
        const aspectRatio = width / height;
        
        if (aspectRatio > maxWidth / maxHeight) {
          newWidth = maxWidth;
          newHeight = maxWidth / aspectRatio;
        } else {
          newHeight = maxHeight;
          newWidth = maxHeight * aspectRatio;
        }
      }

      // For now, return original URI (optimization can be added with image manipulation library)
      console.log(`Optimized wallpaper dimensions: ${newWidth}x${newHeight} with quality: ${quality}`);
      return uri;
    } catch (error) {
      console.error('Failed to optimize wallpaper image:', error);
      return uri;
    }
  }

  // Get image dimensions
  private getImageDimensions(uri: string): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      Image.getSize(
        uri,
        (width, height) => resolve({ width, height }),
        (error) => reject(error)
      );
    });
  }

  // Clear wallpaper for a specific chat
  async clearWallpaperForChat(chatId: string, isGroupChat: boolean = false): Promise<void> {
    try {
      // Prevent wallpaper functionality for group chats (unless it's internal cleanup)
      if (isGroupChat) {
        console.log('🚫 Wallpaper functionality is disabled for group chats');
        return;
      }
      const allSettings = await this.getAllWallpaperSettings();
      const updatedSettings = allSettings.filter(s => s.chatId !== chatId);
      
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(updatedSettings));

      // Also remove from SQLite (with proper error handling)
      try {
        // Check if offline database service is available and ready
        if (offlineDatabaseService && offlineDatabaseService.isReady()) {
          const db = offlineDatabaseService.getDatabase();

          // Only proceed if database is properly initialized
          if (db) {
            const wallpaperKey = `wallpaper_${chatId}`;
            await db.runAsync(`
              DELETE FROM app_settings
              WHERE key = ? AND type = 'wallpaper'
            `, [wallpaperKey]);

            console.log(`✅ Wallpaper cleared from SQLite for chat ${chatId}`);
          }
        } else {
          console.log('📱 SQLite database not ready, wallpaper cleared from AsyncStorage only');
        }
      } catch (sqliteError) {
        console.warn('⚠️ Failed to clear wallpaper from SQLite:', sqliteError);
        // Continue without SQLite clear - AsyncStorage clear was successful
      }

      // Remove cached file
      await this.removeCachedWallpaper(chatId);

      console.log(`Wallpaper cleared for chat ${chatId}`);
    } catch (error) {
      console.error('Failed to clear wallpaper:', error);
      throw error;
    }
  }

  // Remove cached wallpaper file
  private async removeCachedWallpaper(chatId: string): Promise<void> {
    try {
      const cachedPath = this.cache.get(chatId);
      if (cachedPath) {
        const fileInfo = await FileSystem.getInfoAsync(cachedPath);
        if (fileInfo.exists) {
          await FileSystem.deleteAsync(cachedPath);
        }
        this.cache.delete(chatId);
      }

      // Also check file system for any remaining files
      const files = await FileSystem.readDirectoryAsync(this.CACHE_DIR);
      const wallpaperFiles = files.filter(file => file.startsWith(`wallpaper_${chatId}_`));
      
      for (const file of wallpaperFiles) {
        await FileSystem.deleteAsync(`${this.CACHE_DIR}${file}`);
      }
    } catch (error) {
      console.error('Failed to remove cached wallpaper:', error);
    }
  }

  // Clean up old cached wallpapers (call periodically)
  async cleanupOldWallpapers(maxAgeMs: number = 30 * 24 * 60 * 60 * 1000): Promise<void> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.CACHE_DIR);
      const now = Date.now();
      
      for (const file of files) {
        const filePath = `${this.CACHE_DIR}${file}`;
        const fileInfo = await FileSystem.getInfoAsync(filePath);
        
        if (fileInfo.exists && fileInfo.modificationTime) {
          const fileAge = now - fileInfo.modificationTime * 1000;
          
          if (fileAge > maxAgeMs) {
            await FileSystem.deleteAsync(filePath);
            console.log(`Cleaned up old wallpaper: ${file}`);
          }
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old wallpapers:', error);
    }
  }

  // Get cache size
  async getCacheSize(): Promise<number> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.CACHE_DIR);
      let totalSize = 0;
      
      for (const file of files) {
        const filePath = `${this.CACHE_DIR}${file}`;
        const fileInfo = await FileSystem.getInfoAsync(filePath);
        
        if (fileInfo.exists && fileInfo.size) {
          totalSize += fileInfo.size;
        }
      }
      
      return totalSize;
    } catch (error) {
      console.error('Failed to get cache size:', error);
      return 0;
    }
  }

  // Clear all cached wallpapers
  async clearAllCache(): Promise<void> {
    try {
      const files = await FileSystem.readDirectoryAsync(this.CACHE_DIR);
      
      for (const file of files) {
        await FileSystem.deleteAsync(`${this.CACHE_DIR}${file}`);
      }
      
      this.cache.clear();
      console.log('All wallpaper cache cleared');
    } catch (error) {
      console.error('Failed to clear wallpaper cache:', error);
      throw error;
    }
  }

  // Save wallpaper to Firestore wallpapers collection
  private async saveWallpaperToFirestore(chatId: string, settings: ChatWallpaperSettings): Promise<void> {
    try {
      const wallpaperRef = doc(db, 'wallpapers', chatId);
      await setDoc(wallpaperRef, {
        ...settings,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Wallpaper saved to Firestore wallpapers collection');
    } catch (error) {
      console.error('❌ Failed to save wallpaper to Firestore:', error);
      // Don't throw error - wallpaper functionality should work even if Firestore fails
    }
  }

  // Load wallpaper from Firestore wallpapers collection
  private async loadWallpaperFromFirestore(chatId: string): Promise<ChatWallpaperSettings | null> {
    try {
      const wallpaperDoc = await getDoc(doc(db, 'wallpapers', chatId));

      if (!wallpaperDoc.exists()) {
        return null;
      }

      const data = wallpaperDoc.data();
      return {
        chatId: data.chatId,
        isGroupChat: data.isGroupChat,
        wallpaperConfig: data.wallpaperConfig,
        lastUpdated: data.lastUpdated,
      };
    } catch (error) {
      console.error('Failed to load wallpaper from Firestore:', error);
      return null;
    }
  }

  // Get user's custom wallpapers from Firestore
  async getUserCustomWallpapers(userId: string): Promise<ChatWallpaperSettings[]> {
    try {
      const wallpapersRef = collection(db, 'wallpapers');
      const customWallpapersQuery = query(
        wallpapersRef,
        where('wallpaperConfig.type', '==', 'custom'),
        where('userId', '==', userId)
      );

      const snapshot = await getDocs(customWallpapersQuery);
      const wallpapers: ChatWallpaperSettings[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        wallpapers.push({
          chatId: data.chatId,
          isGroupChat: data.isGroupChat,
          wallpaperConfig: data.wallpaperConfig,
          lastUpdated: data.lastUpdated,
        });
      });

      return wallpapers;
    } catch (error) {
      console.error('Failed to get user custom wallpapers:', error);
      return [];
    }
  }

  // Update wallpaper settings
  async updateWallpaperSettings(chatId: string, updates: Partial<ChatWallpaperSettings>): Promise<void> {
    try {
      const wallpaperRef = doc(db, 'wallpapers', chatId);
      await updateDoc(wallpaperRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Wallpaper settings updated');
    } catch (error) {
      console.error('Failed to update wallpaper settings:', error);
      throw error;
    }
  }

  // Validate and clean up invalid wallpapers
  async validateAndCleanupWallpapers(): Promise<void> {
    try {
      console.log('🎨 Starting wallpaper validation and cleanup...');

      // Get all wallpapers from storage
      const allSettings = await this.getAllWallpaperSettings();
      const invalidChatIds: string[] = [];

      // Check each custom wallpaper
      for (const settings of allSettings) {
        if (settings.wallpaperConfig.type === 'custom') {
          const cachedUri = await this.getCachedWallpaperUri(settings.chatId);

          if (cachedUri) {
            try {
              const fileInfo = await FileSystem.getInfoAsync(cachedUri);
              if (!fileInfo.exists) {
                console.warn(`⚠️ Invalid wallpaper file for chat ${settings.chatId}: ${cachedUri}`);
                invalidChatIds.push(settings.chatId);
              }
            } catch (error) {
              console.warn(`⚠️ Error checking wallpaper file for chat ${settings.chatId}:`, error);
              invalidChatIds.push(settings.chatId);
            }
          } else {
            console.warn(`⚠️ No cached file found for custom wallpaper in chat ${settings.chatId}`);
            invalidChatIds.push(settings.chatId);
          }
        }
      }

      // Clean up invalid wallpapers
      for (const chatId of invalidChatIds) {
        console.log(`🧹 Cleaning up invalid wallpaper for chat ${chatId}`);
        await this.clearWallpaperForChat(chatId);
      }

      console.log(`🎨 Wallpaper validation complete. Cleaned up ${invalidChatIds.length} invalid wallpapers`);
    } catch (error) {
      console.error('❌ Error during wallpaper validation:', error);
    }
  }
}

export const wallpaperService = WallpaperService.getInstance();
export default wallpaperService;
