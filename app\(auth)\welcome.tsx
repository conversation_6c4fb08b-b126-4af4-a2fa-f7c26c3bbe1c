import { Image, Text, View, StyleSheet, Animated, TouchableOpacity } from "react-native";
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { markAppLaunched } from "../../src/services/authStorageSimple";
import { IraChatWallpaper } from "../../src/components/ui/IraChatWallpaper";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from "../../src/styles/iraChatDesignSystem";
import { useEffect, useRef, useState } from "react";
import { SafeAreaView } from 'react-native-safe-area-context';




export default function WelcomeScreen() {
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const scaleAnimation = useRef(new Animated.Value(0.8)).current;
  const [isNavigating, setIsNavigating] = useState(false);

  useEffect(() => {
    // Beautiful entrance animations
    Animated.sequence([
      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: ANIMATIONS.slow,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnimation, {
          toValue: 0,
          duration: ANIMATIONS.slow,
          useNativeDriver: true,
        }),
      ]),
      Animated.spring(scaleAnimation, {
        toValue: 1,
        tension: 50,
        friction: 7,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnimation, scaleAnimation, slideAnimation]);

  const handleCreateAccount = async () => {
    // Prevent double navigation
    if (isNavigating) {
      console.log('🔐 Welcome: Navigation already in progress, ignoring duplicate call');
      return;
    }

    try {
      setIsNavigating(true);
      await markAppLaunched();
      console.log('🔐 Welcome: Attempting to navigate to phone register');

      // Add a small delay to ensure component is ready
      setTimeout(() => {
        try {
          console.log('🔐 Welcome: Executing navigation to register info screen');

          // Navigate to register info screen (explains phone registration requirement)
          router.push('/(auth)/register-info');
          console.log('✅ Welcome: Navigation to register-info successful');
        } catch (navError) {
          console.error('❌ Welcome: Navigation error:', navError);

          // Try alternative approaches
          try {
            console.log('🔐 Welcome: Trying router.replace to register-info');
            router.replace('/(auth)/register-info');
          } catch (replaceError) {
            console.error('❌ Welcome: Replace navigation failed:', replaceError);
          }
        }

        // Reset navigation state
        setTimeout(() => {
          setIsNavigating(false);
        }, 1000);
      }, 100);

    } catch (error) {
      console.error('❌ Welcome: Setup error:', error);
      setIsNavigating(false);
    }
  };



  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#000000" />

      {/* Beautiful animated wallpaper */}
      <IraChatWallpaper variant="light" animated={true} />

      <SafeAreaView style={styles.safeArea}>
        {/* Content */}
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnimation,
              transform: [
                { translateY: slideAnimation },
                { scale: scaleAnimation }
              ],
            },
          ]}
        >
        {/* Logo with glow effect */}
        <View style={styles.logoContainer}>
          <View style={styles.logoGlow}>
            <Image
              source={require("../../assets/images/LOGO.png")}
              style={styles.logo}
              resizeMode="cover"
            />
          </View>
        </View>

        {/* Welcome Text with gradient */}
        <View style={styles.textContainer}>
          <Text style={styles.welcomeTitle}>
            Welcome to IraChat
          </Text>
          <Text style={styles.welcomeSubtitle}>
            Connect with friends and family
          </Text>
        </View>

        {/* Feature highlights */}
        <View style={styles.featuresContainer}>
          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>💬</Text>
            </View>
            <Text style={styles.featureText}>Instant Messaging</Text>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>📞</Text>
            </View>
            <Text style={styles.featureText}>Voice & Video Calls</Text>
          </View>

          <View style={styles.featureItem}>
            <View style={styles.featureIcon}>
              <Text style={styles.featureEmoji}>👥</Text>
            </View>
            <Text style={styles.featureText}>Group Chats</Text>
          </View>
        </View>



        {/* Sign In Option for Existing Users */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.signInButton, isNavigating && styles.disabledButton]}
            onPress={() => router.push('/(auth)/sign-in')}
            disabled={isNavigating}
          >
            <Ionicons name="log-in" size={20} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.buttonText}>
              {isNavigating ? "Loading..." : "Sign In"}
            </Text>
          </TouchableOpacity>

          <View style={styles.dividerContainer}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>or create new account</Text>
            <View style={styles.dividerLine} />
          </View>

          <TouchableOpacity
            style={[styles.createAccountButton, isNavigating && styles.disabledButton]}
            onPress={handleCreateAccount}
            disabled={isNavigating}
          >
            <Ionicons name="call" size={20} color={IRACHAT_COLORS.primary} style={styles.buttonIcon} />
            <Text style={styles.createAccountButtonText}>
              {isNavigating ? "Loading..." : "Continue with Phone Number"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.createAccountButton, isNavigating && styles.disabledButton]}
            onPress={() => router.push('/(auth)/email-register-info')}
            disabled={isNavigating}
          >
            <Ionicons name="mail" size={20} color={IRACHAT_COLORS.primary} style={styles.buttonIcon} />
            <Text style={styles.createAccountButtonText}>
              Continue with Email
            </Text>
          </TouchableOpacity>          

          <Text style={styles.termsText}>
            By continuing, you agree to our Terms of Service and Privacy Policy
          </Text>
        </View>
        </Animated.View>
      </SafeAreaView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  logoContainer: {
    marginBottom: SPACING.xl,
    alignItems: 'center',
  },
  logoGlow: {
    ...SHADOWS.xl,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: IRACHAT_COLORS.surface,
    padding: SPACING.sm,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  welcomeTitle: {
    fontSize: TYPOGRAPHY.fontSize['4xl'],
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    marginBottom: SPACING.md,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
    letterSpacing: 1,
  },
  welcomeSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    lineHeight: TYPOGRAPHY.lineHeight.relaxed * TYPOGRAPHY.fontSize.lg,
    opacity: 0.95,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    paddingHorizontal: SPACING.md,
  },
  featuresContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: SPACING.xl,
    paddingHorizontal: SPACING.md,
  },
  featureItem: {
    alignItems: 'center',
    flex: 1,
  },
  featureIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: IRACHAT_COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: SPACING.sm,
    ...SHADOWS.md,
  },
  featureEmoji: {
    fontSize: 24,
  },
  featureText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    opacity: 0.9,
  },

  buttonContainer: {
    width: '100%',
    alignItems: 'center',
  },
  signInButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: BORDER_RADIUS.lg,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    minHeight: 50,
  },
  createAccountButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: BORDER_RADIUS.lg,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
    minHeight: 50,
  },
  buttonIcon: {
    marginRight: SPACING.sm,
  },
  buttonText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: '#FFFFFF',
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  createAccountButtonText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.primary,
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  disabledButton: {
    opacity: 0.6,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginVertical: SPACING.md,
    paddingHorizontal: SPACING.md,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  dividerText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    marginHorizontal: SPACING.md,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },

  termsText: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: IRACHAT_COLORS.textOnPrimary,
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: TYPOGRAPHY.lineHeight.normal * TYPOGRAPHY.fontSize.xs,
    paddingHorizontal: SPACING.lg,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
    backgroundColor: IRACHAT_COLORS.surface,
  },
  modalTitle: {
    fontSize: TYPOGRAPHY.fontSize.xl,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: IRACHAT_COLORS.text,
  },
  closeButton: {
    padding: SPACING.sm,
    borderRadius: BORDER_RADIUS.full,
    backgroundColor: IRACHAT_COLORS.backgroundDark,
  },
});
