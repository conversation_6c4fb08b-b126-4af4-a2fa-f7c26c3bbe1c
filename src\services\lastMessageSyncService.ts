/**
 * Last Message Sync Service
 * Ensures chat last messages are properly updated and displayed
 */

import { doc, updateDoc, serverTimestamp, collection, query, orderBy, limit, getDocs } from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { memoryCacheService } from './memoryCache';

interface LastMessageInfo {
  content: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  type: 'text' | 'image' | 'video' | 'audio' | 'file';
}

interface ChatLastMessage {
  chatId: string;
  lastMessage: LastMessageInfo;
}

class LastMessageSyncService {
  private static instance: LastMessageSyncService;
  private updateQueue = new Map<string, LastMessageInfo>();
  private isProcessing = false;

  static getInstance(): LastMessageSyncService {
    if (!LastMessageSyncService.instance) {
      LastMessageSyncService.instance = new LastMessageSyncService();
    }
    return LastMessageSyncService.instance;
  }

  /**
   * Update last message for a chat
   */
  async updateChatLastMessage(
    chatId: string,
    content: string,
    senderId: string,
    senderName: string,
    messageType: 'text' | 'image' | 'video' | 'audio' | 'file' = 'text'
  ): Promise<void> {
    const lastMessage: LastMessageInfo = {
      content: this.formatMessageContent(content, messageType),
      senderId,
      senderName,
      timestamp: new Date(),
      type: messageType,
    };

    // Add to queue for batch processing
    this.updateQueue.set(chatId, lastMessage);

    // Process queue
    this.processUpdateQueue();

    // Update local cache immediately for instant UI updates
    this.updateLocalCache(chatId, lastMessage);
  }

  /**
   * Sync all chat last messages from their actual latest messages
   */
  async syncAllChatLastMessages(userId: string): Promise<void> {
    try {
      console.log('🔄 Syncing all chat last messages...');

      // Get user's chats
      const userChats = await this.getUserChats(userId);
      console.log(`📱 Found ${userChats.length} chats to sync`);

      // Process in batches to avoid overwhelming
      const batchSize = 10;
      for (let i = 0; i < userChats.length; i += batchSize) {
        const batch = userChats.slice(i, i + batchSize);
        await Promise.all(
          batch.map(chatId => this.syncChatLastMessage(chatId))
        );
      }

      console.log('✅ All chat last messages synced');
    } catch (error) {
      console.error('❌ Error syncing chat last messages:', error);
    }
  }

  /**
   * Sync last message for a specific chat
   */
  async syncChatLastMessage(chatId: string): Promise<void> {
    try {
      // Get the actual latest message from the chat
      const latestMessage = await this.getLatestMessage(chatId);
      
      if (latestMessage) {
        // Update the chat's last message
        await this.updateChatDocument(chatId, latestMessage);
        
        // Update local cache
        this.updateLocalCache(chatId, latestMessage);
        
        console.log(`✅ Synced last message for chat: ${chatId}`);
      }
    } catch (error) {
      console.error(`❌ Error syncing last message for chat ${chatId}:`, error);
    }
  }

  /**
   * Get cached last message for instant display
   */
  getCachedLastMessage(chatId: string): LastMessageInfo | null {
    const cacheKey = `last_message_${chatId}`;
    return memoryCacheService.getSettings(cacheKey) as LastMessageInfo || null;
  }

  /**
   * Process the update queue in batches
   */
  private async processUpdateQueue(): Promise<void> {
    if (this.isProcessing || this.updateQueue.size === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // Process all queued updates
      const updates = Array.from(this.updateQueue.entries());
      this.updateQueue.clear();

      // Update Firebase in parallel
      await Promise.all(
        updates.map(([chatId, lastMessage]) => 
          this.updateChatDocument(chatId, lastMessage)
        )
      );

      console.log(`✅ Processed ${updates.length} last message updates`);
    } catch (error) {
      console.error('❌ Error processing last message updates:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Update chat document in Firebase
   */
  private async updateChatDocument(chatId: string, lastMessage: LastMessageInfo): Promise<void> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        lastMessage: {
          content: lastMessage.content,
          senderId: lastMessage.senderId,
          senderName: lastMessage.senderName,
          timestamp: lastMessage.timestamp,
          type: lastMessage.type,
        },
        lastMessageTime: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error(`❌ Error updating chat document ${chatId}:`, error);
    }
  }

  /**
   * Update local cache for instant UI updates
   */
  private updateLocalCache(chatId: string, lastMessage: LastMessageInfo): void {
    const cacheKey = `last_message_${chatId}`;
    memoryCacheService.setSettings(cacheKey, lastMessage);

    // Also update offline database
    this.updateOfflineDatabase(chatId, lastMessage);
  }

  /**
   * Update offline database
   */
  private async updateOfflineDatabase(chatId: string, lastMessage: LastMessageInfo): Promise<void> {
    try {
      if (!offlineDatabaseService.isReady()) return;

      const db = offlineDatabaseService.getDatabase();
      await db.runAsync(`
        UPDATE chats 
        SET 
          lastMessage = ?,
          lastMessageSender = ?,
          lastMessageTime = ?,
          updatedAt = ?
        WHERE id = ?
      `, [
        lastMessage.content,
        lastMessage.senderName,
        lastMessage.timestamp.getTime(),
        Date.now(),
        chatId
      ]);
    } catch (error) {
      console.error('❌ Error updating offline database:', error);
    }
  }

  /**
   * Get user's chats
   */
  private async getUserChats(userId: string): Promise<string[]> {
    try {
      if (!offlineDatabaseService.isReady()) return [];

      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT DISTINCT id FROM chats 
        WHERE participants LIKE '%${userId}%'
        ORDER BY updatedAt DESC
      `);

      return result.map((row: any) => row.id);
    } catch (error) {
      console.error('❌ Error getting user chats:', error);
      return [];
    }
  }

  /**
   * Get the latest message from a chat
   */
  private async getLatestMessage(chatId: string): Promise<LastMessageInfo | null> {
    try {
      // Try Firebase first
      const messagesRef = collection(db, `chats/${chatId}/messages`);
      const q = query(messagesRef, orderBy('timestamp', 'desc'), limit(1));
      const snapshot = await getDocs(q);

      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        const data = doc.data();
        
        return {
          content: this.formatMessageContent(data.content || data.text || '', data.type || 'text'),
          senderId: data.senderId || '',
          senderName: data.senderName || '',
          timestamp: data.timestamp?.toDate() || new Date(),
          type: data.type || 'text',
        };
      }

      // Fallback to offline database
      return this.getLatestMessageFromOffline(chatId);
    } catch (error) {
      console.error(`❌ Error getting latest message for chat ${chatId}:`, error);
      return this.getLatestMessageFromOffline(chatId);
    }
  }

  /**
   * Get latest message from offline database
   */
  private async getLatestMessageFromOffline(chatId: string): Promise<LastMessageInfo | null> {
    try {
      if (!offlineDatabaseService.isReady()) return null;

      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT * FROM messages
        WHERE chatId = ? AND isDeleted = 0
        ORDER BY timestamp DESC
        LIMIT 1
      `, [chatId]);

      if (result.length > 0) {
        const row = result[0] as any;
        return {
          content: this.formatMessageContent(row.content || row.text || '', row.type || 'text'),
          senderId: row.senderId || '',
          senderName: row.senderName || '',
          timestamp: new Date(row.timestamp),
          type: row.type || 'text',
        };
      }

      return null;
    } catch (error) {
      console.error('❌ Error getting latest message from offline:', error);
      return null;
    }
  }

  /**
   * Format message content for display
   */
  private formatMessageContent(content: string, type: string): string {
    if (type === 'text') {
      return content || 'Message';
    }

    // Format media messages
    switch (type) {
      case 'image':
        return '📸 Photo';
      case 'video':
        return '🎥 Video';
      case 'audio':
        return '🎵 Audio';
      case 'file':
        return '📄 Document';
      default:
        return content || 'Message';
    }
  }
}

export const lastMessageSyncService = LastMessageSyncService.getInstance();
