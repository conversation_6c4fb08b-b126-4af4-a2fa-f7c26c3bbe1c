import React, { useState, useCallback, useMemo, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Alert,
  Linking,
  ActivityIndicator,
  InteractionManager,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants/theme';
import { useInteractionManager, useOptimizedState } from '../../utils/performance';
import { BusinessPost } from '../../types/Business';
import { LazyMediaGrid } from './LazyMediaGrid';

interface OptimizedProductDetailModalProps {
  visible: boolean;
  post: BusinessPost | null;
  onClose: () => void;
  onMediaPress: (mediaIndex: number) => void;
  onEditPress?: () => void;
  onDeletePress?: () => void;
  onDeleteMedia?: (mediaId: string) => void;
  onPriceReductionPress?: () => void;
  isOwner?: boolean;
  modalViewCount: number;
  modalLikeCount: number;
  modalShareCount: number;
  modalIsLiked: boolean;
  onLikePress: () => void;
  onSharePress: () => void;
  onBusinessNamePress?: (post: BusinessPost) => void;
}

export const OptimizedProductDetailModal: React.FC<OptimizedProductDetailModalProps> = ({
  visible,
  post,
  onClose,
  onMediaPress,
  onEditPress,
  onDeletePress,
  onDeleteMedia,
  onPriceReductionPress,
  isOwner = false,
  modalViewCount,
  modalLikeCount,
  modalShareCount,
  modalIsLiked,
  onLikePress,
  onSharePress,
  onBusinessNamePress,
}) => {
  const [isContentReady, setIsContentReady] = useOptimizedState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const { runAfterInteractions } = useInteractionManager();

  // Prepare content after modal opens to avoid blocking the opening animation
  useEffect(() => {
    if (visible && post) {
      const startTime = Date.now();
      const cleanup = runAfterInteractions(() => {
        const loadTime = Date.now() - startTime;
        console.log(`⚡ Optimized modal content loaded in ${loadTime}ms`);
        setIsContentReady(true);
      });
      return cleanup;
    } else {
      setIsContentReady(false);
    }
  }, [visible, post, runAfterInteractions, setIsContentReady]);

  // Memoized header component
  const HeaderComponent = useMemo(() => (
    <View style={styles.header}>
      <TouchableOpacity onPress={onClose} style={styles.closeButton}>
        <Ionicons name="close" size={24} color={COLORS.text} />
      </TouchableOpacity>
      <Text style={styles.title}>Product Details</Text>
      <View style={styles.headerActions}>
        {/* Call Button */}
        {post?.contact?.phone && (
          <TouchableOpacity
            onPress={() => {
              const phoneNumber = post.contact?.phone?.replace(/[^\d+]/g, '');
              if (phoneNumber) {
                Alert.alert(
                  'Call Business',
                  `Do you want to call ${post.businessName}?\n\n${post.contact?.phone}`,
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Call Now',
                      onPress: () => {
                        const phoneUrl = `tel:${phoneNumber}`;
                        Linking.openURL(phoneUrl).catch(() => {
                          Alert.alert('Error', 'Unable to make phone call');
                        });
                      },
                    },
                  ]
                );
              }
            }}
            style={styles.actionButton}
          >
            <Ionicons name="call" size={24} color={COLORS.success} />
          </TouchableOpacity>
        )}
        
        {/* Owner Actions */}
        {isOwner && (
          <>
            <TouchableOpacity onPress={onEditPress} style={styles.actionButton}>
              <Ionicons name="create-outline" size={24} color={COLORS.primary} />
            </TouchableOpacity>
            <TouchableOpacity onPress={onDeletePress} style={styles.actionButton}>
              <Ionicons name="trash-outline" size={24} color={COLORS.error} />
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  ), [post, onClose, onEditPress, onDeletePress, isOwner]);

  // Memoized basic info component
  const BasicInfoComponent = useMemo(() => {
    if (!post) return null;
    
    return (
      <View style={styles.basicInfo}>
        <TouchableOpacity onPress={() => onBusinessNamePress?.(post)}>
          <Text style={[styles.businessName, styles.clickableBusinessName]}>{post.businessName}</Text>
        </TouchableOpacity>
        <Text style={styles.productTitle}>{post.title}</Text>
        <View style={styles.priceContainer}>
          <View style={styles.priceRow}>
            {post.oldPrice && (
              <Text style={styles.oldPriceStrikethrough}>
                {post.currency} {typeof post.oldPrice === 'number' ? post.oldPrice.toLocaleString() : post.oldPrice}
              </Text>
            )}
            <Text style={[styles.price, post.oldPrice ? styles.newPrice : null]}>
              {post.currency} {typeof post.price === 'number' ? post.price.toLocaleString() : post.price}
            </Text>
            {post.isNegotiable && (
              <Text style={styles.negotiable}>Negotiable</Text>
            )}
          </View>
          {isOwner && onPriceReductionPress && (
            <TouchableOpacity
              style={styles.reducePriceButton}
              onPress={onPriceReductionPress}
            >
              <Ionicons name="trending-up-outline" size={16} color={COLORS.primary} />
              <Text style={styles.reducePriceText}>Adjust</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }, [post]);

  // Memoized stats component
  const StatsComponent = useMemo(() => (
    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Ionicons name="eye-outline" size={16} color={COLORS.textSecondary} />
        <Text style={styles.statText}>{modalViewCount}</Text>
      </View>
      <TouchableOpacity onPress={onLikePress} style={styles.statItem}>
        <Ionicons 
          name={modalIsLiked ? "heart" : "heart-outline"} 
          size={16} 
          color={modalIsLiked ? COLORS.error : COLORS.textSecondary} 
        />
        <Text style={styles.statText}>{modalLikeCount}</Text>
      </TouchableOpacity>
      <TouchableOpacity onPress={onSharePress} style={styles.statItem}>
        <Ionicons name="share-outline" size={16} color={COLORS.textSecondary} />
        <Text style={styles.statText}>{modalShareCount}</Text>
      </TouchableOpacity>
    </View>
  ), [modalViewCount, modalLikeCount, modalShareCount, modalIsLiked, onLikePress, onSharePress]);

  const handleClose = useCallback(() => {
    setIsContentReady(false);
    onClose();
  }, [onClose, setIsContentReady]);

  // Contact action handlers
  const handleContactAction = useCallback(async (action: 'message' | 'phone' | 'whatsapp' | 'email') => {
    if (!post) return;

    try {
      switch (action) {
        case 'message':
          // Navigate to IraChat individual chat room
          await handleIraChatMessage(post);
          break;

        case 'phone':
          if (post.contact?.phone) {
            const phoneUrl = `tel:${post.contact.phone}`;
            const supported = await Linking.canOpenURL(phoneUrl);
            if (supported) {
              await Linking.openURL(phoneUrl);
            } else {
              Alert.alert('Error', 'Phone calls are not supported on this device.');
            }
          } else {
            Alert.alert('No Phone Number', 'This seller has not provided a phone number.');
          }
          break;

        case 'whatsapp':
          if (post.contact?.phone) {
            const message = `Hi! I'm interested in your product: ${post.title}`;
            const whatsappUrl = `whatsapp://send?phone=${post.contact.phone}&text=${encodeURIComponent(message)}`;
            const supported = await Linking.canOpenURL(whatsappUrl);
            if (supported) {
              await Linking.openURL(whatsappUrl);
            } else {
              Alert.alert('WhatsApp Not Available', 'WhatsApp is not installed on this device.');
            }
          } else {
            Alert.alert('No Phone Number', 'This seller has not provided a phone number for WhatsApp.');
          }
          break;

        case 'email':
          if (post.contact?.email) {
            const subject = `Inquiry about: ${post.title}`;
            const body = `Hi ${post.businessName},\n\nI'm interested in your product "${post.title}" listed on IraChat.\n\nPrice: ${post.currency} ${post.price?.toLocaleString()}\n\nPlease let me know if it's still available.\n\nBest regards`;
            const emailUrl = `mailto:${post.contact.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
            const supported = await Linking.canOpenURL(emailUrl);
            if (supported) {
              await Linking.openURL(emailUrl);
            } else {
              Alert.alert('Error', 'Email is not supported on this device.');
            }
          } else {
            Alert.alert('No Email', 'This seller has not provided an email address.');
          }
          break;
      }
    } catch (error) {
      console.error('❌ Error handling contact action:', error);
      Alert.alert('Error', 'Failed to open contact method.');
    }
  }, [post]);

  // Handle IraChat messaging
  const handleIraChatMessage = useCallback(async (post: BusinessPost) => {
    try {
      console.log('🔍 Checking if seller is registered on IraChat...');

      // Check if seller is registered by phone or email
      const sellerContact = {
        phone: post.contact?.phone,
        email: post.contact?.email,
        businessName: post.businessName
      };

      // Try to find user by phone number first
      let foundUser = null;

      if (sellerContact.phone) {
        // Import the auth service to check registration
        const { searchUsersByPhone, searchUsersByEmail } = await import('../../services/authService');
        const phoneUsers = await searchUsersByPhone(sellerContact.phone);
        foundUser = phoneUsers.length > 0 ? phoneUsers[0] : null;

        if (!foundUser && sellerContact.email) {
          // If not found by phone, try email
          const emailUsers = await searchUsersByEmail(sellerContact.email);
          foundUser = emailUsers.length > 0 ? emailUsers[0] : null;
        }
      } else if (sellerContact.email) {
        // If no phone, try email only
        const { searchUsersByEmail } = await import('../../services/authService');
        const emailUsers = await searchUsersByEmail(sellerContact.email);
        foundUser = emailUsers.length > 0 ? emailUsers[0] : null;
      }

      if (foundUser) {
        console.log('✅ Seller found on IraChat:', foundUser.id);

        // Navigate to IraChat individual chat room
        const { router } = await import('expo-router');

        // Navigate to individual chat with the seller
        router.push({
          pathname: '/individual-chat',
          params: {
            contactId: foundUser.id,
            contactName: (foundUser as any).name || (foundUser as any).fullName || post.businessName,
            contactAvatar: foundUser.avatar || '',
            contactIsOnline: 'false',
            contactLastSeen: '',
            chatId: foundUser.id, // Use user ID as chat ID for now
            // Product context for the chat
            productContext: JSON.stringify({
              productId: post.id,
              productTitle: post.title,
              productPrice: post.price ? `${post.currency} ${post.price.toLocaleString()}` : 'Contact for price',
              businessName: post.businessName
            })
          }
        });

        console.log('📱 Opened IraChat with seller');
      } else {
        // Seller not registered on IraChat
        console.log('❌ Seller not found on IraChat');

        Alert.alert(
          'Seller Not on IraChat',
          `${post.businessName} is not registered on IraChat. You can contact them using:\n\n• Phone: ${post.contact?.phone || 'Not available'}\n• Email: ${post.contact?.email || 'Not available'}\n• WhatsApp: ${post.contact?.phone ? 'Available' : 'Not available'}`,
          [
            { text: 'Cancel', style: 'cancel' },
            ...(post.contact?.phone ? [{
              text: 'Call Now',
              onPress: () => handleContactAction('phone')
            }] : []),
            ...(post.contact?.phone ? [{
              text: 'WhatsApp',
              onPress: () => handleContactAction('whatsapp')
            }] : []),
            ...(post.contact?.email ? [{
              text: 'Email',
              onPress: () => handleContactAction('email')
            }] : [])
          ]
        );
      }
    } catch (error) {
      console.error('❌ Error checking seller registration:', error);

      Alert.alert(
        'Unable to Check Registration',
        'Could not verify if the seller is on IraChat. You can try contacting them directly using phone, email, or WhatsApp.',
        [
          { text: 'OK', style: 'default' },
          ...(post.contact?.phone ? [{
            text: 'Call Instead',
            onPress: () => handleContactAction('phone')
          }] : [])
        ]
      );
    }
  }, [handleContactAction]);

  if (!post) return null;

  return (
    <Modal 
      visible={visible} 
      animationType="slide" 
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.container}>
        {HeaderComponent}
        
        {!isContentReady ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading details...</Text>
          </View>
        ) : (
          <ScrollView 
            style={styles.content}
            showsVerticalScrollIndicator={false}
            removeClippedSubviews={true}
          >
            {BasicInfoComponent}
            {StatsComponent}
            
            {/* Products Section - Lazy loaded */}
            {post.media && post.media.length > 0 && (
              <View style={styles.mediaSection}>
                <Text style={styles.sectionTitle}>Products ({post.media.length})</Text>
                <LazyMediaGrid
                  media={post.media.map(m => ({
                    id: m.id,
                    url: m.url,
                    type: m.type,
                    thumbnailUrl: m.thumbnailUrl,
                    duration: m.duration,
                  }))}
                  onMediaPress={onMediaPress}
                  onDeleteMedia={onDeleteMedia}
                  isOwner={isOwner}
                  maxInitialItems={6}
                />
              </View>
            )}
            
            {/* Description */}
            {post.description && (
              <View style={styles.descriptionSection}>
                <Text style={styles.sectionTitle}>Description</Text>
                <Text 
                  style={styles.description}
                  numberOfLines={showFullDescription ? undefined : 3}
                >
                  {post.description}
                </Text>
                {post.description.length > 150 && (
                  <TouchableOpacity 
                    onPress={() => setShowFullDescription(!showFullDescription)}
                    style={styles.showMoreButton}
                  >
                    <Text style={styles.showMoreText}>
                      {showFullDescription ? 'Show Less' : 'Show More'}
                    </Text>
                  </TouchableOpacity>
                )}
              </View>
            )}

            {/* Product Details */}
            {post.productDetails && (
              <View style={styles.productDetailsSection}>
                <Text style={styles.sectionTitle}>Product Details</Text>
                {post.productDetails.brand && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Brand:</Text>
                    <Text style={styles.detailValue}>{post.productDetails.brand}</Text>
                  </View>
                )}
                {post.productDetails.model && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Model:</Text>
                    <Text style={styles.detailValue}>{post.productDetails.model}</Text>
                  </View>
                )}
                {post.productDetails.condition && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Condition:</Text>
                    <Text style={styles.detailValue}>{post.productDetails.condition}</Text>
                  </View>
                )}
              </View>
            )}

            {/* Contact Details Section */}
            <View style={styles.contactSection}>
              <Text style={styles.sectionTitle}>Contact Seller</Text>

              {/* Business Info */}
              <View style={styles.businessInfoContainer}>
                <TouchableOpacity onPress={() => onBusinessNamePress?.(post)} style={styles.businessHeader}>
                  <View style={styles.businessNameContainer}>
                    <Text style={styles.contactBusinessName}>{post.businessName}</Text>
                    {post.isVerified && (
                      <Ionicons name="checkmark-circle" size={16} color={COLORS.primary} style={styles.verifiedIcon} />
                    )}
                  </View>
                  <Text style={styles.businessType}>{post.businessType}</Text>
                </TouchableOpacity>
              </View>

              {/* Contact Actions */}
              <View style={styles.contactActionsContainer}>
                {/* Message Seller */}
                <TouchableOpacity
                  style={[styles.contactButton, styles.messageButton]}
                  onPress={() => handleContactAction('message')}
                >
                  <Image
                    source={require('../../../assets/images/LOGO.png')}
                    style={styles.iraChatLogo}
                    resizeMode="cover"
                  />
                  <Text style={styles.contactButtonText}>IraChat</Text>
                </TouchableOpacity>

                {/* Phone Call */}
                {post.contact?.phone && (
                  <TouchableOpacity
                    style={[styles.contactButton, styles.phoneButton]}
                    onPress={() => handleContactAction('phone')}
                  >
                    <Ionicons name="call-outline" size={20} color="white" />
                    <Text style={styles.contactButtonText}>Call</Text>
                  </TouchableOpacity>
                )}

                {/* WhatsApp */}
                {post.contact?.phone && (
                  <TouchableOpacity
                    style={[styles.contactButton, styles.whatsappButton]}
                    onPress={() => handleContactAction('whatsapp')}
                  >
                    <Ionicons name="logo-whatsapp" size={20} color="white" />
                    <Text style={styles.contactButtonText}>WhatsApp</Text>
                  </TouchableOpacity>
                )}

                {/* Email */}
                {post.contact?.email && (
                  <TouchableOpacity
                    style={[styles.contactButton, styles.emailButton]}
                    onPress={() => handleContactAction('email')}
                  >
                    <Ionicons name="mail-outline" size={20} color="white" />
                    <Text style={styles.contactButtonText}>Email</Text>
                  </TouchableOpacity>
                )}

                {/* View Business Profile */}
                <TouchableOpacity
                  style={[styles.contactButton, styles.profileButton]}
                  onPress={() => onBusinessNamePress?.(post)}
                >
                  <Ionicons name="business-outline" size={20} color="white" />
                  <Text style={styles.contactButtonText}>Profile</Text>
                </TouchableOpacity>
              </View>

              {/* Contact Details */}
              <View style={styles.contactDetailsContainer}>
                {post.contact?.phone && (
                  <View style={styles.contactDetailRow}>
                    <Ionicons name="call-outline" size={16} color={COLORS.textSecondary} />
                    <Text style={styles.contactDetailText}>{post.contact.phone}</Text>
                  </View>
                )}
                {post.contact?.email && (
                  <View style={styles.contactDetailRow}>
                    <Ionicons name="mail-outline" size={16} color={COLORS.textSecondary} />
                    <Text style={styles.contactDetailText}>{post.contact.email}</Text>
                  </View>
                )}
                {post.location && (
                  <View style={styles.contactDetailRow}>
                    <Ionicons name="location-outline" size={16} color={COLORS.textSecondary} />
                    <Text style={styles.contactDetailText}>
                      {post.location.address ?
                        `${post.location.address}, ${post.location.city}, ${post.location.district}` :
                        `${post.location.city}, ${post.location.district}`
                      }
                    </Text>
                  </View>
                )}

                {/* Availability Status */}
                <View style={styles.contactDetailRow}>
                  <Ionicons
                    name={post.availability === 'available' ? 'checkmark-circle-outline' : 'close-circle-outline'}
                    size={16}
                    color={post.availability === 'available' ? COLORS.success : COLORS.error}
                  />
                  <Text style={[
                    styles.contactDetailText,
                    { color: post.availability === 'available' ? COLORS.success : COLORS.error }
                  ]}>
                    {post.availability === 'available' ? 'Available' :
                     post.availability === 'out_of_stock' ? 'Out of Stock' : 'Discontinued'}
                  </Text>
                </View>
              </View>
            </View>




          </ScrollView>
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = {
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  closeButton: {
    padding: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: COLORS.text,
  },
  headerActions: {
    flexDirection: 'row' as const,
    gap: 12,
  },
  actionButton: {
    padding: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    gap: 12,
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  content: {
    flex: 1,
  },
  basicInfo: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  productTitle: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: COLORS.text,
    marginBottom: 4,
  },
  businessName: {
    fontSize: 16,
    color: COLORS.textSecondary,
    marginBottom: 8,
  },
  clickableBusinessName: {
    color: COLORS.primary,
    textDecorationLine: 'underline' as const,
  },
  priceContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'space-between' as const,
    gap: 8,
  },
  priceRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
  },
  price: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: COLORS.success,
  },
  oldPriceStrikethrough: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textDecorationLine: 'line-through' as const,
  },
  newPrice: {
    color: COLORS.primary,
  },
  negotiable: {
    fontSize: 14,
    color: COLORS.primary,
    fontStyle: 'italic' as const,
  },
  reducePriceButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: COLORS.success + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  reducePriceText: {
    fontSize: 12,
    color: COLORS.success,
    fontWeight: '500' as const,
  },
  statsContainer: {
    flexDirection: 'row' as const,
    justifyContent: 'space-around' as const,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  statItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 4,
  },
  statText: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  mediaSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginBottom: 12,
  },
  descriptionSection: {
    padding: 16,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    color: COLORS.text,
  },
  showMoreButton: {
    marginTop: 8,
  },
  showMoreText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500' as const,
  },
  productDetailsSection: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  detailRow: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
    paddingVertical: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500' as const,
  },
  detailValue: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '400' as const,
  },
  contactItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingVertical: 8,
    gap: 12,
  },
  contactText: {
    fontSize: 14,
    color: COLORS.text,
  },
  ownerActions: {
    padding: 16,
    gap: 12,
  },
  actionButtonLarge: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600' as const,
  },

  // Contact Section Styles
  contactSection: {
    padding: 16,
    backgroundColor: COLORS.background,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
    marginTop: 8,
  },
  businessInfoContainer: {
    marginBottom: 16,
  },
  businessHeader: {
    padding: 12,
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  businessNameContainer: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 4,
  },
  contactBusinessName: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: COLORS.text,
    marginRight: 6,
  },
  verifiedIcon: {
    marginLeft: 4,
  },
  businessType: {
    fontSize: 14,
    color: COLORS.textSecondary,
    textTransform: 'capitalize' as const,
  },
  contactActionsContainer: {
    flexDirection: 'row' as const,
    flexWrap: 'wrap' as const,
    gap: 12,
    marginBottom: 16,
  },
  contactButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    gap: 8,
    minWidth: 100,
    flex: 1,
  },
  messageButton: {
    backgroundColor: COLORS.primary,
  },
  phoneButton: {
    backgroundColor: '#17BF63',
  },
  whatsappButton: {
    backgroundColor: '#25D366',
  },
  emailButton: {
    backgroundColor: '#1DA1F2',
  },
  profileButton: {
    backgroundColor: '#6B46C1',
  },
  contactButtonText: {
    fontSize: 14,
    color: 'white',
    fontWeight: '600' as const,
  },
  iraChatLogo: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  contactDetailsContainer: {
    gap: 8,
  },
  contactDetailRow: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    gap: 8,
    paddingVertical: 4,
  },
  contactDetailText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    flex: 1,
  },
};
