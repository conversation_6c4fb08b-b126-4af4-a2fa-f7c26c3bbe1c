import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  Pressable,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface GroupMessage {
  id: string;
  text?: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  timestamp: any;
  status: "sent" | "delivered" | "read";
  type: "text" | "image" | "video" | "audio" | "document" | "voice" | "call" | "location" | "contact" | "poll" | "announcement";
  mediaUrl?: string;
  mediaThumbnail?: string;
  duration?: number;
  fileName?: string;
  fileSize?: number;
}

interface MessageSearchModalProps {
  visible: boolean;
  onClose: () => void;
  messages: GroupMessage[];
  onMessageSelect?: (message: GroupMessage) => void;
}

export const MessageSearchModal: React.FC<MessageSearchModalProps> = ({
  visible,
  onClose,
  messages,
  onMessageSelect,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredMessages, setFilteredMessages] = useState<GroupMessage[]>([]);
  const [searchType, setSearchType] = useState<'all' | 'text' | 'media' | 'links'>('all');

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredMessages([]);
      return;
    }

    const filtered = messages.filter((message) => {
      // Filter by search type
      if (searchType === 'text' && message.type !== 'text') return false;
      if (searchType === 'media' && !['image', 'video', 'audio', 'document'].includes(message.type)) return false;
      
      // Filter by search query
      const searchLower = searchQuery.toLowerCase();
      
      // Search in message text
      if (message.text && message.text.toLowerCase().includes(searchLower)) return true;
      
      // Search in sender name
      if (message.senderName.toLowerCase().includes(searchLower)) return true;
      
      // Search in file name
      if (message.fileName && message.fileName.toLowerCase().includes(searchLower)) return true;
      
      return false;
    });

    setFilteredMessages(filtered.slice(0, 50)); // Limit results for performance
  }, [searchQuery, messages, searchType]);

  const handleMessagePress = (message: GroupMessage) => {
    onMessageSelect?.(message);
    onClose();
  };

  const formatMessagePreview = (message: GroupMessage) => {
    switch (message.type) {
      case 'text':
        return message.text || '';
      case 'image':
        return '📷 Photo';
      case 'video':
        return '🎥 Video';
      case 'audio':
        return '🎵 Audio';
      case 'voice':
        return '🎤 Voice message';
      case 'document':
        return `📄 ${message.fileName || 'Document'}`;
      case 'location':
        return '📍 Location';
      case 'contact':
        return '👤 Contact';
      case 'poll':
        return '📊 Poll';
      case 'announcement':
        return '📢 Announcement';
      default:
        return 'Message';
    }
  };

  const renderMessage = ({ item }: { item: GroupMessage }) => (
    <TouchableOpacity
      onPress={() => handleMessagePress(item)}
      style={{
        flexDirection: 'row',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
        alignItems: 'center',
      }}
    >
      {/* Sender Avatar */}
      {item.senderAvatar ? (
        <Image
          source={{ uri: item.senderAvatar }}
          style={{ width: 40, height: 40, borderRadius: 20, marginRight: 12 }}
        />
      ) : (
        <View
          style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: '#87CEEB',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 12,
          }}
        >
          <Text style={{ color: 'white', fontSize: 14, fontWeight: 'bold' }}>
            {item.senderName.charAt(0).toUpperCase()}
          </Text>
        </View>
      )}

      {/* Message Content */}
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 4 }}>
          <Text style={{ fontSize: 14, fontWeight: '600', color: '#87CEEB' }}>
            {item.senderName}
          </Text>
          <Text style={{ fontSize: 12, color: '#999' }}>
            {item.timestamp?.toDate?.()?.toLocaleDateString() || 'Recent'}
          </Text>
        </View>
        
        <Text
          style={{ fontSize: 14, color: '#333', lineHeight: 20 }}
          numberOfLines={2}
        >
          {formatMessagePreview(item)}
        </Text>
      </View>

      <Ionicons name="chevron-forward" size={16} color="#999" style={{ marginLeft: 8 }} />
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)' }}>
        <View
          style={{
            flex: 1,
            backgroundColor: 'white',
            marginTop: 50,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
          }}
        >
          {/* Header */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              padding: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#E5E7EB',
            }}
          >
            <TouchableOpacity onPress={onClose} style={{ marginRight: 16 }}>
              <Ionicons name="arrow-back" size={24} color="#333" />
            </TouchableOpacity>
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#333', flex: 1 }}>
              Search Messages
            </Text>
          </View>

          {/* Search Input */}
          <View style={{ padding: 16 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#F8F9FA',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 12,
              }}
            >
              <Ionicons name="search" size={20} color="#999" />
              <TextInput
                placeholder="Search messages..."
                placeholderTextColor="#999"
                value={searchQuery}
                onChangeText={setSearchQuery}
                style={{
                  flex: 1,
                  marginLeft: 12,
                  fontSize: 16,
                  color: '#333',
                }}
                autoFocus
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Ionicons name="close-circle" size={20} color="#999" />
                </TouchableOpacity>
              )}
            </View>

            {/* Search Type Filters */}
            <View style={{ flexDirection: 'row', marginTop: 16 }}>
              {[
                { key: 'all', label: 'All' },
                { key: 'text', label: 'Text' },
                { key: 'media', label: 'Media' },
              ].map((filter) => (
                <TouchableOpacity
                  key={filter.key}
                  onPress={() => setSearchType(filter.key as any)}
                  style={{
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    borderRadius: 20,
                    backgroundColor: searchType === filter.key ? '#87CEEB' : '#F8F9FA',
                    marginRight: 8,
                  }}
                >
                  <Text
                    style={{
                      fontSize: 14,
                      color: searchType === filter.key ? 'white' : '#666',
                      fontWeight: searchType === filter.key ? '600' : 'normal',
                    }}
                  >
                    {filter.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Results */}
          {searchQuery.trim() === '' ? (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <Ionicons name="search" size={64} color="#E5E7EB" />
              <Text style={{ fontSize: 16, color: '#999', marginTop: 16 }}>
                Start typing to search messages
              </Text>
            </View>
          ) : filteredMessages.length === 0 ? (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <Ionicons name="document-text-outline" size={64} color="#E5E7EB" />
              <Text style={{ fontSize: 16, color: '#999', marginTop: 16 }}>
                No messages found
              </Text>
            </View>
          ) : (
            <FlatList
              data={filteredMessages}
              keyExtractor={(item) => item.id}
              renderItem={renderMessage}
              style={{ flex: 1 }}
            />
          )}
        </View>
      </View>
    </Modal>
  );
};
