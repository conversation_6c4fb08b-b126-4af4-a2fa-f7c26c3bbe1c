/**
 * Enhanced Firebase Upload Service for IraChat
 * Comprehensive upload system with progress tracking, retry logic, compression, and metadata handling
 */

import { storage, db } from './firebaseSimple';
import { ref, uploadBytesResumable, getDownloadURL, deleteObject } from 'firebase/storage';
import { collection, addDoc, updateDoc, doc, serverTimestamp } from 'firebase/firestore';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import { errorHandlingService } from './errorHandlingService';

export interface UploadProgress {
  uploadId: string;
  progress: number; // 0-100
  stage: 'compressing' | 'uploading' | 'saving' | 'complete' | 'error';
  bytesTransferred: number;
  totalBytes: number;
  error?: string;
}

export interface MediaMetadata {
  width: number;
  height: number;
  duration?: number; // for videos
  size: number;
  format: string;
  aspectRatio: number;
  isCompressed: boolean;
  originalSize?: number;
}

export interface UploadOptions {
  compress?: boolean;
  quality?: number; // 0-1
  maxWidth?: number;
  maxHeight?: number;
  retryAttempts?: number;
  onProgress?: (progress: UploadProgress) => void;
}

class EnhancedFirebaseUploadService {
  private activeUploads = new Map<string, any>();
  private uploadQueue: Array<() => Promise<void>> = [];
  private isProcessingQueue = false;
  private maxConcurrentUploads = 3;

  /**
   * Upload media with comprehensive features
   */
  async uploadMedia(
    mediaUri: string,
    mediaType: 'photo' | 'video',
    userId: string,
    caption: string,
    hashtags: string[] = [],
    mentions: string[] = [],
    options: UploadOptions = {}
  ): Promise<string> {
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const defaultOptions: UploadOptions = {
      compress: true,
      quality: 0.8,
      maxWidth: 1080,
      maxHeight: 1920,
      retryAttempts: 3,
      ...options,
    };

    try {
      console.log(`🚀 Starting enhanced upload for ${mediaType}:`, uploadId);

      // Stage 1: Compression and optimization
      this.notifyProgress(uploadId, {
        uploadId,
        progress: 0,
        stage: 'compressing',
        bytesTransferred: 0,
        totalBytes: 0,
      }, defaultOptions.onProgress);

      const { processedUri, metadata } = await this.processMedia(
        mediaUri,
        mediaType,
        defaultOptions
      );

      // Stage 2: Upload to Firebase Storage
      this.notifyProgress(uploadId, {
        uploadId,
        progress: 10,
        stage: 'uploading',
        bytesTransferred: 0,
        totalBytes: metadata.size,
      }, defaultOptions.onProgress);

      const downloadUrl = await this.uploadToStorage(
        processedUri,
        mediaType,
        userId,
        uploadId,
        defaultOptions.onProgress
      );

      // Stage 3: Save to Firestore
      this.notifyProgress(uploadId, {
        uploadId,
        progress: 90,
        stage: 'saving',
        bytesTransferred: metadata.size,
        totalBytes: metadata.size,
      }, defaultOptions.onProgress);

      const postId = await this.saveToFirestore(
        downloadUrl,
        mediaType,
        userId,
        caption,
        hashtags,
        mentions,
        metadata
      );

      // Stage 4: Complete
      this.notifyProgress(uploadId, {
        uploadId,
        progress: 100,
        stage: 'complete',
        bytesTransferred: metadata.size,
        totalBytes: metadata.size,
      }, defaultOptions.onProgress);

      // Clean up processed file if different from original
      if (processedUri !== mediaUri) {
        await FileSystem.deleteAsync(processedUri, { idempotent: true });
      }

      console.log(`✅ Upload completed successfully:`, { uploadId, postId, downloadUrl });
      return postId;

    } catch (error) {
      console.error(`❌ Upload failed for ${uploadId}:`, error);

      // Use comprehensive error handling
      errorHandlingService.handleError(
        error instanceof Error ? error : new Error('Upload failed'),
        `Enhanced Firebase Upload - ${mediaType} upload`,
        userId
      );

      this.notifyProgress(uploadId, {
        uploadId,
        progress: 0,
        stage: 'error',
        bytesTransferred: 0,
        totalBytes: 0,
        error: error instanceof Error ? error.message : 'Upload failed',
      }, defaultOptions.onProgress);

      // Retry logic
      if (defaultOptions.retryAttempts && defaultOptions.retryAttempts > 0) {
        console.log(`🔄 Retrying upload (${defaultOptions.retryAttempts} attempts left)...`);
        await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds

        return this.uploadMedia(
          mediaUri,
          mediaType,
          userId,
          caption,
          hashtags,
          mentions,
          { ...defaultOptions, retryAttempts: defaultOptions.retryAttempts - 1 }
        );
      }

      throw error;
    } finally {
      this.activeUploads.delete(uploadId);
    }
  }

  /**
   * Process media (compression, optimization)
   */
  private async processMedia(
    mediaUri: string,
    mediaType: 'photo' | 'video',
    options: UploadOptions
  ): Promise<{ processedUri: string; metadata: MediaMetadata }> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(mediaUri);
      const originalSize = fileInfo.size || 0;

      if (mediaType === 'photo' && options.compress) {
        // Compress image
        const manipulateOptions: any = {
          compress: options.quality || 0.8,
          format: SaveFormat.JPEG,
        };

        // Resize if needed
        if (options.maxWidth || options.maxHeight) {
          manipulateOptions.resize = {
            width: options.maxWidth,
            height: options.maxHeight,
          };
        }

        const result = await manipulateAsync(mediaUri, [], manipulateOptions);
        
        const processedInfo = await FileSystem.getInfoAsync(result.uri);
        const processedSize = processedInfo.size || 0;

        const metadata: MediaMetadata = {
          width: result.width,
          height: result.height,
          size: processedSize,
          format: 'jpeg',
          aspectRatio: result.width / result.height,
          isCompressed: true,
          originalSize,
        };

        console.log(`📸 Image processed: ${originalSize} → ${processedSize} bytes (${((1 - processedSize/originalSize) * 100).toFixed(1)}% reduction)`);

        return { processedUri: result.uri, metadata };
      } else {
        // For videos or uncompressed photos, use original
        const metadata: MediaMetadata = {
          width: 0, // Would need video processing library to get dimensions
          height: 0,
          size: originalSize,
          format: mediaType === 'video' ? 'mp4' : 'jpeg',
          aspectRatio: 16/9, // Default assumption
          isCompressed: false,
          originalSize,
        };

        return { processedUri: mediaUri, metadata };
      }
    } catch (error) {
      console.error('❌ Error processing media:', error);
      throw new Error('Failed to process media');
    }
  }

  /**
   * Upload to Firebase Storage with progress tracking
   */
  private async uploadToStorage(
    mediaUri: string,
    mediaType: 'photo' | 'video',
    userId: string,
    uploadId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    try {
      // Create storage reference
      const timestamp = Date.now();
      const fileName = `${mediaType}_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
      const storagePath = `updates/${userId}/${fileName}`;
      const storageRef = ref(storage, storagePath);

      // Read file as blob
      const response = await fetch(mediaUri);
      const blob = await response.blob();

      // Create upload task
      const uploadTask = uploadBytesResumable(storageRef, blob);
      this.activeUploads.set(uploadId, uploadTask);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 80 + 10; // 10-90%
            
            this.notifyProgress(uploadId, {
              uploadId,
              progress,
              stage: 'uploading',
              bytesTransferred: snapshot.bytesTransferred,
              totalBytes: snapshot.totalBytes,
            }, onProgress);
          },
          (error) => {
            console.error('❌ Upload error:', error);
            reject(error);
          },
          async () => {
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              console.log('✅ File uploaded successfully:', downloadURL);
              resolve(downloadURL);
            } catch (error) {
              reject(error);
            }
          }
        );
      });
    } catch (error) {
      console.error('❌ Error uploading to storage:', error);
      throw error;
    }
  }

  /**
   * Save post data to Firestore
   */
  private async saveToFirestore(
    mediaUrl: string,
    mediaType: 'photo' | 'video',
    userId: string,
    caption: string,
    hashtags: string[],
    mentions: string[],
    metadata: MediaMetadata
  ): Promise<string> {
    try {
      const postData = {
        userId,
        type: mediaType,
        mediaUrl,
        caption: caption.trim(),
        hashtags,
        mentions,
        metadata,
        likes: [],
        likeCount: 0,
        comments: [],
        commentCount: 0,
        shares: 0,
        views: 0,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        isActive: true,
        reportCount: 0,
      };

      const docRef = await addDoc(collection(db, 'updates'), postData);
      console.log('✅ Post saved to Firestore:', docRef.id);
      
      return docRef.id;
    } catch (error) {
      console.error('❌ Error saving to Firestore:', error);
      throw error;
    }
  }

  /**
   * Cancel upload
   */
  async cancelUpload(uploadId: string): Promise<void> {
    const uploadTask = this.activeUploads.get(uploadId);
    if (uploadTask) {
      uploadTask.cancel();
      this.activeUploads.delete(uploadId);
      console.log(`🚫 Upload cancelled: ${uploadId}`);
    }
  }

  /**
   * Get active uploads
   */
  getActiveUploads(): string[] {
    return Array.from(this.activeUploads.keys());
  }

  /**
   * Notify progress
   */
  private notifyProgress(
    uploadId: string,
    progress: UploadProgress,
    onProgress?: (progress: UploadProgress) => void
  ): void {
    if (onProgress) {
      onProgress(progress);
    }
  }

  /**
   * Clean up failed uploads
   */
  async cleanupFailedUpload(mediaUrl: string): Promise<void> {
    try {
      const storageRef = ref(storage, mediaUrl);
      await deleteObject(storageRef);
      console.log('🧹 Cleaned up failed upload:', mediaUrl);
    } catch (error) {
      console.warn('⚠️ Could not clean up failed upload:', error);
    }
  }
}

export const enhancedFirebaseUploadService = new EnhancedFirebaseUploadService();
