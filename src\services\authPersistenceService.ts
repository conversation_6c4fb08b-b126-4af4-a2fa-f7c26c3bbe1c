// 🔐 AUTHENTICATION PERSISTENCE SERVICE
// Handles secure session storage and automatic login restoration

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';
import NetInfo from '@react-native-community/netinfo';
import { auth } from './firebaseSimple';
import { User as FirebaseUser } from 'firebase/auth';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from './firebaseSimple';
import { User } from '../types';

interface SessionData {
  userId: string;
  email?: string;
  phoneNumber?: string;
  authMethod: 'phone' | 'email';
  lastLoginTime: number;
  sessionToken: string;
  refreshToken: string;
  deviceId: string;
  appVersion: string;
  encryptionKey: string;
}

interface SessionValidationResult {
  isValid: boolean;
  shouldRefresh: boolean;
  reason?: string;
  user?: User;
}

export class AuthPersistenceService {
  private static instance: AuthPersistenceService;
  private currentSession: SessionData | null = null;
  private sessionCheckInterval: NodeJS.Timeout | null = null;
  private readonly STORAGE_KEYS = {
    SESSION: 'irachat_session',
    DEVICE_ID: 'irachat_device_id',
    LAST_LOGIN: 'irachat_last_login',
    AUTH_STATE: 'irachat_auth_state',
  };

  static getInstance(): AuthPersistenceService {
    if (!AuthPersistenceService.instance) {
      AuthPersistenceService.instance = new AuthPersistenceService();
    }
    return AuthPersistenceService.instance;
  }

  /**
   * Initialize the persistence service
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔐 Initializing authentication persistence service...');
      
      // Start session validation interval (check every 5 minutes)
      this.startSessionValidation();
      
      // Listen to Firebase auth state changes
      this.setupAuthStateListener();
      
      console.log('✅ Authentication persistence service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize auth persistence service:', error);
    }
  }

  /**
   * Save user session securely
   */
  async saveSession(user: User, _firebaseUser: FirebaseUser): Promise<void> {
    try {
      console.log('💾 Saving user session...');

      // Validate user data before proceeding
      if (!user || !user.id || user.id.trim() === '') {
        throw new Error('Invalid user data: User ID is required');
      }

      // Additional validation for SecureStore key compatibility
      const userIdPattern = /^[a-zA-Z0-9._-]+$/;
      if (!userIdPattern.test(user.id)) {
        console.warn('⚠️ User ID contains invalid characters for SecureStore:', user.id);
        // Don't throw error, just log warning and continue
      }

      console.log('💾 Saving session for user ID:', user.id);

      const deviceId = await this.getOrCreateDeviceId();
      const sessionToken = await this.generateSessionToken();
      const refreshToken = await this.generateRefreshToken();
      const encryptionKey = await this.generateEncryptionKey();

      const sessionData: SessionData = {
        userId: user.id,
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
        authMethod: user.authMethod || 'phone',
        lastLoginTime: Date.now(),
        sessionToken,
        refreshToken,
        deviceId,
        appVersion: this.getAppVersion(),
        encryptionKey,
      };

      // Encrypt session data
      const encryptedSession = await this.encryptSessionData(sessionData);

      // Validate data before storing
      if (!encryptedSession || encryptedSession.trim() === '') {
        throw new Error('Encrypted session data is empty');
      }

      if (!this.STORAGE_KEYS.SESSION || this.STORAGE_KEYS.SESSION.trim() === '') {
        throw new Error('Storage key is invalid');
      }

      // Validate SecureStore key format
      if (!this.isValidSecureStoreKey(this.STORAGE_KEYS.SESSION)) {
        throw new Error(`Invalid SecureStore key format: ${this.STORAGE_KEYS.SESSION}`);
      }

      // Store in secure storage (iOS Keychain / Android Keystore)
      console.log('💾 Storing session data...');
      console.log('💾 Platform:', Platform.OS);
      console.log('💾 Storage key:', this.STORAGE_KEYS.SESSION);
      console.log('💾 Data length:', encryptedSession.length);

      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        console.log('💾 Using SecureStore for session storage');
        await SecureStore.setItemAsync(this.STORAGE_KEYS.SESSION, encryptedSession);
        console.log('✅ SecureStore.setItemAsync completed successfully');
      } else {
        console.log('💾 Using AsyncStorage for session storage (fallback)');
        // Fallback to AsyncStorage for other platforms
        await AsyncStorage.setItem(this.STORAGE_KEYS.SESSION, encryptedSession);
        console.log('✅ AsyncStorage.setItem completed successfully');
      }

      // Store device ID separately
      await AsyncStorage.setItem(this.STORAGE_KEYS.DEVICE_ID, deviceId);
      await AsyncStorage.setItem(this.STORAGE_KEYS.LAST_LOGIN, Date.now().toString());

      // Update user's last login in Firestore with serializable timestamp
      await updateDoc(doc(db, 'users', user.id), {
        lastLoginTime: Date.now(), // Use number instead of Date object
        deviceId,
        isOnline: true,
      });

      this.currentSession = sessionData;
      console.log('✅ Session saved successfully');
    } catch (error) {
      console.error('❌ Failed to save session:', error);
      throw error;
    }
  }

  /**
   * Restore user session on app startup
   */
  async restoreSession(): Promise<User | null> {
    try {
      console.log('🔄 Attempting to restore user session...');

      // Check network connectivity first (but don't fail if offline)
      const netInfo = await NetInfo.fetch();
      const isOnline = netInfo.isConnected;
      if (!isOnline) {
        console.log('📡 No internet connection - attempting offline session restore');
      }

      // Validate storage key before using
      if (!this.STORAGE_KEYS.SESSION || this.STORAGE_KEYS.SESSION.trim() === '') {
        console.error('❌ Invalid storage key for session');
        return null;
      }

      // Get encrypted session data
      let encryptedSession: string | null = null;

      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        encryptedSession = await SecureStore.getItemAsync(this.STORAGE_KEYS.SESSION);
      } else {
        encryptedSession = await AsyncStorage.getItem(this.STORAGE_KEYS.SESSION);
      }

      if (!encryptedSession) {
        console.log('📭 No saved session found');
        return null;
      }

      // Decrypt session data
      const sessionData = await this.decryptSessionData(encryptedSession);
      if (!sessionData) {
        console.log('🔓 Failed to decrypt session data');
        await this.clearSession();
        return null;
      }

      // Validate session (be more lenient when offline)
      const validation = await this.validateSession(sessionData, isOnline || false);
      if (!validation.isValid) {
        console.log('❌ Session validation failed:', validation.reason);
        // Be very lenient - only clear session for critical security failures
        const criticalFailures = ['Session expired', 'Invalid session token', 'Security violation'];
        if (criticalFailures.includes(validation.reason || '')) {
          console.log('🚨 Critical session failure - clearing session');
          await this.clearSession();
          return null;
        } else {
          // For non-critical failures, keep user logged in but log the issue
          console.log('⚠️ Non-critical session issue - keeping user logged in:', validation.reason);
          // When offline or network issues, use cached user data
          console.log('📡 Using cached session data due to validation issue');
          // Construct a basic User object from sessionData
          const fallbackUser: User = sessionData.email ? {
            id: sessionData.userId,
            username: sessionData.email.split('@')[0],
            displayName: sessionData.email.split('@')[0],
            name: sessionData.email.split('@')[0],
            avatar: '',
            followersCount: 0,
            followingCount: 0,
            likesCount: 0,
            email: sessionData.email,
            authMethod: 'email' as const,
            emailVerified: true,
          } : {
            id: sessionData.userId,
            username: sessionData.phoneNumber || sessionData.userId,
            displayName: sessionData.phoneNumber || sessionData.userId,
            name: sessionData.phoneNumber || sessionData.userId,
            avatar: '',
            followersCount: 0,
            followingCount: 0,
            likesCount: 0,
            phoneNumber: sessionData.phoneNumber || '',
            authMethod: 'phone' as const,
            phoneVerified: true,
          };
          return validation.user || fallbackUser;
        }
      }

      // Refresh session if needed (but be more conservative)
      if (validation.shouldRefresh && isOnline) {
        console.log('🔄 Refreshing session...');
        try {
          await this.refreshSession(sessionData);
        } catch (refreshError) {
          console.warn('⚠️ Session refresh failed, but keeping user logged in:', refreshError);
          // Don't fail the entire session restore if refresh fails
        }
      }

      this.currentSession = sessionData;
      console.log('✅ Session restored successfully');
      return validation.user || null;
    } catch (error) {
      console.error('❌ Failed to restore session:', error);
      await this.clearSession();
      return null;
    }
  }

  /**
   * Prioritizes keeping users logged in unless there's a critical security issue
   */
  private async validateSession(sessionData: SessionData, isOnline: boolean = true): Promise<SessionValidationResult> {
    try {
      console.log('🔍 Validating session for user:', sessionData.userId);

      // If offline, be very lenient and trust the cached session
      if (!isOnline) {
        console.log('📡 Offline mode - using cached session validation');
        // Only check if session is extremely old (180 days instead of 90)
        const sessionAge = Date.now() - sessionData.lastLoginTime;
        const maxOfflineSessionAge = 180 * 24 * 60 * 60 * 1000; // 180 days

        if (sessionAge > maxOfflineSessionAge) {
          console.log('❌ Session too old for offline use:', sessionAge / (24 * 60 * 60 * 1000), 'days');
          return { isValid: false, shouldRefresh: false, reason: 'Session too old for offline use' };
        }

        console.log('✅ Offline session valid');
        // Return valid with cached user data
        return {
          isValid: true,
          shouldRefresh: false,
          user: sessionData.userId ? { id: sessionData.userId } as User : undefined,
        };
      }

      // Online validation - be very lenient (180 days instead of 90)
      const sessionAge = Date.now() - sessionData.lastLoginTime;
      const maxSessionAge = 180 * 24 * 60 * 60 * 1000; // 180 days

      if (sessionAge > maxSessionAge) {
        console.log('❌ Session expired:', sessionAge / (24 * 60 * 60 * 1000), 'days old');
        return { isValid: false, shouldRefresh: false, reason: 'Session expired' };
      }

      // REMOVED: App version compatibility check - too strict
      // Users should stay logged in across app updates
      console.log('✅ Skipping app version check to maintain user session');

      // REMOVED: Device ID check - too strict for development and user experience
      // Device IDs can change for various reasons and shouldn't log users out
      console.log('✅ Skipping device ID check to maintain user session');

      // LENIENT: Try to verify user in Firestore, but don't fail if unavailable
      let userDoc;
      try {
        console.log('🔍 Verifying user exists in Firestore...');
        userDoc = await getDoc(doc(db, 'users', sessionData.userId));
        if (!userDoc.exists()) {
          console.warn('⚠️ User document not found in Firestore, but keeping session valid');
          // Don't invalidate session - user might exist but document might be missing
          // Return basic user object to keep them logged in
          return {
            isValid: true,
            shouldRefresh: false,
            user: {
              id: sessionData.userId,
              email: sessionData.email,
              phoneNumber: sessionData.phoneNumber,
              authMethod: sessionData.authMethod
            } as User,
          };
        }
        console.log('✅ User document found in Firestore');
      } catch (firestoreError) {
        console.warn('⚠️ Failed to verify user in Firestore, keeping session valid:', firestoreError);
        // If Firestore is unavailable, don't invalidate the session
        return {
          isValid: true,
          shouldRefresh: false,
          user: {
            id: sessionData.userId,
            email: sessionData.email,
            phoneNumber: sessionData.phoneNumber,
            authMethod: sessionData.authMethod
          } as User,
        };
      }

      const userData = userDoc.data() as User;

      // Convert any Firestore Timestamps to numbers for Redux serialization
      const convertTimestamps = (obj: any): any => {
        if (!obj) return obj;

        const converted = { ...obj };
        const timestampFields = ['lastSeen', 'createdAt', 'updatedAt', 'lastLoginTime'];

        timestampFields.forEach(field => {
          if (converted[field]) {
            // Handle Firestore Timestamps
            if (converted[field] && typeof converted[field] === 'object' && 'seconds' in converted[field] && 'nanoseconds' in converted[field]) {
              converted[field] = converted[field].seconds * 1000 + converted[field].nanoseconds / 1000000;
            }
            // Handle Firestore Timestamp objects with toDate method
            else if (converted[field] && typeof converted[field] === 'object' && typeof converted[field].toDate === 'function') {
              converted[field] = converted[field].toDate().getTime();
            }
            // Handle Date objects
            else if (converted[field] instanceof Date) {
              converted[field] = converted[field].getTime();
            }
            // Ensure it's a number
            else if (typeof converted[field] !== 'number') {
              converted[field] = Date.now(); // Fallback to current time
            }
          }
        });

        return converted;
      };

      const serializedUserData = convertTimestamps(userData);

      // LENIENT: Only invalidate for explicitly banned accounts
      if ((serializedUserData as any).isBanned === true) {
        console.log('❌ Account is banned');
        return { isValid: false, shouldRefresh: false, reason: 'Account banned' };
      }

      // Don't invalidate for soft-deleted accounts - they might be recoverable
      if ((serializedUserData as any).isDeleted === true) {
        console.log('⚠️ Account marked as deleted but keeping session valid for recovery');
      }

      // Check if session should be refreshed (older than 60 days instead of 30)
      const refreshThreshold = 60 * 24 * 60 * 60 * 1000; // 60 days
      const shouldRefresh = sessionAge > refreshThreshold;

      console.log('✅ Session validation passed for user:', sessionData.userId);

      return {
        isValid: true,
        shouldRefresh,
        user: { ...serializedUserData, id: userDoc.id },
      };
    } catch (error) {
      console.error('❌ Session validation error:', error);
      return { isValid: false, shouldRefresh: false, reason: 'Validation error' };
    }
  }

  /**
   * Refresh session tokens
   */
  private async refreshSession(sessionData: SessionData): Promise<void> {
    try {
      const newSessionToken = await this.generateSessionToken();
      const newRefreshToken = await this.generateRefreshToken();

      sessionData.sessionToken = newSessionToken;
      sessionData.refreshToken = newRefreshToken;
      sessionData.lastLoginTime = Date.now();

      // Re-encrypt and save updated session
      const encryptedSession = await this.encryptSessionData(sessionData);
      
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        await SecureStore.setItemAsync(this.STORAGE_KEYS.SESSION, encryptedSession);
      } else {
        await AsyncStorage.setItem(this.STORAGE_KEYS.SESSION, encryptedSession);
      }

      console.log('✅ Session refreshed successfully');
    } catch (error) {
      console.error('❌ Failed to refresh session:', error);
      throw error;
    }
  }

  /**
   * Clear all session data
   */
  async clearSession(): Promise<void> {
    try {
      console.log('🗑️ Clearing session data...');

      // Clear secure storage
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        await SecureStore.deleteItemAsync(this.STORAGE_KEYS.SESSION).catch(() => {});
      } else {
        await AsyncStorage.removeItem(this.STORAGE_KEYS.SESSION);
      }

      // Clear other storage
      await AsyncStorage.multiRemove([
        this.STORAGE_KEYS.LAST_LOGIN,
        this.STORAGE_KEYS.AUTH_STATE,
      ]);

      this.currentSession = null;
      console.log('✅ Session cleared successfully');
    } catch (error) {
      console.error('❌ Failed to clear session:', error);
    }
  }

  /**
   * Check if user should be logged out due to specific scenarios
   */
  async checkLogoutScenarios(): Promise<boolean> {
    try {
      // Disable aggressive logout scenarios that were causing constant logouts
      console.log('⚠️ Logout scenarios check disabled to prevent constant logouts');
      return false;

      // Original logic commented out:
      // const netInfo = await NetInfo.fetch();
      //
      // // Clear credentials if user tried to login without internet
      // if (!netInfo.isConnected) {
      //   const lastLoginAttempt = await AsyncStorage.getItem('irachat_login_attempt');
      //   if (lastLoginAttempt) {
      //     console.log('📡 Clearing credentials due to offline login attempt');
      //     await this.clearSession();
      //     await AsyncStorage.removeItem('irachat_login_attempt');
      //     return true;
      //   }
      // }
      //
      // return false;
    } catch (error) {
      console.error('❌ Error checking logout scenarios:', error);
      return false;
    }
  }

  /**
   * Generate secure session token
   */
  private async generateSessionToken(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return `session_${timestamp}_${random}`;
  }

  /**
   * Generate secure refresh token
   */
  private async generateRefreshToken(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    return `refresh_${timestamp}_${random}`;
  }

  /**
   * Generate encryption key
   */
  private async generateEncryptionKey(): Promise<string> {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * Get or create device ID
   */
  private async getOrCreateDeviceId(): Promise<string> {
    let deviceId = await AsyncStorage.getItem(this.STORAGE_KEYS.DEVICE_ID);
    
    if (!deviceId) {
      deviceId = `device_${Date.now()}_${Math.random().toString(36).substring(2)}`;
      await AsyncStorage.setItem(this.STORAGE_KEYS.DEVICE_ID, deviceId);
    }
    
    return deviceId;
  }

  /**
   * Get current app version from app configuration
   */
  private getAppVersion(): string {
    try {
      // Get version from Expo Constants or app.json
      const Constants = require('expo-constants').default;
      return Constants.expoConfig?.version || Constants.manifest?.version || '1.0.0';
    } catch (error) {
      console.warn('Could not get app version, using default:', error);
      return '1.0.0';
    }
  }

  /**
   * Handle app version update
   */
  async handleAppVersionUpdate(newVersion: string): Promise<void> {
    try {
      if (!this.currentSession) return;

      const oldVersion = this.currentSession.appVersion;

      if (this.isSignificantVersionChange(oldVersion, newVersion)) {
        console.log(`📱 Significant app update detected (${oldVersion} → ${newVersion}) - clearing session`);
        await this.clearSession();
      } else {
        console.log(`📱 Minor app update detected (${oldVersion} → ${newVersion}) - keeping session`);
        // Update version in session
        this.currentSession.appVersion = newVersion;
        const encryptedSession = await this.encryptSessionData(this.currentSession);

        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          await SecureStore.setItemAsync(this.STORAGE_KEYS.SESSION, encryptedSession);
        } else {
          await AsyncStorage.setItem(this.STORAGE_KEYS.SESSION, encryptedSession);
        }
      }
    } catch (error) {
      console.error('❌ Failed to handle app version update:', error);
    }
  }

  /**
   * Check if version change is significant enough to require re-login
   */
  private isSignificantVersionChange(oldVersion: string, newVersion: string): boolean {
    const oldMajor = parseInt(oldVersion.split('.')[0]);
    const newMajor = parseInt(newVersion.split('.')[0]);
    
    // Require re-login for major version changes
    return newMajor > oldMajor;
  }

  /**
   * Encrypt session data using simple base64 encoding (React Native compatible)
   */
  private async encryptSessionData(sessionData: SessionData): Promise<string> {
    try {
      // Validate session data
      if (!sessionData || !sessionData.userId || sessionData.userId.trim() === '') {
        throw new Error('Invalid session data: userId is required');
      }

      // Use simple JSON string for React Native compatibility (no base64 needed)
      const dataString = JSON.stringify(sessionData);

      if (!dataString || dataString.trim() === '') {
        throw new Error('Failed to serialize session data');
      }

      // For React Native, we'll use simple JSON string instead of base64
      // SecureStore handles the encryption at the OS level
      return dataString;
    } catch (error) {
      console.error('❌ Encryption failed:', error);
      throw new Error(`Session encryption failed: ${error}`);
    }
  }

  /**
   * Decrypt session data using simple base64 decoding (React Native compatible)
   */
  private async decryptSessionData(encryptedData: string): Promise<SessionData | null> {
    try {
      // Since we're now using plain JSON, just parse it directly
      if (!encryptedData || encryptedData.trim() === '') {
        console.error('❌ Empty encrypted data provided');
        return null;
      }

      return JSON.parse(encryptedData);
    } catch (error) {
      console.error('❌ Failed to decrypt session data:', error);
      return null;
    }
  }

  /**
   * Validate SecureStore key format
   */
  private isValidSecureStoreKey(key: string): boolean {
    if (!key || key.trim() === '') {
      return false;
    }

    // SecureStore keys must contain only alphanumeric characters, ".", "-", and "_"
    const validKeyPattern = /^[a-zA-Z0-9._-]+$/;
    return validKeyPattern.test(key);
  }

  /**
   * Setup Firebase auth state listener - DISABLED to prevent logout loops
   */
  private setupAuthStateListener(): void {
    // DISABLED: This was causing logout loops when Firebase auth state changed
    console.log('🔓 Firebase auth state listener DISABLED to prevent logout loops');

    // onAuthStateChanged(auth, async (firebaseUser) => {
    //   if (!firebaseUser && this.currentSession) {
    //     // User signed out from Firebase - clear local session
    //     console.log('🔓 Firebase user signed out - clearing local session');
    //     await this.clearSession();
    //   }
    // });
  }

  /**
   * Start periodic session validation (disabled to prevent constant logouts)
   */
  private startSessionValidation(): void {
    // Disable periodic session validation to prevent constant logouts
    // This was causing users to be logged out every 5 minutes
    console.log('⚠️ Periodic session validation disabled to prevent constant logouts');

    // If we need periodic validation in the future, we can re-enable with more lenient checks
    // this.sessionCheckInterval = setInterval(async () => {
    //   if (this.currentSession) {
    //     const validation = await this.validateSession(this.currentSession);
    //     if (!validation.isValid) {
    //       console.log('🔓 Session validation failed during periodic check');
    //       await this.clearSession();
    //     }
    //   }
    // }, 30 * 60 * 1000); // Check every 30 minutes instead of 5
  }

  /**
   * Stop session validation
   */
  stopSessionValidation(): void {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
    }
  }

  /**
   * Get current session
   */
  getCurrentSession(): SessionData | null {
    return this.currentSession;
  }

  /**
   * Check if user is logged in
   */
  isLoggedIn(): boolean {
    return this.currentSession !== null && auth.currentUser !== null;
  }
}

export const authPersistenceService = AuthPersistenceService.getInstance();
