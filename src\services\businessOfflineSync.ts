/**
 * REAL Business Offline/Online Sync Service
 * Handles genuine Firebase synchronization for business operations
 */

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDocs,
  getDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  enableNetwork,
  disableNetwork
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { BusinessPost, BusinessProfile } from '../types/Business';

interface PendingOperation {
  id: string;
  type: 'CREATE_POST' | 'UPDATE_POST' | 'DELETE_POST' | 'LIKE_POST' | 'CREATE_PROFILE' | 'UPDATE_PROFILE';
  data: any;
  timestamp: number;
  retryCount: number;
}

interface SyncResult {
  success: boolean;
  synced: number;
  failed: number;
  errors: string[];
}

class BusinessOfflineSyncService {
  private readonly PENDING_OPERATIONS_KEY = 'business_pending_operations';
  private readonly CACHED_POSTS_KEY = 'business_cached_posts';
  private readonly CACHED_PROFILES_KEY = 'business_cached_profiles';
  private readonly LAST_SYNC_KEY = 'business_last_sync';
  private readonly MAX_RETRY_COUNT = 3;
  
  private isOnline = true;
  private syncInProgress = false;
  private listeners: (() => void)[] = [];

  constructor() {
    this.initializeNetworkListener();
  }

  /**
   * Add sync listener
   */
  addSyncListener(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener();
      } catch (error) {
        console.error('❌ Error in sync listener:', error);
      }
    });
  }

  // Initialize network state monitoring
  private initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected || false;
      
      if (!wasOnline && this.isOnline) {
        // Just came back online - sync pending operations
        this.syncPendingOperations();
        this.notifyListeners();
      }
      
      // Update Firestore network state
      if (this.isOnline) {
        enableNetwork(db);
      } else {
        disableNetwork(db);
      }
    });
  }

  // Create business post with offline support
  async createBusinessPost(postData: Omit<BusinessPost, 'id' | 'createdAt' | 'updatedAt'>): Promise<{
    success: boolean;
    data?: BusinessPost;
    error?: string;
  }> {
    const tempId = `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const now = new Date();
    
    const post: BusinessPost = {
      ...postData,
      id: tempId,
      createdAt: now,
      updatedAt: now,
      views: 0,
      likes: [],
      shares: 0,
      isPromoted: false,
    };

    try {
      if (this.isOnline) {
        // Online: Create in Firebase immediately
        const docRef = await addDoc(collection(db, 'businessPosts'), {
          ...postData,
          createdAt: Timestamp.fromDate(now),
          updatedAt: Timestamp.fromDate(now),
          views: 0,
          likes: [],
          shares: 0,
          isPromoted: false,
        });

        const finalPost = { ...post, id: docRef.id };
        
        // Cache the post locally
        await this.cachePost(finalPost);
        
        return { success: true, data: finalPost };
      } else {
        // Offline: Store as pending operation
        await this.addPendingOperation({
          id: tempId,
          type: 'CREATE_POST',
          data: postData,
          timestamp: Date.now(),
          retryCount: 0,
        });

        // Cache the post locally with temp ID
        await this.cachePost(post);
        
        return { success: true, data: post };
      }
    } catch (error) {
      console.error('❌ Error creating business post:', error);
      
      // If online creation failed, store as pending
      if (this.isOnline) {
        await this.addPendingOperation({
          id: tempId,
          type: 'CREATE_POST',
          data: postData,
          timestamp: Date.now(),
          retryCount: 0,
        });
        
        await this.cachePost(post);
        return { success: true, data: post };
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create post',
      };
    }
  }

  // Get business posts with offline support
  async getBusinessPosts(filters: {
    category?: string;
    businessType?: string;
    location?: string;
    priceRange?: { min: number; max: number };
    limit?: number;
    offset?: number;
  } = {}): Promise<{
    success: boolean;
    data?: BusinessPost[];
    error?: string;
  }> {
    try {
      if (this.isOnline) {
        // Online: Fetch from Firebase
        let q = query(
          collection(db, 'businessPosts'),
          orderBy('createdAt', 'desc')
        );

        if (filters.category) {
          q = query(q, where('category', '==', filters.category));
        }
        if (filters.businessType) {
          q = query(q, where('businessType', '==', filters.businessType));
        }
        if (filters.limit) {
          q = query(q, limit(filters.limit));
        }

        const snapshot = await getDocs(q);
        const posts: BusinessPost[] = [];

        snapshot.forEach((doc) => {
          const data = doc.data();
          posts.push({
            id: doc.id,
            businessId: data.businessId,
            businessName: data.businessName,
            businessLogo: data.businessLogo,
            businessType: data.businessType,
            title: data.title,
            description: data.description,
            category: data.category,
            price: data.price,
            currency: data.currency,
            isNegotiable: data.isNegotiable,
            availability: data.availability,
            status: data.status || 'available',
            location: data.location,
            contact: data.contact || {
              phone: data.contactPhone || '',
              email: data.contactEmail || ''
            },
            media: data.media || [],
            tags: data.tags || [],
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            views: data.views || 0,
            likes: data.likes || [],
            comments: data.comments || [],
            shares: data.shares || 0,
            downloads: data.downloads || 0,
            isActive: data.isActive !== undefined ? data.isActive : true,
            isPinned: data.isPinned || false,
            isPromoted: data.isPromoted || false,
            promotionEndsAt: data.promotionEndsAt?.toDate(),
            isVerified: data.isVerified || false,
          });
        });

        // Cache the posts
        await this.cachePosts(posts);
        
        return { success: true, data: posts };
      } else {
        // Offline: Load from cache
        const cachedPosts = await this.getCachedPosts();
        let filteredPosts = cachedPosts;

        // Apply filters to cached data
        if (filters.category) {
          filteredPosts = filteredPosts.filter(post => post.category === filters.category);
        }
        if (filters.businessType) {
          filteredPosts = filteredPosts.filter(post => post.businessType === filters.businessType);
        }
        if (filters.limit) {
          filteredPosts = filteredPosts.slice(0, filters.limit);
        }

        return { success: true, data: filteredPosts };
      }
    } catch (error) {
      console.error('❌ Error getting business posts:', error);
      
      // Fallback to cached data
      const cachedPosts = await this.getCachedPosts();
      return { success: true, data: cachedPosts };
    }
  }

  // Like/unlike business post with offline support
  async toggleLikePost(postId: string, userId: string): Promise<{
    success: boolean;
    error?: string;
  }> {
    try {
      if (this.isOnline) {
        // Online: Update Firebase immediately
        const postRef = doc(db, 'businessPosts', postId);
        const postDoc = await getDoc(postRef);
        
        if (!postDoc.exists()) {
          return { success: false, error: 'Post not found' };
        }

        const currentLikes = postDoc.data().likes || [];
        const isLiked = currentLikes.includes(userId);

        if (isLiked) {
          await updateDoc(postRef, {
            likes: currentLikes.filter((id: string) => id !== userId),
            updatedAt: Timestamp.now(),
          });
        } else {
          await updateDoc(postRef, {
            likes: [...currentLikes, userId],
            updatedAt: Timestamp.now(),
          });
        }

        // Update cached post
        await this.updateCachedPostLikes(postId, userId, !isLiked);
        
        return { success: true };
      } else {
        // Offline: Store as pending operation
        await this.addPendingOperation({
          id: `like_${postId}_${userId}_${Date.now()}`,
          type: 'LIKE_POST',
          data: { postId, userId },
          timestamp: Date.now(),
          retryCount: 0,
        });

        // Update cached post optimistically
        await this.updateCachedPostLikes(postId, userId, true);
        
        return { success: true };
      }
    } catch (error) {
      console.error('❌ Error toggling like:', error);
      
      // If online operation failed, store as pending
      if (this.isOnline) {
        await this.addPendingOperation({
          id: `like_${postId}_${userId}_${Date.now()}`,
          type: 'LIKE_POST',
          data: { postId, userId },
          timestamp: Date.now(),
          retryCount: 0,
        });
        
        await this.updateCachedPostLikes(postId, userId, true);
        return { success: true };
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to like post',
      };
    }
  }

  // Create business profile with offline support
  async createBusinessProfile(profileData: Omit<BusinessProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<{
    success: boolean;
    data?: BusinessProfile;
    error?: string;
  }> {
    const tempId = `temp_profile_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const now = new Date();
    
    const profile: BusinessProfile = {
      ...profileData,
      id: tempId,
      createdAt: now,
      updatedAt: now,
      isVerified: false,
      verificationDocuments: [],
      totalPosts: 0,
      totalViews: 0,
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0,
      totalDownloads: 0,
      allowDirectMessages: true,
      allowPhoneCalls: true,
      businessHours: [],
    };

    try {
      if (this.isOnline) {
        // Online: Create in Firebase immediately
        const docRef = await addDoc(collection(db, 'businessProfiles'), {
          ...profileData,
          createdAt: Timestamp.fromDate(now),
          updatedAt: Timestamp.fromDate(now),
          isVerified: false,
          rating: 0,
          reviewCount: 0,
          totalPosts: 0,
          totalViews: 0,
        });

        const finalProfile = { ...profile, id: docRef.id };
        
        // Cache the profile locally
        await this.cacheProfile(finalProfile);
        
        return { success: true, data: finalProfile };
      } else {
        // Offline: Store as pending operation
        await this.addPendingOperation({
          id: tempId,
          type: 'CREATE_PROFILE',
          data: profileData,
          timestamp: Date.now(),
          retryCount: 0,
        });

        // Cache the profile locally with temp ID
        await this.cacheProfile(profile);
        
        return { success: true, data: profile };
      }
    } catch (error) {
      console.error('❌ Error creating business profile:', error);
      
      // If online creation failed, store as pending
      if (this.isOnline) {
        await this.addPendingOperation({
          id: tempId,
          type: 'CREATE_PROFILE',
          data: profileData,
          timestamp: Date.now(),
          retryCount: 0,
        });
        
        await this.cacheProfile(profile);
        return { success: true, data: profile };
      }
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create profile',
      };
    }
  }

  // Sync pending operations when back online
  async syncPendingOperations(): Promise<SyncResult> {
    if (this.syncInProgress || !this.isOnline) {
      return { success: false, synced: 0, failed: 0, errors: ['Sync already in progress or offline'] };
    }

    this.syncInProgress = true;
    const result: SyncResult = { success: true, synced: 0, failed: 0, errors: [] };

    try {
      const pendingOps = await this.getPendingOperations();
      
      for (const op of pendingOps) {
        try {
          await this.executePendingOperation(op);
          await this.removePendingOperation(op.id);
          result.synced++;
        } catch (error) {
          console.error(`❌ Failed to sync operation ${op.id}:`, error);
          
          // Increment retry count
          op.retryCount++;
          if (op.retryCount >= this.MAX_RETRY_COUNT) {
            await this.removePendingOperation(op.id);
            result.failed++;
            result.errors.push(`Operation ${op.id} failed after ${this.MAX_RETRY_COUNT} retries`);
          } else {
            await this.updatePendingOperation(op);
          }
        }
      }

      // Update last sync timestamp
      await AsyncStorage.setItem(this.LAST_SYNC_KEY, Date.now().toString());
      
    } catch (error) {
      console.error('❌ Error during sync:', error);
      result.success = false;
      result.errors.push(error instanceof Error ? error.message : 'Unknown sync error');
    } finally {
      this.syncInProgress = false;
    }

    return result;
  }

  // Execute a pending operation
  private async executePendingOperation(op: PendingOperation): Promise<void> {
    switch (op.type) {
      case 'CREATE_POST':
        const postRef = await addDoc(collection(db, 'businessPosts'), {
          ...op.data,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
          views: 0,
          likes: [],
          shares: 0,
          isPromoted: false,
        });
        // Update cached post with real ID
        await this.updateCachedPostId(op.id, postRef.id);
        break;

      case 'CREATE_PROFILE':
        const profileRef = await addDoc(collection(db, 'businessProfiles'), {
          ...op.data,
          createdAt: Timestamp.now(),
          updatedAt: Timestamp.now(),
          isVerified: false,
          rating: 0,
          reviewCount: 0,
          totalPosts: 0,
          totalViews: 0,
        });
        // Update cached profile with real ID
        await this.updateCachedProfileId(op.id, profileRef.id);
        break;

      case 'LIKE_POST':
        const { postId, userId } = op.data;
        const postDocRef = doc(db, 'businessPosts', postId);
        const postDoc = await getDoc(postDocRef);

        if (postDoc.exists()) {
          const currentLikes = postDoc.data().likes || [];
          const isLiked = currentLikes.includes(userId);

          if (!isLiked) {
            await updateDoc(postDocRef, {
              likes: [...currentLikes, userId],
              updatedAt: Timestamp.now(),
            });
          }
        }
        break;

      default:
        throw new Error(`Unknown operation type: ${op.type}`);
    }
  }

  // Cache management methods
  private async cachePost(post: BusinessPost): Promise<void> {
    try {
      const cachedPosts = await this.getCachedPosts();
      const updatedPosts = [post, ...cachedPosts.filter(p => p.id !== post.id)];
      await AsyncStorage.setItem(this.CACHED_POSTS_KEY, JSON.stringify(updatedPosts));
    } catch (error) {
      console.error('❌ Error caching post:', error);
    }
  }

  private async cachePosts(posts: BusinessPost[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.CACHED_POSTS_KEY, JSON.stringify(posts));
    } catch (error) {
      console.error('❌ Error caching posts:', error);
    }
  }

  private async getCachedPosts(): Promise<BusinessPost[]> {
    try {
      const cached = await AsyncStorage.getItem(this.CACHED_POSTS_KEY);
      if (!cached) return [];

      const posts = JSON.parse(cached);
      return posts.map((post: any) => ({
        ...post,
        createdAt: new Date(post.createdAt),
        updatedAt: new Date(post.updatedAt),
        promotionEndsAt: post.promotionEndsAt ? new Date(post.promotionEndsAt) : undefined,
      }));
    } catch (error) {
      console.error('❌ Error getting cached posts:', error);
      return [];
    }
  }

  private async updateCachedPostLikes(postId: string, userId: string, isLiked: boolean): Promise<void> {
    try {
      const cachedPosts = await this.getCachedPosts();
      const updatedPosts = cachedPosts.map(post => {
        if (post.id === postId) {
          const currentLikes = post.likes || [];
          return {
            ...post,
            likes: isLiked
              ? [...currentLikes.filter(id => id !== userId), userId]
              : currentLikes.filter(id => id !== userId),
            updatedAt: new Date(),
          };
        }
        return post;
      });
      await AsyncStorage.setItem(this.CACHED_POSTS_KEY, JSON.stringify(updatedPosts));
    } catch (error) {
      console.error('❌ Error updating cached post likes:', error);
    }
  }

  private async updateCachedPostId(tempId: string, realId: string): Promise<void> {
    try {
      const cachedPosts = await this.getCachedPosts();
      const updatedPosts = cachedPosts.map(post =>
        post.id === tempId ? { ...post, id: realId } : post
      );
      await AsyncStorage.setItem(this.CACHED_POSTS_KEY, JSON.stringify(updatedPosts));
    } catch (error) {
      console.error('❌ Error updating cached post ID:', error);
    }
  }

  private async cacheProfile(profile: BusinessProfile): Promise<void> {
    try {
      const cachedProfiles = await this.getCachedProfiles();
      const updatedProfiles = [profile, ...cachedProfiles.filter(p => p.id !== profile.id)];
      await AsyncStorage.setItem(this.CACHED_PROFILES_KEY, JSON.stringify(updatedProfiles));
    } catch (error) {
      console.error('❌ Error caching profile:', error);
    }
  }

  private async getCachedProfiles(): Promise<BusinessProfile[]> {
    try {
      const cached = await AsyncStorage.getItem(this.CACHED_PROFILES_KEY);
      if (!cached) return [];

      const profiles = JSON.parse(cached);
      return profiles.map((profile: any) => ({
        ...profile,
        createdAt: new Date(profile.createdAt),
        updatedAt: new Date(profile.updatedAt),
      }));
    } catch (error) {
      console.error('❌ Error getting cached profiles:', error);
      return [];
    }
  }

  private async updateCachedProfileId(tempId: string, realId: string): Promise<void> {
    try {
      const cachedProfiles = await this.getCachedProfiles();
      const updatedProfiles = cachedProfiles.map(profile =>
        profile.id === tempId ? { ...profile, id: realId } : profile
      );
      await AsyncStorage.setItem(this.CACHED_PROFILES_KEY, JSON.stringify(updatedProfiles));
    } catch (error) {
      console.error('❌ Error updating cached profile ID:', error);
    }
  }

  // Pending operations management
  private async addPendingOperation(operation: PendingOperation): Promise<void> {
    try {
      const pending = await this.getPendingOperations();
      pending.push(operation);
      await AsyncStorage.setItem(this.PENDING_OPERATIONS_KEY, JSON.stringify(pending));
    } catch (error) {
      console.error('❌ Error adding pending operation:', error);
    }
  }

  private async getPendingOperations(): Promise<PendingOperation[]> {
    try {
      const pending = await AsyncStorage.getItem(this.PENDING_OPERATIONS_KEY);
      return pending ? JSON.parse(pending) : [];
    } catch (error) {
      console.error('❌ Error getting pending operations:', error);
      return [];
    }
  }

  private async removePendingOperation(operationId: string): Promise<void> {
    try {
      const pending = await this.getPendingOperations();
      const filtered = pending.filter(op => op.id !== operationId);
      await AsyncStorage.setItem(this.PENDING_OPERATIONS_KEY, JSON.stringify(filtered));
    } catch (error) {
      console.error('❌ Error removing pending operation:', error);
    }
  }

  private async updatePendingOperation(operation: PendingOperation): Promise<void> {
    try {
      const pending = await this.getPendingOperations();
      const updated = pending.map(op => op.id === operation.id ? operation : op);
      await AsyncStorage.setItem(this.PENDING_OPERATIONS_KEY, JSON.stringify(updated));
    } catch (error) {
      console.error('❌ Error updating pending operation:', error);
    }
  }

  // Public methods for external use
  async getNetworkStatus(): Promise<boolean> {
    return this.isOnline;
  }

  async getPendingOperationsCount(): Promise<number> {
    const pending = await this.getPendingOperations();
    return pending.length;
  }

  async clearCache(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        this.CACHED_POSTS_KEY,
        this.CACHED_PROFILES_KEY,
        this.PENDING_OPERATIONS_KEY,
        this.LAST_SYNC_KEY,
      ]);
    } catch (error) {
      console.error('❌ Error clearing cache:', error);
    }
  }

  async getLastSyncTime(): Promise<number | null> {
    try {
      const lastSync = await AsyncStorage.getItem(this.LAST_SYNC_KEY);
      return lastSync ? parseInt(lastSync, 10) : null;
    } catch (error) {
      console.error('❌ Error getting last sync time:', error);
      return null;
    }
  }
}

export const businessOfflineSync = new BusinessOfflineSyncService();
