// OPTIMIZED CONTACTS SERVICE - Fast loading with caching and performance improvements
import * as Contacts from "expo-contacts";
import { collection, getDocs, query, where } from "firebase/firestore";
import { Platform } from "react-native";
import { cleanContactName, deduplicateContacts, generatePlaceholderAvatar } from "../utils/avatarUtils";
import { safeTimestampToDate } from "../utils/firebaseSerializers";
import { firestore } from "./firebaseSimple";
import { onlineStatusService } from "./onlineStatusService";

export interface Contact {
  id: string;
  name: string;
  username?: string;
  phoneNumber: string;
  primaryEmail?: string;
  company?: string;
  avatar?: string;
  isIraChatUser: boolean;
  status?: string;
  lastSeen?: Date;
  bio?: string;
  userId?: string;
  isOnline?: boolean;
}

// OPTIMIZED CONTACTS SERVICE with caching and performance improvements
class OptimizedContactsService {
  private contactsCache: Contact[] | null = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_DURATION = 10 * 60 * 1000; // 10 minutes - longer cache for better performance
  private isLoading = false;
  private hasPermission = false;

  /**
   * Request contacts permission
   */
  async requestPermission(): Promise<boolean> {
    try {
      console.log("🔐 Requesting contacts permission...");

      if (Platform.OS === "web") {
        console.log("🌐 Web platform - contacts not available");
        return false;
      }

      // First check current permission status
      const { status: currentStatus } = await Contacts.getPermissionsAsync();
      console.log(`🔐 Current permission status: ${currentStatus}`);

      if (currentStatus === 'granted') {
        this.hasPermission = true;
        console.log(`🔐 Permission already granted`);
        return true;
      }

      // Request permission if not granted
      const { status } = await Contacts.requestPermissionsAsync();
      this.hasPermission = status === "granted";

      console.log(`🔐 Permission request result: ${status}`);
      console.log(`🔐 Has permission: ${this.hasPermission}`);

      if (!this.hasPermission) {
        console.warn("❌ Contacts permission denied - user needs to grant permission in settings");
      }

      return this.hasPermission;
    } catch (error) {
      console.error("❌ Error requesting contacts permission:", error);
      this.hasPermission = false;
      return false;
    }
  }

  /**
   * Normalize phone number for consistent comparison
   */
  private normalizePhoneNumber(phoneNumber: string): string {
    if (!phoneNumber) return "";

    // Remove all non-digit characters except +
    let normalized = phoneNumber.replace(/[^\d+]/g, "");

    // Handle different formats
    if (normalized.startsWith("00")) {
      normalized = "+" + normalized.substring(2);
    } else if (normalized.startsWith("0") && !normalized.startsWith("00")) {
      // Remove leading 0 for local numbers
      normalized = normalized.substring(1);
    }

    // Add country code if not present
    if (!normalized.startsWith("+")) {
      // For Uganda numbers (9 digits starting with 7)
      if (normalized.length === 9 && normalized.startsWith("7")) {
        normalized = "+256" + normalized;
      }
      // For US/Canada numbers (10 digits)
      else if (normalized.length === 10) {
        normalized = "+1" + normalized;
      }
      // For other numbers, add + if it looks like an international number
      else if (normalized.length >= 7) {
        normalized = "+" + normalized;
      }
    }

    console.log(`📞 [DEBUG] Phone normalization: "${phoneNumber}" -> "${normalized}"`);
    return normalized;
  }

  /**
   * Get phone contacts from device - OPTIMIZED
   */
  async getPhoneContacts(): Promise<Contact[]> {
    try {
      if (Platform.OS === "web") {
        console.log("🌐 Web platform - no contacts available");
        return [];
      }

      const hasPermission = await this.requestPermission();
      console.log(`📱 Contacts permission result: ${hasPermission}`);

      if (!hasPermission) {
        console.warn("❌ No contacts permission - returning empty list");
        return [];
      }

      console.log("📱 Fetching phone contacts from device...");

      const { data } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.ID,
          Contacts.Fields.Name,
          Contacts.Fields.FirstName,
          Contacts.Fields.LastName,
          Contacts.Fields.MiddleName,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
          Contacts.Fields.Image,
          Contacts.Fields.ImageAvailable,
          Contacts.Fields.Addresses,
          Contacts.Fields.Company,
          Contacts.Fields.JobTitle,
          Contacts.Fields.Department,
          Contacts.Fields.Note,
          Contacts.Fields.InstantMessageAddresses,
          Contacts.Fields.Dates,
          Contacts.Fields.Relationships,
          Contacts.Fields.SocialProfiles,
          Contacts.Fields.RawImage,
        ],
        sort: Contacts.SortTypes.FirstName,
      });

      console.log(`📱 Raw contacts fetched: ${data.length}`);

      // COMPREHENSIVE: Process contacts from ALL sources with improved filtering
      const rawContacts = data
        .filter(contact => {
          // Accept contacts with name AND (phone numbers OR emails)
          const hasName = contact.name || contact.firstName || contact.lastName;
          const hasPhone = contact.phoneNumbers && contact.phoneNumbers.length > 0;
          const hasEmail = contact.emails && contact.emails.length > 0;
          return hasName && (hasPhone || hasEmail);
        })
        .map(contact => {
          // Build comprehensive name from all available name fields
          const firstName = contact.firstName || '';
          const lastName = contact.lastName || '';
          const middleName = contact.middleName || '';
          const fullName = contact.name ||
            [firstName, middleName, lastName].filter(Boolean).join(' ').trim() ||
            'Unknown Contact';

          const cleanName = cleanContactName(fullName);

          // Get all phone numbers, not just the first one
          const phoneNumbers = contact.phoneNumbers || [];
          const primaryPhone = phoneNumbers.length > 0 ?
            this.normalizePhoneNumber(phoneNumbers[0].number || "") : '';

          // Get all email addresses
          const emails = contact.emails || [];
          let primaryEmail = emails.length > 0 ? emails[0].email || '' : '';

          // Normalize email to lowercase for consistent matching
          if (primaryEmail) {
            primaryEmail = primaryEmail.toLowerCase().trim();
          }

          // Fallback: Extract email from contact name if no email field
          if (!primaryEmail && fullName) {
            const emailMatch = fullName.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
            if (emailMatch) {
              primaryEmail = emailMatch[1].toLowerCase().trim();
              console.log(`📧 [DEBUG] Extracted email from name "${fullName}": ${primaryEmail}`);
            }
          }

          // Additional email extraction from other fields
          if (!primaryEmail) {
            // Check organization/company field
            const organization = contact.company || (contact as any).organization || '';
            if (organization) {
              const orgEmailMatch = organization.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
              if (orgEmailMatch) {
                primaryEmail = orgEmailMatch[1].toLowerCase();
                console.log(`📧 [DEBUG] Extracted email from organization "${organization}": ${primaryEmail}`);
              }
            }

            // Check notes/description field if available
            const notes = (contact as any).note || (contact as any).notes || '';
            if (notes) {
              const notesEmailMatch = notes.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
              if (notesEmailMatch) {
                primaryEmail = notesEmailMatch[1].toLowerCase().trim();
                console.log(`📧 [DEBUG] Extracted email from notes "${notes}": ${primaryEmail}`);
              }
            }
          }

          // Get company/organization info
          const company = contact.company || '';
          const jobTitle = contact.jobTitle || '';

          return {
            id: contact.id || `contact_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            name: cleanName,
            firstName,
            lastName,
            phoneNumber: primaryPhone,
            phoneNumbers: phoneNumbers.map(p => ({
              number: this.normalizePhoneNumber(p.number || ''),
              label: p.label || 'mobile'
            })),
            emails: emails.map(e => ({
              email: e.email || '',
              label: e.label || 'home'
            })),
            primaryEmail,
            company,
            jobTitle,
            isIraChatUser: false, // Will be updated with real Firebase check
            status: undefined, // Only IraChat users have status
            lastSeen: new Date(),
            avatar: contact.imageAvailable ? contact.image?.uri : generatePlaceholderAvatar(cleanName),
            hasImage: contact.imageAvailable || false,
            // Include raw contact for debugging and additional processing
            rawContact: contact,
            // Source information for debugging
            source: 'device' // Will be enhanced later to detect specific sources
          };
        })
        .filter(contact => {
          // Filter out contacts without valid phone numbers or emails
          const hasValidPhone = contact.phoneNumber && contact.phoneNumber.length > 5;
          const hasValidEmail = contact.primaryEmail && contact.primaryEmail.includes('@');
          return hasValidPhone || hasValidEmail;
        });

      // Apply deduplication and system contact filtering
      const contacts = deduplicateContacts(rawContacts);

      // Enhanced logging for debugging
      console.log(`✅ Found ${contacts.length} valid contacts from all sources`);
      console.log(`📱 Contacts with phone numbers: ${contacts.filter(c => c.phoneNumber).length}`);
      console.log(`📧 Contacts with emails: ${contacts.filter(c => c.primaryEmail).length}`);
      console.log(`🏢 Contacts with company info: ${contacts.filter(c => c.company).length}`);
      console.log(`📸 Contacts with images: ${contacts.filter(c => c.hasImage).length}`);

      // Log sample contact sources for debugging
      if (contacts.length > 0) {
        const sampleContacts = contacts.slice(0, 3);
        console.log('📋 Sample contacts:', sampleContacts.map(c => ({
          name: c.name,
          hasPhone: !!c.phoneNumber,
          hasEmail: !!c.primaryEmail,
          company: c.company,
          source: c.source
        })));
      }

      return contacts;
    } catch (error) {
      console.error("❌ Error fetching contacts:", error);
      return [];
    }
  }



  /**
   * FAST: Get IraChat contacts with caching and optimization
   */
  async getIraChatContacts(): Promise<Contact[]> {
    try {
      // Check cache first - INSTANT RETURN
      if (this.contactsCache && this.isCacheValid()) {
        console.log("⚡ Returning cached contacts (instant)");
        return this.contactsCache;
      }

      // If cache exists but expired, return it immediately and refresh in background
      if (this.contactsCache && this.contactsCache.length > 0) {
        console.log("⚡ Returning expired cache immediately, refreshing in background");
        // Refresh in background without blocking
        this.refreshContactsInBackground();
        return this.contactsCache;
      }

      // Prevent multiple simultaneous loads
      if (this.isLoading) {
        console.log("⏳ Already loading contacts...");
        // Wait for current load to complete instead of returning empty
        await new Promise(resolve => setTimeout(resolve, 100));
        return this.contactsCache || [];
      }

      this.isLoading = true;
      console.log("🚀 Fast-loading contacts...");

      // Add timeout to prevent hanging - increased for better reliability
      const timeoutPromise = new Promise<Contact[]>((_, reject) =>
        setTimeout(() => reject(new Error('Contact loading timeout')), 10000) // Increased from 3 seconds to 10 seconds
      );

      const loadPromise = this.loadContactsInternal();

      try {
        const result = await Promise.race([loadPromise, timeoutPromise]);
        // isLoading is reset inside loadContactsInternal, but ensure it's reset here too
        this.isLoading = false;
        console.log(`✅ Contact loading completed successfully: ${result.length} contacts`);
        return result;
      } catch (error) {
        console.error('❌ Contact loading failed:', error);
        this.isLoading = false;

        // Return cached contacts if available
        if (this.contactsCache && this.contactsCache.length > 0) {
          console.log(`📦 Returning ${this.contactsCache.length} cached contacts due to loading failure`);
          return this.contactsCache;
        }

        // Return empty array as last resort
        console.warn("⚠️ No cached contacts available, returning empty array");
        return [];
      }
    } catch (error) {
      console.error("❌ Error loading contacts:", error);
      this.isLoading = false;
      return this.contactsCache || [];
    }
  }

  private async loadContactsInternal(): Promise<Contact[]> {
    try {
      console.log("🔍 Starting loadContactsInternal...");

      // Get phone contacts
      const phoneContacts = await this.getPhoneContacts();
      console.log(`📱 Phone contacts loaded: ${phoneContacts.length}`);

      if (phoneContacts.length === 0) {
        console.warn("⚠️ No phone contacts found - this could be due to:");
        console.warn("  1. No contacts permission");
        console.warn("  2. No contacts on device");
        console.warn("  3. Contacts loading failed");
        this.isLoading = false;
        return [];
      }

      // OPTIMIZED: Get unique phone numbers and emails
      const uniquePhones = [...new Set(phoneContacts.map(c => c.phoneNumber).filter(Boolean))];
      const uniqueEmails = [...new Set(phoneContacts.map(c => c.primaryEmail).filter((email): email is string => Boolean(email)))];
      console.log(`🔍 Checking ${uniquePhones.length} unique phone numbers and ${uniqueEmails.length} unique emails...`);
      console.log(`🔍 [DEBUG] Sample phone numbers:`, uniquePhones.slice(0, 5));
      console.log(`🔍 [DEBUG] Sample emails:`, uniqueEmails.slice(0, 5));

      // Debug: Show email-only contacts
      const emailOnlyContacts = phoneContacts.filter(c => !c.phoneNumber && c.primaryEmail);
      console.log(`📧 [DEBUG] Found ${emailOnlyContacts.length} email-only contacts:`,
        emailOnlyContacts.slice(0, 3).map(c => `${c.name} (${c.primaryEmail})`));

      // Debug: Show all extracted emails for verification
      console.log(`📧 [DEBUG] All extracted emails (first 10):`, uniqueEmails.slice(0, 10));

      // OPTIMIZED: Use larger batches and parallel processing with timeout
      let registeredUsers = new Map();
      let onlineStatusMap = new Map();

      try {
        console.log(`🔍 [DEBUG] About to start Firebase queries - phones: ${uniquePhones.length > 0}, emails: ${uniqueEmails.length > 0}`);

        // Focus ONLY on phone number matching as requested
        console.log("🔍 [DEBUG] About to call batchCheckRegistration with", uniquePhones.length, "phone numbers");
        const phonePromise = uniquePhones.length > 0 ?
          this.batchCheckRegistration(uniquePhones).then(result => {
            console.log("✅ [DEBUG] Phone registration check completed with", result.size, "users");
            return result;
          }).catch(error => {
            console.error("❌ [DEBUG] Phone registration check failed:", error);
            return new Map();
          }) : // Process all phone numbers
          Promise.resolve(new Map());

        console.log("🔍 [DEBUG] About to call batchCheckRegistrationByEmail with", uniqueEmails.length, "emails");
        const emailPromise = uniqueEmails.length > 0 ?
          this.batchCheckRegistrationByEmail(uniqueEmails.slice(0, 50)).then(result => {
            console.log("✅ [DEBUG] Email registration check completed with", result.size, "users");
            return result;
          }).catch(error => {
            console.error("❌ [DEBUG] Email registration check failed:", error);
            return new Map();
          }) : // Keep email as backup
          Promise.resolve(new Map());

        const timeoutPromise = new Promise<[Map<string, any>, Map<string, any>]>((_, reject) =>
          setTimeout(() => {
            console.error("⏰ [DEBUG] Registration check timeout after 15 seconds - continuing with cached data");
            reject(new Error('Registration check timeout'));
          }, 15000) // Increased from 5 seconds to 15 seconds for better reliability
        );

        console.log("🔍 [DEBUG] Waiting for phone and email promises to complete...");
        const [phoneUsers, emailUsers] = await Promise.race([
          Promise.all([phonePromise, emailPromise]),
          timeoutPromise
        ]);
        console.log("✅ [DEBUG] Phone and email promises completed successfully");

        // Merge phone and email users - FOCUS ON PHONE NUMBERS
        console.log("🔍 [DEBUG] Merging phone users:", phoneUsers.size, "users");
        phoneUsers.forEach((userData: any, phone: string) => {
          console.log("🔍 [DEBUG] Adding phone user:", phone, "->", userData.userId || userData.name);
          registeredUsers.set(phone, userData);
        });

        console.log("🔍 [DEBUG] Merging email users:", emailUsers.size, "users");
        emailUsers.forEach((userData: any, email: string) => {
          console.log("🔍 [DEBUG] Adding email user:", email, "->", userData.userId || userData.name);
          registeredUsers.set(email, userData);
        });

        console.log(`✅ Found ${phoneUsers.size} users by phone, ${emailUsers.size} users by email (${registeredUsers.size} total)`);

        // Debug: Show what's in the registeredUsers map
        if (registeredUsers.size > 0) {
          console.log("🔍 [DEBUG] Final registeredUsers map contents:");
          registeredUsers.forEach((userData, key) => {
            console.log(`🔍 [DEBUG] - ${key} -> ${userData.userId || userData.name || 'unknown'}`);
          });
        } else {
          console.log("⚠️ [DEBUG] registeredUsers map is empty!");
        }

        // Get online status with timeout
        if (registeredUsers.size > 0) {
          const statusPromise = onlineStatusService.getOnlineStatusByPhoneNumbers(
            Array.from(registeredUsers.keys()).slice(0, 50) // Limit status checks
          );
          const statusTimeoutPromise = new Promise<Map<string, any>>((_, reject) =>
            setTimeout(() => reject(new Error('Status check timeout')), 5000)
          );

          onlineStatusMap = await Promise.race([statusPromise, statusTimeoutPromise]);
        }
      } catch (error) {
        console.error('⚠️ Registration/status check failed:', error);
        // Don't block the UI - continue with empty maps
        registeredUsers = new Map();
        onlineStatusMap = new Map();
        // Log the specific error for debugging
        if (error instanceof Error && error.message.includes('timeout')) {
          console.warn('🕐 Registration check timed out - this is normal and won\'t affect app functionality');
        }
      }

      // OPTIMIZED: Map contacts efficiently - FOCUS ON PHONE NUMBER MATCHING
      const iraChatContacts = phoneContacts.map(contact => {
        // PRIMARY: Check phone number match (this is the main requirement)
        const userDataByPhone = registeredUsers.get(contact.phoneNumber);
        // SECONDARY: Check email as backup
        const userDataByEmail = contact.primaryEmail ? registeredUsers.get(contact.primaryEmail) : null;

        const userData = userDataByPhone || userDataByEmail;
        const onlineStatus = onlineStatusMap.get(contact.phoneNumber) ||
                           (contact.primaryEmail ? onlineStatusMap.get(contact.primaryEmail) : null);

        // Debug logging for contact mapping - include email-only contacts
        if (contact.name && (contact.phoneNumber || contact.primaryEmail)) {
          const isRegistered = !!userData;
          const matchType = userDataByPhone ? 'phone' : userDataByEmail ? 'email' : 'none';
          console.log(`📞 [DEBUG] Contact "${contact.name}" (${contact.phoneNumber || 'no-phone'}/${contact.primaryEmail || 'no-email'}) -> IraChat user: ${isRegistered} (matched by: ${matchType})`);

          if (!isRegistered && registeredUsers.size > 0) {
            // Show available registered identifiers for comparison
            const registeredPhones = Array.from(registeredUsers.keys()).filter(key => key.startsWith('+')).slice(0, 3);
            const registeredEmails = Array.from(registeredUsers.keys()).filter(key => key.includes('@')).slice(0, 3);
            console.log(`📞 [DEBUG] Available registered phones (sample): ${registeredPhones.join(', ')}`);
            console.log(`📧 [DEBUG] Available registered emails (sample): ${registeredEmails.join(', ')}`);
          }
        }

        if (userData) {
          console.log(`✅ [DEBUG] Mapping IraChat user: ${contact.name} -> ${userData.userId}`);
          return {
            ...contact,
            isIraChatUser: true,
            userId: userData.userId,
            avatar: userData.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(contact.name)}&background=87CEEB&color=FFFFFF`,
            lastSeen: onlineStatus?.lastSeen || safeTimestampToDate(userData.lastSeen),
            isOnline: onlineStatus?.isOnline || false,
            status: userData.status || "I use IraChat",
            bio: userData.bio,
            username: userData.username,
          };
        }

        return contact;
      });

      // Sort: IraChat users first, then alphabetically
      const sortedContacts = iraChatContacts.sort((a, b) => {
        if (a.isIraChatUser && !b.isIraChatUser) return -1;
        if (!a.isIraChatUser && b.isIraChatUser) return 1;
        return a.name.localeCompare(b.name);
      });

      const registeredCount = sortedContacts.filter(c => c.isIraChatUser).length;
      console.log(`✅ Found ${registeredCount} IraChat users (${sortedContacts.length} total contacts)`);

      // Cache results
      this.contactsCache = sortedContacts;
      this.cacheTimestamp = Date.now();
      this.isLoading = false;

      return sortedContacts;
    } catch (error) {
      console.error("❌ Error loading contacts:", error);
      this.isLoading = false;
      return [];
    }
  }

  /**
   * OPTIMIZED: Batch check registration with parallel processing + DEBUG
   */
  private async batchCheckRegistration(phoneNumbers: string[]): Promise<Map<string, any>> {
    try {
      console.log("🔍 [DEBUG] Starting batch registration check...");
      console.log("🔍 [DEBUG] Input phone numbers:", phoneNumbers);
      console.log("🔍 [DEBUG] Firestore instance:", firestore ? "available" : "null/undefined");
      console.log("🔍 [DEBUG] Firestore type:", typeof firestore);

      if (!firestore) {
        console.log("❌ [DEBUG] Firestore not available - returning empty map");
        return new Map();
      }

      // Check authentication
      const { auth } = await import("./firebaseSimple");
      const currentUser = auth?.currentUser;
      console.log("🔍 [DEBUG] Current user:", currentUser ? currentUser.uid : "NOT AUTHENTICATED");
      console.log("🔍 [DEBUG] Current user phone:", currentUser?.phoneNumber);
      console.log("🔍 [DEBUG] Current user email:", currentUser?.email);

      if (!currentUser) {
        console.log("❌ [DEBUG] User not authenticated - returning empty map");
        return new Map();
      }

      // Test if users collection exists and check ALL users for phone numbers
      try {
        console.log("🔍 [DEBUG] Testing users collection access...");
        console.log("🔍 [DEBUG] Creating query for users collection...");
        const testQuery = query(collection(firestore, "users"));
        console.log("🔍 [DEBUG] Query created successfully, executing getDocs...");
        const testSnapshot = await getDocs(testQuery);
        console.log("✅ [DEBUG] Users collection accessible, found", testSnapshot.docs.length, "documents");

        if (testSnapshot.docs.length === 0) {
          console.log("⚠️ [DEBUG] Users collection is EMPTY - no registered users found");
          return new Map();
        }

        // Check ALL users to see which ones have phone numbers
        let usersWithPhones = 0;
        let usersWithoutPhones = 0;
        testSnapshot.docs.forEach((doc) => {
          const userData = doc.data();
          const hasPhone = userData.phoneNumber && userData.phoneNumber.trim() !== '';
          if (hasPhone) {
            usersWithPhones++;
            console.log(`� [DEBUG] User WITH phone ${usersWithPhones}:`, {
              id: doc.id,
              phoneNumber: userData.phoneNumber,
              email: userData.email,
              name: userData.name || userData.firstName || userData.displayName
            });
          } else {
            usersWithoutPhones++;
            if (usersWithoutPhones <= 3) { // Only log first 3 users without phones
              console.log(`❌ [DEBUG] User WITHOUT phone ${usersWithoutPhones}:`, {
                id: doc.id,
                phoneNumber: userData.phoneNumber,
                email: userData.email,
                name: userData.name || userData.firstName || userData.displayName
              });
            }
          }
        });

        console.log(`📊 [DEBUG] Phone number summary: ${usersWithPhones} users WITH phones, ${usersWithoutPhones} users WITHOUT phones`);
      } catch (testError: any) {
        console.error("❌ [DEBUG] Users collection test failed:", testError.message);
        if (testError.code === 'permission-denied') {
          console.log("🔧 [DEBUG] Permission denied - returning empty map");
          return new Map();
        }
      }

      console.log("🔍 Checking", phoneNumbers.length, "phone numbers");

      const registeredUsers = new Map();
      const batchSize = 20; // Smaller batch size for faster queries
      const promises = [];

      // Create parallel batch promises
      for (let i = 0; i < phoneNumbers.length; i += batchSize) {
        const batch = phoneNumbers.slice(i, i + batchSize);
        promises.push(this.queryUserBatch(batch));
      }

      console.log("🔍 [DEBUG] Executing", promises.length, "batch queries...");

      // Execute all batches in parallel
      const results = await Promise.all(promises);

      console.log("🔍 [DEBUG] Batch queries completed, merging results...");

      // Merge results
      results.forEach((batchMap, index) => {
        console.log(`🔍 [DEBUG] Batch ${index + 1} returned ${batchMap.size} users`);
        batchMap.forEach((userData, phone) => {
          registeredUsers.set(phone, userData);
        });
      });

      console.log("✅ [DEBUG] Total registered users found:", registeredUsers.size);
      return registeredUsers;
    } catch (error: any) {
      console.error("❌ [DEBUG] Error in batchCheckRegistration:", error);
      console.error("❌ [DEBUG] Error details:", {
        name: error?.name,
        message: error?.message,
        code: error?.code,
        stack: error?.stack
      });
      return new Map();
    }
  }

  /**
   * Query single batch of users + DEBUG
   */
  private async queryUserBatch(phoneNumbers: string[]): Promise<Map<string, any>> {
    try {
      console.log("🔍 [DEBUG] Querying batch with numbers:", phoneNumbers);
      console.log("🔍 [DEBUG] About to create Firestore query for phoneNumber field...");

      const usersQuery = query(
        collection(firestore, "users"),
        where("phoneNumber", "in", phoneNumbers)
      );

      console.log("🔍 [DEBUG] Firestore query created successfully");
      console.log("🔍 [DEBUG] Query searching for phone numbers:", phoneNumbers);
      console.log("🔍 [DEBUG] About to execute getDocs...");

      const snapshot = await getDocs(usersQuery);

      console.log("✅ [DEBUG] getDocs completed successfully!");
      console.log("✅ [DEBUG] Query successful! Found", snapshot.docs.length, "users");
      console.log("🔍 [DEBUG] Query returned", snapshot.size, "documents");
      console.log("🔍 [DEBUG] Snapshot empty?", snapshot.empty);
      console.log("🔍 [DEBUG] Snapshot metadata:", snapshot.metadata);

      if (snapshot.docs.length === 0) {
        console.log("⚠️ [DEBUG] No users found with phone numbers:", phoneNumbers);
        console.log("⚠️ [DEBUG] This might indicate a data structure mismatch");
      }

      const batchUsers = new Map();

      snapshot.forEach(doc => {
        const userData = doc.data();
        console.log("🔍 [DEBUG] Found user:", userData.phoneNumber, "->", doc.id, "name:", userData.name || userData.firstName);
        batchUsers.set(userData.phoneNumber, {
          userId: doc.id,
          ...userData,
        });
      });

      console.log("✅ [DEBUG] Batch processing complete:", batchUsers.size, "users mapped");
      return batchUsers;
    } catch (error: any) {
      console.error("❌ [DEBUG] Error in queryUserBatch:", error);
      console.error("❌ [DEBUG] Error details:", {
        name: error?.name,
        message: error?.message,
        code: error?.code
      });

      // Check if it's a permissions error specifically
      if (error?.code === 'permission-denied') {
        console.error("🚨 [DEBUG] PERMISSION DENIED - Returning empty map");
        console.error("🚨 [DEBUG] Update Firestore rules to fix this issue");

        // Return empty map - no fallback data
        console.log("🚨 Permission denied - check Firestore rules");
        return new Map();
      }

      return new Map();
    }
  }



  /**
   * Batch check registration by email addresses
   */
  private async batchCheckRegistrationByEmail(emails: string[]): Promise<Map<string, any>> {
    try {
      console.log("🔍 [DEBUG] Starting batch email registration check...");

      if (!firestore) {
        console.log("❌ [DEBUG] Firestore not available");
        return new Map();
      }

      console.log("🔍 Checking", emails.length, "email addresses");

      const registeredUsers = new Map();
      const batchSize = 20; // Smaller batch size for faster queries
      const promises = [];

      // Create parallel batch promises
      for (let i = 0; i < emails.length; i += batchSize) {
        const batch = emails.slice(i, i + batchSize);
        promises.push(this.queryUserBatchByEmail(batch));
      }

      console.log("🔍 [DEBUG] Executing", promises.length, "email batch queries...");

      // Execute all batches in parallel
      const results = await Promise.all(promises);

      console.log("🔍 [DEBUG] Email batch queries completed, merging results...");

      // Merge results
      results.forEach((batchMap, index) => {
        console.log(`🔍 [DEBUG] Email batch ${index + 1} returned ${batchMap.size} users`);
        batchMap.forEach((userData: any, email: string) => {
          registeredUsers.set(email, userData);
        });
      });

      console.log("✅ [DEBUG] Total registered users found by email:", registeredUsers.size);
      return registeredUsers;
    } catch (error: any) {
      console.error("❌ [DEBUG] Error in batchCheckRegistrationByEmail:", error);
      return new Map();
    }
  }

  /**
   * Query single batch of users by email + DEBUG
   */
  private async queryUserBatchByEmail(emails: string[]): Promise<Map<string, any>> {
    try {
      console.log("🔍 [DEBUG] Querying email batch with emails:", emails);

      const usersQuery = query(
        collection(firestore, "users"),
        where("email", "in", emails)
      );

      console.log("🔍 [DEBUG] Executing Firestore email query...");
      const snapshot = await getDocs(usersQuery);
      console.log("✅ [DEBUG] Email query successful! Found", snapshot.docs.length, "users");

      const batchUsers = new Map();

      snapshot.forEach(doc => {
        const userData = doc.data();
        console.log("🔍 [DEBUG] Found user by email:", userData.email, "->", doc.id, "name:", userData.name || userData.firstName || userData.displayName);
        batchUsers.set(userData.email, {
          userId: doc.id,
          ...userData,
        });
      });

      console.log("✅ [DEBUG] Email batch processing complete:", batchUsers.size, "users mapped");
      return batchUsers;
    } catch (error: any) {
      console.error("❌ [DEBUG] Error in queryUserBatchByEmail:", error);
      return new Map();
    }
  }

  /**
   * Check if cache is valid
   */
  private isCacheValid(): boolean {
    return this.contactsCache !== null && 
           (Date.now() - this.cacheTimestamp) < this.CACHE_DURATION;
  }

  /**
   * Clear cache and force refresh
   */
  async refreshContacts(): Promise<Contact[]> {
    console.log("🔄 Force refreshing contacts...");
    this.contactsCache = null;
    this.cacheTimestamp = 0;
    return this.getIraChatContacts();
  }

  /**
   * Get only registered IraChat contacts
   */
  async getRegisteredContacts(): Promise<Contact[]> {
    const contacts = await this.getIraChatContacts();
    return contacts.filter(contact => contact.isIraChatUser);
  }

  /**
   * Search contacts (uses cache)
   */
  async searchContacts(query: string): Promise<Contact[]> {
    const contacts = await this.getIraChatContacts();
    const searchTerm = query.toLowerCase();

    return contacts.filter(contact =>
      contact.name.toLowerCase().includes(searchTerm) ||
      contact.phoneNumber.includes(query) ||
      contact.username?.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Refresh contacts in background without blocking UI
   */
  private refreshContactsInBackground(): void {
    setTimeout(async () => {
      try {
        console.log("🔄 Background refresh started");
        this.contactsCache = null;
        this.cacheTimestamp = 0;
        await this.getIraChatContacts();
        console.log("✅ Background refresh completed");
      } catch (error) {
        console.warn("⚠️ Background refresh failed:", error);
      }
    }, 100);
  }

  /**
   * Debug method to check service status
   */
  getServiceStatus(): { hasPermission: boolean; cacheSize: number; isLoading: boolean; cacheAge: number } {
    return {
      hasPermission: this.hasPermission,
      cacheSize: this.contactsCache?.length || 0,
      isLoading: this.isLoading,
      cacheAge: Date.now() - this.cacheTimestamp
    };
  }
}

// Create optimized service instance
const optimizedContactsService = new OptimizedContactsService();

export default optimizedContactsService;
