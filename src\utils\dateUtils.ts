import { Dimensions } from 'react-native';

// Get device dimensions for responsive date formatting
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

// Date formatting interfaces
export interface DateFormatOptions {
  includeTime?: boolean;
  includeSeconds?: boolean;
  use24Hour?: boolean;
  shortFormat?: boolean;
  relative?: boolean;
}

export interface RelativeTimeOptions {
  shortFormat?: boolean;
  maxDays?: number;
  includeTime?: boolean;
}

/**
 * Enhanced message time formatting with mobile optimization
 */
export const formatMessageTime = (timestamp: any, options: DateFormatOptions = {}): string => {
  try {
    if (!timestamp) return "";

    const date = timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : timestamp.toDate
      ? timestamp.toDate()
      : new Date(timestamp);

    if (isNaN(date.getTime())) return "";

    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    const diffInMinutes = (now.getTime() - date.getTime()) / (1000 * 60);

    // Use shorter formats for small devices unless specified otherwise
    const useShortFormat = options.shortFormat ?? isSmallDevice;
    // Detect device's time format preference
    const deviceUses24Hour = (() => {
      try {
        const testDate = new Date(2023, 0, 1, 13, 0, 0);
        const timeString = testDate.toLocaleTimeString();
        const has12HourIndicator = /AM|PM|am|pm/i.test(timeString);
        return !has12HourIndicator;
      } catch {
        return false;
      }
    })();
    const use24Hour = options.use24Hour ?? deviceUses24Hour;

    if (diffInMinutes < 1) {
      return useShortFormat ? "now" : "just now";
    } else if (diffInHours < 1) {
      const minutes = Math.floor(diffInMinutes);
      return useShortFormat ? `${minutes}m` : `${minutes}m ago`;
    } else if (diffInHours < 24) {
      const timeFormat = use24Hour
        ? { hour: "2-digit", minute: "2-digit", hour12: false }
        : { hour: "2-digit", minute: "2-digit", hour12: true };
      return date.toLocaleTimeString([], timeFormat as any);
    } else if (diffInHours < 48) {
      return useShortFormat ? "1d" : "Yesterday";
    } else if (diffInHours < 168) {
      // 7 days
      return useShortFormat
        ? date.toLocaleDateString([], { weekday: "narrow" })
        : date.toLocaleDateString([], { weekday: "short" });
    } else {
      return useShortFormat
        ? date.toLocaleDateString([], { month: "numeric", day: "numeric" })
        : date.toLocaleDateString([], { month: "short", day: "numeric" });
    }
  } catch (error) {
    return "";
  }
};

/**
 * Enhanced chat time formatting with mobile optimization
 */
export const formatChatTime = (timestamp: any, options: DateFormatOptions = {}): string => {
  try {
    if (!timestamp) return "";

    const date = timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : timestamp.toDate
      ? timestamp.toDate()
      : new Date(timestamp);

    if (isNaN(date.getTime())) return "";

    const now = new Date();
    const diffInDays = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24),
    );
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    const useShortFormat = options.shortFormat ?? isSmallDevice;
    // Detect device's time format preference
    const deviceUses24Hour = (() => {
      try {
        const testDate = new Date(2023, 0, 1, 13, 0, 0);
        const timeString = testDate.toLocaleTimeString();
        const has12HourIndicator = /AM|PM|am|pm/i.test(timeString);
        return !has12HourIndicator;
      } catch {
        return false;
      }
    })();
    const use24Hour = options.use24Hour ?? deviceUses24Hour;

    if (diffInHours < 1) {
      const minutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return useShortFormat ? (minutes < 1 ? "now" : `${minutes}m`) : (minutes < 1 ? "now" : `${minutes}m ago`);
    } else if (diffInDays === 0) {
      const timeFormat = use24Hour
        ? { hour: "2-digit", minute: "2-digit", hour12: false }
        : { hour: "2-digit", minute: "2-digit", hour12: true };
      return date.toLocaleTimeString([], timeFormat as any);
    } else if (diffInDays === 1) {
      return useShortFormat ? "1d" : "Yesterday";
    } else if (diffInDays < 7) {
      return useShortFormat
        ? date.toLocaleDateString([], { weekday: "narrow" })
        : date.toLocaleDateString([], { weekday: "short" });
    } else if (diffInDays < 365) {
      return useShortFormat
        ? date.toLocaleDateString([], { month: "numeric", day: "numeric" })
        : date.toLocaleDateString([], { month: "short", day: "numeric" });
    } else {
      return useShortFormat
        ? date.toLocaleDateString([], { year: "2-digit", month: "numeric", day: "numeric" })
        : date.toLocaleDateString([], { year: "numeric", month: "short", day: "numeric" });
    }
  } catch (error) {
    return "";
  }
};

/**
 * Enhanced date comparison utilities
 */
export const isToday = (timestamp: any): boolean => {
  try {
    if (!timestamp) return false;

    const date = timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : timestamp.toDate
      ? timestamp.toDate()
      : new Date(timestamp);

    if (isNaN(date.getTime())) return false;

    const today = new Date();
    return date.toDateString() === today.toDateString();
  } catch (error) {
    return false;
  }
};

export const isYesterday = (timestamp: any): boolean => {
  try {
    if (!timestamp) return false;

    const date = timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : timestamp.toDate
      ? timestamp.toDate()
      : new Date(timestamp);

    if (isNaN(date.getTime())) return false;

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    return date.toDateString() === yesterday.toDateString();
  } catch (error) {
    return false;
  }
};

/**
 * Check if date is within the current week
 */
export const isThisWeek = (timestamp: any): boolean => {
  try {
    if (!timestamp) return false;

    const date = timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : timestamp.toDate
      ? timestamp.toDate()
      : new Date(timestamp);

    if (isNaN(date.getTime())) return false;

    const now = new Date();
    const weekStart = new Date(now);
    weekStart.setDate(now.getDate() - now.getDay());
    weekStart.setHours(0, 0, 0, 0);

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    return date >= weekStart && date <= weekEnd;
  } catch (error) {
    return false;
  }
};

/**
 * Check if date is within the current month
 */
export const isThisMonth = (timestamp: any): boolean => {
  try {
    if (!timestamp) return false;

    const date = timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : timestamp.toDate
      ? timestamp.toDate()
      : new Date(timestamp);

    if (isNaN(date.getTime())) return false;

    const now = new Date();
    return date.getMonth() === now.getMonth() && date.getFullYear() === now.getFullYear();
  } catch (error) {
    return false;
  }
};

/**
 * Check if date is within the current year
 */
export const isThisYear = (timestamp: any): boolean => {
  try {
    if (!timestamp) return false;

    const date = timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : timestamp.toDate
      ? timestamp.toDate()
      : new Date(timestamp);

    if (isNaN(date.getTime())) return false;

    const now = new Date();
    return date.getFullYear() === now.getFullYear();
  } catch (error) {
    return false;
  }
};

/**
 * Enhanced relative time formatting with mobile optimization
 */
export const formatTimeAgo = (timestamp: any, options: RelativeTimeOptions = {}): string => {
  try {
    if (!timestamp) return "now";

    const now = new Date();
    const updateTime = timestamp.toDate
      ? timestamp.toDate()
      : timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : new Date(timestamp);

    if (isNaN(updateTime.getTime())) return "now";

    const diffInSeconds = Math.floor(
      (now.getTime() - updateTime.getTime()) / 1000,
    );

    const useShortFormat = options.shortFormat ?? isSmallDevice;
    const maxDays = options.maxDays ?? 30;

    if (diffInSeconds < 0) return "now"; // Future dates
    if (diffInSeconds < 60) return "now";

    if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return useShortFormat ? `${minutes}m` : `${minutes}m ago`;
    }

    if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return useShortFormat ? `${hours}h` : `${hours}h ago`;
    }

    const days = Math.floor(diffInSeconds / 86400);
    if (days <= maxDays) {
      if (days === 1) {
        return useShortFormat ? "1d" : "1 day ago";
      } else if (days < 7) {
        return useShortFormat ? `${days}d` : `${days} days ago`;
      } else if (days < 30) {
        const weeks = Math.floor(days / 7);
        return useShortFormat ? `${weeks}w` : weeks === 1 ? "1 week ago" : `${weeks} weeks ago`;
      } else {
        const months = Math.floor(days / 30);
        return useShortFormat ? `${months}mo` : months === 1 ? "1 month ago" : `${months} months ago`;
      }
    }

    // For dates beyond maxDays, show actual date
    if (options.includeTime) {
      return updateTime.toLocaleDateString([], {
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
      });
    } else {
      return updateTime.toLocaleDateString([], {
        month: "short",
        day: "numeric",
        year: isThisYear(updateTime) ? undefined : "numeric"
      });
    }
  } catch (error) {
    return "now";
  }
};

/**
 * Enhanced date formatting with mobile optimization
 */
export const formatDate = (date: Date, options: DateFormatOptions = {}): string => {
  try {
    if (!date || isNaN(date.getTime())) return "";

    const useShortFormat = options.shortFormat ?? isSmallDevice;

    if (useShortFormat) {
      return date.toLocaleDateString([], {
        year: "2-digit",
        month: "short",
        day: "numeric",
      });
    } else {
      return date.toLocaleDateString([], {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }
  } catch (error) {
    return "";
  }
};

/**
 * Enhanced time formatting with mobile optimization
 */
export const formatTime = (date: Date, options: DateFormatOptions = {}): string => {
  try {
    if (!date || isNaN(date.getTime())) return "";

    // Detect device's time format preference
    const deviceUses24Hour = (() => {
      try {
        const testDate = new Date(2023, 0, 1, 13, 0, 0);
        const timeString = testDate.toLocaleTimeString();
        const has12HourIndicator = /AM|PM|am|pm/i.test(timeString);
        return !has12HourIndicator;
      } catch {
        return false;
      }
    })();
    const use24Hour = options.use24Hour ?? deviceUses24Hour;
    const includeSeconds = options.includeSeconds ?? false;

    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: !use24Hour,
    };

    if (includeSeconds) {
      timeOptions.second = "2-digit";
    }

    return date.toLocaleTimeString([], timeOptions);
  } catch (error) {
    return "";
  }
};

/**
 * Format date and time together
 */
export const formatDateTime = (date: Date, options: DateFormatOptions = {}): string => {
  try {
    if (!date || isNaN(date.getTime())) return "";

    const useShortFormat = options.shortFormat ?? isSmallDevice;
    // Detect device's time format preference
    const deviceUses24Hour = (() => {
      try {
        const testDate = new Date(2023, 0, 1, 13, 0, 0);
        const timeString = testDate.toLocaleTimeString();
        const has12HourIndicator = /AM|PM|am|pm/i.test(timeString);
        return !has12HourIndicator;
      } catch {
        return false;
      }
    })();
    const use24Hour = options.use24Hour ?? deviceUses24Hour;

    if (useShortFormat) {
      return date.toLocaleDateString([], {
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: !use24Hour,
      });
    } else {
      return date.toLocaleDateString([], {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: !use24Hour,
      });
    }
  } catch (error) {
    return "";
  }
};

/**
 * Enhanced call time formatting with mobile optimization
 */
export const formatCallTime = (timestamp: any, options: RelativeTimeOptions = {}): string => {
  try {
    if (!timestamp) return '';

    const date = timestamp?.toDate
      ? timestamp.toDate()
      : timestamp.seconds
      ? new Date(timestamp.seconds * 1000)
      : new Date(timestamp);

    if (isNaN(date.getTime())) return '';

    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    const useShortFormat = options.shortFormat ?? isSmallDevice;

    if (diffInMinutes < 1) {
      return "now";
    } else if (diffInMinutes < 60) {
      return useShortFormat ? `${diffInMinutes}m` : `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return useShortFormat ? `${diffInHours}h` : `${diffInHours}h ago`;
    } else if (diffInDays < 7) {
      return useShortFormat ? `${diffInDays}d` : `${diffInDays}d ago`;
    } else if (isThisYear(date)) {
      return date.toLocaleDateString([], { month: "short", day: "numeric" });
    } else {
      return date.toLocaleDateString([], {
        year: useShortFormat ? "2-digit" : "numeric",
        month: "short",
        day: "numeric"
      });
    }
  } catch (error) {
    return '';
  }
};

/**
 * Enhanced call duration formatting with mobile optimization
 */
export const formatCallDuration = (seconds: number, options: { shortFormat?: boolean } = {}): string => {
  try {
    if (seconds < 0) return "0s";

    const useShortFormat = options.shortFormat ?? isSmallDevice;

    if (seconds < 60) {
      return useShortFormat ? `${seconds}s` : `${seconds} sec`;
    }

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes < 60) {
      if (useShortFormat) {
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
      } else {
        return remainingSeconds > 0
          ? `${minutes}m ${remainingSeconds}s`
          : `${minutes}m`;
      }
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (useShortFormat) {
      return `${hours}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      const parts = [`${hours}h`];
      if (remainingMinutes > 0) parts.push(`${remainingMinutes}m`);
      if (remainingSeconds > 0 && hours === 0) parts.push(`${remainingSeconds}s`);
      return parts.join(' ');
    }
  } catch (error) {
    return "0s";
  }
};

/**
 * Get day of week name
 */
export const getDayName = (date: Date, shortFormat?: boolean): string => {
  try {
    if (!date || isNaN(date.getTime())) return "";

    const useShortFormat = shortFormat ?? isSmallDevice;
    return date.toLocaleDateString([], {
      weekday: useShortFormat ? "short" : "long"
    });
  } catch (error) {
    return "";
  }
};

/**
 * Get month name
 */
export const getMonthName = (date: Date, shortFormat?: boolean): string => {
  try {
    if (!date || isNaN(date.getTime())) return "";

    const useShortFormat = shortFormat ?? isSmallDevice;
    return date.toLocaleDateString([], {
      month: useShortFormat ? "short" : "long"
    });
  } catch (error) {
    return "";
  }
};

/**
 * Format date for message grouping
 */
export const formatMessageGroupDate = (date: Date): string => {
  try {
    if (!date || isNaN(date.getTime())) return "";

    if (isToday(date)) {
      return "Today";
    } else if (isYesterday(date)) {
      return "Yesterday";
    } else if (isThisWeek(date)) {
      return getDayName(date, false);
    } else if (isThisYear(date)) {
      return date.toLocaleDateString([], { month: "long", day: "numeric" });
    } else {
      return date.toLocaleDateString([], {
        year: "numeric",
        month: "long",
        day: "numeric"
      });
    }
  } catch (error) {
    return "";
  }
};

/**
 * Calculate age from birthdate
 */
export const calculateAge = (birthDate: Date): number => {
  try {
    if (!birthDate || isNaN(birthDate.getTime())) return 0;

    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return Math.max(0, age);
  } catch (error) {
    return 0;
  }
};

/**
 * Get time zone offset string
 */
export const getTimeZoneOffset = (): string => {
  try {
    const offset = new Date().getTimezoneOffset();
    const hours = Math.floor(Math.abs(offset) / 60);
    const minutes = Math.abs(offset) % 60;
    const sign = offset <= 0 ? '+' : '-';

    return `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  } catch (error) {
    return "+00:00";
  }
};

/**
 * Parse various timestamp formats
 */
export const parseTimestamp = (timestamp: any): Date | null => {
  try {
    if (!timestamp) return null;

    // Firebase Timestamp
    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }

    // Firebase Timestamp with seconds
    if (timestamp.seconds) {
      return new Date(timestamp.seconds * 1000);
    }

    // Regular Date object
    if (timestamp instanceof Date) {
      return isNaN(timestamp.getTime()) ? null : timestamp;
    }

    // Number (milliseconds)
    if (typeof timestamp === 'number') {
      return new Date(timestamp);
    }

    // String
    if (typeof timestamp === 'string') {
      const parsed = new Date(timestamp);
      return isNaN(parsed.getTime()) ? null : parsed;
    }

    return null;
  } catch (error) {
    return null;
  }
};

/**
 * Check if timestamp is valid
 */
export const isValidTimestamp = (timestamp: any): boolean => {
  return parseTimestamp(timestamp) !== null;
};

/**
 * Get start of day
 */
export const getStartOfDay = (date: Date): Date => {
  try {
    const start = new Date(date);
    start.setHours(0, 0, 0, 0);
    return start;
  } catch (error) {
    return new Date();
  }
};

/**
 * Get end of day
 */
export const getEndOfDay = (date: Date): Date => {
  try {
    const end = new Date(date);
    end.setHours(23, 59, 59, 999);
    return end;
  } catch (error) {
    return new Date();
  }
};

/**
 * Get days between two dates
 */
export const getDaysBetween = (date1: Date, date2: Date): number => {
  try {
    const diffTime = Math.abs(date2.getTime() - date1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  } catch (error) {
    return 0;
  }
};
