import * as ImagePicker from "expo-image-picker";
import { useState } from "react";
import {
  Alert,
  Image,
  Modal,
  Text,
  TouchableOpacity,
  View,
  StyleSheet
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS } from "../../styles/iraChatDesignSystem";
import { ResponsiveScale, ResponsiveSpacing } from "../../utils/responsiveUtils";

interface ProfilePicturePickerProps {
  currentImage?: string;
  onImageSelect: (_imageUrl: string) => void;
  size?: number;
}

export default function ProfilePicturePicker({
  currentImage,
  onImageSelect,
  size = 100,
}: ProfilePicturePickerProps) {
  const [showModal, setShowModal] = useState(false);

  // No mock images - users must upload their own photos

  const handleImageSelect = (imageUrl: string) => {
    onImageSelect(imageUrl);
    setShowModal(false);
  };

  const removeImage = () => {
    onImageSelect("");
    setShowModal(false);
  };

  const handleCameraCapture = async () => {
    try {
      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Camera permission is required to take photos. Please enable it in your device settings.",
          [{ text: "OK" }],
        );
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ["images"],
        allowsEditing: true,
        aspect: [1, 1], // Square aspect ratio
        quality: 1,
        // Note: The "CROP" button text is from native iOS/Android interface
        // and cannot be customized in Expo ImagePicker
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        handleImageSelect(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert("Error", "Failed to open camera. Please try again.");
    }
  };

  const handleGallerySelect = async () => {
    try {
      // Request media library permissions
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Gallery permission is required to select photos. Please enable it in your device settings.",
          [{ text: "OK" }],
        );
        return;
      }

      // Launch image library
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ["images"],
        allowsEditing: true,
        aspect: [1, 1], // Square aspect ratio
        quality: 1,
        // Note: Expo ImagePicker doesn't support custom button text
        // The "CROP" text is from the native iOS/Android interface
        // This is expected behavior and cannot be customized
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        handleImageSelect(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert("Error", "Failed to open gallery. Please try again.");
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        onPress={() => setShowModal(true)}
        style={[styles.imageContainer, { width: size, height: size, borderRadius: size / 2 }]}
      >
        {currentImage ? (
          <View style={[styles.imageWrapper, { width: size, height: size, borderRadius: size / 2 }]}>
            <Image
              source={{ uri: currentImage }}
              style={[styles.image, {
                width: size - 8,
                height: size - 8,
                borderRadius: (size - 8) / 2
              }]}
              resizeMode="cover"
            />
          </View>
        ) : (
          <View
            style={[styles.placeholderContainer, { width: size, height: size, borderRadius: size / 2 }]}
          >
            <Ionicons name="person" size={size * 0.4} color={IRACHAT_COLORS.textMuted} />
            <Text style={styles.placeholderText}>Add Photo</Text>
          </View>
        )}

        {/* Edit overlay */}
        <View style={styles.editOverlay}>
          <Ionicons name="camera" size={16} color="white" />
        </View>
      </TouchableOpacity>

      {/* Image Selection Modal */}
      <Modal
        visible={showModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowModal(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          justifyContent: 'flex-end'
        }}>
          <View style={{
            backgroundColor: 'white',
            borderTopLeftRadius: 24,
            borderTopRightRadius: 24,
            padding: 24,
            maxHeight: '60%'
          }}>
            <Text style={{
              fontSize: 20,
              textAlign: 'center',
              marginBottom: 24,
              fontWeight: '700',
              color: '#1F2937'
            }}>
              Choose Profile Picture
            </Text>

            {/* Camera and Gallery Options */}
            <View style={styles.optionsContainer}>
              <TouchableOpacity
                onPress={handleCameraCapture}
                style={styles.optionButton}
              >
                <Ionicons name="camera" size={24} color="white" style={styles.optionIcon} />
                <View style={styles.optionTextContainer}>
                  <Text style={styles.optionTitle}>
                    Take Photo
                  </Text>
                  <Text style={styles.optionSubtitle}>
                    Use camera.
                  </Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={handleGallerySelect}
                style={styles.optionButton}
              >
                <Ionicons name="images" size={24} color="white" style={styles.optionIcon} />
                <View style={styles.optionTextContainer}>
                  <Text style={styles.optionTitle}>
                    Choose from Gallery
                  </Text>
                  <Text style={styles.optionSubtitle}>
                    Select an existing photo from your device
                  </Text>
                </View>
              </TouchableOpacity>
            </View>

            {/* Note: Avatar options removed - users must upload their own photos */}

            <View style={styles.buttonContainer}>
              {currentImage && (
                <TouchableOpacity
                  onPress={removeImage}
                  style={styles.removeButton}
                >
                  <Text style={styles.removeButtonText}>
                    Remove Photo
                  </Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                onPress={() => setShowModal(false)}
                style={styles.cancelButton}
              >
                <Text style={styles.cancelButtonText}>
                  Cancel
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  imageContainer: {
    position: 'relative',
    borderWidth: 4,
    borderColor: IRACHAT_COLORS.primary,
    backgroundColor: IRACHAT_COLORS.background,
    overflow: 'hidden',
  },
  imageWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  image: {
    backgroundColor: IRACHAT_COLORS.backgroundDark,
  },
  placeholderContainer: {
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.border,
    borderStyle: 'dashed',
  },
  placeholderText: {
    fontSize: ResponsiveScale.fontScale(10),
    fontFamily: TYPOGRAPHY.fontFamily,
    color: IRACHAT_COLORS.textMuted,
    marginTop: ResponsiveSpacing.xs,
    textAlign: 'center',
  },
  editOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
  },
  optionsContainer: {
    gap: ResponsiveSpacing.md,
  },
  optionButton: {
    backgroundColor: IRACHAT_COLORS.primary,
    paddingHorizontal: ResponsiveSpacing.lg,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: BORDER_RADIUS.lg,
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    marginRight: ResponsiveSpacing.sm,
  },
  optionTextContainer: {
    flex: 1,
  },
  optionTitle: {
    color: 'white',
    fontSize: ResponsiveScale.fontScale(16),
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  optionSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: ResponsiveScale.fontScale(14),
    fontFamily: TYPOGRAPHY.fontFamily,
    marginTop: 2,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: ResponsiveSpacing.lg,
    gap: ResponsiveSpacing.md,
  },
  removeButton: {
    backgroundColor: IRACHAT_COLORS.error,
    paddingHorizontal: ResponsiveSpacing.lg,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: BORDER_RADIUS.lg,
    minWidth: 120,
    alignItems: 'center',
    shadowColor: IRACHAT_COLORS.error,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  removeButtonText: {
    color: 'white',
    fontSize: ResponsiveScale.fontScale(14),
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  cancelButton: {
    backgroundColor: IRACHAT_COLORS.textSecondary,
    paddingHorizontal: ResponsiveSpacing.lg,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: BORDER_RADIUS.lg,
    minWidth: 120,
    alignItems: 'center',
    shadowColor: IRACHAT_COLORS.textSecondary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  cancelButtonText: {
    color: 'white',
    fontSize: ResponsiveScale.fontScale(14),
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
});
