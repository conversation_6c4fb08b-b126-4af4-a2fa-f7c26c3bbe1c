# PowerShell script to set complete development environment variables for F: drive
Write-Host "Setting Complete Development Environment for F: Drive..." -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green

# Function to add path to environment if not already present
function Add-ToPath {
    param(
        [string]$PathToAdd,
        [string]$Description
    )

    if (Test-Path $PathToAdd) {
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)
        if ($currentPath -notlike "*$PathToAdd*") {
            $newPath = "$currentPath;$PathToAdd"
            [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::User)
            Write-Host "✅ Added to PATH: $Description ($PathToAdd)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✓ Already in PATH: $Description ($PathToAdd)" -ForegroundColor Gray
            return $false
        }
    } else {
        Write-Host "⚠️  Path not found, skipping: $Description ($PathToAdd)" -ForegroundColor Yellow
        return $false
    }
}

# 1. JAVA SETUP
Write-Host "`n🔧 Setting up Java..." -ForegroundColor Cyan
$javaPath = "F:\IraChat\java17\jdk-17.0.12+7"
if (Test-Path $javaPath) {
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath, [EnvironmentVariableTarget]::User)
    Write-Host "✅ Set JAVA_HOME = $javaPath" -ForegroundColor Green
    Add-ToPath "$javaPath\bin" "Java JDK 17"
} else {
    Write-Host "❌ Java 17 not found at: $javaPath" -ForegroundColor Red
}

# 2. ANDROID SDK SETUP
Write-Host "`n🤖 Setting up Android SDK..." -ForegroundColor Cyan
$possibleSdkPaths = @(
    "F:\Android\Sdk",
    "F:\android\sdk",
    "F:\AndroidSDK",
    "F:\android-sdk",
    "F:\SDK\Android",
    "F:\Tools\Android\Sdk",
    "F:\Development\Android\Sdk"
)

$sdkPath = $null
foreach ($path in $possibleSdkPaths) {
    if (Test-Path $path) {
        $sdkPath = $path
        Write-Host "✅ Found Android SDK at: $sdkPath" -ForegroundColor Green
        break
    }
}

if ($sdkPath) {
    [Environment]::SetEnvironmentVariable("ANDROID_HOME", $sdkPath, [EnvironmentVariableTarget]::User)
    [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $sdkPath, [EnvironmentVariableTarget]::User)
    Write-Host "✅ Set ANDROID_HOME = $sdkPath" -ForegroundColor Green

    # Add Android tools to PATH
    Add-ToPath "$sdkPath\platform-tools" "Android Platform Tools (ADB)"
    Add-ToPath "$sdkPath\cmdline-tools\latest\bin" "Android Command Line Tools"
    Add-ToPath "$sdkPath\tools" "Android Tools"
    Add-ToPath "$sdkPath\tools\bin" "Android Tools Bin"
    Add-ToPath "$sdkPath\emulator" "Android Emulator"
    Add-ToPath "$sdkPath\build-tools" "Android Build Tools"
} else {
    Write-Host "❌ Android SDK not found in common F: drive locations." -ForegroundColor Red
}

# 3. NODE.JS SETUP
Write-Host "`n📦 Setting up Node.js..." -ForegroundColor Cyan
$possibleNodePaths = @(
    "F:\nodejs",
    "F:\Node",
    "F:\Tools\nodejs",
    "F:\Development\nodejs",
    "F:\Program Files\nodejs"
)

$nodePath = $null
foreach ($path in $possibleNodePaths) {
    if (Test-Path "$path\node.exe") {
        $nodePath = $path
        Write-Host "✅ Found Node.js at: $nodePath" -ForegroundColor Green
        Add-ToPath $nodePath "Node.js"
        break
    }
}

if (-not $nodePath) {
    Write-Host "⚠️  Node.js not found in common F: drive locations" -ForegroundColor Yellow
    Write-Host "   Checking if Node.js is already in system PATH..." -ForegroundColor Gray
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Host "✅ Node.js is available in system PATH: $nodeVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Node.js not found. Please install Node.js." -ForegroundColor Red
    }
}

# 4. EXPO CLI SETUP
Write-Host "`n🚀 Setting up Expo CLI..." -ForegroundColor Cyan
try {
    $expoVersion = npx expo --version 2>$null
    if ($expoVersion) {
        Write-Host "✅ Expo CLI is available: $expoVersion" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️  Expo CLI not found. Installing globally..." -ForegroundColor Yellow
    npm install -g @expo/cli
}

# 5. ADDITIONAL TOOLS SETUP
Write-Host "`n🛠️  Setting up additional tools..." -ForegroundColor Cyan
$possibleToolPaths = @(
    "F:\Tools",
    "F:\Development\Tools",
    "F:\SDK",
    "F:\bin"
)

foreach ($toolPath in $possibleToolPaths) {
    if (Test-Path $toolPath) {
        Add-ToPath $toolPath "Additional Tools"
    }
}

# 6. VERIFICATION
Write-Host "`n🔍 Verifying setup..." -ForegroundColor Cyan

# Test Java
try {
    $javaVersion = java -version 2>&1 | Select-Object -First 1
    Write-Host "✅ Java: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java not working" -ForegroundColor Red
}

# Test Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not working" -ForegroundColor Red
}

# Test npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm: v$npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not working" -ForegroundColor Red
}

# Test ADB
if ($sdkPath) {
    try {
        $adbVersion = adb version 2>&1 | Select-Object -First 1
        Write-Host "✅ ADB: $adbVersion" -ForegroundColor Green
    } catch {
        Write-Host "❌ ADB not working" -ForegroundColor Red
    }
}

Write-Host "`n🎉 Environment setup completed!" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green

if ($sdkPath) {
    Write-Host "📍 ANDROID_HOME = $sdkPath" -ForegroundColor Cyan
    Write-Host "📍 ANDROID_SDK_ROOT = $sdkPath" -ForegroundColor Cyan
}
Write-Host "📍 JAVA_HOME = $javaPath" -ForegroundColor Cyan

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Close and restart your terminal/PowerShell" -ForegroundColor White
Write-Host "2. Run: npm install (if not done already)" -ForegroundColor White
Write-Host "3. Run: npx expo start" -ForegroundColor White
Write-Host "4. Press 'a' to open Android" -ForegroundColor White

Write-Host "`n🔧 To verify the setup works:" -ForegroundColor Yellow
Write-Host "   java -version" -ForegroundColor White
Write-Host "   node --version" -ForegroundColor White
Write-Host "   npm --version" -ForegroundColor White
Write-Host "   adb version" -ForegroundColor White
Write-Host "   echo `$env:ANDROID_HOME" -ForegroundColor White
Write-Host "   echo `$env:JAVA_HOME" -ForegroundColor White

Write-Host "`n⚠️  IMPORTANT: You must restart your terminal for changes to take effect!" -ForegroundColor Red
Write-Host ""

Read-Host "Press Enter to exit"
