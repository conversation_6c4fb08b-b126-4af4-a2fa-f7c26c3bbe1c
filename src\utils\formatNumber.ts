import { Dimensions } from 'react-native';

// Get device dimensions for responsive number formatting
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

// Number formatting options interface
export interface NumberFormatOptions {
  precision?: number;
  useShortFormat?: boolean;
  showSign?: boolean;
  locale?: string;
  currency?: string;
  showDecimal?: boolean;
  maxLength?: number;
}

// Enhanced number formatting with mobile optimization
export const formatNumber = (num: number, options: NumberFormatOptions = {}): string => {
  try {
    if (typeof num !== 'number' || isNaN(num)) {
      return '0';
    }

    const {
      precision = 1,
      useShortFormat = isSmallDevice,
      showSign = false,
      showDecimal = true,
      maxLength = isSmallDevice ? 6 : 10
    } = options;

    const absNum = Math.abs(num);
    const sign = num < 0 ? '-' : (showSign && num > 0 ? '+' : '');

    // Use shorter formats for small devices or when requested
    if (useShortFormat) {
      if (absNum >= 1000000000) {
        const formatted = (absNum / 1000000000).toFixed(precision);
        return `${sign}${removeTrailingZeros(formatted)}B`;
      }
      if (absNum >= 1000000) {
        const formatted = (absNum / 1000000).toFixed(precision);
        return `${sign}${removeTrailingZeros(formatted)}M`;
      }
      if (absNum >= 1000) {
        const formatted = (absNum / 1000).toFixed(precision);
        return `${sign}${removeTrailingZeros(formatted)}K`;
      }
    } else {
      // Full format for larger devices
      if (absNum >= 1000000000) {
        const formatted = (absNum / 1000000000).toFixed(precision);
        return `${sign}${removeTrailingZeros(formatted)} billion`;
      }
      if (absNum >= 1000000) {
        const formatted = (absNum / 1000000).toFixed(precision);
        return `${sign}${removeTrailingZeros(formatted)} million`;
      }
      if (absNum >= 1000) {
        const formatted = (absNum / 1000).toFixed(precision);
        return `${sign}${removeTrailingZeros(formatted)} thousand`;
      }
    }

    // For numbers less than 1000
    let result: string;
    if (showDecimal && absNum % 1 !== 0) {
      result = absNum.toFixed(precision);
      result = removeTrailingZeros(result);
    } else {
      result = Math.floor(absNum).toString();
    }

    // Truncate if too long
    if (result.length > maxLength) {
      result = result.substring(0, maxLength - 1) + '…';
    }

    return `${sign}${result}`;
  } catch (error) {
    return '0';
  }
};

// Remove trailing zeros from decimal numbers
const removeTrailingZeros = (numStr: string): string => {
  if (numStr.includes('.')) {
    return numStr.replace(/\.?0+$/, '');
  }
  return numStr;
};

/**
 * Format currency with mobile optimization
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'USD',
  options: NumberFormatOptions = {}
): string => {
  try {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return '$0';
    }

    const {
      precision = 2,
      useShortFormat = isSmallDevice,
      locale = 'en-US'
    } = options;

    // Use Intl.NumberFormat for proper currency formatting
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    });

    // For large amounts on small devices, use short format
    if (useShortFormat && Math.abs(amount) >= 1000) {
      const shortAmount = formatNumber(amount, {
        ...options,
        useShortFormat: true
      });
      const symbol = getCurrencySymbol(currency);
      return `${symbol}${shortAmount.replace(/^[+-]/, '')}`;
    }

    return formatter.format(amount);
  } catch (error) {
    // Fallback formatting
    const symbol = getCurrencySymbol(currency);
    return `${symbol}${formatNumber(amount, options)}`;
  }
};

/**
 * Get currency symbol
 */
const getCurrencySymbol = (currency: string): string => {
  const symbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    JPY: '¥',
    CNY: '¥',
    INR: '₹',
    KRW: '₩',
    BRL: 'R$',
    CAD: 'C$',
    AUD: 'A$',
  };
  return symbols[currency.toUpperCase()] || currency;
};

/**
 * Format percentage with mobile optimization
 */
export const formatPercentage = (
  value: number,
  options: NumberFormatOptions = {}
): string => {
  try {
    if (typeof value !== 'number' || isNaN(value)) {
      return '0%';
    }

    const {
      precision = isSmallDevice ? 0 : 1,
      showSign = false,
      maxLength = isSmallDevice ? 5 : 8
    } = options;

    const percentage = value * 100;
    const sign = percentage < 0 ? '-' : (showSign && percentage > 0 ? '+' : '');
    const absPercentage = Math.abs(percentage);

    let result: string;
    if (absPercentage >= 1000 && isSmallDevice) {
      // Use K notation for very large percentages on small devices
      result = formatNumber(absPercentage, { useShortFormat: true, precision });
    } else {
      result = absPercentage.toFixed(precision);
      result = removeTrailingZeros(result);
    }

    // Truncate if too long
    if (result.length > maxLength - 1) { // -1 for % symbol
      result = result.substring(0, maxLength - 2) + '…';
    }

    return `${sign}${result}%`;
  } catch (error) {
    return '0%';
  }
};

/**
 * Format file size with appropriate units
 */
export const formatFileSize = (bytes: number, options: NumberFormatOptions = {}): string => {
  try {
    if (typeof bytes !== 'number' || isNaN(bytes) || bytes < 0) {
      return '0 B';
    }

    const { precision = 1, useShortFormat = isSmallDevice } = options;
    const units = useShortFormat
      ? ['B', 'KB', 'MB', 'GB', 'TB']
      : ['bytes', 'KB', 'MB', 'GB', 'TB'];

    if (bytes === 0) return `0 ${units[0]}`;

    const k = 1024;
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const size = bytes / Math.pow(k, i);

    let formattedSize: string;
    if (i === 0) {
      formattedSize = bytes.toString();
    } else {
      formattedSize = removeTrailingZeros(size.toFixed(precision));
    }

    return `${formattedSize} ${units[i]}`;
  } catch (error) {
    return '0 B';
  }
};

/**
 * Format duration in seconds to human readable format
 */
export const formatDuration = (seconds: number, options: NumberFormatOptions = {}): string => {
  try {
    if (typeof seconds !== 'number' || isNaN(seconds) || seconds < 0) {
      return '0s';
    }

    const { useShortFormat = isSmallDevice } = options;

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    const parts: string[] = [];

    if (useShortFormat) {
      if (hours > 0) parts.push(`${hours}h`);
      if (minutes > 0) parts.push(`${minutes}m`);
      if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}s`);
    } else {
      if (hours > 0) parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
      if (minutes > 0) parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
      if (remainingSeconds > 0 || parts.length === 0) {
        parts.push(`${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`);
      }
    }

    // Limit parts for small devices
    if (isSmallDevice && parts.length > 2) {
      return parts.slice(0, 2).join(' ');
    }

    return parts.join(' ');
  } catch (error) {
    return '0s';
  }
};

/**
 * Format count with ordinal suffix (1st, 2nd, 3rd, etc.)
 */
export const formatOrdinal = (num: number): string => {
  try {
    if (typeof num !== 'number' || isNaN(num)) {
      return '0th';
    }

    const absNum = Math.abs(Math.floor(num));
    const lastDigit = absNum % 10;
    const lastTwoDigits = absNum % 100;

    let suffix: string;
    if (lastTwoDigits >= 11 && lastTwoDigits <= 13) {
      suffix = 'th';
    } else if (lastDigit === 1) {
      suffix = 'st';
    } else if (lastDigit === 2) {
      suffix = 'nd';
    } else if (lastDigit === 3) {
      suffix = 'rd';
    } else {
      suffix = 'th';
    }

    return `${absNum}${suffix}`;
  } catch (error) {
    return '0th';
  }
};

/**
 * Format number with locale-specific formatting
 */
export const formatLocaleNumber = (
  num: number,
  locale: string = 'en-US',
  options: NumberFormatOptions = {}
): string => {
  try {
    if (typeof num !== 'number' || isNaN(num)) {
      return '0';
    }

    const { precision, useShortFormat } = options;

    // Use short format for large numbers on small devices
    if (useShortFormat && Math.abs(num) >= 1000) {
      return formatNumber(num, options);
    }

    // Use Intl.NumberFormat for locale-specific formatting
    const formatter = new Intl.NumberFormat(locale, {
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    });

    return formatter.format(num);
  } catch (error) {
    return formatNumber(num, options);
  }
};

/**
 * Parse formatted number string back to number
 */
export const parseFormattedNumber = (formattedNum: string): number => {
  try {
    if (!formattedNum || typeof formattedNum !== 'string') {
      return 0;
    }

    // Remove common formatting characters
    let cleaned = formattedNum
      .replace(/[,\s]/g, '') // Remove commas and spaces
      .replace(/[^\d.-]/g, ''); // Keep only digits, dots, and minus

    // Handle special suffixes
    const original = formattedNum.toLowerCase();
    let multiplier = 1;

    if (original.includes('k')) {
      multiplier = 1000;
      cleaned = cleaned.replace(/k/gi, '');
    } else if (original.includes('m')) {
      multiplier = 1000000;
      cleaned = cleaned.replace(/m/gi, '');
    } else if (original.includes('b')) {
      multiplier = 1000000000;
      cleaned = cleaned.replace(/b/gi, '');
    }

    const parsed = parseFloat(cleaned);
    return isNaN(parsed) ? 0 : parsed * multiplier;
  } catch (error) {
    return 0;
  }
};

/**
 * Get responsive precision based on device size
 */
export const getResponsivePrecision = (baseValue: number): number => {
  if (isTablet) return Math.min(baseValue + 1, 3); // More precision on tablets
  if (isSmallDevice) return Math.max(baseValue - 1, 0); // Less precision on small devices
  return baseValue;
};

// Export default object with all functions
export default {
  formatNumber,
  formatCurrency,
  formatPercentage,
  formatFileSize,
  formatDuration,
  formatOrdinal,
  formatLocaleNumber,
  parseFormattedNumber,
  getResponsivePrecision,
};
