// 🔥 CALL ERROR BOUNDARY - COMPREHENSIVE ERROR HANDLING
// No crashes, graceful recovery for all WebRTC and calling errors

import { Component, ReactNode } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { soundService } from '../services/soundService';
import { realCallService } from '../services/realCallService';

const { width: _width, height: _height } = Dimensions.get('window');

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (_error: Error, _errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
  errorType: 'webrtc' | 'permission' | 'network' | 'general';
}

export class CallErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorType: 'general',
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Determine error type based on error message
    let errorType: State['errorType'] = 'general';
    
    const errorMessage = error.message.toLowerCase();
    
    if (errorMessage.includes('webrtc') || 
        errorMessage.includes('peer') || 
        errorMessage.includes('ice') ||
        errorMessage.includes('sdp')) {
      errorType = 'webrtc';
    } else if (errorMessage.includes('permission') || 
               errorMessage.includes('camera') || 
               errorMessage.includes('microphone')) {
      errorType = 'permission';
    } else if (errorMessage.includes('network') || 
               errorMessage.includes('connection') || 
               errorMessage.includes('timeout')) {
      errorType = 'network';
    }

    return {
      hasError: true,
      error,
      errorType,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('🚨 Call Error Boundary caught error:', error);
    console.error('🚨 Error Info:', errorInfo);

    this.setState({
      error,
      errorInfo,
    });

    // Play error haptic feedback
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

    // Call onError prop if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error for analytics
    this.logError(error, errorInfo);
  }

  private logError = async (error: Error, errorInfo: any) => {
    try {
      // Log error to console with detailed info
      console.error('🚨 CALL ERROR DETAILS:');
      console.error('Error:', error.name);
      console.error('Message:', error.message);
      console.error('Stack:', error.stack);
      console.error('Component Stack:', errorInfo.componentStack);
      console.error('Error Type:', this.state.errorType);

      // In production, you would send this to your error tracking service
      // Example: Sentry, Crashlytics, etc.
    } catch (logError) {
      console.error('❌ Error logging error:', logError);
    }
  };

  private handleRetry = async () => {
    try {
      console.log('🔄 Retrying after error...');
      
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // Different retry strategies based on error type
      switch (this.state.errorType) {
        case 'webrtc':
          await this.retryWebRTC();
          break;
        case 'permission':
          await this.retryPermissions();
          break;
        case 'network':
          await this.retryNetwork();
          break;
        default:
          await this.retryGeneral();
      }

      // Reset error state
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorType: 'general',
      });

      console.log('✅ Error recovery successful');
    } catch (retryError) {
      console.error('❌ Error during retry:', retryError);
      Alert.alert(
        'Recovery Failed',
        'Unable to recover from the error. Please restart the app.',
        [{ text: 'OK' }]
      );
    }
  };

  private retryWebRTC = async () => {
    console.log('🔄 Retrying WebRTC...');
    
    // Cleanup existing connections
    await realCallService.cleanup();
    
    // Reinitialize WebRTC
    await realCallService.initialize();
    
    console.log('✅ WebRTC retry completed');
  };

  private retryPermissions = async () => {
    console.log('🔄 Retrying permissions...');
    
    // Check permissions again
    const permissions = await realCallService.checkPermissions();
    
    if (!permissions.camera || !permissions.microphone) {
      Alert.alert(
        'Permissions Required',
        'Camera and microphone permissions are required for calling. Please enable them in Settings.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Open Settings', onPress: () => {
            // Open app settings
            console.log('📱 Opening app settings...');
          }},
        ]
      );
      return;
    }
    
    console.log('✅ Permissions retry completed');
  };

  private retryNetwork = async () => {
    console.log('🔄 Retrying network...');
    
    // Wait a moment for network to stabilize
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Reinitialize services
    await realCallService.initialize();
    
    console.log('✅ Network retry completed');
  };

  private retryGeneral = async () => {
    console.log('🔄 General retry...');
    
    // General recovery - reinitialize everything
    await soundService.initialize();
    await realCallService.initialize();
    
    console.log('✅ General retry completed');
  };

  private handleRestart = () => {
    Alert.alert(
      'Restart App',
      'This will restart the app to recover from the error.',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Restart', onPress: () => {
          // In a real app, you might use a library like react-native-restart
          console.log('🔄 Restarting app...');
        }},
      ]
    );
  };

  private getErrorMessage = (): string => {
    switch (this.state.errorType) {
      case 'webrtc':
        return 'WebRTC connection error. This might be due to network issues or device compatibility.';
      case 'permission':
        return 'Camera or microphone permission denied. Please enable permissions to make calls.';
      case 'network':
        return 'Network connection error. Please check your internet connection.';
      default:
        return 'An unexpected error occurred during the call.';
    }
  };

  private getErrorIcon = (): string => {
    switch (this.state.errorType) {
      case 'webrtc':
        return 'videocam-off';
      case 'permission':
        return 'lock-closed';
      case 'network':
        return 'wifi-off';
      default:
        return 'alert-circle';
    }
  };

  render() {
    if (this.state.hasError) {
      // Custom error UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <View style={styles.container}>
          <LinearGradient
            colors={['#FF6B6B', '#FF8E8E', '#FFB3B3']}
            style={styles.background}
          />
          
          <View style={styles.content}>
            <View style={styles.iconContainer}>
              <Ionicons
                name={this.getErrorIcon() as any}
                size={80}
                color="#FFFFFF"
              />
            </View>

            <Text style={styles.title}>Call Error</Text>
            <Text style={styles.message}>{this.getErrorMessage()}</Text>

            {this.state.error && (
              <Text style={styles.errorDetails}>
                {this.state.error.message}
              </Text>
            )}

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[styles.button, styles.retryButton]}
                onPress={this.handleRetry}
                activeOpacity={0.8}
              >
                <Ionicons name="refresh" size={24} color="#FFFFFF" />
                <Text style={styles.buttonText}>Try Again</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.button, styles.restartButton]}
                onPress={this.handleRestart}
                activeOpacity={0.8}
              >
                <Ionicons name="reload" size={24} color="#FFFFFF" />
                <Text style={styles.buttonText}>Restart App</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FF6B6B',
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  iconContainer: {
    marginBottom: 30,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 16,
  },
  message: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
    opacity: 0.9,
  },
  errorDetails: {
    fontSize: 12,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 30,
    fontFamily: 'monospace',
  },
  buttonContainer: {
    width: '100%',
    gap: 16,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
  },
  retryButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  restartButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default CallErrorBoundary;
