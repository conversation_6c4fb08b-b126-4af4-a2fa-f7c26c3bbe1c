import React, { useState, useCallback } from 'react';
import {
  View,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Update, Story } from '../../types/Update';
import { StoriesSection } from './StoriesSection';
import { COLORS } from '../../constants/theme';

interface FeedContentProps {
  updates: Update[];
  stories: Story[];
  myStories: Story[];
  currentUserId?: string;
  isLoading: boolean;
  isRefreshing: boolean;
  onRefresh: () => void;
  onLoadMore: () => void;
  onStoryPress: (story: Story, index: number) => void;
  onCreateStoryPress: () => void;
  onUpdatePress: (update: Update) => void;
  onLikePress: (updateId: string) => Promise<void>;
  onCommentPress: (updateId: string) => void;
  onSharePress: (updateId: string) => void;
  isOnline: boolean;
}

export const FeedContent: React.FC<FeedContentProps> = ({
  updates,
  stories,
  myStories,
  currentUserId,
  isLoading,
  isRefreshing,
  onRefresh,
  onLoadMore,
  onStoryPress,
  onCreateStoryPress,
  onUpdatePress,
  onLikePress,
  onCommentPress,
  onSharePress,
  isOnline,
}) => {
  const [likedUpdates, setLikedUpdates] = useState<Set<string>>(new Set());

  const handleLike = useCallback(async (updateId: string) => {
    if (!isOnline) {
      Alert.alert('Offline', 'You need to be online to like posts');
      return;
    }

    try {
      // Call the like handler first to avoid state update conflicts
      await onLikePress(updateId);

      // Then update local state
      setLikedUpdates(prev => {
        const newSet = new Set(prev);
        if (newSet.has(updateId)) {
          newSet.delete(updateId);
        } else {
          newSet.add(updateId);
        }
        return newSet;
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to like post. Please try again.');
    }
  }, [isOnline, onLikePress]);

  const renderUpdateItem = ({ item }: { item: Update }) => {
    const isLiked = likedUpdates.has(item.id);
    const timeAgo = getTimeAgo(item.createdAt || item.timestamp || new Date());

    return (
      <TouchableOpacity
        style={styles.updateCard}
        onPress={() => onUpdatePress(item)}
        activeOpacity={0.95}
      >
        {/* Update Header */}
        <View style={styles.updateHeader}>
          <View style={styles.userInfo}>
            <View style={styles.userAvatar}>
              <Text style={styles.userInitial}>
                {item.userName?.charAt(0).toUpperCase() || 'U'}
              </Text>
            </View>
            <View style={styles.userDetails}>
              <Text style={styles.userName}>{item.userName || 'Unknown User'}</Text>
              <Text style={styles.updateTime}>{timeAgo}</Text>
            </View>
          </View>
          
          {!isOnline && (
            <View style={styles.offlineIndicator}>
              <Ionicons name="cloud-offline" size={16} color="#EF4444" />
            </View>
          )}
        </View>

        {/* Update Content */}
        {item.caption && (
          <Text style={styles.updateCaption}>{item.caption}</Text>
        )}

        {/* Update Media Preview */}
        {item.mediaUrl && (
          <View style={styles.mediaPreview}>
            <View style={styles.mediaPlaceholder}>
              <Ionicons
                name={item.type === 'video' ? 'videocam' : 'image'}
                size={32}
                color={COLORS.textSecondary}
              />
              <Text style={styles.mediaText}>
                {item.type === 'video' ? 'Video' : 'Image'}
              </Text>
            </View>
          </View>
        )}

        {/* Update Actions */}
        <View style={styles.updateActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => handleLike(item.id)}
            disabled={!isOnline}
            activeOpacity={0.7}
          >
            <Ionicons
              name={isLiked ? 'heart' : 'heart-outline'}
              size={20}
              color={isLiked ? '#FF3040' : COLORS.textSecondary}
            />
            <Text style={[styles.actionText, isLiked && styles.likedText]}>
              {Array.isArray(item.likes) ? item.likes.length : (item.likesCount || item.likeCount || 0)}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onCommentPress(item.id)}
          >
            <Ionicons name="chatbubble-outline" size={20} color={COLORS.textSecondary} />
            <Text style={styles.actionText}>{Array.isArray(item.comments) ? item.comments.length : (item.commentsCount || item.commentCount || 0)}</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => onSharePress(item.id)}
          >
            <Ionicons name="share-outline" size={20} color={COLORS.textSecondary} />
            <Text style={styles.actionText}>{Array.isArray(item.shares) ? item.shares.length : (item.shareCount || 0)}</Text>
          </TouchableOpacity>

          <View style={styles.actionButton}>
            <Ionicons name="eye-outline" size={20} color={COLORS.textSecondary} />
            <Text style={styles.actionText}>{Array.isArray(item.views) ? item.views.length : (item.viewCount || 0)}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="newspaper-outline" size={64} color={COLORS.textSecondary} />
      <Text style={styles.emptyTitle}>No Updates Yet</Text>
      <Text style={styles.emptySubtitle}>
        {isOnline 
          ? 'Be the first to share an update!' 
          : 'Connect to internet to see updates'
        }
      </Text>
    </View>
  );

  const getTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={updates}
        renderItem={renderUpdateItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={
          <StoriesSection
            stories={stories}
            myStories={myStories}
            currentUserId={currentUserId}
            onStoryPress={onStoryPress}
            onCreateStoryPress={onCreateStoryPress}
            isLoading={isLoading}
          />
        }
        ListEmptyComponent={!isLoading ? renderEmptyState : null}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            tintColor={COLORS.primary}
            colors={[COLORS.primary]}
          />
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={updates.length === 0 ? styles.emptyContainer : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  updateCard: {
    backgroundColor: '#1F2937',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 12,
    padding: 16,
  },
  updateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userInitial: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    color: COLORS.text,
    fontSize: 16,
    fontWeight: '600',
  },
  updateTime: {
    color: COLORS.textSecondary,
    fontSize: 12,
    marginTop: 2,
  },
  offlineIndicator: {
    padding: 4,
  },
  updateCaption: {
    color: COLORS.text,
    fontSize: 16,
    lineHeight: 22,
    marginBottom: 12,
  },
  mediaPreview: {
    height: 200,
    borderRadius: 8,
    backgroundColor: '#374151',
    marginBottom: 12,
    overflow: 'hidden',
  },
  mediaPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaText: {
    color: COLORS.textSecondary,
    fontSize: 14,
    marginTop: 8,
  },
  updateActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#374151',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
  },
  actionText: {
    color: COLORS.textSecondary,
    fontSize: 14,
    marginLeft: 6,
  },
  likedText: {
    color: '#FF3040',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyTitle: {
    color: COLORS.text,
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    color: COLORS.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
  emptyContainer: {
    flexGrow: 1,
  },
});
