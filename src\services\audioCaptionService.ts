/**
 * Audio Caption Service
 * Manages audio caption playback across the entire feed to ensure only one audio plays at a time
 */

import { Audio } from 'expo-av';
import { AudioCaption } from '../types/Update';

interface AudioCaptionInstance {
  id: string;
  sound: Audio.Sound;
  audioCaption: AudioCaption;
  isPlaying: boolean;
  position: number;
  duration: number;
}

class AudioCaptionService {
  private activeAudios: Map<string, AudioCaptionInstance> = new Map();
  private currentlyPlayingId: string | null = null;
  private globalAudioPause: boolean = false;

  /**
   * Initialize audio mode for the app
   */
  async initialize(): Promise<void> {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        playsInSilentModeIOS: true,
        staysActiveInBackground: false,
        shouldDuckAndroid: true,
      });
    } catch (error) {
      console.error('Error initializing audio caption service:', error);
    }
  }

  /**
   * Register an audio caption for playback management
   */
  async registerAudio(
    id: string,
    audioCaption: AudioCaption,
    onStatusUpdate?: (isPlaying: boolean, position: number) => void
  ): Promise<Audio.Sound | null> {
    try {
      // If already registered, return existing sound
      if (this.activeAudios.has(id)) {
        return this.activeAudios.get(id)!.sound;
      }

      const { sound } = await Audio.Sound.createAsync(
        { uri: audioCaption.url },
        {
          shouldPlay: false,
          volume: audioCaption.volume || 1.0,
        }
      );

      // Set up status update callback
      sound.setOnPlaybackStatusUpdate((status: any) => {
        if (status.isLoaded) {
          const instance = this.activeAudios.get(id);
          if (instance) {
            instance.isPlaying = status.isPlaying || false;
            instance.position = status.positionMillis || 0;
            instance.duration = status.durationMillis || audioCaption.duration * 1000;

            // Notify callback
            if (onStatusUpdate) {
              onStatusUpdate(instance.isPlaying, instance.position);
            }

            // Handle playback completion
            if (status.didJustFinish) {
              this.handleAudioFinished(id);
            }
          }
        }
      });

      // Register the audio instance
      const instance: AudioCaptionInstance = {
        id,
        sound,
        audioCaption,
        isPlaying: false,
        position: 0,
        duration: audioCaption.duration * 1000,
      };

      this.activeAudios.set(id, instance);
      return sound;

    } catch (error) {
      console.error('Error registering audio caption:', error);
      return null;
    }
  }

  /**
   * Unregister an audio caption
   */
  async unregisterAudio(id: string): Promise<void> {
    const instance = this.activeAudios.get(id);
    if (instance) {
      try {
        await instance.sound.unloadAsync();
      } catch (error) {
        console.error('Error unloading audio:', error);
      }
      
      this.activeAudios.delete(id);
      
      if (this.currentlyPlayingId === id) {
        this.currentlyPlayingId = null;
      }
    }
  }

  /**
   * Play an audio caption (stops any currently playing audio)
   */
  async playAudio(id: string): Promise<boolean> {
    if (this.globalAudioPause) {
      return false;
    }

    const instance = this.activeAudios.get(id);
    if (!instance) {
      console.warn('Audio caption not registered:', id);
      return false;
    }

    try {
      // Stop currently playing audio
      if (this.currentlyPlayingId && this.currentlyPlayingId !== id) {
        await this.pauseAudio(this.currentlyPlayingId);
      }

      // Play the requested audio
      await instance.sound.playAsync();
      this.currentlyPlayingId = id;
      instance.isPlaying = true;

      console.log('🎵 Playing audio caption:', id);
      return true;

    } catch (error) {
      console.error('Error playing audio caption:', error);
      return false;
    }
  }

  /**
   * Pause an audio caption
   */
  async pauseAudio(id: string): Promise<boolean> {
    const instance = this.activeAudios.get(id);
    if (!instance) {
      return false;
    }

    try {
      await instance.sound.pauseAsync();
      instance.isPlaying = false;
      
      if (this.currentlyPlayingId === id) {
        this.currentlyPlayingId = null;
      }

      return true;

    } catch (error) {
      console.error('Error pausing audio caption:', error);
      return false;
    }
  }

  /**
   * Stop all audio captions
   */
  async pauseAllAudios(): Promise<void> {
    const pausePromises = Array.from(this.activeAudios.keys()).map(id => 
      this.pauseAudio(id)
    );
    
    await Promise.all(pausePromises);
    this.currentlyPlayingId = null;
  }

  /**
   * Set global audio pause state
   */
  setGlobalAudioPause(paused: boolean): void {
    this.globalAudioPause = paused;
    
    if (paused) {
      this.pauseAllAudios();
    }
  }

  /**
   * Get currently playing audio ID
   */
  getCurrentlyPlayingId(): string | null {
    return this.currentlyPlayingId;
  }

  /**
   * Check if an audio is currently playing
   */
  isAudioPlaying(id: string): boolean {
    const instance = this.activeAudios.get(id);
    return instance ? instance.isPlaying : false;
  }

  /**
   * Handle audio playback completion
   */
  private handleAudioFinished(id: string): void {
    const instance = this.activeAudios.get(id);
    if (instance) {
      instance.isPlaying = false;
      instance.position = 0;
    }
    
    if (this.currentlyPlayingId === id) {
      this.currentlyPlayingId = null;
    }
  }

  /**
   * Upload audio caption to storage
   */
  async uploadAudioCaption(
    audioCaption: AudioCaption,
    userId: string,
    onProgress?: (progress: number) => void
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      // This would typically upload to Firebase Storage or similar
      // For now, we'll simulate the upload process
      
      if (onProgress) {
        // Simulate upload progress
        for (let i = 0; i <= 100; i += 10) {
          onProgress(i);
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      // In a real implementation, you would:
      // 1. Upload the audio file to Firebase Storage
      // 2. Get the download URL
      // 3. Return the URL
      
      // For now, return the local URI (in production, this would be the uploaded URL)
      return {
        success: true,
        url: audioCaption.url,
      };

    } catch (error) {
      console.error('Error uploading audio caption:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  /**
   * Clean up all resources
   */
  async cleanup(): Promise<void> {
    const unloadPromises = Array.from(this.activeAudios.values()).map(instance =>
      instance.sound.unloadAsync().catch(error => 
        console.error('Error unloading audio during cleanup:', error)
      )
    );

    await Promise.all(unloadPromises);
    this.activeAudios.clear();
    this.currentlyPlayingId = null;
  }
}

// Export singleton instance
export const audioCaptionService = new AudioCaptionService();
