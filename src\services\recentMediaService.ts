/**
 * Recent Media Service for IraChat
 * Fetches recent photos and videos from device gallery for WhatsApp-style modal
 */

import * as MediaLibrary from 'expo-media-library';
import { Alert } from 'react-native';

export interface RecentMediaItem {
  id: string;
  uri: string;
  mediaType: 'photo' | 'video';
  width: number;
  height: number;
  duration?: number;
  creationTime: number;
  filename: string;
}

class RecentMediaService {
  private hasPermission = false;
  private lastCursor: string | undefined;

  /**
   * Request media library permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      this.hasPermission = status === 'granted';
      
      if (!this.hasPermission) {
        Alert.alert(
          'Permission Required',
          'Gallery access is needed to show your recent photos and videos.',
          [{ text: 'OK' }]
        );
      }
      
      return this.hasPermission;
    } catch (error) {
      console.error('❌ Error requesting media permissions:', error);
      return false;
    }
  }

  /**
   * Get recent media items from device gallery with pagination support
   */
  async getRecentMedia(limit: number = 20, after?: string): Promise<RecentMediaItem[]> {
    try {
      if (!this.hasPermission) {
        const hasPermission = await this.requestPermissions();
        if (!hasPermission) return [];
      }

      // Get recent photos and videos with pagination
      const mediaAssets = await MediaLibrary.getAssetsAsync({
        first: limit,
        after: after, // For pagination
        mediaType: [MediaLibrary.MediaType.photo, MediaLibrary.MediaType.video],
        sortBy: MediaLibrary.SortBy.creationTime,
      });

      const recentMedia: RecentMediaItem[] = [];

      // Update cursor for pagination
      if (mediaAssets.endCursor) {
        this.lastCursor = mediaAssets.endCursor;
      }

      for (const asset of mediaAssets.assets) {
        try {
          // Get asset info for additional details
          const assetInfo = await MediaLibrary.getAssetInfoAsync(asset);
          
          recentMedia.push({
            id: asset.id,
            uri: assetInfo.localUri || asset.uri,
            mediaType: asset.mediaType === MediaLibrary.MediaType.video ? 'video' : 'photo',
            width: asset.width,
            height: asset.height,
            duration: asset.duration,
            creationTime: asset.creationTime,
            filename: asset.filename,
          });
        } catch (assetError) {
          console.warn('⚠️ Error getting asset info for:', asset.id, assetError);
          // Still add basic info if detailed info fails
          recentMedia.push({
            id: asset.id,
            uri: asset.uri,
            mediaType: asset.mediaType === MediaLibrary.MediaType.video ? 'video' : 'photo',
            width: asset.width,
            height: asset.height,
            duration: asset.duration,
            creationTime: asset.creationTime,
            filename: asset.filename,
          });
        }
      }

      console.log(`✅ Loaded ${recentMedia.length} recent media items`);
      return recentMedia;
    } catch (error) {
      console.error('❌ Error getting recent media:', error);
      return [];
    }
  }

  /**
   * Get recent photos only
   */
  async getRecentPhotos(limit: number = 10): Promise<RecentMediaItem[]> {
    try {
      if (!this.hasPermission) {
        const hasPermission = await this.requestPermissions();
        if (!hasPermission) return [];
      }

      const mediaAssets = await MediaLibrary.getAssetsAsync({
        first: limit,
        mediaType: MediaLibrary.MediaType.photo,
        sortBy: MediaLibrary.SortBy.creationTime,
      });

      const recentPhotos: RecentMediaItem[] = [];

      for (const asset of mediaAssets.assets) {
        try {
          const assetInfo = await MediaLibrary.getAssetInfoAsync(asset);
          
          recentPhotos.push({
            id: asset.id,
            uri: assetInfo.localUri || asset.uri,
            mediaType: 'photo',
            width: asset.width,
            height: asset.height,
            creationTime: asset.creationTime,
            filename: asset.filename,
          });
        } catch (assetError) {
          console.warn('⚠️ Error getting photo asset info:', assetError);
          recentPhotos.push({
            id: asset.id,
            uri: asset.uri,
            mediaType: 'photo',
            width: asset.width,
            height: asset.height,
            creationTime: asset.creationTime,
            filename: asset.filename,
          });
        }
      }

      return recentPhotos;
    } catch (error) {
      console.error('❌ Error getting recent photos:', error);
      return [];
    }
  }

  /**
   * Get recent videos only
   */
  async getRecentVideos(limit: number = 10): Promise<RecentMediaItem[]> {
    try {
      if (!this.hasPermission) {
        const hasPermission = await this.requestPermissions();
        if (!hasPermission) return [];
      }

      const mediaAssets = await MediaLibrary.getAssetsAsync({
        first: limit,
        mediaType: MediaLibrary.MediaType.video,
        sortBy: MediaLibrary.SortBy.creationTime,
      });

      const recentVideos: RecentMediaItem[] = [];

      for (const asset of mediaAssets.assets) {
        try {
          const assetInfo = await MediaLibrary.getAssetInfoAsync(asset);
          
          recentVideos.push({
            id: asset.id,
            uri: assetInfo.localUri || asset.uri,
            mediaType: 'video',
            width: asset.width,
            height: asset.height,
            duration: asset.duration,
            creationTime: asset.creationTime,
            filename: asset.filename,
          });
        } catch (assetError) {
          console.warn('⚠️ Error getting video asset info:', assetError);
          recentVideos.push({
            id: asset.id,
            uri: asset.uri,
            mediaType: 'video',
            width: asset.width,
            height: asset.height,
            duration: asset.duration,
            creationTime: asset.creationTime,
            filename: asset.filename,
          });
        }
      }

      return recentVideos;
    } catch (error) {
      console.error('❌ Error getting recent videos:', error);
      return [];
    }
  }

  /**
   * Get more media items using pagination
   */
  async getMoreMedia(limit: number = 20): Promise<RecentMediaItem[]> {
    return this.getRecentMedia(limit, this.lastCursor);
  }

  /**
   * Reset pagination cursor
   */
  resetPagination(): void {
    this.lastCursor = undefined;
  }

  /**
   * Check if more media is available
   */
  hasMoreMedia(): boolean {
    return this.lastCursor !== undefined;
  }

  /**
   * Format duration for display
   */
  formatDuration(duration?: number): string {
    if (!duration) return '';

    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);

    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

export const recentMediaService = new RecentMediaService();
