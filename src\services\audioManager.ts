// 🎵 GLOBAL AUDIO MANAGER
// Prevents audio/video overlap and manages playback state

import { Audio } from 'expo-av';

class AudioManager {
  private currentSound: Audio.Sound | null = null;
  private currentVideo: any = null;
  private currentMessageId: string | null = null;
  private playbackListeners: Map<string, (isPlaying: boolean, position: number) => void> = new Map();
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) return;
    
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });
      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Failed to initialize audio:', error);
    }
  }

  async stopAll() {
    try {
      // Stop current audio
      if (this.currentSound) {
        await this.currentSound.stopAsync();
        await this.currentSound.unloadAsync();
        this.currentSound = null;
      }

      // Stop current video
      if (this.currentVideo) {
        await this.currentVideo.stopAsync();
        this.currentVideo = null;
      }

      // Notify all listeners that playback stopped
      this.notifyAllListeners(false, 0);
      this.currentMessageId = null;
    } catch (error) {
      console.error('❌ Failed to stop audio/video:', error);
    }
  }

  // Register a listener for voice message playback
  registerVoiceMessageListener(messageId: string, callback: (isPlaying: boolean, position: number) => void) {
    this.playbackListeners.set(messageId, callback);
  }

  // Unregister a listener
  unregisterVoiceMessageListener(messageId: string) {
    this.playbackListeners.delete(messageId);
  }

  // Notify all listeners
  private notifyAllListeners(isPlaying: boolean, position: number) {
    this.playbackListeners.forEach((callback, messageId) => {
      if (messageId === this.currentMessageId) {
        callback(isPlaying, position);
      } else {
        callback(false, 0); // Stop all other messages
      }
    });
  }

  // Play voice message
  async playVoiceMessage(messageId: string, audioUrl: string): Promise<void> {
    try {
      console.log(`🔊 Playing voice message ${messageId}`);

      // Stop any currently playing audio
      await this.stopAll();

      if (!this.isInitialized) {
        await this.initialize();
      }

      // Create and play sound
      const { sound } = await Audio.Sound.createAsync(
        { uri: audioUrl },
        { shouldPlay: true }
      );

      this.currentSound = sound;
      this.currentMessageId = messageId;

      // Set up status updates
      sound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded) {
          const position = status.positionMillis ? Math.floor(status.positionMillis / 1000) : 0;

          if (status.didJustFinish) {
            this.notifyAllListeners(false, 0);
            this.currentSound = null;
            this.currentMessageId = null;
          } else {
            this.notifyAllListeners(status.isPlaying ?? false, position);
          }
        }
      });

      console.log(`✅ Voice message ${messageId} started playing`);
    } catch (error) {
      console.error(`❌ Failed to play voice message ${messageId}:`, error);
      this.notifyAllListeners(false, 0);
      throw error;
    }
  }

  // Pause current voice message
  async pauseVoiceMessage(): Promise<void> {
    try {
      if (this.currentSound && this.currentMessageId) {
        await this.currentSound.pauseAsync();
        // Get current position and notify listeners
        const status = await this.currentSound.getStatusAsync();
        if (status.isLoaded) {
          const position = status.positionMillis ? Math.floor(status.positionMillis / 1000) : 0;
          this.notifyAllListeners(false, position);
        }
        console.log(`⏸️ Voice message ${this.currentMessageId} paused`);
      }
    } catch (error) {
      console.error('❌ Failed to pause voice message:', error);
    }
  }

  // Resume current voice message
  async resumeVoiceMessage(): Promise<void> {
    try {
      if (this.currentSound && this.currentMessageId) {
        await this.currentSound.playAsync();
        // Get current position and notify listeners
        const status = await this.currentSound.getStatusAsync();
        if (status.isLoaded) {
          const position = status.positionMillis ? Math.floor(status.positionMillis / 1000) : 0;
          this.notifyAllListeners(true, position);
        }
        console.log(`▶️ Voice message ${this.currentMessageId} resumed`);
      }
    } catch (error) {
      console.error('❌ Failed to resume voice message:', error);
    }
  }

  // Check if a specific message is playing
  isVoiceMessagePlaying(messageId: string): boolean {
    return this.currentMessageId === messageId && this.currentSound !== null;
  }

  async playAudio(uri: string, onStatusUpdate?: (status: any) => void): Promise<Audio.Sound | null> {
    try {
      // Stop any currently playing audio/video
      await this.stopAll();
      
      // Initialize if needed
      await this.initialize();

      // Create new sound
      const { sound } = await Audio.Sound.createAsync(
        { uri },
        { shouldPlay: true },
        onStatusUpdate
      );

      this.currentSound = sound;
      return sound;
    } catch (error) {
      console.error('❌ Failed to play audio:', error);
      return null;
    }
  }

  async pauseCurrentAudio() {
    try {
      if (this.currentSound) {
        await this.currentSound.pauseAsync();
      }
    } catch (error) {
      console.error('❌ Failed to pause audio:', error);
    }
  }

  async resumeCurrentAudio() {
    try {
      if (this.currentSound) {
        await this.currentSound.playAsync();
      }
    } catch (error) {
      console.error('❌ Failed to resume audio:', error);
    }
  }

  async seekAudio(positionMillis: number) {
    try {
      if (this.currentSound) {
        await this.currentSound.setPositionAsync(positionMillis);
      }
    } catch (error) {
      console.error('❌ Failed to seek audio:', error);
    }
  }

  async setPlaybackRate(rate: number) {
    try {
      if (this.currentSound) {
        await this.currentSound.setRateAsync(rate, true);
      }
    } catch (error) {
      console.error('❌ Failed to set playback rate:', error);
    }
  }

  registerVideo(videoRef: any) {
    // Stop any current audio when video starts
    if (videoRef) {
      this.stopAll();
      this.currentVideo = videoRef;
    }
  }

  getCurrentSound() {
    return this.currentSound;
  }

  isPlaying() {
    return this.currentSound !== null;
  }
}

export const audioManager = new AudioManager();
