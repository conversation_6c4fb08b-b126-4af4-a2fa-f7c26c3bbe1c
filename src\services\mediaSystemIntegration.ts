/**
 * Media System Integration Service for IraChat
 * Coordinates all media-related services and ensures proper initialization
 * This is the main entry point for all media functionality
 */

import { autoDownloadService } from './autoDownloadService';
import { remembranceService } from './remembranceService';
import { advancedMediaActions } from './advancedMediaActions';
import { externalAppIntegration } from './externalAppIntegration';
import { realTimeMessagingService } from './realTimeMessagingService';
import { realPrivacyService } from './realPrivacyService';

export interface MediaSystemConfig {
  userId: string;
  autoDownloadEnabled: boolean;
  remembranceEnabled: boolean;
  externalAppsEnabled: boolean;
}

export interface MediaSystemStatus {
  autoDownload: 'initialized' | 'initializing' | 'error' | 'disabled';
  remembrance: 'initialized' | 'initializing' | 'error' | 'disabled';
  mediaActions: 'initialized' | 'initializing' | 'error' | 'disabled';
  externalApps: 'initialized' | 'initializing' | 'error' | 'disabled';
  messaging: 'initialized' | 'initializing' | 'error' | 'disabled';
}

class MediaSystemIntegrationService {
  private isInitialized = false;
  private currentConfig: MediaSystemConfig | null = null;
  private status: MediaSystemStatus = {
    autoDownload: 'disabled',
    remembrance: 'disabled',
    mediaActions: 'disabled',
    externalApps: 'disabled',
    messaging: 'disabled',
  };

  /**
   * Initialize the complete media system
   */
  async initialize(config: MediaSystemConfig): Promise<void> {
    if (this.isInitialized && this.currentConfig?.userId === config.userId) {
      console.log('✅ Media system already initialized for user:', config.userId);
      return;
    }

    console.log('🚀 Initializing IraChat media system for user:', config.userId);
    this.currentConfig = config;

    try {
      // Initialize services in order of dependency
      await this.initializeMessagingService(config);
      await this.initializeAutoDownloadService(config);
      await this.initializeRemembranceService(config);
      await this.initializeMediaActionsService(config);
      await this.initializeExternalAppsService(config);

      this.isInitialized = true;
      console.log('✅ Media system fully initialized');
      this.logSystemStatus();
    } catch (error) {
      console.error('❌ Failed to initialize media system:', error);
      throw error;
    }
  }

  /**
   * Initialize messaging service with media integration
   */
  private async initializeMessagingService(config: MediaSystemConfig): Promise<void> {
    try {
      this.status.messaging = 'initializing';
      await realTimeMessagingService.initialize(config.userId);
      this.status.messaging = 'initialized';
      console.log('✅ Messaging service initialized with media integration');
    } catch (error) {
      this.status.messaging = 'error';
      console.error('❌ Failed to initialize messaging service:', error);
      throw error;
    }
  }

  /**
   * Initialize auto download service
   */
  private async initializeAutoDownloadService(config: MediaSystemConfig): Promise<void> {
    if (!config.autoDownloadEnabled) {
      this.status.autoDownload = 'disabled';
      console.log('⏭️ Auto download service disabled by config');
      return;
    }

    try {
      this.status.autoDownload = 'initializing';
      await autoDownloadService.initialize(config.userId);
      this.status.autoDownload = 'initialized';
      console.log('✅ Auto download service initialized');
    } catch (error) {
      this.status.autoDownload = 'error';
      console.error('❌ Failed to initialize auto download service:', error);
      // Don't throw - this is not critical for basic functionality
    }
  }

  /**
   * Initialize remembrance service
   */
  private async initializeRemembranceService(config: MediaSystemConfig): Promise<void> {
    if (!config.remembranceEnabled) {
      this.status.remembrance = 'disabled';
      console.log('⏭️ Remembrance service disabled by config');
      return;
    }

    try {
      this.status.remembrance = 'initializing';
      await remembranceService.initialize(config.userId);
      this.status.remembrance = 'initialized';
      console.log('✅ Remembrance service initialized');
    } catch (error) {
      this.status.remembrance = 'error';
      console.error('❌ Failed to initialize remembrance service:', error);
      // Don't throw - this is not critical for basic functionality
    }
  }

  /**
   * Initialize advanced media actions service
   */
  private async initializeMediaActionsService(config: MediaSystemConfig): Promise<void> {
    try {
      this.status.mediaActions = 'initializing';
      await advancedMediaActions.initialize(config.userId);
      this.status.mediaActions = 'initialized';
      console.log('✅ Advanced media actions service initialized');
    } catch (error) {
      this.status.mediaActions = 'error';
      console.error('❌ Failed to initialize media actions service:', error);
      // Don't throw - this is not critical for basic functionality
    }
  }

  /**
   * Initialize external apps integration
   */
  private async initializeExternalAppsService(config: MediaSystemConfig): Promise<void> {
    if (!config.externalAppsEnabled) {
      this.status.externalApps = 'disabled';
      console.log('⏭️ External apps integration disabled by config');
      return;
    }

    try {
      this.status.externalApps = 'initializing';
      await externalAppIntegration.initialize();
      this.status.externalApps = 'initialized';
      console.log('✅ External apps integration initialized');
    } catch (error) {
      this.status.externalApps = 'error';
      console.error('❌ Failed to initialize external apps integration:', error);
      // Don't throw - this is not critical for basic functionality
    }
  }

  /**
   * Get current system status
   */
  getSystemStatus(): MediaSystemStatus {
    return { ...this.status };
  }

  /**
   * Check if a specific service is available
   */
  isServiceAvailable(service: keyof MediaSystemStatus): boolean {
    return this.status[service] === 'initialized';
  }

  /**
   * Update auto download settings
   */
  async updateAutoDownloadSettings(settings: {
    images?: 'never' | 'wifi' | 'always';
    videos?: 'never' | 'wifi' | 'always';
    audio?: 'never' | 'wifi' | 'always';
    documents?: 'never' | 'wifi' | 'always';
    maxFileSize?: number;
    onlyInChats?: boolean;
  }): Promise<{ success: boolean; error?: string }> {
    if (!this.isServiceAvailable('autoDownload')) {
      return { success: false, error: 'Auto download service not available' };
    }

    try {
      await autoDownloadService.updateSettings(settings);
      console.log('✅ Auto download settings updated');
      return { success: true };
    } catch (error) {
      console.error('❌ Failed to update auto download settings:', error);
      return { success: false, error: 'Failed to update settings' };
    }
  }

  /**
   * Save media for remembrance
   */
  async saveForRemembrance(
    mediaUrl: string,
    type: 'image' | 'video' | 'audio' | 'document',
    fileName: string,
    sourceInfo: {
      chatId: string;
      chatName: string;
      senderId: string;
      senderName: string;
      messageId: string;
    },
    options?: {
      caption?: string;
      tags?: string[];
      notes?: string;
    }
  ): Promise<{ success: boolean; rememberedId?: string; error?: string }> {
    if (!this.isServiceAvailable('remembrance')) {
      return { success: false, error: 'Remembrance service not available' };
    }

    try {
      const result = await remembranceService.saveForRemembrance(
        mediaUrl,
        type,
        fileName,
        sourceInfo,
        options
      );
      return result;
    } catch (error) {
      console.error('❌ Failed to save for remembrance:', error);
      return { success: false, error: 'Failed to save for remembrance' };
    }
  }

  /**
   * Share media to external app
   */
  async shareToExternalApp(
    appId: string,
    content: {
      type: 'text' | 'image' | 'video' | 'audio' | 'file';
      uri?: string;
      text?: string;
      subject?: string;
    }
  ): Promise<{ success: boolean; error?: string }> {
    if (!this.isServiceAvailable('externalApps')) {
      return { success: false, error: 'External apps integration not available' };
    }

    try {
      const result = await externalAppIntegration.shareToApp(appId, content);
      return result;
    } catch (error) {
      console.error('❌ Failed to share to external app:', error);
      return { success: false, error: 'Failed to share to external app' };
    }
  }

  /**
   * Get installed external apps for media type
   */
  getExternalAppsForMediaType(mediaType: 'image' | 'video' | 'audio' | 'document') {
    if (!this.isServiceAvailable('externalApps')) {
      return [];
    }

    return externalAppIntegration.getInstalledAppsForType(mediaType);
  }

  /**
   * Perform bulk media operations
   */
  async performBulkOperation(
    operation: 'download' | 'share' | 'delete' | 'remember',
    mediaItems: any[]
  ): Promise<{ success: boolean; processed: number; failed: number; errors: string[] }> {
    if (!this.isServiceAvailable('mediaActions')) {
      return {
        success: false,
        processed: 0,
        failed: mediaItems.length,
        errors: ['Media actions service not available'],
      };
    }

    try {
      switch (operation) {
        case 'download':
          return await advancedMediaActions.bulkDownload(mediaItems);
        case 'share':
          return await advancedMediaActions.bulkShare(mediaItems);
        case 'delete':
          return await advancedMediaActions.bulkDelete(mediaItems);
        case 'remember':
          return await advancedMediaActions.bulkSaveToRemembrance(mediaItems);
        default:
          return {
            success: false,
            processed: 0,
            failed: mediaItems.length,
            errors: ['Unknown operation'],
          };
      }
    } catch (error) {
      console.error(`❌ Failed to perform bulk ${operation}:`, error);
      return {
        success: false,
        processed: 0,
        failed: mediaItems.length,
        errors: [`Failed to perform bulk ${operation}`],
      };
    }
  }

  /**
   * Get comprehensive media statistics
   */
  async getMediaStatistics(): Promise<{
    autoDownload: {
      totalDownloaded: number;
      pendingDownloads: number;
      failedDownloads: number;
    };
    remembrance: {
      totalItems: number;
      totalSize: number;
      byType: { [key: string]: { count: number; size: number } };
      localStorageUsed: number;
    };
    externalApps: {
      installedApps: number;
      supportedTypes: string[];
    };
  }> {
    const stats = {
      autoDownload: {
        totalDownloaded: 0,
        pendingDownloads: 0,
        failedDownloads: 0,
      },
      remembrance: {
        totalItems: 0,
        totalSize: 0,
        byType: {},
        localStorageUsed: 0,
      },
      externalApps: {
        installedApps: 0,
        supportedTypes: [] as string[],
      },
    };

    try {
      // Get remembrance stats
      if (this.isServiceAvailable('remembrance')) {
        stats.remembrance = await remembranceService.getStorageStats();
      }

      // Get external apps stats
      if (this.isServiceAvailable('externalApps')) {
        const installedApps = externalAppIntegration.getAllInstalledApps();
        stats.externalApps.installedApps = installedApps.length;
        stats.externalApps.supportedTypes = [
          ...new Set(installedApps.flatMap(app => app.supportedTypes))
        ];
      }

      return stats;
    } catch (error) {
      console.error('❌ Failed to get media statistics:', error);
      return stats;
    }
  }

  /**
   * Log current system status
   */
  private logSystemStatus(): void {
    console.log('📊 Media System Status:');
    console.log('  Auto Download:', this.status.autoDownload);
    console.log('  Remembrance:', this.status.remembrance);
    console.log('  Media Actions:', this.status.mediaActions);
    console.log('  External Apps:', this.status.externalApps);
    console.log('  Messaging:', this.status.messaging);
  }

  /**
   * Cleanup all services
   */
  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up media system...');

    try {
      await autoDownloadService.cleanup();
      await remembranceService.cleanup();
      advancedMediaActions.cleanup();
      externalAppIntegration.cleanup();
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }

    this.isInitialized = false;
    this.currentConfig = null;
    this.status = {
      autoDownload: 'disabled',
      remembrance: 'disabled',
      mediaActions: 'disabled',
      externalApps: 'disabled',
      messaging: 'disabled',
    };

    console.log('✅ Media system cleanup completed');
  }

  /**
   * Reinitialize system with new configuration
   */
  async reinitialize(config: MediaSystemConfig): Promise<void> {
    await this.cleanup();
    await this.initialize(config);
  }

  /**
   * Check if system is fully initialized
   */
  isFullyInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Get current configuration
   */
  getCurrentConfig(): MediaSystemConfig | null {
    return this.currentConfig;
  }
}

export const mediaSystemIntegration = new MediaSystemIntegrationService();
