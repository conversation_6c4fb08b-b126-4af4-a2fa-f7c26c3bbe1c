/**
 * Offline Comments Service for IraChat
 * Handles comment actions when offline and syncs with Firebase when online
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { networkStateManager } from './networkStateManager';
import { realUpdatesService } from './realUpdatesService';

export interface CommentAction {
  id: string;
  updateId: string;
  userId: string;
  userName: string;
  userAvatar: string | undefined;
  content: string;
  parentCommentId?: string;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

export interface OfflineComment {
  id: string;
  updateId: string;
  userId: string;
  userName: string;
  userAvatar: string | undefined;
  content: string;
  parentCommentId?: string;
  timestamp: number;
  isOffline: boolean;
}

class OfflineCommentsService {
  private readonly QUEUE_KEY = 'offline_comments_queue';
  private readonly COMMENTS_KEY = 'offline_comments_state';
  private queue: CommentAction[] = [];
  private offlineComments: Map<string, OfflineComment[]> = new Map(); // updateId -> comments
  private isProcessing = false;

  constructor() {
    this.initializeService();
    this.setupNetworkListener();
  }

  /**
   * Force initialization (useful for debugging)
   */
  async forceInitialize(): Promise<void> {
    await this.initializeService();
  }

  private async initializeService(): Promise<void> {
    try {
      await this.loadQueue();
      await this.loadOfflineComments();
      console.log('✅ Offline comments service initialized');
      console.log(`📊 Loaded ${this.queue.length} queued comments and ${this.offlineComments.size} offline comment groups`);
    } catch (error) {
      console.error('❌ Error initializing offline comments service:', error);
    }
  }

  private setupNetworkListener(): void {
    networkStateManager.addListener('offline-comments', (state) => {
      if (state.isConnected && this.queue.length > 0) {
        console.log('🌐 Network connected, processing comments queue');
        this.processQueue();
      }
    });
  }

  private async loadQueue(): Promise<void> {
    try {
      const queueData = await AsyncStorage.getItem(this.QUEUE_KEY);
      this.queue = queueData ? JSON.parse(queueData) : [];
    } catch (error) {
      console.error('❌ Error loading comments queue:', error);
      this.queue = [];
    }
  }

  private async saveQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.QUEUE_KEY, JSON.stringify(this.queue));
    } catch (error) {
      console.error('❌ Error saving comments queue:', error);
    }
  }

  private async loadOfflineComments(): Promise<void> {
    try {
      const commentsData = await AsyncStorage.getItem(this.COMMENTS_KEY);
      if (commentsData) {
        const parsed = JSON.parse(commentsData);
        this.offlineComments = new Map(Object.entries(parsed));
      }
    } catch (error) {
      console.error('❌ Error loading offline comments:', error);
      this.offlineComments = new Map();
    }
  }

  private async saveOfflineComments(): Promise<void> {
    try {
      const commentsObj = Object.fromEntries(this.offlineComments);
      await AsyncStorage.setItem(this.COMMENTS_KEY, JSON.stringify(commentsObj));
    } catch (error) {
      console.error('❌ Error saving offline comments:', error);
    }
  }

  /**
   * Add comment to update (works offline)
   */
  async addComment(
    updateId: string,
    userId: string,
    userName: string,
    userAvatar: string | undefined,
    content: string,
    parentCommentId?: string
  ): Promise<{ success: boolean; commentId?: string; comment?: OfflineComment; error?: string; isOffline?: boolean }> {
    const commentId = `offline_comment_${Date.now()}_${userId}`;
    const timestamp = Date.now();

    // Create offline comment for immediate UI update
    const offlineComment: OfflineComment = {
      id: commentId,
      updateId,
      userId,
      userName,
      userAvatar,
      content,
      parentCommentId,
      timestamp,
      isOffline: true,
    };

    // Add to offline comments for immediate display
    const existingComments = this.offlineComments.get(updateId) || [];
    existingComments.push(offlineComment);
    this.offlineComments.set(updateId, existingComments);
    await this.saveOfflineComments();

    // For offline updates, always queue the comment regardless of network status
    // This prevents infinite loops and ensures proper offline handling

    // Queue for later sync
    const queueAction: CommentAction = {
      id: commentId,
      updateId,
      userId,
      userName,
      userAvatar,
      content,
      parentCommentId,
      timestamp,
      retryCount: 0,
      maxRetries: 3,
    };

    this.queue.push(queueAction);
    await this.saveQueue();

    console.log(`📱 Comment queued for offline sync: ${updateId}`);
    console.log(`📊 Total offline comments for ${updateId}: ${this.offlineComments.get(updateId)?.length || 0}`);
    console.log(`📊 Total queued comments: ${this.queue.length}`);

    return {
      success: true,
      commentId,
      comment: offlineComment,
      isOffline: true,
    };
  }

  /**
   * Get offline comments for an update
   */
  getOfflineComments(updateId: string): OfflineComment[] {
    return this.offlineComments.get(updateId) || [];
  }

  /**
   * Check if an update has pending offline comments
   */
  hasPendingComments(updateId: string): boolean {
    return this.queue.some(action => action.updateId === updateId) || 
           this.offlineComments.has(updateId);
  }

  /**
   * Get the count of offline comments for an update
   */
  getOfflineCommentCount(updateId: string): number {
    return this.offlineComments.get(updateId)?.length || 0;
  }

  /**
   * Delete an offline comment
   */
  async deleteOfflineComment(updateId: string, commentId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Remove from offline comments
      const comments = this.offlineComments.get(updateId) || [];
      const filteredComments = comments.filter(c => c.id !== commentId);

      if (filteredComments.length === 0) {
        this.offlineComments.delete(updateId);
      } else {
        this.offlineComments.set(updateId, filteredComments);
      }

      // Remove from queue if it exists
      this.queue = this.queue.filter(q => q.id !== commentId);

      // Save changes
      await this.saveOfflineComments();
      await this.saveQueue();

      console.log(`🗑️ Deleted offline comment: ${commentId}`);

      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting offline comment:', error);
      return { success: false, error: 'Failed to delete comment' };
    }
  }

  /**
   * Edit an offline comment
   */
  async editOfflineComment(updateId: string, commentId: string, newContent: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Update offline comments
      const comments = this.offlineComments.get(updateId) || [];
      const updatedComments = comments.map(comment =>
        comment.id === commentId
          ? { ...comment, content: newContent, updatedAt: Date.now() }
          : comment
      );

      this.offlineComments.set(updateId, updatedComments);

      // Update queue if it exists
      this.queue = this.queue.map(action =>
        action.id === commentId
          ? { ...action, content: newContent }
          : action
      );

      // Save changes
      await this.saveOfflineComments();
      await this.saveQueue();

      console.log(`✏️ Edited offline comment: ${commentId}`);

      return { success: true };
    } catch (error) {
      console.error('❌ Error editing offline comment:', error);
      return { success: false, error: 'Failed to edit comment' };
    }
  }

  /**
   * Process the queue when network is available
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || !networkStateManager.isOnline() || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;
    console.log(`🔄 Processing ${this.queue.length} queued comment actions`);

    const actionsToProcess = [...this.queue];
    
    for (const action of actionsToProcess) {
      try {
        const result = await realUpdatesService.addComment(
          action.updateId,
          action.userId,
          action.userName,
          action.userAvatar,
          action.content,
          action.parentCommentId,
          true // Flag to indicate this is from offline service
        );
        
        if (result.success) {
          // Remove from queue and offline comments
          this.queue = this.queue.filter(q => q.id !== action.id);
          
          // Remove the offline comment
          const comments = this.offlineComments.get(action.updateId) || [];
          const filteredComments = comments.filter(c => c.id !== action.id);
          if (filteredComments.length === 0) {
            this.offlineComments.delete(action.updateId);
          } else {
            this.offlineComments.set(action.updateId, filteredComments);
          }
          
          console.log(`✅ Synced comment action for update: ${action.updateId}`);
        } else {
          throw new Error('Add comment failed');
        }
      } catch (error) {
        console.error(`❌ Error syncing comment action for ${action.updateId}:`, error);
        
        // Increment retry count
        const actionIndex = this.queue.findIndex(q => q.id === action.id);
        if (actionIndex !== -1) {
          this.queue[actionIndex].retryCount++;
          
          // Remove if max retries exceeded
          if (this.queue[actionIndex].retryCount >= action.maxRetries) {
            console.warn(`⚠️ Max retries exceeded for comment action: ${action.updateId}`);
            this.queue.splice(actionIndex, 1);
            
            // Also remove from offline comments
            const comments = this.offlineComments.get(action.updateId) || [];
            const filteredComments = comments.filter(c => c.id !== action.id);
            if (filteredComments.length === 0) {
              this.offlineComments.delete(action.updateId);
            } else {
              this.offlineComments.set(action.updateId, filteredComments);
            }
          }
        }
      }
    }

    await this.saveQueue();
    await this.saveOfflineComments();
    this.isProcessing = false;

    console.log(`✅ Comments queue processing complete. Remaining: ${this.queue.length}`);
  }

  /**
   * Clear all offline data (for testing or reset)
   */
  async clearOfflineData(): Promise<void> {
    this.queue = [];
    this.offlineComments.clear();
    await this.saveQueue();
    await this.saveOfflineComments();
    console.log('🧹 Offline comments data cleared');
  }
}

export const offlineCommentsService = new OfflineCommentsService();
