/**
 * Business Chat Service
 * Handles real-time messaging between buyers and sellers
 */

import {
  collection,
  doc,
  addDoc,
  updateDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  writeBatch,
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';


// Business chat types
export interface BusinessChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  receiverId: string;
  receiverName: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'product_reference';
  mediaUrl?: string;
  productReference?: {
    postId: string;
    title: string;
    price?: number;
    currency?: string;
    imageUrl?: string;
  };
  timestamp: Date;
  isRead: boolean;
  isDelivered: boolean;
  reactions?: { [userId: string]: string };
  replyTo?: string;
}

export interface BusinessChat {
  id: string;
  participants: string[];
  participantNames: { [userId: string]: string };
  participantAvatars: { [userId: string]: string };
  businessId: string;
  businessName: string;
  customerId: string;
  customerName: string;
  productContext?: {
    postId: string;
    title: string;
    imageUrl?: string;
  };
  lastMessage?: BusinessChatMessage;
  lastMessageTime: Date;
  unreadCount: { [userId: string]: number };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatResponse {
  success: boolean;
  data?: any;
  error?: string;
}

class BusinessChatService {
  private chatsCollection = collection(db, 'business_chats');

  private isOnline: boolean = true;

  constructor() {
    // Monitor network state
    networkStateManager.addListener('businessChatService', (networkState) => {
      this.isOnline = networkState.isConnected;
    });
  }

  /**
   * Create or get existing chat between buyer and seller
   */
  async createOrGetChat(
    customerId: string,
    customerName: string,
    businessId: string,
    businessName: string,
    productContext?: { postId: string; title: string; imageUrl?: string }
  ): Promise<ChatResponse> {
    try {
      // Check if chat already exists
      const existingChatQuery = query(
        this.chatsCollection,
        where('participants', 'array-contains', customerId),
        where('businessId', '==', businessId)
      );

      if (this.isOnline) {
        try {
          const existingChats = await getDocs(existingChatQuery);
          
          if (!existingChats.empty) {
            const chatDoc = existingChats.docs[0];
            const chatData = chatDoc.data();
            
            const chat: BusinessChat = {
              id: chatDoc.id,
              participants: chatData.participants,
              participantNames: chatData.participantNames,
              participantAvatars: chatData.participantAvatars || {},
              businessId: chatData.businessId,
              businessName: chatData.businessName,
              customerId: chatData.customerId,
              customerName: chatData.customerName,
              productContext: chatData.productContext,
              lastMessageTime: chatData.lastMessageTime?.toDate() || new Date(),
              unreadCount: chatData.unreadCount || {},
              isActive: chatData.isActive,
              createdAt: chatData.createdAt?.toDate() || new Date(),
              updatedAt: chatData.updatedAt?.toDate() || new Date(),
            };

            // Cache offline
            await this.cacheChatOffline(chat);
            
            return { success: true, data: chat };
          }
        } catch (error) {
          console.error('❌ Online chat check failed:', error);
        }
      }

      // Create new chat
      const newChat: Omit<BusinessChat, 'id'> = {
        participants: [customerId, businessId],
        participantNames: {
          [customerId]: customerName,
          [businessId]: businessName,
        },
        participantAvatars: {},
        businessId,
        businessName,
        customerId,
        customerName,
        productContext,
        lastMessageTime: new Date(),
        unreadCount: {
          [customerId]: 0,
          [businessId]: 0,
        },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      if (this.isOnline) {
        try {
          const docRef = await addDoc(this.chatsCollection, {
            ...newChat,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            lastMessageTime: serverTimestamp(),
          });

          const createdChat = { ...newChat, id: docRef.id };
          
          // Cache offline
          await this.cacheChatOffline(createdChat);
          
          return { success: true, data: createdChat };
        } catch (error) {
          console.error('❌ Online chat creation failed:', error);
        }
      }

      // Offline handling
      const offlineId = `offline_chat_${Date.now()}_${customerId}_${businessId}`;
      const createdChat = { ...newChat, id: offlineId };
      
      await this.cacheChatOffline(createdChat);
      
      return { success: true, data: createdChat };
    } catch (error) {
      console.error('❌ Error creating/getting chat:', error);
      return { success: false, error: 'Failed to create chat' };
    }
  }

  /**
   * Send message in business chat
   */
  async sendMessage(
    chatId: string,
    senderId: string,
    senderName: string,
    receiverId: string,
    receiverName: string,
    content: string,
    type: BusinessChatMessage['type'] = 'text',
    mediaUrl?: string,
    productReference?: BusinessChatMessage['productReference']
  ): Promise<ChatResponse> {
    try {
      const message: Omit<BusinessChatMessage, 'id'> = {
        chatId,
        senderId,
        senderName,
        receiverId,
        receiverName,
        content,
        type,
        mediaUrl,
        productReference,
        timestamp: new Date(),
        isRead: false,
        isDelivered: false,
        reactions: {},
      };

      if (this.isOnline) {
        try {
          // Add message to subcollection
          const messagesRef = collection(db, `business_chats/${chatId}/messages`);
          const docRef = await addDoc(messagesRef, {
            ...message,
            timestamp: serverTimestamp(),
          });

          // Update chat with last message
          await updateDoc(doc(this.chatsCollection, chatId), {
            lastMessageTime: serverTimestamp(),
            updatedAt: serverTimestamp(),
            [`unreadCount.${receiverId}`]: 1, // Increment unread count for receiver
          });

          const sentMessage = { ...message, id: docRef.id };
          
          // Cache offline
          await this.cacheMessageOffline(sentMessage);
          
          return { success: true, data: sentMessage };
        } catch (error) {
          console.error('❌ Online message send failed:', error);
        }
      }

      // Offline handling
      const offlineId = `offline_msg_${Date.now()}_${senderId}`;
      const sentMessage = { ...message, id: offlineId };
      
      await this.cacheMessageOffline(sentMessage);
      
      return { success: true, data: sentMessage };
    } catch (error) {
      console.error('❌ Error sending message:', error);
      return { success: false, error: 'Failed to send message' };
    }
  }

  /**
   * Get chat messages
   */
  async getChatMessages(chatId: string, limitCount: number = 50): Promise<ChatResponse> {
    try {
      let messages: BusinessChatMessage[] = [];

      if (this.isOnline) {
        try {
          const messagesRef = collection(db, `business_chats/${chatId}/messages`);
          const q = query(messagesRef, orderBy('timestamp', 'desc'), limit(limitCount));
          const snapshot = await getDocs(q);

          messages = snapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              chatId: data.chatId,
              senderId: data.senderId,
              senderName: data.senderName,
              senderAvatar: data.senderAvatar,
              receiverId: data.receiverId,
              receiverName: data.receiverName,
              content: data.content,
              type: data.type,
              mediaUrl: data.mediaUrl,
              productReference: data.productReference,
              timestamp: data.timestamp?.toDate() || new Date(),
              isRead: data.isRead,
              isDelivered: data.isDelivered,
              reactions: data.reactions || {},
              replyTo: data.replyTo,
            } as BusinessChatMessage;
          }).reverse(); // Reverse to show oldest first

          // Cache messages offline
          for (const message of messages) {
            await this.cacheMessageOffline(message);
          }
        } catch (error) {
          console.error('❌ Online messages fetch failed:', error);
        }
      }

      // If online fetch failed or we're offline, get cached messages
      if (messages.length === 0) {
        messages = await this.getCachedMessagesOffline(chatId, limitCount);
      }

      return { success: true, data: messages };
    } catch (error) {
      console.error('❌ Error getting chat messages:', error);
      return { success: false, error: 'Failed to get messages' };
    }
  }

  /**
   * Listen to real-time chat messages
   */
  listenToChatMessages(
    chatId: string,
    onMessagesUpdate: (messages: BusinessChatMessage[]) => void,
    onError?: (error: Error) => void
  ): () => void {
    if (!this.isOnline) {
      // Return cached messages for offline
      this.getCachedMessagesOffline(chatId).then(messages => {
        onMessagesUpdate(messages);
      });
      return () => {}; // No-op unsubscribe
    }

    try {
      const messagesRef = collection(db, `business_chats/${chatId}/messages`);
      const q = query(messagesRef, orderBy('timestamp', 'asc'));

      return onSnapshot(q, 
        (snapshot) => {
          const messages = snapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              chatId: data.chatId,
              senderId: data.senderId,
              senderName: data.senderName,
              senderAvatar: data.senderAvatar,
              receiverId: data.receiverId,
              receiverName: data.receiverName,
              content: data.content,
              type: data.type,
              mediaUrl: data.mediaUrl,
              productReference: data.productReference,
              timestamp: data.timestamp?.toDate() || new Date(),
              isRead: data.isRead,
              isDelivered: data.isDelivered,
              reactions: data.reactions || {},
              replyTo: data.replyTo,
            } as BusinessChatMessage;
          });

          onMessagesUpdate(messages);

          // Cache messages offline
          messages.forEach(message => {
            this.cacheMessageOffline(message);
          });
        },
        (error) => {
          console.error('❌ Real-time messages error:', error);
          onError?.(error);
        }
      );
    } catch (error) {
      console.error('❌ Error setting up real-time listener:', error);
      onError?.(error as Error);
      return () => {};
    }
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(chatId: string, userId: string): Promise<ChatResponse> {
    try {
      if (this.isOnline) {
        try {
          // Update unread count in chat
          await updateDoc(doc(this.chatsCollection, chatId), {
            [`unreadCount.${userId}`]: 0,
            updatedAt: serverTimestamp(),
          });

          // Mark messages as read
          const messagesRef = collection(db, `business_chats/${chatId}/messages`);
          const unreadQuery = query(
            messagesRef,
            where('receiverId', '==', userId),
            where('isRead', '==', false)
          );

          const unreadMessages = await getDocs(unreadQuery);
          const batch = writeBatch(db);

          unreadMessages.docs.forEach(doc => {
            batch.update(doc.ref, { isRead: true });
          });

          await batch.commit();
          
          return { success: true };
        } catch (error) {
          console.error('❌ Online mark as read failed:', error);
        }
      }

      // Cache read status offline
      await this.cacheReadStatusOffline(chatId, userId);
      
      return { success: true };
    } catch (error) {
      console.error('❌ Error marking messages as read:', error);
      return { success: false, error: 'Failed to mark messages as read' };
    }
  }

  // Cache methods
  private async cacheChatOffline(chat: BusinessChat): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_chats (
          id, participants, participantNames, participantAvatars,
          businessId, businessName, customerId, customerName,
          productContext, lastMessageTime, unreadCount,
          isActive, createdAt, updatedAt, syncStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        chat.id,
        JSON.stringify(chat.participants),
        JSON.stringify(chat.participantNames),
        JSON.stringify(chat.participantAvatars),
        chat.businessId,
        chat.businessName,
        chat.customerId,
        chat.customerName,
        JSON.stringify(chat.productContext || {}),
        chat.lastMessageTime.getTime(),
        JSON.stringify(chat.unreadCount),
        chat.isActive ? 1 : 0,
        chat.createdAt.getTime(),
        chat.updatedAt.getTime(),
        this.isOnline ? 'synced' : 'pending'
      ]);
    } catch (error) {
      console.error('❌ Error caching chat offline:', error);
    }
  }

  private async cacheMessageOffline(message: BusinessChatMessage): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_chat_messages (
          id, chatId, senderId, senderName, senderAvatar,
          receiverId, receiverName, content, type, mediaUrl,
          productReference, timestamp, isRead, isDelivered,
          reactions, replyTo, syncStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        message.id,
        message.chatId,
        message.senderId,
        message.senderName,
        message.senderAvatar || null,
        message.receiverId,
        message.receiverName,
        message.content,
        message.type,
        message.mediaUrl || null,
        JSON.stringify(message.productReference || {}),
        message.timestamp.getTime(),
        message.isRead ? 1 : 0,
        message.isDelivered ? 1 : 0,
        JSON.stringify(message.reactions || {}),
        message.replyTo || null,
        this.isOnline ? 'synced' : 'pending'
      ]);
    } catch (error) {
      console.error('❌ Error caching message offline:', error);
    }
  }

  private async getCachedMessagesOffline(chatId: string, limitCount: number = 50): Promise<BusinessChatMessage[]> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const results = await database.getAllAsync(`
        SELECT * FROM business_chat_messages 
        WHERE chatId = ? 
        ORDER BY timestamp ASC 
        LIMIT ?
      `, [chatId, limitCount]);

      return results.map((result: any) => ({
        id: result.id,
        chatId: result.chatId,
        senderId: result.senderId,
        senderName: result.senderName,
        senderAvatar: result.senderAvatar,
        receiverId: result.receiverId,
        receiverName: result.receiverName,
        content: result.content,
        type: result.type,
        mediaUrl: result.mediaUrl,
        productReference: JSON.parse(result.productReference || '{}'),
        timestamp: new Date(result.timestamp),
        isRead: Boolean(result.isRead),
        isDelivered: Boolean(result.isDelivered),
        reactions: JSON.parse(result.reactions || '{}'),
        replyTo: result.replyTo,
      })) as BusinessChatMessage[];
    } catch (error) {
      console.error('❌ Error getting cached messages offline:', error);
      return [];
    }
  }

  private async cacheReadStatusOffline(chatId: string, userId: string): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_chat_read_status (
          chatId, userId, timestamp, syncStatus
        ) VALUES (?, ?, ?, ?)
      `, [chatId, userId, Date.now(), 'pending']);
    } catch (error) {
      console.error('❌ Error caching read status offline:', error);
    }
  }
}

export const businessChatService = new BusinessChatService();
