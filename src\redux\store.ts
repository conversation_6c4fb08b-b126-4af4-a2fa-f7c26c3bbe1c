import AsyncStorage from "@react-native-async-storage/async-storage";
import { combineReducers, configureStore } from "@reduxjs/toolkit";
import {
  FLUSH,
  PAUSE,
  PERSIST,
  persistReducer,
  persistStore,
  PURGE,
  REGISTER,
  REHYDRAT<PERSON>,
  createTransform,
} from "redux-persist";
import chatReducer from "./chatSlice";
import userReducer from "./userSlice";

// Transform to ensure timestamps remain as numbers, not Date objects or Firestore Timestamps
const timestampTransform = createTransform(
  // Transform state on its way to being serialized and persisted
  (inboundState: any) => {
    if (inboundState && inboundState.currentUser) {
      const user = { ...inboundState.currentUser };

      // Convert all timestamp fields to numbers for serialization
      const timestampFields = ['lastSeen', 'createdAt', 'updatedAt', 'lastLoginTime'];

      timestampFields.forEach(field => {
        if (user[field]) {
          // Handle Date objects
          if (user[field] instanceof Date) {
            user[field] = user[field].getTime();
          }
          // Handle Firestore Timestamps
          else if (user[field] && typeof user[field] === 'object' && 'seconds' in user[field] && 'nanoseconds' in user[field]) {
            user[field] = user[field].seconds * 1000 + user[field].nanoseconds / 1000000;
          }
          // Handle Firestore Timestamp objects with toDate method
          else if (user[field] && typeof user[field] === 'object' && typeof user[field].toDate === 'function') {
            user[field] = user[field].toDate().getTime();
          }
          // Ensure it's a number
          else if (typeof user[field] !== 'number') {
            user[field] = Date.now(); // Fallback to current time
          }
        }
      });

      return { ...inboundState, currentUser: user };
    }
    return inboundState;
  },
  // Transform state being rehydrated
  (outboundState: any) => {
    // Keep timestamps as numbers when retrieving from storage
    return outboundState;
  },
  // Define which reducers this transform applies to
  { whitelist: ['user'] }
);

// Persist configuration
const persistConfig = {
  key: "root",
  storage: AsyncStorage,
  whitelist: ["user", "chat"], // Only persist user and chat data
  blacklist: [], // Don't persist these reducers
  transforms: [timestampTransform], // Apply timestamp transform
};

// Combine reducers
const rootReducer = combineReducers({
  user: userReducer,
  chat: chatReducer,
});

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer as any);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types for serialization checks
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER, 'user/setUser'],
        // Ignore these field paths in the state
        ignoredPaths: [
          "register",
          "user.currentUser.lastSeen",
          "user.currentUser.createdAt",
          "user.currentUser.updatedAt",
          "user.currentUser.lastLoginTime",
          "payload.lastSeen",
          "payload.createdAt",
          "payload.updatedAt",
          "payload.lastLoginTime"
        ],
        // Custom serialization check to handle Firestore Timestamps
        isSerializable: (value: any) => {
          // Allow Firestore Timestamps to pass through (they'll be converted by our transform)
          if (value && typeof value === 'object' && 'seconds' in value && 'nanoseconds' in value) {
            return true;
          }
          // Allow Date objects to pass through (they'll be converted by our transform)
          if (value instanceof Date) {
            return true;
          }
          // Use default serializable check for everything else
          return typeof value !== 'function' && typeof value !== 'symbol';
        },
        warnAfter: 32,
      },
    }),
  devTools: process.env.NODE_ENV !== "production",
});

// Create persistor
export const persistor = persistStore(store);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
