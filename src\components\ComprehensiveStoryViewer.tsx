// 🎬 COMPREHENSIVE STORY VIEWER
// Full-screen story viewing with advanced features
// Progress indicators, reactions, replies, and analytics

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Modal,
  StatusBar,
  Animated,
  Image,
  TextInput,
  Alert,
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';
import { LinearGradient } from 'expo-linear-gradient';
import { Story, Reaction } from '../types/Update';
import { comprehensiveUpdatesService } from '../services/comprehensiveUpdatesService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface ComprehensiveStoryViewerProps {
  visible: boolean;
  stories: Story[];
  initialIndex: number;
  currentUserId: string;
  currentUserName: string;
  currentUserAvatar?: string;
  onClose: () => void;
  onStoryChange?: (index: number) => void;
  onStoryUpdate?: (storyId: string, updates: Partial<Story>) => void;
  onSendMessage?: (recipientId: string, message: string) => Promise<boolean>;
}

export const ComprehensiveStoryViewer: React.FC<ComprehensiveStoryViewerProps> = ({
  visible,
  stories,
  initialIndex,
  currentUserId,
  currentUserName,
  currentUserAvatar,
  onClose,
  onStoryChange,
  onStoryUpdate,
  onSendMessage,
}) => {
  // ==================== STATE MANAGEMENT ====================

  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isPaused, setIsPaused] = useState(false);
  const [showReactions, setShowReactions] = useState(false);
  const [showReplyInput, setShowReplyInput] = useState(false);
  const [replyText, setReplyText] = useState('');
  const [reactions, setReactions] = useState<Reaction[]>([]);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [videoProgress, setVideoProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localStories, setLocalStories] = useState<Story[]>(stories);

  // Animation refs
  const progressAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const reactionAnim = useRef(new Animated.Value(0)).current;

  // Timer refs
  const progressTimer = useRef<NodeJS.Timeout | null>(null);
  const storyDuration = 5000; // 5 seconds per story

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible && stories.length > 0) {
      setCurrentIndex(initialIndex);
      startStoryProgress();
      trackStoryView();
    }

    return () => {
      stopStoryProgress();
    };
  }, [visible, initialIndex]);

  useEffect(() => {
    if (visible) {
      startStoryProgress();
    }
  }, [currentIndex, isPaused]);

  // ==================== STORY PROGRESS MANAGEMENT ====================

  const startStoryProgress = () => {
    stopStoryProgress();

    if (isPaused) return;

    const currentStory = localStories[currentIndex];
    if (!currentStory) return;

    // For videos, let video player control progress
    const media = currentStory.media[0];
    if (media?.type === 'video') {
      progressAnim.setValue(0);
      return;
    }

    // For images, use timer-based progress
    progressAnim.setValue(0);
    const startTime = Date.now();

    progressTimer.current = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const newProgress = Math.min(elapsed / storyDuration, 1);

      progressAnim.setValue(newProgress);

      if (newProgress >= 1) {
        nextStory();
      }
    }, 50);
  };

  const stopStoryProgress = () => {
    if (progressTimer.current) {
      clearInterval(progressTimer.current);
      progressTimer.current = null;
    }
  };

  const pauseStory = () => {
    setIsPaused(true);
    stopStoryProgress();
  };

  const resumeStory = () => {
    setIsPaused(false);
    startStoryProgress();
  };

  // ==================== NAVIGATION ====================

  const nextStory = useCallback(() => {
    if (currentIndex < localStories.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      setError(null); // Clear any errors when navigating
      setIsVideoPlaying(false); // Reset video state
      setVideoProgress(0);
      onStoryChange?.(newIndex);

      // Track view for new story
      setTimeout(() => {
        trackStoryView();
      }, 100);
    } else {
      onClose();
    }
  }, [currentIndex, localStories.length, onStoryChange, onClose]);

  const previousStory = useCallback(() => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      setError(null); // Clear any errors when navigating
      setIsVideoPlaying(false); // Reset video state
      setVideoProgress(0);
      onStoryChange?.(newIndex);

      // Track view for new story immediately
      trackStoryView();
    }
  }, [currentIndex, onStoryChange]);

  // ==================== INTERACTIONS ====================

  const handleDoubleTap = useCallback(() => {
    // Double tap to like story
    handleLike();
    
    // Animate heart
    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 1.5, duration: 200, useNativeDriver: true }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 200, useNativeDriver: true }),
    ]).start();
  }, []);

  const handleLike = async () => {
    const currentStory = localStories[currentIndex];
    if (!currentStory) return;

    try {
      setIsLoading(true);
      setError(null);

      // Optimistically update local state
      const updatedStories = [...localStories];
      const isCurrentlyLiked = currentStory.likes.includes(currentUserId);

      if (isCurrentlyLiked) {
        // Remove like
        updatedStories[currentIndex] = {
          ...currentStory,
          likes: currentStory.likes.filter(userId => userId !== currentUserId),
          likeCount: Math.max(0, currentStory.likeCount - 1),
          isLikedByCurrentUser: false
        };
      } else {
        // Add like
        updatedStories[currentIndex] = {
          ...currentStory,
          likes: [...currentStory.likes, currentUserId],
          likeCount: currentStory.likeCount + 1,
          isLikedByCurrentUser: true
        };
      }

      setLocalStories(updatedStories);
      onStoryUpdate?.(currentStory.id, updatedStories[currentIndex]);

      // Sync with backend
      const result = await comprehensiveUpdatesService.toggleLike(
        currentStory.id,
        currentUserId,
        currentUserName,
        currentUserAvatar
      );

      if (!result.success) {
        // Revert optimistic update on failure
        setLocalStories(stories);
        setError('Failed to update like. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error liking story:', error);
      // Revert optimistic update
      setLocalStories(stories);
      setError('Failed to update like. Please check your connection.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReaction = async (emoji: string) => {
    const currentStory = localStories[currentIndex];
    if (!currentStory) return;

    try {
      setIsLoading(true);
      setError(null);

      // Create reaction object
      const newReaction: Reaction = {
        id: `reaction_${Date.now()}`,
        userId: currentUserId,
        userName: currentUserName,
        emoji,
        timestamp: new Date(),
      };

      // Optimistically update local state
      const updatedStories = [...localStories];
      const existingReactionIndex = currentStory.reactions.findIndex(r => r.userId === currentUserId);

      if (existingReactionIndex >= 0) {
        // Update existing reaction
        updatedStories[currentIndex] = {
          ...currentStory,
          reactions: currentStory.reactions.map((r, index) =>
            index === existingReactionIndex ? newReaction : r
          )
        };
      } else {
        // Add new reaction
        updatedStories[currentIndex] = {
          ...currentStory,
          reactions: [...currentStory.reactions, newReaction]
        };
      }

      setLocalStories(updatedStories);
      onStoryUpdate?.(currentStory.id, updatedStories[currentIndex]);

      // Add floating reaction animation
      setReactions(prev => [...prev, newReaction]);

      // Animate reaction
      Animated.sequence([
        Animated.timing(reactionAnim, { toValue: 1, duration: 300, useNativeDriver: true }),
        Animated.delay(2000),
        Animated.timing(reactionAnim, { toValue: 0, duration: 300, useNativeDriver: true }),
      ]).start(() => {
        setReactions(prev => prev.filter(r => r.id !== newReaction.id));
      });

      // Sync with backend (reactions are stored locally for now)
      // TODO: Implement reaction sync when backend method is available
      console.log('📝 Reaction added locally:', {
        storyId: currentStory.id,
        userId: currentUserId,
        emoji,
        timestamp: new Date()
      });

      setShowReactions(false);
    } catch (error) {
      console.error('❌ Error adding reaction:', error);
      setError('Failed to add reaction. Please try again.');
      // Revert optimistic update
      setLocalStories(stories);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReply = async () => {
    if (!replyText.trim()) return;

    const currentStory = localStories[currentIndex];
    if (!currentStory) return;

    // Don't send reply to yourself
    if (currentStory.userId === currentUserId) {
      Alert.alert('Cannot Reply', 'You cannot reply to your own story');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Send real message using the messaging service
      if (onSendMessage) {
        const success = await onSendMessage(currentStory.userId, replyText.trim());

        if (success) {
          Alert.alert('Reply Sent', 'Your reply has been sent privately to ' + currentStory.userName);
          setReplyText('');
          setShowReplyInput(false);
        } else {
          throw new Error('Failed to send message');
        }
      } else {
        // Fallback: Store reply locally and show success
        console.log('📤 Story reply (no messaging service):', {
          storyId: currentStory.id,
          storyOwner: currentStory.userId,
          reply: replyText.trim(),
          timestamp: new Date()
        });

        Alert.alert('Reply Sent', 'Your reply has been sent privately');
        setReplyText('');
        setShowReplyInput(false);
      }
    } catch (error) {
      console.error('❌ Error sending reply:', error);
      setError('Failed to send reply. Please check your connection.');
      Alert.alert('Error', 'Failed to send reply. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const trackStoryView = async () => {
    const currentStory = localStories[currentIndex];
    if (!currentStory) return;

    // Don't track views for your own stories
    if (currentStory.userId === currentUserId) return;

    try {
      // Update local state optimistically
      const updatedStories = [...localStories];
      const hasViewed = currentStory.views.some(view => view.userId === currentUserId);

      if (!hasViewed) {
        const newView = {
          userId: currentUserId,
          userName: currentUserName,
          userAvatar: currentUserAvatar,
          timestamp: new Date(),
          type: 'view' as const
        };

        updatedStories[currentIndex] = {
          ...currentStory,
          views: [...currentStory.views, newView],
          viewCount: currentStory.viewCount + 1,
          isViewedByCurrentUser: true
        };

        setLocalStories(updatedStories);
        onStoryUpdate?.(currentStory.id, updatedStories[currentIndex]);
      }

      // Sync with backend
      await comprehensiveUpdatesService.viewUpdate(
        currentStory.id,
        currentUserId,
        currentUserName,
        currentUserAvatar
      );
    } catch (error) {
      console.error('❌ Error tracking story view:', error);
      // Don't show error to user for view tracking failures
    }
  };

  // ==================== GESTURE HANDLERS ====================

  const panGesture = Gesture.Pan()
    .minDistance(10) // Prevent accidental pans
    .onChange((event) => {
      // Only allow vertical pan for closing
      if (Math.abs(event.translationY) > Math.abs(event.translationX)) {
        reactionAnim.setValue(Math.max(0, event.translationY));
      }
    })
    .onEnd((event) => {
      const { translationY, velocityY } = event;

      if (translationY > 100 || velocityY > 500) {
        // Swipe down to close
        onClose();
      } else {
        // Snap back
        Animated.spring(reactionAnim, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    });

  const doubleTapGesture = Gesture.Tap()
    .numberOfTaps(2)
    .maxDelay(300) // Maximum delay between taps
    .onEnd(() => {
      handleDoubleTap();
    });

  const singleTapGesture = Gesture.Tap()
    .numberOfTaps(1)
    .maxDelay(300)
    .onEnd(() => {
      // Pause/resume on single tap
      if (isPaused) {
        resumeStory();
      } else {
        pauseStory();
      }
    });

  // Combine gestures with proper priority
  const combinedGesture = Gesture.Race(
    doubleTapGesture,
    singleTapGesture,
    panGesture
  );

  // ==================== RENDER METHODS ====================

  const renderProgressBars = () => (
    <View style={styles.progressContainer}>
      {stories.map((_, index) => (
        <View key={index} style={styles.progressBarContainer}>
          <View style={styles.progressBarBackground} />
          <Animated.View
            style={[
              styles.progressBarFill,
              {
                width: index === currentIndex 
                  ? progressAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0%', '100%'],
                    })
                  : index < currentIndex ? '100%' : '0%'
              }
            ]}
          />
        </View>
      ))}
    </View>
  );

  const renderStoryHeader = () => {
    const currentStory = localStories[currentIndex];
    if (!currentStory) return null;

    const getTimeAgo = (timestamp: Date) => {
      const now = Date.now();
      const diff = now - timestamp.getTime();
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor(diff / (1000 * 60));

      if (hours > 0) return `${hours}h`;
      if (minutes > 0) return `${minutes}m`;
      return 'now';
    };

    return (
      <View style={styles.storyHeader}>
        <View style={styles.userInfo}>
          <View style={styles.avatarContainer}>
            {currentStory.userAvatar ? (
              <Image
                source={{ uri: currentStory.userAvatar }}
                style={styles.userAvatar}
                onError={() => {
                  console.warn('Failed to load user avatar:', currentStory.userAvatar);
                }}
              />
            ) : (
              <View style={[styles.userAvatar, styles.defaultAvatar]}>
                <Text style={styles.defaultAvatarText}>
                  {currentStory.userName.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{currentStory.userName}</Text>
            <Text style={styles.storyTime}>
              {getTimeAgo(currentStory.timestamp)}
            </Text>
          </View>
        </View>

        <View style={styles.headerActions}>
          {error && (
            <TouchableOpacity
              style={styles.errorButton}
              onPress={() => setError(null)}
            >
              <Ionicons name="warning" size={20} color="#ff6b6b" />
            </TouchableOpacity>
          )}
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderStoryContent = () => {
    const currentStory = stories[currentIndex];
    if (!currentStory) return null;

    const media = currentStory.media[0];
    if (!media) return null;

    return (
      <View style={styles.storyContent}>
        {media.type === 'video' ? (
          <Video
            source={{ uri: media.url }}
            style={styles.storyMedia}
            resizeMode={ResizeMode.COVER}
            shouldPlay={!isPaused && isVideoPlaying}
            isLooping={false}
            onPlaybackStatusUpdate={(status: any) => {
              if (status.isLoaded) {
                setIsVideoPlaying(status.shouldPlay);

                // Update progress based on video playback
                if (status.durationMillis && status.positionMillis) {
                  const progress = status.positionMillis / status.durationMillis;
                  setVideoProgress(progress);
                  progressAnim.setValue(progress);
                }

                // Auto-advance when video finishes
                if (status.didJustFinish) {
                  nextStory();
                }
              }
            }}
            onLoad={(status: any) => {
              setIsVideoPlaying(true);
              // Start video immediately for stories
              if (status.durationMillis) {
                console.log('📹 Video loaded, duration:', status.durationMillis / 1000, 'seconds');
              }
            }}
            onError={(error: any) => {
              console.error('❌ Video playback error:', error);
              setError('Failed to load video. Please try again.');
            }}
          />
        ) : (
          <Image
            source={{ uri: media.url }}
            style={styles.storyMedia}
            onLoad={() => {
              console.log('🖼️ Image loaded successfully');
            }}
            onError={(error: any) => {
              console.error('❌ Image load error:', error);
              setError('Failed to load image. Please try again.');
            }}
          />
        )}

        {/* Story Caption */}
        {currentStory.caption && (
          <View style={styles.captionContainer}>
            <Text style={styles.captionText}>{currentStory.caption}</Text>
          </View>
        )}

        {/* Touch Areas for Navigation */}
        <TouchableOpacity 
          style={styles.leftTouchArea} 
          onPress={previousStory}
          onLongPress={pauseStory}
          onPressOut={resumeStory}
        />
        <TouchableOpacity 
          style={styles.rightTouchArea} 
          onPress={nextStory}
          onLongPress={pauseStory}
          onPressOut={resumeStory}
        />
      </View>
    );
  };

  const renderStoryActions = () => (
    <View style={styles.storyActions}>
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={() => setShowReactions(true)}
      >
        <Ionicons name="heart-outline" size={24} color="white" />
      </TouchableOpacity>
      
      <TouchableOpacity 
        style={styles.actionButton}
        onPress={() => setShowReplyInput(true)}
      >
        <Ionicons name="chatbubble-outline" size={24} color="white" />
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.actionButton}>
        <Ionicons name="share-outline" size={24} color="white" />
      </TouchableOpacity>
    </View>
  );

  const renderReactionPicker = () => (
    <Modal visible={showReactions} transparent animationType="slide">
      <View style={styles.reactionModal}>
        <TouchableOpacity 
          style={styles.reactionBackdrop} 
          onPress={() => setShowReactions(false)} 
        />
        <View style={styles.reactionPicker}>
          {['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥'].map((emoji) => (
            <TouchableOpacity
              key={emoji}
              style={styles.reactionButton}
              onPress={() => handleReaction(emoji)}
            >
              <Text style={styles.reactionEmoji}>{emoji}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </Modal>
  );

  const renderReplyInput = () => (
    <Modal visible={showReplyInput} transparent animationType="slide">
      <View style={styles.replyModal}>
        <TouchableOpacity 
          style={styles.replyBackdrop} 
          onPress={() => setShowReplyInput(false)} 
        />
        <View style={styles.replyContainer}>
          <TextInput
            style={styles.replyInput}
            placeholder="Send a message..."
            placeholderTextColor="#999"
            value={replyText}
            onChangeText={setReplyText}
            multiline
            autoFocus
          />
          <TouchableOpacity style={styles.sendButton} onPress={handleReply}>
            <Ionicons name="send" size={20} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const renderFloatingReactions = () => (
    <View style={styles.floatingReactions}>
      {reactions.map((reaction) => (
        <Animated.View
          key={reaction.id}
          style={[
            styles.floatingReaction,
            {
              opacity: reactionAnim,
              transform: [
                {
                  translateY: reactionAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -100],
                  }),
                },
              ],
            },
          ]}
        >
          <Text style={styles.floatingReactionEmoji}>{reaction.emoji}</Text>
        </Animated.View>
      ))}
    </View>
  );

  if (!visible || stories.length === 0) return null;

  return (
    <Modal visible={visible} animationType="fade" statusBarTranslucent>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <GestureDetector gesture={combinedGesture}>
        <Animated.View style={styles.container}>
          <LinearGradient
            colors={['rgba(0,0,0,0.3)', 'transparent', 'rgba(0,0,0,0.3)']}
            style={styles.gradient}
          >
            {renderProgressBars()}
            {renderStoryHeader()}
            {renderStoryContent()}
            {renderStoryActions()}
            {renderFloatingReactions()}

            {/* Loading Indicator */}
            {isLoading && (
              <View style={styles.loadingOverlay}>
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>Loading...</Text>
                </View>
              </View>
            )}

            {/* Error Display */}
            {error && (
              <View style={styles.errorOverlay}>
                <View style={styles.errorContainer}>
                  <Ionicons name="warning" size={24} color="#ff6b6b" />
                  <Text style={styles.errorText}>{error}</Text>
                  <TouchableOpacity
                    style={styles.errorDismiss}
                    onPress={() => setError(null)}
                  >
                    <Text style={styles.errorDismissText}>Dismiss</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </LinearGradient>
        </Animated.View>
      </GestureDetector>
      
      {renderReactionPicker()}
      {renderReplyInput()}
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  gradient: {
    flex: 1,
  },
  progressContainer: {
    flexDirection: 'row',
    paddingHorizontal: 8,
    paddingTop: 50,
    paddingBottom: 8,
  },
  progressBarContainer: {
    flex: 1,
    height: 2,
    backgroundColor: 'rgba(255,255,255,0.3)',
    marginHorizontal: 2,
    borderRadius: 1,
    overflow: 'hidden',
  },
  progressBarBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255,255,255,0.3)',
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: 'white',
  },
  storyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: 8,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  defaultAvatar: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  defaultAvatarText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  storyTime: {
    color: 'rgba(255,255,255,0.7)',
    fontSize: 12,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorButton: {
    padding: 8,
    marginRight: 4,
  },
  closeButton: {
    padding: 8,
  },
  storyContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  storyMedia: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    position: 'absolute',
  },
  captionContainer: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
  },
  captionText: {
    color: 'white',
    fontSize: 16,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  leftTouchArea: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: SCREEN_WIDTH * 0.3,
    height: '100%',
  },
  rightTouchArea: {
    position: 'absolute',
    right: 0,
    top: 0,
    width: SCREEN_WIDTH * 0.7,
    height: '100%',
  },
  storyActions: {
    position: 'absolute',
    bottom: 50,
    right: 16,
    alignItems: 'center',
  },
  actionButton: {
    padding: 12,
    marginVertical: 8,
  },
  floatingReactions: {
    position: 'absolute',
    bottom: 200,
    left: 20,
  },
  floatingReaction: {
    marginVertical: 4,
  },
  floatingReactionEmoji: {
    fontSize: 24,
  },
  reactionModal: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  reactionBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  reactionPicker: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingVertical: 20,
    paddingHorizontal: 16,
    justifyContent: 'space-around',
  },
  reactionButton: {
    padding: 8,
  },
  reactionEmoji: {
    fontSize: 32,
  },
  replyModal: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  replyBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
  },
  replyContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0,0,0,0.9)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'flex-end',
  },
  replyInput: {
    flex: 1,
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    color: 'white',
    maxHeight: 100,
    marginRight: 8,
  },
  sendButton: {
    backgroundColor: '#667eea',
    borderRadius: 20,
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    backgroundColor: 'rgba(0,0,0,0.8)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  loadingText: {
    color: 'white',
    fontSize: 14,
  },
  errorOverlay: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
  },
  errorContainer: {
    backgroundColor: 'rgba(0,0,0,0.9)',
    padding: 16,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  errorText: {
    color: 'white',
    fontSize: 14,
    flex: 1,
    marginLeft: 8,
  },
  errorDismiss: {
    marginLeft: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  errorDismissText: {
    color: '#667eea',
    fontSize: 12,
    fontWeight: '600',
  },
});
