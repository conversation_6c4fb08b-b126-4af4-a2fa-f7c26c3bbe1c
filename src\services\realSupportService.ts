import {
  doc,
  setDoc,
  getDoc,
  collection,
  query,
  orderBy,
  getDocs,
  addDoc,
  serverTimestamp,
  where,
  limit,
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface SupportTicket {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  subject: string;
  description: string;
  category: 'bug' | 'feature' | 'account' | 'billing' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  attachments?: string[];
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  adminNotes?: string;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  helpful: number;
  notHelpful: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface SupportStats {
  totalTickets: number;
  openTickets: number;
  resolvedTickets: number;
  averageResponseTime: number;
  userSatisfaction: number;
}

class RealSupportService {
  private isInitialized = false;
  private ticketCache: Map<string, SupportTicket[]> = new Map();
  private faqCache: FAQItem[] = [];
  private syncQueue: Set<string> = new Set();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();
      await this.loadDataIntoCache();

      // Set up network state listener for sync
      networkStateManager.addListener('realSupportService', this.handleNetworkStateChange.bind(this), 5);

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async loadDataIntoCache(): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();

      // Load tickets
      const ticketResult = await db.getAllAsync('SELECT * FROM support_tickets ORDER BY createdAt DESC');
      const ticketsByUser: Map<string, SupportTicket[]> = new Map();

      ticketResult.forEach((row: any) => {
        const ticket = this.rowToSupportTicket(row);
        const userTickets = ticketsByUser.get(ticket.userId) || [];
        userTickets.push(ticket);
        ticketsByUser.set(ticket.userId, userTickets);
      });

      this.ticketCache = ticketsByUser;

      // Load FAQs
      const faqResult = await db.getAllAsync('SELECT * FROM faqs ORDER BY helpful DESC');
      this.faqCache = faqResult.map((row: any) => this.rowToFAQItem(row));
    } catch (error) {
      // Continue without cache if loading fails
    }
  }

  private rowToSupportTicket(row: any): SupportTicket {
    return {
      id: row.id,
      userId: row.userId,
      userName: row.userName,
      userEmail: row.userEmail,
      subject: row.subject,
      description: row.description,
      category: row.category,
      priority: row.priority,
      status: row.status,
      attachments: row.attachments ? JSON.parse(row.attachments) : undefined,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      resolvedAt: row.resolvedAt ? new Date(row.resolvedAt) : undefined,
      adminNotes: row.adminNotes,
    };
  }

  private rowToFAQItem(row: any): FAQItem {
    return {
      id: row.id,
      question: row.question,
      answer: row.answer,
      category: row.category,
      tags: row.tags ? JSON.parse(row.tags) : [],
      helpful: row.helpful,
      notHelpful: row.notHelpful,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
    };
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected && this.syncQueue.size > 0) {
      this.processSyncQueue();
    }
  }

  private async processSyncQueue(): Promise<void> {
    if (!networkStateManager.isOnline() || this.syncQueue.size === 0) return;

    const ticketIds = Array.from(this.syncQueue);
    this.syncQueue.clear();

    for (const ticketId of ticketIds) {
      try {
        await this.syncTicketWithFirebase(ticketId);
      } catch (error) {
        // Re-add to queue for retry
        this.syncQueue.add(ticketId);
      }
    }
  }

  private async syncTicketWithFirebase(ticketId: string): Promise<void> {
    try {
      const offlineDb = offlineDatabaseService.getDatabase();
      const result = await offlineDb.getFirstAsync(`
        SELECT * FROM support_tickets WHERE id = ?
      `, [ticketId]);

      if (result) {
        const ticket = this.rowToSupportTicket(result as any);
        const ticketRef = doc(db, 'support_tickets', ticketId);
        await setDoc(ticketRef, {
          ...ticket,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });

        // Update sync status
        await this.updateTicketSyncStatus(ticketId, 'synced');
      }
    } catch (error) {
      await this.updateTicketSyncStatus(ticketId, 'failed');
      throw error;
    }
  }

  private async updateTicketSyncStatus(ticketId: string, status: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE support_tickets SET syncStatus = ?, lastSyncAttempt = ? WHERE id = ?
    `, [status, Date.now(), ticketId]);
  }

  private async saveTicketOffline(ticket: SupportTicket): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      INSERT OR REPLACE INTO support_tickets (
        id, userId, userName, userEmail, subject, description, category,
        priority, status, attachments, createdAt, updatedAt, resolvedAt,
        adminNotes, syncStatus
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      ticket.id,
      ticket.userId,
      ticket.userName,
      ticket.userEmail,
      ticket.subject,
      ticket.description,
      ticket.category,
      ticket.priority,
      ticket.status,
      ticket.attachments ? JSON.stringify(ticket.attachments) : null,
      ticket.createdAt.getTime(),
      ticket.updatedAt.getTime(),
      ticket.resolvedAt ? ticket.resolvedAt.getTime() : null,
      ticket.adminNotes || null,
      'pending'
    ]);
  }

  private async saveFAQOffline(faq: FAQItem): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      INSERT OR REPLACE INTO faqs (
        id, question, answer, category, tags, helpful, notHelpful,
        createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      faq.id,
      faq.question,
      faq.answer,
      faq.category,
      JSON.stringify(faq.tags),
      faq.helpful,
      faq.notHelpful,
      faq.createdAt.getTime(),
      faq.updatedAt.getTime()
    ]);
  }

  /**
   * Submit a support ticket (with offline support)
   */
  async submitTicket(
    userId: string,
    userName: string,
    userEmail: string,
    ticketData: {
      subject: string;
      description: string;
      category: 'bug' | 'feature' | 'account' | 'billing' | 'other';
      priority: 'low' | 'medium' | 'high' | 'urgent';
      attachments?: string[];
    }
  ): Promise<{ success: boolean; ticketId?: string; error?: string }> {
    try {
      const ticketRef = collection(db, 'support_tickets');
      const ticketDoc = await addDoc(ticketRef, {
        userId,
        userName,
        userEmail,
        ...ticketData,
        status: 'open',
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      // Create the ticket object for offline storage and caching
      const ticket: SupportTicket = {
        id: ticketDoc.id,
        userId,
        userName,
        userEmail,
        subject: ticketData.subject,
        description: ticketData.description,
        category: ticketData.category,
        priority: ticketData.priority,
        status: 'open',
        attachments: ticketData.attachments,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save ticket offline
      await this.saveTicketOffline(ticket);

      // Update cache
      const userTickets = this.ticketCache.get(userId) || [];
      userTickets.unshift(ticket);
      this.ticketCache.set(userId, userTickets);

      return { success: true, ticketId: ticketDoc.id };
    } catch (error) {
      return { success: false, error: 'Failed to submit support ticket' };
    }
  }

  /**
   * Get user's support tickets
   */
  async getUserTickets(userId: string): Promise<{ success: boolean; tickets?: SupportTicket[]; error?: string }> {
    try {
      // Try cache first
      const cachedTickets = this.ticketCache.get(userId);
      if (cachedTickets) {
        return { success: true, tickets: cachedTickets };
      }

      // Try offline database
      const offlineDb = offlineDatabaseService.getDatabase();
      const result = await offlineDb.getAllAsync(`
        SELECT * FROM support_tickets WHERE userId = ? ORDER BY createdAt DESC
      `, [userId]);

      if (result.length > 0) {
        const tickets = result.map((row: any) => this.rowToSupportTicket(row));
        this.ticketCache.set(userId, tickets);
        return { success: true, tickets };
      }

      // Try online if available
      if (networkStateManager.isOnline()) {
        try {
          const ticketsRef = collection(db, 'support_tickets');
          const q = query(
            ticketsRef,
            where('userId', '==', userId),
            orderBy('createdAt', 'desc')
          );

          const querySnapshot = await getDocs(q);
          const tickets: SupportTicket[] = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date(),
            updatedAt: doc.data().updatedAt?.toDate() || new Date(),
            resolvedAt: doc.data().resolvedAt?.toDate(),
          })) as SupportTicket[];

          // Cache offline
          for (const ticket of tickets) {
            await this.saveTicketOffline(ticket);
          }
          this.ticketCache.set(userId, tickets);

          return { success: true, tickets };
        } catch (onlineError) {
          // Continue to return empty array
        }
      }

      return { success: true, tickets: [] };
    } catch (error) {
      return { success: false, error: 'Failed to get support tickets' };
    }
  }

  /**
   * Get FAQ items
   */
  async getFAQs(category?: string): Promise<{ success: boolean; faqs?: FAQItem[]; error?: string }> {
    try {
      // Try cache first
      let cachedFAQs = this.faqCache;
      if (category) {
        cachedFAQs = this.faqCache.filter(faq => faq.category === category);
      }

      if (cachedFAQs.length > 0) {
        return { success: true, faqs: cachedFAQs };
      }

      // Try offline database
      const offlineDb = offlineDatabaseService.getDatabase();
      let sqlQuery = 'SELECT * FROM faqs ORDER BY helpful DESC LIMIT 50';
      let params: any[] = [];

      if (category) {
        sqlQuery = 'SELECT * FROM faqs WHERE category = ? ORDER BY helpful DESC';
        params = [category];
      }

      const result = await offlineDb.getAllAsync(sqlQuery, params);

      if (result.length > 0) {
        const faqs = result.map((row: any) => this.rowToFAQItem(row));
        if (!category) {
          this.faqCache = faqs;
        }
        return { success: true, faqs };
      }

      // Try online if available
      if (networkStateManager.isOnline()) {
        try {
          const faqsRef = collection(db, 'faqs');
          let q = query(faqsRef, orderBy('helpful', 'desc'), limit(50));

          if (category) {
            q = query(faqsRef, where('category', '==', category), orderBy('helpful', 'desc'));
          }

          const querySnapshot = await getDocs(q);
          const faqs: FAQItem[] = querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data(),
            createdAt: doc.data().createdAt?.toDate() || new Date(),
            updatedAt: doc.data().updatedAt?.toDate() || new Date(),
          })) as FAQItem[];

          // Cache offline
          for (const faq of faqs) {
            await this.saveFAQOffline(faq);
          }

          if (!category) {
            this.faqCache = faqs;
          }

          return { success: true, faqs };
        } catch (onlineError) {
          // Continue to return empty array
        }
      }

      return { success: true, faqs: [] };
    } catch (error) {
      return { success: false, error: 'Failed to get FAQs' };
    }
  }

  /**
   * Rate FAQ helpfulness
   */
  async rateFAQ(faqId: string, helpful: boolean): Promise<{ success: boolean; error?: string }> {
    try {
      const faqRef = doc(db, 'faqs', faqId);
      const faqDoc = await getDoc(faqRef);
      
      if (!faqDoc.exists()) {
        return { success: false, error: 'FAQ not found' };
      }

      const currentData = faqDoc.data();
      const updates = helpful 
        ? { helpful: (currentData.helpful || 0) + 1 }
        : { notHelpful: (currentData.notHelpful || 0) + 1 };

      await setDoc(faqRef, {
        ...currentData,
        ...updates,
        updatedAt: serverTimestamp(),
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to rate FAQ' };
    }
  }

  /**
   * Search FAQs
   */
  async searchFAQs(searchTerm: string): Promise<{ success: boolean; faqs?: FAQItem[]; error?: string }> {
    try {
      // In a real implementation, you'd use a search service like Algolia
      // For now, we'll do a simple client-side search after fetching all FAQs
      const result = await this.getFAQs();
      
      if (!result.success || !result.faqs) {
        return result;
      }

      const searchTermLower = searchTerm.toLowerCase();
      const filteredFAQs = result.faqs.filter(faq => 
        faq.question.toLowerCase().includes(searchTermLower) ||
        faq.answer.toLowerCase().includes(searchTermLower) ||
        faq.tags.some(tag => tag.toLowerCase().includes(searchTermLower))
      );

      return { success: true, faqs: filteredFAQs };
    } catch (error) {
      return { success: false, error: 'Failed to search FAQs' };
    }
  }

  /**
   * Get support statistics
   */
  async getSupportStats(): Promise<{ success: boolean; stats?: SupportStats; error?: string }> {
    try {
      const statsRef = doc(db, 'support_stats', 'global');
      const statsDoc = await getDoc(statsRef);
      
      if (!statsDoc.exists()) {
        // Return default stats if none exist
        const defaultStats: SupportStats = {
          totalTickets: 0,
          openTickets: 0,
          resolvedTickets: 0,
          averageResponseTime: 24, // hours
          userSatisfaction: 4.5, // out of 5
        };
        return { success: true, stats: defaultStats };
      }

      const stats = statsDoc.data() as SupportStats;
      return { success: true, stats };
    } catch (error) {
      return { success: false, error: 'Failed to get support statistics' };
    }
  }

  /**
   * Report a bug
   */
  async reportBug(
    userId: string,
    userName: string,
    userEmail: string,
    bugData: {
      title: string;
      description: string;
      steps: string;
      expectedBehavior: string;
      actualBehavior: string;
      deviceInfo: string;
      appVersion: string;
      screenshots?: string[];
    }
  ): Promise<{ success: boolean; ticketId?: string; error?: string }> {
    try {
      const description = `
**Bug Description:**
${bugData.description}

**Steps to Reproduce:**
${bugData.steps}

**Expected Behavior:**
${bugData.expectedBehavior}

**Actual Behavior:**
${bugData.actualBehavior}

**Device Information:**
${bugData.deviceInfo}

**App Version:**
${bugData.appVersion}
      `.trim();

      return await this.submitTicket(userId, userName, userEmail, {
        subject: `Bug Report: ${bugData.title}`,
        description,
        category: 'bug',
        priority: 'medium',
        attachments: bugData.screenshots,
      });
    } catch (error) {
      return { success: false, error: 'Failed to report bug' };
    }
  }

  /**
   * Request a feature
   */
  async requestFeature(
    userId: string,
    userName: string,
    userEmail: string,
    featureData: {
      title: string;
      description: string;
      useCase: string;
      priority: 'low' | 'medium' | 'high';
    }
  ): Promise<{ success: boolean; ticketId?: string; error?: string }> {
    try {
      const description = `
**Feature Request:**
${featureData.description}

**Use Case:**
${featureData.useCase}

**Priority:** ${featureData.priority}
      `.trim();

      return await this.submitTicket(userId, userName, userEmail, {
        subject: `Feature Request: ${featureData.title}`,
        description,
        category: 'feature',
        priority: featureData.priority,
      });
    } catch (error) {
      return { success: false, error: 'Failed to request feature' };
    }
  }

  /**
   * Get contact information
   */
  getContactInfo() {
    return {
      email: '<EMAIL>',
      phone: '+256 787 272 445',
      website: 'https://irachat.app/support',
      socialMedia: {
        twitter: '@IraChat',
        facebook: 'IraChatApp',
        instagram: '@irachatapp',
      },
      businessHours: 'Monday - Friday, 8 AM - 6 PM EAT (East Africa Time)',
      emergencySupport: '24/7 for critical issues',
    };
  }

  /**
   * Cleanup method
   */
  cleanup(): void {
    networkStateManager.removeListener('realSupportService');
    this.ticketCache.clear();
    this.faqCache = [];
    this.syncQueue.clear();
    this.isInitialized = false;
  }
}

export const realSupportService = new RealSupportService();
