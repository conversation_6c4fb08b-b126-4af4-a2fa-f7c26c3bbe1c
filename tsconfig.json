{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "es2017", "lib": ["DOM", "ESNext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "preserve", "moduleDetection": "force", "moduleResolution": "bundler", "customConditions": ["react-native"], "resolveJsonModule": true, "isolatedModules": true, "jsx": "react-native", "allowSyntheticDefaultImports": true, "incremental": true, "downlevelIteration": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", "nativewind-env.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base.json"}