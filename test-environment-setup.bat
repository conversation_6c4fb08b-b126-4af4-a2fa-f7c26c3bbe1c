@echo off
echo ========================================
echo Testing Development Environment Setup
echo ========================================
echo.

echo Checking Environment Variables:
echo JAVA_HOME: %JAVA_HOME%
echo ANDROID_HOME: %ANDROID_HOME%
echo ANDROID_SDK_ROOT: %ANDROID_SDK_ROOT%
echo.

echo Testing Tools:
echo.

echo Testing Java...
java -version
if %ERRORLEVEL% EQU 0 (
    echo ✓ Java is working
) else (
    echo ❌ Java is not working
)
echo.

echo Testing Node.js...
node --version
if %ERRORLEVEL% EQU 0 (
    echo ✓ Node.js is working
) else (
    echo ❌ Node.js is not working
)
echo.

echo Testing npm...
npm --version
if %ERRORLEVEL% EQU 0 (
    echo ✓ npm is working
) else (
    echo ❌ npm is not working
)
echo.

echo Testing ADB...
adb version
if %ERRORLEVEL% EQU 0 (
    echo ✓ ADB is working
) else (
    echo ❌ ADB is not working
)
echo.

echo Testing Expo CLI...
npx expo --version
if %ERRORLEVEL% EQU 0 (
    echo ✓ Expo CLI is working
) else (
    echo ❌ Expo CLI is not working
)
echo.

echo ========================================
echo Environment Test Complete
echo ========================================
echo.
echo If all tools show as working, you can now run:
echo   npm install
echo   npx expo start
echo.
pause
