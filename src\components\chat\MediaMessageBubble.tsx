/**
 * Media Message Bubble for IraChat
 * Enhanced media bubble with thumbnails, captions, and download states
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';
import { useTheme } from '../../contexts/ThemeContext';
import { enhancedMediaCacheService } from '../../services/enhancedMediaCacheService';

interface MediaMessageBubbleProps {
  messageId: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'voice' | 'sticker' | 'location' | 'contact';
  mediaUrl?: string;
  thumbnailUrl?: string;
  caption?: string;
  fileName?: string;
  fileSize?: number;
  duration?: number;
  isOwn: boolean;
  onPress?: () => void;
  onLongPress?: () => void;
  onMediaPress?: () => void;
}

export const MediaMessageBubble: React.FC<MediaMessageBubbleProps> = ({
  messageId,
  type,
  mediaUrl,
  thumbnailUrl,
  caption,
  fileName,
  fileSize,
  duration,
  isOwn,
  onPress,
  onLongPress,
  onMediaPress,
}) => {
  const { colors } = useTheme();
  const [downloadState, setDownloadState] = useState<'none' | 'downloading' | 'downloaded' | 'error'>('none');
  const [localMediaPath, setLocalMediaPath] = useState<string | null>(null);
  const [downloadProgress, setDownloadProgress] = useState(0);

  useEffect(() => {
    checkCachedMedia();
  }, [messageId]);

  const checkCachedMedia = async () => {
    try {
      const cachedMedia = await enhancedMediaCacheService.getCachedMedia(messageId);
      if (cachedMedia && cachedMedia.localPath) {
        setLocalMediaPath(cachedMedia.localPath);
        setDownloadState('downloaded');
      } else {
        setDownloadState('none');
      }
    } catch (error) {
      console.error('Failed to check cached media:', error);
      setDownloadState('error');
    }
  };

  const handleDownload = async () => {
    if (!mediaUrl || downloadState === 'downloading') return;

    setDownloadState('downloading');
    setDownloadProgress(0);

    try {
      const result = await enhancedMediaCacheService.cacheMedia(
        messageId,
        'chat_id', // You'd pass the actual chat ID
        mediaUrl,
        type as any,
        fileName
      );

      if (result) {
        setLocalMediaPath(result.localPath || null);
        setDownloadState('downloaded');
      } else {
        setDownloadState('error');
      }
    } catch (error) {
      console.error('Failed to download media:', error);
      setDownloadState('error');
    }
  };

  const formatFileSize = (bytes?: number): string => {
    if (!bytes || bytes === 0) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = bytes / Math.pow(1024, i);

    // Show more precision for smaller files
    const precision = i <= 1 ? 0 : 1; // No decimals for B and KB, 1 decimal for MB+
    return `${size.toFixed(precision)} ${sizes[i]}`;
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderImageContent = () => (
    <View style={styles.imageContainer}>
      <Image
        source={{ uri: localMediaPath || thumbnailUrl || mediaUrl }}
        style={styles.image}
        resizeMode="cover"
      />
      {downloadState === 'none' && !localMediaPath && (
        <TouchableOpacity style={styles.downloadOverlay} onPress={handleDownload}>
          <View style={[styles.downloadButton, { backgroundColor: colors.surface }]}>
            <Ionicons name="download" size={24} color={colors.primary} />
          </View>
        </TouchableOpacity>
      )}
      {downloadState === 'downloading' && (
        <View style={styles.downloadOverlay}>
          <View style={[styles.downloadButton, { backgroundColor: colors.surface }]}>
            <ActivityIndicator size="small" color={colors.primary} />
          </View>
        </View>
      )}
    </View>
  );

  const renderVideoContent = () => (
    <View style={styles.videoContainer}>
      {localMediaPath ? (
        <Video
          source={{ uri: localMediaPath }}
          style={styles.video}
          useNativeControls={false}
          resizeMode={ResizeMode.COVER}
          shouldPlay={false}
          isLooping={false}
        />
      ) : (
        <Image
          source={{ uri: thumbnailUrl || mediaUrl }}
          style={styles.video}
          resizeMode="cover"
        />
      )}
      
      {/* Play button overlay */}
      <TouchableOpacity 
        style={styles.playOverlay} 
        onPress={localMediaPath ? onMediaPress : handleDownload}
      >
        <View style={[styles.playButton, { backgroundColor: 'rgba(0,0,0,0.6)' }]}>
          <Ionicons 
            name={localMediaPath ? "play" : "download"} 
            size={32} 
            color="white" 
          />
        </View>
      </TouchableOpacity>

      {/* Duration badge */}
      {duration && (
        <View style={[styles.durationBadge, { backgroundColor: 'rgba(0,0,0,0.6)' }]}>
          <Text style={styles.durationText}>{formatDuration(duration)}</Text>
        </View>
      )}

      {downloadState === 'downloading' && (
        <View style={styles.downloadOverlay}>
          <ActivityIndicator size="large" color="white" />
        </View>
      )}
    </View>
  );

  const renderAudioContent = () => (
    <View style={[styles.audioContainer, { backgroundColor: colors.surface }]}>
      <TouchableOpacity 
        style={styles.audioPlayButton}
        onPress={localMediaPath ? onMediaPress : handleDownload}
      >
        <Ionicons 
          name={localMediaPath ? "play" : "download"} 
          size={24} 
          color={colors.primary} 
        />
      </TouchableOpacity>
      
      <View style={styles.audioInfo}>
        <Text style={[styles.audioTitle, { color: colors.text }]}>
          {fileName || 'Audio Message'}
        </Text>
        <Text style={[styles.audioMeta, { color: colors.textSecondary }]}>
          {formatDuration(duration)} • {formatFileSize(fileSize)}
        </Text>
      </View>

      {downloadState === 'downloading' && (
        <ActivityIndicator size="small" color={colors.primary} />
      )}
    </View>
  );

  const renderDocumentContent = () => (
    <View style={[styles.documentContainer, { backgroundColor: colors.surface }]}>
      <View style={[styles.documentIcon, { backgroundColor: colors.primary }]}>
        <Ionicons name="document" size={24} color="white" />
      </View>
      
      <View style={styles.documentInfo}>
        <Text style={[styles.documentTitle, { color: colors.text }]} numberOfLines={1}>
          {fileName || 'Document'}
        </Text>
        <Text style={[styles.documentMeta, { color: colors.textSecondary }]}>
          {formatFileSize(fileSize)}
        </Text>
      </View>

      <TouchableOpacity 
        style={styles.documentAction}
        onPress={localMediaPath ? onMediaPress : handleDownload}
      >
        <Ionicons 
          name={localMediaPath ? "open" : "download"} 
          size={20} 
          color={colors.primary} 
        />
      </TouchableOpacity>

      {downloadState === 'downloading' && (
        <ActivityIndicator size="small" color={colors.primary} />
      )}
    </View>
  );

  const renderStickerContent = () => (
    <View style={styles.stickerContainer}>
      <Image
        source={{ uri: localMediaPath || mediaUrl }}
        style={styles.sticker}
        resizeMode="contain"
      />
    </View>
  );

  const renderMediaContent = () => {
    switch (type) {
      case 'image':
        return renderImageContent();
      case 'video':
        return renderVideoContent();
      case 'audio':
      case 'voice':
        return renderAudioContent();
      case 'document':
        return renderDocumentContent();
      case 'sticker':
        return renderStickerContent();
      default:
        return null;
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isOwn ? styles.ownMessage : styles.otherMessage,
      ]}
      onPress={onPress}
      onLongPress={onLongPress}
      activeOpacity={0.8}
    >
      {renderMediaContent()}
      
      {/* Caption */}
      {caption && (
        <View style={styles.captionContainer}>
          <Text style={[styles.captionText, { color: colors.text }]}>
            {caption}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const { width: screenWidth } = Dimensions.get('window');
const maxBubbleWidth = screenWidth * 0.7;

const styles = StyleSheet.create({
  container: {
    maxWidth: maxBubbleWidth,
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: 2,
  },
  ownMessage: {
    alignSelf: 'flex-end',
  },
  otherMessage: {
    alignSelf: 'flex-start',
  },
  
  // Image styles
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: maxBubbleWidth,
    height: 200,
    borderRadius: 12,
  },
  
  // Video styles
  videoContainer: {
    position: 'relative',
  },
  video: {
    width: maxBubbleWidth,
    height: 200,
    borderRadius: 12,
  },
  playOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  playButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  durationBadge: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  durationText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  
  // Audio styles
  audioContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
  },
  audioPlayButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  audioInfo: {
    flex: 1,
  },
  audioTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  audioMeta: {
    fontSize: 12,
    marginTop: 2,
  },
  
  // Document styles
  documentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 14,
    fontWeight: '500',
  },
  documentMeta: {
    fontSize: 12,
    marginTop: 2,
  },
  documentAction: {
    padding: 8,
  },
  
  // Sticker styles
  stickerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  sticker: {
    width: 120,
    height: 120,
  },
  
  // Download overlay
  downloadOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  downloadButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // Caption styles
  captionContainer: {
    padding: 8,
  },
  captionText: {
    fontSize: 14,
    lineHeight: 18,
  },
});

export default MediaMessageBubble;
