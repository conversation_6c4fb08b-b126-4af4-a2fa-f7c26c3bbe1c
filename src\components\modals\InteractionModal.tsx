import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TextInput,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
};

interface Comment {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  text: string;
  timestamp: Date;
}

interface Like {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  timestamp: Date;
}

interface InteractionModalProps {
  visible: boolean;
  updateId: string;
  type: 'likes' | 'views' | 'shares' | 'comments';
  onClose: () => void;
  currentUser?: any;
}

export const InteractionModal: React.FC<InteractionModalProps> = ({
  visible,
  updateId,
  type,
  onClose,
  currentUser,
}) => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [newComment, setNewComment] = useState('');

  useEffect(() => {
    if (visible && updateId) {
      loadInteractionData();
    }
  }, [visible, updateId, type]);

  const loadInteractionData = async () => {
    if (!updateId) return;

    setLoading(true);
    try {
      // TODO: Replace with real API calls
      if (type === 'comments') {
        // const result = await comprehensiveUpdatesService.getComments(updateId);
        // if (result.success && result.comments) {
        //   setData(result.comments);
        // }
        setData([]); // Empty for now until real implementation
      } else if (type === 'likes') {
        // const result = await comprehensiveUpdatesService.getLikes(updateId);
        // if (result.success && result.likes) {
        //   setData(result.likes);
        // }
        setData([]); // Empty for now until real implementation
      } else if (type === 'shares') {
        // TODO: Implement dedicated getShares method in service
        setData([]);
      }
    } catch (error) {
      console.error('Error loading interaction data:', error);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAddComment = async () => {
    if (!newComment.trim() || !currentUser) return;

    try {
      // TODO: Replace with real API call
      // const result = await comprehensiveUpdatesService.addComment(
      //   updateId,
      //   currentUser.id,
      //   currentUser.name || 'Unknown User',
      //   currentUser.avatar,
      //   newComment.trim()
      // );

      // if (result.success) {
      //   setNewComment('');
      //   await loadInteractionData(); // Reload comments
      // }
      
      // For now, just show success
      Alert.alert('Success', 'Comment added successfully!');
      setNewComment('');
    } catch (error) {
      console.error('Error adding comment:', error);
      Alert.alert('Error', 'Failed to add comment');
    }
  };

  const renderCommentItem = ({ item }: { item: Comment }) => (
    <View style={styles.commentItem}>
      {item.userAvatar ? (
        <Image source={{ uri: item.userAvatar }} style={styles.avatar} />
      ) : (
        <View style={styles.defaultAvatar}>
          <Ionicons name="person" size={16} color={COLORS.textMuted} />
        </View>
      )}
      <View style={styles.commentContent}>
        <Text style={styles.userName}>{item.userName}</Text>
        <Text style={styles.commentText}>{item.text}</Text>
      </View>
    </View>
  );

  const renderLikeItem = ({ item }: { item: Like }) => (
    <View style={styles.likeItem}>
      <View style={styles.defaultAvatar}>
        <Ionicons name="person" size={16} color={COLORS.textMuted} />
      </View>
      <View style={styles.likeContent}>
        <Text style={styles.userName}>User {item.userId}</Text>
        <Text style={styles.likeText}>Liked this update</Text>
      </View>
      <Ionicons name="heart" size={16} color="#EF4444" />
    </View>
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
          <View style={styles.placeholder} />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        ) : (
          <FlatList
            data={data}
            renderItem={type === 'comments' ? renderCommentItem : renderLikeItem}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.listContainer}
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  {type === 'comments' ? 'No comments yet' : 
                   type === 'likes' ? 'No likes yet' : 
                   type === 'shares' ? 'No shares yet' : 
                   `No ${type} yet`}
                </Text>
              </View>
            }
          />
        )}

        {type === 'comments' && currentUser && (
          <View style={styles.commentInputContainer}>
            <TextInput
              style={styles.commentInput}
              placeholder="Add a comment..."
              placeholderTextColor={COLORS.textMuted}
              value={newComment}
              onChangeText={setNewComment}
              multiline
            />
            <TouchableOpacity
              onPress={handleAddComment}
              disabled={!newComment.trim()}
              style={[
                styles.sendButton,
                { backgroundColor: newComment.trim() ? COLORS.primary : COLORS.textMuted }
              ]}
            >
              <Ionicons name="send" size={16} color="white" />
            </TouchableOpacity>
          </View>
        )}
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.textSecondary,
    fontSize: 16,
    marginTop: 16,
  },
  listContainer: {
    padding: 16,
  },
  commentItem: {
    flexDirection: 'row',
    marginBottom: 15,
    padding: 10,
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 8,
  },
  likeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
    padding: 10,
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 8,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 10,
  },
  defaultAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  commentContent: {
    flex: 1,
  },
  likeContent: {
    flex: 1,
  },
  userName: {
    color: COLORS.text,
    fontWeight: 'bold',
    fontSize: 14,
  },
  commentText: {
    color: COLORS.textSecondary,
    fontSize: 14,
    marginTop: 2,
  },
  likeText: {
    color: COLORS.textSecondary,
    fontSize: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    color: COLORS.textSecondary,
    fontSize: 16,
    textAlign: 'center',
  },
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 8,
    padding: 10,
    margin: 16,
  },
  commentInput: {
    flex: 1,
    color: COLORS.text,
    fontSize: 14,
    paddingHorizontal: 10,
  },
  sendButton: {
    borderRadius: 16,
    padding: 8,
    marginLeft: 10,
  },
});
