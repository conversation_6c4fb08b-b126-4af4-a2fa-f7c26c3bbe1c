import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface DateSeparatorProps {
  date: Date;
  isDarkMode?: boolean;
}

export const DateSeparator: React.FC<DateSeparatorProps> = ({ date, isDarkMode = false }) => {
  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const messageDate = new Date(date);
    messageDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);
    yesterday.setHours(0, 0, 0, 0);
    
    if (messageDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (messageDate.getTime() === yesterday.getTime()) {
      return 'Yesterday';
    } else {
      const daysDiff = Math.floor((today.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));
      
      if (daysDiff < 7) {
        // Show day of week for messages within the last week
        return messageDate.toLocaleDateString('en-US', { weekday: 'long' });
      } else {
        // Show full date for older messages
        return messageDate.toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric',
          year: messageDate.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
        });
      }
    }
  };

  const themeColors = {
    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
    textColor: isDarkMode ? '#FFFFFF' : '#666666',
  };

  return (
    <View style={styles.container}>
      <View style={[styles.separator, { backgroundColor: themeColors.backgroundColor }]}>
        <Text style={[styles.dateText, { color: themeColors.textColor }]}>
          {formatDate(date)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    marginVertical: 16,
  },
  separator: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  dateText: {
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
