/**
 * Selectable Message Bubble for IraChat
 * Enhanced message bubble with selection support and animations
 */

import React, { useRef, useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Platform,
  StyleSheet,
  Dimensions,
  PanResponder,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useMessageSelection, SelectedMessage } from '../../contexts/MessageSelectionContext';
import { useTheme } from '../../contexts/ThemeContext';

interface SelectableMessageBubbleProps {
  message: SelectedMessage;
  isOwn: boolean;
  onPress?: () => void;
  onLongPress?: () => void;
  onSwipeReply?: () => void;
  onSwipeDelete?: () => void;
  children: React.ReactNode;
  style?: any;
}

export const SelectableMessageBubble: React.FC<SelectableMessageBubbleProps> = ({
  message,
  isOwn,
  onPress,
  onLongPress,
  onSwipeReply,
  onSwipeDelete,
  children,
  style,
}) => {
  const { colors: _colors } = useTheme(); // Prefix with underscore to indicate intentionally unused
  const {
    isSelectionMode,
    isMessageSelected,
    enterSelectionMode,
    toggleMessageSelection,
  } = useMessageSelection();

  // Animation values
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const rippleAnim = useRef(new Animated.Value(0)).current;
  const selectionAnim = useRef(new Animated.Value(0)).current;
  const swipeAnim = useRef(new Animated.Value(0)).current;
  const replyIconOpacity = useRef(new Animated.Value(0)).current;
  const deleteIconOpacity = useRef(new Animated.Value(0)).current;
  const [ripplePosition, setRipplePosition] = useState({ x: 0, y: 0 });
  const [showReplyIcon, setShowReplyIcon] = useState(false);
  const [showDeleteIcon, setShowDeleteIcon] = useState(false);

  const isSelected = isMessageSelected(message.id);



  // Update selection animation when selection state changes
  useEffect(() => {
    Animated.timing(selectionAnim, {
      toValue: isSelected ? 1 : 0,
      duration: 200,
      useNativeDriver: false, // Keep consistent with other animations
    }).start();
  }, [isSelected]);

  // Create ripple effect
  const createRippleEffect = (x: number, y: number) => {
    setRipplePosition({ x, y });
    rippleAnim.setValue(0);

    Animated.timing(rippleAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: false, // Changed to false to match other animations
    }).start(() => {
      rippleAnim.setValue(0);
    });
  };

  // Create scale animation
  const createScaleAnimation = () => {
    scaleAnim.setValue(1);

    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.05,
        duration: 100,
        useNativeDriver: false, // Changed to false to match other animations
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: false, // Changed to false to match other animations
      }),
    ]).start();
  };

  // Handle long press
  const handleLongPress = (event: any) => {
    const { locationX, locationY } = event.nativeEvent;
    
    // Create animations
    createScaleAnimation();
    createRippleEffect(locationX, locationY);
    
    if (!isSelectionMode) {
      // Enter selection mode with this message
      enterSelectionMode(message);
    } else {
      // Toggle selection
      toggleMessageSelection(message);
    }
    
    onLongPress?.();
  };

  // Handle tap
  const handlePress = () => {
    if (isSelectionMode) {
      // In selection mode, toggle selection
      toggleMessageSelection(message);
    } else {
      // Normal tap behavior
      onPress?.();
    }
  };

  // Pan responder for swipe to reply and touch handling
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => false,
      onMoveShouldSetPanResponder: (_evt, gestureState) => {
        // Only respond to horizontal swipes when not in selection mode
        const isHorizontalSwipe = Math.abs(gestureState.dx) > Math.abs(gestureState.dy);
        const hasMinimumDistance = Math.abs(gestureState.dx) > 1; // ULTRA low threshold for instant response
        const shouldRespond = !isSelectionMode && isHorizontalSwipe && hasMinimumDistance;

        if (shouldRespond) {
          console.log('🔄 Swipe detected INSTANTLY:', gestureState.dx, 'selection mode:', isSelectionMode);
        }

        return shouldRespond;
      },
      onPanResponderGrant: (event) => {
        // Store touch position for ripple effect
        const { locationX, locationY } = event.nativeEvent;
        setRipplePosition({ x: locationX, y: locationY });
      },
      onPanResponderMove: (_evt, gestureState) => {
        const swipeValue = gestureState.dx;

        if (swipeValue > 0) {
          // Swipe right - reply
          const clampedValue = Math.min(swipeValue, 80);
          swipeAnim.setValue(clampedValue);

          // Show reply icon INSTANTLY
          if (!showReplyIcon) {
            setShowReplyIcon(true);
            replyIconOpacity.setValue(1); // Instant appearance
          }

          // Update reply icon opacity
          const opacity = Math.min(clampedValue / 50, 1);
          replyIconOpacity.setValue(opacity);

          // Hide delete icon if showing
          if (showDeleteIcon) {
            setShowDeleteIcon(false);
            deleteIconOpacity.setValue(0);
          }
        } else if (swipeValue < 0) {
          // Swipe left - delete (for all messages, but only own messages can actually be deleted)
          const clampedValue = Math.max(swipeValue, -80);
          swipeAnim.setValue(clampedValue);

          console.log('🗑️ Swipe left detected:', swipeValue, 'isOwn:', isOwn);

          // Show delete icon INSTANTLY (show for all, but only functional for own messages)
          if (!showDeleteIcon) {
            setShowDeleteIcon(true);
            deleteIconOpacity.setValue(1); // Instant appearance
          }

          // Update delete icon opacity
          const opacity = Math.min(Math.abs(clampedValue) / 30, 1); // Lower threshold
          deleteIconOpacity.setValue(opacity);

          // Hide reply icon if showing
          if (showReplyIcon) {
            setShowReplyIcon(false);
            replyIconOpacity.setValue(0);
          }
        }
      },
      onPanResponderRelease: (_evt, gestureState) => {
        const swipeThreshold = 15; // EXTREMELY low threshold for instant response

        if (gestureState.dx > swipeThreshold && onSwipeReply) {
          // Trigger reply
          console.log('🔄 Triggering reply for message:', message.id);
          onSwipeReply();
        } else if (gestureState.dx < -swipeThreshold && onSwipeDelete) {
          // Trigger delete (for any message, but only own messages will actually delete)
          console.log('🗑️ Triggering delete for message:', message.id, 'isOwn:', isOwn, 'threshold:', gestureState.dx);
          onSwipeDelete();
        }

        // Reset immediately - NO ANIMATION
        swipeAnim.setValue(0);
        replyIconOpacity.setValue(0);
        deleteIconOpacity.setValue(0);
        setShowReplyIcon(false);
        setShowDeleteIcon(false);
      },
    })
  ).current;

  // Selection background color - covers full width
  const selectionBackgroundColor = selectionAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['transparent', Platform.OS === 'ios' ? 'rgba(0, 122, 255, 0.15)' : 'rgba(33, 150, 243, 0.15)'],
  });

  // Selection border color
  const selectionBorderColor = selectionAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['transparent', Platform.OS === 'ios' ? 'rgba(0, 122, 255, 0.4)' : 'rgba(33, 150, 243, 0.4)'],
  });

  return (
    <View style={styles.swipeContainer}>
      {/* Reply Icon */}
      {showReplyIcon && (
        <Animated.View style={[
          styles.replyIcon,
          {
            opacity: replyIconOpacity,
            left: isOwn ? 20 : undefined,
            right: isOwn ? undefined : 20,
          }
        ]}>
          <Ionicons name="arrow-undo" size={20} color="#4A90E2" />
        </Animated.View>
      )}

      {/* Delete Icon */}
      {showDeleteIcon && (
        <Animated.View style={[
          styles.deleteIcon,
          {
            opacity: deleteIconOpacity,
            right: 20,
          }
        ]}>
          <Ionicons name="trash" size={20} color="#FF4444" />
        </Animated.View>
      )}

      <Animated.View
        style={[
          styles.fullWidthContainer,
          {
            backgroundColor: selectionBackgroundColor,
            borderColor: selectionBorderColor,
            borderWidth: isSelected ? 1 : 0,
            transform: [
              { scale: scaleAnim },
              { translateX: swipeAnim }
            ],
          },
        ]}
        {...panResponder.panHandlers}
      >
      <TouchableOpacity
        onPress={handlePress}
        onLongPress={handleLongPress}
        delayLongPress={500}
        activeOpacity={0.8}
        style={styles.touchable}
      >
        <View
          style={[
            styles.bubble,
            style,
            isOwn ? styles.ownBubble : styles.otherBubble,
          ]}
        >
          {/* Selection indicator */}
          {isSelected && (
            <View style={[
              styles.selectionIndicator,
              isOwn ? styles.ownSelectionIndicator : styles.otherSelectionIndicator,
            ]}>
              <Ionicons
                name="checkmark-circle"
                size={20}
                color={Platform.OS === 'ios' ? '#007AFF' : '#2196F3'}
              />
            </View>
          )}

          {/* Ripple effect */}
          {Platform.OS === 'android' && (
            <Animated.View
              style={[
                styles.ripple,
                {
                  left: ripplePosition.x - 50,
                  top: ripplePosition.y - 50,
                  transform: [
                    {
                      scale: rippleAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0, 2],
                      }),
                    },
                  ],
                  opacity: rippleAnim.interpolate({
                    inputRange: [0, 0.5, 1],
                    outputRange: [0.5, 0.3, 0],
                  }),
                },
              ]}
            />
          )}

          {/* Message content */}
          {children}
        </View>
      </TouchableOpacity>
    </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  swipeContainer: {
    position: 'relative',
    width: '100%',
  },
  replyIcon: {
    position: 'absolute',
    top: '50%',
    zIndex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -20,
  },
  deleteIcon: {
    position: 'absolute',
    top: '50%',
    zIndex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -20,
  },
  container: {
    marginVertical: 2,
  },
  fullWidthContainer: {
    width: '100%',
    marginVertical: 2,
    paddingHorizontal: 8,
  },
  touchable: {
    flex: 1,
  },
  bubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
    maxWidth: Dimensions.get('window').width * 0.8,
    position: 'relative',
    overflow: 'hidden',
  },
  ownBubble: {
    alignSelf: 'flex-end',
    marginLeft: 50,
  },
  otherBubble: {
    alignSelf: 'flex-start',
    marginRight: 50,
  },
  selectionIndicator: {
    position: 'absolute',
    top: -8,
    zIndex: 10,
  },
  ownSelectionIndicator: {
    right: -8,
  },
  otherSelectionIndicator: {
    left: -8,
  },
  ripple: {
    position: 'absolute',
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Platform.OS === 'ios' ? 'rgba(0, 122, 255, 0.3)' : 'rgba(33, 150, 243, 0.3)',
  },
});

export default SelectableMessageBubble;
