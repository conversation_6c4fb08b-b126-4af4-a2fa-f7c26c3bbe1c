import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  TextInput,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
};

interface CreateUpdateModalProps {
  visible: boolean;
  onClose: () => void;
  onCameraPress: () => void;
  onGalleryPress?: () => void;
  onTextUpdatePress?: () => void;
  currentUser?: any;
}

export const CreateUpdateModal: React.FC<CreateUpdateModalProps> = ({
  visible,
  onClose,
  onCameraPress,
  onGalleryPress,
  onTextUpdatePress,
  currentUser,
}) => {
  const [textContent, setTextContent] = useState('');

  const handleTextUpdate = () => {
    if (!textContent.trim()) {
      Alert.alert('Error', 'Please enter some text for your update');
      return;
    }

    // Handle text update creation
    Alert.alert('Success', 'Text update created successfully!');
    setTextContent('');
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Create Update</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content}>
          {/* Text Update Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Share your thoughts</Text>
            <TextInput
              style={styles.textInput}
              placeholder="What's on your mind?"
              placeholderTextColor={COLORS.textMuted}
              value={textContent}
              onChangeText={setTextContent}
              multiline
              numberOfLines={4}
              maxLength={500}
            />
            {textContent.length > 0 && (
              <TouchableOpacity style={styles.postButton} onPress={handleTextUpdate}>
                <Text style={styles.postButtonText}>Post Update</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Media Options */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Add Media</Text>
            
            {/* Camera Button */}
            <TouchableOpacity style={styles.optionButton} onPress={onCameraPress}>
              <Ionicons name="camera" size={24} color={COLORS.primary} />
              <Text style={styles.optionText}>Take Photo/Video</Text>
              <Ionicons name="chevron-forward" size={20} color={COLORS.textMuted} />
            </TouchableOpacity>

            {/* Gallery Button */}
            {onGalleryPress && (
              <TouchableOpacity style={styles.optionButton} onPress={onGalleryPress}>
                <Ionicons name="images" size={24} color={COLORS.primary} />
                <Text style={styles.optionText}>Choose from Gallery</Text>
                <Ionicons name="chevron-forward" size={20} color={COLORS.textMuted} />
              </TouchableOpacity>
            )}

            {/* Text Update Button */}
            {onTextUpdatePress && (
              <TouchableOpacity style={styles.optionButton} onPress={onTextUpdatePress}>
                <Ionicons name="text" size={24} color={COLORS.primary} />
                <Text style={styles.optionText}>Text Only Update</Text>
                <Ionicons name="chevron-forward" size={20} color={COLORS.textMuted} />
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
  },
  textInput: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 16,
    color: COLORS.text,
    fontSize: 16,
    minHeight: 100,
    textAlignVertical: 'top',
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  postButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignSelf: 'flex-end',
    marginTop: 12,
  },
  postButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    marginLeft: 16,
    fontWeight: '500',
  },
});
