@echo off
echo Installing Android SDK to D: drive with Administrator privileges...
echo.
echo This will:
echo - Download Android SDK Command Line Tools
echo - Download Android Platform Tools (ADB)
echo - Install to D:\Android\Sdk
echo - Set environment variables
echo.
pause

PowerShell -Command "Start-Process PowerShell -ArgumentList '-ExecutionPolicy Bypass -File \"%~dp0install-android-sdk-d-drive.ps1\"' -Verb RunAs"

echo.
echo Installation script launched with administrator privileges.
echo Please follow the instructions in the PowerShell window.
pause
