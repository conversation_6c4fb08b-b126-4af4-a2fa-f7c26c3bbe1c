import { Alert, Dimensions } from "react-native";

// Get device dimensions for responsive delete handling
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;

// Enhanced delete options interface
interface DeleteOptions {
  onDelete: () => Promise<void>;
  onCancel?: () => void;
  title?: string;
  message?: string;
  type?: "update" | "message" | "media" | "file" | "chat" | "contact" | "group" | "call";
  confirmText?: string;
  cancelText?: string;
  showWarning?: boolean;
  requireConfirmation?: boolean;
  itemName?: string;
  isOffline?: boolean;
}

// Delete result interface
export interface DeleteResult {
  success: boolean;
  error?: string;
  wasOffline?: boolean;
  pendingSync?: boolean;
}

// Batch delete options
interface BatchDeleteOptions {
  items: {
    id: string;
    type: DeleteOptions['type'];
    name?: string;
  }[];
  onDeleteItem: (id: string, type: string) => Promise<void>;
  onCancel?: () => void;
  title?: string;
  showProgress?: boolean;
}

/**
 * Enhanced delete handler with offline support and mobile optimization
 */
export const handleDelete = async ({
  onDelete,
  onCancel,
  title,
  message,
  type = "message",
  confirmText,
  cancelText,

  requireConfirmation = true,
  itemName,
  isOffline = false,
}: DeleteOptions): Promise<DeleteResult> => {

  // Get responsive text based on device size
  const getTitle = (): string => {
    if (title) return title;

    const baseTitle = isSmallDevice ? "Delete" : "Delete Confirmation";

    switch (type) {
      case "update": return isSmallDevice ? "Delete Update" : "Delete Update";
      case "message": return isSmallDevice ? "Delete Message" : "Delete Message";
      case "media": return isSmallDevice ? "Delete Media" : "Delete Media";
      case "file": return isSmallDevice ? "Delete File" : "Delete File";
      case "chat": return isSmallDevice ? "Delete Chat" : "Delete Chat";
      case "contact": return isSmallDevice ? "Delete Contact" : "Delete Contact";
      case "group": return isSmallDevice ? "Leave Group" : "Leave Group";
      case "call": return isSmallDevice ? "Delete Call" : "Delete Call Record";
      default: return baseTitle;
    }
  };

  const getMessage = (): string => {
    if (message) return message;

    const itemText = itemName ? ` "${itemName}"` : "";
    const offlineWarning = isOffline ? " (Will sync when online)" : "";

    switch (type) {
      case "update":
        return `Are you sure you want to delete this update${itemText}? This action cannot be undone.${offlineWarning}`;
      case "message":
        return `Are you sure you want to delete this message${itemText}? This action cannot be undone.${offlineWarning}`;
      case "media":
        return `Are you sure you want to delete this media${itemText}? This action cannot be undone.${offlineWarning}`;
      case "file":
        return `Are you sure you want to delete this file${itemText}? This action cannot be undone.${offlineWarning}`;
      case "chat":
        return `Are you sure you want to delete this chat${itemText}? All messages will be permanently deleted.${offlineWarning}`;
      case "contact":
        return `Are you sure you want to delete this contact${itemText}? This action cannot be undone.${offlineWarning}`;
      case "group":
        return `Are you sure you want to leave this group${itemText}? You won't receive any more messages.${offlineWarning}`;
      case "call":
        return `Are you sure you want to delete this call record${itemText}? This action cannot be undone.${offlineWarning}`;
      default:
        return `Are you sure you want to delete this${itemText}? This action cannot be undone.${offlineWarning}`;
    }
  };

  // If confirmation is not required, delete immediately
  if (!requireConfirmation) {
    try {
      await onDelete();
      return { success: true, wasOffline: isOffline };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Delete failed",
        wasOffline: isOffline
      };
    }
  }

  // Return a promise that resolves when the alert is handled
  return new Promise((resolve) => {
    const buttons = [
      {
        text: cancelText || "Cancel",
        style: "cancel" as const,
        onPress: () => {
          onCancel?.();
          resolve({ success: false, wasOffline: isOffline });
        },
      },
      {
        text: confirmText || "Delete",
        style: "destructive" as const,
        onPress: async () => {
          try {
            await onDelete();
            resolve({ success: true, wasOffline: isOffline, pendingSync: isOffline });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : "Failed to delete. Please try again.";

            // Show error alert
            Alert.alert(
              "Delete Failed",
              errorMessage,
              [{ text: "OK" }]
            );

            resolve({
              success: false,
              error: errorMessage,
              wasOffline: isOffline
            });
          }
        },
      },
    ];

    Alert.alert(
      getTitle(),
      getMessage(),
      buttons,
      { cancelable: true }
    );
  });
};

/**
 * Handle batch delete operations with progress tracking
 */
export const handleBatchDelete = async ({
  items,
  onDeleteItem,
  onCancel,
  title,
  showProgress = true,
}: BatchDeleteOptions): Promise<{
  success: boolean;
  completed: number;
  failed: number;
  errors: string[];
}> => {
  const itemCount = items.length;
  const itemText = isSmallDevice
    ? `${itemCount} items`
    : `${itemCount} ${itemCount === 1 ? 'item' : 'items'}`;

  const alertTitle = title || (isSmallDevice ? "Delete Items" : "Delete Multiple Items");
  const alertMessage = `Are you sure you want to delete ${itemText}? This action cannot be undone.`;

  return new Promise((resolve) => {
    Alert.alert(
      alertTitle,
      alertMessage,
      [
        {
          text: "Cancel",
          style: "cancel",
          onPress: () => {
            onCancel?.();
            resolve({ success: false, completed: 0, failed: 0, errors: [] });
          },
        },
        {
          text: "Delete All",
          style: "destructive",
          onPress: async () => {
            let completed = 0;
            let failed = 0;
            const errors: string[] = [];

            for (let i = 0; i < items.length; i++) {
              const item = items[i];

              try {
                await onDeleteItem(item.id, item.type || 'message');
                completed++;

                // Show progress for large batches
                if (showProgress && items.length > 5 && (i + 1) % 5 === 0) {
                  // Could show a progress indicator here
                }
              } catch (error) {
                failed++;
                const errorMsg = error instanceof Error ? error.message : `Failed to delete ${item.name || item.id}`;
                errors.push(errorMsg);
              }
            }

            // Show completion summary for large batches
            if (showProgress && items.length > 3) {
              const summary = failed > 0
                ? `Deleted ${completed} items. ${failed} failed.`
                : `Successfully deleted ${completed} items.`;

              Alert.alert(
                "Delete Complete",
                summary,
                [{ text: "OK" }]
              );
            }

            resolve({
              success: failed === 0,
              completed,
              failed,
              errors,
            });
          },
        },
      ],
      { cancelable: true }
    );
  });
};

/**
 * Quick delete without confirmation (for swipe actions)
 */
export const quickDelete = async (
  onDelete: () => Promise<void>,
  type: DeleteOptions['type'] = 'message',
  isOffline: boolean = false
): Promise<DeleteResult> => {
  try {
    await onDelete();
    return { success: true, wasOffline: isOffline, pendingSync: isOffline };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Delete failed",
      wasOffline: isOffline
    };
  }
};

/**
 * Delete with undo functionality
 */
export const deleteWithUndo = async ({
  onDelete,
  onUndo,
  type = 'message',
  itemName,
  undoTimeout = 5000,
}: {
  onDelete: () => Promise<void>;
  onUndo: () => Promise<void>;
  type?: DeleteOptions['type'];
  itemName?: string;
  undoTimeout?: number;
}): Promise<DeleteResult> => {
  try {
    await onDelete();

    // Show undo notification
    const undoText = isSmallDevice ? "Undo" : "Undo Delete";
    const itemText = itemName ? ` "${itemName}"` : "";
    const message = `Deleted${itemText}`;

    // This would typically integrate with a toast/snackbar component
    // For now, we'll use an alert with a timeout
    setTimeout(() => {
      Alert.alert(
        "Item Deleted",
        `${message}. Tap ${undoText} to restore.`,
        [
          { text: undoText, onPress: onUndo },
          { text: "OK" }
        ]
      );
    }, 100);

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Delete failed"
    };
  }
};

// Offline delete management
/**
 * Queue delete operation for offline sync
 */
export const queueOfflineDelete = async (
  itemId: string,
  itemType: string,
  additionalData?: any
): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync(`
      INSERT OR REPLACE INTO pending_deletes (
        id, itemId, itemType, timestamp, additionalData
      ) VALUES (?, ?, ?, ?, ?)
    `, [
      `delete_${itemId}_${Date.now()}`,
      itemId,
      itemType,
      Date.now(),
      JSON.stringify(additionalData || {})
    ]);
  } catch (error) {
    // Queue failed - continue without queuing
  }
};

/**
 * Process pending offline deletes
 */
export const processPendingDeletes = async (): Promise<{
  processed: number;
  failed: number;
  errors: string[];
}> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const pendingDeletes = await database.getAllAsync(`
      SELECT * FROM pending_deletes ORDER BY timestamp ASC
    `);

    let processed = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const deleteOp of pendingDeletes) {
      try {
        // Process the delete operation based on type
        await processDeleteByType(
          (deleteOp as any).itemId,
          (deleteOp as any).itemType,
          JSON.parse((deleteOp as any).additionalData || '{}')
        );

        // Remove from pending queue
        await database.runAsync('DELETE FROM pending_deletes WHERE id = ?', [(deleteOp as any).id]);
        processed++;
      } catch (error) {
        failed++;
        errors.push(error instanceof Error ? error.message : 'Unknown error');
      }
    }

    return { processed, failed, errors };
  } catch (error) {
    return { processed: 0, failed: 0, errors: ['Failed to process pending deletes'] };
  }
};

/**
 * Process delete operation by type
 */
const processDeleteByType = async (itemId: string, itemType: string, additionalData: any): Promise<void> => {
  // This would integrate with the appropriate service based on type
  const metadata = additionalData || {};

  switch (itemType) {
    case 'message':
      // Delete message from Firebase/backend using itemId and metadata
      await deleteMessageFromBackend(itemId, metadata);
      break;
    case 'chat':
      // Delete chat from Firebase/backend using itemId and metadata
      await deleteChatFromBackend(itemId, metadata);
      break;
    case 'update':
      // Delete update from Firebase/backend using itemId and metadata
      await deleteUpdateFromBackend(itemId, metadata);
      break;
    case 'media':
      // Delete media file and record using itemId and metadata
      await deleteMediaFromBackend(itemId, metadata);
      break;
    default:
      throw new Error(`Unknown delete type: ${itemType} for item ${itemId}`);
  }
};

// Placeholder functions for backend delete operations
const deleteMessageFromBackend = async (messageId: string, metadata: any): Promise<void> => {
  // Implementation would call the appropriate message service
  if (!messageId) throw new Error('Message ID required');

  // Use metadata for additional context
  const chatId = metadata?.chatId;
  const senderId = metadata?.senderId;

  // Validate required metadata
  if (chatId && typeof chatId !== 'string') {
    throw new Error('Invalid chat ID in metadata');
  }
  if (senderId && typeof senderId !== 'string') {
    throw new Error('Invalid sender ID in metadata');
  }
};

const deleteChatFromBackend = async (chatId: string, metadata: any): Promise<void> => {
  // Implementation would call the appropriate chat service
  if (!chatId) throw new Error('Chat ID required');

  // Use metadata for additional context
  const userId = metadata?.userId;
  const isGroup = metadata?.isGroup;

  // Validate metadata
  if (userId && typeof userId !== 'string') {
    throw new Error('Invalid user ID in metadata');
  }
  if (isGroup !== undefined && typeof isGroup !== 'boolean') {
    throw new Error('Invalid group flag in metadata');
  }
};

const deleteUpdateFromBackend = async (updateId: string, metadata: any): Promise<void> => {
  // Implementation would call the appropriate update service
  if (!updateId) throw new Error('Update ID required');

  // Use metadata for additional context
  const userId = metadata?.userId;
  const mediaUrls = metadata?.mediaUrls;

  // Validate metadata
  if (userId && typeof userId !== 'string') {
    throw new Error('Invalid user ID in metadata');
  }
  if (mediaUrls && !Array.isArray(mediaUrls)) {
    throw new Error('Invalid media URLs in metadata');
  }
};

const deleteMediaFromBackend = async (mediaId: string, metadata: any): Promise<void> => {
  // Implementation would call the appropriate media service
  if (!mediaId) throw new Error('Media ID required');

  // Use metadata for additional context
  const fileUrl = metadata?.fileUrl;
  const fileType = metadata?.fileType;

  // Validate metadata
  if (fileUrl && typeof fileUrl !== 'string') {
    throw new Error('Invalid file URL in metadata');
  }
  if (fileType && typeof fileType !== 'string') {
    throw new Error('Invalid file type in metadata');
  }
};

/**
 * Clear all pending deletes (for cleanup)
 */
export const clearPendingDeletes = async (): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync('DELETE FROM pending_deletes');
  } catch (error) {
    // Clear failed - continue
  }
};

/**
 * Get count of pending deletes
 */
export const getPendingDeleteCount = async (): Promise<number> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const result = await database.getAllAsync('SELECT COUNT(*) as count FROM pending_deletes');
    return (result[0] as any)?.count || 0;
  } catch (error) {
    return 0;
  }
};
