import { networkStateManager } from './networkStateManager';
import { offlineDatabaseService } from './offlineDatabase';
import { realPrivacyService, PrivacySettings } from './realPrivacyService';
import { messageLifecycleService } from './messageLifecycleService';

interface SyncOperation {
  id: string;
  type: 'privacy_update' | 'message_delete' | 'message_archive' | 'avatar_upload';
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

// Database row interfaces
interface PrivacySettingsRow {
  userId: string;
  lastSeen: string;
  lastSeenCustomContacts: string | null;
  profilePhoto: string;
  profilePhotoCustomContacts: string | null;
  status: string;
  statusCustomContacts: string | null;
  about: string;
  aboutCustomContacts: string | null;
  readReceipts: number;
  groupsAddMe: string;
  groupsAddMeCustomContacts: string | null;
  liveLocation: number;
  callsFrom: string;
  blockedContacts: string | null;
  twoStepVerification: number;
  disappearingMessages: number;
  disappearingMessagesDuration: string;
  disappearingMessagesScope: string;
  disappearingMessagesCustomContacts: string | null;
  disappearingMessagesStorage: string;
  screenshotNotification: number;
  screenshotControl: number;
  screenshotControlScope: string;
  screenshotControlCustomContacts: string | null;
  onlineStatus: string;
  forwardedMessages: number;
  forwardedMessagesScope: string;
  forwardedMessagesCustomContacts: string | null;
  autoDownloadMedia: string;
  securityNotifications: number;
  createdAt: number;
  updatedAt: number;
  syncStatus: string;
  lastSyncAttempt: number | null;
}

interface PendingDeletionRow {
  messageId: string;
  type: string;
  timestamp: number;
}

interface PendingArchiveRow {
  messageId: string;
  timestamp: number;
}

class RealSyncService {
  private isInitialized = false;
  private syncInProgress = false;
  private syncQueue: SyncOperation[] = [];
  private syncInterval: NodeJS.Timeout | null = null;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();
      await messageLifecycleService.initialize();
      
      // Listen for network state changes
      networkStateManager.addListener('realSync', this.handleNetworkStateChange.bind(this), 1);
      
      // Load pending sync operations
      await this.loadPendingSyncOperations();
      
      // Start periodic sync check
      this.startPeriodicSync();
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize RealSyncService:', error);
      throw error;
    }
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected && !this.syncInProgress) {
      // User came online - start syncing immediately
      this.performSync();
    }
  }

  /**
   * Get current sync status for the entire app
   */
  getSyncStatus(): { inProgress: boolean; pending: number; lastSync?: Date } {
    return {
      inProgress: this.syncInProgress,
      pending: this.syncQueue.length,
      lastSync: this.getLastSyncTime(),
    };
  }

  /**
   * Get the last successful sync time
   */
  private getLastSyncTime(): Date | undefined {
    try {
      const lastSyncStr = localStorage.getItem('lastSyncTime');
      return lastSyncStr ? new Date(lastSyncStr) : undefined;
    } catch {
      return undefined;
    }
  }

  /**
   * Update the last sync time
   */
  private updateLastSyncTime(): void {
    try {
      localStorage.setItem('lastSyncTime', new Date().toISOString());
    } catch (error) {
      console.error('Failed to update last sync time:', error);
    }
  }

  private startPeriodicSync(): void {
    // Check for sync every 30 seconds when online
    this.syncInterval = setInterval(() => {
      if (networkStateManager.isOnline() && !this.syncInProgress) {
        this.performSync();
      }
    }, 30000);
  }

  private async loadPendingSyncOperations(): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      
      // Load privacy settings that need sync
      const pendingPrivacy = await db.getAllAsync<PrivacySettingsRow>(`
        SELECT * FROM privacy_settings WHERE syncStatus = 'pending'
      `);

      for (const privacy of pendingPrivacy) {
        this.syncQueue.push({
          id: `privacy_${privacy.userId}`,
          type: 'privacy_update',
          data: privacy,
          timestamp: privacy.updatedAt,
          retryCount: 0,
          maxRetries: 5
        });
      }

      // Load pending message deletions
      const pendingDeletions = await db.getAllAsync<PendingDeletionRow>(`
        SELECT * FROM pending_deletions
      `);

      for (const deletion of pendingDeletions) {
        this.syncQueue.push({
          id: `delete_${deletion.messageId}`,
          type: 'message_delete',
          data: deletion,
          timestamp: deletion.timestamp,
          retryCount: 0,
          maxRetries: 3
        });
      }

      // Load pending message archives
      const pendingArchives = await db.getAllAsync<PendingArchiveRow>(`
        SELECT * FROM pending_archives
      `);

      for (const archive of pendingArchives) {
        this.syncQueue.push({
          id: `archive_${archive.messageId}`,
          type: 'message_archive',
          data: archive,
          timestamp: archive.timestamp,
          retryCount: 0,
          maxRetries: 3
        });
      }

    } catch (error) {
      console.error('Error loading pending sync operations:', error);
    }
  }

  async performSync(): Promise<void> {
    if (this.syncInProgress || !networkStateManager.isOnline()) {
      return;
    }

    this.syncInProgress = true;

    // Process sync queue
    const operations = [...this.syncQueue];
    this.syncQueue = [];

    try {

      for (const operation of operations) {
        try {
          await this.processSyncOperation(operation);
        } catch (error) {
          console.error(`Error processing sync operation ${operation.id}:`, error);
          
          // Retry logic
          operation.retryCount++;
          if (operation.retryCount < operation.maxRetries) {
            // Add back to queue for retry
            this.syncQueue.push(operation);
          } else {
            // Max retries reached - log error and abandon
            console.error(`Max retries reached for sync operation ${operation.id}`);
            await this.markSyncOperationFailed(operation);
          }
        }
      }

    } finally {
      this.syncInProgress = false;
      // Update last sync time if we processed any operations
      if (operations.length > 0) {
        this.updateLastSyncTime();
      }
    }
  }

  private async processSyncOperation(operation: SyncOperation): Promise<void> {
    switch (operation.type) {
      case 'privacy_update':
        await this.syncPrivacyUpdate(operation.data);
        break;
      case 'message_delete':
        await this.syncMessageDeletion(operation.data);
        break;
      case 'message_archive':
        await this.syncMessageArchive(operation.data);
        break;
      case 'avatar_upload':
        await this.syncAvatarUpload(operation.data);
        break;
    }
  }

  private async syncPrivacyUpdate(privacyData: PrivacySettingsRow): Promise<void> {
    try {
      // Convert database row back to privacy settings object
      const settings: PrivacySettings = {
        userId: privacyData.userId,
        lastSeen: privacyData.lastSeen as "everyone" | "contacts" | "nobody" | "custom",
        lastSeenCustomContacts: privacyData.lastSeenCustomContacts ? JSON.parse(privacyData.lastSeenCustomContacts) : [],
        profilePhoto: privacyData.profilePhoto as "everyone" | "contacts" | "nobody" | "custom",
        profilePhotoCustomContacts: privacyData.profilePhotoCustomContacts ? JSON.parse(privacyData.profilePhotoCustomContacts) : [],
        status: privacyData.status as "everyone" | "contacts" | "nobody" | "custom",
        statusCustomContacts: privacyData.statusCustomContacts ? JSON.parse(privacyData.statusCustomContacts) : [],
        about: privacyData.about as "everyone" | "contacts" | "nobody" | "custom",
        aboutCustomContacts: privacyData.aboutCustomContacts ? JSON.parse(privacyData.aboutCustomContacts) : [],
        readReceipts: Boolean(privacyData.readReceipts),
        groupsAddMe: privacyData.groupsAddMe as "everyone" | "contacts" | "nobody" | "custom",
        groupsAddMeCustomContacts: privacyData.groupsAddMeCustomContacts ? JSON.parse(privacyData.groupsAddMeCustomContacts) : [],
        liveLocation: Boolean(privacyData.liveLocation),
        callsFrom: privacyData.callsFrom as "everyone" | "contacts" | "nobody",
        blockedContacts: privacyData.blockedContacts ? JSON.parse(privacyData.blockedContacts) : [],
        twoStepVerification: Boolean(privacyData.twoStepVerification),
        disappearingMessages: Boolean(privacyData.disappearingMessages),
        disappearingMessagesDuration: privacyData.disappearingMessagesDuration as "1hour" | "1day" | "1week" | "1month" | "never",
        disappearingMessagesScope: privacyData.disappearingMessagesScope as "everyone" | "contacts" | "nobody" | "custom",
        disappearingMessagesCustomContacts: privacyData.disappearingMessagesCustomContacts ? JSON.parse(privacyData.disappearingMessagesCustomContacts) : [],
        disappearingMessagesStorage: privacyData.disappearingMessagesStorage as "delete_everywhere" | "delete_chat_only" | "archive_locally",
        screenshotNotification: Boolean(privacyData.screenshotNotification),
        screenshotControl: Boolean(privacyData.screenshotControl),
        screenshotControlScope: privacyData.screenshotControlScope as "everyone" | "contacts" | "nobody" | "custom",
        screenshotControlCustomContacts: privacyData.screenshotControlCustomContacts ? JSON.parse(privacyData.screenshotControlCustomContacts) : [],
        onlineStatus: privacyData.onlineStatus as "everyone" | "contacts" | "nobody",
        forwardedMessages: Boolean(privacyData.forwardedMessages),
        forwardedMessagesScope: privacyData.forwardedMessagesScope as "everyone" | "contacts" | "nobody" | "custom",
        forwardedMessagesCustomContacts: privacyData.forwardedMessagesCustomContacts ? JSON.parse(privacyData.forwardedMessagesCustomContacts) : [],
        autoDownloadMedia: (privacyData.autoDownloadMedia || "wifi") as "never" | "wifi" | "always",
        securityNotifications: Boolean(privacyData.securityNotifications),
        createdAt: new Date(privacyData.createdAt),
        updatedAt: new Date(privacyData.updatedAt)
      };

      // Sync to Firebase
      const result = await realPrivacyService.syncPrivacySettingsToFirebase(settings);
      
      if (result.success) {
        // Mark as synced in local database
        const db = offlineDatabaseService.getDatabase();
        await db.runAsync(`
          UPDATE privacy_settings 
          SET syncStatus = 'synced', lastSyncAttempt = ? 
          WHERE userId = ?
        `, [Date.now(), privacyData.userId]);
      } else {
        throw new Error(result.error || 'Failed to sync privacy settings');
      }

    } catch (error) {
      console.error('Error syncing privacy update:', error);
      throw error;
    }
  }

  private async syncMessageDeletion(deletionData: PendingDeletionRow): Promise<void> {
    try {
      // Perform actual Firebase deletion
      await messageLifecycleService.deleteMessageFromFirebase(deletionData.messageId, deletionData.type);

      // Remove from pending deletions
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync('DELETE FROM pending_deletions WHERE messageId = ?', [deletionData.messageId]);

    } catch (error) {
      console.error('Error syncing message deletion:', error);
      throw error;
    }
  }

  private async syncMessageArchive(archiveData: PendingArchiveRow): Promise<void> {
    try {
      // Perform actual Firebase archiving
      await messageLifecycleService.archiveMessageInFirebase(archiveData.messageId);

      // Remove from pending archives
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync('DELETE FROM pending_archives WHERE messageId = ?', [archiveData.messageId]);

    } catch (error) {
      console.error('Error syncing message archive:', error);
      throw error;
    }
  }

  private async markSyncOperationFailed(operation: SyncOperation): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      
      switch (operation.type) {
        case 'privacy_update':
          await db.runAsync(`
            UPDATE privacy_settings 
            SET syncStatus = 'failed', lastSyncAttempt = ? 
            WHERE userId = ?
          `, [Date.now(), operation.data.userId]);
          break;
        case 'message_delete':
          // Keep in pending_deletions but mark as failed
          await db.runAsync(`
            UPDATE pending_deletions 
            SET type = 'failed' 
            WHERE messageId = ?
          `, [operation.data.messageId]);
          break;
        case 'message_archive':
          // Keep in pending_archives but mark as failed
          await db.runAsync(`
            UPDATE pending_archives 
            SET messageId = ? 
            WHERE messageId = ?
          `, [`failed_${operation.data.messageId}`, operation.data.messageId]);
          break;
      }
    } catch (error) {
      console.error('Error marking sync operation as failed:', error);
    }
  }

  async queuePrivacyUpdate(userId: string, updates: any): Promise<void> {
    // Add to sync queue for when user comes online
    this.syncQueue.push({
      id: `privacy_${userId}_${Date.now()}`,
      type: 'privacy_update',
      data: { userId, ...updates },
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 5
    });

    // Try to sync immediately if online
    if (networkStateManager.isOnline()) {
      this.performSync();
    }
  }

  async queueMessageDeletion(messageId: string, type: string): Promise<void> {
    this.syncQueue.push({
      id: `delete_${messageId}`,
      type: 'message_delete',
      data: { messageId, type },
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3
    });

    if (networkStateManager.isOnline()) {
      this.performSync();
    }
  }

  async queueMessageArchive(messageId: string): Promise<void> {
    this.syncQueue.push({
      id: `archive_${messageId}`,
      type: 'message_archive',
      data: { messageId },
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3
    });

    if (networkStateManager.isOnline()) {
      this.performSync();
    }
  }

  async queueAvatarUpload(userId: string, imageUri: string): Promise<void> {
    this.syncQueue.push({
      id: `avatar_${userId}_${Date.now()}`,
      type: 'avatar_upload',
      data: { userId, imageUri },
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3
    });

    if (networkStateManager.isOnline()) {
      this.performSync();
    }
  }

  private async syncAvatarUpload(data: { userId: string; imageUri: string }): Promise<void> {
    try {
      const { avatarService } = await import('./avatarService');
      const avatarUrl = await avatarService.uploadAvatar(data.userId, data.imageUri);

      console.log('✅ Avatar synced successfully via sync service:', avatarUrl);

      // Remove from offline storage
      const AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
      const offlineKey = `offline_avatar_${data.userId}`;
      await AsyncStorage.removeItem(offlineKey);

    } catch (error) {
      console.error('❌ Failed to sync avatar upload:', error);
      throw error; // Re-throw to trigger retry mechanism
    }
  }



  cleanup(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    networkStateManager.removeListener('realSync');
    this.isInitialized = false;
  }
}

export const realSyncService = new RealSyncService();
