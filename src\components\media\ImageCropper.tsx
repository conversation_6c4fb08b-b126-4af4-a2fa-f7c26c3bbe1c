import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import {
  Gesture,
  GestureDetector,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  runOnJS,
  withSpring,
} from 'react-native-reanimated';
import * as ImageManipulator from 'expo-image-manipulator';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface ImageCropperProps {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
  onCropComplete: (croppedUri: string) => void;
  aspectRatio?: number; // width/height ratio, e.g., 1 for square, 16/9 for widescreen
}

export const ImageCropper: React.FC<ImageCropperProps> = ({
  visible,
  imageUri,
  onClose,
  onCropComplete,
  aspectRatio,
}) => {
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [isProcessing, setIsProcessing] = useState(false);
  const [cropArea, setCropArea] = useState({
    x: 0,
    y: 0,
    width: SCREEN_WIDTH - 40,
    height: SCREEN_WIDTH - 40,
  });

  // Animation values
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const lastScale = useSharedValue(1);

  const panGesture = Gesture.Pan()
    .onStart(() => {
      // Store initial values
    })
    .onUpdate((event) => {
      translateX.value = event.translationX;
      translateY.value = event.translationY;
    })
    .onEnd(() => {
      // Snap back if needed
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
    });

  const pinchGesture = Gesture.Pinch()
    .onStart(() => {
      lastScale.value = scale.value;
    })
    .onUpdate((event) => {
      scale.value = lastScale.value * event.scale;
    })
    .onEnd(() => {
      // Constrain scale
      if (scale.value < 0.5) {
        scale.value = withSpring(0.5);
      } else if (scale.value > 3) {
        scale.value = withSpring(3);
      }
      lastScale.value = scale.value;
    });

  const composedGesture = Gesture.Simultaneous(panGesture, pinchGesture);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
    ],
  }));

  const handleImageLoad = (event: any) => {
    const { width, height } = event.nativeEvent.source;
    setImageSize({ width, height });
    
    // Calculate initial crop area based on aspect ratio
    if (aspectRatio) {
      const maxWidth = SCREEN_WIDTH - 40;
      const maxHeight = SCREEN_HEIGHT * 0.6;
      
      let cropWidth = maxWidth;
      let cropHeight = maxWidth / aspectRatio;
      
      if (cropHeight > maxHeight) {
        cropHeight = maxHeight;
        cropWidth = maxHeight * aspectRatio;
      }
      
      setCropArea({
        x: (SCREEN_WIDTH - cropWidth) / 2,
        y: (SCREEN_HEIGHT * 0.3 - cropHeight) / 2,
        width: cropWidth,
        height: cropHeight,
      });
    }
  };

  const handleCrop = async () => {
    if (!imageSize.width || !imageSize.height) {
      Alert.alert('Error', 'Image not loaded properly');
      return;
    }

    setIsProcessing(true);

    try {
      // Calculate crop parameters based on image size and current view
      const imageAspectRatio = imageSize.width / imageSize.height;
      const displayWidth = SCREEN_WIDTH - 40;
      const displayHeight = displayWidth / imageAspectRatio;

      // Calculate scale factors
      const scaleX = imageSize.width / displayWidth;
      const scaleY = imageSize.height / displayHeight;

      // Calculate crop area in image coordinates
      const cropX = Math.max(0, (cropArea.x - 20) * scaleX);
      const cropY = Math.max(0, (cropArea.y - (SCREEN_HEIGHT * 0.2)) * scaleY);
      const cropWidth = Math.min(imageSize.width - cropX, cropArea.width * scaleX);
      const cropHeight = Math.min(imageSize.height - cropY, cropArea.height * scaleY);

      const manipulateResult = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          {
            crop: {
              originX: cropX,
              originY: cropY,
              width: cropWidth,
              height: cropHeight,
            },
          },
        ],
        {
          compress: 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      onCropComplete(manipulateResult.uri);
      onClose();
    } catch (error) {
      console.error('Error cropping image:', error);
      Alert.alert('Error', 'Failed to crop image. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const resetCrop = () => {
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    scale.value = withSpring(1);
    lastScale.value = 1;
  };

  if (!visible) return null;

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.headerButton} onPress={onClose}>
          <Ionicons name="close" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.title}>Crop Image</Text>
        <TouchableOpacity
          style={[styles.headerButton, isProcessing && styles.headerButtonDisabled]}
          onPress={handleCrop}
          disabled={isProcessing}
        >
          <Text style={[styles.headerButtonText, isProcessing && styles.headerButtonTextDisabled]}>
            {isProcessing ? 'Processing...' : 'Done'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Image Container */}
      <View style={styles.imageContainer}>
        {/* Overlay */}
        <View style={styles.overlay} />
        
        {/* Crop Area */}
        <View
          style={[
            styles.cropArea,
            {
              left: cropArea.x,
              top: cropArea.y,
              width: cropArea.width,
              height: cropArea.height,
            },
          ]}
        />

        {/* Image */}
        <GestureDetector gesture={composedGesture}>
          <Animated.View style={animatedStyle}>
            <Image
              source={{ uri: imageUri }}
              style={styles.image}
              onLoad={handleImageLoad}
              resizeMode="contain"
            />
          </Animated.View>
        </GestureDetector>

        {/* Crop Grid */}
        <View
          style={[
            styles.cropGrid,
            {
              left: cropArea.x,
              top: cropArea.y,
              width: cropArea.width,
              height: cropArea.height,
            },
          ]}
        >
          {/* Grid lines */}
          <View style={[styles.gridLine, styles.verticalLine, { left: '33%' }]} />
          <View style={[styles.gridLine, styles.verticalLine, { left: '66%' }]} />
          <View style={[styles.gridLine, styles.horizontalLine, { top: '33%' }]} />
          <View style={[styles.gridLine, styles.horizontalLine, { top: '66%' }]} />
        </View>
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <TouchableOpacity style={styles.controlButton} onPress={resetCrop}>
          <Ionicons name="refresh" size={24} color="#FFFFFF" />
          <Text style={styles.controlText}>Reset</Text>
        </TouchableOpacity>

        {aspectRatio && (
          <View style={styles.aspectRatioInfo}>
            <Text style={styles.aspectRatioText}>
              Aspect Ratio: {aspectRatio === 1 ? '1:1' : `${Math.round(aspectRatio * 100) / 100}:1`}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  headerButton: {
    padding: 8,
  },
  headerButtonDisabled: {
    opacity: 0.5,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  headerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#667eea',
  },
  headerButtonTextDisabled: {
    color: '#666',
  },
  imageContainer: {
    flex: 1,
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  cropArea: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: '#FFFFFF',
    backgroundColor: 'transparent',
    zIndex: 2,
  },
  image: {
    width: SCREEN_WIDTH - 40,
    height: SCREEN_HEIGHT * 0.6,
  },
  cropGrid: {
    position: 'absolute',
    zIndex: 3,
  },
  gridLine: {
    position: 'absolute',
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  verticalLine: {
    width: 1,
    height: '100%',
  },
  horizontalLine: {
    height: 1,
    width: '100%',
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 20,
    backgroundColor: '#1a1a1a',
  },
  controlButton: {
    alignItems: 'center',
    gap: 4,
  },
  controlText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  aspectRatioInfo: {
    alignItems: 'center',
  },
  aspectRatioText: {
    color: '#999',
    fontSize: 14,
  },
});
