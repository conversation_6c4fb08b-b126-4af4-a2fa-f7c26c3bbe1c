import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import { AudioCaption } from '../../types/Update';
import { audioCaptionService } from '../../services/audioCaptionService';

interface AudioCaptionPlayerProps {
  audioCaption: AudioCaption;
  isInViewport: boolean;
  autoPlay?: boolean;
  onPlaybackStatusUpdate?: (isPlaying: boolean, position: number) => void;
  style?: any;
}

export const AudioCaptionPlayer: React.FC<AudioCaptionPlayerProps> = ({
  audioCaption,
  isInViewport,
  autoPlay = true,
  onPlaybackStatusUpdate,
  style,
}) => {
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [position, setPosition] = useState(0);
  const [duration, setDuration] = useState(audioCaption.duration * 1000); // Convert to ms

  const waveformAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const isMountedRef = useRef(true);

  // Register with audio caption service when component mounts
  useEffect(() => {
    const audioId = `audio_${audioCaption.id}`;

    const registerAudio = async () => {
      const registeredSound = await audioCaptionService.registerAudio(
        audioId,
        audioCaption,
        (isPlaying, position) => {
          if (isMountedRef.current) {
            setIsPlaying(isPlaying);
            setPosition(position);
            if (onPlaybackStatusUpdate) {
              onPlaybackStatusUpdate(isPlaying, position);
            }
          }
        }
      );

      if (registeredSound && isMountedRef.current) {
        setSound(registeredSound);
        setIsLoading(false);
      }
    };

    registerAudio();

    return () => {
      isMountedRef.current = false;
      audioCaptionService.unregisterAudio(audioId);
    };
  }, [audioCaption.id, audioCaption.url]);

  // Handle viewport changes and auto-play
  useEffect(() => {
    if (!sound) return;

    if (isInViewport && autoPlay) {
      playSound();
    } else {
      pauseSound();
    }
  }, [isInViewport, sound, autoPlay]);

  // Animate waveform when playing
  useEffect(() => {
    if (isPlaying) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(waveformAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(waveformAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      waveformAnim.stopAnimation();
      waveformAnim.setValue(0);
    }
  }, [isPlaying]);



  const playSound = async () => {
    const audioId = `audio_${audioCaption.id}`;
    await audioCaptionService.playAudio(audioId);
  };

  const pauseSound = async () => {
    const audioId = `audio_${audioCaption.id}`;
    await audioCaptionService.pauseAudio(audioId);
  };

  const togglePlayback = () => {
    if (isPlaying) {
      pauseSound();
    } else {
      playSound();
    }
  };

  const formatDuration = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <View style={[styles.container, style]}>
      {/* Audio Type Icon */}
      <View style={styles.iconContainer}>
        <Ionicons
          name={audioCaption.type === 'voice' ? 'mic' : 'musical-notes'}
          size={16}
          color="#667eea"
        />
      </View>

      {/* Waveform Visualization */}
      <View style={styles.waveformContainer}>
        {[...Array(12)].map((_, index) => (
          <Animated.View
            key={index}
            style={[
              styles.waveformBar,
              {
                transform: [{
                  scaleY: isPlaying
                    ? waveformAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.3, Math.random() * 1.5 + 0.5],
                      })
                    : 0.3
                }]
              }
            ]}
          />
        ))}
      </View>

      {/* Play/Pause Button */}
      <TouchableOpacity
        style={styles.playButton}
        onPress={togglePlayback}
        disabled={isLoading}
      >
        {isLoading ? (
          <View style={styles.loadingDot} />
        ) : (
          <Ionicons
            name={isPlaying ? 'pause' : 'play'}
            size={14}
            color="#FFFFFF"
          />
        )}
      </TouchableOpacity>

      {/* Duration */}
      <Text style={styles.duration}>
        {formatDuration(position)} / {formatDuration(duration)}
      </Text>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressTrack} />
        <Animated.View
          style={[
            styles.progressFill,
            {
              width: progressAnim.interpolate({
                inputRange: [0, 1],
                outputRange: ['0%', '100%'],
              })
            }
          ]}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginVertical: 4,
  },
  iconContainer: {
    marginRight: 8,
  },
  waveformContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 20,
    marginRight: 8,
  },
  waveformBar: {
    width: 2,
    height: 12,
    backgroundColor: '#1DA1F2',
    marginHorizontal: 0.5,
    borderRadius: 1,
  },
  playButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#1DA1F2',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  loadingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FFFFFF',
  },
  duration: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '500',
    marginRight: 8,
    minWidth: 60,
  },
  progressContainer: {
    flex: 1,
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 1.5,
    overflow: 'hidden',
  },
  progressTrack: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
  },
});
