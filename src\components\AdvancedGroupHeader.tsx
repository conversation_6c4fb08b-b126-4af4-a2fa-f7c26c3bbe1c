import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  Animated,
  Image,
  Text,
  View,
  AccessibilityInfo,
  Pressable,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import * as Haptics from "expo-haptics";
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS } from "../styles/iraChatDesignSystem";
import { ResponsiveSpacing, ResponsiveTypography, DeviceInfo } from "../utils/responsiveUtils";
import ErrorHandling, { ErrorCategory, ErrorSeverity } from "../services/errorHandling";

interface GroupHeaderProps {
  groupName: string;
  groupAvatar: string;
  memberCount: number;
  admins: { id: string; name: string; username: string }[];
  mostActiveUser?: {
    id: string;
    name: string;
    avatar: string;
    isVisible: boolean; // 24-hour visibility cycle
  };
  onGroupProfilePress: () => void;
  onBack: () => void;
  isScrolled: boolean; // For scroll-aware hiding
  isLoading?: boolean;
  onAdminPress?: (admin: { id: string; name: string; username: string }) => void;
  onMenuAction?: (action: 'settings' | 'profile' | 'mute' | 'leave') => void;
}

export const AdvancedGroupHeader: React.FC<GroupHeaderProps> = ({
  groupName,
  groupAvatar,
  memberCount,
  admins,
  mostActiveUser,
  onGroupProfilePress,
  onBack,
  isScrolled,
  isLoading = false,
  onAdminPress,
  onMenuAction,
}) => {
  const insets = useSafeAreaInsets();
  const [showAdminList, setShowAdminList] = useState(false);
  const [showDropdownMenu, setShowDropdownMenu] = useState(false);
  const [showMostActiveText, setShowMostActiveText] = useState(false);
  const [mostActiveTextTaps, setMostActiveTextTaps] = useState(0);
  const [imageLoadError, setImageLoadError] = useState(false);
  const [mostActiveImageError, setMostActiveImageError] = useState(false);
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] = useState(false);

  // Animations
  const headerOpacity = useRef(new Animated.Value(1)).current;
  const adminListAnimation = useRef(new Animated.Value(0)).current;
  const dropdownAnimation = useRef(new Animated.Value(0)).current;
  const mostActiveTextAnimation = useRef(new Animated.Value(0)).current;

  // Check accessibility settings
  useEffect(() => {
    const checkAccessibility = async () => {
      try {
        const screenReaderEnabled = await AccessibilityInfo.isScreenReaderEnabled();
        setIsScreenReaderEnabled(screenReaderEnabled);
      } catch (error) {
        await ErrorHandling.handleError(error, ErrorCategory._SYSTEM, ErrorSeverity._MEDIUM, {
          component: 'AdvancedGroupHeader',
          action: 'checkAccessibility'
        });
      }
    };
    checkAccessibility();
  }, []);

  // Scroll-aware header hiding with improved animation
  useEffect(() => {
    Animated.timing(headerOpacity, {
      toValue: isScrolled ? 0.8 : 1, // Keep slightly visible when scrolled
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [isScrolled, headerOpacity]);

  // Auto-hide overlays when scrolling
  useEffect(() => {
    if (isScrolled && (showAdminList || showDropdownMenu || showMostActiveText)) {
      setShowAdminList(false);
      setShowDropdownMenu(false);
      setShowMostActiveText(false);
      setMostActiveTextTaps(0);
    }
  }, [isScrolled, showAdminList, showDropdownMenu, showMostActiveText]);

  // Handle most active member avatar tap with haptic feedback
  const handleMostActiveTap = useCallback(async () => {
    try {
      // Provide haptic feedback
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      const newTapCount = mostActiveTextTaps + 1;
      setMostActiveTextTaps(newTapCount);

      if (newTapCount % 2 === 1) {
        // Odd taps: Show text
        setShowMostActiveText(true);
        Animated.timing(mostActiveTextAnimation, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      } else {
        // Even taps: Hide text
        Animated.timing(mostActiveTextAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start(() => {
          setShowMostActiveText(false);
        });
      }
    } catch (error) {
      await ErrorHandling.handleError(error, ErrorCategory._SYSTEM, ErrorSeverity._LOW, {
        component: 'AdvancedGroupHeader',
        action: 'handleMostActiveTap'
      });
    }
  }, [mostActiveTextTaps, mostActiveTextAnimation]);

  // Toggle admin list with haptic feedback
  const toggleAdminList = useCallback(async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Close other overlays first
      if (showDropdownMenu) {
        setShowDropdownMenu(false);
        Animated.timing(dropdownAnimation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
      }

      const newState = !showAdminList;
      setShowAdminList(newState);

      Animated.timing(adminListAnimation, {
        toValue: newState ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      await ErrorHandling.handleError(error, ErrorCategory._SYSTEM, ErrorSeverity._LOW, {
        component: 'AdvancedGroupHeader',
        action: 'toggleAdminList'
      });
    }
  }, [showAdminList, showDropdownMenu, adminListAnimation, dropdownAnimation]);

  // Toggle dropdown menu with haptic feedback
  const toggleDropdownMenu = useCallback(async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Close other overlays first
      if (showAdminList) {
        setShowAdminList(false);
        Animated.timing(adminListAnimation, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start();
      }

      const newState = !showDropdownMenu;
      setShowDropdownMenu(newState);

      Animated.timing(dropdownAnimation, {
        toValue: newState ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      await ErrorHandling.handleError(error, ErrorCategory._SYSTEM, ErrorSeverity._LOW, {
        component: 'AdvancedGroupHeader',
        action: 'toggleDropdownMenu'
      });
    }
  }, [showDropdownMenu, showAdminList, dropdownAnimation, adminListAnimation]);

  // Handle image load errors
  const handleImageError = useCallback(() => {
    setImageLoadError(true);
  }, []);

  const handleMostActiveImageError = useCallback(() => {
    setMostActiveImageError(true);
  }, []);

  // Handle back button with haptic feedback
  const handleBack = useCallback(async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      onBack();
    } catch (error) {
      onBack(); // Fallback if haptics fail
    }
  }, [onBack]);

  // Handle group profile press with haptic feedback
  const handleGroupProfilePress = useCallback(async () => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onGroupProfilePress();
    } catch (error) {
      onGroupProfilePress(); // Fallback if haptics fail
    }
  }, [onGroupProfilePress]);

  // Handle admin press
  const handleAdminPress = useCallback(async (admin: { id: string; name: string; username: string }) => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onAdminPress?.(admin);
    } catch (error) {
      onAdminPress?.(admin); // Fallback if haptics fail
    }
  }, [onAdminPress]);

  // Handle menu actions
  const handleMenuAction = useCallback(async (action: 'settings' | 'profile' | 'mute' | 'leave') => {
    try {
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onMenuAction?.(action);
      setShowDropdownMenu(false);
      Animated.timing(dropdownAnimation, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } catch (error) {
      onMenuAction?.(action); // Fallback if haptics fail
    }
  }, [onMenuAction, dropdownAnimation]);

  // Calculate responsive dimensions
  const headerHeight = DeviceInfo.statusBarHeight + ResponsiveSpacing.xl;
  const avatarSize = DeviceInfo.isSmallPhone ? 36 : 40;
  const smallAvatarSize = DeviceInfo.isSmallPhone ? 20 : 24;

  return (
    <Animated.View
      style={{
        opacity: headerOpacity,
        backgroundColor: IRACHAT_COLORS.primary,
        paddingTop: insets.top + ResponsiveSpacing.md,
        paddingBottom: ResponsiveSpacing.md,
        paddingHorizontal: ResponsiveSpacing.screenPadding,
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        position: "relative",
        minHeight: headerHeight,
        ...SHADOWS.md,
      }}
    >
      {/* Left Side - Group Profile & Most Active */}
      <View style={{ flexDirection: "row", alignItems: "center", flex: 1 }}>
        {/* Back Button */}
        <Pressable
          onPress={handleBack}
          style={({ pressed }) => ({
            marginRight: ResponsiveSpacing.sm,
            padding: ResponsiveSpacing.xs,
            borderRadius: ResponsiveSpacing.xs,
            backgroundColor: pressed ? 'rgba(255,255,255,0.1)' : 'transparent',
            minWidth: ResponsiveSpacing.minTouchTarget,
            minHeight: ResponsiveSpacing.minTouchTarget,
            alignItems: 'center',
            justifyContent: 'center',
          })}
          accessibilityRole="button"
          accessibilityLabel="Go back"
          accessibilityHint="Navigate back to previous screen"
        >
          <Ionicons
            name="arrow-back"
            size={DeviceInfo.isSmallPhone ? 20 : 24}
            color={IRACHAT_COLORS.textOnPrimary}
          />
        </Pressable>

        {/* Group Profile Picture */}
        <Pressable
          onPress={handleGroupProfilePress}
          style={({ pressed }) => ({
            position: "relative",
            opacity: pressed ? 0.8 : 1,
          })}
          accessibilityRole="button"
          accessibilityLabel={`View ${groupName} profile`}
          accessibilityHint="Open group profile and settings"
        >
          {imageLoadError ? (
            <View
              style={{
                width: avatarSize,
                height: avatarSize,
                borderRadius: avatarSize / 2,
                backgroundColor: IRACHAT_COLORS.primaryLight,
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Text
                style={{
                  color: IRACHAT_COLORS.textOnPrimary,
                  fontSize: ResponsiveTypography.fontSize.sm,
                  fontWeight: '600',
                }}
              >
                {groupName.substring(0, 2).toUpperCase()}
              </Text>
            </View>
          ) : (
            <Image
              source={{ uri: groupAvatar }}
              style={{
                width: avatarSize,
                height: avatarSize,
                borderRadius: avatarSize / 2,
                backgroundColor: IRACHAT_COLORS.primaryLight,
              }}
              onError={handleImageError}
            />
          )}

          {/* Most Active Member Avatar (Stacked 50% overlap) */}
          {mostActiveUser?.isVisible && (
            <Pressable
              onPress={handleMostActiveTap}
              style={({ pressed }) => ({
                position: "absolute",
                right: -8,
                bottom: -4,
                width: smallAvatarSize,
                height: smallAvatarSize,
                borderRadius: smallAvatarSize / 2,
                borderWidth: 2,
                borderColor: IRACHAT_COLORS.surface,
                opacity: pressed ? 0.8 : 1,
              })}
              accessibilityRole="button"
              accessibilityLabel={`Most active member: ${mostActiveUser.name}`}
              accessibilityHint="Tap to see more information about the most active member"
            >
              {mostActiveImageError ? (
                <View
                  style={{
                    width: smallAvatarSize - 4,
                    height: smallAvatarSize - 4,
                    borderRadius: (smallAvatarSize - 4) / 2,
                    backgroundColor: IRACHAT_COLORS.primaryLight,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Text
                    style={{
                      color: IRACHAT_COLORS.textOnPrimary,
                      fontSize: ResponsiveTypography.fontSize.xs,
                      fontWeight: '600',
                    }}
                  >
                    {mostActiveUser.name.substring(0, 1).toUpperCase()}
                  </Text>
                </View>
              ) : (
                <Image
                  source={{ uri: mostActiveUser.avatar }}
                  style={{
                    width: smallAvatarSize - 4,
                    height: smallAvatarSize - 4,
                    borderRadius: (smallAvatarSize - 4) / 2,
                    backgroundColor: IRACHAT_COLORS.primaryLight,
                  }}
                  onError={handleMostActiveImageError}
                />
              )}
            </Pressable>
          )}
        </Pressable>

        {/* Group Info */}
        <Pressable
          onPress={handleGroupProfilePress}
          style={({ pressed }) => ({
            marginLeft: ResponsiveSpacing.sm,
            flex: 1,
            opacity: pressed ? 0.8 : 1,
            paddingVertical: ResponsiveSpacing.xs,
          })}
          accessibilityRole="button"
          accessibilityLabel={`${groupName}, ${memberCount} members`}
          accessibilityHint="Tap to view group details"
        >
          <Text
            style={{
              color: IRACHAT_COLORS.textOnPrimary,
              fontSize: ResponsiveTypography.fontSize.lg,
              fontWeight: "600",
              fontFamily: TYPOGRAPHY.fontFamily,
              marginBottom: 2,
            }}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {groupName}
          </Text>
          <Text
            style={{
              color: IRACHAT_COLORS.primaryLight,
              fontSize: ResponsiveTypography.fontSize.sm,
              fontFamily: TYPOGRAPHY.fontFamily,
            }}
            numberOfLines={1}
          >
            {memberCount} {memberCount === 1 ? 'member' : 'members'}
            {isLoading && ' • Loading...'}
          </Text>
        </Pressable>
      </View>

      {/* Right Side - Admin Toggle & Menu */}
      <View style={{ flexDirection: "row", alignItems: "center" }}>
        {/* Admin Toggle Icon */}
        {admins.length > 0 && (
          <Pressable
            onPress={toggleAdminList}
            style={({ pressed }) => ({
              marginRight: ResponsiveSpacing.sm,
              padding: ResponsiveSpacing.xs,
              borderRadius: ResponsiveSpacing.xs,
              backgroundColor: pressed ? 'rgba(255,255,255,0.1)' : 'transparent',
              minWidth: ResponsiveSpacing.minTouchTarget,
              minHeight: ResponsiveSpacing.minTouchTarget,
              alignItems: 'center',
              justifyContent: 'center',
            })}
            accessibilityRole="button"
            accessibilityLabel={`View admins (${admins.length})`}
            accessibilityHint="Show list of group administrators"
            accessibilityState={{ expanded: showAdminList }}
          >
            <Ionicons
              name="shield-checkmark"
              size={DeviceInfo.isSmallPhone ? 18 : 20}
              color={IRACHAT_COLORS.textOnPrimary}
            />
          </Pressable>
        )}

        {/* 3-Dot Menu */}
        <Pressable
          onPress={toggleDropdownMenu}
          style={({ pressed }) => ({
            padding: ResponsiveSpacing.xs,
            borderRadius: ResponsiveSpacing.xs,
            backgroundColor: pressed ? 'rgba(255,255,255,0.1)' : 'transparent',
            minWidth: ResponsiveSpacing.minTouchTarget,
            minHeight: ResponsiveSpacing.minTouchTarget,
            alignItems: 'center',
            justifyContent: 'center',
          })}
          accessibilityRole="button"
          accessibilityLabel="More options"
          accessibilityHint="Open group menu with additional options"
          accessibilityState={{ expanded: showDropdownMenu }}
        >
          <Ionicons
            name="ellipsis-vertical"
            size={DeviceInfo.isSmallPhone ? 18 : 20}
            color={IRACHAT_COLORS.textOnPrimary}
          />
        </Pressable>
      </View>

      {/* Most Active Member Text (Positioned to the right) */}
      {showMostActiveText && mostActiveUser && (
        <Animated.View
          style={{
            position: "absolute",
            right: ResponsiveSpacing.screenPadding,
            top: headerHeight + ResponsiveSpacing.xs,
            backgroundColor: IRACHAT_COLORS.overlay,
            paddingHorizontal: ResponsiveSpacing.md,
            paddingVertical: ResponsiveSpacing.sm,
            borderRadius: ResponsiveSpacing.sm,
            maxWidth: DeviceInfo.screenWidth * 0.8,
            opacity: mostActiveTextAnimation,
            transform: [
              {
                translateY: mostActiveTextAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-10, 0],
                }),
              },
              {
                scale: mostActiveTextAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.9, 1],
                }),
              },
            ],
            ...SHADOWS.sm,
          }}
          accessibilityRole="text"
          accessibilityLabel={`Most active member information: ${mostActiveUser.name} has been the most active in the last seven days`}
        >
          <Text
            style={{
              color: IRACHAT_COLORS.textOnPrimary,
              fontSize: ResponsiveTypography.fontSize.sm,
              fontWeight: "500",
              fontFamily: TYPOGRAPHY.fontFamily,
              lineHeight: ResponsiveTypography.fontSize.sm * 1.4,
            }}
          >
            {mostActiveUser.name} is the most active member in the last seven days.
          </Text>
        </Animated.View>
      )}

      {/* Admin List Overlay */}
      {showAdminList && admins.length > 0 && (
        <Animated.View
          style={{
            position: "absolute",
            top: headerHeight + ResponsiveSpacing.xs,
            right: ResponsiveSpacing.screenPadding + ResponsiveSpacing.xl,
            backgroundColor: IRACHAT_COLORS.surface,
            borderRadius: ResponsiveSpacing.md,
            padding: ResponsiveSpacing.md,
            minWidth: DeviceInfo.isSmallPhone ? 140 : 160,
            maxWidth: DeviceInfo.screenWidth * 0.6,
            opacity: adminListAnimation,
            transform: [
              {
                scale: adminListAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }),
              },
              {
                translateY: adminListAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-10, 0],
                }),
              },
            ],
            ...SHADOWS.lg,
          }}
          accessibilityRole="menu"
          accessibilityLabel="Group administrators list"
        >
          <Text
            style={{
              fontSize: ResponsiveTypography.fontSize.base,
              fontWeight: "600",
              marginBottom: ResponsiveSpacing.sm,
              color: IRACHAT_COLORS.text,
              fontFamily: TYPOGRAPHY.fontFamily,
            }}
          >
            Admins ({admins.length})
          </Text>
          {admins.map((admin, index) => (
            <Pressable
              key={admin.id}
              onPress={() => handleAdminPress(admin)}
              style={({ pressed }) => ({
                paddingVertical: ResponsiveSpacing.xs,
                paddingHorizontal: ResponsiveSpacing.sm,
                borderRadius: ResponsiveSpacing.xs,
                backgroundColor: pressed ? IRACHAT_COLORS.overlayLight : 'transparent',
                marginBottom: index < admins.length - 1 ? ResponsiveSpacing.xs : 0,
              })}
              accessibilityRole="button"
              accessibilityLabel={`Admin: ${admin.name || admin.username}`}
              accessibilityHint="Tap to view admin profile"
            >
              <Text
                style={{
                  fontSize: ResponsiveTypography.fontSize.sm,
                  color: IRACHAT_COLORS.textSecondary,
                  fontFamily: TYPOGRAPHY.fontFamily,
                }}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {admin.name || admin.username}
              </Text>
            </Pressable>
          ))}
        </Animated.View>
      )}

      {/* Dropdown Menu */}
      {showDropdownMenu && (
        <Animated.View
          style={{
            position: "absolute",
            top: headerHeight + ResponsiveSpacing.xs,
            right: ResponsiveSpacing.screenPadding,
            backgroundColor: IRACHAT_COLORS.surface,
            borderRadius: ResponsiveSpacing.md,
            padding: ResponsiveSpacing.xs,
            minWidth: DeviceInfo.isSmallPhone ? 140 : 160,
            maxWidth: DeviceInfo.screenWidth * 0.5,
            opacity: dropdownAnimation,
            transform: [
              {
                scale: dropdownAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 1],
                }),
              },
              {
                translateY: dropdownAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-10, 0],
                }),
              },
            ],
            ...SHADOWS.lg,
          }}
          accessibilityRole="menu"
          accessibilityLabel="Group options menu"
        >
          <Pressable
            onPress={() => handleMenuAction('profile')}
            style={({ pressed }) => ({
              paddingVertical: ResponsiveSpacing.sm,
              paddingHorizontal: ResponsiveSpacing.md,
              borderRadius: ResponsiveSpacing.xs,
              backgroundColor: pressed ? IRACHAT_COLORS.overlayLight : 'transparent',
              flexDirection: 'row',
              alignItems: 'center',
            })}
            accessibilityRole="menuitem"
            accessibilityLabel="View group profile"
          >
            <Ionicons
              name="people-outline"
              size={16}
              color={IRACHAT_COLORS.textSecondary}
              style={{ marginRight: ResponsiveSpacing.sm }}
            />
            <Text
              style={{
                fontSize: ResponsiveTypography.fontSize.base,
                color: IRACHAT_COLORS.text,
                fontFamily: TYPOGRAPHY.fontFamily,
              }}
            >
              Group Info
            </Text>
          </Pressable>

          <Pressable
            onPress={() => handleMenuAction('settings')}
            style={({ pressed }) => ({
              paddingVertical: ResponsiveSpacing.sm,
              paddingHorizontal: ResponsiveSpacing.md,
              borderRadius: ResponsiveSpacing.xs,
              backgroundColor: pressed ? IRACHAT_COLORS.overlayLight : 'transparent',
              flexDirection: 'row',
              alignItems: 'center',
            })}
            accessibilityRole="menuitem"
            accessibilityLabel="Group settings"
          >
            <Ionicons
              name="settings-outline"
              size={16}
              color={IRACHAT_COLORS.textSecondary}
              style={{ marginRight: ResponsiveSpacing.sm }}
            />
            <Text
              style={{
                fontSize: ResponsiveTypography.fontSize.base,
                color: IRACHAT_COLORS.text,
                fontFamily: TYPOGRAPHY.fontFamily,
              }}
            >
              Settings
            </Text>
          </Pressable>

          <Pressable
            onPress={() => handleMenuAction('mute')}
            style={({ pressed }) => ({
              paddingVertical: ResponsiveSpacing.sm,
              paddingHorizontal: ResponsiveSpacing.md,
              borderRadius: ResponsiveSpacing.xs,
              backgroundColor: pressed ? IRACHAT_COLORS.overlayLight : 'transparent',
              flexDirection: 'row',
              alignItems: 'center',
            })}
            accessibilityRole="menuitem"
            accessibilityLabel="Mute notifications"
          >
            <Ionicons
              name="notifications-off-outline"
              size={16}
              color={IRACHAT_COLORS.textSecondary}
              style={{ marginRight: ResponsiveSpacing.sm }}
            />
            <Text
              style={{
                fontSize: ResponsiveTypography.fontSize.base,
                color: IRACHAT_COLORS.text,
                fontFamily: TYPOGRAPHY.fontFamily,
              }}
            >
              Mute
            </Text>
          </Pressable>

          <View
            style={{
              height: 1,
              backgroundColor: IRACHAT_COLORS.border,
              marginVertical: ResponsiveSpacing.xs,
            }}
          />

          <Pressable
            onPress={() => handleMenuAction('leave')}
            style={({ pressed }) => ({
              paddingVertical: ResponsiveSpacing.sm,
              paddingHorizontal: ResponsiveSpacing.md,
              borderRadius: ResponsiveSpacing.xs,
              backgroundColor: pressed ? 'rgba(255, 107, 107, 0.1)' : 'transparent',
              flexDirection: 'row',
              alignItems: 'center',
            })}
            accessibilityRole="menuitem"
            accessibilityLabel="Leave group"
          >
            <Ionicons
              name="exit-outline"
              size={16}
              color={IRACHAT_COLORS.error}
              style={{ marginRight: ResponsiveSpacing.sm }}
            />
            <Text
              style={{
                fontSize: ResponsiveTypography.fontSize.base,
                color: IRACHAT_COLORS.error,
                fontFamily: TYPOGRAPHY.fontFamily,
              }}
            >
              Leave Group
            </Text>
          </Pressable>
        </Animated.View>
      )}
    </Animated.View>
  );
};
