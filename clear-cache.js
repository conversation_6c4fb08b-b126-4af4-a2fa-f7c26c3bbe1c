#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 Clearing Metro and React Native caches...');

try {
  // Clear Metro cache
  console.log('📱 Clearing Metro cache...');
  execSync('npx expo start --clear', { stdio: 'inherit' });
} catch (error) {
  console.log('⚠️ Metro cache clear failed, trying alternative methods...');
  
  try {
    // Alternative: Clear node_modules and reinstall
    console.log('🗑️ Clearing node_modules...');
    execSync('rm -rf node_modules', { stdio: 'inherit' });
    
    console.log('📦 Reinstalling dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    
    console.log('🚀 Starting fresh Metro server...');
    execSync('npx expo start --clear', { stdio: 'inherit' });
  } catch (fallbackError) {
    console.error('❌ Failed to clear cache:', fallbackError.message);
    console.log('💡 Manual steps to try:');
    console.log('1. Delete node_modules folder');
    console.log('2. Run: npm install');
    console.log('3. Run: npx expo start --clear');
    console.log('4. If still failing, restart your development machine');
  }
}
