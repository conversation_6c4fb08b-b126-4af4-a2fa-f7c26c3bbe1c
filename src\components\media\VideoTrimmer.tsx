import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { PanGestureHandler, State } from 'react-native-gesture-handler';
import { VideoView, useVideoPlayer } from 'expo-video';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';

const { width: screenWidth } = Dimensions.get('window');

interface VideoTrimmerProps {
  visible: boolean;
  videoUri: string;
  onClose: () => void;
  onTrimComplete: (trimmedUri: string, startTime: number, endTime: number) => void;
  maxDuration?: number;
}

export const VideoTrimmer: React.FC<VideoTrimmerProps> = ({
  visible,
  videoUri,
  onClose,
  onTrimComplete,
  maxDuration = 60, // 60 seconds max
}) => {
  const player = useVideoPlayer(videoUri, (player) => {
    player.loop = false;
    player.muted = false;
  });
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [startTime, setStartTime] = useState(0);
  const [endTime, setEndTime] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);

  const trimmerWidth = screenWidth - 40;
  const handleWidth = 20;

  useEffect(() => {
    if (duration > 0 && endTime === 0) {
      setEndTime(Math.min(duration, maxDuration));
    }
  }, [duration, maxDuration]);

  useEffect(() => {
    if (!player) return;

    const subscription = player.addListener('statusChange', (status) => {
      if (status.status === 'readyToPlay' && !isLoaded) {
        setIsLoaded(true);
        setDuration(player.duration || 0);
      }

      setCurrentTime(player.currentTime || 0);
      setIsPlaying(player.playing || false);

      // Auto-pause when reaching end time
      if ((player.currentTime || 0) >= endTime && player.playing) {
        player.pause();
      }
    });

    return () => {
      subscription?.remove();
    };
  }, [player, endTime, isLoaded]);

  const togglePlayPause = () => {
    if (!player) return;

    if (isPlaying) {
      player.pause();
    } else {
      // If at end, restart from start time
      if (currentTime >= endTime) {
        player.currentTime = startTime;
      }
      player.play();
    }
  };

  const seekToPosition = (time: number) => {
    if (player) {
      player.currentTime = time;
    }
  };

  const onStartHandleGesture = (event: any) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      const newStartTime = Math.max(0, Math.min(
        (event.nativeEvent.x / trimmerWidth) * duration,
        endTime - 1
      ));
      setStartTime(newStartTime);
      seekToPosition(newStartTime);
    }
  };

  const onEndHandleGesture = (event: any) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      const newEndTime = Math.min(duration, Math.max(
        (event.nativeEvent.x / trimmerWidth) * duration,
        startTime + 1
      ));
      setEndTime(newEndTime);
    }
  };

  const handleTrimComplete = () => {
    // In a real implementation, this would return the actual trimmed video URI
    onTrimComplete(videoUri, startTime, endTime);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const startPosition = (startTime / duration) * trimmerWidth;
  const endPosition = (endTime / duration) * trimmerWidth;
  const currentPosition = (currentTime / duration) * trimmerWidth;

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <View style={styles.videoContainer}>
        <VideoView
          style={styles.video}
          player={player}
          allowsFullscreen={false}
          allowsPictureInPicture={false}
          contentFit="contain"
          nativeControls={false}
        />
        
        <TouchableOpacity style={styles.playButton} onPress={togglePlayPause}>
          <Ionicons 
            name={isPlaying ? "pause" : "play"} 
            size={40} 
            color="white" 
          />
        </TouchableOpacity>
      </View>

      <View style={styles.controls}>
        <Text style={styles.timeText}>
          {formatTime(startTime)} - {formatTime(endTime)}
        </Text>
        
        <View style={styles.trimmerContainer}>
          <View style={styles.trimmerTrack}>
            {/* Selected range */}
            <View 
              style={[
                styles.selectedRange,
                {
                  left: startPosition,
                  width: endPosition - startPosition,
                }
              ]} 
            />
            
            {/* Current time indicator */}
            <View 
              style={[
                styles.currentTimeIndicator,
                { left: currentPosition }
              ]} 
            />
            
            {/* Start handle */}
            <PanGestureHandler onGestureEvent={onStartHandleGesture}>
              <View 
                style={[
                  styles.trimHandle,
                  styles.startHandle,
                  { left: startPosition - handleWidth / 2 }
                ]}
              >
                <View style={styles.handleGrip} />
              </View>
            </PanGestureHandler>
            
            {/* End handle */}
            <PanGestureHandler onGestureEvent={onEndHandleGesture}>
              <View 
                style={[
                  styles.trimHandle,
                  styles.endHandle,
                  { left: endPosition - handleWidth / 2 }
                ]}
              >
                <View style={styles.handleGrip} />
              </View>
            </PanGestureHandler>
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#6B7280" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.doneButton} onPress={handleTrimComplete}>
            <Ionicons name="checkmark" size={24} color="white" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  playButton: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controls: {
    backgroundColor: '#1F2937',
    padding: 20,
    paddingBottom: 40,
  },
  timeText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
  trimmerContainer: {
    marginBottom: 30,
  },
  trimmerTrack: {
    height: 40,
    backgroundColor: '#374151',
    borderRadius: 4,
    position: 'relative',
  },
  selectedRange: {
    position: 'absolute',
    height: '100%',
    backgroundColor: '#87CEEB',
    borderRadius: 4,
  },
  currentTimeIndicator: {
    position: 'absolute',
    width: 2,
    height: '100%',
    backgroundColor: '#EF4444',
    zIndex: 10,
  },
  trimHandle: {
    position: 'absolute',
    width: 20,
    height: 40,
    backgroundColor: '#87CEEB',
    borderRadius: 4,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 20,
  },
  startHandle: {
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  endHandle: {
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
  },
  handleGrip: {
    width: 3,
    height: 20,
    backgroundColor: 'white',
    borderRadius: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cancelButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#374151',
    justifyContent: 'center',
    alignItems: 'center',
  },
  doneButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#87CEEB',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
