import { Alert } from 'react-native';

// Type interfaces for React Native globals
interface HermesInternalType {
  hasPromise?: boolean;
  getPromiseRejectionTracker?: () => ((id: number, rejection: any) => void) | null;
  setPromiseRejectionTracker?: (handler: (id: number, rejection: any) => void) => void;
}

interface ErrorUtilsType {
  getGlobalHandler?: () => ((error: Error, isFatal?: boolean) => void) | undefined;
  setGlobalHandler?: (handler: (error: Error, isFatal?: boolean) => void) => void;
}

// Type-safe global access
const getHermesInternal = (): HermesInternalType | undefined => {
  return (global as any).HermesInternal;
};

const getErrorUtils = (): ErrorUtilsType | undefined => {
  return (global as any).ErrorUtils;
};

/**
 * Crash Prevention Service
 * Handles unhandled errors and prevents app crashes during chat
 */
class CrashPreventionService {
  private errorCount = 0;
  private lastErrorTime = 0;
  private readonly MAX_ERRORS_PER_MINUTE = 5;

  /**
   * Initialize crash prevention
   */
  initialize() {
    // Handle unhandled promise rejections
    this.setupUnhandledRejectionHandler();
    
    // Handle JavaScript errors
    this.setupErrorHandler();
    
    console.log('🛡️ Crash prevention service initialized');
  }

  /**
   * Setup unhandled promise rejection handler
   */
  private setupUnhandledRejectionHandler() {
    const hermesInternal = getHermesInternal();
    const originalHandler = hermesInternal?.hasPromise
      ? hermesInternal.getPromiseRejectionTracker?.()
      : null;

    // Override the default unhandled rejection handler
    if (hermesInternal?.setPromiseRejectionTracker) {
      hermesInternal.setPromiseRejectionTracker((id: number, rejection: any) => {
        console.error('🚨 Unhandled Promise Rejection:', rejection);
        this.handleError(rejection, 'Unhandled Promise Rejection');

        // Call original handler if it exists
        if (originalHandler) {
          originalHandler(id, rejection);
        }
      });
    }
  }

  /**
   * Setup global error handler
   */
  private setupErrorHandler() {
    const errorUtils = getErrorUtils();
    const originalErrorHandler = errorUtils?.getGlobalHandler?.();

    errorUtils?.setGlobalHandler?.((error: Error, isFatal?: boolean) => {
      console.error('🚨 Global Error:', error);
      console.error('🚨 Is Fatal:', isFatal);

      this.handleError(error, isFatal ? 'Fatal Error' : 'Non-Fatal Error');

      // Call original handler
      if (originalErrorHandler) {
        originalErrorHandler(error, isFatal);
      }
    });
  }

  /**
   * Handle errors with rate limiting
   */
  private handleError(error: any, context: string) {
    const now = Date.now();
    
    // Reset error count if more than a minute has passed
    if (now - this.lastErrorTime > 60000) {
      this.errorCount = 0;
    }
    
    this.errorCount++;
    this.lastErrorTime = now;
    
    // If too many errors, show a general message
    if (this.errorCount > this.MAX_ERRORS_PER_MINUTE) {
      console.warn('⚠️ Too many errors, suppressing further error dialogs');
      return;
    }

    // Log error details
    console.error(`❌ ${context}:`, error);
    
    // Extract meaningful error message
    const errorMessage = this.extractErrorMessage(error);
    
    // Show user-friendly error message for critical errors
    if (this.isCriticalError(error, context)) {
      setTimeout(() => {
        Alert.alert(
          'App Error',
          `Something went wrong: ${errorMessage}\n\nThe app will continue running, but you may need to restart if issues persist.`,
          [{ text: 'OK' }]
        );
      }, 100);
    }
  }

  /**
   * Extract meaningful error message
   */
  private extractErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    if (error?.toString) {
      return error.toString();
    }
    
    return 'Unknown error occurred';
  }

  /**
   * Determine if error is critical enough to show to user
   */
  private isCriticalError(error: any, context: string): boolean {
    const errorMessage = this.extractErrorMessage(error).toLowerCase();
    
    // Don't show alerts for these common non-critical errors
    const nonCriticalPatterns = [
      'network request failed',
      'timeout',
      'cancelled',
      'aborted',
      'permission denied',
      'not found',
      'already exists',
    ];
    
    if (nonCriticalPatterns.some(pattern => errorMessage.includes(pattern))) {
      return false;
    }
    
    // Show alerts for fatal errors or auth-related errors
    return context.includes('Fatal') || 
           errorMessage.includes('auth') || 
           errorMessage.includes('firebase') ||
           errorMessage.includes('database');
  }

  /**
   * Manually report an error
   */
  reportError(error: any, context: string = 'Manual Report') {
    this.handleError(error, context);
  }

  /**
   * Safe async wrapper that catches errors
   */
  async safeAsync<T>(
    asyncFn: () => Promise<T>, 
    fallback?: T,
    context: string = 'Async Operation'
  ): Promise<T | undefined> {
    try {
      return await asyncFn();
    } catch (error) {
      console.error(`❌ Safe async error in ${context}:`, error);
      this.handleError(error, context);
      return fallback;
    }
  }

  /**
   * Safe sync wrapper that catches errors
   */
  safeSync<T>(
    syncFn: () => T, 
    fallback?: T,
    context: string = 'Sync Operation'
  ): T | undefined {
    try {
      return syncFn();
    } catch (error) {
      console.error(`❌ Safe sync error in ${context}:`, error);
      this.handleError(error, context);
      return fallback;
    }
  }
}

// Export singleton instance
export const crashPreventionService = new CrashPreventionService();
