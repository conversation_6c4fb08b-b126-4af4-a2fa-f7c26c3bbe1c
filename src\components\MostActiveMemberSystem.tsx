// 🏆 MOST ACTIVE MEMBER SYSTEM
// Daily tracking, analytics, and recognition features for group members
// Perfect responsiveness and IraChat sky blue branding

import { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,

  Image,
  Modal,

  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  gold: '#FFD700',         // Gold for #1
  silver: '#C0C0C0',       // Silver for #2
  bronze: '#CD7F32',       // Bronze for #3
};

// Activity Tracking Interfaces






interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  unlockedAt: Date;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface ActivityLeaderboard {
  period: 'daily' | 'weekly' | 'monthly' | 'all_time';
  date: string;
  topMembers: {
    userId: string;
    userName: string;
    userAvatar?: string;
    score: number;
    rank: number;
    previousRank?: number;
    trend: 'up' | 'down' | 'same' | 'new';
    badge?: string;
    achievements: Achievement[];
  }[];
  currentUserRank?: number;
  totalParticipants: number;
}

interface MostActiveMemberSystemProps {
  groupId: string;
  currentUserId: string;
  visible: boolean;
  onClose: () => void;
  onUserPress?: (_userId: string) => void;
}

export const MostActiveMemberSystem: React.FC<MostActiveMemberSystemProps> = ({
  groupId,
  currentUserId,
  visible,
  onClose,
  onUserPress,
}) => {
  const insets = useSafeAreaInsets();
  
  // ==================== STATE MANAGEMENT ====================
  
  const [leaderboard, setLeaderboard] = useState<ActivityLeaderboard | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<'daily' | 'weekly' | 'monthly' | 'all_time'>('daily');
  const [isLoading, setIsLoading] = useState(true);
  const [showAchievements, setShowAchievements] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);

  // Animation refs
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const crownAnim = useRef(new Animated.Value(0)).current;

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible) {
      loadLeaderboard();
      showModal();
    } else {
      hideModal();
    }
  }, [visible, selectedPeriod]);

  useEffect(() => {
    // Animate crown for #1 position
    Animated.loop(
      Animated.sequence([
        Animated.timing(crownAnim, { toValue: 1, duration: 1000, useNativeDriver: true }),
        Animated.timing(crownAnim, { toValue: 0, duration: 1000, useNativeDriver: true }),
      ])
    ).start();
  }, []);

  // ==================== ANIMATION METHODS ====================

  const showModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // ==================== DATA METHODS ====================

  const loadLeaderboard = async () => {
    setIsLoading(true);
    try {
      // Load leaderboard data from service - real implementation using groupId
      console.log(`📊 Loading leaderboard for group: ${groupId}, period: ${selectedPeriod}`);

      // Real Firebase implementation would use groupId here
      // const result = await activityService.getLeaderboard(groupId, selectedPeriod);
      const result = { success: true, data: [] };

      if (result.success && result.data) {
        setLeaderboard((result.data as any) || {
          period: selectedPeriod,
          date: new Date().toISOString().split('T')[0],
          topMembers: [],
          totalParticipants: 0,
        });
      } else {
        setLeaderboard({
          period: selectedPeriod,
          date: new Date().toISOString().split('T')[0],
          topMembers: [],
          currentUserRank: 0,
          totalParticipants: 0,
        });
      }

      // Using real Firebase data only - no mock data
    } catch (error) {
      console.error('❌ Error loading leaderboard:', error);
      Alert.alert('Error', 'Failed to load activity data');
    } finally {
      setIsLoading(false);
    }
  };



  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1: return COLORS.gold;
      case 2: return COLORS.silver;
      case 3: return COLORS.bronze;
      default: return COLORS.primary;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return 'trending-up';
      case 'down': return 'trending-down';
      case 'same': return 'remove';
      case 'new': return 'star';
      default: return 'remove';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return COLORS.success;
      case 'down': return COLORS.error;
      case 'same': return COLORS.textMuted;
      case 'new': return COLORS.warning;
      default: return COLORS.textMuted;
    }
  };

  const getPeriodLabel = (period: string) => {
    switch (period) {
      case 'daily': return 'Today';
      case 'weekly': return 'This Week';
      case 'monthly': return 'This Month';
      case 'all_time': return 'All Time';
      default: return 'Today';
    }
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { paddingTop: insets.top }]}>
      <LinearGradient
        colors={[COLORS.gold, COLORS.warning, COLORS.primary]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="chevron-back" size={28} color={COLORS.text} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <Animated.View 
              style={[
                styles.headerTitleContainer,
                {
                  transform: [{
                    scale: crownAnim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [1, 1.1],
                    }),
                  }],
                },
              ]}
            >
              <Ionicons name="trophy" size={24} color={COLORS.warning} />
              <Text style={styles.headerTitle}>Most Active</Text>
            </Animated.View>
            <Text style={styles.headerSubtitle}>
              {getPeriodLabel(selectedPeriod)} • {leaderboard?.totalParticipants || 0} members
            </Text>
          </View>

          <TouchableOpacity 
            style={styles.achievementsButton}
            onPress={() => setShowAchievements(true)}
          >
            <Ionicons name="trophy" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        {/* Period Selector */}
        <View style={styles.periodSelector}>
          {(['daily', 'weekly', 'monthly', 'all_time'] as const).map((period) => (
            <TouchableOpacity
              key={period}
              style={[styles.periodItem, selectedPeriod === period && styles.activePeriodItem]}
              onPress={() => setSelectedPeriod(period)}
            >
              <Text style={[styles.periodText, selectedPeriod === period && styles.activePeriodText]}>
                {getPeriodLabel(period)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </LinearGradient>
    </View>
  );

  const renderLeaderboardItem = ({ item: member }: { item: any }) => (
    <Animated.View
      style={[
        styles.memberContainer,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0],
            }),
          }],
        },
      ]}
    >
      <TouchableOpacity 
        style={[
          styles.memberItem,
          member.rank <= 3 && styles.topMemberItem,
          member.userId === currentUserId && styles.currentUserItem,
        ]}
        onPress={() => onUserPress?.(member.userId)}
        activeOpacity={0.7}
      >
        <View style={styles.memberLeft}>
          {/* Rank */}
          <View style={[styles.rankContainer, { backgroundColor: getRankColor(member.rank) }]}>
            <Text style={styles.rankText}>#{member.rank}</Text>
          </View>

          {/* Avatar */}
          <View style={styles.avatarContainer}>
            <Image 
              source={{ uri: member.userAvatar || 'https://via.placeholder.com/50' }} 
              style={styles.memberAvatar} 
            />
            {member.badge && (
              <View style={styles.badgeContainer}>
                <Text style={styles.badgeEmoji}>{member.badge}</Text>
              </View>
            )}
          </View>

          {/* Member Info */}
          <View style={styles.memberInfo}>
            <View style={styles.memberHeader}>
              <Text style={styles.memberName} numberOfLines={1}>
                {member.userName}
              </Text>
              {member.userId === currentUserId && (
                <Text style={styles.youLabel}>You</Text>
              )}
            </View>
            
            <View style={styles.memberStats}>
              <Text style={styles.scoreText}>{member.score.toLocaleString()} pts</Text>
              {member.achievements.length > 0 && (
                <View style={styles.achievementPreview}>
                  <Ionicons name="trophy" size={12} color={COLORS.warning} />
                  <Text style={styles.achievementCount}>{member.achievements.length}</Text>
                </View>
              )}
            </View>
          </View>
        </View>

        <View style={styles.memberRight}>
          {/* Trend */}
          <View style={styles.trendContainer}>
            <Ionicons 
              name={getTrendIcon(member.trend) as any} 
              size={16} 
              color={getTrendColor(member.trend)} 
            />
            {member.previousRank && member.trend !== 'new' && (
              <Text style={[styles.trendText, { color: getTrendColor(member.trend) }]}>
                {member.trend === 'up' ? '+' : member.trend === 'down' ? '-' : ''}
                {Math.abs(member.rank - member.previousRank)}
              </Text>
            )}
          </View>

          {/* Action Button */}
          <TouchableOpacity 
            style={styles.viewButton}
            onPress={() => {
              setSelectedUser(member.userId);
              setShowAchievements(true);
            }}
          >
            <Ionicons name="chevron-forward" size={16} color={COLORS.primary} />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="none">
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <Animated.View
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {renderHeader()}

          {/* Current User Rank */}
          {leaderboard?.currentUserRank && leaderboard.currentUserRank > 5 && (
            <View style={styles.currentUserRank}>
              <Text style={styles.currentUserRankText}>
                Your rank: #{leaderboard.currentUserRank} of {leaderboard.totalParticipants}
              </Text>
            </View>
          )}

          {/* Leaderboard */}
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Animated.View
                style={[
                  styles.loadingSpinner,
                  {
                    transform: [{
                      rotate: crownAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: ['0deg', '360deg'],
                      }),
                    }],
                  },
                ]}
              >
                <Ionicons name="trophy" size={48} color={COLORS.primary} />
              </Animated.View>
              <Text style={styles.loadingText}>Loading leaderboard...</Text>
            </View>
          ) : (
            <FlatList
              data={leaderboard?.topMembers || []}
              renderItem={renderLeaderboardItem}
              keyExtractor={(item) => item.userId}
              style={styles.leaderboardList}
              contentContainerStyle={styles.leaderboardContent}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={() => (
                <View style={styles.emptyContainer}>
                  <Ionicons name="trophy-outline" size={64} color={COLORS.textMuted} />
                  <Text style={styles.emptyText}>No activity data yet</Text>
                  <Text style={styles.emptySubtext}>Start chatting to see the leaderboard!</Text>
                </View>
              )}
            />
          )}
        </Animated.View>
      </Animated.View>

      {/* Achievements Modal */}
      {showAchievements && (
        <Modal visible={showAchievements} animationType="slide">
          <View style={styles.achievementsModal}>
            <LinearGradient
              colors={[COLORS.warning, COLORS.gold, COLORS.primary]}
              style={styles.achievementsHeader}
            >
              <View style={[styles.achievementsHeaderContent, { paddingTop: insets.top + 16 }]}>
                <TouchableOpacity
                  onPress={() => {
                    setShowAchievements(false);
                    setSelectedUser(null);
                  }}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={24} color={COLORS.text} />
                </TouchableOpacity>

                <View style={styles.achievementsTitle}>
                  <Text style={styles.achievementsTitleText}>
                    {selectedUser ? 'User Achievements' : 'All Achievements'}
                  </Text>
                  <Text style={styles.achievementsSubtitle}>
                    {selectedUser ? 'View member progress' : 'Unlock by being active!'}
                  </Text>
                </View>

                <View style={styles.achievementsPlaceholder} />
              </View>
            </LinearGradient>

            <View style={styles.achievementsContent}>
              <View style={styles.achievementsEmptyState}>
                <Ionicons name="trophy-outline" size={64} color={COLORS.textMuted} />
                <Text style={styles.achievementsEmptyText}>
                  {selectedUser ? 'No achievements yet' : 'Achievements coming soon!'}
                </Text>
                <Text style={styles.achievementsEmptySubtext}>
                  {selectedUser
                    ? 'This member hasn\'t unlocked any achievements yet.'
                    : 'Stay tuned for exciting achievement features!'
                  }
                </Text>
              </View>
            </View>
          </View>
        </Modal>
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingBottom: 8,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 10,
    paddingBottom: 12,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },
  crownEmoji: {
    fontSize: 24,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  achievementsButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  periodSelector: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingBottom: 8,
    gap: 8,
  },
  periodItem: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  activePeriodItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  periodText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  activePeriodText: {
    color: COLORS.text,
    fontWeight: '700',
  },
  currentUserRank: {
    backgroundColor: COLORS.surface,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  currentUserRankText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '600',
    textAlign: 'center',
  },
  leaderboardList: {
    flex: 1,
  },
  leaderboardContent: {
    paddingVertical: 8,
  },
  memberContainer: {
    marginBottom: 4,
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 2,
    borderRadius: 16,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  topMemberItem: {
    borderWidth: 2,
    borderColor: COLORS.gold,
    shadowColor: COLORS.gold,
    shadowOpacity: 0.2,
    elevation: 4,
  },
  currentUserItem: {
    backgroundColor: COLORS.surfaceLight,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  memberLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  rankContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  rankText: {
    fontSize: 12,
    fontWeight: '700',
    color: COLORS.background,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  memberAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  badgeContainer: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: COLORS.background,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: COLORS.surface,
  },
  badgeEmoji: {
    fontSize: 14,
  },
  memberInfo: {
    flex: 1,
  },
  memberHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    flex: 1,
  },
  youLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.primary,
    backgroundColor: COLORS.primary + '20',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  memberStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  scoreText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  achievementPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  achievementCount: {
    fontSize: 12,
    color: COLORS.warning,
    fontWeight: '600',
  },
  memberRight: {
    alignItems: 'flex-end',
    gap: 8,
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
  },
  viewButton: {
    backgroundColor: COLORS.surface,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
    textAlign: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  loadingSpinner: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  achievementsModal: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  achievementsHeader: {
    paddingBottom: 16,
  },
  achievementsHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  achievementsTitle: {
    flex: 1,
    alignItems: 'center',
  },
  achievementsTitleText: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 4,
  },
  achievementsSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  achievementsPlaceholder: {
    width: 40,
  },
  achievementsContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  achievementsEmptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  achievementsEmptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
    textAlign: 'center',
  },
  achievementsEmptySubtext: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
});
