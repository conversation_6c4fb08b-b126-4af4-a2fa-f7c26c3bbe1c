// ⚙️ REAL SETTINGS SERVICE - Complete app settings and preferences
// User profile management, privacy settings, notifications, and app preferences

import {
  doc,
  updateDoc,
  getDoc,
  serverTimestamp,
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL } from 'firebase/storage';
import { updateProfile } from 'firebase/auth';
import { db, storage, auth } from './firebaseSimple';
import * as ImagePicker from 'expo-image-picker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface UserSettings {
  // Profile settings
  displayName: string;
  bio?: string;
  avatar?: string;
  phoneNumber: string;

  
  // Privacy settings
  profileVisibility: 'everyone' | 'contacts' | 'nobody';
  lastSeenVisibility: 'everyone' | 'contacts' | 'nobody';
  profilePhotoVisibility: 'everyone' | 'contacts' | 'nobody';
  statusVisibility: 'everyone' | 'contacts' | 'nobody';
  readReceiptsEnabled: boolean;
  
  // Notification settings
  messageNotifications: boolean;
  callNotifications: boolean;
  groupNotifications: boolean;
  soundEnabled: boolean;
  vibrationEnabled: boolean;
  notificationPreview: boolean;
  
  // Chat settings
  enterToSend: boolean;
  fontSize: 'small' | 'medium' | 'large';
  chatWallpaper?: string;
  autoDownloadMedia: 'never' | 'wifi' | 'always';
  
  // Security settings
  twoFactorEnabled: boolean;
  fingerprintLockEnabled: boolean;
  autoLockTime: number; // in minutes
  
  // App preferences
  theme: 'light' | 'dark' | 'auto';
  language: string;
  
  // Backup settings
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
  includeVideos: boolean;
  
  // Advanced settings
  keepChatArchived: boolean;
  showSecurityNotifications: boolean;
  
  // Timestamps
  updatedAt: Date;
}

export interface ProfileUpdateData {
  displayName?: string;
  bio?: string;
  avatar?: string;
  phoneNumber?: string;
  email?: string;
}

class RealSettingsService {
  private readonly SETTINGS_STORAGE_KEY = 'user_settings';
  private isInitialized = false;
  private settingsCache: Map<string, UserSettings> = new Map();
  private syncQueue: Set<string> = new Set();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();
      await this.loadSettingsIntoCache();

      // Set up network state listener for sync
      networkStateManager.addListener('realSettingsService', this.handleNetworkStateChange.bind(this), 5);

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async loadSettingsIntoCache(): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync('SELECT * FROM user_settings');

      result.forEach((row: any) => {
        const settings = this.rowToUserSettings(row);
        this.settingsCache.set(settings.phoneNumber, settings);
      });
    } catch (error) {
      // Continue without cache if loading fails
    }
  }

  private rowToUserSettings(row: any): UserSettings {
    return {
      displayName: row.displayName,
      bio: row.bio,
      avatar: row.avatar,
      phoneNumber: row.phoneNumber,
      profileVisibility: row.profileVisibility,
      lastSeenVisibility: row.lastSeenVisibility,
      profilePhotoVisibility: row.profilePhotoVisibility,
      statusVisibility: row.statusVisibility,
      readReceiptsEnabled: Boolean(row.readReceiptsEnabled),
      messageNotifications: Boolean(row.messageNotifications),
      callNotifications: Boolean(row.callNotifications),
      groupNotifications: Boolean(row.groupNotifications),
      soundEnabled: Boolean(row.soundEnabled),
      vibrationEnabled: Boolean(row.vibrationEnabled),
      notificationPreview: Boolean(row.notificationPreview),
      enterToSend: Boolean(row.enterToSend),
      fontSize: row.fontSize,
      chatWallpaper: row.chatWallpaper,
      autoDownloadMedia: row.autoDownloadMedia,
      twoFactorEnabled: Boolean(row.twoFactorEnabled),
      fingerprintLockEnabled: Boolean(row.fingerprintLockEnabled),
      autoLockTime: row.autoLockTime,
      theme: row.theme,
      language: row.language,
      autoBackup: Boolean(row.autoBackup),
      backupFrequency: row.backupFrequency,
      includeVideos: Boolean(row.includeVideos),
      keepChatArchived: Boolean(row.keepChatArchived),
      showSecurityNotifications: Boolean(row.showSecurityNotifications),
      updatedAt: new Date(row.updatedAt),
    };
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected && this.syncQueue.size > 0) {
      this.processSyncQueue();
    }
  }

  private async processSyncQueue(): Promise<void> {
    if (!networkStateManager.isOnline() || this.syncQueue.size === 0) return;

    const phoneNumbers = Array.from(this.syncQueue);
    this.syncQueue.clear();

    for (const phoneNumber of phoneNumbers) {
      try {
        await this.syncSettingsWithFirebase(phoneNumber);
      } catch (error) {
        // Re-add to queue for retry
        this.syncQueue.add(phoneNumber);
      }
    }
  }

  private async syncSettingsWithFirebase(phoneNumber: string): Promise<void> {
    const localSettings = this.settingsCache.get(phoneNumber);
    if (!localSettings) return;

    try {
      const settingsRef = doc(db, 'user_settings', phoneNumber);
      await updateDoc(settingsRef, {
        ...localSettings,
        updatedAt: serverTimestamp(),
      });

      // Update sync status in local database
      await this.updateSettingsSyncStatus(phoneNumber, 'synced');
    } catch (error) {
      await this.updateSettingsSyncStatus(phoneNumber, 'failed');
      throw error;
    }
  }

  private async updateSettingsSyncStatus(phoneNumber: string, status: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE user_settings SET syncStatus = ?, lastSyncAttempt = ? WHERE phoneNumber = ?
    `, [status, Date.now(), phoneNumber]);
  }

  private async saveSettingsOffline(settings: UserSettings): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      INSERT OR REPLACE INTO user_settings (
        displayName, bio, avatar, phoneNumber, profileVisibility,
        lastSeenVisibility, profilePhotoVisibility, statusVisibility,
        readReceiptsEnabled, messageNotifications, callNotifications,
        groupNotifications, soundEnabled, vibrationEnabled, notificationPreview,
        enterToSend, fontSize, chatWallpaper, autoDownloadMedia,
        twoFactorEnabled, fingerprintLockEnabled, autoLockTime,
        theme, language, autoBackup, backupFrequency, includeVideos,
        keepChatArchived, showSecurityNotifications, updatedAt, syncStatus
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      settings.displayName,
      settings.bio || null,
      settings.avatar || null,
      settings.phoneNumber,
      settings.profileVisibility,
      settings.lastSeenVisibility,
      settings.profilePhotoVisibility,
      settings.statusVisibility,
      settings.readReceiptsEnabled ? 1 : 0,
      settings.messageNotifications ? 1 : 0,
      settings.callNotifications ? 1 : 0,
      settings.groupNotifications ? 1 : 0,
      settings.soundEnabled ? 1 : 0,
      settings.vibrationEnabled ? 1 : 0,
      settings.notificationPreview ? 1 : 0,
      settings.enterToSend ? 1 : 0,
      settings.fontSize,
      settings.chatWallpaper || null,
      settings.autoDownloadMedia,
      settings.twoFactorEnabled ? 1 : 0,
      settings.fingerprintLockEnabled ? 1 : 0,
      settings.autoLockTime,
      settings.theme,
      settings.language,
      settings.autoBackup ? 1 : 0,
      settings.backupFrequency,
      settings.includeVideos ? 1 : 0,
      settings.keepChatArchived ? 1 : 0,
      settings.showSecurityNotifications ? 1 : 0,
      settings.updatedAt.getTime(),
      'pending'
    ]);
  }

  /**
   * Get user settings (with offline support)
   */
  async getUserSettings(userId: string): Promise<UserSettings | null> {
    try {
      // Try cache first
      const cachedSettings = this.settingsCache.get(userId);
      if (cachedSettings) {
        return cachedSettings;
      }

      // Try offline database
      const offlineDb = offlineDatabaseService.getDatabase();
      const result = await offlineDb.getFirstAsync(`
        SELECT * FROM user_settings WHERE phoneNumber = ?
      `, [userId]);

      if (result) {
        const settings = this.rowToUserSettings(result as any);
        this.settingsCache.set(userId, settings);
        return settings;
      }

      // Try AsyncStorage cache
      const cachedAsyncSettings = await this.getCachedSettings(userId);
      if (cachedAsyncSettings && cachedAsyncSettings.displayName && cachedAsyncSettings.phoneNumber) {
        const fullSettings = cachedAsyncSettings as UserSettings;
        // Save to offline database for future use
        await this.saveSettingsOffline(fullSettings);
        this.settingsCache.set(userId, fullSettings);
        return fullSettings;
      }

      // Try online if available
      if (networkStateManager.isOnline()) {
        try {
          const userRef = doc(db, 'users', userId);
          const userDoc = await getDoc(userRef);

          if (userDoc.exists()) {
            const userData = userDoc.data();
            const settings: UserSettings = {
              // Profile settings
              displayName: userData.name || userData.displayName || '',
              bio: userData.bio || '',
              avatar: userData.avatar || '',
              phoneNumber: userData.phoneNumber || '',

              // Privacy settings (with defaults)
              profileVisibility: userData.settings?.profileVisibility || 'everyone',
              lastSeenVisibility: userData.settings?.lastSeenVisibility || 'everyone',
              profilePhotoVisibility: userData.settings?.profilePhotoVisibility || 'everyone',
              statusVisibility: userData.settings?.statusVisibility || 'everyone',
              readReceiptsEnabled: userData.settings?.readReceiptsEnabled ?? true,

              // Notification settings
              messageNotifications: userData.settings?.messageNotifications ?? true,
              callNotifications: userData.settings?.callNotifications ?? true,
              groupNotifications: userData.settings?.groupNotifications ?? true,
              soundEnabled: userData.settings?.soundEnabled ?? true,
              vibrationEnabled: userData.settings?.vibrationEnabled ?? true,
              notificationPreview: userData.settings?.notificationPreview ?? true,

              // Chat settings
              enterToSend: userData.settings?.enterToSend ?? false,
              fontSize: userData.settings?.fontSize || 'medium',
              chatWallpaper: userData.settings?.chatWallpaper,
              autoDownloadMedia: userData.settings?.autoDownloadMedia || 'wifi',

              // Security settings
              twoFactorEnabled: userData.settings?.twoFactorEnabled ?? false,
              fingerprintLockEnabled: userData.settings?.fingerprintLockEnabled ?? false,
              autoLockTime: userData.settings?.autoLockTime || 5,

              // App preferences
              theme: userData.settings?.theme || 'auto',
              language: userData.settings?.language || 'en',

              // Backup settings
              autoBackup: userData.settings?.autoBackup ?? true,
              backupFrequency: userData.settings?.backupFrequency || 'weekly',
              includeVideos: userData.settings?.includeVideos ?? false,

              // Advanced settings
              keepChatArchived: userData.settings?.keepChatArchived ?? true,
              showSecurityNotifications: userData.settings?.showSecurityNotifications ?? true,

              updatedAt: userData.updatedAt?.toDate() || new Date(),
            };

            // Cache the settings offline
            await this.saveSettingsOffline(settings);
            await this.cacheSettings(userId, settings);
            this.settingsCache.set(userId, settings);

            return settings;
          }
        } catch (onlineError) {
          // Continue to return null if no offline data
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Update user settings
   */
  async updateUserSettings(
    userId: string,
    settings: Partial<UserSettings>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const userRef = doc(db, 'users', userId);
      
      // Prepare settings update
      const settingsUpdate: any = {};
      Object.keys(settings).forEach(key => {
        if (key !== 'updatedAt' && settings[key as keyof UserSettings] !== undefined) {
          settingsUpdate[`settings.${key}`] = settings[key as keyof UserSettings];
        }
      });
      
      // Update cache first
      const cachedSettings = this.settingsCache.get(userId);
      if (cachedSettings) {
        const updatedSettings = { ...cachedSettings, ...settings, updatedAt: new Date() };
        this.settingsCache.set(userId, updatedSettings);
        await this.saveSettingsOffline(updatedSettings);
      }

      // Try to update online if connected
      if (networkStateManager.isOnline()) {
        try {
          // Update in Firebase
          await updateDoc(userRef, {
            ...settingsUpdate,
            updatedAt: serverTimestamp(),
          });

          await this.updateSettingsSyncStatus(userId, 'synced');
        } catch (onlineError) {
          // Queue for sync when online
          this.syncQueue.add(userId);
        }
      } else {
        // Queue for sync when online
        this.syncQueue.add(userId);
      }

      // Cache settings locally
      await this.cacheSettings(userId, settings);

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to update settings' };
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(
    userId: string,
    profileData: ProfileUpdateData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const userRef = doc(db, 'users', userId);
      
      // Update in Firebase
      await updateDoc(userRef, {
        ...profileData,
        updatedAt: serverTimestamp(),
      });
      
      // Update Firebase Auth profile if needed
      if (auth.currentUser && (profileData.displayName || profileData.avatar)) {
        await updateProfile(auth.currentUser, {
          displayName: profileData.displayName,
          photoURL: profileData.avatar,
        });
      }
      
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to update profile' };
    }
  }

  /**
   * Upload profile picture
   */
  async uploadProfilePicture(
    userId: string,
    imageUri: string
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      // Convert URI to blob
      const response = await fetch(imageUri);
      const blob = await response.blob();
      
      // Create storage reference
      const fileName = `profile_${userId}_${Date.now()}.jpg`;
      const storageRef = ref(storage, `profiles/${fileName}`);
      
      // Upload file
      await uploadBytes(storageRef, blob);
      
      // Get download URL
      const downloadURL = await getDownloadURL(storageRef);
      
      // Update user profile with new avatar
      await this.updateUserProfile(userId, { avatar: downloadURL });
      
      return { success: true, url: downloadURL };
    } catch (error) {
      return { success: false, error: 'Failed to upload profile picture' };
    }
  }

  /**
   * Change profile picture
   */
  async changeProfilePicture(userId: string): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      // Request permission
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        return { success: false, error: 'Permission denied' };
      }
      
      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets[0]) {
        return await this.uploadProfilePicture(userId, result.assets[0].uri);
      }
      
      return { success: false, error: 'No image selected' };
    } catch (error) {
      return { success: false, error: 'Failed to change profile picture' };
    }
  }




  /**
   * Cache settings locally
   */
  private async cacheSettings(userId: string, settings: Partial<UserSettings>): Promise<void> {
    try {
      const key = `${this.SETTINGS_STORAGE_KEY}_${userId}`;
      const existingSettings = await AsyncStorage.getItem(key);
      const currentSettings = existingSettings ? JSON.parse(existingSettings) : {};
      
      const updatedSettings = { ...currentSettings, ...settings };
      await AsyncStorage.setItem(key, JSON.stringify(updatedSettings));
    } catch (error) {
      // Silently fail cache operations
    }
  }

  /**
   * Get cached settings
   */
  async getCachedSettings(userId: string): Promise<Partial<UserSettings> | null> {
    try {
      const key = `${this.SETTINGS_STORAGE_KEY}_${userId}`;
      const cachedSettings = await AsyncStorage.getItem(key);
      return cachedSettings ? JSON.parse(cachedSettings) : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Clear cached settings
   */
  async clearCachedSettings(userId: string): Promise<void> {
    try {
      const key = `${this.SETTINGS_STORAGE_KEY}_${userId}`;
      await AsyncStorage.removeItem(key);
    } catch (error) {
      // Silently fail cache operations
    }
  }

  /**
   * Export user data
   */
  async exportUserData(userId: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        
        // Remove sensitive data
        const exportData = {
          profile: {
            name: userData.name,
            bio: userData.bio,
            phoneNumber: userData.phoneNumber,
            email: userData.email,
            createdAt: userData.createdAt,
          },
          settings: userData.settings,
          // Add other non-sensitive data as needed
        };
        
        return { success: true, data: exportData };
      }
      
      return { success: false, error: 'User data not found' };
    } catch (error) {
      return { success: false, error: 'Failed to export user data' };
    }
  }

  /**
   * Delete user account
   */
  async deleteUserAccount(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // This would require careful implementation in production
      // For now, just mark as deleted
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        deleted: true,
        deletedAt: serverTimestamp(),
      });
      
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete account' };
    }
  }

  /**
   * Cleanup method
   */
  cleanup(): void {
    networkStateManager.removeListener('realSettingsService');
    this.settingsCache.clear();
    this.syncQueue.clear();
    this.isInitialized = false;
  }
}

// Export singleton instance
export const realSettingsService = new RealSettingsService();
export default realSettingsService;
