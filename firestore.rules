rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // ========================================
    // SECURITY HELPER FUNCTIONS
    // ========================================

    function isAuthenticated() {
      return request.auth != null && request.auth.uid != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isValidPhoneAuth() {
      return isAuthenticated() &&
             request.auth.token.phone_number != null &&
             request.auth.token.phone_number.size() > 0;
    }

    function isValidEmailAuth() {
      return isAuthenticated() &&
             request.auth.token.email != null &&
             request.auth.token.email.size() > 0 &&
             request.auth.token.email_verified == true;
    }

    function hasValidAuth() {
      return isValidPhoneAuth() || isValidEmailAuth();
    }

    function isValidUsername(username) {
      return username is string &&
             username.size() >= 3 &&
             username.size() <= 20 &&
             username.matches('^[a-zA-Z0-9_]+$');
    }

    function isValidUser() {
      return isAuthenticated() &&
             request.auth.uid != null &&
             request.auth.uid.size() > 0 &&
             hasValidAuth();
    }

    function isValidString(str, minLen, maxLen) {
      return str is string &&
             str.size() >= minLen &&
             str.size() <= maxLen;
    }

    function isValidTimestamp(ts) {
      return ts is timestamp;
    }

    function isRecentTimestamp(ts) {
      return ts is timestamp &&
             ts > request.time - duration.value(1, 'h') &&
             ts <= request.time + duration.value(5, 'm');
    }

    function hasValidUserData(data) {
      return data.keys().hasAll(['displayName']) &&
             isValidString(data.displayName, 1, 50) &&
             (
               // Must have either phone number or email
               (data.keys().hasAny(['phoneNumber']) && isValidString(data.phoneNumber, 10, 20)) ||
               (data.keys().hasAny(['email']) && isValidString(data.email, 5, 100))
             );
    }

    function isGroupMember(groupId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/groups/$(groupId)) &&
             request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.members;
    }

    function isGroupAdmin(groupId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/groups/$(groupId)) &&
             (request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.admins ||
              request.auth.uid == get(/databases/$(database)/documents/groups/$(groupId)).data.createdBy);
    }

    function isChatParticipant(chatId) {
      return isAuthenticated() &&
             exists(/databases/$(database)/documents/chats/$(chatId)) &&
             request.auth.uid in get(/databases/$(database)/documents/chats/$(chatId)).data.participants;
    }
    
    // ========================================
    // USERS COLLECTION - SECURE ACCESS
    // ========================================
    match /users/{userId} {
      // Users can only read/write their own data
      allow read, write: if isOwner(userId);

      // Allow reading limited public profile info only
      allow read: if isAuthenticated() &&
                     resource.data.keys().hasAny(['displayName', 'photoURL', 'status', 'isOnline', 'username']) &&
                     resource.data.get('isPublic', false) == true;

      // Allow authenticated users to read basic profile info for contacts/search
      allow read: if isAuthenticated();

      // Allow users to update their online status
      allow update: if isOwner(userId) &&
                       request.resource.data.keys().hasOnly(['isOnline', 'lastSeen', 'status']) &&
                       request.resource.data.isOnline is bool;

      // Strict validation for user creation/updates
      allow create: if isOwner(userId) &&
                       hasValidUserData(request.resource.data) &&
                       isValidUsername(request.resource.data.username) &&
                       isValidTimestamp(request.resource.data.createdAt) &&
                       isRecentTimestamp(request.resource.data.createdAt);

      allow update: if isOwner(userId) &&
                       hasValidUserData(request.resource.data) &&
                       isValidUsername(request.resource.data.username) &&
                       request.resource.data.createdAt == resource.data.createdAt; // Prevent createdAt modification
    }
    
    // ========================================
    // CHATS COLLECTION - PARTICIPANT ACCESS ONLY
    // ========================================
    match /chats/{chatId} {
      // Only chat participants can read/write
      allow read: if isChatParticipant(chatId);

      allow create: if isAuthenticated() &&
                       request.auth.uid in request.resource.data.participants &&
                       request.resource.data.participants.size() >= 2 &&
                       request.resource.data.participants.size() <= 100 &&
                       isValidTimestamp(request.resource.data.createdAt) &&
                       isRecentTimestamp(request.resource.data.createdAt);

      allow update: if isChatParticipant(chatId) &&
                       request.resource.data.participants == resource.data.participants && // Prevent participant modification
                       request.resource.data.createdAt == resource.data.createdAt; // Prevent createdAt modification

      allow delete: if false; // Chats cannot be deleted, only archived
    }
    
    // ========================================
    // MESSAGES SUBCOLLECTION - SECURE MESSAGING
    // ========================================
    match /chats/{chatId}/messages/{messageId} {
      // Only chat participants can read messages
      allow read: if isChatParticipant(chatId);

      // Strict message creation validation
      allow create: if isChatParticipant(chatId) &&
                       isOwner(request.resource.data.senderId) &&
                       isValidString(request.resource.data.text, 1, 5000) &&
                       isValidTimestamp(request.resource.data.timestamp) &&
                       isRecentTimestamp(request.resource.data.timestamp) &&
                       request.resource.data.senderId == request.auth.uid;

      // Only sender can update their own messages (for editing)
      allow update: if isChatParticipant(chatId) &&
                       isOwner(resource.data.senderId) &&
                       request.resource.data.senderId == resource.data.senderId &&
                       request.resource.data.timestamp == resource.data.timestamp && // Prevent timestamp modification
                       isValidString(request.resource.data.text, 1, 5000);

      // Only sender can delete their own messages
      allow delete: if isChatParticipant(chatId) && isOwner(resource.data.senderId);

      // Reactions subcollection under messages
      match /reactions/{reactionId} {
        allow read: if isAuthenticated();
        allow create: if isAuthenticated() &&
                         isOwner(request.resource.data.userId) &&
                         isValidUser();
        allow update: if isAuthenticated() && isOwner(resource.data.userId);
        allow delete: if isAuthenticated() && isOwner(resource.data.userId);
      }

      // Replies subcollection under messages
      match /replies/{replyId} {
        allow read: if isAuthenticated();
        allow create: if isAuthenticated() &&
                         isOwner(request.resource.data.senderId) &&
                         isValidUser();
        allow update: if isAuthenticated() && isOwner(resource.data.senderId);
        allow delete: if isAuthenticated() && isOwner(resource.data.senderId);
      }
    }

    // Reactions collection (top-level) - users can read all, write their own
    match /reactions/{reactionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Groups collection - members can read, creators can write
    match /groups/{groupId} {
      allow read: if isAuthenticated() && 
                     request.auth.uid in resource.data.members;
      
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.createdBy) &&
                       request.auth.uid in request.resource.data.members;
      
      allow update: if isAuthenticated() && 
                       (isOwner(resource.data.createdBy) || 
                        request.auth.uid in resource.data.admins);
      
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }
    
    // Group messages subcollection
    match /groups/{groupId}/messages/{messageId} {
      allow read: if isAuthenticated() && 
                     exists(/databases/$(database)/documents/groups/$(groupId)) &&
                     request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.members;
      
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.senderId) &&
                       exists(/databases/$(database)/documents/groups/$(groupId)) &&
                       request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.members;
      
      allow update: if isAuthenticated() && isOwner(resource.data.senderId);
      allow delete: if isAuthenticated() && 
                       (isOwner(resource.data.senderId) || 
                        request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.admins);
    }
    
    // Updates/Posts collection - users can read all, write their own
    match /updates/{updateId} {
      allow read: if isAuthenticated();
      
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      
      allow update: if isAuthenticated() && 
                       isOwner(resource.data.userId);
      
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }
    
    // Comments subcollection for updates
    match /updates/{updateId}/comments/{commentId} {
      allow read: if isAuthenticated();
      
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && 
                       (isOwner(resource.data.userId) || 
                        isOwner(get(/databases/$(database)/documents/updates/$(updateId)).data.userId));
    }
    
    // Calls collection - participants can read/write
    match /calls/{callId} {
      allow read, write: if isAuthenticated() &&
                            (isOwner(resource.data.callerId) ||
                             isOwner(resource.data.receiverId));

      allow create: if isAuthenticated() &&
                       (isOwner(request.resource.data.callerId) ||
                        isOwner(request.resource.data.receiverId));
    }
    
    // Notifications collection - users can read/write their own
    match /notifications/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Contacts collection - users can read/write their own
    match /contacts/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // User settings collection - users can read/write their own
    match /settings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    // Block/Report collections - users can read/write their own
    match /blocks/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }
    
    match /reports/{reportId} {
      allow create: if isAuthenticated() && 
                       isOwner(request.resource.data.reporterId) &&
                       isValidUser();
      
      allow read: if isAuthenticated() && isOwner(resource.data.reporterId);
    }
    
    // Analytics collection - read-only for authenticated users
    match /analytics/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write analytics
    }
    
    // Admin collection - no client access
    match /admin/{document=**} {
      allow read, write: if false; // Only server/admin functions can access
    }
    
    // Media collection - users can read all, write their own
    match /media/{mediaId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.uploadedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.uploadedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.uploadedBy);
    }

    // Documents collection - users can read all, write their own
    match /documents/{documentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.uploadedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.uploadedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.uploadedBy);
    }

    // Downloads collection - users can read/write their own
    match /downloads/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Message status collection - participants can read/write
    match /messageStatus/{statusId} {
      allow read, write: if isAuthenticated() &&
                            (isOwner(resource.data.senderId) ||
                             isOwner(resource.data.receiverId));
      allow create: if isAuthenticated() &&
                       (isOwner(request.resource.data.senderId) ||
                        isOwner(request.resource.data.receiverId));
    }

    // Message reactions collection - users can read all, write their own
    match /messageReactions/{reactionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Voice messages collection - chat participants can access
    match /voiceMessages/{messageId} {
      allow read, write: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.senderId) &&
                       isValidUser();
    }

    // Typing indicators collection - chat participants can access
    match /typing/{chatId} {
      allow read, write: if isAuthenticated();
    }

    // Archived chats collection - users can read/write their own
    match /archivedChats/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Pinned chats collection - users can read/write their own
    match /pinnedChats/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Update views collection - track who viewed updates
    match /updateViews/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.viewerId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.viewerId);
    }

    // Update likes collection - track who liked updates
    match /updateLikes/{likeId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Stories collection - users can read all, write their own
    match /stories/{storyId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Story views collection - track who viewed stories
    match /storyViews/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.viewerId) &&
                       isValidUser();
    }

    // Status updates collection - users can read all, write their own
    match /statusUpdates/{statusId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Channels collection - public read, creators can write
    match /channels/{channelId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() &&
                       (isOwner(resource.data.createdBy) ||
                        request.auth.uid in resource.data.admins);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Channel subscriptions collection - users can read/write their own
    match /channelSubscriptions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Call logs collection - participants can read/write
    match /callLogs/{callId} {
      allow read, write: if isAuthenticated() &&
                            (isOwner(resource.data.callerId) ||
                             isOwner(resource.data.receiverId));
      allow create: if isAuthenticated() &&
                       (isOwner(request.resource.data.callerId) ||
                        isOwner(request.resource.data.receiverId));
    }

    // Active calls collection - participants can read/write
    match /activeCalls/{callId} {
      allow read, write: if isAuthenticated() &&
                            (isOwner(resource.data.callerId) ||
                             isOwner(resource.data.receiverId));
      allow create: if isAuthenticated() &&
                       (isOwner(request.resource.data.callerId) ||
                        isOwner(request.resource.data.receiverId));
    }

    // Online status collection - users can read all, write their own
    match /onlineStatus/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(userId);
    }

    // Last seen collection - users can read all, write their own
    match /lastSeen/{userId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && isOwner(userId);
    }

    // Blocked users collection - users can read/write their own
    match /blockedUsers/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // User analytics collection - users can read/write their own
    match /userAnalytics/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // App usage collection - users can read/write their own
    match /appUsage/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Login sessions collection - users can read/write their own
    match /loginSessions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Security logs collection - users can read their own, system can write
    match /securityLogs/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if false; // Only server can write security logs
    }

    // Two factor auth collection - users can read/write their own
    match /twoFactorAuth/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Encryption keys collection - users can read/write their own
    match /encryptionKeys/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Phone verification collection - users can read/write their own
    match /phoneVerification/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Reported content collection - users can create reports, read their own
    match /reportedContent/{reportId} {
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.reporterId) &&
                       isValidUser();
      allow read: if isAuthenticated() && isOwner(resource.data.reporterId);
    }





    // Moderation actions collection - read-only for users, write for admins
    match /moderationActions/{actionId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server/admin functions can write
    }

    // Engagement metrics collection - read-only for authenticated users
    match /engagementMetrics/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write metrics
    }

    // Chat exports collection - users can read/write their own
    match /chatExports/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Backups collection - users can read/write their own
    match /backups/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // User sessions collection - users can read/write their own
    match /userSessions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Shared content collection - users can read all, write their own
    match /sharedContent/{contentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.sharedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.sharedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.sharedBy);
    }

    // Message replies collection - chat participants can access
    match /messageReplies/{replyId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.senderId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.senderId);
      allow delete: if isAuthenticated() && isOwner(resource.data.senderId);
    }

    // Message forwards collection - users can read all, write their own
    match /messageForwards/{forwardId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.forwardedBy) &&
                       isValidUser();
    }

    // Message edits collection - message owners can read/write
    match /messageEdits/{editId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.editedBy) &&
                       isValidUser();
    }

    // Scheduled messages collection - users can read/write their own
    match /messageScheduled/{messageId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.senderId);
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.senderId) &&
                       isValidUser();
    }

    // Group invites collection - group members and invitees can access
    match /groupInvites/{inviteId} {
      allow read: if isAuthenticated() &&
                     (isOwner(resource.data.invitedBy) ||
                      isOwner(resource.data.invitedUser));
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.invitedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() &&
                       (isOwner(resource.data.invitedBy) ||
                        isOwner(resource.data.invitedUser));
      allow delete: if isAuthenticated() &&
                       (isOwner(resource.data.invitedBy) ||
                        isOwner(resource.data.invitedUser));
    }

    // Group roles collection - group admins can read/write
    match /groupRoles/{roleId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAuthenticated() &&
                                       isOwner(request.resource.data.assignedBy);
    }

    // Group settings collection - group members can read, admins can write
    match /groupSettings/{groupId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() &&
                      exists(/databases/$(database)/documents/groups/$(groupId)) &&
                      (request.auth.uid in get(/databases/$(database)/documents/groups/$(groupId)).data.admins ||
                       request.auth.uid == get(/databases/$(database)/documents/groups/$(groupId)).data.createdBy);
    }

    // Muted chats collection - users can read/write their own
    match /mutedChats/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Starred messages collection - users can read/write their own
    match /starredMessages/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // User profiles collection - public read, owner write
    match /userProfiles/{userId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }



    // Update comments collection - users can read all, write their own
    match /updateComments/{commentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Comments subcollection under updates
    match /updates/{updateId}/comments/{commentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Likes subcollection under updates
    match /updates/{updateId}/likes/{likeId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Views subcollection under updates
    match /updates/{updateId}/views/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
    }

    // Test collections for development and testing
    match /test/{testId} {
      allow read, write: if isAuthenticated();
    }

    match /test_messages/{messageId} {
      allow read, write: if isAuthenticated();
    }

    // Navigation state collection - users can read/write their own
    match /navigationState/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Device tokens for push notifications
    match /deviceTokens/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // App settings collection - users can read/write their own
    match /appSettings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // User preferences collection - users can read/write their own
    match /userPreferences/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Chat themes collection - users can read all, write their own
    match /chatThemes/{themeId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Wallpapers collection - users can read all, write their own
    match /wallpapers/{wallpaperId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.uploadedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.uploadedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.uploadedBy);
    }

    // Group calls collection - group members can access
    match /groupCalls/{callId} {
      allow read, write: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.initiatedBy) &&
                       isValidUser();
    }

    // User settings collection - users can read/write their own
    match /userSettings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Deleted messages collection - users can read/write their own
    match /deletedMessages/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Deleted media collection - users can read/write their own
    match /deletedMedia/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Deleted chats collection - users can read/write their own
    match /deletedChats/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }



    // Message scheduling collection - users can read/write their own
    match /scheduledMessages/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Message drafts collection - users can read/write their own
    match /messageDrafts/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Chat templates collection - users can read all, write their own
    match /chatTemplates/{templateId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Quick replies collection - users can read/write their own
    match /quickReplies/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Auto replies collection - users can read/write their own
    match /autoReplies/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Message filters collection - users can read/write their own
    match /messageFilters/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Spam detection collection - read-only for users, write for system
    match /spamDetection/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // Content moderation collection - read-only for users
    match /contentModeration/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // Usage statistics collection - users can read/write their own
    match /usageStats/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Error logs collection - users can create, read their own
    match /errorLogs/{errorId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
    }

    // Feedback collection - users can create and read their own
    match /feedback/{feedbackId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // User reports collection - users can create and read their own
    match /userReports/{reportId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.reporterId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.reporterId);
    }

    // Muted users collection - users can read/write their own
    match /mutedUsers/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Cache data collection - users can read/write their own
    match /cacheData/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // App performance metrics - read-only for users
    match /performanceMetrics/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // System notifications - read-only for users
    match /systemNotifications/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // Profile views collection - users can read/write their own
    match /profileViews/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // User updates grid collection - users can read/write their own
    match /userUpdatesGrid/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Update shares collection - users can read all, write their own
    match /updateShares/{shareId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.sharedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.sharedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.sharedBy);
    }

    // Update saves collection - users can read/write their own
    match /updateSaves/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Full screen interactions collection - users can read/write their own
    match /fullScreenInteractions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Video autoplay analytics collection - read-only for users
    match /videoAutoplayAnalytics/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // Profile highlights collection - users can read all, write their own
    match /profileHighlights/{highlightId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Download queue collection - users can read/write their own
    match /downloadQueue/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }







    // Posts collection - users can read all, write their own
    match /posts/{postId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.authorId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.authorId);
      allow delete: if isAuthenticated() && isOwner(resource.data.authorId);
    }

    // Comments collection - users can read all, write their own
    match /comments/{commentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.authorId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.authorId);
      allow delete: if isAuthenticated() && isOwner(resource.data.authorId);
    }

    // Likes collection - users can read all, write their own
    match /likes/{likeId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Views collection - users can read all, write their own
    match /views/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
    }

    // Sessions collection - users can read/write their own
    match /sessions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Threads collection - users can read all, write their own
    match /threads/{threadId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // ========================================
    // SEARCH & DISCOVERY COLLECTIONS
    // ========================================

    // Search history collection - users can read/write their own
    match /searchHistory/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Search suggestions collection - users can read all, write their own
    match /searchSuggestions/{suggestionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Search analytics collection - read-only for users
    match /searchAnalytics/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // User discovery collection - public read, authenticated write
    match /userDiscovery/{discoveryId} {
      allow read: if true; // Public for discovery
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // ========================================
    // CALLING SYSTEM EXTENDED
    // ========================================

    // Video calls collection - participants can access
    match /videoCalls/{callId} {
      allow read, write: if isAuthenticated() &&
                            (isOwner(resource.data.callerId) ||
                             isOwner(resource.data.receiverId));
      allow create: if isAuthenticated() &&
                       (isOwner(request.resource.data.callerId) ||
                        isOwner(request.resource.data.receiverId));
    }

    // Voice calls collection - participants can access
    match /voiceCalls/{callId} {
      allow read, write: if isAuthenticated() &&
                            (isOwner(resource.data.callerId) ||
                             isOwner(resource.data.receiverId));
      allow create: if isAuthenticated() &&
                       (isOwner(request.resource.data.callerId) ||
                        isOwner(request.resource.data.receiverId));
    }

    // Call history collection - users can read/write their own
    match /callHistory/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // WEBRTC SIGNALING COLLECTIONS
    // ========================================

    // WebRTC signaling data - participants can read/write
    match /signaling/{callId} {
      allow read, write: if isAuthenticated();
    }

    // ICE candidates for WebRTC - participants can read/write
    match /signaling/{callId}/candidates/{candidateId} {
      allow read, write: if isAuthenticated();
    }

    // WebRTC offers and answers - participants can read/write
    match /signaling/{callId}/offers/{offerId} {
      allow read, write: if isAuthenticated();
    }

    match /signaling/{callId}/answers/{answerId} {
      allow read, write: if isAuthenticated();
    }

    // Call participants collection - participants can read/write
    match /callParticipants/{callId} {
      allow read, write: if isAuthenticated();
    }

    // User call history subcollection - users can read/write their own
    match /users/{userId}/call_history/{callLogId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Device tokens for push notifications - users can read/write their own
    match /users/{userId}/tokens/{tokenId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // SOCIAL FEATURES EXTENDED
    // ========================================

    // Update downloads collection - users can read all, write their own
    match /updateDownloads/{downloadId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Update download history collection - users can read/write their own
    match /updateDownloadHistory/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Update download queue collection - users can read/write their own
    match /updateDownloadQueue/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Update download analytics collection - read-only for users
    match /updateDownloadAnalytics/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // ========================================
    // ANALYTICS & TRACKING COLLECTIONS
    // ========================================

    // Scroll tracking collection - users can read/write their own
    match /scrollTracking/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Navigation analytics collection - read-only for users
    match /navigationAnalytics/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // Page views collection - users can read/write their own
    match /pageViews/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Screen time tracking collection - users can read/write their own
    match /screenTime/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Scroll positions collection - users can read/write their own
    match /scrollPositions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Navigation history collection - users can read/write their own
    match /navigationHistory/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // NOTIFICATIONS & SYSTEM EXTENDED
    // ========================================

    // Push notifications collection - users can read/write their own
    match /pushNotifications/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Notification settings collection - users can read/write their own
    match /notificationSettings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Theme settings collection - users can read/write their own
    match /themeSettings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Privacy settings collection - users can read/write their own
    match /privacySettings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Account settings collection - users can read/write their own
    match /accountSettings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // DOWNLOAD FEATURES EXTENDED
    // ========================================

    // Media downloads collection - users can read/write their own
    match /mediaDownloads/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Document downloads collection - users can read/write their own
    match /documentDownloads/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Voice downloads collection - users can read/write their own
    match /voiceDownloads/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Download progress collection - users can read/write their own
    match /downloadProgress/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Download statistics collection - users can read/write their own
    match /downloadStats/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // ADDITIONAL APP FEATURES
    // ========================================

    // Bookmarks collection - users can read/write their own
    match /bookmarks/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Favorites collection - users can read/write their own
    match /favorites/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Recent activity collection - users can read/write their own
    match /recentActivity/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Activity logs collection - users can read/write their own
    match /activityLogs/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // MISSING COLLECTIONS IMPLEMENTATION
    // ========================================

    // Individual chats collection - participants can read/write
    match /individual_chats/{chatId} {
      allow read: if isChatParticipant(chatId);
      allow create: if isAuthenticated() &&
                       request.auth.uid in request.resource.data.participants &&
                       request.resource.data.participants.size() == 2 &&
                       isValidTimestamp(request.resource.data.createdAt) &&
                       isRecentTimestamp(request.resource.data.createdAt);
      allow update: if isChatParticipant(chatId) &&
                       request.resource.data.participants == resource.data.participants &&
                       request.resource.data.createdAt == resource.data.createdAt;
      allow delete: if false; // Individual chats cannot be deleted, only archived
    }

    // Shared media collection - chat participants can access
    match /shared_media/{mediaId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.uploadedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.uploadedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.uploadedBy);
    }

    // Message threads collection - chat participants can access
    match /message_threads/{threadId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Scheduled messages collection - users can read/write their own
    match /scheduled_messages/{messageId} {
      allow read, write: if isAuthenticated() && isOwner(resource.data.senderId);
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.senderId) &&
                       isValidUser();
    }

    // Chat settings collection - chat participants can access
    match /chat_settings/{chatId} {
      allow read: if isChatParticipant(chatId);
      allow write: if isChatParticipant(chatId);
    }

    // Message templates collection - users can read all, write their own
    match /message_templates/{templateId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // ========================================
    // DEVELOPMENT & TESTING (REMOVE IN PRODUCTION)
    // ========================================

    // Development collections - authenticated users can access (REMOVE IN PRODUCTION)
    match /dev/{document=**} {
      allow read, write: if isAuthenticated();
    }

    // ========================================
    // SECURITY FALLBACK - DENY ALL OTHER ACCESS
    // ========================================

    // Explicitly deny access to any collection not explicitly allowed above
    // This is a security best practice to prevent accidental data exposure
    match /{document=**} {
      allow read, write: if false;
    }

    // ========================================
    // ADDITIONAL MISSING COLLECTIONS
    // ========================================

    // Hashtags collection - users can read all, write their own
    match /hashtags/{hashtagId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Trending collection - read-only for users
    match /trending/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // Featured content collection - read-only for users
    match /featured/{document=**} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // Recommendations collection - users can read their own
    match /recommendations/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if false; // Only server can write
    }

    // User interests collection - users can read/write their own
    match /userInterests/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Content categories collection - users can read all
    match /contentCategories/{categoryId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // Tags collection - users can read all, write their own
    match /tags/{tagId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // ========================================
    // COMPREHENSIVE MESSAGING COLLECTIONS
    // ========================================

    // Message threads collection - users can read all, write their own
    match /messageThreads/{threadId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Message attachments collection - users can read all, write their own
    match /messageAttachments/{attachmentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.uploadedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.uploadedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.uploadedBy);
    }

    // Message mentions collection - users can read all, write their own
    match /messageMentions/{mentionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.mentionedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.mentionedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.mentionedBy);
    }

    // Message links collection - users can read all, write their own
    match /messageLinks/{linkId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.sharedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.sharedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.sharedBy);
    }

    // Message polls collection - users can read all, write their own
    match /messagePolls/{pollId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.createdBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.createdBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.createdBy);
    }

    // Poll votes collection - users can read all, write their own
    match /pollVotes/{voteId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.voterId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.voterId);
      allow delete: if isAuthenticated() && isOwner(resource.data.voterId);
    }

    // ========================================
    // ADVANCED USER FEATURES
    // ========================================

    // User badges collection - users can read all, admins can write
    match /userBadges/{badgeId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // User achievements collection - users can read all, system can write
    match /userAchievements/{achievementId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // User levels collection - users can read all, system can write
    match /userLevels/{levelId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only server can write
    }

    // User points collection - users can read their own, system can write
    match /userPoints/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if false; // Only server can write
    }

    // User streaks collection - users can read their own, system can write
    match /userStreaks/{userId} {
      allow read: if isAuthenticated() && isOwner(userId);
      allow write: if false; // Only server can write
    }

    // ========================================
    // CONTENT MODERATION EXTENDED
    // ========================================

    // Content flags collection - users can create, admins can read/write
    match /contentFlags/{flagId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.flaggedBy) &&
                       isValidUser();
      allow update: if false; // Only admins can update
      allow delete: if false; // Only admins can delete
    }

    // Content reviews collection - read-only for users
    match /contentReviews/{reviewId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only admins can write
    }

    // Banned content collection - read-only for users
    match /bannedContent/{contentId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only admins can write
    }

    // Content warnings collection - read-only for users
    match /contentWarnings/{warningId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only admins can write
    }

    // ========================================
    // MICRO-INTERACTIONS & UI STATE RULES
    // ========================================

    // Button clicks collection - users can read/write their own
    match /buttonClicks/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Screen taps collection - users can read/write their own
    match /screenTaps/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Scroll positions collection - users can read/write their own
    match /scrollPositions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Keyboard events collection - users can read/write their own
    match /keyboardEvents/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Swipe gestures collection - users can read/write their own
    match /swipeGestures/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Long presses collection - users can read/write their own
    match /longPresses/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Double taps collection - users can read/write their own
    match /doubleTaps/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Pinch zoom collection - users can read/write their own
    match /pinchZoom/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // DETAILED MESSAGE INTERACTION RULES
    // ========================================

    // Message views collection - users can read all, write their own
    match /messageViews/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.viewerId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.viewerId);
      allow delete: if isAuthenticated() && isOwner(resource.data.viewerId);
    }

    // Message hovers collection - users can read all, write their own
    match /messageHovers/{hoverId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Message selections collection - users can read/write their own
    match /messageSelections/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Message copies collection - users can read/write their own
    match /messageCopies/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Message shares collection - users can read all, write their own
    match /messageShares/{shareId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.sharedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.sharedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.sharedBy);
    }

    // Message bookmarks collection - users can read/write their own
    match /messageBookmarks/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Message translations collection - users can read/write their own
    match /messageTranslations/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Message quotes collection - users can read all, write their own
    match /messageQuotes/{quoteId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.quotedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.quotedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.quotedBy);
    }

    // ========================================
    // EMOJI & REACTIONS DETAILED RULES
    // ========================================

    // Emoji usage collection - users can read/write their own
    match /emojiUsage/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Emoji picker collection - users can read/write their own
    match /emojiPicker/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Custom emojis collection - users can read all, write their own
    match /customEmojis/{emojiId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Reaction animations collection - users can read/write their own
    match /reactionAnimations/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Reaction combos collection - users can read all, write their own
    match /reactionCombos/{comboId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // ========================================
    // TYPING & INPUT DETAILED RULES
    // ========================================

    // Typing speed collection - users can read/write their own
    match /typingSpeed/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Typing patterns collection - users can read/write their own
    match /typingPatterns/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Autocorrections collection - users can read/write their own
    match /autocorrections/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Text predictions collection - users can read/write their own
    match /textPredictions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Voice to text collection - users can read/write their own
    match /voiceToText/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // MEDIA INTERACTIONS DETAILED RULES
    // ========================================

    // Image views collection - users can read all, write their own
    match /imageViews/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.viewerId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.viewerId);
      allow delete: if isAuthenticated() && isOwner(resource.data.viewerId);
    }

    // Video plays collection - users can read all, write their own
    match /videoPlays/{playId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.viewerId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.viewerId);
      allow delete: if isAuthenticated() && isOwner(resource.data.viewerId);
    }

    // Audio pauses collection - users can read/write their own
    match /audioPauses/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Media rotations collection - users can read/write their own
    match /mediaRotations/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Media filters collection - users can read/write their own
    match /mediaFilters/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Media crops collection - users can read/write their own
    match /mediaCrops/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // NAVIGATION & SCREEN TRACKING RULES
    // ========================================

    // Screen transitions collection - users can read/write their own
    match /screenTransitions/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Tab switches collection - users can read/write their own
    match /tabSwitches/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Back button presses collection - users can read/write their own
    match /backButtonPresses/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Menu opens collection - users can read/write their own
    match /menuOpens/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Modal opens collection - users can read/write their own
    match /modalOpens/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Tooltip views collection - users can read/write their own
    match /tooltipViews/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // CONTACT & SOCIAL INTERACTION RULES
    // ========================================

    // Contact searches collection - users can read/write their own
    match /contactSearches/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Contact adds collection - users can read/write their own
    match /contactAdds/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Contact blocks collection - users can read/write their own
    match /contactBlocks/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Contact unblocks collection - users can read/write their own
    match /contactUnblocks/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Profile edits collection - users can read/write their own
    match /profileEdits/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Status views collection - users can read all, write their own
    match /statusViews/{viewId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.viewerId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.viewerId);
      allow delete: if isAuthenticated() && isOwner(resource.data.viewerId);
    }

    // ========================================
    // CALL DETAILED TRACKING RULES
    // ========================================

    // Call attempts collection - users can read all, write their own
    match /callAttempts/{attemptId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.callerId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.callerId);
      allow delete: if isAuthenticated() && isOwner(resource.data.callerId);
    }

    // Call rejections collection - users can read all, write their own
    match /callRejections/{rejectionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.rejectedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.rejectedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.rejectedBy);
    }

    // Call mutes collection - users can read/write their own
    match /callMutes/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Call speaker toggles collection - users can read/write their own
    match /callSpeakerToggles/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Call video toggles collection - users can read/write their own
    match /callVideoToggles/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Call screen shares collection - users can read/write their own
    match /callScreenShares/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Call recordings collection - users can read/write their own
    match /callRecordings/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // GROUP DETAILED INTERACTION RULES
    // ========================================

    // Group joins collection - users can read all, write their own
    match /groupJoins/{joinId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Group leaves collection - users can read all, write their own
    match /groupLeaves/{leaveId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Group kicks collection - admins can read/write
    match /groupKicks/{kickId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.kickedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.kickedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.kickedBy);
    }

    // Group promotions collection - admins can read/write
    match /groupPromotions/{promotionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.promotedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.promotedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.promotedBy);
    }

    // Group demotions collection - admins can read/write
    match /groupDemotions/{demotionId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.demotedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.demotedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.demotedBy);
    }

    // Group info edits collection - admins can read/write
    match /groupInfoEdits/{editId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.editedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.editedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.editedBy);
    }

    // ========================================
    // UPDATE/STORY DETAILED TRACKING RULES
    // ========================================

    // Update uploads collection - users can read all, write their own
    match /updateUploads/{uploadId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.userId) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.userId);
      allow delete: if isAuthenticated() && isOwner(resource.data.userId);
    }

    // Update deletes collection - users can read all, write their own
    match /updateDeletes/{deleteId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.deletedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.deletedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.deletedBy);
    }

    // Update reports collection - users can read all, write their own
    match /updateReports/{reportId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() &&
                       isOwner(request.resource.data.reportedBy) &&
                       isValidUser();
      allow update: if isAuthenticated() && isOwner(resource.data.reportedBy);
      allow delete: if isAuthenticated() && isOwner(resource.data.reportedBy);
    }

    // ========================================
    // ADDITIONAL MICRO-FEATURE RULES
    // ========================================

    // Crash reports collection - users can read/write their own
    match /crashReports/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Network metrics collection - users can read/write their own
    match /networkMetrics/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // Banned users collection - read-only for users
    match /bannedUsers/{userId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only admins can write
    }

    // Content analysis collection - read-only for users
    match /contentAnalysis/{analysisId} {
      allow read: if isAuthenticated();
      allow write: if false; // Only system can write
    }

    // Sync status collection - users can read/write their own
    match /syncStatus/{userId} {
      allow read, write: if isAuthenticated() && isOwner(userId);
    }

    // ========================================
    // STORIES COLLECTION - TEMPORARY FIX
    // ========================================
    match /stories/{storyId} {
      // Allow authenticated users to read all stories
      allow read: if isAuthenticated();

      // Allow users to create/update their own stories
      allow create, update: if isAuthenticated() &&
                               isOwner(request.resource.data.userId);

      // Allow users to delete their own stories
      allow delete: if isAuthenticated() &&
                       isOwner(resource.data.userId);
    }

    // ========================================
    // UPDATES COLLECTION - TEMPORARY FIX
    // ========================================
    match /updates/{updateId} {
      // Allow authenticated users to read all updates
      allow read: if isAuthenticated();

      // Allow users to create/update their own updates
      allow create, update: if isAuthenticated() &&
                               isOwner(request.resource.data.userId);

      // Allow users to delete their own updates
      allow delete: if isAuthenticated() &&
                       isOwner(resource.data.userId);
    }

    // ========================================
    // SECURE FALLBACK RULES - PRODUCTION READY
    // ========================================

    // Deny all access to undefined collections for security
    // All collections must have explicit rules defined above
    match /{document=**} {
      allow read, write: if false; // Secure by default - no fallback access
    }
  }
}

// Storage rules for Firebase Storage
// Note: This should be in a separate storage.rules file
/*
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload to their own folders
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Profile pictures - readable by all authenticated users
    match /profiles/{userId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Updates media - readable by all authenticated users
    match /updates/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null;
    }
    
    // Chat media - only accessible by chat participants
    match /chats/{chatId}/{allPaths=**} {
      allow read, write: if request.auth != null;
      // Note: More complex rules would check chat membership
    }
    
    // Default deny
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
*/
