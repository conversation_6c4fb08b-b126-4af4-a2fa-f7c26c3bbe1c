/**
 * Contact Cache Service
 * Manages local caching of IraChat contacts to prevent them from disappearing
 * and reduce unnecessary Firestore queries
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { EnhancedContact } from './enhancedContactsService';

interface CachedContactData {
  contact: EnhancedContact;
  lastUpdated: number;
  isIraChatUser: boolean;
  firestoreId?: string;
}

interface ContactCache {
  contacts: Record<string, CachedContactData>; // Key: phone number or email
  lastFullSync: number;
  deviceContactsHash: string; // Hash of device contacts to detect changes
  version: number;
}

interface ContactChangeDetection {
  newContacts: EnhancedContact[];
  removedContactIds: string[];
  modifiedContacts: EnhancedContact[];
}

class ContactCacheService {
  private readonly CACHE_KEY = '@irachat_contact_cache';
  private readonly CACHE_VERSION = 1;
  private readonly CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours
  private readonly SYNC_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours
  
  private cache: ContactCache | null = null;
  private isInitialized = false;

  /**
   * Initialize the cache service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadCache();
      this.isInitialized = true;
      console.log('📦 Contact cache service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize contact cache:', error);
      await this.createEmptyCache();
      this.isInitialized = true;
    }
  }

  /**
   * Load cache from AsyncStorage
   */
  private async loadCache(): Promise<void> {
    try {
      const cacheData = await AsyncStorage.getItem(this.CACHE_KEY);
      
      if (cacheData) {
        const parsedCache = JSON.parse(cacheData) as ContactCache;
        
        // Check cache version compatibility
        if (parsedCache.version !== this.CACHE_VERSION) {
          console.log('📦 Cache version mismatch, creating new cache');
          await this.createEmptyCache();
          return;
        }
        
        this.cache = parsedCache;
        console.log(`📦 Loaded ${Object.keys(parsedCache.contacts).length} cached contacts`);
      } else {
        await this.createEmptyCache();
      }
    } catch (error) {
      console.error('❌ Error loading contact cache:', error);
      await this.createEmptyCache();
    }
  }

  /**
   * Create empty cache structure
   */
  private async createEmptyCache(): Promise<void> {
    this.cache = {
      contacts: {},
      lastFullSync: 0,
      deviceContactsHash: '',
      version: this.CACHE_VERSION,
    };
    await this.saveCache();
  }

  /**
   * Save cache to AsyncStorage
   */
  private async saveCache(): Promise<void> {
    if (!this.cache) return;

    try {
      await AsyncStorage.setItem(this.CACHE_KEY, JSON.stringify(this.cache));
      console.log(`📦 Saved ${Object.keys(this.cache.contacts).length} contacts to cache`);
    } catch (error) {
      console.error('❌ Error saving contact cache:', error);
    }
  }

  /**
   * Generate hash for device contacts to detect changes
   */
  private generateContactsHash(contacts: EnhancedContact[]): string {
    const contactData = contacts
      .filter(c => c.source === 'device')
      .map(c => `${c.id}:${c.name}:${c.phoneNumber || ''}:${c.primaryEmail || ''}`)
      .sort()
      .join('|');
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < contactData.length; i++) {
      const char = contactData.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  /**
   * Detect changes in device contacts
   */
  async detectContactChanges(currentDeviceContacts: EnhancedContact[]): Promise<ContactChangeDetection> {
    await this.initialize();
    
    if (!this.cache) {
      return {
        newContacts: currentDeviceContacts,
        removedContactIds: [],
        modifiedContacts: [],
      };
    }

    const currentHash = this.generateContactsHash(currentDeviceContacts);
    const previousHash = this.cache.deviceContactsHash;

    // If hash is the same, no changes detected
    if (currentHash === previousHash) {
      console.log('📦 No device contact changes detected');
      return {
        newContacts: [],
        removedContactIds: [],
        modifiedContacts: [],
      };
    }

    console.log('📦 Device contact changes detected, analyzing...');

    // Get previously cached device contacts
    const cachedDeviceContacts = Object.values(this.cache.contacts)
      .filter(cached => cached.contact.source === 'device')
      .map(cached => cached.contact);

    // Find new contacts
    const cachedContactIds = new Set(cachedDeviceContacts.map(c => c.id));
    const newContacts = currentDeviceContacts.filter(c => !cachedContactIds.has(c.id));

    // Find removed contacts
    const currentContactIds = new Set(currentDeviceContacts.map(c => c.id));
    const removedContactIds = cachedDeviceContacts
      .filter(c => !currentContactIds.has(c.id))
      .map(c => c.id);

    // Find modified contacts (same ID but different data)
    const modifiedContacts = currentDeviceContacts.filter(current => {
      const cached = cachedDeviceContacts.find(c => c.id === current.id);
      if (!cached) return false;
      
      return (
        cached.name !== current.name ||
        cached.phoneNumber !== current.phoneNumber ||
        cached.primaryEmail !== current.primaryEmail
      );
    });

    console.log(`📦 Contact changes: ${newContacts.length} new, ${removedContactIds.length} removed, ${modifiedContacts.length} modified`);

    return {
      newContacts,
      removedContactIds,
      modifiedContacts,
    };
  }

  /**
   * Cache IraChat contact data
   */
  async cacheIraChatContact(contact: EnhancedContact, firestoreId?: string): Promise<void> {
    await this.initialize();
    if (!this.cache) return;

    const key = this.getContactKey(contact);
    if (!key) return;

    this.cache.contacts[key] = {
      contact: {
        ...contact,
        isIraChatUser: true,
        source: contact.source || 'firestore',
      },
      lastUpdated: Date.now(),
      isIraChatUser: true,
      firestoreId,
    };

    await this.saveCache();
    console.log(`📦 Cached IraChat contact: ${contact.name}`);
  }

  /**
   * Cache non-IraChat contact
   */
  async cacheNonIraChatContact(contact: EnhancedContact): Promise<void> {
    await this.initialize();
    if (!this.cache) return;

    const key = this.getContactKey(contact);
    if (!key) return;

    this.cache.contacts[key] = {
      contact: {
        ...contact,
        isIraChatUser: false,
      },
      lastUpdated: Date.now(),
      isIraChatUser: false,
    };

    await this.saveCache();
    console.log(`📦 Cached non-IraChat contact: ${contact.name}`);
  }

  /**
   * Get cached contact by phone or email
   */
  async getCachedContact(phoneNumber?: string, email?: string): Promise<CachedContactData | null> {
    await this.initialize();
    if (!this.cache) return null;

    if (phoneNumber) {
      const normalizedPhone = this.normalizePhoneNumber(phoneNumber);
      const cached = this.cache.contacts[normalizedPhone];
      if (cached && !this.isCacheExpired(cached)) {
        return cached;
      }
    }

    if (email) {
      const normalizedEmail = email.toLowerCase().trim();
      const cached = this.cache.contacts[normalizedEmail];
      if (cached && !this.isCacheExpired(cached)) {
        return cached;
      }
    }

    return null;
  }

  /**
   * Get all cached IraChat contacts
   */
  async getCachedIraChatContacts(): Promise<EnhancedContact[]> {
    await this.initialize();
    if (!this.cache) return [];

    return Object.values(this.cache.contacts)
      .filter(cached => cached.isIraChatUser && !this.isCacheExpired(cached))
      .map(cached => cached.contact);
  }

  /**
   * Update device contacts hash
   */
  async updateDeviceContactsHash(deviceContacts: EnhancedContact[]): Promise<void> {
    await this.initialize();
    if (!this.cache) return;

    this.cache.deviceContactsHash = this.generateContactsHash(deviceContacts);
    await this.saveCache();
  }

  /**
   * Update last full sync timestamp
   */
  async updateLastFullSync(): Promise<void> {
    await this.initialize();
    if (!this.cache) return;

    this.cache.lastFullSync = Date.now();
    await this.saveCache();
  }

  /**
   * Check if full sync is needed
   */
  async needsFullSync(): Promise<boolean> {
    await this.initialize();
    if (!this.cache) return true;

    const timeSinceLastSync = Date.now() - this.cache.lastFullSync;
    return timeSinceLastSync > this.SYNC_INTERVAL;
  }

  /**
   * Clear expired cache entries
   */
  async clearExpiredEntries(): Promise<void> {
    await this.initialize();
    if (!this.cache) return;

    const beforeCount = Object.keys(this.cache.contacts).length;
    
    for (const [key, cached] of Object.entries(this.cache.contacts)) {
      if (this.isCacheExpired(cached)) {
        delete this.cache.contacts[key];
      }
    }

    const afterCount = Object.keys(this.cache.contacts).length;
    
    if (beforeCount !== afterCount) {
      await this.saveCache();
      console.log(`📦 Cleared ${beforeCount - afterCount} expired cache entries`);
    }
  }

  /**
   * Clear all cache
   */
  async clearCache(): Promise<void> {
    await AsyncStorage.removeItem(this.CACHE_KEY);
    await this.createEmptyCache();
    console.log('📦 Contact cache cleared');
  }

  /**
   * Get contact key for caching (phone number or email)
   */
  private getContactKey(contact: EnhancedContact): string | null {
    if (contact.phoneNumber) {
      return this.normalizePhoneNumber(contact.phoneNumber);
    }
    if (contact.primaryEmail) {
      return contact.primaryEmail.toLowerCase().trim();
    }
    return null;
  }

  /**
   * Normalize phone number for consistent caching
   */
  private normalizePhoneNumber(phoneNumber: string): string {
    return phoneNumber.replace(/[^\d+]/g, '');
  }

  /**
   * Check if cache entry is expired
   */
  private isCacheExpired(cached: CachedContactData): boolean {
    return (Date.now() - cached.lastUpdated) > this.CACHE_EXPIRY;
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    totalContacts: number;
    iraChatContacts: number;
    expiredContacts: number;
    lastFullSync: Date | null;
  }> {
    await this.initialize();
    if (!this.cache) {
      return {
        totalContacts: 0,
        iraChatContacts: 0,
        expiredContacts: 0,
        lastFullSync: null,
      };
    }

    const contacts = Object.values(this.cache.contacts);
    const expiredCount = contacts.filter(c => this.isCacheExpired(c)).length;
    const iraChatCount = contacts.filter(c => c.isIraChatUser).length;

    return {
      totalContacts: contacts.length,
      iraChatContacts: iraChatCount,
      expiredContacts: expiredCount,
      lastFullSync: this.cache.lastFullSync ? new Date(this.cache.lastFullSync) : null,
    };
  }
}

// Create and export service instance
export const contactCacheService = new ContactCacheService();
export default contactCacheService;
