import React, { useState, useEffect, useRef, useCallback } from "react";
import { useRouter } from "expo-router";
import {
  Alert,
  Image,
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
  ScrollView,
  Animated,
  // Easing, // Removed - no animations
  RefreshControl,
  StatusBar,
  Pressable,
  Dimensions,

} from "react-native";

import { LinearGradient } from 'expo-linear-gradient';
// import * as Haptics from 'expo-haptics'; // Removed vibrations
import { useSelector } from "react-redux";
import { Ionicons } from "@expo/vector-icons";

import { RootState } from "../../src/redux/store";
import { navigationService, ROUTES } from "../../src/services/navigationService";
import { generatePlaceholderAvatar } from "../../src/utils/avatarUtils";
// import PrivacySettings from "../../src/components/privacy/PrivacySettings";
// import { usePrivacyLock } from "../../src/contexts/PrivacyLockContext";

import { ResponsiveContainer } from "../../src/components/ui/ResponsiveContainer";
import { IRACHAT_COLORS, TYPOGRAPHY, BORDER_RADIUS, SHADOWS } from "../../src/styles/iraChatDesignSystem";
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../../src/utils/responsiveUtils";
import { businessService } from "../../src/services/businessService";
import { BusinessProfile } from "../../src/types/Business";
import { BusinessProfilePage } from "../../src/components/business/BusinessProfilePage";
// import {
//   fadeIn,
//   createStaggerAnimation,
//   createSpringAnimation,
//   createTimingAnimation
// } from "../../src/utils/animations"; // Removed - no animations

// Screen dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Universal Types for Profile Data
interface ChatItem {
  id: string;
  name: string;
  avatar?: string;
  lastMessage: string;
  timestamp: string;
  unreadCount?: number;
  isOnline?: boolean;
  messageType?: 'text' | 'image' | 'video' | 'audio' | 'file';
  isGroup?: boolean;
}

interface MediaItem {
  id: string;
  uri?: string;
  name?: string;
  type: 'image' | 'video' | 'pdf' | 'doc' | 'excel' | 'ppt' | 'file';
  timestamp: string;
  size?: string;
  duration?: string;
}

interface ProfileData {
  chats: ChatItem[];
  media: {
    photos: MediaItem[];
    videos: MediaItem[];
    files: MediaItem[];
  };
}

// Shimmer Skeleton Component
const SkeletonLoader: React.FC<{
  width?: number | string;
  height?: number;
  borderRadius?: number;
  style?: any;
}> = ({ width = '100%', height = 20, borderRadius = 4, style }) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    shimmerAnimation.start();
    return () => shimmerAnimation.stop();
  }, [shimmerAnim]);

  return (
    <View style={[{ width, height, borderRadius, overflow: 'hidden', backgroundColor: '#2A2A2A' }, style]}>
      <Animated.View
        style={[
          {
            width: '100%',
            height: '100%',
            backgroundColor: '#3A3A3A',
            opacity: shimmerAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.3, 0.7],
            }),
          },
        ]}
      />
    </View>
  );
};

// Chat Item Skeleton
const ChatItemSkeleton: React.FC = () => (
  <View style={styles.chatItem}>
    <View style={styles.chatItemContent}>
      <View style={styles.chatAvatar}>
        <SkeletonLoader width={50} height={50} borderRadius={25} />
      </View>
      <View style={styles.chatInfo}>
        <View style={styles.chatHeader}>
          <SkeletonLoader width="60%" height={16} style={{ marginBottom: 4 }} />
          <SkeletonLoader width="30%" height={12} />
        </View>
        <View style={styles.chatMessageRow}>
          <SkeletonLoader width="80%" height={14} style={{ marginTop: 4 }} />
        </View>
      </View>
    </View>
  </View>
);

// Media Item Skeleton
const MediaItemSkeleton: React.FC = () => (
  <View style={styles.photoItem}>
    <SkeletonLoader width={100} height={100} borderRadius={8} />
  </View>
);

// Settings Item Skeleton
const SettingsItemSkeleton: React.FC = () => (
  <View style={styles.settingItem}>
    <View style={styles.settingButton}>
      <View style={styles.settingIcon}>
        <SkeletonLoader width={24} height={24} borderRadius={12} />
      </View>
      <View style={styles.settingContent}>
        <SkeletonLoader width="70%" height={16} style={{ marginBottom: 4 }} />
        <SkeletonLoader width="90%" height={12} />
      </View>
    </View>
  </View>
);

// Proper Error Boundary Class Component
class MediaTabErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Media tab crashed:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={{ padding: 20, alignItems: 'center' }}>
          <Ionicons name="warning" size={48} color="#FF6B6B" />
          <Text style={{ marginTop: 10, textAlign: 'center', color: '#666' }}>
            Media content failed to load
          </Text>
          <TouchableOpacity
            onPress={() => this.setState({ hasError: false })}
            style={{ marginTop: 10, padding: 10, backgroundColor: '#1DA1F2', borderRadius: 5 }}
          >
            <Text style={{ color: 'white' }}>Try Again</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return this.props.children;
  }
}

// Universal utility functions
const getDefaultAvatar = (name: string): string => {
  const colors = ['87CEEB', 'FFD700', '98FB98', 'FFA07A', 'DDA0DD', 'F0E68C', 'FFB6C1', '87CEFA'];
  const colorIndex = name.length % colors.length;
  return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=${colors[colorIndex]}&color=fff`;
};

const getFileIcon = (type: string): string => {
  switch (type) {
    case 'pdf': return 'document-text';
    case 'doc': return 'document';
    case 'excel': return 'grid';
    case 'ppt': return 'easel';
    case 'image': return 'image';
    case 'video': return 'videocam';
    case 'audio': return 'musical-notes';
    default: return 'document';
  }
};

const getMessageTypeIcon = (type: string): string => {
  switch (type) {
    case 'image': return '📷 ';
    case 'video': return '🎥 ';
    case 'audio': return '🎵 ';
    case 'file': return '📄 ';
    default: return '';
  }
};



// Safe Mock data for demonstration (TEMPORARY - will be replaced with real data)
// UNIVERSAL REAL DATA LOADER - Loads actual chats with real messages
const loadRealProfileData = async (): Promise<ProfileData> => {
  try {
    console.log('🔄 Loading REAL profile data from database...');

    // Load real chats from offline database
    let realChats: ChatItem[] = [];

    try {
      const { iraChatOfflineEngine } = await import('../../src/services/iraChatOfflineEngine');
      await iraChatOfflineEngine.initialize();

      // Get all chats that have messages
      const allChats = await iraChatOfflineEngine.getChats();
      console.log(`📱 Found ${allChats.length} chats in database`);

      // Convert to ChatItem format with REAL last messages
      for (const chat of allChats) {
        const messages = await iraChatOfflineEngine.getMessages(chat.id, 1, 0); // Get last message
        const lastMessage = messages[0];

        if (lastMessage) {
          realChats.push({
            id: chat.id,
            name: chat.name || `Chat ${chat.id}`,
            avatar: chat.avatar || '',
            lastMessage: lastMessage.text || 'No message content',
            timestamp: formatTimestamp(lastMessage.timestamp),
            unreadCount: chat.unreadCount || 0,
            isOnline: false, // Default to offline since isOnline is not available in Chat type
            messageType: lastMessage.type as any,
            isGroup: chat.isGroup || false
          });
        }
      }

      console.log(`✅ Loaded ${realChats.length} chats with real messages`);
    } catch (error) {
      console.warn('⚠️ Could not load real chats, using fallback:', error);
    }

    // If no real chats found, create demo chats with actual stored messages
    if (realChats.length === 0) {
      console.log('📱 No real chats found, creating demo chats with stored messages...');

      try {
        const { iraChatOfflineEngine } = await import('../../src/services/iraChatOfflineEngine');
        await iraChatOfflineEngine.initialize();

        // Create demo chats and store their messages in the database
        const demoChats = [
          { id: 'demo_sarah', name: 'Sarah Johnson', isGroup: false, isOnline: true },
          { id: 'demo_tech_team', name: 'Tech Team', isGroup: true, isOnline: false }
        ];

        for (const chat of demoChats) {
          // Store demo messages in database so they appear in chat rooms
          const demoMessages = [
            chat.isGroup ? 'Welcome to the team chat!' : 'Hey! How are you doing today?',
            chat.isGroup ? 'Meeting at 3 PM today' : 'I\'m doing great! Just finished a project.'
          ];

          for (const msgText of demoMessages) {
            await iraChatOfflineEngine.sendMessage(
              chat.id,
              msgText,
              chat.isGroup ? 'team_member' : chat.id,
              'text'
            );
          }

          // Get the last message we just stored
          const storedMessages = await iraChatOfflineEngine.getMessages(chat.id, 1, 0);
          const lastStoredMessage = storedMessages[0];

          realChats.push({
            id: chat.id,
            name: chat.name,
            avatar: generatePlaceholderAvatar(chat.name, 100),
            lastMessage: lastStoredMessage?.text || 'No messages',
            timestamp: formatTimestamp(lastStoredMessage?.timestamp || new Date()),
            unreadCount: 0,
            isOnline: chat.isOnline,
            messageType: 'text',
            isGroup: chat.isGroup
          });
        }

        console.log('✅ Created demo chats with stored messages');
      } catch (error) {
        console.error('❌ Could not create demo chats:', error);
      }
    }

    return {
      chats: realChats,
      media: {
        photos: [],
        videos: [],
        files: []
      }
    };
  } catch (error) {
    console.error('❌ Error loading real profile data:', error);
    // Fallback to empty data
    return {
      chats: [],
      media: { photos: [], videos: [], files: [] }
    };
  }
};

// Helper function to format timestamps
const formatTimestamp = (timestamp: Date): string => {
  const now = new Date();
  const diff = now.getTime() - timestamp.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes} min ago`;
  if (hours < 24) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  if (days < 7) return `${days} day${days > 1 ? 's' : ''} ago`;
  return timestamp.toLocaleDateString();
};

// Profile tabs configuration
const profileTabs = [
  {
    label: "Chats",
    value: "chats",
    icon: require("../../assets/images/comment.png"),
  },
  {
    label: "Business",
    value: "business",
    icon: require("../../assets/images/posts.png"),
  },
  {
    label: "Media",
    value: "media",
    icon: require("../../assets/images/posts.png"),
  },
];

export default function ProfileScreen() {
  console.log('📱 ProfileScreen: Component rendering...');
  const router = useRouter();

  // Get current user from Redux store
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const isAuthenticated = useSelector(
    (state: RootState) => state.user.isAuthenticated,
  );

  // Debug log to see what's in currentUser
  console.log("🔍 Profile: currentUser data:", currentUser);

  // State management
  const [activeTab, setActiveTab] = useState("chats");
  const [refreshing, setRefreshing] = useState(false);
  const [showStickyHeader, setShowStickyHeader] = useState(false);
  const [currentSection, setCurrentSection] = useState("profile");

  // Smart scroll restoration
  const [scrollPositions, setScrollPositions] = useState<{[key: string]: number}>({
    chats: 0,
    media: 0,
    settings: 0,
    profile: 0
  });

  // Universal profile data state
  const [profileData, setProfileData] = useState<ProfileData>({ chats: [], media: { photos: [], videos: [], files: [] } });
  const [isLoadingMedia, setIsLoadingMedia] = useState(false);
  const [isLoadingChats, setIsLoadingChats] = useState(true);

  // Business profiles state
  const [userBusinessProfiles, setUserBusinessProfiles] = useState<BusinessProfile[]>([]);
  const [isLoadingBusiness, setIsLoadingBusiness] = useState(true);
  const [selectedBusinessProfile, setSelectedBusinessProfile] = useState<BusinessProfile | null>(null);
  const [showBusinessProfile, setShowBusinessProfile] = useState(false);

  // Privacy lock state (temporarily disabled)
  console.log('📱 ProfileScreen: Privacy lock temporarily disabled');

  // Load fresh profile data on mount and when returning to page
  useEffect(() => {
    const loadFreshProfileData = async () => {
      if (!currentUser?.id) return;

      try {
        console.log('🔄 Loading real profile data...');
        // Import the services dynamically to avoid circular dependencies
        const { realUserService } = await import('../../src/services/realUserService');
        const { realSettingsService } = await import('../../src/services/realSettingsService');

        // Load fresh user data from database
        console.log('🔄 Loading REAL profile data from database...');
        const userData = await realUserService.getUserById(currentUser.id);
        const userSettings = await realSettingsService.getUserSettings(currentUser.id);

        if (userData) {
          // Update Redux store with fresh data
          const { updateUser } = await import('../../src/redux/userSlice');
          const { store } = await import('../../src/redux/store');

          const updatedUserData = {
            ...currentUser,
            ...userData,
            settings: userSettings || currentUser.settings,
          };

          store.dispatch(updateUser(updatedUserData as any));
          console.log('✅ Profile data refreshed from database');
        }
      } catch (error) {
        console.error('❌ Error loading fresh profile data:', error);
      }
    };

    loadFreshProfileData();
  }, [currentUser?.id]);

  // Profile overlay menu state
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const profileMenuOpacity = useRef(new Animated.Value(0)).current;
  const profileMenuScale = useRef(new Animated.Value(0.8)).current;
  const profileMenuTranslateY = useRef(new Animated.Value(-20)).current;

  // Load user business profiles
  const loadUserBusinessProfiles = useCallback(async () => {
    if (!currentUser?.id) {
      console.log('❌ No current user ID for loading business profiles');
      return;
    }

    try {
      setIsLoadingBusiness(true);
      console.log('🏢 Loading user business profiles for user:', currentUser.id);
      const result = await businessService.getUserBusinessProfiles(currentUser.id);
      console.log('🏢 Business profiles result:', result);

      if (result.success && result.data) {
        setUserBusinessProfiles(result.data);
        console.log(`✅ Loaded ${result.data.length} business profiles:`, result.data.map(b => b.businessName));
      } else {
        console.log('❌ Failed to load business profiles:', result.error);
        setUserBusinessProfiles([]);
      }
    } catch (error) {
      console.error('❌ Error loading business profiles:', error);
      setUserBusinessProfiles([]);
    } finally {
      setIsLoadingBusiness(false);
    }
  }, [currentUser?.id]);

  // Load real profile data on component mount
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        setIsLoadingChats(true);
        console.log('🔄 Loading real profile data...');
        const realData = await loadRealProfileData();
        setProfileData(realData);
        console.log(`✅ Loaded ${realData.chats.length} real chats`);
      } catch (error) {
        console.error('❌ Error loading profile data:', error);
      } finally {
        setIsLoadingChats(false);
      }
    };

    loadProfileData();
    loadUserBusinessProfiles(); // Also load business profiles
  }, [loadUserBusinessProfiles]);

  // Profile photo zoom state
  const [isProfilePhotoZoomed, setIsProfilePhotoZoomed] = useState(false);
  const zoomScale = useRef(new Animated.Value(1)).current;
  const zoomOpacity = useRef(new Animated.Value(0)).current;

  // Simple scroll handling
  const scrollY = useRef(new Animated.Value(0)).current;

  // Add breathing animation to indicate interactivity
  useEffect(() => {
    const breathingAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(avatarScale, {
          toValue: 1.05,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(avatarScale, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    );

    breathingAnimation.start();

    return () => breathingAnimation.stop();
  }, []);

  // Profile photo zoom toggle function
  const toggleProfilePhotoZoom = () => {
    const toValue = isProfilePhotoZoomed ? 0 : 1;

    // Haptic feedback for photo zoom - removed

    setIsProfilePhotoZoomed(!isProfilePhotoZoomed);

    // Animate zoom overlay
    Animated.parallel([
      Animated.spring(zoomOpacity, {
        toValue,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
      Animated.spring(zoomScale, {
        toValue: toValue === 1 ? 1.2 : 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }),
    ]).start();
  };

  // Section layout tracking
  const sectionLayouts = useRef({
    profile: { y: 0, height: 0 },
    tabs: { y: 0, height: 0 },
    content: { y: 0, height: 0 }
  }).current;

  // Enhanced scroll handler with sticky header logic
  const handleScroll = Animated.event(
    [{ nativeEvent: { contentOffset: { y: scrollY } } }],
    {
      useNativeDriver: false,
      listener: (event: any) => {
        const offsetY = event.nativeEvent?.contentOffset?.y || 0;
        const contentHeight = event.nativeEvent?.contentSize?.height || 0;

        // Close profile menu on scroll - INSTANT
        if (showProfileMenu) {
          closeProfileMenuFast();
        }

        // Determine when to show sticky header (when profile header is mostly scrolled out)
        const profileHeaderHeight = 300; // Approximate height of profile header
        const shouldShowSticky = offsetY > profileHeaderHeight * 0.7;

        if (shouldShowSticky !== showStickyHeader) {
          setShowStickyHeader(shouldShowSticky);

          // Animate sticky header appearance - REMOVED
          // Instant state changes without animations
          stickyHeaderOpacity.setValue(shouldShowSticky ? 1 : 0);
          stickyHeaderTranslateY.setValue(shouldShowSticky ? 0 : -(DeviceInfo.statusBarHeight + 100));
          profileHeaderOpacity.setValue(shouldShowSticky ? 0.3 : 1);
          profileHeaderScale.setValue(shouldShowSticky ? 0.95 : 1);
        }

        // Determine current section based on scroll position
        let newSection = "profile";
        if (offsetY > sectionLayouts.tabs.y - 100) {
          newSection = "tabs";
        }
        if (offsetY > sectionLayouts.content.y - 100) {
          newSection = "content";
        }

        if (newSection !== currentSection) {
          setCurrentSection(newSection);
          // Haptic feedback for section changes - removed
        }

        // Save current scroll position for active tab
        setScrollPositions(prev => ({
          ...prev,
          [activeTab]: offsetY,
          profile: offsetY
        }));

        // Provide haptic feedback at scroll boundaries - removed
      }
    }
  );

  // Scroll event handlers for better UX (will be defined after animation values)

  // Scroll to top function for better UX
  const scrollViewRef = useRef<ScrollView>(null);

  const scrollToTop = useCallback(() => {
    scrollViewRef.current?.scrollTo({
      y: 0,
      animated: true,
    });
  }, []);

  // Scroll event handlers for better UX with performance optimization
  const handleScrollBeginDrag = useCallback(() => {
    // Scroll started
  }, []);

  const handleScrollEndDrag = useCallback(() => {
    // Scroll ended
  }, []);

  const handleMomentumScrollBegin = useCallback(() => {
    // Momentum scroll started
  }, []);

  const handleMomentumScrollEnd = useCallback(() => {
    // Momentum scroll ended
  }, []);

  // Universal function to load user's actual profile data
  const loadUserProfileData = async (userId: string): Promise<ProfileData> => {
    try {
      // TODO: Replace with actual API calls to get user's chats and media
      // This is where you would integrate with your chat service and media service

      // Example API calls (to be implemented):
      // const userChats = await chatService.getUserChats(userId);
      // const userMedia = await mediaService.getUserMedia(userId);

      // For now, return empty data but structure is ready for real data
      return { chats: [], media: { photos: [], videos: [], files: [] } };
    } catch (error) {
      console.error('Error loading profile data:', error);
      return { chats: [], media: { photos: [], videos: [], files: [] } };
    }
  };

  // Universal function to handle chat navigation
  const handleChatPress = (chat: ChatItem) => {
    try {
      // Add haptic feedback for better UX - removed

      console.log(`🚀 Opening chat with: ${chat.name} (ID: ${chat.id}, Group: ${chat.isGroup})`);

      // Navigate to the appropriate chat room with user information
      if (chat.isGroup) {
        // UNIVERSAL GROUP CHAT NAVIGATION - Works for ANY group
        console.log(`📱 Opening group chat: ${chat.name}`);
        router.push({
          pathname: '/group-chat',
          params: {
            groupId: chat.id, // Use actual group ID (universal for all groups)
            groupName: chat.name,
            groupAvatar: chat.avatar || '',
          }
        });
      } else {
        // Navigate to individual chat with user details
        console.log(`💬 Opening individual chat with: ${chat.name}`);
        // UNIVERSAL INDIVIDUAL CHAT NAVIGATION - Works for ANY chat
        router.push({
          pathname: '/individual-chat',
          params: {
            contactId: chat.id,
            contactName: chat.name,
            contactAvatar: chat.avatar || '',
            contactIsOnline: chat.isOnline?.toString() || 'false',
            contactLastSeen: '',
            chatId: chat.id, // Use the actual chat ID (universal for all chats)
          }
        });
      }
    } catch (error) {
      console.error('❌ Error opening chat:', error);

      // Show user-friendly error message
      Alert.alert(
        "Chat Error",
        `Unable to open chat with ${chat.name}. Please try again.`,
        [{ text: "OK" }]
      );
    }
  };

  // Universal function to handle media press
  const handleMediaPress = (media: MediaItem) => {
    try {
      // Universal media handling - works for any media type
      switch (media.type) {
        case 'image':
          console.log(`Opening image: ${media.id}`);
          // navigationService.navigate(ROUTES.MEDIA.IMAGE_VIEWER, { mediaId: media.id });
          break;
        case 'video':
          console.log(`Playing video: ${media.id}`);
          // navigationService.navigate(ROUTES.MEDIA.VIDEO_PLAYER, { mediaId: media.id });
          break;
        default:
          console.log(`Opening file: ${media.name}`);
          // navigationService.navigate(ROUTES.MEDIA.FILE_VIEWER, { mediaId: media.id });
          break;
      }
    } catch (error) {
      console.error('Error handling media press:', error);
    }
  };

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const tabAnimations = useRef(profileTabs.map(() => new Animated.Value(0))).current;
  const avatarScale = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Sticky header animations
  const stickyHeaderOpacity = useRef(new Animated.Value(0)).current;
  const stickyHeaderTranslateY = useRef(new Animated.Value(-(DeviceInfo.statusBarHeight + 100))).current;
  const profileHeaderScale = useRef(new Animated.Value(1)).current;
  const profileHeaderOpacity = useRef(new Animated.Value(1)).current;

  // Tab transition animations
  const tabContentOpacity = useRef(new Animated.Value(1)).current;

  // Load user profile data
  useEffect(() => {
    const loadData = async () => {
      if (currentUser?.id) {
        try {
          // Simulate loading states
          setIsLoadingChats(true);
          setIsLoadingMedia(true);

          const data = await loadUserProfileData(currentUser.id);
          setProfileData(data);

          // Simulate different loading times for different sections
          setTimeout(() => setIsLoadingChats(false), 1000);
          setTimeout(() => setIsLoadingMedia(false), 1500);
        } catch (error) {
          console.error('Failed to load profile data:', error);
          setIsLoadingChats(false);
          setIsLoadingMedia(false);
        }
      }
    };

    loadData();
  }, [currentUser?.id]);

  // Smart scroll restoration on component mount
  useEffect(() => {
    const restoreScrollPosition = () => {
      const savedPosition = scrollPositions.profile || 0;
      if (savedPosition > 0) {
        // Delay restoration to allow content to render
        setTimeout(() => {
          scrollViewRef.current?.scrollTo({
            y: savedPosition,
            animated: false, // No animation on initial restore
          });
        }, 100);
      }
    };

    restoreScrollPosition();
  }, []);



  // Initialize animations on mount - REMOVED
  useEffect(() => {
    const initAnimations = () => {
      // Stagger entrance animations - REMOVED
      // Instant state setup without animations
      fadeAnim.setValue(1);
      slideAnim.setValue(0);
      scaleAnim.setValue(1);
      headerOpacity.setValue(1);

      // Stagger tab animations - REMOVED
      // Instant tab setup
      tabAnimations.forEach(anim => anim.setValue(1));

      // Start pulse animation for avatar - REMOVED
      // No pulse animation
    };

    initAnimations();
  }, []);

  // Pulse animation for avatar - REMOVED
  // No pulse animation

  // Handler functions

  const handleTabPress = (tabValue: string, index: number) => {
    try {
      // Close profile menu if open - INSTANT
      if (showProfileMenu) {
        closeProfileMenuFast();
      }

      // Haptic feedback for tab selection - removed

      // Simple fade transition without bouncing/scaling effects - REMOVED
      // No animations for instant tab switching
      tabContentOpacity.setValue(1);

      // Simple tab button animation without bouncing - REMOVED
      // No animations for instant tab switching
      tabAnimations[index].setValue(1);

      // Set active tab after a short delay for smooth transition
      setTimeout(() => {
        setActiveTab(tabValue);

        // Restore scroll position for the new tab after a brief delay
        setTimeout(() => {
          const savedPosition = scrollPositions[tabValue] || 0;
          if (savedPosition > 0) {
            scrollViewRef.current?.scrollTo({
              y: savedPosition,
              animated: true,
            });
          }
        }, 200);
      }, 120);

    } catch (error) {
      console.error('Error handling tab press:', error);
      // Fallback: just set the active tab without animation
      setActiveTab(tabValue);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);

    // Simulate refresh with animation - REMOVED
    // No refresh animation

    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  };





  // Profile menu functions
  const toggleProfileMenu = () => {
    const isOpening = !showProfileMenu;
    setShowProfileMenu(isOpening);

    // Add haptic feedback (non-blocking) - removed

    if (isOpening) {
      // INSTANT opening - no animation delay
      profileMenuOpacity.setValue(1);
      profileMenuScale.setValue(1);
      profileMenuTranslateY.setValue(0);
    } else {
      // ULTRA-FAST closing animation
      Animated.parallel([
        Animated.timing(profileMenuOpacity, {
          toValue: 0,
          duration: 50, // Super fast
          useNativeDriver: true,
        }),
        Animated.timing(profileMenuScale, {
          toValue: 0.95,
          duration: 50, // Super fast
          useNativeDriver: true,
        }),
        Animated.timing(profileMenuTranslateY, {
          toValue: -5,
          duration: 50, // Super fast
          useNativeDriver: true,
        }),
      ]).start();
    }
  };

  // Fast close function for outside taps
  const closeProfileMenuFast = () => {
    if (!showProfileMenu) return;

    setShowProfileMenu(false);
    // INSTANT close - no animation
    profileMenuOpacity.setValue(0);
    profileMenuScale.setValue(0.95);
    profileMenuTranslateY.setValue(-5);
  };

  const handleMenuItemPress = (action: () => void) => {
    // Instant haptic feedback (non-blocking) - removed
    // Close menu instantly and execute action immediately
    closeProfileMenuFast();
    action();
  };

  // Show loading or error state if user data is not available
  if (!isAuthenticated || !currentUser) {
    return (
      <ResponsiveContainer centered>
        <Animated.View style={[styles.loadingContainer, { opacity: fadeAnim }]}>
          <Animated.View style={[styles.loadingSpinner, { transform: [{ rotate: pulseAnim.interpolate({
            inputRange: [0, 1],
            outputRange: ['0deg', '360deg']
          }) }] }]}>
            <Ionicons name="person-circle-outline" size={60} color={IRACHAT_COLORS.primary} />
          </Animated.View>
          <Text style={styles.loadingText}>Loading profile...</Text>
          <Text style={styles.loadingSubtext}>
            Please wait while we load your information
          </Text>
        </Animated.View>
      </ResponsiveContainer>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={IRACHAT_COLORS.primary} />

      {/* Sticky Header */}
      <Animated.View
        style={[
          styles.stickyHeader,
          {
            opacity: stickyHeaderOpacity,
            transform: [{ translateY: stickyHeaderTranslateY }],
          }
        ]}
        pointerEvents={showStickyHeader ? 'auto' : 'none'}
      >
        <LinearGradient
          colors={IRACHAT_COLORS.primaryGradient as any}
          style={styles.stickyHeaderGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {/* Back Button */}
          <TouchableOpacity
            style={styles.stickyBackButton}
            onPress={() => navigationService.goBack()}
            activeOpacity={0.8}
            accessible={true}
            accessibilityRole="button"
            accessibilityLabel="Go back"
            accessibilityHint="Navigate back to previous screen"
          >
            <Ionicons name="arrow-back" size={20} color={IRACHAT_COLORS.textOnPrimary} />
          </TouchableOpacity>

          {/* Compact Profile Info */}
          <View style={styles.stickyProfileInfo}>
            <View style={styles.stickyAvatarContainer}>
              {currentUser?.avatar ? (
                <Image
                  source={{ uri: currentUser.avatar }}
                  style={styles.stickyAvatar}
                  resizeMode="cover"
                />
              ) : (
                <LinearGradient
                  colors={['#FFFFFF', '#F0F8FF']}
                  style={styles.stickyAvatarFallback}
                >
                  <Text style={styles.stickyAvatarText}>
                    {(currentUser?.name || "U").charAt(0).toUpperCase()}
                  </Text>
                </LinearGradient>
              )}
            </View>
            <View style={styles.stickyUserInfo}>
              <Text style={styles.stickyUserName} numberOfLines={1}>
                {currentUser?.name || "User"}
              </Text>
              <Text style={styles.stickyUserHandle} numberOfLines={1}>
                {currentUser?.username ?
                  (currentUser.username.startsWith("@") ? currentUser.username : `@${currentUser.username}`)
                  : currentUser?.phoneNumber || currentUser?.email}
              </Text>
            </View>
          </View>

          {/* Section Indicator */}
          <View style={styles.stickySectionIndicator}>
            <Text style={styles.stickySectionText}>
              {currentSection === "profile" ? "Profile" :
               currentSection === "tabs" ? "Navigation" :
               activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </Text>
          </View>
        </LinearGradient>
      </Animated.View>

      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        onScrollBeginDrag={handleScrollBeginDrag}
        onScrollEndDrag={handleScrollEndDrag}
        onMomentumScrollBegin={handleMomentumScrollBegin}
        onMomentumScrollEnd={handleMomentumScrollEnd}
        scrollEventThrottle={16}
        bounces={true}
        bouncesZoom={false}
        alwaysBounceVertical={true}
        alwaysBounceHorizontal={false}
        decelerationRate="normal"
        scrollToOverflowEnabled={true}
        nestedScrollEnabled={true}
        keyboardShouldPersistTaps="handled"
        removeClippedSubviews={true}
        // Accessibility improvements
        accessible={true}
        accessibilityRole="none"
        accessibilityLabel="Profile content"
        accessibilityHint="Scroll to view profile information, chats, media, and settings"
        // Performance optimizations
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10,
        }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[IRACHAT_COLORS.primary]}
            tintColor={IRACHAT_COLORS.primary}
            progressBackgroundColor={IRACHAT_COLORS.background}
            progressViewOffset={0}
            accessibilityLabel="Pull to refresh profile"
          />
        }
      >
        {/* Animated Profile Header */}
        <Animated.View
          style={[
            styles.headerContainer,
            {
              opacity: headerOpacity,
              transform: [{ translateY: slideAnim }]
            }
          ]}
          onLayout={(event) => {
            const { y, height } = event.nativeEvent.layout;
            sectionLayouts.profile = { y, height };
          }}
        >
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={scrollToTop}
            style={styles.headerTouchable}
          >
            <Animated.View
              style={[
                styles.profileHeaderWrapper,
                {
                  transform: [
                    { scale: profileHeaderScale },
                    {
                      translateY: scrollY.interpolate({
                        inputRange: [0, 200],
                        outputRange: [0, -50],
                        extrapolate: 'clamp',
                      })
                    }
                  ]
                }
              ]}
            >
              <LinearGradient
                colors={IRACHAT_COLORS.primaryGradient as any}
                style={styles.profileHeader}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
              >
            {/* Header Navigation */}
            <Animated.View style={[
              styles.headerNavigation,
              { opacity: headerOpacity }
            ]}>
              {/* Back Button */}
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => navigationService.goBack()}
                activeOpacity={0.8}
              >
                <Ionicons name="arrow-back" size={24} color={IRACHAT_COLORS.textOnPrimary} />
              </TouchableOpacity>

              {/* Profile Menu Button */}
              <TouchableOpacity
                style={styles.menuButton}
                onPress={toggleProfileMenu}
                activeOpacity={0.6}
              >
                <Ionicons name="ellipsis-vertical" size={24} color={IRACHAT_COLORS.textOnPrimary} />
              </TouchableOpacity>
            </Animated.View>

            {/* Background Pattern with Parallax */}
            <Animated.View
              style={[
                styles.backgroundPattern,
                {
                  transform: [{
                    translateY: scrollY.interpolate({
                      inputRange: [0, 400],
                      outputRange: [0, -200],
                      extrapolate: 'clamp',
                    })
                  }]
                }
              ]}
            >
              {[...Array(20)].map((_, i) => (
                <Animated.View
                  key={i}
                  style={[
                    styles.patternDot,
                    {
                      opacity: Animated.multiply(
                        pulseAnim.interpolate({
                          inputRange: [1, 1.05],
                          outputRange: [0.1, 0.3]
                        }),
                        scrollY.interpolate({
                          inputRange: [0, 300],
                          outputRange: [1, 0],
                          extrapolate: 'clamp',
                        })
                      ),
                      transform: [
                        {
                          scale: Animated.multiply(
                            pulseAnim,
                            scrollY.interpolate({
                              inputRange: [0, 200],
                              outputRange: [1, 1.5],
                              extrapolate: 'clamp',
                            })
                          )
                        },
                        { translateX: (i % 5) * 60 },
                        { translateY: Math.floor(i / 5) * 40 },
                        {
                          rotate: scrollY.interpolate({
                            inputRange: [0, 400],
                            outputRange: ['0deg', '180deg'],
                            extrapolate: 'clamp',
                          })
                        }
                      ]
                    }
                  ]}
                />
              ))}
            </Animated.View>

            {/* Profile Content */}
            <Animated.View style={[
              styles.profileContent,
              {
                transform: [
                  { scale: scaleAnim },
                  {
                    translateY: scrollY.interpolate({
                      inputRange: [0, 300],
                      outputRange: [0, -100],
                      extrapolate: 'clamp',
                    })
                  }
                ],
                opacity: profileHeaderOpacity
              }
            ]}>
              {/* Avatar Section */}
              <Pressable onPress={toggleProfilePhotoZoom}>
                <Animated.View style={[
                  styles.avatarContainer,
                  {
                    transform: [
                      {
                        scale: Animated.multiply(
                          Animated.multiply(avatarScale, pulseAnim),
                          scrollY.interpolate({
                            inputRange: [0, 200],
                            outputRange: [1, 0.8],
                            extrapolate: 'clamp',
                          })
                        )
                      }
                    ]
                  }
                ]}>
                  <View style={styles.avatarWrapper}>
                    <View style={styles.avatarBorder}>
                      {currentUser?.avatar ? (
                        <Image
                          source={{ uri: currentUser.avatar }}
                          style={styles.avatar}
                          resizeMode="cover"
                        />
                      ) : (
                        <LinearGradient
                          colors={['#FFFFFF', '#F0F8FF']}
                          style={styles.avatarFallback}
                        >
                          <Text style={styles.avatarText}>
                            {(currentUser?.name || "U").charAt(0).toUpperCase()}
                          </Text>
                        </LinearGradient>
                      )}
                    </View>

                    {/* Online Status Indicator */}
                    <Animated.View style={[
                      styles.onlineIndicator,
                      {
                        transform: [{ scale: pulseAnim }]
                      }
                    ]}>
                      <View style={styles.onlineDot} />
                    </Animated.View>

                    {/* Edit Icon */}
                    <Animated.View style={[
                      styles.editIcon,
                      {
                        transform: [{ scale: avatarScale }]
                      }
                    ]}>
                      <Ionicons name="expand-outline" size={16} color={IRACHAT_COLORS.textOnPrimary} />
                    </Animated.View>
                  </View>
                </Animated.View>
              </Pressable>



              {/* User Info */}
              <Animated.View style={[
                styles.userInfo,
                {
                  opacity: Animated.multiply(
                    fadeAnim,
                    scrollY.interpolate({
                      inputRange: [0, 150],
                      outputRange: [1, 0],
                      extrapolate: 'clamp',
                    })
                  ),
                  transform: [{
                    translateY: scrollY.interpolate({
                      inputRange: [0, 200],
                      outputRange: [0, -50],
                      extrapolate: 'clamp',
                    })
                  }]
                }
              ]}>
                <Text style={styles.userName}>
                  {currentUser?.name || "User"}
                </Text>

                {currentUser?.username && (
                  <Text style={styles.userHandle}>
                    {currentUser.username.startsWith("@")
                      ? currentUser.username
                      : `@${currentUser.username}`}
                  </Text>
                )}

                {currentUser?.phoneNumber && (
                  <Text style={styles.userPhone}>
                    {currentUser.phoneNumber}
                  </Text>
                )}

                {currentUser?.email && (
                  <Text style={styles.userPhone}>
                    {currentUser.email}
                  </Text>
                )}

                <Text style={styles.userStatus}>
                  {currentUser?.bio || "I Love IraChat ❤️"}
                </Text>
              </Animated.View>


            </Animated.View>
          </LinearGradient>
          </Animated.View>
          </TouchableOpacity>
        </Animated.View>

        {/* Profile Overlay Menu */}
        {showProfileMenu && (
          <Animated.View
            style={[
              styles.profileMenuOverlay,
              {
                opacity: profileMenuOpacity,
                transform: [
                  { scale: profileMenuScale },
                  { translateY: profileMenuTranslateY }
                ]
              }
            ]}
          >
            <TouchableOpacity
              style={styles.profileMenuBackdrop}
              onPress={closeProfileMenuFast}
              activeOpacity={1}
            />
            <View style={styles.profileMenuContainer}>
              {/* Minimal Menu Items */}
              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => handleMenuItemPress(() => navigationService.navigate(ROUTES.PROFILE.EDIT))}
                activeOpacity={0.5}
              >
                <Text style={styles.profileMenuItemText}>Edit</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.profileMenuItem}
                onPress={() => handleMenuItemPress(() => {
                  console.log('🔐 Profile: Privacy menu item pressed');
                  console.log('🔐 Profile: ROUTES.SETTINGS.PRIVACY =', ROUTES.SETTINGS.PRIVACY);

                  // Direct navigation test
                  const { router } = require('expo-router');
                  console.log('🔐 Profile: Attempting direct navigation to /privacy-settings');
                  router.push('/privacy-settings');
                })}
                activeOpacity={0.5}
              >
                <Text style={styles.profileMenuItemText}>Privacy</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        )}

        {/* Profile Tabs - Normal Position */}
        <Animated.View
          style={[
            styles.tabsContainer,
            { opacity: fadeAnim }
          ]}
          onLayout={(event) => {
            const { y, height } = event.nativeEvent.layout;
            sectionLayouts.tabs = { y, height };
          }}
        >
          <View style={styles.tabsWrapper}>
            {profileTabs.map((tab, index) => (
              <Animated.View
                key={tab.value}
                style={[
                  styles.tabItem,
                  {
                    opacity: tabAnimations[index],
                    transform: [
                      {
                        translateY: tabAnimations[index].interpolate({
                          inputRange: [0, 1],
                          outputRange: [20, 0]
                        })
                      }
                    ]
                  }
                ]}
              >
                <TouchableOpacity
                  onPress={() => handleTabPress(tab.value, index)}
                  style={[
                    styles.tabButton,
                    activeTab === tab.value && styles.tabButtonActive
                  ]}
                  activeOpacity={0.8}
                >
                  {activeTab === tab.value && (
                    <LinearGradient
                      colors={['#FFFFFF', '#F8FAFC']}
                      style={styles.tabButtonGradient}
                    />
                  )}

                  <Animated.View style={[
                    styles.tabContent,
                    {
                      transform: [
                        {
                          scale: activeTab === tab.value ? 1.05 : 1
                        }
                      ]
                    }
                  ]}>
                    <Image
                      source={tab.icon}
                      style={[
                        styles.tabIcon,
                        {
                          tintColor: activeTab === tab.value
                            ? IRACHAT_COLORS.primary
                            : IRACHAT_COLORS.textMuted
                        }
                      ]}
                      resizeMode="contain"
                    />
                    <Text style={[
                      styles.tabLabel,
                      {
                        color: activeTab === tab.value
                          ? IRACHAT_COLORS.primary
                          : IRACHAT_COLORS.textMuted
                      }
                    ]}>
                      {tab.label}
                    </Text>
                  </Animated.View>
                </TouchableOpacity>
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Animated Tab Content */}
        <Animated.View
          style={[
            styles.tabContentContainer,
            {
              opacity: Animated.multiply(fadeAnim, tabContentOpacity)
            }
          ]}
          onLayout={(event) => {
            const { y, height } = event.nativeEvent.layout;
            sectionLayouts.content = { y, height };
          }}
        >
          {activeTab === "chats" && (
            <Animated.View style={[
              styles.contentSection,
              {
                opacity: fadeAnim
              }
            ]}>
                <View style={styles.chatsList}>
                  {isLoadingChats ? (
                    // Show skeleton loading
                    [...Array(3)].map((_, index) => (
                      <ChatItemSkeleton key={`skeleton-${index}`} />
                    ))
                  ) : profileData.chats.length > 0 ? (
                    profileData.chats.map((chat: ChatItem) => (
                      <Animated.View
                        key={chat.id}
                        style={[
                          styles.chatItem,
                          {
                            opacity: Animated.multiply(fadeAnim, tabContentOpacity)
                          }
                        ]}
                      >
                        <TouchableOpacity
                          style={styles.chatItemContent}
                          activeOpacity={0.6}
                          onPress={() => handleChatPress(chat)}
                          accessible={true}
                          accessibilityRole="button"
                          accessibilityLabel={`Open chat with ${chat.name}`}
                          accessibilityHint={`Tap to view messages and reply to ${chat.isGroup ? 'group' : chat.name}`}
                        >
                          {/* Avatar with online indicator */}
                          <View style={styles.chatAvatar}>
                            <Image
                              source={{ uri: chat.avatar || getDefaultAvatar(chat.name) }}
                              style={styles.chatAvatarImage}
                            />
                            {chat.isOnline && (
                              <View style={styles.chatOnlineIndicator} />
                            )}
                            {chat.isGroup && (
                              <View style={styles.groupIndicator}>
                                <Ionicons name="people" size={10} color="#FFFFFF" />
                              </View>
                            )}
                          </View>

                          {/* Chat info */}
                          <View style={styles.chatInfo}>
                            <View style={styles.chatHeader}>
                              <Text style={styles.chatName} numberOfLines={1}>
                                {chat.name}
                              </Text>
                              <Text style={styles.chatTimestamp}>
                                {chat.timestamp}
                              </Text>
                            </View>

                            <View style={styles.chatMessageRow}>
                              <Text style={styles.chatLastMessage} numberOfLines={1}>
                                {getMessageTypeIcon(chat.messageType || 'text')}
                                {chat.lastMessage}
                              </Text>
                              {(chat.unreadCount || 0) > 0 && (
                                <View style={styles.unreadBadge}>
                                  <Text style={styles.unreadCount}>
                                    {chat.unreadCount}
                                  </Text>
                                </View>
                              )}
                            </View>
                          </View>
                        </TouchableOpacity>
                      </Animated.View>
                    ))
                  ) : (
                    <View style={styles.emptyState}>
                      <Ionicons
                        name="chatbubbles-outline"
                        size={48}
                        color={IRACHAT_COLORS.textMuted}
                        style={styles.emptyStateIcon}
                      />
                      <Text style={styles.emptyStateText}>
                        Your recent chats will appear here
                      </Text>
                    </View>
                  )}
                </View>
            </Animated.View>
          )}

          {activeTab === "business" && (
            <Animated.View style={[
              styles.contentSection,
              {
                opacity: fadeAnim
              }
            ]}>
              <View style={styles.businessList}>
                {isLoadingBusiness ? (
                  // Show skeleton loading
                  [...Array(2)].map((_, index) => (
                    <View key={`business-skeleton-${index}`} style={styles.businessItemSkeleton}>
                      <View style={styles.businessLogoSkeleton} />
                      <View style={styles.businessInfoSkeleton}>
                        <View style={styles.businessNameSkeleton} />
                        <View style={styles.businessTypeSkeleton} />
                      </View>
                    </View>
                  ))
                ) : userBusinessProfiles.length > 0 ? (
                  userBusinessProfiles.map((business: BusinessProfile) => (
                    <TouchableOpacity
                      key={business.id}
                      style={styles.businessItem}
                      onPress={() => {
                        console.log('Opening business profile:', business.businessName);
                        setSelectedBusinessProfile(business);
                        setShowBusinessProfile(true);
                      }}
                    >
                      {/* Business Logo */}
                      <Image
                        source={business.logo ? { uri: business.logo } : undefined}
                        style={styles.businessLogo}
                      />

                      {/* Business Info */}
                      <View style={styles.businessInfo}>
                        <Text style={styles.businessName}>{business.businessName}</Text>
                        <Text style={styles.businessType}>{business.businessType}</Text>
                        <View style={styles.businessStats}>
                          <Text style={styles.businessStat}>{business.totalPosts} posts</Text>
                          <Text style={styles.businessStat}>•</Text>
                          <Text style={styles.businessStat}>{business.totalViews} views</Text>
                        </View>
                      </View>

                      {/* Verified Badge */}
                      {business.isVerified && (
                        <Ionicons name="checkmark-circle" size={20} color="#3B82F6" />
                      )}
                    </TouchableOpacity>
                  ))
                ) : (
                  <View style={styles.emptyState}>
                    <Ionicons
                      name="business-outline"
                      size={64}
                      color={IRACHAT_COLORS.textSecondary}
                      style={styles.emptyStateIcon}
                    />
                    <Text style={styles.emptyStateText}>
                      You haven't created any business profiles yet
                    </Text>
                  </View>
                )}
              </View>
            </Animated.View>
          )}

          {activeTab === "media" && (
            <Animated.View style={[
              styles.contentSection,
              {
                opacity: fadeAnim
              }
            ]}>
                <MediaTabErrorBoundary>
                  {profileData && profileData.media ? (
                  <View style={styles.mediaContainer}>
                    {/* Media Tabs */}
                    <View style={styles.mediaTabsContainer}>
                    <TouchableOpacity style={styles.mediaTab}>
                      <Ionicons name="images" size={20} color={IRACHAT_COLORS.primary} />
                      <Text style={styles.mediaTabText}>Photos</Text>
                      <View style={styles.mediaTabBadge}>
                        <Text style={styles.mediaTabBadgeText}>
                          {profileData?.media?.photos?.length || 0}
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.mediaTab}>
                      <Ionicons name="videocam" size={20} color={IRACHAT_COLORS.primary} />
                      <Text style={styles.mediaTabText}>Videos</Text>
                      <View style={styles.mediaTabBadge}>
                        <Text style={styles.mediaTabBadgeText}>
                          {profileData?.media?.videos?.length || 0}
                        </Text>
                      </View>
                    </TouchableOpacity>

                    <TouchableOpacity style={styles.mediaTab}>
                      <Ionicons name="document" size={20} color={IRACHAT_COLORS.primary} />
                      <Text style={styles.mediaTabText}>Files</Text>
                      <View style={styles.mediaTabBadge}>
                        <Text style={styles.mediaTabBadgeText}>
                          {profileData?.media?.files?.length || 0}
                        </Text>
                      </View>
                    </TouchableOpacity>


                  </View>

                  {/* Photos Grid */}
                  <View style={styles.mediaSection}>
                    <Text style={styles.mediaSectionTitle}>Recent Photos</Text>
                    <View style={styles.photosGrid}>
                      {isLoadingMedia ? (
                        // Show skeleton loading for photos
                        [...Array(6)].map((_, index) => (
                          <MediaItemSkeleton key={`photo-skeleton-${index}`} />
                        ))
                      ) : (profileData?.media?.photos || []).slice(0, 6).map((photo: MediaItem) => (
                        <Animated.View
                          key={photo.id}
                          style={[
                            styles.photoItem,
                            {
                              opacity: Animated.multiply(fadeAnim, tabContentOpacity)
                            }
                          ]}
                        >
                          <TouchableOpacity
                            style={styles.photoTouchable}
                            activeOpacity={0.8}
                            onPress={() => handleMediaPress(photo)}
                          >
                            <Image
                              source={{ uri: photo.uri || 'https://via.placeholder.com/200x200/87CEEB/FFFFFF?text=Photo' }}
                              style={styles.photoImage}
                              resizeMode="cover"
                              onError={() => console.log('Photo load error:', photo.id)}
                            />
                          </TouchableOpacity>
                        </Animated.View>
                      ))}
                    </View>
                  </View>

                  {/* Videos Section */}
                  <View style={styles.mediaSection}>
                    <Text style={styles.mediaSectionTitle}>Recent Videos</Text>
                    <View style={styles.videosGrid}>
                      {(profileData?.media?.videos || []).map((video: MediaItem) => (
                        <Animated.View
                          key={video.id}
                          style={[
                            styles.videoItem,
                            {
                              opacity: fadeAnim,
                              transform: [{
                                translateY: fadeAnim.interpolate({
                                  inputRange: [0, 1],
                                  outputRange: [20, 0]
                                })
                              }]
                            }
                          ]}
                        >
                          <TouchableOpacity
                            style={styles.videoTouchable}
                            activeOpacity={0.8}
                            onPress={() => handleMediaPress(video)}
                          >
                            <Image
                              source={{ uri: video.uri || 'https://via.placeholder.com/200x200/FFA07A/FFFFFF?text=Video' }}
                              style={styles.videoThumbnail}
                              resizeMode="cover"
                              onError={() => console.log('Video thumbnail load error:', video.id)}
                            />
                            <View style={styles.videoOverlay}>
                              <Ionicons name="play-circle" size={40} color="rgba(255,255,255,0.9)" />
                            </View>
                            <View style={styles.videoDuration}>
                              <Text style={styles.videoDurationText}>{video.duration || '0:00'}</Text>
                            </View>
                          </TouchableOpacity>
                        </Animated.View>
                      ))}
                    </View>
                  </View>

                  {/* Files Section */}
                  <View style={styles.mediaSection}>
                    <Text style={styles.mediaSectionTitle}>Recent Files</Text>
                    <View style={styles.filesList}>
                      {(profileData?.media?.files || []).map((file: MediaItem) => (
                        <Animated.View
                          key={file.id}
                          style={[
                            styles.fileItem,
                            {
                              opacity: fadeAnim
                            }
                          ]}
                        >
                          <TouchableOpacity
                            style={styles.fileTouchable}
                            activeOpacity={0.7}
                            onPress={() => handleMediaPress(file)}
                          >
                            <View style={styles.fileIcon}>
                              <Ionicons
                                name={getFileIcon(file.type) as any}
                                size={24}
                                color={IRACHAT_COLORS.primary}
                              />
                            </View>
                            <View style={styles.fileInfo}>
                              <Text style={styles.fileName} numberOfLines={1}>
                                {file.name || 'Unknown File'}
                              </Text>
                              <Text style={styles.fileDetails}>
                                {file.size || '0 KB'} • {file.timestamp || 'Unknown'}
                              </Text>
                            </View>
                            <Ionicons name="chevron-forward" size={20} color={IRACHAT_COLORS.textMuted} />
                          </TouchableOpacity>
                        </Animated.View>
                      ))}
                    </View>
                  </View>
                  </View>
                  ) : (
                    <View style={styles.emptyState}>
                      <Ionicons
                        name="images-outline"
                        size={48}
                        color={IRACHAT_COLORS.textMuted}
                        style={styles.emptyStateIcon}
                      />
                      <Text style={styles.emptyStateText}>
                        No media content available
                      </Text>
                    </View>
                  )}
                </MediaTabErrorBoundary>
            </Animated.View>
          )}

        </Animated.View>
      </ScrollView>



      {/* Profile Photo Zoom Overlay */}
      <Animated.View
        style={[
          styles.zoomOverlay,
          {
            opacity: zoomOpacity,
            pointerEvents: isProfilePhotoZoomed ? 'auto' : 'none',
          }
        ]}
      >
        <TouchableOpacity
          style={styles.zoomOverlayBackground}
          onPress={toggleProfilePhotoZoom}
          activeOpacity={1}
        >
          <Animated.View
            style={[
              styles.zoomedImageContainer,
              {
                transform: [{ scale: zoomScale }]
              }
            ]}
          >
            {currentUser?.avatar ? (
              <Image
                source={{ uri: currentUser.avatar }}
                style={styles.zoomedImage}
                resizeMode="contain"
              />
            ) : (
              <LinearGradient
                colors={['#FFFFFF', '#F0F8FF']}
                style={styles.zoomedImageFallback}
              >
                <Text style={styles.zoomedImageText}>
                  {(currentUser?.name || "U").charAt(0).toUpperCase()}
                </Text>
              </LinearGradient>
            )}
          </Animated.View>

          {/* Tap to close hint */}
          <Animated.View style={styles.zoomHint}>
            <Text style={styles.zoomHintText}>Tap anywhere to close</Text>
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>

      {/* Privacy Settings Modal - Temporarily Disabled */}
      {/* {showPrivacySettings && (
        <Modal
          visible={showPrivacySettings}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowPrivacySettings(false)}
        >
          <PrivacySettings
            onClose={() => setShowPrivacySettings(false)}
            onLockEnabled={() => {
              setShowPrivacySettings(false);
              Alert.alert(
                "Privacy Lock Enabled",
                "Your app is now protected with privacy lock. You can lock it anytime from the settings.",
                [{ text: "OK" }]
              );
            }}
          />
        </Modal>
      )} */}

      {/* Business Profile Modal */}
      {selectedBusinessProfile && (
        <BusinessProfilePage
          visible={showBusinessProfile}
          businessProfile={selectedBusinessProfile}
          onClose={() => {
            setShowBusinessProfile(false);
            setSelectedBusinessProfile(null);
          }}
          onPostPress={(post) => {
            // Handle post press if needed
            console.log('Post pressed from business profile:', post.title);
          }}
          isOwner={true} // Always true since these are user's own businesses
          onAddProduct={() => {
            // TODO: Navigate to add product page
            Alert.alert('Add Product', 'Navigate to add product functionality');
          }}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  // Container Styles
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
    paddingTop: DeviceInfo.statusBarHeight,
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingBottom: ResponsiveSpacing.xl * 2, // Extra padding for better scroll experience
    minHeight: SCREEN_HEIGHT, // Ensure content fills screen height for proper bounce
  },

  // Loading Styles
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: ResponsiveSpacing.xl,
  },
  loadingSpinner: {
    marginBottom: ResponsiveSpacing.lg,
  },
  loadingText: {
    fontSize: ResponsiveTypography.fontSize.lg,
    color: IRACHAT_COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.sm,
  },
  loadingSubtext: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textMuted,
    textAlign: 'center',
  },

  // Header Styles
  headerContainer: {
    marginBottom: ResponsiveSpacing.sm, // Reduced from lg to sm
  },
  headerTouchable: {
    width: '100%',
  },
  profileHeaderWrapper: {
    width: '100%',
  },

  // Sticky Header Styles
  stickyHeader: {
    position: 'absolute',
    top: -DeviceInfo.statusBarHeight, // Offset by negative status bar height since container has padding
    left: 0,
    right: 0,
    zIndex: 1000,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
  },
  stickyHeaderGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.md,
    paddingTop: DeviceInfo.statusBarHeight + ResponsiveSpacing.lg, // Increased padding to prevent status bar encroachment
    paddingBottom: ResponsiveSpacing.md, // Increased bottom padding for better spacing
    minHeight: DeviceInfo.statusBarHeight + 80, // Ensure minimum height for proper spacing
    ...SHADOWS.lg,
  },
  stickyBackButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: ResponsiveSpacing.md,
  },
  stickyProfileInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  stickyAvatarContainer: {
    marginRight: ResponsiveSpacing.sm,
  },
  stickyAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.textOnPrimary,
  },
  stickyAvatarFallback: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.textOnPrimary,
  },
  stickyAvatarText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.primary,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
  },
  stickyUserInfo: {
    flex: 1,
  },
  stickyUserName: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    marginBottom: 2,
  },
  stickyUserHandle: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },
  stickySectionIndicator: {
    paddingHorizontal: ResponsiveSpacing.sm,
    paddingVertical: ResponsiveSpacing.xs,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: BORDER_RADIUS.md,
  },
  stickySectionText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },


  profileHeader: {
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.lg,
    paddingTop: ResponsiveSpacing.lg, // Container now handles status bar padding
    alignItems: 'center',
    position: 'relative',
    overflow: 'hidden',
    ...SHADOWS.lg,
  },
  headerNavigation: {
    position: 'absolute',
    top: DeviceInfo.statusBarHeight + ResponsiveSpacing.xl + ResponsiveSpacing.md, // Increased padding for more space
    left: ResponsiveSpacing.screenPadding,
    right: ResponsiveSpacing.screenPadding,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    zIndex: 10,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  menuButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.1,
  },
  patternDot: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: IRACHAT_COLORS.textOnPrimary,
  },
  profileContent: {
    alignItems: 'center',
    zIndex: 1,
    position: 'relative', // Enable absolute positioning for children
  },

  // Avatar Styles
  avatarContainer: {
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.md, // Reduced from lg to md
  },
  avatarWrapper: {
    position: 'relative',
  },
  avatarBorder: {
    width: ComponentSizes.avatarSize.large + 6, // Reduced from xlarge to large
    height: ComponentSizes.avatarSize.large + 6,
    borderRadius: (ComponentSizes.avatarSize.large + 6) / 2,
    backgroundColor: IRACHAT_COLORS.textOnPrimary,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: ResponsiveSpacing.md, // Push down from status bar
    ...SHADOWS.xl,
  },
  avatar: {
    width: ComponentSizes.avatarSize.large, // Reduced from xlarge to large
    height: ComponentSizes.avatarSize.large,
    borderRadius: ComponentSizes.avatarSize.large / 2,
    overflow: 'hidden',
  },
  avatarFallback: {
    width: ComponentSizes.avatarSize.large, // Reduced from xlarge to large
    height: ComponentSizes.avatarSize.large,
    borderRadius: ComponentSizes.avatarSize.large / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: ResponsiveTypography.fontSize['3xl'],
    color: IRACHAT_COLORS.primary,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: IRACHAT_COLORS.textOnPrimary,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.md,
  },
  onlineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: IRACHAT_COLORS.online,
  },
  editIcon: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: `${IRACHAT_COLORS.primary}CC`,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.sm,
  },

  // User Info Styles
  userInfo: {
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.sm, // Further reduced from md to sm
  },
  userName: {
    fontSize: ResponsiveTypography.fontSize.xl, // Reduced from 2xl to xl
    color: '#FFD700', // Bright Gold - shines beautifully on blue
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.xs,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  userHandle: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: '#00FFFF', // Bright Cyan - electric blue that pops
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.xs,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },
  userPhone: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: '#98FB98', // Pale Green - fresh and bright
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.sm,
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },
  userStatus: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: '#FFA07A', // Light Salmon - warm and inviting
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    textAlign: 'center',
    fontStyle: 'italic',
    textShadowColor: 'rgba(0, 0, 0, 0.2)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 1,
  },




  // Tab Styles
  tabsContainer: {
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    marginBottom: 0, // Remove bottom margin completely
    marginTop: 2, // Just 2px space from profile container
    backgroundColor: IRACHAT_COLORS.background,
    zIndex: 100,
  },
  stickyTabsContainer: {
    position: 'absolute',
    top: 0, // Container now handles status bar padding
    left: 0,
    right: 0,
    backgroundColor: IRACHAT_COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
    ...SHADOWS.sm,
    zIndex: 1000,
  },
  tabsWrapper: {
    flexDirection: 'row',
    backgroundColor: IRACHAT_COLORS.surfaceLight,
    borderRadius: BORDER_RADIUS.xl,
    padding: ResponsiveSpacing.xs,
    ...SHADOWS.sm,
  },
  tabItem: {
    flex: 1,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: ResponsiveSpacing.sm, // Reduced from md to sm
    paddingHorizontal: ResponsiveSpacing.xs, // Reduced from sm to xs
    borderRadius: BORDER_RADIUS.md, // Reduced from lg to md
    position: 'relative',
    overflow: 'hidden',
  },
  tabButtonActive: {
    ...SHADOWS.sm,
  },
  tabButtonGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  tabContent: {
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  tabIcon: {
    width: 16, // Reduced from 18 to 16
    height: 16, // Reduced from 18 to 16
    marginRight: ResponsiveSpacing.xs / 2, // Reduced margin
    overflow: 'hidden',
  },
  tabLabel: {
    fontSize: ResponsiveTypography.fontSize.xs, // Reduced from sm to xs
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },

  // Tab Content Styles
  tabContentContainer: {
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    marginBottom: ResponsiveSpacing.md, // Reduced from xl to md
  },
  contentSection: {
    marginBottom: ResponsiveSpacing.sm, // Reduced from lg to sm
  },


  // Chat List Styles
  chatsList: {
    paddingVertical: ResponsiveSpacing.sm,
  },
  chatItem: {
    marginBottom: ResponsiveSpacing.sm,
  },
  chatItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.lg,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: 'rgba(135, 206, 235, 0.1)',
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  chatAvatar: {
    position: 'relative',
    marginRight: ResponsiveSpacing.md,
  },
  chatAvatarImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: IRACHAT_COLORS.surfaceLight,
    overflow: 'hidden',
  },
  chatOnlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 14,
    height: 14,
    borderRadius: 7,
    backgroundColor: IRACHAT_COLORS.online,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.surface,
  },
  groupIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: IRACHAT_COLORS.accent,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.surface,
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.xs / 2,
  },
  chatName: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.text,
    flex: 1,
  },
  chatTimestamp: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    marginLeft: ResponsiveSpacing.sm,
  },
  chatMessageRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  chatLastMessage: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    flex: 1,
  },
  unreadBadge: {
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.xs,
    marginLeft: ResponsiveSpacing.sm,
  },
  unreadCount: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
  },

  // Media Styles
  mediaContainer: {
    paddingVertical: ResponsiveSpacing.sm,
  },
  mediaTabsContainer: {
    flexDirection: 'row',
    backgroundColor: IRACHAT_COLORS.surfaceLight,
    borderRadius: BORDER_RADIUS.lg,
    padding: ResponsiveSpacing.sm,
    marginBottom: ResponsiveSpacing.lg,
    marginHorizontal: ResponsiveSpacing.sm,
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  mediaTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: IRACHAT_COLORS.surface,
    marginHorizontal: ResponsiveSpacing.xs,
    minHeight: 48,
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.03,
    shadowRadius: 2,
    elevation: 1,
  },
  mediaTabText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.text,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    marginLeft: ResponsiveSpacing.xs,
    marginRight: ResponsiveSpacing.xs,
  },
  mediaTabBadge: {
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.xs / 2,
  },
  mediaTabBadgeText: {
    fontSize: ResponsiveTypography.fontSize.xs - 2,
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
  },
  mediaSection: {
    marginBottom: ResponsiveSpacing.lg,
    paddingHorizontal: ResponsiveSpacing.sm,
  },
  mediaSectionTitle: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.text,
    marginBottom: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.xs,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: ResponsiveSpacing.xs,
  },
  photoItem: {
    width: '31%',
    aspectRatio: 1,
    marginBottom: ResponsiveSpacing.md,
    marginHorizontal: '1%',
  },
  photoTouchable: {
    flex: 1,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    ...SHADOWS.sm,
  },
  photoImage: {
    width: '100%',
    height: '100%',
    backgroundColor: IRACHAT_COLORS.surfaceLight,
    overflow: 'hidden',
  },
  videosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingHorizontal: ResponsiveSpacing.xs,
  },
  videoItem: {
    width: '47%',
    aspectRatio: 16/9,
    marginBottom: ResponsiveSpacing.md,
    marginHorizontal: '1.5%',
  },
  videoTouchable: {
    flex: 1,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    position: 'relative',
    ...SHADOWS.sm,
  },
  videoThumbnail: {
    width: '100%',
    height: '100%',
    backgroundColor: IRACHAT_COLORS.surfaceLight,
    overflow: 'hidden',
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  videoDuration: {
    position: 'absolute',
    bottom: ResponsiveSpacing.xs,
    right: ResponsiveSpacing.xs,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: BORDER_RADIUS.sm,
    paddingHorizontal: ResponsiveSpacing.xs,
    paddingVertical: ResponsiveSpacing.xs / 2,
  },
  videoDurationText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: '#FFFFFF',
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },
  filesList: {
    paddingVertical: ResponsiveSpacing.xs,
    paddingHorizontal: ResponsiveSpacing.xs,
  },
  fileItem: {
    marginBottom: ResponsiveSpacing.md,
  },
  fileTouchable: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.lg,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: 'rgba(135, 206, 235, 0.1)',
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  fileIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${IRACHAT_COLORS.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: ResponsiveSpacing.md,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    color: IRACHAT_COLORS.text,
    marginBottom: ResponsiveSpacing.xs / 2,
  },
  fileDetails: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textMuted,
  },

  // Empty State Styles
  emptyState: {
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.xl * 2,
  },
  mediaGrid: {
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.xl * 2,
  },
  settingsList: {
    paddingVertical: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.sm,
  },
  emptyStateText: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textMuted,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.md,
  },
  emptyStateIcon: {
    opacity: 0.5,
  },

  // Settings Styles
  settingItem: {
    marginBottom: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.xs,
  },
  settingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.lg,
    paddingHorizontal: ResponsiveSpacing.lg,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: 'rgba(135, 206, 235, 0.1)',
    shadowColor: IRACHAT_COLORS.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
    minHeight: 72,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: `${IRACHAT_COLORS.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: ResponsiveSpacing.md,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.text,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    marginBottom: ResponsiveSpacing.xs / 2,
  },
  settingSubtitle: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textMuted,
  },

  // Business Styles
  businessList: {
    paddingHorizontal: ResponsiveSpacing.screenPadding,
  },
  businessItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.md,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: ResponsiveSpacing.sm,
    ...SHADOWS.sm,
  },
  businessLogo: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: IRACHAT_COLORS.surface,
    marginRight: ResponsiveSpacing.md,
  },
  businessInfo: {
    flex: 1,
  },
  businessName: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.primary,
    marginBottom: ResponsiveSpacing.xs / 2,
  },
  businessType: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    textTransform: 'uppercase',
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    marginBottom: ResponsiveSpacing.xs / 2,
  },
  businessStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: ResponsiveSpacing.xs,
  },
  businessStat: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
  },
  // Business skeleton styles
  businessItemSkeleton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.md,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: ResponsiveSpacing.sm,
  },
  businessLogoSkeleton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: IRACHAT_COLORS.surface,
    marginRight: ResponsiveSpacing.md,
  },
  businessInfoSkeleton: {
    flex: 1,
  },
  businessNameSkeleton: {
    height: 16,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: 4,
    marginBottom: ResponsiveSpacing.xs / 2,
    width: '70%',
  },
  businessTypeSkeleton: {
    height: 12,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: 4,
    width: '50%',
  },

  // App Info Styles
  appInfo: {
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.xl,
    borderTopWidth: 1,
    borderTopColor: IRACHAT_COLORS.borderLight,
    backgroundColor: IRACHAT_COLORS.surface,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.lg,
  },
  logoImage: {
    width: ResponsiveScale.iconSize(48),
    height: ResponsiveScale.iconSize(48),
    marginBottom: ResponsiveSpacing.sm,
  },
  appVersion: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    marginBottom: ResponsiveSpacing.xs,
  },
  appTagline: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    textAlign: 'center',
  },

  // Profile Menu Overlay Styles - Minimal Design
  profileMenuOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop: ResponsiveSpacing.xl * 3,
    paddingRight: ResponsiveSpacing.md,
  },
  profileMenuBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent', // No backdrop color
  },
  profileMenuContainer: {
    backgroundColor: IRACHAT_COLORS.background,
    borderRadius: BORDER_RADIUS.md,
    ...SHADOWS.md,
    paddingVertical: ResponsiveSpacing.xs,
    minWidth: 100,
  },
  profileMenuItem: {
    paddingVertical: ResponsiveSpacing.sm,
    paddingHorizontal: ResponsiveSpacing.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileMenuItemText: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.text,
    fontWeight: '500',
  },



  // Profile Photo Zoom Overlay Styles
  zoomOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  zoomOverlayBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomedImageContainer: {
    width: SCREEN_WIDTH * 0.9,
    height: SCREEN_WIDTH * 0.9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomedImage: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  zoomedImageFallback: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomedImageText: {
    fontSize: SCREEN_WIDTH * 0.2,
    color: IRACHAT_COLORS.primary,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
  },

  zoomHint: {
    position: 'absolute',
    bottom: 100,
    alignSelf: 'center',
  },
  zoomHintText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: ResponsiveTypography.fontSize.sm,
    textAlign: 'center',
  },
});
