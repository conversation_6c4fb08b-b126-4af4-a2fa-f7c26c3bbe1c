/**
 * Message Actions Service for IraChat
 * Handles all message operations: reply, delete, forward, copy, star, pin, edit, etc.
 */


import * as Clipboard from 'expo-clipboard';
import { doc, updateDoc, getDoc, collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from './firebaseSimple';
import { realTimeMessagingService } from './realTimeMessagingService';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';
import { SelectedMessage } from '../contexts/MessageSelectionContext';

export interface MessageActionResult {
  success: boolean;
  error?: string;
  data?: any;
}

export interface ForwardTarget {
  chatId: string;
  chatName: string;
  isGroup: boolean;
}

class MessageActionsService {
  /**
   * Reply to a message
   */
  async replyToMessage(
    originalMessage: SelectedMessage,
    replyContent: string,
    currentUserId: string,
    currentUserName: string
  ): Promise<MessageActionResult> {
    try {
      const replyData = {
        messageId: originalMessage.id,
        content: originalMessage.content,
        senderName: originalMessage.senderId, // Use senderId as senderName since SelectedMessage doesn't have senderName
        type: originalMessage.type,
      };

      const result = await realTimeMessagingService.sendMessage(
        originalMessage.chatId,
        currentUserId,
        currentUserName,
        replyContent,
        'text',
        undefined,
        undefined,
        replyData
      );

      if (result.success) {
        return { success: true, data: { messageId: result.messageId } };
      } else {
        return { success: false, error: result.error || 'Failed to send reply' };
      }
    } catch (error) {
      console.error('Error replying to message:', error);
      return { success: false, error: 'Failed to send reply' };
    }
  }

  /**
   * Delete messages
   */
  async deleteMessages(
    messages: SelectedMessage[],
    currentUserId: string,
    deleteForEveryone: boolean = false
  ): Promise<MessageActionResult> {
    try {
      const results = [];
      
      for (const message of messages) {
        if (!message.isOwn && deleteForEveryone) {
          return { success: false, error: 'Cannot delete others\' messages for everyone' };
        }

        let result;
        if (deleteForEveryone) {
          result = await realTimeMessagingService.deleteMessageForEveryone(message.id, currentUserId);
        } else {
          result = await realTimeMessagingService.deleteMessageForSelf(message.id, currentUserId, message.chatId);
        }
        
        results.push(result);
      }

      const failedCount = results.filter(r => !r.success).length;
      if (failedCount === 0) {
        return { success: true };
      } else if (failedCount === results.length) {
        return { success: false, error: 'Failed to delete all messages' };
      } else {
        return { success: true, error: `Failed to delete ${failedCount} of ${results.length} messages` };
      }
    } catch (error) {
      console.error('Error deleting messages:', error);
      return { success: false, error: 'Failed to delete messages' };
    }
  }

  /**
   * Forward messages to other chats
   */
  async forwardMessages(
    messages: SelectedMessage[],
    targets: ForwardTarget[],
    currentUserId: string,
    currentUserName: string
  ): Promise<MessageActionResult> {
    try {
      const results = [];

      for (const target of targets) {
        for (const message of messages) {
          let content = message.content;
          let type = message.type;
          let mediaUrl = message.mediaUrl;

          // Add forwarded indicator
          if (type === 'text') {
            content = `🔄 Forwarded: ${content}`;
          }

          const result = await realTimeMessagingService.sendMessage(
            target.chatId,
            currentUserId,
            currentUserName,
            content,
            type as any,
            mediaUrl,
            undefined // senderAvatar
            // Note: isForwarded flag is not supported by sendMessage, will be handled separately
          );

          // Store in messageForwards collection
          if (result.success && networkStateManager.isOnline()) {
            try {
              const forwardRef = collection(db, 'messageForwards');
              await addDoc(forwardRef, {
                originalMessageId: message.id,
                originalChatId: message.chatId,
                forwardedMessageId: result.messageId,
                forwardedToChatId: target.chatId,
                forwardedBy: currentUserId,
                forwardedAt: serverTimestamp(),
                originalContent: message.content,
                originalType: message.type,
                originalSenderId: message.senderId,
                targetChatName: target.chatName,
                targetIsGroup: target.isGroup,
              });
            } catch (forwardError) {
              console.warn('Failed to log forward action:', forwardError);
            }
          }

          results.push(result);
        }
      }

      const failedCount = results.filter(r => !r.success).length;
      if (failedCount === 0) {
        return { success: true, data: { forwardedCount: results.length } };
      } else {
        return { success: false, error: `Failed to forward ${failedCount} of ${results.length} messages` };
      }
    } catch (error) {
      console.error('Error forwarding messages:', error);
      return { success: false, error: 'Failed to forward messages' };
    }
  }

  /**
   * Copy message content to clipboard
   */
  async copyMessages(messages: SelectedMessage[], currentUserId?: string): Promise<MessageActionResult> {
    try {
      let textToCopy = '';

      for (const message of messages) {
        let content = '';

        if (message.type === 'text') {
          content = message.content;
        } else if (message.caption) {
          content = message.caption;
        } else {
          content = `[${message.type.toUpperCase()}]`;
        }

        if (textToCopy) {
          textToCopy += '\n\n';
        }
        textToCopy += content;
      }

      await Clipboard.setStringAsync(textToCopy);

      // Store in messageCopies collection
      if (currentUserId && networkStateManager.isOnline()) {
        try {
          for (const message of messages) {
            const copyRef = collection(db, 'messageCopies');
            await addDoc(copyRef, {
              messageId: message.id,
              chatId: message.chatId,
              copiedBy: currentUserId,
              copiedAt: serverTimestamp(),
              content: message.content,
              type: message.type,
              originalSenderId: message.senderId,
              copiedText: textToCopy,
            });
          }
        } catch (copyError) {
          console.warn('Failed to log copy action:', copyError);
        }
      }

      return {
        success: true,
        data: {
          copiedText: textToCopy,
          messageCount: messages.length
        }
      };
    } catch (error) {
      console.error('Error copying messages:', error);
      return { success: false, error: 'Failed to copy messages' };
    }
  }

  /**
   * Star/unstar messages
   */
  async toggleStarMessages(
    messages: SelectedMessage[],
    currentUserId: string,
    star: boolean = true
  ): Promise<MessageActionResult> {
    try {
      const results = [];

      for (const message of messages) {
        try {
          // Update in Firebase - use the correct collection path
          if (networkStateManager.isOnline()) {
            try {
              // Try to update the message in the messages collection
              const messageRef = doc(db, 'messages', message.id);
              const messageDoc = await getDoc(messageRef);

              if (messageDoc.exists()) {
                await updateDoc(messageRef, {
                  [`starredBy.${currentUserId}`]: star ? serverTimestamp() : null,
                  isStarred: star,
                });
              } else {
                console.warn('Message not found in Firebase, updating locally only');
              }
            } catch (firebaseError) {
              console.warn('Failed to update message in Firebase:', firebaseError);
              // Continue with local update
            }

            // Add to starredMessages collection
            if (star) {
              const starredRef = collection(db, 'starredMessages');
              await addDoc(starredRef, {
                messageId: message.id,
                chatId: message.chatId,
                userId: currentUserId,
                content: message.content,
                type: message.type,
                timestamp: message.timestamp,
                starredAt: serverTimestamp(),
                senderId: message.senderId,
              });
            }
          }

          // Update in local database
          const db_local = offlineDatabaseService.getDatabase();
          await db_local.runAsync(
            `UPDATE messages SET isStarred = ? WHERE id = ?`,
            [star ? 1 : 0, message.id]
          );

          // Add to local starred messages table
          if (star) {
            await db_local.runAsync(
              `INSERT OR REPLACE INTO starred_messages
               (messageId, chatId, userId, content, type, timestamp, starredAt, senderId)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                message.id,
                message.chatId,
                currentUserId,
                message.content,
                message.type,
                message.timestamp.toISOString(),
                new Date().toISOString(),
                message.senderId,
              ]
            );
          }

          results.push({ success: true });
        } catch (error) {
          console.error('Error starring message:', message.id, error);
          results.push({ success: false });
        }
      }

      const failedCount = results.filter(r => !r.success).length;
      if (failedCount === 0) {
        return { success: true };
      } else {
        return { success: false, error: `Failed to ${star ? 'star' : 'unstar'} ${failedCount} messages` };
      }
    } catch (error) {
      console.error('Error toggling star on messages:', error);
      return { success: false, error: `Failed to ${star ? 'star' : 'unstar'} messages` };
    }
  }

  /**
   * Pin/unpin message (only one message can be pinned per chat)
   */
  async togglePinMessage(
    message: SelectedMessage,
    currentUserId: string,
    pin: boolean = true,
    duration: number | null = null
  ): Promise<MessageActionResult> {
    try {
      const expiresAt = duration ? new Date(Date.now() + duration) : null;

      // Update in Firebase
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', message.chatId);
          const chatDoc = await getDoc(chatRef);

          if (chatDoc.exists()) {
            await updateDoc(chatRef, {
              pinnedMessage: pin ? {
                id: message.id,
                content: message.content,
                senderId: message.senderId,
                timestamp: message.timestamp,
                pinnedBy: currentUserId,
                pinnedAt: serverTimestamp(),
                expiresAt: expiresAt,
              } : null,
            });
          } else {
            console.warn('Chat not found in Firebase, updating locally only');
          }
        } catch (firebaseError) {
          console.warn('Failed to update chat in Firebase:', firebaseError);
          // Continue with local update
        }

        // Also add to pinnedMessages collection
        if (pin) {
          const pinnedRef = collection(db, 'pinnedMessages');
          await addDoc(pinnedRef, {
            messageId: message.id,
            chatId: message.chatId,
            content: message.content,
            senderId: message.senderId,
            senderName: message.senderId, // You might want to get actual sender name
            timestamp: message.timestamp,
            pinnedBy: currentUserId,
            pinnedAt: serverTimestamp(),
            expiresAt: expiresAt,
            type: message.type,
          });
        }
      }

      // Update in local database
      const db_local = offlineDatabaseService.getDatabase();
      await db_local.runAsync(
        `UPDATE chats SET pinnedMessageId = ?, pinnedMessageExpiresAt = ? WHERE id = ?`,
        [pin ? message.id : null, expiresAt?.toISOString() || null, message.chatId]
      );

      // Add to local pinned messages table
      if (pin) {
        await db_local.runAsync(
          `INSERT OR REPLACE INTO pinned_messages
           (messageId, chatId, content, senderId, timestamp, pinnedBy, pinnedAt, expiresAt, type)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            message.id,
            message.chatId,
            message.content,
            message.senderId,
            message.timestamp.toISOString(),
            currentUserId,
            new Date().toISOString(),
            expiresAt?.toISOString() || null,
            message.type,
          ]
        );
      }

      return { success: true };
    } catch (error) {
      console.error('Error pinning message:', error);
      return { success: false, error: `Failed to ${pin ? 'pin' : 'unpin'} message` };
    }
  }

  /**
   * Edit message (only text messages)
   */
  async editMessage(
    message: SelectedMessage,
    newContent: string,
    currentUserId: string
  ): Promise<MessageActionResult> {
    try {
      if (!message.isOwn) {
        return { success: false, error: 'Cannot edit others\' messages' };
      }

      if (message.type !== 'text') {
        return { success: false, error: 'Can only edit text messages' };
      }

      const editedAt = new Date();

      // Update in Firebase
      if (networkStateManager.isOnline()) {
        try {
          const messageRef = doc(db, 'messages', message.id);
          const messageDoc = await getDoc(messageRef);

          if (messageDoc.exists()) {
            await updateDoc(messageRef, {
              content: newContent,
              edited: true,
              editedAt: serverTimestamp(),
            });
          } else {
            console.warn('Message not found in Firebase, updating locally only');
          }
        } catch (firebaseError) {
          console.warn('Failed to update message in Firebase:', firebaseError);
          // Continue with local update
        }

        // Store in messageEdits collection
        const editRef = collection(db, 'messageEdits');
        await addDoc(editRef, {
          messageId: message.id,
          chatId: message.chatId,
          editedBy: currentUserId,
          editedAt: serverTimestamp(),
          originalContent: message.content,
          newContent: newContent,
          originalSenderId: message.senderId,
          editNumber: 1, // You could track multiple edits
        });
      }

      // Update in local database
      const db_local = offlineDatabaseService.getDatabase();
      await db_local.runAsync(
        `UPDATE messages SET content = ?, edited = 1, editedAt = ? WHERE id = ?`,
        [newContent, editedAt.toISOString(), message.id]
      );

      // Store edit history locally
      await db_local.runAsync(
        `INSERT INTO message_edits
         (messageId, chatId, editedBy, editedAt, originalContent, newContent, originalSenderId)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          message.id,
          message.chatId,
          currentUserId,
          editedAt.toISOString(),
          message.content,
          newContent,
          message.senderId,
        ]
      );

      return { success: true };
    } catch (error) {
      console.error('Error editing message:', error);
      return { success: false, error: 'Failed to edit message' };
    }
  }

  /**
   * Add emoji reaction to message
   */
  async addReaction(
    message: SelectedMessage,
    emoji: string,
    currentUserId: string
  ): Promise<MessageActionResult> {
    try {
      // First update local database immediately for offline support
      const db_local = offlineDatabaseService.getDatabase();

      // Check if message exists locally
      const messageExists = await db_local.getAllAsync(
        `SELECT id FROM messages WHERE id = ?`,
        [message.id]
      );

      if (messageExists.length === 0) {
        console.error('Message not found in local database:', message.id);
        return { success: false, error: 'Message not found locally' };
      }

      // Add reaction to local database
      await db_local.runAsync(
        `INSERT OR REPLACE INTO message_reactions (messageId, userId, emoji, timestamp) VALUES (?, ?, ?, ?)`,
        [message.id, currentUserId, emoji, new Date().toISOString()]
      );

      // Try to sync with Firebase if online
      if (networkStateManager.isOnline()) {
        try {
          const result = await realTimeMessagingService.addReaction(
            message.chatId,
            message.id,
            currentUserId,
            emoji
          );

          if (!result.success) {
            console.warn('Failed to sync reaction to Firebase:', result.error);
            // Don't fail the operation, just log the warning
          }
        } catch (syncError) {
          console.warn('Failed to sync reaction to Firebase:', syncError);
          // Don't fail the operation, reaction is saved locally
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Error adding reaction:', error);
      return { success: false, error: 'Failed to add reaction' };
    }
  }



  /**
   * Archive messages
   */
  async archiveMessages(
    messages: SelectedMessage[],
    currentUserId: string
  ): Promise<MessageActionResult> {
    try {
      const results = [];

      for (const message of messages) {
        try {
          // Update in Firebase
          if (networkStateManager.isOnline()) {
            const messageRef = doc(db, 'individual_chats', message.chatId, 'messages', message.id);
            await updateDoc(messageRef, {
              [`archivedBy.${currentUserId}`]: serverTimestamp(),
              isArchived: true,
            });
          }

          // Update in local database
          const db_local = offlineDatabaseService.getDatabase();
          await db_local.runAsync(
            `UPDATE messages SET isArchived = 1 WHERE id = ?`,
            [message.id]
          );

          results.push({ success: true });
        } catch (error) {
          console.error('Error archiving message:', message.id, error);
          results.push({ success: false });
        }
      }

      const failedCount = results.filter(r => !r.success).length;
      if (failedCount === 0) {
        return { success: true };
      } else {
        return { success: false, error: `Failed to archive ${failedCount} messages` };
      }
    } catch (error) {
      console.error('Error archiving messages:', error);
      return { success: false, error: 'Failed to archive messages' };
    }
  }

  /**
   * Get message info
   */
  async getMessageInfo(message: SelectedMessage): Promise<MessageActionResult> {
    try {
      const info = {
        id: message.id,
        type: message.type,
        timestamp: message.timestamp,
        senderId: message.senderId,
        chatId: message.chatId,
        content: message.content,
        mediaUrl: message.mediaUrl,
        fileName: message.fileName,
        fileSize: message.fileSize,
      };

      return { success: true, data: info };
    } catch (error) {
      console.error('Error getting message info:', error);
      return { success: false, error: 'Failed to get message info' };
    }
  }
}

export const messageActionsService = new MessageActionsService();
export default messageActionsService;
