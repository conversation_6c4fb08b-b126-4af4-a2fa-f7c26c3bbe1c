import { useLocalSearchPara<PERSON>, useRouter } from "expo-router";
import React, { useEffect, useState, useMemo, useCallback } from "react";
import { View, StyleSheet, Text } from "react-native";
import { StatusBar } from 'expo-status-bar';
import { useSelector } from "react-redux";
import { RootState } from "../../src/redux/store";
import { UltimateIndividualChatRoom } from "../../src/components/UltimateIndividualChatRoom";
import { IRACHAT_COLORS } from "../../src/styles/iraChatDesignSystem";
import { offlineDatabaseService } from "../../src/services/offlineDatabase";
import * as Contacts from 'expo-contacts';

export default function ChatScreen() {
  const params = useLocalSearchParams<{
    id: string;
    partnerName?: string;
    partnerAvatar?: string;
    isOnline?: string;
  }>();
  const { id } = params;
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state?.user?.currentUser);

  // Stabilize currentUser ID to prevent unnecessary re-renders
  const currentUserId = useMemo(() => {
    if (currentUser?.id) {
      return currentUser.id;
    }

    // Fallback: create a consistent user ID for testing
    console.warn('⚠️ No currentUser.id found, using fallback');
    return 'test_user_783835749'; // Consistent fallback ID for testing
  }, [currentUser?.id]);

  // Debug logging
  useEffect(() => {
    console.log('🔍 Chat Debug Info:', {
      currentUser: currentUser ? { id: currentUser.id, name: currentUser.name } : null,
      currentUserId,
      chatId: id,
      reduxState: currentUser
    });
  }, [currentUser, currentUserId, id]);

  // Early return if no user is available (but allow fallback to work)
  if (!currentUserId) {
    console.error('❌ No current user ID available, cannot open chat');
    return null;
  }

  // PARTNER DEFINITION: Extract partner info from chat ID or params
  // For individual chats, the chatId format is: "user1_user2" or just "partnerId"
  const partnerId = useMemo(() => {
    if (!id) return '';

    // If chatId contains current user ID, extract the other user's ID
    if (id.includes('_')) {
      const parts = id.split('_');
      return parts.find(part => part !== currentUserId) || parts[0];
    }

    // Otherwise, assume the entire ID is the partner ID
    return id;
  }, [id, currentUserId]);

  // CONSISTENT CHAT ID: Generate consistent chat ID for messaging
  const actualChatId = useMemo(() => {
    if (!currentUserId || !partnerId) return id;

    // If the route ID is already a proper chat ID (contains both users), use it
    if (id.includes('_') && id.includes(currentUserId)) {
      return id;
    }

    // Otherwise, generate consistent chat ID by sorting user IDs
    return [currentUserId, partnerId].sort().join('_');
  }, [id, currentUserId, partnerId]);

  // INSTANT LOADING: Initialize with parameters from navigation or fallback data
  const [partnerName, setPartnerName] = useState<string>(
    params.partnerName || `User ${partnerId.substring(0, 8)}` || 'Contact'
  );
  const [partnerAvatar, setPartnerAvatar] = useState<string>(params.partnerAvatar || '');
  const [isOnline, setIsOnline] = useState<boolean>(params.isOnline === 'true');

  // Debug logging for partner definition
  console.log('🔍 Chat ID Resolution:', {
    routeId: id,
    partnerId,
    actualChatId,
    partnerName,
    currentUserId,
    params
  });

  // INSTANT RECENT CHATS: Add chat to recent list immediately when opened
  const addToRecentChats = useCallback(async () => {
    if (!id || !currentUserId) return;

    try {
      // Ensure database is properly initialized with error recovery
      await offlineDatabaseService.ensureReady();

      if (!offlineDatabaseService.isReady()) {
        console.warn('⚠️ Database not ready, skipping recent chats update');
        return;
      }

      let db = offlineDatabaseService.getDatabase();

      // Check if chat already exists with proper error handling
      let existingChat = null;
      try {
        existingChat = await db.getFirstAsync(`
          SELECT id FROM chats WHERE id = ?
        `, [id]);
      } catch (dbError: any) {
        if (dbError.message?.includes('shared object') || dbError.message?.includes('already released')) {
          console.warn('⚠️ Database connection issue, reinitializing...');
          await offlineDatabaseService.forceReinitialize();
          db = offlineDatabaseService.getDatabase();
          existingChat = await db.getFirstAsync(`
            SELECT id FROM chats WHERE id = ?
          `, [id]);
        } else {
          throw dbError;
        }
      }

      if (!existingChat) {
        await db.runAsync(`
          INSERT INTO chats (
            id, name, avatar, partnerId, partnerName, partnerAvatar,
            isGroup, lastMessage, lastMessageTime, lastMessageSender,
            unreadCount, isOnline, isPinned, isArchived, isMuted,
            createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          id,
          partnerName,
          partnerAvatar || '',
          id,
          partnerName,
          partnerAvatar || '',
          0, // isGroup
          '', // Empty last message initially
          Date.now(),
          '',
          0, // unreadCount
          isOnline ? 1 : 0,
          0, // isPinned
          0, // isArchived
          0, // isMuted
          Date.now(),
          Date.now()
        ]);
        console.log('✅ New chat added to recent chats immediately');
      } else {
        // Update existing chat's timestamp to bring it to top
        await db.runAsync(`
          UPDATE chats SET updatedAt = ? WHERE id = ?
        `, [Date.now(), id]);
        console.log('✅ Existing chat moved to top of recent chats');
      }
    } catch (error: any) {
      console.error('❌ Failed to add chat to recent chats:', error);

      // If it's a database connection issue, try to reinitialize
      if (error.message?.includes('shared object') || error.message?.includes('already released')) {
        console.log('🔄 Attempting database recovery...');
        try {
          await offlineDatabaseService.forceReinitialize();
          console.log('✅ Database recovered successfully');
        } catch (recoveryError) {
          console.error('❌ Failed to recover database:', recoveryError);
        }
      }
    }
  }, [id, currentUserId, partnerName, partnerAvatar, isOnline]);

  // ENHANCED CONTACT NAME RESOLUTION: Load better names in background
  const enhanceContactName = useCallback(async () => {
    if (!id) return;

    try {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status === 'granted') {
        const { data } = await Contacts.getContactsAsync({
          fields: [Contacts.Fields.Name, Contacts.Fields.PhoneNumbers],
        });

        const contact = data.find(c =>
          c.phoneNumbers?.some(phone =>
            phone.number?.replace(/\D/g, '') === id.replace(/\D/g, '')
          )
        );

        if (contact?.name && contact.name !== partnerName) {
          setPartnerName(contact.name);
          console.log('✅ Enhanced contact name:', contact.name);
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to enhance contact name:', error);
    }
  }, [id, partnerName]);

  useEffect(() => {
    if (!id || !currentUserId) return;

    // Add to recent chats immediately
    addToRecentChats();

    // Enhance contact name in background
    enhanceContactName();
  }, [id, currentUserId]); // FIXED: Removed function dependencies to prevent infinite loop

  // FIXED: Better error handling to prevent black screen
  if (!id) {
    console.warn('⚠️ Missing chat ID');
    return (
      <View style={styles.fullScreenContainer}>
        <StatusBar style="light" backgroundColor={IRACHAT_COLORS.primary} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Invalid chat ID</Text>
        </View>
      </View>
    );
  }

  if (!currentUser) {
    console.warn('⚠️ No current user - showing loading');
    return (
      <View style={styles.fullScreenContainer}>
        <StatusBar style="light" backgroundColor={IRACHAT_COLORS.primary} />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Loading user data...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.fullScreenContainer}>
      <StatusBar style="light" backgroundColor={IRACHAT_COLORS.primary} />
      <UltimateIndividualChatRoom
        chatId={actualChatId}
        partnerId={partnerId}
        partnerName={partnerName}
        partnerAvatar={partnerAvatar}
        currentUserId={currentUserId}
        isDarkMode={false} // You can make this dynamic later
      />
    </View>
  );
}

const styles = StyleSheet.create({
  fullScreenContainer: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: IRACHAT_COLORS.text,
    textAlign: 'center',
  },
});