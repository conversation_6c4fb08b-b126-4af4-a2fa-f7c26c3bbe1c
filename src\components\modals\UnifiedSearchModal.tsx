import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Image,
  Alert,
  StyleSheet,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { unifiedSearchService, UnifiedSearchFilters, SearchResult } from '../../services/unifiedSearchService';
import { COLORS } from '../../constants/theme';

interface UnifiedSearchModalProps {
  visible: boolean;
  onClose: () => void;
  activeTab: 'feed' | 'marketplace' | 'skills';
  onResultPress: (result: SearchResult) => void;
}

export const UnifiedSearchModal: React.FC<UnifiedSearchModalProps> = ({
  visible,
  onClose,
  activeTab,
  onResultPress,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchFilters, setSearchFilters] = useState<UnifiedSearchFilters>({});
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // Perform unified search across all tabs
  const performSearch = async (query: string, filters: UnifiedSearchFilters = {}) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      // Determine search type based on active tab
      let searchType: 'all' | 'feed' | 'business' | 'education' = 'all';
      if (activeTab === 'feed') searchType = 'feed';
      else if (activeTab === 'marketplace') searchType = 'business';
      else if (activeTab === 'skills') searchType = 'education';

      const searchFilters: UnifiedSearchFilters = {
        ...filters,
        query: query.trim(),
        type: searchType,
      };

      const result = await unifiedSearchService.search(searchFilters, 50);
      
      if (result.success) {
        setSearchResults(result.results);
      } else {
        console.error('❌ Unified search failed:', result.error);
        Alert.alert('Search Error', result.error || 'Failed to search. Please try again.');
        setSearchResults([]);
      }
    } catch (error) {
      console.error('❌ Unified search error:', error);
      Alert.alert('Search Error', 'Failed to search. Please try again.');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Get search suggestions
  const getSearchSuggestions = async (query: string) => {
    if (!query.trim()) {
      setSuggestions([]);
      return;
    }

    try {
      const suggestions = await unifiedSearchService.getSearchSuggestions(query, 5);
      setSuggestions(suggestions);
    } catch (error) {
      console.error('❌ Error getting search suggestions:', error);
      setSuggestions([]);
    }
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
    setSuggestions([]);
    unifiedSearchService.clearCache();
  };

  // Handle search input change
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    performSearch(text, searchFilters);
    getSearchSuggestions(text);
  };

  // Handle filter change
  const handleFilterChange = (type: 'all' | 'business' | 'education') => {
    const newFilters = { ...searchFilters, type };
    setSearchFilters(newFilters);
    performSearch(searchQuery, newFilters);
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!visible) {
      clearSearch();
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <TouchableOpacity style={styles.backButton} onPress={onClose}>
              <Ionicons name="arrow-back" size={24} color="#F9FAFB" />
            </TouchableOpacity>
            <View style={styles.searchInputContainer}>
              <Ionicons name="search" size={20} color="#D1D5DB" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder={`Search ${activeTab === 'feed' ? 'updates' : activeTab === 'marketplace' ? 'products' : 'education'}...`}
                placeholderTextColor="#9CA3AF"
                value={searchQuery}
                onChangeText={handleSearchChange}
                autoFocus
                returnKeyType="search"
                onSubmitEditing={() => performSearch(searchQuery, searchFilters)}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity style={styles.clearButton} onPress={clearSearch}>
                  <Ionicons name="close-circle" size={20} color="#9CA3AF" />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Search Filters */}
          <View style={styles.filters}>
            <TouchableOpacity
              style={[styles.filterChip, searchFilters.type === 'all' && styles.filterChipActive]}
              onPress={() => handleFilterChange('all')}
            >
              <Text style={[styles.filterChipText, searchFilters.type === 'all' && styles.filterChipTextActive]}>
                All
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.filterChip, searchFilters.type === 'business' && styles.filterChipActive]}
              onPress={() => handleFilterChange('business')}
            >
              <Text style={[styles.filterChipText, searchFilters.type === 'business' && styles.filterChipTextActive]}>
                Business
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.filterChip, searchFilters.type === 'education' && styles.filterChipActive]}
              onPress={() => handleFilterChange('education')}
            >
              <Text style={[styles.filterChipText, searchFilters.type === 'education' && styles.filterChipTextActive]}>
                Education
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Content */}
        {isSearching ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Searching...</Text>
          </View>
        ) : searchResults.length > 0 ? (
          <FlatList
            data={searchResults}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.resultItem}
                onPress={() => onResultPress(item)}
              >
                <View style={styles.resultContent}>
                  {item.imageUrl && (
                    <Image source={{ uri: item.imageUrl }} style={styles.resultImage} />
                  )}
                  <View style={styles.resultText}>
                    <Text style={styles.resultTitle} numberOfLines={2}>
                      {item.title}
                    </Text>
                    <Text style={styles.resultDescription} numberOfLines={2}>
                      {item.description}
                    </Text>
                    <View style={styles.resultMeta}>
                      <Text style={styles.resultType}>
                        {item.type === 'business' ? '🏢' : item.type === 'education' ? '🎓' : '📱'} {item.businessName || item.userName}
                      </Text>
                      {item.price && (
                        <Text style={styles.resultPrice}>
                          {item.currency} {item.price.toLocaleString()}
                        </Text>
                      )}
                    </View>
                  </View>
                </View>
              </TouchableOpacity>
            )}
            keyExtractor={(item, index) => `search_${item.id}_${index}`}
            showsVerticalScrollIndicator={false}
          />
        ) : searchQuery.length > 0 ? (
          <View style={styles.emptyContainer}>
            <Ionicons name="search-outline" size={64} color="#E5E7EB" />
            <Text style={styles.emptyText}>No results found</Text>
            <Text style={styles.emptySubtext}>
              Try different keywords or check your filters
            </Text>
          </View>
        ) : (
          <View style={styles.initialContainer}>
            <Ionicons name="search" size={64} color="#E5E7EB" />
            <Text style={styles.initialText}>
              Search across {activeTab === 'feed' ? 'updates' : activeTab === 'marketplace' ? 'products' : 'education'}
            </Text>
            <Text style={styles.initialSubtext}>
              Find content by title, description, or tags
            </Text>
            
            {/* Search Suggestions */}
            {suggestions.length > 0 && (
              <View style={styles.suggestionsContainer}>
                <Text style={styles.suggestionsTitle}>Suggestions</Text>
                {suggestions.map((suggestion, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.suggestionItem}
                    onPress={() => {
                      setSearchQuery(suggestion);
                      performSearch(suggestion, searchFilters);
                    }}
                  >
                    <Ionicons name="search" size={16} color="#9CA3AF" />
                    <Text style={styles.suggestionText}>{suggestion}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    backgroundColor: '#1F2937',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
  },
  headerContent: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    marginBottom: 16,
  },
  backButton: {
    marginRight: 16,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    backgroundColor: '#374151',
    borderRadius: 12,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    color: '#F9FAFB',
    fontSize: 16,
    paddingVertical: 12,
  },
  clearButton: {
    marginLeft: 8,
  },
  filters: {
    flexDirection: 'row' as const,
    gap: 8,
  },
  filterChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#374151',
  },
  filterChipActive: {
    backgroundColor: COLORS.primary,
  },
  filterChipText: {
    color: '#D1D5DB',
    fontSize: 14,
    fontWeight: '500' as const,
  },
  filterChipTextActive: {
    color: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  loadingText: {
    color: '#D1D5DB',
    marginTop: 16,
    fontSize: 16,
  },
  resultItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#374151',
  },
  resultContent: {
    flexDirection: 'row' as const,
  },
  resultImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 12,
  },
  resultText: {
    flex: 1,
  },
  resultTitle: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '600' as const,
    marginBottom: 4,
  },
  resultDescription: {
    color: '#D1D5DB',
    fontSize: 14,
    marginBottom: 8,
  },
  resultMeta: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    alignItems: 'center' as const,
  },
  resultType: {
    color: '#9CA3AF',
    fontSize: 12,
  },
  resultPrice: {
    color: COLORS.primary,
    fontSize: 14,
    fontWeight: '600' as const,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 32,
  },
  emptyText: {
    color: '#F9FAFB',
    fontSize: 18,
    fontWeight: '600' as const,
    marginTop: 16,
  },
  emptySubtext: {
    color: '#9CA3AF',
    fontSize: 14,
    textAlign: 'center' as const,
    marginTop: 8,
  },
  initialContainer: {
    flex: 1,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    paddingHorizontal: 32,
  },
  initialText: {
    color: '#F9FAFB',
    fontSize: 18,
    fontWeight: '600' as const,
    marginTop: 16,
    textAlign: 'center' as const,
  },
  initialSubtext: {
    color: '#9CA3AF',
    fontSize: 14,
    textAlign: 'center' as const,
    marginTop: 8,
  },
  suggestionsContainer: {
    marginTop: 32,
    width: '100%',
  },
  suggestionsTitle: {
    color: '#F9FAFB',
    fontSize: 16,
    fontWeight: '600' as const,
    marginBottom: 12,
  },
  suggestionItem: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#374151',
    borderRadius: 8,
    marginBottom: 8,
  },
  suggestionText: {
    color: '#D1D5DB',
    marginLeft: 8,
    fontSize: 14,
  },
});
