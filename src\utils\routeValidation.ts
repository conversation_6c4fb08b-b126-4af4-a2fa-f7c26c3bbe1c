/**
 * 🧭 ROUTE VALIDATION UTILITY
 * Validates routes and provides debugging information for navigation issues
 */

import { ROUTES } from '../services/navigationService';

export interface RouteValidationResult {
  isValid: boolean;
  exists: boolean;
  suggestions: string[];
  message: string;
}

/**
 * Validate a route and provide helpful feedback
 */
export function validateRoute(route: string): RouteValidationResult {
  const result: RouteValidationResult = {
    isValid: false,
    exists: false,
    suggestions: [],
    message: ''
  };

  // Check if route is empty
  if (!route || route.trim() === '') {
    result.message = 'Route cannot be empty';
    return result;
  }

  // Get all available routes
  const allRoutes = getAllDefinedRoutes();
  
  // Check if route exists exactly
  if (allRoutes.includes(route)) {
    result.isValid = true;
    result.exists = true;
    result.message = 'Route is valid and exists';
    return result;
  }

  // Check if it's a valid route pattern (starts with / or contains parentheses)
  if (route.startsWith('/') || route.includes('(')) {
    result.isValid = true;
    result.message = 'Route appears to be a valid path pattern';
  }

  // Find similar routes for suggestions
  result.suggestions = findSimilarRoutes(route, allRoutes);
  
  if (result.suggestions.length > 0) {
    result.message += `. Did you mean: ${result.suggestions.slice(0, 3).join(', ')}?`;
  }

  return result;
}

/**
 * Get all defined routes from the ROUTES constant
 */
function getAllDefinedRoutes(): string[] {
  const routes: string[] = [];
  
  function extractRoutes(obj: any, prefix = '') {
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        routes.push(obj[key]);
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        extractRoutes(obj[key], `${prefix}${key}.`);
      }
    }
  }
  
  extractRoutes(ROUTES);
  return routes;
}

/**
 * Find routes similar to the given route
 */
function findSimilarRoutes(targetRoute: string, allRoutes: string[]): string[] {
  const similarities: Array<{ route: string; score: number }> = [];
  
  for (const route of allRoutes) {
    const score = calculateSimilarity(targetRoute.toLowerCase(), route.toLowerCase());
    if (score > 0.3) { // Only include routes with >30% similarity
      similarities.push({ route, score });
    }
  }
  
  // Sort by similarity score (highest first)
  similarities.sort((a, b) => b.score - a.score);
  
  return similarities.slice(0, 5).map(item => item.route);
}

/**
 * Calculate similarity between two strings using Levenshtein distance
 */
function calculateSimilarity(str1: string, str2: string): number {
  const matrix: number[][] = [];
  
  // Initialize matrix
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  // Fill matrix
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1, // substitution
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j] + 1      // deletion
        );
      }
    }
  }
  
  const distance = matrix[str2.length][str1.length];
  const maxLength = Math.max(str1.length, str2.length);
  
  return maxLength === 0 ? 1 : (maxLength - distance) / maxLength;
}

/**
 * Debug function to log all available routes
 */
export function logAllRoutes(): void {
  console.log('📱 All Available Routes:');
  const routes = getAllDefinedRoutes();
  routes.sort().forEach((route, index) => {
    console.log(`${index + 1}. ${route}`);
  });
}

/**
 * Check if a route is an authentication route
 */
export function isAuthRoute(route: string): boolean {
  return route.includes('(auth)') || 
         route.includes('/auth/') ||
         Object.values(ROUTES.AUTH).includes(route as any);
}

/**
 * Check if a route is a tab route
 */
export function isTabRoute(route: string): boolean {
  return route.includes('(tabs)') || 
         route.includes('/tabs/') ||
         Object.values(ROUTES.TABS).includes(route as any);
}

/**
 * Get route category for debugging
 */
export function getRouteCategory(route: string): string {
  if (isAuthRoute(route)) return 'Authentication';
  if (isTabRoute(route)) return 'Tabs';
  if (route.includes('/chat/')) return 'Chat';
  if (route.includes('/call/')) return 'Call';
  if (route.includes('/group/')) return 'Group';
  return 'Other';
}
