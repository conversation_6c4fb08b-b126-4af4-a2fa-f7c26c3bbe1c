import { Animated, Easing, Platform, Dimensions } from "react-native";

// Get device dimensions for responsive animations
const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Device type detection for optimized animations
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

// Performance-optimized animation durations based on device
const getOptimizedDuration = (baseDuration: number): number => {
  if (isSmallDevice) return Math.round(baseDuration * 0.8); // Faster on small devices
  if (isTablet) return Math.round(baseDuration * 1.2); // Slightly slower on tablets
  return baseDuration;
};

// Animation configuration for different device types
export const ANIMATION_CONFIG = {
  durations: {
    fast: getOptimizedDuration(150),
    normal: getOptimizedDuration(300),
    slow: getOptimizedDuration(500),
    veryFast: getOptimizedDuration(100),
  },
  spring: {
    tension: isSmallDevice ? 120 : 100,
    friction: isSmallDevice ? 10 : 8,
  },
  easing: {
    ease: Easing.ease,
    easeIn: Easing.in(Easing.ease),
    easeOut: Easing.out(Easing.ease),
    easeInOut: Easing.inOut(Easing.ease),
    bounce: Easing.bounce,
    elastic: Easing.elastic(1),
  },
};

// Enhanced mobile-optimized animation utilities
export const createWebSafeAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  duration: number = ANIMATION_CONFIG.durations.normal,
  easing: ((value: number) => number) = ANIMATION_CONFIG.easing.ease,
  useNativeDriver: boolean = true,
) => {
  return Animated.timing(animatedValue, {
    toValue,
    duration: getOptimizedDuration(duration),
    easing,
    useNativeDriver: Platform.OS !== "web" && useNativeDriver,
  });
};

export const createSpringAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  useNativeDriver: boolean = true,
  tension: number = ANIMATION_CONFIG.spring.tension,
  friction: number = ANIMATION_CONFIG.spring.friction,
) => {
  return Animated.spring(animatedValue, {
    toValue,
    tension,
    friction,
    useNativeDriver: Platform.OS !== "web" && useNativeDriver,
  });
};

// Mobile-optimized timing animation
export const createTimingAnimation = (
  animatedValue: Animated.Value,
  toValue: number,
  duration: number = ANIMATION_CONFIG.durations.normal,
  easing: ((value: number) => number) = ANIMATION_CONFIG.easing.easeOut,
) => {
  return Animated.timing(animatedValue, {
    toValue,
    duration: getOptimizedDuration(duration),
    easing,
    useNativeDriver: true,
  });
};

export const createRotationAnimation = (
  animatedValue: Animated.Value,
  duration: number = 2000,
  useNativeDriver: boolean = true,
) => {
  return Animated.loop(
    Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      useNativeDriver: Platform.OS !== "web" && useNativeDriver,
    }),
  );
};

// Platform-specific animation configurations
export const getAnimationConfig = () => {
  return {
    useNativeDriver: Platform.OS !== "web",
    duration: Platform.OS === "web" ? 200 : 300, // Shorter duration for web
    tension: Platform.OS === "web" ? 120 : 100,
    friction: Platform.OS === "web" ? 10 : 8,
  };
};

// Enhanced responsive animation values for mobile devices
export const getResponsiveAnimationValues = (screenWidth: number = SCREEN_WIDTH) => {
  const deviceIsSmall = screenWidth < 375;
  const deviceIsMedium = screenWidth >= 375 && screenWidth < 414;
  const deviceIsLarge = screenWidth >= 414 && screenWidth < 768;
  const deviceIsTablet = screenWidth >= 768;

  return {
    scale: {
      pressed: deviceIsSmall ? 0.97 : 0.95,
      normal: 1,
      hover: deviceIsTablet ? 1.02 : 1, // Subtle hover effect for tablets
    },
    duration: {
      fast: deviceIsSmall ? 120 : deviceIsTablet ? 200 : 150,
      normal: deviceIsSmall ? 250 : deviceIsTablet ? 350 : 300,
      slow: deviceIsSmall ? 400 : deviceIsTablet ? 600 : 500,
    },
    spring: {
      tension: deviceIsSmall ? 120 : deviceIsTablet ? 80 : 100,
      friction: deviceIsSmall ? 10 : deviceIsTablet ? 6 : 8,
    },
    translate: {
      small: deviceIsSmall ? 8 : 10,
      medium: deviceIsSmall ? 16 : 20,
      large: deviceIsSmall ? 24 : 30,
    },
    opacity: {
      hidden: 0,
      visible: 1,
      dimmed: 0.6,
      subtle: 0.8,
    },
    screenSize: {
      isSmall: deviceIsSmall,
      isMedium: deviceIsMedium,
      isLarge: deviceIsLarge,
      isTablet: deviceIsTablet,
      width: screenWidth,
    },
  };
};

// Enhanced fade animations with mobile optimization
export const fadeIn = (
  value: Animated.Value,
  duration: number = ANIMATION_CONFIG.durations.normal,
  easing: ((value: number) => number) = ANIMATION_CONFIG.easing.easeOut
) => {
  return Animated.timing(value, {
    toValue: 1,
    duration: getOptimizedDuration(duration),
    easing,
    useNativeDriver: true,
  });
};

export const fadeOut = (
  value: Animated.Value,
  duration: number = ANIMATION_CONFIG.durations.normal,
  easing: ((value: number) => number) = ANIMATION_CONFIG.easing.easeIn
) => {
  return Animated.timing(value, {
    toValue: 0,
    duration: getOptimizedDuration(duration),
    easing,
    useNativeDriver: true,
  });
};

// Fade with custom opacity values
export const fadeTo = (
  value: Animated.Value,
  toValue: number,
  duration: number = ANIMATION_CONFIG.durations.normal
) => {
  return Animated.timing(value, {
    toValue,
    duration: getOptimizedDuration(duration),
    easing: ANIMATION_CONFIG.easing.easeOut,
    useNativeDriver: true,
  });
};

// Enhanced slide animations with direction support
export const slideIn = (
  value: Animated.Value,
  duration: number = ANIMATION_CONFIG.durations.normal
) => {
  return Animated.timing(value, {
    toValue: 0,
    duration: getOptimizedDuration(duration),
    easing: ANIMATION_CONFIG.easing.easeOut,
    useNativeDriver: true,
  });
};

export const slideOut = (
  value: Animated.Value,
  duration: number = ANIMATION_CONFIG.durations.normal,
  direction: 'up' | 'down' | 'left' | 'right' = 'down'
) => {
  const distance = isSmallDevice ? 80 : 100;
  const toValue = direction === 'up' ? -distance :
                  direction === 'down' ? distance :
                  direction === 'left' ? -distance : distance;

  return Animated.timing(value, {
    toValue,
    duration: getOptimizedDuration(duration),
    easing: ANIMATION_CONFIG.easing.easeIn,
    useNativeDriver: true,
  });
};

// Slide from specific direction
export const slideFromDirection = (
  value: Animated.Value,
  direction: 'up' | 'down' | 'left' | 'right',
  distance: number = isSmallDevice ? 50 : 100,
  duration: number = ANIMATION_CONFIG.durations.normal
) => {
  const startValue = direction === 'up' ? distance :
                     direction === 'down' ? -distance :
                     direction === 'left' ? distance : -distance;

  value.setValue(startValue);

  return Animated.timing(value, {
    toValue: 0,
    duration: getOptimizedDuration(duration),
    easing: ANIMATION_CONFIG.easing.easeOut,
    useNativeDriver: true,
  });
};

// Enhanced scale animations with mobile optimization
export const scaleIn = (value: Animated.Value) => {
  return Animated.spring(value, {
    toValue: 1,
    useNativeDriver: true,
    friction: ANIMATION_CONFIG.spring.friction,
    tension: ANIMATION_CONFIG.spring.tension,
  });
};

export const scaleOut = (value: Animated.Value) => {
  return Animated.spring(value, {
    toValue: 0,
    useNativeDriver: true,
    friction: ANIMATION_CONFIG.spring.friction,
    tension: ANIMATION_CONFIG.spring.tension,
  });
};

// Scale to specific value
export const scaleTo = (
  value: Animated.Value,
  toValue: number,
  useSpring: boolean = true
) => {
  if (useSpring) {
    return Animated.spring(value, {
      toValue,
      useNativeDriver: true,
      friction: ANIMATION_CONFIG.spring.friction,
      tension: ANIMATION_CONFIG.spring.tension,
    });
  } else {
    return Animated.timing(value, {
      toValue,
      duration: ANIMATION_CONFIG.durations.normal,
      easing: ANIMATION_CONFIG.easing.easeOut,
      useNativeDriver: true,
    });
  }
};

export const shake = (value: Animated.Value) => {
  return Animated.sequence([
    Animated.timing(value, {
      toValue: 10,
      duration: 100,
      useNativeDriver: true,
    }),
    Animated.timing(value, {
      toValue: -10,
      duration: 100,
      useNativeDriver: true,
    }),
    Animated.timing(value, {
      toValue: 10,
      duration: 100,
      useNativeDriver: true,
    }),
    Animated.timing(value, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }),
  ]);
};

export const pulse = (value: Animated.Value) => {
  return Animated.sequence([
    Animated.timing(value, {
      toValue: 1.1,
      duration: 200,
      useNativeDriver: true,
    }),
    Animated.timing(value, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }),
  ]);
};

export const createPressAnimation = (scale: Animated.Value) => {
  return {
    onPressIn: () => {
      Animated.spring(scale, {
        toValue: 0.95,
        useNativeDriver: true,
        friction: 8,
        tension: 40,
      }).start();
    },
    onPressOut: () => {
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: true,
        friction: 8,
        tension: 40,
      }).start();
    },
  };
};

export const createSwipeAnimation = (translateX: Animated.Value) => {
  const swipeDistance = isSmallDevice ? 80 : 100;

  return {
    onSwipeLeft: () => {
      Animated.timing(translateX, {
        toValue: -swipeDistance,
        duration: ANIMATION_CONFIG.durations.normal,
        easing: ANIMATION_CONFIG.easing.easeOut,
        useNativeDriver: true,
      }).start();
    },
    onSwipeRight: () => {
      Animated.timing(translateX, {
        toValue: swipeDistance,
        duration: ANIMATION_CONFIG.durations.normal,
        easing: ANIMATION_CONFIG.easing.easeOut,
        useNativeDriver: true,
      }).start();
    },
    reset: () => {
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
        friction: ANIMATION_CONFIG.spring.friction,
        tension: ANIMATION_CONFIG.spring.tension,
      }).start();
    },
  };
};

// Mobile-optimized loading animation
export const createLoadingAnimation = (rotateValue: Animated.Value) => {
  return Animated.loop(
    Animated.timing(rotateValue, {
      toValue: 1,
      duration: isSmallDevice ? 1000 : 1200,
      easing: Easing.linear,
      useNativeDriver: true,
    })
  );
};

// Stagger animation for lists
export const createStaggerAnimation = (
  values: Animated.Value[],
  staggerDelay: number = 100
) => {
  const animations = values.map((value, index) =>
    Animated.timing(value, {
      toValue: 1,
      duration: ANIMATION_CONFIG.durations.normal,
      delay: index * (isSmallDevice ? staggerDelay * 0.8 : staggerDelay),
      easing: ANIMATION_CONFIG.easing.easeOut,
      useNativeDriver: true,
    })
  );

  return Animated.parallel(animations);
};

// Bounce animation for notifications
export const createBounceAnimation = (value: Animated.Value) => {
  return Animated.sequence([
    Animated.timing(value, {
      toValue: 1.1,
      duration: ANIMATION_CONFIG.durations.fast,
      easing: ANIMATION_CONFIG.easing.easeOut,
      useNativeDriver: true,
    }),
    Animated.timing(value, {
      toValue: 0.95,
      duration: ANIMATION_CONFIG.durations.fast,
      easing: ANIMATION_CONFIG.easing.easeIn,
      useNativeDriver: true,
    }),
    Animated.timing(value, {
      toValue: 1,
      duration: ANIMATION_CONFIG.durations.fast,
      easing: ANIMATION_CONFIG.easing.easeOut,
      useNativeDriver: true,
    }),
  ]);
};

// Smooth transition for tab switching
export const createTabSwitchAnimation = (
  currentTab: Animated.Value,
  newTabIndex: number
) => {
  return Animated.timing(currentTab, {
    toValue: newTabIndex,
    duration: ANIMATION_CONFIG.durations.normal,
    easing: ANIMATION_CONFIG.easing.easeInOut,
    useNativeDriver: false, // Layout animations need useNativeDriver: false
  });
};
