import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, AppStateStatus } from 'react-native';
import CryptoJS from 'crypto-js';

// Conditional import for expo-local-authentication
let LocalAuthentication: any = null;
try {
  LocalAuthentication = require('expo-local-authentication');
} catch (error) {
  console.warn('expo-local-authentication not available:', error);
}

// Storage keys
const STORAGE_KEYS = {
  LOCK_PIN: 'irachat_lock_pin',
  LOCK_TYPE: 'irachat_lock_type',
  AUTO_LOCK_DURATION: 'irachat_auto_lock_duration',
  IS_LOCKED: 'irachat_is_locked',
  LOCK_TIMESTAMP: 'irachat_lock_timestamp',
  USE_SYSTEM_AUTH: 'irachat_use_system_auth',
} as const;

// Lock types
export enum LockType {
  NONE = 'none',
  PIN = 'pin',
  PASSWORD = 'password',
  SYSTEM = 'system', // Use phone's biometric/system lock
}

// Auto-lock durations (in milliseconds)
export enum AutoLockDuration {
  IMMEDIATE = 0,
  THIRTY_SECONDS = 30 * 1000,
  ONE_MINUTE = 60 * 1000,
  FIVE_MINUTES = 5 * 60 * 1000,
  FIFTEEN_MINUTES = 15 * 60 * 1000,
  THIRTY_MINUTES = 30 * 60 * 1000,
  ONE_HOUR = 60 * 60 * 1000,
  NEVER = -1,
}

// Lock configuration interface
export interface LockConfig {
  lockType: LockType;
  autoLockDuration: AutoLockDuration;
  useSystemAuth: boolean;
}

// Privacy lock service class
class PrivacyLockService {
  private lockListeners: Array<(isLocked: boolean) => void> = [];
  private appStateSubscription: any = null;
  private lockTimer: NodeJS.Timeout | null = null;
  private lastActiveTime: number = Date.now();

  constructor() {
    this.initializeAppStateListener();
  }

  // Initialize app state listener for auto-lock
  private initializeAppStateListener() {
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
  }

  // Handle app state changes for auto-lock
  private handleAppStateChange = async (nextAppState: AppStateStatus) => {
    if (nextAppState === 'background' || nextAppState === 'inactive') {
      // App is going to background, start lock timer
      await this.startLockTimer();
    } else if (nextAppState === 'active') {
      // App is becoming active, check if should be locked
      await this.checkAutoLock();
      this.clearLockTimer();
    }
  };

  // Start auto-lock timer
  private async startLockTimer() {
    const config = await this.getLockConfig();
    if (config.autoLockDuration === AutoLockDuration.NEVER) return;

    this.lastActiveTime = Date.now();
    await AsyncStorage.setItem(STORAGE_KEYS.LOCK_TIMESTAMP, this.lastActiveTime.toString());

    if (config.autoLockDuration === AutoLockDuration.IMMEDIATE) {
      await this.lockApp();
      return;
    }

    this.clearLockTimer();
    this.lockTimer = setTimeout(async () => {
      await this.lockApp();
    }, config.autoLockDuration);
  }

  // Clear lock timer
  private clearLockTimer() {
    if (this.lockTimer) {
      clearTimeout(this.lockTimer);
      this.lockTimer = null;
    }
  }

  // Check if app should be auto-locked
  private async checkAutoLock() {
    const config = await this.getLockConfig();
    if (config.lockType === LockType.NONE || config.autoLockDuration === AutoLockDuration.NEVER) {
      return;
    }

    const lastActiveTimeStr = await AsyncStorage.getItem(STORAGE_KEYS.LOCK_TIMESTAMP);
    if (!lastActiveTimeStr) return;

    const lastActiveTime = parseInt(lastActiveTimeStr, 10);
    const timeDiff = Date.now() - lastActiveTime;

    if (timeDiff >= config.autoLockDuration) {
      await this.lockApp();
    }
  }

  // Hash PIN/password for secure storage
  private hashCredential(credential: string): string {
    return CryptoJS.SHA256(credential).toString();
  }

  // Set up lock with PIN/password
  async setupLock(lockType: LockType, credential?: string): Promise<boolean> {
    try {
      if (lockType === LockType.NONE) {
        await this.removeLock();
        return true;
      }

      if (lockType === LockType.SYSTEM) {
        // Check if system authentication is available
        if (!LocalAuthentication) {
          throw new Error('System authentication not available - expo-local-authentication not installed');
        }

        const hasHardware = await LocalAuthentication.hasHardwareAsync();
        const isEnrolled = await LocalAuthentication.isEnrolledAsync();

        if (!hasHardware || !isEnrolled) {
          throw new Error('System authentication not available on this device');
        }
      } else if (!credential) {
        throw new Error('PIN/Password required for this lock type');
      }

      // Store lock configuration
      await AsyncStorage.setItem(STORAGE_KEYS.LOCK_TYPE, lockType);
      
      if (credential) {
        const hashedCredential = this.hashCredential(credential);
        await AsyncStorage.setItem(STORAGE_KEYS.LOCK_PIN, hashedCredential);
      }

      console.log('✅ Privacy lock setup completed');
      return true;
    } catch (error) {
      console.error('❌ Error setting up privacy lock:', error);
      return false;
    }
  }

  // Remove lock
  async removeLock(): Promise<void> {
    await AsyncStorage.multiRemove([
      STORAGE_KEYS.LOCK_PIN,
      STORAGE_KEYS.LOCK_TYPE,
      STORAGE_KEYS.IS_LOCKED,
    ]);
    await AsyncStorage.setItem(STORAGE_KEYS.LOCK_TYPE, LockType.NONE);
    this.notifyLockListeners(false);
  }

  // Lock the app
  async lockApp(): Promise<void> {
    const config = await this.getLockConfig();
    if (config.lockType === LockType.NONE) return;

    await AsyncStorage.setItem(STORAGE_KEYS.IS_LOCKED, 'true');
    this.notifyLockListeners(true);
    console.log('🔒 App locked');
  }

  // Unlock the app with credential
  async unlockApp(credential: string): Promise<boolean> {
    try {
      const config = await this.getLockConfig();
      
      if (config.lockType === LockType.NONE) {
        return true;
      }

      if (config.lockType === LockType.SYSTEM) {
        return await this.authenticateWithSystem();
      }

      // Validate PIN/password
      const storedHash = await AsyncStorage.getItem(STORAGE_KEYS.LOCK_PIN);
      if (!storedHash) return false;

      const inputHash = this.hashCredential(credential);
      if (inputHash === storedHash) {
        await AsyncStorage.setItem(STORAGE_KEYS.IS_LOCKED, 'false');
        this.notifyLockListeners(false);
        console.log('🔓 App unlocked');
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Error unlocking app:', error);
      return false;
    }
  }

  // Authenticate with system (biometric/PIN)
  async authenticateWithSystem(): Promise<boolean> {
    try {
      if (!LocalAuthentication) {
        console.error('❌ System authentication not available - expo-local-authentication not installed');
        return false;
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Unlock IraChat',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
      });

      if (result.success) {
        await AsyncStorage.setItem(STORAGE_KEYS.IS_LOCKED, 'false');
        this.notifyLockListeners(false);
        console.log('🔓 App unlocked with system authentication');
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ System authentication error:', error);
      return false;
    }
  }

  // Check if app is currently locked
  async isLocked(): Promise<boolean> {
    const isLocked = await AsyncStorage.getItem(STORAGE_KEYS.IS_LOCKED);
    return isLocked === 'true';
  }

  // Get current lock configuration
  async getLockConfig(): Promise<LockConfig> {
    const [lockType, autoLockDuration, useSystemAuth] = await AsyncStorage.multiGet([
      STORAGE_KEYS.LOCK_TYPE,
      STORAGE_KEYS.AUTO_LOCK_DURATION,
      STORAGE_KEYS.USE_SYSTEM_AUTH,
    ]);

    return {
      lockType: (lockType[1] as LockType) || LockType.NONE,
      autoLockDuration: parseInt(autoLockDuration[1] || AutoLockDuration.FIVE_MINUTES.toString(), 10),
      useSystemAuth: useSystemAuth[1] === 'true',
    };
  }

  // Set auto-lock duration
  async setAutoLockDuration(duration: AutoLockDuration): Promise<void> {
    await AsyncStorage.setItem(STORAGE_KEYS.AUTO_LOCK_DURATION, duration.toString());
  }

  // Add lock state listener
  addLockListener(listener: (isLocked: boolean) => void): () => void {
    this.lockListeners.push(listener);
    
    // Return unsubscribe function
    return () => {
      const index = this.lockListeners.indexOf(listener);
      if (index > -1) {
        this.lockListeners.splice(index, 1);
      }
    };
  }

  // Notify all lock listeners
  private notifyLockListeners(isLocked: boolean) {
    this.lockListeners.forEach(listener => listener(isLocked));
  }

  // Check if system authentication is available
  async isSystemAuthAvailable(): Promise<boolean> {
    try {
      if (!LocalAuthentication) {
        return false;
      }
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      return hasHardware && isEnrolled;
    } catch {
      return false;
    }
  }

  // Get available authentication types
  async getAvailableAuthTypes(): Promise<any[]> {
    try {
      if (!LocalAuthentication) {
        return [];
      }
      return await LocalAuthentication.supportedAuthenticationTypesAsync();
    } catch {
      return [];
    }
  }

  // Cleanup
  destroy() {
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
    }
    this.clearLockTimer();
    this.lockListeners = [];
  }
}

// Export singleton instance
export const privacyLockService = new PrivacyLockService();
export default privacyLockService;
