import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Pressable,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface GroupMember {
  id: string;
  name: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member';
  isOnline: boolean;
  lastSeen?: Date;
  joinedAt: Date;
}

interface GroupInfo {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  createdBy: string;
  createdAt: Date;
  memberCount: number;
  settings: {
    whoCanSendMessages: 'everyone' | 'admins_only';
    whoCanAddMembers: 'everyone' | 'admins_only';
    whoCanEditGroupInfo: 'admins_only';
    disappearingMessages: boolean;
    disappearingMessagesDuration: number;
    allowMemberInvites: boolean;
    requireAdminApproval: boolean;
    muteNotifications: boolean;
    showReadReceipts: boolean;
  };
  pinnedMessages: string[];
  rules?: string[];
  category?: string;
  isPublic: boolean;
  inviteLink?: string;
}

interface GroupInfoModalProps {
  visible: boolean;
  onClose: () => void;
  groupInfo: GroupInfo | null;
  members: GroupMember[];
  currentUserRole: 'owner' | 'admin' | 'member';
  onEditGroup?: () => void;
  onViewMembers?: () => void;
  onLeaveGroup?: () => void;
}

export const GroupInfoModal: React.FC<GroupInfoModalProps> = ({
  visible,
  onClose,
  groupInfo,
  members,
  currentUserRole,
  onEditGroup,
  onViewMembers,
  onLeaveGroup,
}) => {
  if (!groupInfo) return null;

  const canEdit = currentUserRole === 'owner' || currentUserRole === 'admin';

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <Pressable
        style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'flex-end',
        }}
        onPress={onClose}
      >
        <View
          style={{
            backgroundColor: 'white',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            maxHeight: '80%',
          }}
        >
          {/* Header */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
              borderBottomWidth: 1,
              borderBottomColor: '#E5E7EB',
            }}
          >
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#333' }}>
              Group Info
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={{ flex: 1 }}>
            {/* Group Avatar and Name */}
            <View style={{ alignItems: 'center', padding: 20 }}>
              {groupInfo.avatar ? (
                <Image
                  source={{ uri: groupInfo.avatar }}
                  style={{
                    width: 100,
                    height: 100,
                    borderRadius: 50,
                    marginBottom: 16,
                  }}
                />
              ) : (
                <View
                  style={{
                    width: 100,
                    height: 100,
                    borderRadius: 50,
                    backgroundColor: '#87CEEB',
                    justifyContent: 'center',
                    alignItems: 'center',
                    marginBottom: 16,
                  }}
                >
                  <Ionicons name="people" size={40} color="white" />
                </View>
              )}
              
              <Text style={{ fontSize: 24, fontWeight: 'bold', color: '#333', textAlign: 'center' }}>
                {groupInfo.name}
              </Text>
              
              {groupInfo.description && (
                <Text style={{ fontSize: 16, color: '#666', textAlign: 'center', marginTop: 8 }}>
                  {groupInfo.description}
                </Text>
              )}
              
              <Text style={{ fontSize: 14, color: '#999', marginTop: 8 }}>
                {members.length} members
              </Text>
            </View>

            {/* Action Buttons */}
            <View style={{ paddingHorizontal: 20, paddingBottom: 20 }}>
              <TouchableOpacity
                onPress={onViewMembers}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 16,
                  paddingHorizontal: 16,
                  backgroundColor: '#F8F9FA',
                  borderRadius: 12,
                  marginBottom: 12,
                }}
              >
                <Ionicons name="people-outline" size={24} color="#87CEEB" />
                <Text style={{ marginLeft: 16, fontSize: 16, color: '#333', flex: 1 }}>
                  View Members
                </Text>
                <Ionicons name="chevron-forward" size={20} color="#999" />
              </TouchableOpacity>

              {canEdit && (
                <TouchableOpacity
                  onPress={onEditGroup}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingVertical: 16,
                    paddingHorizontal: 16,
                    backgroundColor: '#F8F9FA',
                    borderRadius: 12,
                    marginBottom: 12,
                  }}
                >
                  <Ionicons name="create-outline" size={24} color="#87CEEB" />
                  <Text style={{ marginLeft: 16, fontSize: 16, color: '#333', flex: 1 }}>
                    Edit Group
                  </Text>
                  <Ionicons name="chevron-forward" size={20} color="#999" />
                </TouchableOpacity>
              )}

              <TouchableOpacity
                onPress={onLeaveGroup}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingVertical: 16,
                  paddingHorizontal: 16,
                  backgroundColor: '#FEE2E2',
                  borderRadius: 12,
                }}
              >
                <Ionicons name="exit-outline" size={24} color="#EF4444" />
                <Text style={{ marginLeft: 16, fontSize: 16, color: '#EF4444', flex: 1 }}>
                  Leave Group
                </Text>
              </TouchableOpacity>
            </View>

            {/* Group Details */}
            <View style={{ paddingHorizontal: 20, paddingBottom: 20 }}>
              <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#333', marginBottom: 16 }}>
                Group Details
              </Text>
              
              <View style={{ backgroundColor: '#F8F9FA', borderRadius: 12, padding: 16 }}>
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
                  <Text style={{ color: '#666' }}>Created</Text>
                  <Text style={{ color: '#333' }}>
                    {groupInfo.createdAt.toLocaleDateString()}
                  </Text>
                </View>
                
                <View style={{ flexDirection: 'row', justifyContent: 'space-between', marginBottom: 12 }}>
                  <Text style={{ color: '#666' }}>Privacy</Text>
                  <Text style={{ color: '#333' }}>
                    {groupInfo.isPublic ? 'Public' : 'Private'}
                  </Text>
                </View>
                
                <View style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
                  <Text style={{ color: '#666' }}>Messages</Text>
                  <Text style={{ color: '#333' }}>
                    {groupInfo.settings.whoCanSendMessages === 'everyone' ? 'All members' : 'Admins only'}
                  </Text>
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
      </Pressable>
    </Modal>
  );
};
