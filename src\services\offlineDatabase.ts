/**
 * Comprehensive Offline Database Service for IraChat
 * Enhanced SQLite implementation with all necessary tables for offline functionality
 * Similar to WhatsApp's local database structure
 */

import * as SQLite from 'expo-sqlite';
import { Message, Chat, User, Contact } from '../types';

export interface DatabaseConfig {
  name: string;
  version: number;
  enableWAL: boolean; // Write-Ahead Logging for better performance
  enableForeignKeys: boolean;
  cacheSize: number; // SQLite cache size
}

export interface SyncStatus {
  status: 'pending' | 'synced' | 'failed' | 'conflict';
  lastSyncAttempt?: number;
  retryCount: number;
  errorMessage?: string;
}

export interface LocalMessage extends Message {
  localId: string;
  syncStatus: SyncStatus;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt?: number;
}

export interface LocalChat extends Chat {
  localId: string;
  syncStatus: SyncStatus;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt?: number;
  lastMessageId?: string;
  lastMessageText?: string;
  lastMessageTimestamp?: number;
  unreadCount: number;
  isPinned: boolean;
  pinnedAt?: number;
  isArchived: boolean;
  archivedAt?: number;
  isMuted: boolean;
  mutedUntil?: number;
}

export interface LocalContact extends Contact {
  localId: string;
  syncStatus: SyncStatus;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt?: number;
  isBlocked: boolean;
  blockedAt?: number;
  isFavorite: boolean;
  favoritedAt?: number;
}

export interface LocalUser {
  localId: string;
  syncStatus: SyncStatus;
  createdAt: number;
  updatedAt: number;
  isDeleted: boolean;
  deletedAt?: number;
  lastSeen?: number;
  isOnline: boolean;
  profileCacheExpiry?: number;
}

export type LocalUserWithData = LocalUser & User;

export interface MediaMetadata {
  id: string;
  messageId: string;
  type: 'image' | 'video' | 'audio' | 'document';
  fileName: string;
  fileSize: number;
  mimeType: string;
  localPath?: string;
  remoteUrl?: string;
  thumbnailPath?: string;
  duration?: number; // For audio/video
  width?: number; // For images/videos
  height?: number; // For images/videos
  isDownloaded: boolean;
  downloadProgress: number;
  uploadProgress: number;
  syncStatus: SyncStatus;
  createdAt: number;
  updatedAt: number;
}

export interface AppSettings {
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'object';
  userId?: string;
  syncStatus: SyncStatus;
  createdAt: number;
  updatedAt: number;
}

class OfflineDatabaseService {
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;
  private readonly config: DatabaseConfig = {
    name: 'irachat_offline.db',
    version: 1,
    enableWAL: true,
    enableForeignKeys: true,
    cacheSize: 2000, // 2MB cache
  };

  async initialize(): Promise<void> {
    // If already initialized, return immediately
    if (this.isInitialized) return;

    // If initialization is in progress, wait for it to complete
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    // Start initialization and store the promise
    this.initializationPromise = this.performInitialization();

    try {
      await this.initializationPromise;
    } finally {
      // Clear the promise once initialization is complete (success or failure)
      this.initializationPromise = null;
    }
  }

  private async performInitialization(): Promise<void> {
    try {
      console.log('🗄️ Initializing offline database...');

      // Use a very simple database name for Android compatibility
      try {
        this.db = await SQLite.openDatabaseAsync('irachat.db');
        console.log('✅ Database opened successfully');

        // Test database connection immediately
        await this.testDatabaseConnection();
        console.log('✅ Database connection verified');

      } catch (dbError) {
        console.error('❌ Failed to open database:', dbError);
        this.isInitialized = false;
        throw new Error(`Failed to open database: ${dbError}`);
      }

      // Create essential tables first with error handling
      console.log('📱 Creating essential tables...');
      await this.createEssentialTablesWithRetry();
      console.log('✅ Essential tables created successfully');

      // Run database migrations to fix schema issues
      console.log('📱 Running database migrations...');
      await this.runMigrations();
      console.log('✅ Database migrations completed');

      // Create all other tables with error handling
      console.log('📱 Creating additional tables...');
      await this.createTablesWithRetry();
      console.log('✅ Additional tables created successfully');

      // Create performance indexes with error handling
      console.log('📱 Creating database indexes...');
      await this.createIndexesWithRetry();
      console.log('✅ Database indexes created');

      this.isInitialized = true;
      console.log('✅ Offline database initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize offline database:', error);
      this.isInitialized = false;
      // Clean up database reference on failure
      if (this.db) {
        try {
          await this.db.closeAsync();
        } catch (closeError) {
          console.warn('⚠️ Error closing database:', closeError);
        }
        this.db = null;
      }
      console.warn('⚠️ App will continue without offline database support');
      throw error;
    }
  }

  /**
   * Test database connection to ensure it's working
   */
  private async testDatabaseConnection(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      // Simple test query
      await this.db.execAsync('SELECT 1');
      console.log('✅ Database connection test passed');
    } catch (error) {
      console.error('❌ Database connection test failed:', error);
      throw new Error(`Database connection test failed: ${error}`);
    }
  }

  /**
   * Create essential tables with retry logic
   */
  private async createEssentialTablesWithRetry(maxRetries: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.createEssentialTables();
        return;
      } catch (error) {
        console.error(`❌ Essential tables creation attempt ${attempt} failed:`, error);
        if (attempt === maxRetries) {
          throw error;
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  /**
   * Create tables with retry logic
   */
  private async createTablesWithRetry(maxRetries: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.createTablesWithoutConstraints();
        return;
      } catch (error) {
        console.error(`❌ Tables creation attempt ${attempt} failed:`, error);
        if (attempt === maxRetries) {
          throw error;
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  /**
   * Create indexes with retry logic
   */
  private async createIndexesWithRetry(maxRetries: number = 3): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.createIndexes();
        return;
      } catch (error) {
        console.error(`❌ Indexes creation attempt ${attempt} failed:`, error);
        if (attempt === maxRetries) {
          throw error;
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }

  /**
   * Force reinitialization of the database (useful for applying migrations)
   */
  async forceReinitialize(): Promise<void> {
    console.log('🔄 Forcing database reinitialization...');

    if (this.db) {
      try {
        await this.db.closeAsync();
      } catch (error) {
        console.warn('⚠️ Error closing database:', error);
      }
    }

    this.db = null;
    this.isInitialized = false;

    await this.initialize();
    console.log('✅ Database reinitialization completed');
  }

  /**
   * Run database migrations to update schema
   */
  private async runMigrations(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      // Create migrations table if it doesn't exist
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS migrations (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          version INTEGER UNIQUE NOT NULL,
          name TEXT NOT NULL,
          executed_at INTEGER NOT NULL
        )
      `);

      // Get current migration version
      const result = await this.db.getAllAsync(`SELECT MAX(version) as version FROM migrations`) as any[];
      const currentVersion = (result[0] as any)?.version || 0;

      console.log(`📊 Current database version: ${currentVersion}`);

      // Define migrations
      const migrations = [
        {
          version: 1,
          name: 'add_missing_columns_v1',
          up: async () => {
            await this.fixDatabaseSchema();
          }
        },
        {
          version: 2,
          name: 'add_duration_and_retry_columns',
          up: async () => {
            await this.addMissingColumns();
          }
        },
        {
          version: 3,
          name: 'add_isPinned_column_to_messages',
          up: async () => {
            await this.addIsPinnedColumn();
          }
        },
        {
          version: 4,
          name: 'add_forwardedFrom_column_to_messages',
          up: async () => {
            await this.addForwardedFromColumn();
          }
        },
        {
          version: 5,
          name: 'add_participants_column_to_chats',
          up: async () => {
            await this.addParticipantsColumn();
          }
        },
        {
          version: 6,
          name: 'add_lastSyncAttempt_column_to_messages',
          up: async () => {
            await this.addLastSyncAttemptColumn();
          }
        },
        {
          version: 7,
          name: 'add_retryCount_column_to_messages',
          up: async () => {
            await this.addRetryCountColumn();
          }
        },
        {
          version: 8,
          name: 'add_errorMessage_column_to_messages',
          up: async () => {
            await this.addErrorMessageColumn();
          }
        },
        {
          version: 9,
          name: 'add_deletedAt_column_to_messages',
          up: async () => {
            await this.addDeletedAtColumn();
          }
        },
        {
          version: 10,
          name: 'add_localId_column_to_chats',
          up: async () => {
            await this.addLocalIdColumn();
          }
        },
        // Add future migrations here
      ];

      // Run pending migrations
      for (const migration of migrations) {
        if (migration.version > currentVersion) {
          console.log(`🔄 Running migration ${migration.version}: ${migration.name}`);
          await migration.up();
          await this.db.runAsync(`
            INSERT INTO migrations (version, name, executed_at)
            VALUES (?, ?, ?)
          `, [migration.version, migration.name, Date.now()]);
          console.log(`✅ Migration ${migration.version} completed`);
        }
      }

      console.log('✅ All migrations completed');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Fix database schema issues by adding missing columns
   */
  private async fixDatabaseSchema(): Promise<void> {
    if (!this.db) {
      console.log('🔧 Database not initialized, initializing first...');
      await this.initialize();
      return;
    }

    try {
      console.log('🔧 Fixing database schema issues...');

      // Check and fix chats table
      const chatTableInfo = await this.db.getAllAsync(`PRAGMA table_info(chats);`);
      const chatHasIsDeletedColumn = chatTableInfo.some((column: any) => column.name === 'isDeleted');
      const chatHasSyncStatusColumn = chatTableInfo.some((column: any) => column.name === 'syncStatus');

      if (!chatHasIsDeletedColumn) {
        console.log('🗑️ Adding missing isDeleted column to chats table...');
        await this.db.execAsync(`ALTER TABLE chats ADD COLUMN isDeleted INTEGER DEFAULT 0;`);
        console.log('✅ IsDeleted column added to chats table');
      }

      if (!chatHasSyncStatusColumn) {
        console.log('🔄 Adding missing syncStatus column to chats table...');
        await this.db.execAsync(`ALTER TABLE chats ADD COLUMN syncStatus TEXT DEFAULT 'pending';`);
        console.log('✅ SyncStatus column added to chats table');
      }

      // Check and fix messages table
      const messageTableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
      const messageColumns = messageTableInfo.map((col: any) => col.name);

      const requiredColumns = [
        { name: 'text', type: 'TEXT', default: null },
        { name: 'syncStatus', type: 'TEXT', default: "'pending'" },
        { name: 'isDeleted', type: 'INTEGER', default: '0' },
        { name: 'mediaUrl', type: 'TEXT', default: null },
        { name: 'mediaType', type: 'TEXT', default: null },
        { name: 'fileName', type: 'TEXT', default: null },
        { name: 'fileSize', type: 'INTEGER', default: null },
        { name: 'replyToMessageId', type: 'TEXT', default: null },
        { name: 'reactions', type: 'TEXT', default: null },
        { name: 'mentions', type: 'TEXT', default: null },
        { name: 'isEdited', type: 'INTEGER', default: '0' },
        { name: 'editedAt', type: 'INTEGER', default: null }
      ];

      for (const column of requiredColumns) {
        if (!messageColumns.includes(column.name)) {
          console.log(`📝 Adding missing ${column.name} column to messages table...`);
          const defaultClause = column.default ? ` DEFAULT ${column.default}` : '';
          await this.db.execAsync(`ALTER TABLE messages ADD COLUMN ${column.name} ${column.type}${defaultClause};`);
          console.log(`✅ ${column.name} column added to messages table`);
        }
      }

      // Special handling for text column - copy from content if needed
      if (!messageColumns.includes('text') && messageColumns.includes('content')) {
        console.log('📝 Copying content to text column...');
        await this.db.execAsync(`UPDATE messages SET text = content WHERE text IS NULL AND content IS NOT NULL;`);
        console.log('✅ Content copied to text column');
      }

      console.log('✅ Database schema fix completed');
    } catch (error) {
      console.error('❌ Failed to fix database schema:', error);
      throw error;
    }
  }



  private async createEssentialTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      console.log('🏗️ Creating chats table...');
      // Comprehensive chats table with all necessary columns
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS chats (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          avatar TEXT,
          partnerId TEXT,
          partnerName TEXT,
          partnerAvatar TEXT,
          isGroup INTEGER DEFAULT 0,
          lastMessage TEXT,
          lastMessageTime INTEGER,
          lastMessageSender TEXT,
          lastMessageType TEXT DEFAULT 'text',
          unreadCount INTEGER DEFAULT 0,
          isOnline INTEGER DEFAULT 0,
          lastSeen INTEGER,
          isPinned INTEGER DEFAULT 0,
          isArchived INTEGER DEFAULT 0,
          isMuted INTEGER DEFAULT 0,
          isDeleted INTEGER DEFAULT 0,
          syncStatus TEXT DEFAULT 'pending',
          createdAt INTEGER NOT NULL,
          updatedAt INTEGER NOT NULL
        )
      `);
      console.log('✅ Chats table created');

      console.log('🏗️ Creating messages table...');
      // Essential messages table with all required columns
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS messages (
          id TEXT PRIMARY KEY,
          chatId TEXT NOT NULL,
          text TEXT NOT NULL,
          content TEXT,
          senderId TEXT NOT NULL,
          senderName TEXT,
          timestamp INTEGER NOT NULL,
          type TEXT DEFAULT 'text',
          status TEXT DEFAULT 'sent',
          syncStatus TEXT DEFAULT 'pending',
          isDeleted INTEGER DEFAULT 0,
          mediaUrl TEXT,
          mediaType TEXT,
          mediaThumbnail TEXT,
          fileName TEXT,
          fileSize INTEGER,
          duration INTEGER,
          width INTEGER,
          height INTEGER,
          replyToMessageId TEXT,
          replyToText TEXT,
          replyToSenderName TEXT,
          replyToType TEXT,
          reactions TEXT,
          mentions TEXT,
          isEdited INTEGER DEFAULT 0,
          editedAt INTEGER,
          isStarred INTEGER DEFAULT 0,
          starredAt INTEGER,
          deliveryStatus TEXT DEFAULT 'sent',
          readBy TEXT,
          deliveredTo TEXT,
          createdAt INTEGER NOT NULL,
          updatedAt INTEGER NOT NULL
        )
      `);
      console.log('✅ Messages table created');

      console.log('🏗️ Creating app_settings table...');
      // Simple settings table
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS app_settings (
          key TEXT PRIMARY KEY,
          value TEXT NOT NULL,
          type TEXT DEFAULT 'string',
          userId TEXT,
          syncStatus TEXT DEFAULT 'pending',
          lastSyncAttempt INTEGER,
          retryCount INTEGER DEFAULT 0,
          createdAt INTEGER DEFAULT (strftime('%s', 'now')),
          updatedAt INTEGER DEFAULT (strftime('%s', 'now'))
        )
      `);
      console.log('✅ App settings table created');

      console.log('🏗️ Creating message_reactions table...');
      // Message reactions table for emoji reactions
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS message_reactions (
          id TEXT PRIMARY KEY,
          messageId TEXT NOT NULL,
          userId TEXT NOT NULL,
          emoji TEXT NOT NULL,
          timestamp INTEGER NOT NULL,
          UNIQUE(messageId, userId)
        )
      `);
      console.log('✅ Message reactions table created');

      console.log('✅ All essential tables created successfully');
    } catch (error) {
      console.error('❌ Failed to create essential tables:', error);
      throw error;
    }
  }

  private async createTablesWithoutConstraints(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Users table
    try {
      console.log('🏗️ Creating users table...');
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS users (
          localId TEXT PRIMARY KEY,
          id TEXT UNIQUE,
          phoneNumber TEXT,
          email TEXT,
          name TEXT,
          avatar TEXT,
          bio TEXT,
          isOnline INTEGER DEFAULT 0,
          lastSeen INTEGER,
          profileCacheExpiry INTEGER,
          syncStatus TEXT NOT NULL DEFAULT 'pending',
          lastSyncAttempt INTEGER,
          retryCount INTEGER DEFAULT 0,
          errorMessage TEXT,
          createdAt INTEGER NOT NULL,
          updatedAt INTEGER NOT NULL,
          isDeleted INTEGER DEFAULT 0,
          deletedAt INTEGER
        );
      `);
      console.log('✅ Users table created');
    } catch (error) {
      console.error('❌ Failed to create users table:', error);
      throw error;
    }

    // Contacts table
    try {
      console.log('🏗️ Creating contacts table...');
      await this.db.execAsync(`
        CREATE TABLE IF NOT EXISTS contacts (
          localId TEXT PRIMARY KEY,
          id TEXT UNIQUE,
          userId TEXT,
          phoneNumber TEXT,
          email TEXT,
          name TEXT,
          avatar TEXT,
          isOnline INTEGER DEFAULT 0,
          lastSeen INTEGER,
          isBlocked INTEGER DEFAULT 0,
          blockedAt INTEGER,
          isFavorite INTEGER DEFAULT 0,
          favoritedAt INTEGER,
          syncStatus TEXT NOT NULL DEFAULT 'pending',
          lastSyncAttempt INTEGER,
          retryCount INTEGER DEFAULT 0,
          errorMessage TEXT,
          createdAt INTEGER NOT NULL,
          updatedAt INTEGER NOT NULL,
          isDeleted INTEGER DEFAULT 0,
          deletedAt INTEGER,
          FOREIGN KEY (userId) REFERENCES users(id)
        );
      `);
      console.log('✅ Contacts table created');
    } catch (error) {
      console.error('❌ Failed to create contacts table:', error);
      throw error;
    }

    // Chats table already created in createEssentialTables - no need to recreate

    // Create index for faster chat queries
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_chats_updated
      ON chats(updatedAt DESC);
    `);

    // Chat participants table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS chat_participants (
        id TEXT PRIMARY KEY,
        chatId TEXT NOT NULL,
        userId TEXT NOT NULL,
        role TEXT DEFAULT 'member',
        joinedAt INTEGER NOT NULL,
        leftAt INTEGER,
        isActive INTEGER DEFAULT 1,
        syncStatus TEXT NOT NULL DEFAULT 'pending',
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (chatId) REFERENCES chats(id),
        FOREIGN KEY (userId) REFERENCES users(id),
        UNIQUE(chatId, userId)
      );
    `);

    // Skip creating messages table here - already created in createEssentialTables
    // The enhanced schema will be applied through migrations

    // Media metadata table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS media_metadata (
        id TEXT PRIMARY KEY,
        messageId TEXT NOT NULL,
        type TEXT NOT NULL,
        fileName TEXT NOT NULL,
        fileSize INTEGER NOT NULL,
        mimeType TEXT NOT NULL,
        localPath TEXT,
        remoteUrl TEXT,
        thumbnailPath TEXT,
        duration INTEGER,
        width INTEGER,
        height INTEGER,
        isDownloaded INTEGER DEFAULT 0,
        downloadProgress INTEGER DEFAULT 0,
        uploadProgress INTEGER DEFAULT 0,
        syncStatus TEXT NOT NULL DEFAULT 'pending',
        lastSyncAttempt INTEGER,
        retryCount INTEGER DEFAULT 0,
        errorMessage TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (messageId) REFERENCES messages(id)
      );
    `);

    // Skip creating app_settings table here - already created in createEssentialTables
    // The enhanced schema will be applied through migrations

    // Sync queue table for offline operations
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS sync_queue (
        id TEXT PRIMARY KEY,
        operation TEXT NOT NULL,
        tableName TEXT NOT NULL,
        recordId TEXT NOT NULL,
        data TEXT, -- JSON string
        priority INTEGER DEFAULT 0,
        maxRetries INTEGER DEFAULT 3,
        retryCount INTEGER DEFAULT 0,
        lastAttempt INTEGER,
        nextAttempt INTEGER,
        status TEXT NOT NULL DEFAULT 'pending',
        errorMessage TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      );
    `);

    // Offline interactions table for update interactions
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS offline_interactions (
        id TEXT PRIMARY KEY,
        type TEXT NOT NULL,
        data TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        synced INTEGER DEFAULT 0
      );
    `);

    // Dedicated outbox table for pending messages
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS outbox (
        id TEXT PRIMARY KEY,
        messageId TEXT NOT NULL,
        chatId TEXT NOT NULL,
        senderId TEXT NOT NULL,
        senderName TEXT,
        content TEXT NOT NULL,
        type TEXT DEFAULT 'text',
        mediaUrl TEXT,
        mediaType TEXT,
        fileName TEXT,
        fileSize INTEGER,
        thumbnailUrl TEXT,
        replyToMessageId TEXT,
        priority INTEGER DEFAULT 0,
        status TEXT DEFAULT 'pending', -- pending, sending, sent, failed
        retryCount INTEGER DEFAULT 0,
        maxRetries INTEGER DEFAULT 5,
        lastAttempt INTEGER,
        nextAttempt INTEGER,
        errorMessage TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        FOREIGN KEY (messageId) REFERENCES messages(id),
        FOREIGN KEY (chatId) REFERENCES chats(id)
      );
    `);

    // Privacy settings table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS privacy_settings (
        userId TEXT PRIMARY KEY,
        lastSeen TEXT,
        lastSeenCustomContacts TEXT,
        profilePhoto TEXT,
        profilePhotoCustomContacts TEXT,
        status TEXT,
        statusCustomContacts TEXT,
        about TEXT,
        aboutCustomContacts TEXT,
        readReceipts INTEGER,
        groupsAddMe TEXT,
        groupsAddMeCustomContacts TEXT,
        liveLocation INTEGER,
        callsFrom TEXT,
        blockedContacts TEXT,
        twoStepVerification INTEGER,
        disappearingMessages INTEGER,
        disappearingMessagesDuration TEXT,
        disappearingMessagesScope TEXT,
        disappearingMessagesCustomContacts TEXT,
        disappearingMessagesStorage TEXT,
        screenshotNotification INTEGER,
        screenshotControl INTEGER,
        screenshotControlScope TEXT,
        screenshotControlCustomContacts TEXT,
        onlineStatus TEXT,
        forwardedMessages INTEGER,
        forwardedMessagesScope TEXT,
        forwardedMessagesCustomContacts TEXT,
        autoDownloadMedia TEXT,
        securityNotifications INTEGER,
        createdAt INTEGER,
        updatedAt INTEGER,
        syncStatus TEXT,
        lastSyncAttempt INTEGER
      );
    `);

    // Archived messages table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS archived_messages (
        id TEXT PRIMARY KEY,
        chatId TEXT,
        senderId TEXT,
        receiverId TEXT,
        content TEXT,
        timestamp INTEGER,
        archivedAt INTEGER,
        originalDisappearAt INTEGER
      );
    `);

    // Pending deletions table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS pending_deletions (
        messageId TEXT PRIMARY KEY,
        type TEXT,
        timestamp INTEGER
      );
    `);

    // Auto download queue table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS auto_download_queue (
        id TEXT PRIMARY KEY,
        url TEXT NOT NULL,
        type TEXT NOT NULL,
        fileName TEXT NOT NULL,
        fileSize INTEGER DEFAULT 0,
        chatId TEXT NOT NULL,
        messageId TEXT NOT NULL,
        senderId TEXT NOT NULL,
        priority TEXT DEFAULT 'normal',
        status TEXT DEFAULT 'pending',
        localPath TEXT,
        error TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER
      );
    `);

    // Auto download settings table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS auto_download_settings (
        userId TEXT PRIMARY KEY,
        images TEXT DEFAULT 'wifi',
        videos TEXT DEFAULT 'wifi',
        audio TEXT DEFAULT 'wifi',
        documents TEXT DEFAULT 'never',
        maxFileSize INTEGER DEFAULT 25,
        onlyInChats INTEGER DEFAULT 1,
        updatedAt INTEGER NOT NULL
      );
    `);

    // Remembered media table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS remembered_media (
        id TEXT PRIMARY KEY,
        originalMediaId TEXT NOT NULL,
        userId TEXT NOT NULL,
        mediaUrl TEXT NOT NULL,
        localPath TEXT,
        type TEXT NOT NULL,
        fileName TEXT NOT NULL,
        fileSize INTEGER,
        caption TEXT,
        sourceChat TEXT NOT NULL,
        sourceChatName TEXT NOT NULL,
        originalSender TEXT NOT NULL,
        originalSenderName TEXT NOT NULL,
        tags TEXT DEFAULT '[]',
        notes TEXT,
        createdAt INTEGER NOT NULL,
        syncStatus TEXT DEFAULT 'pending'
      );
    `);

    // Remembered collections table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS remembered_collections (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        mediaIds TEXT DEFAULT '[]',
        isPrivate INTEGER DEFAULT 0,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      );
    `);

    // Pending archives table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS pending_archives (
        messageId TEXT PRIMARY KEY,
        timestamp INTEGER
      );
    `);

    // Screenshot attempts table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS screenshot_attempts (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        attemptedBy TEXT NOT NULL,
        chatId TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        blocked INTEGER DEFAULT 0,
        reason TEXT,
        syncStatus TEXT NOT NULL DEFAULT 'pending',
        createdAt INTEGER NOT NULL DEFAULT (strftime('%s', 'now') * 1000),
        FOREIGN KEY (userId) REFERENCES users(id),
        FOREIGN KEY (attemptedBy) REFERENCES users(id),
        FOREIGN KEY (chatId) REFERENCES chats(id)
      );
    `);

    // Cached user updates table for profile pages
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS cached_user_updates (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        userName TEXT NOT NULL,
        userAvatar TEXT,
        content TEXT,
        caption TEXT,
        type TEXT NOT NULL,
        mediaUrl TEXT,
        privacy TEXT DEFAULT 'public',
        timestamp INTEGER NOT NULL,
        expiresAt INTEGER,
        likes TEXT DEFAULT '[]',
        comments TEXT DEFAULT '[]',
        shares TEXT DEFAULT '[]',
        views TEXT DEFAULT '[]',
        downloads TEXT DEFAULT '[]',
        location TEXT,
        tags TEXT DEFAULT '[]',
        mentions TEXT DEFAULT '[]',
        musicTrack TEXT,
        cachedAt INTEGER NOT NULL
      );
    `);

    // Cached chat list table for offline access
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS cached_chat_list (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        isGroup INTEGER DEFAULT 0,
        lastMessage TEXT,
        lastMessageAt INTEGER,
        lastMessageType TEXT DEFAULT 'text',
        unreadCount INTEGER DEFAULT 0,
        avatar TEXT,
        isArchived INTEGER DEFAULT 0,
        isPinned INTEGER DEFAULT 0,
        isMuted INTEGER DEFAULT 0,
        participants TEXT DEFAULT '[]',
        timestamp INTEGER NOT NULL,
        createdAt INTEGER,
        updatedAt INTEGER
      );
    `);

    // Enhanced cached avatars table with versioning
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS cached_avatars (
        userId TEXT PRIMARY KEY,
        url TEXT NOT NULL,
        localPath TEXT,
        timestamp INTEGER NOT NULL,
        size INTEGER DEFAULT 150,
        version TEXT, -- Version tag from server
        lastChecked INTEGER, -- Last time we checked for updates
        downloadedAt INTEGER, -- When the file was downloaded
        fileSize INTEGER, -- Size of cached file
        isValid INTEGER DEFAULT 1 -- Whether the cached file is still valid
      );
    `);

    // Cached media files table for offline access
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS cached_media (
        id TEXT PRIMARY KEY,
        messageId TEXT NOT NULL,
        chatId TEXT NOT NULL,
        mediaUrl TEXT NOT NULL,
        localPath TEXT,
        thumbnailPath TEXT,
        mediaType TEXT NOT NULL, -- image, video, audio, document
        fileName TEXT,
        fileSize INTEGER,
        width INTEGER,
        height INTEGER,
        duration INTEGER, -- For audio/video
        downloadedAt INTEGER,
        lastAccessed INTEGER,
        isValid INTEGER DEFAULT 1,
        version TEXT, -- Version tag from server
        FOREIGN KEY (messageId) REFERENCES messages(id),
        FOREIGN KEY (chatId) REFERENCES chats(id)
      );
    `);

    // Message actions tables
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS message_reactions (
        id TEXT PRIMARY KEY,
        messageId TEXT NOT NULL,
        userId TEXT NOT NULL,
        emoji TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        UNIQUE(messageId, userId)
      );
    `);

    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS starred_messages (
        id TEXT PRIMARY KEY,
        messageId TEXT NOT NULL,
        chatId TEXT NOT NULL,
        userId TEXT NOT NULL,
        content TEXT,
        type TEXT,
        timestamp INTEGER,
        starredAt INTEGER NOT NULL,
        senderId TEXT
      );
    `);

    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS pinned_messages (
        id TEXT PRIMARY KEY,
        messageId TEXT NOT NULL,
        chatId TEXT NOT NULL,
        content TEXT,
        senderId TEXT,
        timestamp INTEGER,
        pinnedBy TEXT NOT NULL,
        pinnedAt INTEGER NOT NULL,
        expiresAt INTEGER,
        type TEXT
      );
    `);

    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS message_edits (
        id TEXT PRIMARY KEY,
        messageId TEXT NOT NULL,
        chatId TEXT NOT NULL,
        editedBy TEXT NOT NULL,
        editedAt INTEGER NOT NULL,
        originalContent TEXT,
        newContent TEXT,
        originalSenderId TEXT
      );
    `);

    // Add disappearing message fields to existing messages table
    await this.db.execAsync(`
      ALTER TABLE messages ADD COLUMN disappearAt INTEGER;
    `).catch(() => {}); // Ignore if column already exists

    await this.db.execAsync(`
      ALTER TABLE messages ADD COLUMN isForwardable INTEGER DEFAULT 1;
    `).catch(() => {}); // Ignore if column already exists

    // Add missing columns to messages table with proper checks
    try {
      await this.db.execAsync(`
        ALTER TABLE messages ADD COLUMN caption TEXT;
      `);
      console.log('✅ Added caption column to messages');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE messages ADD COLUMN isArchived INTEGER DEFAULT 0;
      `);
      console.log('✅ Added isArchived column to messages');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE messages ADD COLUMN archivedAt INTEGER;
      `);
      console.log('✅ Added archivedAt column to messages');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE messages ADD COLUMN edited INTEGER DEFAULT 0;
      `);
      console.log('✅ Added edited column to messages');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE messages ADD COLUMN editedAt INTEGER;
      `);
      console.log('✅ Added editedAt column to messages');
    } catch (error) {
      // Column already exists, ignore
    }

    // Add missing columns to app_settings table
    try {
      await this.db.execAsync(`
        ALTER TABLE app_settings ADD COLUMN userId TEXT;
      `);
      console.log('✅ Added userId column to app_settings');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE app_settings ADD COLUMN createdAt INTEGER DEFAULT (strftime('%s', 'now'));
      `);
      console.log('✅ Added createdAt column to app_settings');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE app_settings ADD COLUMN updatedAt INTEGER DEFAULT (strftime('%s', 'now'));
      `);
      console.log('✅ Added updatedAt column to app_settings');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE app_settings ADD COLUMN syncStatus TEXT DEFAULT 'pending';
      `);
      console.log('✅ Added syncStatus column to app_settings');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE app_settings ADD COLUMN lastSyncAttempt INTEGER;
      `);
      console.log('✅ Added lastSyncAttempt column to app_settings');
    } catch (error) {
      // Column already exists, ignore
    }

    // Add missing columns to chats table
    try {
      await this.db.execAsync(`
        ALTER TABLE chats ADD COLUMN pinnedMessageId TEXT;
      `);
      console.log('✅ Added pinnedMessageId column to chats');
    } catch (error) {
      // Column already exists, ignore
    }

    try {
      await this.db.execAsync(`
        ALTER TABLE chats ADD COLUMN pinnedMessageExpiresAt INTEGER;
      `);
      console.log('✅ Added pinnedMessageExpiresAt column to chats');
    } catch (error) {
      // Column already exists, ignore
    }

    // Add screenshot control fields to existing privacy_settings table
    await this.db.execAsync(`
      ALTER TABLE privacy_settings ADD COLUMN screenshotControl INTEGER DEFAULT 1;
    `).catch(() => {}); // Ignore if column already exists

    await this.db.execAsync(`
      ALTER TABLE privacy_settings ADD COLUMN screenshotControlScope TEXT DEFAULT 'contacts';
    `).catch(() => {}); // Ignore if column already exists

    await this.db.execAsync(`
      ALTER TABLE privacy_settings ADD COLUMN screenshotControlCustomContacts TEXT DEFAULT '[]';
    `).catch(() => {}); // Ignore if column already exists

    // Add price history and old price columns to business_posts table
    await this.db.execAsync(`
      ALTER TABLE business_posts ADD COLUMN priceHistory TEXT DEFAULT '[]';
    `).catch(() => {}); // Ignore if column already exists

    await this.db.execAsync(`
      ALTER TABLE business_posts ADD COLUMN oldPrice REAL;
    `).catch(() => {}); // Ignore if column already exists

    // Business users table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_users (
        id TEXT PRIMARY KEY,
        email TEXT NOT NULL,
        phoneNumber TEXT,
        fullName TEXT,
        userType TEXT NOT NULL,
        isVerified INTEGER DEFAULT 0,
        subscriptionStatus TEXT,
        subscriptionStartDate INTEGER,
        subscriptionEndDate INTEGER,
        lastPaymentDate INTEGER,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Business profiles table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_profiles (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        businessName TEXT NOT NULL,
        businessType TEXT NOT NULL,
        description TEXT,
        logo TEXT,
        coverImage TEXT,
        contactInfo TEXT,
        location TEXT,
        establishedYear INTEGER,
        website TEXT,
        socialMedia TEXT,
        isVerified INTEGER DEFAULT 0,
        verificationDate INTEGER,
        verificationDocuments TEXT,
        totalPosts INTEGER DEFAULT 0,
        totalViews INTEGER DEFAULT 0,
        totalLikes INTEGER DEFAULT 0,
        totalComments INTEGER DEFAULT 0,
        totalShares INTEGER DEFAULT 0,
        totalDownloads INTEGER DEFAULT 0,
        allowDirectMessages INTEGER DEFAULT 1,
        allowPhoneCalls INTEGER DEFAULT 1,
        businessHours TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Business posts table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_posts (
        id TEXT PRIMARY KEY,
        businessId TEXT NOT NULL,
        businessName TEXT NOT NULL,
        businessLogo TEXT,
        businessType TEXT NOT NULL,
        isVerified INTEGER DEFAULT 0,
        title TEXT NOT NULL,
        description TEXT,
        media TEXT,
        tags TEXT,
        category TEXT,
        price REAL,
        oldPrice REAL,
        priceHistory TEXT,
        currency TEXT,
        isNegotiable INTEGER DEFAULT 0,
        availability TEXT,
        location TEXT,
        views INTEGER DEFAULT 0,
        likes TEXT,
        comments TEXT,
        shares INTEGER DEFAULT 0,
        downloads INTEGER DEFAULT 0,
        isActive INTEGER DEFAULT 1,
        isPinned INTEGER DEFAULT 0,
        isPromoted INTEGER DEFAULT 0,
        promotionEndsAt INTEGER,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Business post interactions table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_post_interactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        postId TEXT NOT NULL,
        userId TEXT NOT NULL,
        type TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Business subscription updates table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_subscription_updates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        userId TEXT NOT NULL,
        updateData TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Business profile updates table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_profile_updates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        profileId TEXT NOT NULL,
        updateData TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Business chats table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_chats (
        id TEXT PRIMARY KEY,
        participants TEXT NOT NULL,
        participantNames TEXT NOT NULL,
        participantAvatars TEXT,
        businessId TEXT NOT NULL,
        businessName TEXT NOT NULL,
        customerId TEXT NOT NULL,
        customerName TEXT NOT NULL,
        productContext TEXT,
        lastMessageTime INTEGER NOT NULL,
        unreadCount TEXT,
        isActive INTEGER DEFAULT 1,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Business chat messages table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_chat_messages (
        id TEXT PRIMARY KEY,
        chatId TEXT NOT NULL,
        senderId TEXT NOT NULL,
        senderName TEXT NOT NULL,
        senderAvatar TEXT,
        receiverId TEXT NOT NULL,
        receiverName TEXT NOT NULL,
        content TEXT NOT NULL,
        type TEXT NOT NULL,
        mediaUrl TEXT,
        productReference TEXT,
        timestamp INTEGER NOT NULL,
        isRead INTEGER DEFAULT 0,
        isDelivered INTEGER DEFAULT 0,
        reactions TEXT,
        replyTo TEXT,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Business chat read status table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS business_chat_read_status (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        chatId TEXT NOT NULL,
        userId TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        syncStatus TEXT NOT NULL DEFAULT 'pending'
      );
    `);

    // Run migrations to add any missing columns to existing tables
    await this.migrateExistingTables();
  }

  private async migrateExistingTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    try {
      console.log('🔄 Checking for database migrations...');

      // Check if email column exists in users table
      const userTableInfo = await this.db.getAllAsync(`PRAGMA table_info(users);`);
      const hasEmailColumn = userTableInfo.some((column: any) => column.name === 'email');

      if (!hasEmailColumn) {
        console.log('📧 Adding email column to users table...');
        await this.db.execAsync(`ALTER TABLE users ADD COLUMN email TEXT;`);
        console.log('✅ Email column added to users table');
      }

      // Check if email column exists in contacts table
      const contactTableInfo = await this.db.getAllAsync(`PRAGMA table_info(contacts);`);
      const contactHasEmailColumn = contactTableInfo.some((column: any) => column.name === 'email');
      const contactHasLastSeenColumn = contactTableInfo.some((column: any) => column.name === 'lastSeen');
      const contactHasIsOnlineColumn = contactTableInfo.some((column: any) => column.name === 'isOnline');

      if (!contactHasEmailColumn) {
        console.log('📧 Adding email column to contacts table...');
        await this.db.execAsync(`ALTER TABLE contacts ADD COLUMN email TEXT;`);
        console.log('✅ Email column added to contacts table');
      }

      if (!contactHasLastSeenColumn) {
        console.log('⏰ Adding lastSeen column to contacts table...');
        await this.db.execAsync(`ALTER TABLE contacts ADD COLUMN lastSeen INTEGER;`);
        console.log('✅ LastSeen column added to contacts table');
      }

      if (!contactHasIsOnlineColumn) {
        console.log('🟢 Adding isOnline column to contacts table...');
        await this.db.execAsync(`ALTER TABLE contacts ADD COLUMN isOnline INTEGER DEFAULT 0;`);
        console.log('✅ IsOnline column added to contacts table');
      }

      // Check if avatar column exists in chats table
      const chatTableInfo = await this.db.getAllAsync(`PRAGMA table_info(chats);`);
      const hasAvatarColumn = chatTableInfo.some((column: any) => column.name === 'avatar');

      if (!hasAvatarColumn) {
        console.log('👤 Adding avatar column to chats table...');
        await this.db.execAsync(`ALTER TABLE chats ADD COLUMN avatar TEXT;`);
        console.log('✅ Avatar column added to chats table');
      }

      // Check if partnerAvatar column exists in chats table
      const hasPartnerAvatarColumn = chatTableInfo.some((column: any) => column.name === 'partnerAvatar');

      if (!hasPartnerAvatarColumn) {
        console.log('👤 Adding partnerAvatar column to chats table...');
        await this.db.execAsync(`ALTER TABLE chats ADD COLUMN partnerAvatar TEXT;`);
        console.log('✅ PartnerAvatar column added to chats table');
      }

      // Check if localId column exists in messages table
      const messageTableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
      const hasLocalIdColumn = messageTableInfo.some((column: any) => column.name === 'localId');

      if (!hasLocalIdColumn) {
        console.log('📝 Adding localId column to messages table...');
        await this.db.execAsync(`ALTER TABLE messages ADD COLUMN localId TEXT;`);
        console.log('✅ LocalId column added to messages table');
      }

      // Check if isDeleted column exists in messages table
      const hasIsDeletedColumn = messageTableInfo.some((column: any) => column.name === 'isDeleted');

      if (!hasIsDeletedColumn) {
        console.log('🗑️ Adding isDeleted column to messages table...');
        await this.db.execAsync(`ALTER TABLE messages ADD COLUMN isDeleted INTEGER DEFAULT 0;`);
        console.log('✅ IsDeleted column added to messages table');
      }

      // Check if other missing columns exist in messages table
      const hasReplyToColumn = messageTableInfo.some((column: any) => column.name === 'replyTo');
      const hasReactionsColumn = messageTableInfo.some((column: any) => column.name === 'reactions');
      const hasEditedAtColumn = messageTableInfo.some((column: any) => column.name === 'editedAt');
      const hasIsForwardedColumn = messageTableInfo.some((column: any) => column.name === 'isForwarded');
      const hasIsStarredColumn = messageTableInfo.some((column: any) => column.name === 'isStarred');
      const hasCaptionColumn = messageTableInfo.some((column: any) => column.name === 'caption');

      if (!hasReplyToColumn) {
        console.log('💬 Adding replyTo column to messages table...');
        await this.db.execAsync(`ALTER TABLE messages ADD COLUMN replyTo TEXT;`);
        console.log('✅ ReplyTo column added to messages table');
      }

      if (!hasReactionsColumn) {
        console.log('😀 Adding reactions column to messages table...');
        await this.db.execAsync(`ALTER TABLE messages ADD COLUMN reactions TEXT DEFAULT '[]';`);
        console.log('✅ Reactions column added to messages table');
      }

      if (!hasEditedAtColumn) {
        console.log('✏️ Adding editedAt column to messages table...');
        await this.db.execAsync(`ALTER TABLE messages ADD COLUMN editedAt INTEGER;`);
        console.log('✅ EditedAt column added to messages table');
      }

      if (!hasIsForwardedColumn) {
        console.log('📤 Adding isForwarded column to messages table...');
        await this.db.execAsync(`ALTER TABLE messages ADD COLUMN isForwarded INTEGER DEFAULT 0;`);
        console.log('✅ IsForwarded column added to messages table');
      }

      if (!hasIsStarredColumn) {
        console.log('⭐ Adding isStarred column to messages table...');
        await this.db.execAsync(`ALTER TABLE messages ADD COLUMN isStarred INTEGER DEFAULT 0;`);
        console.log('✅ IsStarred column added to messages table');
      }

      if (!hasCaptionColumn) {
        console.log('📝 Adding caption column to messages table...');
        await this.db.execAsync(`ALTER TABLE messages ADD COLUMN caption TEXT;`);
        console.log('✅ Caption column added to messages table');
      }

      // Check if chats_list table exists (WhatsApp-style chats list)
      const tablesResult = await this.db.getAllAsync(`
        SELECT name FROM sqlite_master WHERE type='table' AND name='chats_list';
      `);

      if (tablesResult.length === 0) {
        console.log('💬 Creating chats_list table...');
        await this.db.execAsync(`
          CREATE TABLE IF NOT EXISTS chats_list (
            id TEXT PRIMARY KEY,
            userId TEXT NOT NULL,
            partnerId TEXT NOT NULL,
            partnerName TEXT NOT NULL,
            partnerAvatar TEXT,
            partnerEmail TEXT,
            partnerPhone TEXT,
            lastMessageContent TEXT NOT NULL,
            lastMessageSenderId TEXT NOT NULL,
            lastMessageTimestamp TEXT NOT NULL,
            lastMessageType TEXT NOT NULL,
            unreadCount INTEGER DEFAULT 0,
            isPinned INTEGER DEFAULT 0,
            isMuted INTEGER DEFAULT 0,
            isArchived INTEGER DEFAULT 0,
            isOnline INTEGER DEFAULT 0,
            lastSeen TEXT,
            createdAt TEXT NOT NULL,
            updatedAt TEXT NOT NULL
          );
        `);

        // Create indexes for chats_list
        await this.db.execAsync(`
          CREATE INDEX IF NOT EXISTS idx_chats_list_user ON chats_list(userId);
          CREATE INDEX IF NOT EXISTS idx_chats_list_updated ON chats_list(userId, updatedAt DESC);
          CREATE INDEX IF NOT EXISTS idx_chats_list_pinned ON chats_list(userId, isPinned DESC, updatedAt DESC);
        `);

        console.log('✅ Chats_list table created successfully');
      }

      // Check if oldPrice column exists in business_posts table
      const businessPostsTableInfo = await this.db.getAllAsync(`PRAGMA table_info(business_posts);`);
      const hasOldPriceColumn = businessPostsTableInfo.some((column: any) => column.name === 'oldPrice');

      if (!hasOldPriceColumn) {
        console.log('💰 Adding oldPrice column to business_posts table...');
        await this.db.execAsync(`ALTER TABLE business_posts ADD COLUMN oldPrice REAL;`);
        console.log('✅ OldPrice column added to business_posts table');
      }

      // Check if priceHistory column exists in business_posts table
      const hasPriceHistoryColumn = businessPostsTableInfo.some((column: any) => column.name === 'priceHistory');

      if (!hasPriceHistoryColumn) {
        console.log('📈 Adding priceHistory column to business_posts table...');
        await this.db.execAsync(`ALTER TABLE business_posts ADD COLUMN priceHistory TEXT;`);
        console.log('✅ PriceHistory column added to business_posts table');
      }

      console.log('✅ Database migration completed successfully');
    } catch (error) {
      console.error('❌ Database migration failed:', error);
      // Don't throw error - continue with initialization
      console.log('⚠️ Continuing with database initialization despite migration errors');
    }

    console.log('✅ Database tables created successfully');
  }

  private async createIndexes(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Performance indexes - only create indexes for columns that exist in essential tables
    const indexes = [
      // Messages indexes (using essential table schema)
      'CREATE INDEX IF NOT EXISTS idx_messages_chat_timestamp ON messages(chatId, timestamp DESC);',
      'CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(senderId);',
      'CREATE INDEX IF NOT EXISTS idx_messages_status ON messages(status);',
      'CREATE INDEX IF NOT EXISTS idx_messages_type ON messages(type);',

      // Chats indexes (using essential table schema)
      'CREATE INDEX IF NOT EXISTS idx_chats_last_message ON chats(lastMessageTime DESC);',
      'CREATE INDEX IF NOT EXISTS idx_chats_pinned ON chats(isPinned);',
      'CREATE INDEX IF NOT EXISTS idx_chats_archived ON chats(isArchived);',

      // Settings indexes (using essential table schema)
      'CREATE INDEX IF NOT EXISTS idx_settings_key ON app_settings(key);',


    ];

    for (const indexSql of indexes) {
      try {
        await this.db.execAsync(indexSql);
      } catch (error) {
        console.warn(`⚠️ Failed to create index: ${indexSql}`, error);
        // Continue with other indexes even if one fails
      }
    }

    console.log('✅ Database indexes created successfully');
  }

  // Transaction wrapper for better performance
  async transaction<T>(callback: (db: SQLite.SQLiteDatabase) => Promise<T>): Promise<T> {
    if (!this.db) throw new Error('Database not initialized');
    
    await this.db.execAsync('BEGIN TRANSACTION;');
    try {
      const result = await callback(this.db);
      await this.db.execAsync('COMMIT;');
      return result;
    } catch (error) {
      await this.db.execAsync('ROLLBACK;');
      throw error;
    }
  }

  // Database maintenance
  async vacuum(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.execAsync('VACUUM;');
    console.log('✅ Database vacuumed');
  }

  async analyze(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    await this.db.execAsync('ANALYZE;');
    console.log('✅ Database analyzed');
  }

  async getDatabaseSize(): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.getFirstAsync<{ size: number }>(`
      SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size();
    `);
    
    return result?.size || 0;
  }

  async getTableStats(): Promise<Record<string, number>> {
    if (!this.db) throw new Error('Database not initialized');
    
    const tables = ['users', 'contacts', 'chats', 'chat_participants', 'messages', 'media_metadata', 'app_settings', 'sync_queue'];
    const stats: Record<string, number> = {};
    
    for (const table of tables) {
      const result = await this.db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM ${table};`);
      stats[table] = result?.count || 0;
    }
    
    return stats;
  }

  // Cleanup methods
  async cleanupOldData(daysToKeep: number = 30): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    
    await this.transaction(async (db) => {
      // Clean up old deleted records
      await db.runAsync('DELETE FROM messages WHERE isDeleted = 1 AND deletedAt < ?;', [cutoffTime]);
      await db.runAsync('DELETE FROM chats WHERE isDeleted = 1 AND deletedAt < ?;', [cutoffTime]);
      await db.runAsync('DELETE FROM contacts WHERE isDeleted = 1 AND deletedAt < ?;', [cutoffTime]);
      await db.runAsync('DELETE FROM users WHERE isDeleted = 1 AND deletedAt < ?;', [cutoffTime]);
      
      // Clean up old sync queue entries
      await db.runAsync('DELETE FROM sync_queue WHERE status = "completed" AND updatedAt < ?;', [cutoffTime]);
    });
    
    console.log('✅ Old data cleaned up');
  }

  async close(): Promise<void> {
    if (this.db) {
      await this.db.closeAsync();
      this.db = null;
      this.isInitialized = false;
      console.log('✅ Database closed');
    }
  }

  // Getter for database instance (for specific operations)
  getDatabase(): SQLite.SQLiteDatabase {
    if (!this.db) throw new Error('Database not initialized');

    // Test if the database connection is still valid
    try {
      // Simple test to check if the database is still accessible
      this.db.execSync('SELECT 1');
    } catch (error: any) {
      if (error.message?.includes('shared object') || error.message?.includes('already released')) {
        console.warn('⚠️ Database connection lost, marking as uninitialized');
        this.db = null;
        this.isInitialized = false;
        throw new Error('Database connection lost - needs reinitialization');
      }
      throw error;
    }

    return this.db;
  }

  // Check if database is ready for use
  isReady(): boolean {
    return this.isInitialized && this.db !== null;
  }

  /**
   * Ensure database is ready and schema is correct
   */
  async ensureReady(): Promise<void> {
    try {
      if (!this.isReady()) {
        await this.initialize();
      }

      // Double-check schema is correct
      if (this.db) {
        try {
          // Quick test to ensure contacts table has required columns
          await this.db.getAllAsync(`SELECT lastSeen, isOnline FROM contacts LIMIT 1;`);
        } catch (error: any) {
          if (error.message?.includes('shared object') || error.message?.includes('already released')) {
            console.warn('⚠️ Database connection lost during schema check, recovering...');
            await this.recoverDatabase();
          } else if (error instanceof Error && error.message.includes('no such column')) {
            console.log('🔧 Schema issue detected, fixing...');
            await this.fixDatabaseSchema();
          }
        }
      }
    } catch (error: any) {
      if (error.message?.includes('shared object') || error.message?.includes('already released')) {
        console.warn('⚠️ Database connection issue in ensureReady, attempting recovery...');
        await this.recoverDatabase();
      } else {
        throw error;
      }
    }
  }

  /**
   * Recover from database connection issues
   */
  async recoverDatabase(): Promise<void> {
    console.log('🔄 Attempting database recovery...');

    try {
      // Close any existing connection
      if (this.db) {
        try {
          await this.db.closeAsync();
        } catch (closeError) {
          console.warn('⚠️ Error closing database during recovery:', closeError);
        }
      }

      // Reset state
      this.db = null;
      this.isInitialized = false;
      this.initializationPromise = null;

      // Wait a bit before reinitializing
      await new Promise(resolve => setTimeout(resolve, 100));

      // Reinitialize
      await this.initialize();

      console.log('✅ Database recovery completed');
    } catch (error) {
      console.error('❌ Database recovery failed:', error);
      throw new Error('Failed to recover database connection');
    }
  }

  /**
   * Debug method to check database schema
   */
  async debugDatabaseSchema(): Promise<void> {
    if (!this.db) {
      console.log('❌ Database not initialized');
      return;
    }

    try {
      console.log('🔍 Checking database schema...');

      // Check contacts table
      const contactTableInfo = await this.db.getAllAsync(`PRAGMA table_info(contacts);`);
      console.log('📋 Contacts table columns:', contactTableInfo.map((col: any) => col.name));

      // Check chats table
      const chatTableInfo = await this.db.getAllAsync(`PRAGMA table_info(chats);`);
      console.log('📋 Chats table columns:', chatTableInfo.map((col: any) => col.name));

      // Test queries
      try {
        await this.db.getAllAsync(`SELECT lastSeen, isOnline FROM contacts LIMIT 1;`);
        console.log('✅ Contacts table schema is correct');
      } catch (error) {
        console.log('❌ Contacts table schema issue:', error);
      }

      try {
        await this.db.getAllAsync(`SELECT lastSeen, isOnline FROM chats LIMIT 1;`);
        console.log('✅ Chats table schema is correct');
      } catch (error) {
        console.log('❌ Chats table schema issue:', error);
      }

    } catch (error) {
      console.error('❌ Error checking database schema:', error);
    }
  }

  /**
   * Add missing columns to existing tables
   */
  private async addMissingColumns(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding missing columns to existing tables...');

      // Add duration column to messages table if it doesn't exist
      try {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN duration INTEGER;
        `);
        console.log('✅ Added duration column to messages table');
      } catch (error) {
        // Column already exists, ignore
        console.log('ℹ️ Duration column already exists in messages table');
      }

      // Add missing columns to app_settings table
      try {
        await this.db.execAsync(`
          ALTER TABLE app_settings ADD COLUMN syncStatus TEXT DEFAULT 'pending';
        `);
        console.log('✅ Added syncStatus column to app_settings table');
      } catch (error) {
        console.log('ℹ️ syncStatus column already exists in app_settings table');
      }

      try {
        await this.db.execAsync(`
          ALTER TABLE app_settings ADD COLUMN lastSyncAttempt INTEGER;
        `);
        console.log('✅ Added lastSyncAttempt column to app_settings table');
      } catch (error) {
        console.log('ℹ️ lastSyncAttempt column already exists in app_settings table');
      }

      try {
        await this.db.execAsync(`
          ALTER TABLE app_settings ADD COLUMN retryCount INTEGER DEFAULT 0;
        `);
        console.log('✅ Added retryCount column to app_settings table');
      } catch (error) {
        console.log('ℹ️ retryCount column already exists in app_settings table');
      }

      console.log('✅ Missing columns migration completed');
    } catch (error) {
      console.error('❌ Failed to add missing columns:', error);
      throw error;
    }
  }

  /**
   * Add isPinned column to messages table
   */
  private async addIsPinnedColumn(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding isPinned column to messages table...');

      // Check if isPinned column already exists
      const tableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
      const hasIsPinnedColumn = tableInfo.some((column: any) => column.name === 'isPinned');

      if (!hasIsPinnedColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN isPinned INTEGER DEFAULT 0;
        `);
        console.log('✅ Added isPinned column to messages table');
      } else {
        console.log('ℹ️ isPinned column already exists in messages table');
      }

      // Also add pinnedAt and pinnedBy columns if they don't exist
      const hasPinnedAtColumn = tableInfo.some((column: any) => column.name === 'pinnedAt');
      const hasPinnedByColumn = tableInfo.some((column: any) => column.name === 'pinnedBy');

      if (!hasPinnedAtColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN pinnedAt INTEGER;
        `);
        console.log('✅ Added pinnedAt column to messages table');
      }

      if (!hasPinnedByColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN pinnedBy TEXT;
        `);
        console.log('✅ Added pinnedBy column to messages table');
      }

      console.log('✅ isPinned column migration completed');
    } catch (error) {
      console.error('❌ Failed to add isPinned column:', error);
      throw error;
    }
  }

  /**
   * Add forwardedFrom column to messages table
   */
  private async addForwardedFromColumn(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding forwardedFrom column to messages table...');

      // Check if forwardedFrom column already exists
      const tableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
      const hasForwardedFromColumn = tableInfo.some((column: any) => column.name === 'forwardedFrom');

      if (!hasForwardedFromColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN forwardedFrom TEXT;
        `);
        console.log('✅ Added forwardedFrom column to messages table');
      } else {
        console.log('ℹ️ forwardedFrom column already exists in messages table');
      }

      // Also add other forwarding-related columns if they don't exist
      const hasOriginalSenderColumn = tableInfo.some((column: any) => column.name === 'originalSender');
      const hasOriginalChatColumn = tableInfo.some((column: any) => column.name === 'originalChat');

      if (!hasOriginalSenderColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN originalSender TEXT;
        `);
        console.log('✅ Added originalSender column to messages table');
      }

      if (!hasOriginalChatColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN originalChat TEXT;
        `);
        console.log('✅ Added originalChat column to messages table');
      }

      console.log('✅ forwardedFrom column migration completed');
    } catch (error) {
      console.error('❌ Failed to add forwardedFrom column:', error);
      throw error;
    }
  }

  /**
   * Add participants column to chats table
   */
  private async addParticipantsColumn(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding participants column to chats table...');

      // Check if participants column already exists in chats table
      const chatsTableInfo = await this.db.getAllAsync(`PRAGMA table_info(chats);`);
      const hasParticipantsColumn = chatsTableInfo.some((column: any) => column.name === 'participants');

      if (!hasParticipantsColumn) {
        await this.db.execAsync(`
          ALTER TABLE chats ADD COLUMN participants TEXT;
        `);
        console.log('✅ Added participants column to chats table');
      } else {
        console.log('ℹ️ participants column already exists in chats table');
      }

      // Also add other group-related columns if they don't exist
      const hasParticipantNamesColumn = chatsTableInfo.some((column: any) => column.name === 'participantNames');
      const hasParticipantAvatarsColumn = chatsTableInfo.some((column: any) => column.name === 'participantAvatars');
      const hasGroupAdminsColumn = chatsTableInfo.some((column: any) => column.name === 'groupAdmins');
      const hasGroupDescriptionColumn = chatsTableInfo.some((column: any) => column.name === 'groupDescription');

      if (!hasParticipantNamesColumn) {
        await this.db.execAsync(`
          ALTER TABLE chats ADD COLUMN participantNames TEXT;
        `);
        console.log('✅ Added participantNames column to chats table');
      }

      if (!hasParticipantAvatarsColumn) {
        await this.db.execAsync(`
          ALTER TABLE chats ADD COLUMN participantAvatars TEXT;
        `);
        console.log('✅ Added participantAvatars column to chats table');
      }

      if (!hasGroupAdminsColumn) {
        await this.db.execAsync(`
          ALTER TABLE chats ADD COLUMN groupAdmins TEXT;
        `);
        console.log('✅ Added groupAdmins column to chats table');
      }

      if (!hasGroupDescriptionColumn) {
        await this.db.execAsync(`
          ALTER TABLE chats ADD COLUMN groupDescription TEXT;
        `);
        console.log('✅ Added groupDescription column to chats table');
      }

      console.log('✅ participants column migration completed');
    } catch (error) {
      console.error('❌ Failed to add participants column:', error);
      throw error;
    }
  }

  /**
   * Add lastSyncAttempt column to messages table
   */
  private async addLastSyncAttemptColumn(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding lastSyncAttempt column to messages table...');

      // Check if lastSyncAttempt column already exists
      const tableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
      const hasLastSyncAttemptColumn = tableInfo.some((column: any) => column.name === 'lastSyncAttempt');

      if (!hasLastSyncAttemptColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN lastSyncAttempt INTEGER DEFAULT 0;
        `);
        console.log('✅ Added lastSyncAttempt column to messages table');
      } else {
        console.log('ℹ️ lastSyncAttempt column already exists in messages table');
      }

      // Also add other sync-related columns if they don't exist
      const hasSyncRetriesColumn = tableInfo.some((column: any) => column.name === 'syncRetries');
      const hasSyncErrorColumn = tableInfo.some((column: any) => column.name === 'syncError');

      if (!hasSyncRetriesColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN syncRetries INTEGER DEFAULT 0;
        `);
        console.log('✅ Added syncRetries column to messages table');
      }

      if (!hasSyncErrorColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN syncError TEXT;
        `);
        console.log('✅ Added syncError column to messages table');
      }

      console.log('✅ lastSyncAttempt column migration completed');
    } catch (error) {
      console.error('❌ Failed to add lastSyncAttempt column:', error);
      throw error;
    }
  }

  /**
   * Add retryCount column to messages table
   */
  private async addRetryCountColumn(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding retryCount column to messages table...');

      // Check if retryCount column already exists
      const tableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
      const hasRetryCountColumn = tableInfo.some((column: any) => column.name === 'retryCount');

      if (!hasRetryCountColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN retryCount INTEGER DEFAULT 0;
        `);
        console.log('✅ Added retryCount column to messages table');
      } else {
        console.log('ℹ️ retryCount column already exists in messages table');
      }

      // Also add other retry-related columns if they don't exist
      const hasMaxRetriesColumn = tableInfo.some((column: any) => column.name === 'maxRetries');
      const hasNextRetryAtColumn = tableInfo.some((column: any) => column.name === 'nextRetryAt');

      if (!hasMaxRetriesColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN maxRetries INTEGER DEFAULT 3;
        `);
        console.log('✅ Added maxRetries column to messages table');
      }

      if (!hasNextRetryAtColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN nextRetryAt INTEGER;
        `);
        console.log('✅ Added nextRetryAt column to messages table');
      }

      console.log('✅ retryCount column migration completed');
    } catch (error) {
      console.error('❌ Failed to add retryCount column:', error);
      throw error;
    }
  }

  /**
   * Add errorMessage column to messages table
   */
  private async addErrorMessageColumn(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding errorMessage column to messages table...');

      // Check if errorMessage column already exists
      const tableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
      const hasErrorMessageColumn = tableInfo.some((column: any) => column.name === 'errorMessage');

      if (!hasErrorMessageColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN errorMessage TEXT;
        `);
        console.log('✅ Added errorMessage column to messages table');
      } else {
        console.log('ℹ️ errorMessage column already exists in messages table');
      }

      // Also add other error-related columns if they don't exist
      const hasErrorCodeColumn = tableInfo.some((column: any) => column.name === 'errorCode');
      const hasErrorTimestampColumn = tableInfo.some((column: any) => column.name === 'errorTimestamp');

      if (!hasErrorCodeColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN errorCode TEXT;
        `);
        console.log('✅ Added errorCode column to messages table');
      }

      if (!hasErrorTimestampColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN errorTimestamp INTEGER;
        `);
        console.log('✅ Added errorTimestamp column to messages table');
      }

      console.log('✅ errorMessage column migration completed');
    } catch (error) {
      console.error('❌ Failed to add errorMessage column:', error);
      throw error;
    }
  }

  /**
   * Add deletedAt column to messages table
   */
  private async addDeletedAtColumn(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding deletedAt column to messages table...');

      // Check if deletedAt column already exists
      const tableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
      const hasDeletedAtColumn = tableInfo.some((column: any) => column.name === 'deletedAt');

      if (!hasDeletedAtColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN deletedAt INTEGER;
        `);
        console.log('✅ Added deletedAt column to messages table');
      } else {
        console.log('ℹ️ deletedAt column already exists in messages table');
      }

      // Also add other deletion-related columns if they don't exist
      const hasDeletedByColumn = tableInfo.some((column: any) => column.name === 'deletedBy');
      const hasDeletedForEveryoneColumn = tableInfo.some((column: any) => column.name === 'deletedForEveryone');

      if (!hasDeletedByColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN deletedBy TEXT;
        `);
        console.log('✅ Added deletedBy column to messages table');
      }

      if (!hasDeletedForEveryoneColumn) {
        await this.db.execAsync(`
          ALTER TABLE messages ADD COLUMN deletedForEveryone INTEGER DEFAULT 0;
        `);
        console.log('✅ Added deletedForEveryone column to messages table');
      }

      console.log('✅ deletedAt column migration completed');
    } catch (error) {
      console.error('❌ Failed to add deletedAt column:', error);
      throw error;
    }
  }

  /**
   * Add localId column to existing tables
   */
  private async addLocalIdColumn(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Adding localId column to existing tables...');

      // Get list of all tables
      const tables = await this.db.getAllAsync(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name NOT LIKE 'sqlite_%';
      `);

      const tableNames = tables.map((table: any) => table.name);
      console.log('📋 Found tables:', tableNames);

      // Add localId to chats table if it exists
      if (tableNames.includes('chats')) {
        const chatsTableInfo = await this.db.getAllAsync(`PRAGMA table_info(chats);`);
        const hasLocalIdColumn = chatsTableInfo.some((column: any) => column.name === 'localId');

        if (!hasLocalIdColumn) {
          await this.db.execAsync(`
            ALTER TABLE chats ADD COLUMN localId TEXT;
          `);
          console.log('✅ Added localId column to chats table');
        } else {
          console.log('ℹ️ localId column already exists in chats table');
        }
      } else {
        console.log('ℹ️ chats table does not exist, skipping localId addition');
      }

      // Add localId to messages table if it exists
      if (tableNames.includes('messages')) {
        const messagesTableInfo = await this.db.getAllAsync(`PRAGMA table_info(messages);`);
        const hasLocalIdInMessages = messagesTableInfo.some((column: any) => column.name === 'localId');

        if (!hasLocalIdInMessages) {
          await this.db.execAsync(`
            ALTER TABLE messages ADD COLUMN localId TEXT;
          `);
          console.log('✅ Added localId column to messages table');
        } else {
          console.log('ℹ️ localId column already exists in messages table');
        }
      } else {
        console.log('ℹ️ messages table does not exist, skipping localId addition');
      }

      // Only add to groups table if it exists
      if (tableNames.includes('groups')) {
        const groupsTableInfo = await this.db.getAllAsync(`PRAGMA table_info(groups);`);
        const hasLocalIdInGroups = groupsTableInfo.some((column: any) => column.name === 'localId');

        if (!hasLocalIdInGroups) {
          await this.db.execAsync(`
            ALTER TABLE groups ADD COLUMN localId TEXT;
          `);
          console.log('✅ Added localId column to groups table');
        } else {
          console.log('ℹ️ localId column already exists in groups table');
        }
      } else {
        console.log('ℹ️ groups table does not exist, skipping localId addition');
      }

      console.log('✅ localId column migration completed');
    } catch (error) {
      console.error('❌ Failed to add localId column:', error);
      throw error;
    }
  }

  /**
   * Force database schema update by dropping and recreating tables
   */
  async forceSchemaUpdate(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    try {
      console.log('🔄 Forcing database schema update...');

      // Drop existing tables
      const tables = ['messages', 'chats', 'app_settings', 'contacts', 'users'];
      for (const table of tables) {
        try {
          await this.db.execAsync(`DROP TABLE IF EXISTS ${table}`);
          console.log(`🗑️ Dropped table: ${table}`);
        } catch (error) {
          console.warn(`⚠️ Failed to drop table ${table}:`, error);
        }
      }

      // Recreate tables with correct schema
      await this.createEssentialTables();
      console.log('✅ Tables recreated with correct schema');

      // Run migrations to ensure all columns are present
      await this.runMigrations();
      console.log('✅ Schema update completed');
    } catch (error) {
      console.error('❌ Failed to update schema:', error);
      throw error;
    }
  }

  /**
   * Reset database completely (nuclear option for fixing corruption)
   */
  async resetDatabase(): Promise<void> {
    try {
      console.log('💥 Resetting database completely...');

      if (this.db) {
        await this.db.closeAsync();
      }

      // Reset state
      this.db = null;
      this.isInitialized = false;

      // Reinitialize from scratch
      await this.initialize();

      console.log('✅ Database reset completed');
    } catch (error) {
      console.error('❌ Failed to reset database:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const offlineDatabaseService = new OfflineDatabaseService();
export default offlineDatabaseService;
