/**
 * Message Search Component for IraChat
 * Comprehensive search interface with filters and results
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Modal,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { messageSearchService, SearchFilters, SearchResult, getDateRangePresets } from '../services/messageSearchService';

interface MessageSearchProps {
  visible: boolean;
  onClose: () => void;
  chatId?: string;
  onMessageSelect?: (messageId: string) => void;
}

const MESSAGE_TYPES = [
  { value: 'all', label: 'All Messages', icon: 'chatbubbles' },
  { value: 'text', label: 'Text', icon: 'text' },
  { value: 'image', label: 'Images', icon: 'image' },
  { value: 'video', label: 'Videos', icon: 'videocam' },
  { value: 'audio', label: 'Audio', icon: 'musical-notes' },
  { value: 'document', label: 'Documents', icon: 'document' },
  { value: 'voice', label: 'Voice Messages', icon: 'mic' },
];

export const MessageSearch: React.FC<MessageSearchProps> = ({
  visible,
  onClose,
  chatId,
  onMessageSelect,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({
    messageType: 'all',
  });
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedDateRange, setSelectedDateRange] = useState<string>('');

  const dateRangePresets = getDateRangePresets();

  const performSearch = useCallback(async () => {
    if (!searchQuery.trim() && !filters.startDate && !filters.endDate && filters.messageType === 'all') {
      setResults([]);
      setTotalCount(0);
      return;
    }

    setIsLoading(true);
    try {
      const searchFilters: SearchFilters = {
        ...filters,
        query: searchQuery.trim() || undefined,
      };

      const searchResults = chatId
        ? await messageSearchService.searchInChat(chatId, searchFilters, { limit: 100 })
        : await messageSearchService.searchGlobal(searchFilters, { limit: 100 });

      setResults(searchResults.results);
      setTotalCount(searchResults.totalCount);
    } catch (error) {
      Alert.alert('Error', 'Failed to search messages. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [searchQuery, filters, chatId]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch();
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [performSearch]);

  const handleDateRangeSelect = (preset: any) => {
    setFilters(prev => ({
      ...prev,
      startDate: preset.startDate,
      endDate: preset.endDate,
    }));
    setSelectedDateRange(preset.label);
  };

  const clearFilters = () => {
    setFilters({ messageType: 'all' });
    setSelectedDateRange('');
    setSearchQuery('');
  };

  const getMessageTypeIcon = (type: string): any => {
    const typeMap: { [key: string]: any } = {
      text: 'text',
      image: 'image',
      video: 'videocam',
      audio: 'musical-notes',
      document: 'document',
      voice: 'mic',
      location: 'location',
      contact: 'person',
    };
    return typeMap[type] || 'chatbubble';
  };

  const renderSearchResult = (result: SearchResult) => (
    <TouchableOpacity
      key={result.id}
      style={styles.resultItem}
      onPress={() => {
        onMessageSelect?.(result.id);
        onClose();
      }}
    >
      <View style={styles.resultHeader}>
        <View style={styles.resultInfo}>
          <Text style={styles.senderName}>{result.senderName}</Text>
          <Text style={styles.timestamp}>
            {result.timestamp.toLocaleDateString()} {result.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>
        <Ionicons
          name={getMessageTypeIcon(result.type)}
          size={16}
          color="#87CEEB"
        />
      </View>
      
      <View style={styles.resultContent}>
        {result.contextBefore && (
          <Text style={styles.contextText}>...{result.contextBefore}</Text>
        )}
        <Text style={styles.messageContent}>
          {result.matchedText || result.content || result.caption || result.fileName || 'Media message'}
        </Text>
        {result.contextAfter && (
          <Text style={styles.contextText}>{result.contextAfter}...</Text>
        )}
      </View>

      {result.type !== 'text' && (
        <View style={styles.mediaInfo}>
          <Text style={styles.mediaType}>{result.type.toUpperCase()}</Text>
          {result.fileName && <Text style={styles.fileName}>{result.fileName}</Text>}
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#87CEEB" />
          </TouchableOpacity>
          <Text style={styles.title}>Search Messages</Text>
          <TouchableOpacity onPress={() => setShowFilters(!showFilters)}>
            <Ionicons name="options" size={24} color="#87CEEB" />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Ionicons name="search" size={20} color="#888888" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search messages..."
              placeholderTextColor="#888888"
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
            {searchQuery.length > 0 && (
              <TouchableOpacity onPress={() => setSearchQuery('')}>
                <Ionicons name="close-circle" size={20} color="#888888" />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {showFilters && (
          <View style={styles.filtersContainer}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <View style={styles.filterRow}>
                {MESSAGE_TYPES.map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={[
                      styles.filterChip,
                      filters.messageType === type.value && styles.activeFilterChip
                    ]}
                    onPress={() => setFilters(prev => ({ ...prev, messageType: type.value as any }))}
                  >
                    <Ionicons name={type.icon as any} size={16} color={filters.messageType === type.value ? '#000000' : '#87CEEB'} />
                    <Text style={[
                      styles.filterChipText,
                      filters.messageType === type.value && styles.activeFilterChipText
                    ]}>
                      {type.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </ScrollView>

            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.dateFilters}>
              <View style={styles.filterRow}>
                {dateRangePresets.map((preset) => (
                  <TouchableOpacity
                    key={preset.label}
                    style={[
                      styles.filterChip,
                      selectedDateRange === preset.label && styles.activeFilterChip
                    ]}
                    onPress={() => handleDateRangeSelect(preset)}
                  >
                    <Ionicons name="calendar" size={16} color={selectedDateRange === preset.label ? '#000000' : '#87CEEB'} />
                    <Text style={[
                      styles.filterChipText,
                      selectedDateRange === preset.label && styles.activeFilterChipText
                    ]}>
                      {preset.label}
                    </Text>
                  </TouchableOpacity>
                ))}
                
                {(filters.startDate || filters.endDate || filters.messageType !== 'all') && (
                  <TouchableOpacity style={styles.clearFiltersButton} onPress={clearFilters}>
                    <Ionicons name="refresh" size={16} color="#FF6B6B" />
                    <Text style={styles.clearFiltersText}>Clear</Text>
                  </TouchableOpacity>
                )}
              </View>
            </ScrollView>
          </View>
        )}

        <View style={styles.resultsContainer}>
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="#87CEEB" />
              <Text style={styles.loadingText}>Searching...</Text>
            </View>
          ) : results.length > 0 ? (
            <>
              <View style={styles.resultsHeader}>
                <Text style={styles.resultsCount}>
                  {totalCount} message{totalCount !== 1 ? 's' : ''} found
                </Text>
              </View>
              <ScrollView style={styles.resultsList}>
                {results.map(renderSearchResult)}
              </ScrollView>
            </>
          ) : searchQuery.trim() || filters.startDate || filters.messageType !== 'all' ? (
            <View style={styles.noResultsContainer}>
              <Ionicons name="search" size={48} color="#888888" />
              <Text style={styles.noResultsText}>No messages found</Text>
              <Text style={styles.noResultsSubtext}>
                Try adjusting your search terms or filters
              </Text>
            </View>
          ) : (
            <View style={styles.emptyStateContainer}>
              <Ionicons name="search" size={48} color="#888888" />
              <Text style={styles.emptyStateText}>Search your messages</Text>
              <Text style={styles.emptyStateSubtext}>
                Enter keywords or use filters to find specific messages
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

export default MessageSearch;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  searchContainer: {
    padding: 16,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 8,
  },
  filtersContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  filterChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#87CEEB',
  },
  activeFilterChip: {
    backgroundColor: '#87CEEB',
  },
  filterChipText: {
    fontSize: 12,
    color: '#87CEEB',
    marginLeft: 4,
  },
  activeFilterChipText: {
    color: '#000000',
  },
  dateFilters: {
    marginTop: 8,
  },
  clearFiltersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#FF6B6B',
  },
  clearFiltersText: {
    fontSize: 12,
    color: '#FF6B6B',
    marginLeft: 4,
  },
  resultsContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#888888',
    marginTop: 12,
  },
  resultsHeader: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  resultsCount: {
    fontSize: 14,
    color: '#888888',
  },
  resultsList: {
    flex: 1,
  },
  resultItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  resultInfo: {
    flex: 1,
  },
  senderName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#87CEEB',
  },
  timestamp: {
    fontSize: 12,
    color: '#888888',
    marginTop: 2,
  },
  resultContent: {
    marginBottom: 8,
  },
  contextText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
  },
  messageContent: {
    fontSize: 14,
    color: '#FFFFFF',
    lineHeight: 20,
  },
  mediaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  mediaType: {
    fontSize: 10,
    color: '#87CEEB',
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginRight: 8,
  },
  fileName: {
    fontSize: 12,
    color: '#888888',
    flex: 1,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  noResultsText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 16,
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#888888',
    textAlign: 'center',
    marginTop: 8,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 16,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#888888',
    textAlign: 'center',
    marginTop: 8,
  },
});
