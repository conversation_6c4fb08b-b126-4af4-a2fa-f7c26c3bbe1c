import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  TouchableOpacity,
  Image,
  Text,
  FlatList,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants/theme';
import { useOptimizedList } from '../../utils/performance';

const { width: screenWidth } = Dimensions.get('window');
const MEDIA_ITEM_SIZE = (screenWidth - 48) / 3; // 3 items per row with padding

interface MediaItem {
  id: string;
  url: string;
  type: 'image' | 'video';
  thumbnailUrl?: string;
  duration?: number;
}

interface LazyMediaGridProps {
  media: MediaItem[];
  onMediaPress: (index: number) => void;
  onDeleteMedia?: (mediaId: string) => void;
  isOwner?: boolean;
  maxInitialItems?: number;
}

const formatVideoDuration = (duration: number): string => {
  if (!duration || duration <= 0) return '0:00';

  // Convert milliseconds to seconds if the number is too large (likely milliseconds)
  const seconds = duration > 1000 ? Math.floor(duration / 1000) : Math.floor(duration);

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

export const LazyMediaGrid: React.FC<LazyMediaGridProps> = ({
  media,
  onMediaPress,
  onDeleteMedia,
  isOwner = false,
  maxInitialItems = 6,
}) => {
  const [showAll, setShowAll] = useState(false);
  const listProps = useOptimizedList(MEDIA_ITEM_SIZE);

  const displayedMedia = useMemo(() => {
    return showAll ? media : media.slice(0, maxInitialItems);
  }, [media, showAll, maxInitialItems]);

  const renderMediaItem = useCallback(({ item, index }: { item: MediaItem; index: number }) => {
    const actualIndex = media.findIndex(m => m.id === item.id);

    return (
      <TouchableOpacity
        style={styles.mediaItem}
        onPress={() => onMediaPress(actualIndex)}
        activeOpacity={0.8}
      >
        <Image
          source={{ uri: item.thumbnailUrl || item.url }}
          style={styles.mediaImage}
          resizeMode="cover"
        />

        {item.type === 'video' && (
          <View style={styles.videoOverlay}>
            <Ionicons name="play-circle" size={24} color="#FFFFFF" />
            {item.duration && (
              <Text style={styles.videoDuration}>
                {formatVideoDuration(item.duration)}
              </Text>
            )}
          </View>
        )}

        {/* Delete button for owners */}
        {isOwner && onDeleteMedia && (
          <TouchableOpacity
            style={styles.deleteButton}
            onPress={(e) => {
              e.stopPropagation();
              onDeleteMedia(item.id);
            }}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="close-circle" size={20} color="#FF4444" />
          </TouchableOpacity>
        )}

        {/* Show count overlay for remaining items */}
        {!showAll && index === maxInitialItems - 1 && media.length > maxInitialItems && (
          <View style={styles.countOverlay}>
            <Text style={styles.countText}>+{media.length - maxInitialItems}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  }, [media, onMediaPress, onDeleteMedia, isOwner, showAll, maxInitialItems]);

  const keyExtractor = useCallback((item: MediaItem) => item.id, []);

  if (!media || media.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={displayedMedia}
        renderItem={renderMediaItem}
        numColumns={3}
        scrollEnabled={false}
        {...listProps}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.gridContainer}
      />
      
      {!showAll && media.length > maxInitialItems && (
        <TouchableOpacity
          style={styles.showMoreButton}
          onPress={() => setShowAll(true)}
        >
          <Text style={styles.showMoreText}>
            Show All {media.length} Items
          </Text>
          <Ionicons name="chevron-down" size={16} color={COLORS.primary} />
        </TouchableOpacity>
      )}
      
      {showAll && media.length > maxInitialItems && (
        <TouchableOpacity
          style={styles.showMoreButton}
          onPress={() => setShowAll(false)}
        >
          <Text style={styles.showMoreText}>Show Less</Text>
          <Ionicons name="chevron-up" size={16} color={COLORS.primary} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = {
  container: {
    marginTop: 8,
  },
  gridContainer: {
    gap: 4,
  },
  mediaItem: {
    width: MEDIA_ITEM_SIZE,
    height: MEDIA_ITEM_SIZE,
    marginRight: 4,
    marginBottom: 4,
    borderRadius: 8,
    overflow: 'hidden' as const,
    backgroundColor: COLORS.surface,
    position: 'relative' as const,
  },
  mediaImage: {
    width: MEDIA_ITEM_SIZE,
    height: MEDIA_ITEM_SIZE,
  },
  videoOverlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  videoDuration: {
    position: 'absolute' as const,
    bottom: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    color: '#FFFFFF',
    fontSize: 10,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  deleteButton: {
    position: 'absolute' as const,
    top: 4,
    right: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  countOverlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  countText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600' as const,
  },
  showMoreButton: {
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: 12,
    marginTop: 8,
    gap: 4,
  },
  showMoreText: {
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500' as const,
  },
};
