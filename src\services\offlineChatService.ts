/**
 * Comprehensive Offline Chat Management Service for IraChat
 * Handles all chat operations with full offline support
 * Similar to WhatsApp's chat management system
 */

import { Chat } from '../types';
import { offlineDatabaseService, LocalChat } from './offlineDatabase';
import { memoryCacheService } from './memoryCache';
import { networkStateManager } from './networkStateManager';
import { offlineMessageService } from './offlineMessageService';

export interface ChatParticipant {
  userId: string;
  role: 'admin' | 'member' | 'owner';
  joinedAt: number;
  leftAt?: number;
  isActive: boolean;
  addedBy?: string;
}

export interface ChatSettings {
  chatId: string;
  muteNotifications: boolean;
  mutedUntil?: number;
  customNotificationSound?: string;
  showNotificationPreview: boolean;
  allowMediaDownload: boolean;
  autoDeleteMessages?: number; // Days after which messages are auto-deleted
  disappearingMessages?: number; // Seconds after which messages disappear
  readReceipts: boolean;
  lastSeenPrivacy: 'everyone' | 'contacts' | 'nobody';
  profilePhotoPrivacy: 'everyone' | 'contacts' | 'nobody';
  statusPrivacy: 'everyone' | 'contacts' | 'nobody';
  groupInvitePrivacy: 'everyone' | 'contacts' | 'nobody';
  syncStatus: {
    status: 'pending' | 'synced' | 'failed';
    lastSyncAttempt?: number;
    retryCount: number;
    errorMessage?: string;
  };
  createdAt: number;
  updatedAt: number;
}

export interface ChatSearchOptions {
  query: string;
  includeArchived?: boolean;
  includeMuted?: boolean;
  chatType?: 'individual' | 'group' | 'all';
  limit?: number;
  offset?: number;
}

export interface ChatSearchResult {
  chats: LocalChat[];
  totalCount: number;
  hasMore: boolean;
}

export interface ChatStatistics {
  totalMessages: number;
  unreadMessages: number;
  mediaMessages: number;
  lastActivity: number;
  participantCount: number;
  messagesThisWeek: number;
  messagesThisMonth: number;
}

class OfflineChatService {
  private isInitialized = false;
  private chatCache: Map<string, LocalChat> = new Map();
  private participantCache: Map<string, ChatParticipant[]> = new Map();
  private settingsCache: Map<string, ChatSettings> = new Map();
  private syncQueue: Set<string> = new Set();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('💬 Initializing offline chat service...');

      // Initialize dependencies
      await offlineDatabaseService.initialize();
      await offlineMessageService.initialize();

      // Set up network state listener
      networkStateManager.addListener('chatService', this.handleNetworkStateChange.bind(this), 10);

      // Load chats into cache
      await this.loadChatsIntoCache();

      this.isInitialized = true;
      console.log('✅ Offline chat service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize offline chat service:', error);
      throw error;
    }
  }

  /**
   * Create a new chat (works offline)
   */
  async createChat(
    name: string,
    isGroup: boolean = false,
    participantIds: string[] = [],
    createdBy: string,
    description?: string,
    avatar?: string
  ): Promise<string> {
    const now = Date.now();
    const localId = `chat_${now}_${Math.random().toString(36).substring(2, 11)}`;

    const chat: LocalChat = {
      localId,
      id: localId, // Will be updated when synced with server
      name,
      avatar,
      isGroup,
      description,
      createdBy,
      participants: participantIds,
      timestamp: new Date(now).toISOString(),
      unreadCount: 0,
      isPinned: false,
      isArchived: false,
      isMuted: false,
      syncStatus: {
        status: 'pending',
        retryCount: 0,
      },
      createdAt: now,
      updatedAt: now,
      isDeleted: false,
    };

    try {
      // Save to database
      await this.saveChatToDatabase(chat);

      // Add participants
      if (participantIds.length > 0) {
        await this.addParticipants(localId, participantIds, createdBy);
      }

      // Cache the chat
      this.chatCache.set(localId, chat);
      memoryCacheService.setChat(localId, chat);

      // Queue for sync if online
      if (networkStateManager.isOnline()) {
        this.syncQueue.add(localId);
        this.processSyncQueue();
      }

      console.log('✅ Chat created:', localId);
      return localId;
    } catch (error) {
      console.error('❌ Failed to create chat:', error);
      throw error;
    }
  }

  /**
   * Get all chats (with caching)
   */
  async getAllChats(includeArchived: boolean = false, includeMuted: boolean = true): Promise<LocalChat[]> {
    try {
      // Try cache first
      const cacheKey = `all_chats_${includeArchived}_${includeMuted}`;
      const cachedChats = memoryCacheService.getSettings(cacheKey) as LocalChat[];
      if (cachedChats && cachedChats.length > 0) {
        return cachedChats;
      }

      // Get from database
      const db = offlineDatabaseService.getDatabase();
      let query = `
        SELECT * FROM chats 
        WHERE isDeleted = 0
      `;
      const params: any[] = [];

      if (!includeArchived) {
        query += ' AND isArchived = 0';
      }

      if (!includeMuted) {
        query += ' AND isMuted = 0';
      }

      query += ' ORDER BY isPinned DESC, lastMessageTime DESC';

      const result = await db.getAllAsync(query, params);
      const chats = result.map(row => this.rowToChat(row as any));

      // Cache the results
      memoryCacheService.setSettings(cacheKey, chats);

      return chats;
    } catch (error) {
      console.error('❌ Failed to get all chats:', error);
      return [];
    }
  }

  /**
   * Get a specific chat
   */
  async getChat(chatId: string): Promise<LocalChat | null> {
    try {
      // Try cache first
      let chat = this.chatCache.get(chatId);
      if (chat) return chat;

      chat = memoryCacheService.getChat(chatId) as LocalChat;
      if (chat) {
        this.chatCache.set(chatId, chat);
        return chat;
      }

      // Get from database
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getFirstAsync(`
        SELECT * FROM chats WHERE localId = ? OR id = ?
      `, [chatId, chatId]);

      if (!result) return null;

      chat = this.rowToChat(result as any);
      
      // Cache the result
      this.chatCache.set(chatId, chat);
      memoryCacheService.setChat(chatId, chat);
      
      return chat;
    } catch (error) {
      console.error('❌ Failed to get chat:', error);
      return null;
    }
  }

  /**
   * Update chat information
   */
  async updateChat(chatId: string, updates: Partial<LocalChat>): Promise<boolean> {
    try {
      const existingChat = await this.getChat(chatId);
      if (!existingChat) {
        throw new Error('Chat not found');
      }

      const updatedChat: LocalChat = {
        ...existingChat,
        ...updates,
        updatedAt: Date.now(),
        syncStatus: {
          status: 'pending',
          retryCount: 0,
        },
      };

      await this.updateChatInDatabase(updatedChat);
      
      // Update caches
      this.chatCache.set(chatId, updatedChat);
      memoryCacheService.setChat(chatId, updatedChat);

      // Clear related cache entries
      this.clearChatListCache();

      // Queue for sync
      if (networkStateManager.isOnline()) {
        this.syncQueue.add(chatId);
        this.processSyncQueue();
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to update chat:', error);
      return false;
    }
  }

  /**
   * Delete chat
   */
  async deleteChat(chatId: string, _deleteForEveryone: boolean = false): Promise<boolean> {
    try {
      const chat = await this.getChat(chatId);
      if (!chat) return false;

      const now = Date.now();
      const updatedChat: LocalChat = {
        ...chat,
        isDeleted: true,
        deletedAt: now,
        updatedAt: now,
        syncStatus: {
          status: 'pending',
          retryCount: 0,
        },
      };

      await this.updateChatInDatabase(updatedChat);
      
      // Remove from caches
      this.chatCache.delete(chatId);
      memoryCacheService.deleteChat(chatId);
      this.clearChatListCache();

      // Queue for sync
      if (networkStateManager.isOnline()) {
        this.syncQueue.add(chatId);
        this.processSyncQueue();
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to delete chat:', error);
      return false;
    }
  }

  /**
   * Add participants to a group chat
   */
  async addParticipants(chatId: string, userIds: string[], _addedBy: string): Promise<boolean> {
    try {
      const chat = await this.getChat(chatId);
      if (!chat || !chat.isGroup) {
        throw new Error('Chat not found or not a group');
      }

      const now = Date.now();

      await offlineDatabaseService.transaction(async (db) => {
        for (const userId of userIds) {
          const participantId = `${chatId}_${userId}`;
          await db.runAsync(`
            INSERT OR REPLACE INTO chat_participants (
              id, chatId, userId, role, joinedAt, isActive, syncStatus, createdAt, updatedAt
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [participantId, chatId, userId, 'member', now, 1, 'pending', now, now]);
        }

        // Update chat participant list
        const existingParticipants = chat.participants || [];
        const newParticipants = [...new Set([...existingParticipants, ...userIds])];

        await db.runAsync(`
          UPDATE chats SET participantIds = ?, updatedAt = ?, syncStatus = ? WHERE localId = ?
        `, [JSON.stringify(newParticipants), now, 'pending', chatId]);
      });

      // Update cache
      chat.participants = [...new Set([...(chat.participants || []), ...userIds])];
      chat.updatedAt = now;
      this.chatCache.set(chatId, chat);
      memoryCacheService.setChat(chatId, chat);

      // Clear participant cache
      this.participantCache.delete(chatId);

      // Queue for sync
      if (networkStateManager.isOnline()) {
        this.syncQueue.add(chatId);
        this.processSyncQueue();
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to add participants:', error);
      return false;
    }
  }

  /**
   * Remove participants from a group chat
   */
  async removeParticipants(chatId: string, userIds: string[], _removedBy: string): Promise<boolean> {
    try {
      const chat = await this.getChat(chatId);
      if (!chat || !chat.isGroup) {
        throw new Error('Chat not found or not a group');
      }

      const now = Date.now();

      await offlineDatabaseService.transaction(async (db) => {
        for (const userId of userIds) {
          await db.runAsync(`
            UPDATE chat_participants 
            SET isActive = 0, leftAt = ?, updatedAt = ?, syncStatus = ?
            WHERE chatId = ? AND userId = ?
          `, [now, now, 'pending', chatId, userId]);
        }

        // Update chat participant list
        const newParticipants = (chat.participants || []).filter((id: string) => !userIds.includes(id));

        await db.runAsync(`
          UPDATE chats SET participantIds = ?, updatedAt = ?, syncStatus = ? WHERE localId = ?
        `, [JSON.stringify(newParticipants), now, 'pending', chatId]);
      });

      // Update cache
      chat.participants = (chat.participants || []).filter((id: string) => !userIds.includes(id));
      chat.updatedAt = now;
      this.chatCache.set(chatId, chat);
      memoryCacheService.setChat(chatId, chat);

      // Clear participant cache
      this.participantCache.delete(chatId);

      // Queue for sync
      if (networkStateManager.isOnline()) {
        this.syncQueue.add(chatId);
        this.processSyncQueue();
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to remove participants:', error);
      return false;
    }
  }

  /**
   * Get chat participants
   */
  async getChatParticipants(chatId: string): Promise<ChatParticipant[]> {
    try {
      // Try cache first
      const cachedParticipants = this.participantCache.get(chatId);
      if (cachedParticipants) return cachedParticipants;

      // Get from database
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT * FROM chat_participants 
        WHERE chatId = ? AND isActive = 1
        ORDER BY joinedAt ASC
      `, [chatId]);

      const participants: ChatParticipant[] = result.map(row => ({
        userId: (row as any).userId,
        role: (row as any).role,
        joinedAt: (row as any).joinedAt,
        leftAt: (row as any).leftAt,
        isActive: Boolean((row as any).isActive),
        addedBy: (row as any).addedBy,
      }));

      // Cache the results
      this.participantCache.set(chatId, participants);

      return participants;
    } catch (error) {
      console.error('❌ Failed to get chat participants:', error);
      return [];
    }
  }

  /**
   * Pin/unpin chat
   */
  async pinChat(chatId: string, pin: boolean = true): Promise<boolean> {
    return this.updateChat(chatId, {
      isPinned: pin,
      pinnedAt: pin ? Date.now() : undefined,
    });
  }

  /**
   * Archive/unarchive chat
   */
  async archiveChat(chatId: string, archive: boolean = true): Promise<boolean> {
    return this.updateChat(chatId, {
      isArchived: archive,
      archivedAt: archive ? Date.now() : undefined,
    });
  }

  /**
   * Mute/unmute chat
   */
  async muteChat(chatId: string, mute: boolean = true, mutedUntil?: number): Promise<boolean> {
    return this.updateChat(chatId, {
      isMuted: mute,
      mutedUntil: mute ? mutedUntil : undefined,
    });
  }

  /**
   * Update unread count
   */
  async updateUnreadCount(chatId: string, count: number): Promise<boolean> {
    return this.updateChat(chatId, { unreadCount: count });
  }

  /**
   * Mark chat as read
   */
  async markChatAsRead(chatId: string): Promise<boolean> {
    return this.updateUnreadCount(chatId, 0);
  }

  /**
   * Search chats
   */
  async searchChats(options: ChatSearchOptions): Promise<ChatSearchResult> {
    try {
      const db = offlineDatabaseService.getDatabase();
      let query = `
        SELECT * FROM chats 
        WHERE isDeleted = 0 AND name LIKE ?
      `;
      const params: any[] = [`%${options.query}%`];

      if (!options.includeArchived) {
        query += ' AND isArchived = 0';
      }

      if (!options.includeMuted) {
        query += ' AND isMuted = 0';
      }

      if (options.chatType && options.chatType !== 'all') {
        query += ' AND isGroup = ?';
        params.push(options.chatType === 'group' ? 1 : 0);
      }

      query += ' ORDER BY isPinned DESC, lastMessageTime DESC';

      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }

      if (options.offset) {
        query += ' OFFSET ?';
        params.push(options.offset);
      }

      const result = await db.getAllAsync(query, params);
      const chats = result.map(row => this.rowToChat(row as any));

      // Get total count
      let countQuery = `
        SELECT COUNT(*) as count FROM chats 
        WHERE isDeleted = 0 AND name LIKE ?
      `;
      const countParams = [`%${options.query}%`];

      if (!options.includeArchived) {
        countQuery += ' AND isArchived = 0';
      }

      if (!options.includeMuted) {
        countQuery += ' AND isMuted = 0';
      }

      if (options.chatType && options.chatType !== 'all') {
        countQuery += ' AND isGroup = ?';
        countParams.push(options.chatType === 'group' ? '1' : '0');
      }

      const countResult = await db.getFirstAsync<{ count: number }>(countQuery, countParams);
      const totalCount = countResult?.count || 0;

      return {
        chats,
        totalCount,
        hasMore: (options.offset || 0) + chats.length < totalCount,
      };
    } catch (error) {
      console.error('❌ Failed to search chats:', error);
      return { chats: [], totalCount: 0, hasMore: false };
    }
  }

  /**
   * Get chat statistics
   */
  async getChatStatistics(chatId: string): Promise<ChatStatistics> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const now = Date.now();
      const weekAgo = now - (7 * 24 * 60 * 60 * 1000);
      const monthAgo = now - (30 * 24 * 60 * 60 * 1000);

      const [totalResult, unreadResult, mediaResult, weekResult, monthResult, participantResult] = await Promise.all([
        db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM messages WHERE chatId = ? AND isDeleted = 0`, [chatId]),
        db.getFirstAsync<{ count: number }>(`SELECT unreadCount as count FROM chats WHERE localId = ?`, [chatId]),
        db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM messages WHERE chatId = ? AND type != 'text' AND isDeleted = 0`, [chatId]),
        db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM messages WHERE chatId = ? AND timestamp >= ? AND isDeleted = 0`, [chatId, weekAgo]),
        db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM messages WHERE chatId = ? AND timestamp >= ? AND isDeleted = 0`, [chatId, monthAgo]),
        db.getFirstAsync<{ count: number }>(`SELECT COUNT(*) as count FROM chat_participants WHERE chatId = ? AND isActive = 1`, [chatId]),
      ]);

      // Get last activity
      const lastMessageResult = await db.getFirstAsync<{ timestamp: number }>(`
        SELECT timestamp FROM messages 
        WHERE chatId = ? AND isDeleted = 0 
        ORDER BY timestamp DESC LIMIT 1
      `, [chatId]);

      return {
        totalMessages: totalResult?.count || 0,
        unreadMessages: unreadResult?.count || 0,
        mediaMessages: mediaResult?.count || 0,
        lastActivity: lastMessageResult?.timestamp || 0,
        participantCount: participantResult?.count || 0,
        messagesThisWeek: weekResult?.count || 0,
        messagesThisMonth: monthResult?.count || 0,
      };
    } catch (error) {
      console.error('❌ Failed to get chat statistics:', error);
      return {
        totalMessages: 0,
        unreadMessages: 0,
        mediaMessages: 0,
        lastActivity: 0,
        participantCount: 0,
        messagesThisWeek: 0,
        messagesThisMonth: 0,
      };
    }
  }

  // Private helper methods
  private async saveChatToDatabase(chat: LocalChat): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    
    await db.runAsync(`
      INSERT OR REPLACE INTO chats (
        id, name, avatar, isGroup,
        lastMessage, lastMessageTime, lastMessageSender, unreadCount,
        isPinned, isArchived, isMuted,
        syncStatus, isDeleted,
        createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      chat.id,
      chat.name || null,
      chat.avatar || null,
      chat.isGroup ? 1 : 0,
      chat.lastMessageText || null,
      chat.lastMessageTimestamp || null,
      chat.lastMessageId || null,
      chat.unreadCount || 0,
      chat.isPinned ? 1 : 0,
      chat.isArchived ? 1 : 0,
      chat.isMuted ? 1 : 0,
      chat.syncStatus?.status || 'pending',
      chat.isDeleted ? 1 : 0,
      chat.createdAt || Date.now(),
      chat.updatedAt || Date.now()
    ]);
  }

  private async updateChatInDatabase(chat: LocalChat): Promise<void> {
    await this.saveChatToDatabase(chat);
  }

  private rowToChat(row: any): LocalChat {
    return {
      localId: row.localId,
      id: row.id,
      name: row.name,
      avatar: row.avatar,
      isGroup: Boolean(row.isGroup),
      description: row.description,
      createdBy: row.createdBy,
      participants: row.participantIds ? JSON.parse(row.participantIds) : [],
      timestamp: row.timestamp || new Date().toISOString(),
      lastMessageId: row.lastMessageId,
      lastMessageText: row.lastMessageText,
      lastMessageTimestamp: row.lastMessageTime,
      unreadCount: row.unreadCount,
      isPinned: Boolean(row.isPinned),
      pinnedAt: row.pinnedAt,
      isArchived: Boolean(row.isArchived),
      archivedAt: row.archivedAt,
      isMuted: Boolean(row.isMuted),
      mutedUntil: row.mutedUntil,
      syncStatus: {
        status: row.syncStatus,
        lastSyncAttempt: row.lastSyncAttempt,
        retryCount: row.retryCount,
        errorMessage: row.errorMessage,
      },
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      isDeleted: Boolean(row.isDeleted),
      deletedAt: row.deletedAt,
    };
  }

  private async loadChatsIntoCache(): Promise<void> {
    try {
      const chats = await this.getAllChats(true, true);
      chats.forEach(chat => {
        this.chatCache.set(chat.localId, chat);
      });
      console.log(`📱 Loaded ${chats.length} chats into cache`);
    } catch (error) {
      console.error('❌ Failed to load chats into cache:', error);
    }
  }

  private clearChatListCache(): void {
    // Clear all cached chat lists
    const cacheKeys = [
      'all_chats_true_true',
      'all_chats_true_false',
      'all_chats_false_true',
      'all_chats_false_false',
    ];
    
    cacheKeys.forEach(key => {
      memoryCacheService.deleteSettings(key);
    });
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected && this.syncQueue.size > 0) {
      console.log('🔄 Network connected, processing chat sync queue...');
      this.processSyncQueue();
    }
  }

  private async processSyncQueue(): Promise<void> {
    if (!networkStateManager.isOnline() || this.syncQueue.size === 0) return;

    const chatIds = Array.from(this.syncQueue);
    this.syncQueue.clear();

    for (const chatId of chatIds) {
      try {
        // Here you would implement the actual sync logic with your backend
        // For now, we'll just mark as synced
        const chat = await this.getChat(chatId);
        if (chat) {
          chat.syncStatus.status = 'synced';
          chat.syncStatus.lastSyncAttempt = Date.now();
          await this.updateChatInDatabase(chat);
        }
      } catch (error) {
        console.error(`❌ Failed to sync chat ${chatId}:`, error);
        // Re-add to queue for retry
        this.syncQueue.add(chatId);
      }
    }
  }

  // Cleanup
  cleanup(): void {
    networkStateManager.removeListener('chatService');
    this.chatCache.clear();
    this.participantCache.clear();
    this.settingsCache.clear();
    this.syncQueue.clear();

    this.isInitialized = false;
    console.log('🧹 Offline chat service cleaned up');
  }

  // Statistics
  getStats(): {
    cachedChats: number;
    cachedParticipants: number;
    pendingSync: number;
  } {
    return {
      cachedChats: this.chatCache.size,
      cachedParticipants: this.participantCache.size,
      pendingSync: this.syncQueue.size,
    };
  }
}

// Export singleton instance
export const offlineChatService = new OfflineChatService();
export default offlineChatService;
