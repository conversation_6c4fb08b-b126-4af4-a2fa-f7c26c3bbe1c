import { ActivityIndicator, Text, View } from "react-native";

import { useResponsiveDimensions } from "../../hooks/useResponsiveDimensions";
import { fontSizes } from "../../utils/responsive";

interface LoadingStateProps {
  message?: string;
  size?: "small" | "large";
  color?: string;
  accessible?: boolean;
  testID?: string;
}

export default function LoadingState({
  message = "Loading...",
  size = "large",
  color = "#667eea",
  accessible = true,
  testID = "loading-state",
}: LoadingStateProps) {
  const { isXSmall } = useResponsiveDimensions();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        padding: isXSmall ? 16 : 24,
      }}
      accessible={accessible}
      accessibilityLabel={`Loading screen. ${message}`}
      accessibilityRole="progressbar"
      testID={testID}
    >
      <ActivityIndicator
        size={size}
        color={color}
        accessible={accessible}
        accessibilityLabel="Loading indicator"
        testID={`${testID}-indicator`}
      />
      <Text
        style={{
          marginTop: 12,
          fontSize: isXSmall ? fontSizes.sm : fontSizes.base,
          color: "#FFFFFF",
          textAlign: "center",
          maxWidth: "80%",
        }}
        accessible={accessible}
        accessibilityLabel={message}
        testID={`${testID}-message`}
      >
        {message}
      </Text>
    </View>
  );
}
