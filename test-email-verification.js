// Test Email Verification Functionality
require('dotenv').config();

console.log('🔧 Loading Firebase modules...');

const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword, sendEmailVerification, deleteUser } = require('firebase/auth');

console.log('✅ Firebase modules loaded successfully');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID
};

async function testEmailVerification() {
  console.log('🧪 Testing Email Verification System');
  console.log('=====================================');

  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    
    console.log('✅ Firebase initialized successfully');
    console.log('📧 Auth Domain:', firebaseConfig.authDomain);
    
    // Create a test user
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    
    console.log('\n📋 Step 1: Creating test user...');
    console.log('📧 Test email:', testEmail);
    
    const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword);
    const user = userCredential.user;
    
    console.log('✅ Test user created successfully');
    console.log('👤 User UID:', user.uid);
    console.log('📧 User email:', user.email);
    console.log('✉️ Email verified:', user.emailVerified);
    
    console.log('\n📋 Step 2: Sending verification email...');
    
    try {
      await sendEmailVerification(user);
      console.log('✅ Verification email sent successfully!');
      console.log('📧 Email sent to:', user.email);
      console.log('🔗 Check your email for the verification link');
      
      // Check if there are any additional settings needed
      console.log('\n📋 Step 3: Checking email verification status...');
      console.log('✉️ Email verified (before):', user.emailVerified);
      
      // Reload user to get latest status
      await user.reload();
      console.log('✉️ Email verified (after reload):', user.emailVerified);
      
    } catch (emailError) {
      console.error('❌ Failed to send verification email:', emailError);
      console.error('❌ Error code:', emailError.code);
      console.error('❌ Error message:', emailError.message);
      
      if (emailError.code === 'auth/invalid-email') {
        console.log('💡 Suggestion: Check if the email format is valid');
      } else if (emailError.code === 'auth/user-not-found') {
        console.log('💡 Suggestion: User might have been deleted');
      } else if (emailError.code === 'auth/too-many-requests') {
        console.log('💡 Suggestion: Too many requests, try again later');
      } else {
        console.log('💡 Suggestion: Check Firebase Authentication settings in console');
        console.log('💡 Make sure email verification is enabled');
        console.log('💡 Check if custom email templates are configured');
      }
    }
    
    console.log('\n📋 Step 4: Cleaning up test user...');
    
    try {
      await user.delete();
      console.log('✅ Test user deleted successfully');
    } catch (deleteError) {
      console.error('❌ Failed to delete test user:', deleteError);
      console.log('⚠️ You may need to manually delete the user from Firebase Console');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('❌ Error code:', error.code);
    console.error('❌ Error message:', error.message);
    
    if (error.code === 'auth/email-already-in-use') {
      console.log('💡 Test email already exists, this is expected for testing');
    } else if (error.code === 'auth/weak-password') {
      console.log('💡 Password is too weak, but this shouldn\'t happen with our test password');
    } else if (error.code === 'auth/invalid-email') {
      console.log('💡 Invalid email format');
    } else {
      console.log('💡 Check Firebase configuration and network connection');
    }
  }
  
  console.log('\n🎯 Email Verification Test Complete');
  console.log('=====================================');
  console.log('📝 Next steps if verification emails are not being received:');
  console.log('1. Check Firebase Console > Authentication > Templates');
  console.log('2. Verify sender email domain is configured');
  console.log('3. Check spam/junk folders');
  console.log('4. Ensure Firebase project has email verification enabled');
  console.log('5. Check if there are any domain restrictions');
}

// Run the test
testEmailVerification().catch(console.error);
