import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  TextInput,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  gradient: ['#87CEEB', '#4682B4', '#1E90FF'] as const, // Sky blue gradient
};

interface StoryCreatorModalProps {
  visible: boolean;
  onClose: () => void;
  onCameraPress: () => void;
  onGalleryPress?: () => void;
  onTextStoryPress?: () => void;
  currentUser?: any;
}

export const StoryCreatorModal: React.FC<StoryCreatorModalProps> = ({
  visible,
  onClose,
  onCameraPress,
  onGalleryPress,
  onTextStoryPress,
  currentUser,
}) => {
  const [textContent, setTextContent] = useState('');
  const [selectedBackground, setSelectedBackground] = useState(0);

  const backgroundOptions = [
    { type: 'gradient', colors: ['#FF6B6B', '#FF8E53'] as const },
    { type: 'gradient', colors: ['#4ECDC4', '#44A08D'] as const },
    { type: 'gradient', colors: ['#A8E6CF', '#7FCDCD'] as const },
    { type: 'gradient', colors: ['#FFD93D', '#FF6B6B'] as const },
    { type: 'gradient', colors: ['#6C5CE7', '#A29BFE'] as const },
    { type: 'gradient', colors: COLORS.gradient },
  ];

  const handleTextStory = () => {
    if (!textContent.trim()) {
      Alert.alert('Error', 'Please enter some text for your story');
      return;
    }

    // Handle text story creation
    Alert.alert('Success', 'Text story created successfully!');
    setTextContent('');
    onClose();
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Create Story</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content}>
          {/* Text Story Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Text Story</Text>
            <View style={styles.textStoryContainer}>
              <LinearGradient
                colors={backgroundOptions[selectedBackground].colors}
                style={styles.storyPreview}
              >
                <TextInput
                  style={styles.storyTextInput}
                  placeholder="Share your moment..."
                  placeholderTextColor="rgba(255,255,255,0.7)"
                  value={textContent}
                  onChangeText={setTextContent}
                  multiline
                  textAlign="center"
                  maxLength={200}
                />
              </LinearGradient>
              
              {/* Background Options */}
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.backgroundOptions}>
                {backgroundOptions.map((bg, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => setSelectedBackground(index)}
                    style={[
                      styles.backgroundOption,
                      selectedBackground === index && styles.selectedBackground
                    ]}
                  >
                    <LinearGradient
                      colors={bg.colors}
                      style={styles.backgroundPreview}
                    />
                  </TouchableOpacity>
                ))}
              </ScrollView>

              {textContent.length > 0 && (
                <TouchableOpacity style={styles.postButton} onPress={handleTextStory}>
                  <Text style={styles.postButtonText}>Share Story</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Media Options */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Add Media</Text>
            
            {/* Camera Button */}
            <TouchableOpacity style={styles.optionButton} onPress={onCameraPress}>
              <Ionicons name="camera" size={24} color={COLORS.primary} />
              <Text style={styles.optionText}>Take Photo/Video</Text>
              <Ionicons name="chevron-forward" size={20} color={COLORS.textMuted} />
            </TouchableOpacity>

            {/* Gallery Button */}
            {onGalleryPress && (
              <TouchableOpacity style={styles.optionButton} onPress={onGalleryPress}>
                <Ionicons name="images" size={24} color={COLORS.primary} />
                <Text style={styles.optionText}>Choose from Gallery</Text>
                <Ionicons name="chevron-forward" size={20} color={COLORS.textMuted} />
              </TouchableOpacity>
            )}
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
  },
  textStoryContainer: {
    alignItems: 'center',
  },
  storyPreview: {
    width: SCREEN_WIDTH * 0.6,
    height: SCREEN_WIDTH * 0.8,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginBottom: 16,
  },
  storyTextInput: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    width: '100%',
  },
  backgroundOptions: {
    marginBottom: 16,
  },
  backgroundOption: {
    marginHorizontal: 8,
    borderRadius: 20,
    padding: 2,
  },
  selectedBackground: {
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  backgroundPreview: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  postButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  postButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    marginLeft: 16,
    fontWeight: '500',
  },
});
