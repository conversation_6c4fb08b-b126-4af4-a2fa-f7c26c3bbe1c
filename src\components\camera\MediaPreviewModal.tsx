import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Image,
  TextInput,
  Alert,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Video, ResizeMode } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants/theme';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface MediaPreviewModalProps {
  visible: boolean;
  mediaUri: string;
  mediaType: 'image' | 'video';
  onClose: () => void;
  onPost: (caption: string) => void;
  onRetake?: () => void;
}

export const MediaPreviewModal: React.FC<MediaPreviewModalProps> = ({
  visible,
  mediaUri,
  mediaType,
  onClose,
  onPost,
  onRetake,
}) => {
  const [caption, setCaption] = useState('');
  const [isPosting, setIsPosting] = useState(false);

  const handlePost = async () => {
    if (isPosting) return;

    setIsPosting(true);
    try {
      await onPost(caption.trim());
      setCaption('');
    } catch (error) {
      console.error('❌ Error posting media:', error);
      Alert.alert('Error', 'Failed to post. Please try again.');
    } finally {
      setIsPosting(false);
    }
  };

  const handleClose = () => {
    setCaption('');
    onClose();
  };

  if (!visible || !mediaUri) return null;

  return (
    <Modal visible={visible} animationType="slide" statusBarTranslucent>
      <KeyboardAvoidingView 
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.headerButton} onPress={handleClose}>
            <Ionicons name="close" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          
          <Text style={styles.headerTitle}>Preview</Text>
          
          {onRetake && (
            <TouchableOpacity style={styles.headerButton} onPress={onRetake}>
              <Ionicons name="camera-outline" size={24} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </View>

        {/* Media Preview */}
        <View style={styles.mediaContainer}>
          {mediaType === 'image' ? (
            <Image
              source={{ uri: mediaUri }}
              style={styles.media}
              resizeMode="contain"
            />
          ) : (
            <Video
              source={{ uri: mediaUri }}
              style={styles.media}
              resizeMode={ResizeMode.CONTAIN}
              shouldPlay
              isLooping
              isMuted={false}
            />
          )}
          
          {/* Media Type Indicator */}
          <View style={styles.mediaTypeIndicator}>
            <Ionicons
              name={mediaType === 'image' ? 'image' : 'videocam'}
              size={16}
              color="#FFFFFF"
            />
            <Text style={styles.mediaTypeText}>
              {mediaType === 'image' ? 'Photo' : 'Video'}
            </Text>
          </View>
        </View>

        {/* Caption Input */}
        <View style={styles.captionContainer}>
          <View style={styles.captionInputContainer}>
            <TextInput
              style={styles.captionInput}
              placeholder="Write a caption..."
              placeholderTextColor="#9CA3AF"
              value={caption}
              onChangeText={setCaption}
              multiline
              maxLength={500}
              textAlignVertical="top"
            />
            <Text style={styles.characterCount}>
              {caption.length}/500
            </Text>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.cancelButton]}
              onPress={handleClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionButton,
                styles.postButton,
                isPosting && styles.postButtonDisabled,
              ]}
              onPress={handlePost}
              disabled={isPosting}
            >
              <Text style={styles.postButtonText}>
                {isPosting ? 'Posting...' : 'Post'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Additional Options */}
        <View style={styles.optionsContainer}>
          <TouchableOpacity style={styles.optionButton}>
            <Ionicons name="location-outline" size={20} color="#FFFFFF" />
            <Text style={styles.optionText}>Add Location</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.optionButton}>
            <Ionicons name="people-outline" size={20} color="#FFFFFF" />
            <Text style={styles.optionText}>Tag People</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.optionButton}>
            <Ionicons name="musical-notes-outline" size={20} color="#FFFFFF" />
            <Text style={styles.optionText}>Add Music</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  mediaContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  media: {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH * (16 / 9), // 16:9 aspect ratio
    maxHeight: SCREEN_HEIGHT * 0.6,
  },
  mediaTypeIndicator: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  mediaTypeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  captionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  captionInputContainer: {
    backgroundColor: '#1F2937',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  captionInput: {
    color: '#FFFFFF',
    fontSize: 16,
    minHeight: 80,
    maxHeight: 120,
    textAlignVertical: 'top',
  },
  characterCount: {
    color: '#9CA3AF',
    fontSize: 12,
    textAlign: 'right',
    marginTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: '#374151',
  },
  cancelButtonText: {
    color: '#D1D5DB',
    fontSize: 16,
    fontWeight: '600',
  },
  postButton: {
    backgroundColor: COLORS.primary,
  },
  postButtonDisabled: {
    backgroundColor: '#6B7280',
  },
  postButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  optionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#374151',
  },
  optionButton: {
    alignItems: 'center',
    padding: 8,
  },
  optionText: {
    color: '#D1D5DB',
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
});
