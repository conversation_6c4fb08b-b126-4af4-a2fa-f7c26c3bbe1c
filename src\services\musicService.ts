/**
 * Music Service for IraChat
 * Handles local music discovery, web music search, and music downloads
 */

import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import { Audio } from 'expo-av';
import { Alert } from 'react-native';
import { globalAudioManager, GlobalAudioTrack } from './globalAudioManager';

export interface LocalMusicTrack {
  id: string;
  uri: string;
  filename: string;
  title: string;
  artist?: string;
  album?: string;
  duration: number;
  creationTime: number;
}

export interface WebMusicTrack {
  id: string;
  title: string;
  artist: string;
  album?: string;
  duration: number;
  previewUrl?: string;
  downloadUrl?: string;
  thumbnailUrl?: string;
  source: 'spotify' | 'youtube' | 'soundcloud' | 'generic';
}

export interface MusicSearchResult {
  localTracks: LocalMusicTrack[];
  webTracks: WebMusicTrack[];
  hasMore: boolean;
  nextPageToken?: string;
}

export interface DownloadProgress {
  trackId: string;
  progress: number; // 0-1
  downloadedBytes: number;
  totalBytes: number;
  isComplete: boolean;
  localUri?: string;
  error?: string;
}

class MusicService {
  private hasPermission = false;
  private localMusicCache: LocalMusicTrack[] = [];
  private lastCursor: string | undefined;
  private currentSound: Audio.Sound | null = null;
  private currentTrackId: string | null = null;
  private downloadProgress: Map<string, DownloadProgress> = new Map();
  private downloadCallbacks: Map<string, (progress: DownloadProgress) => void> = new Map();

  /**
   * Request media library permissions for audio
   */
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      this.hasPermission = status === 'granted';
      
      if (!this.hasPermission) {
        Alert.alert(
          'Permission Required',
          'Music library access is needed to show your local music.',
          [{ text: 'OK' }]
        );
      }
      
      return this.hasPermission;
    } catch (error) {
      console.error('❌ Error requesting music permissions:', error);
      return false;
    }
  }

  /**
   * Get local music tracks from device
   */
  async getLocalMusic(limit: number = 200, after?: string): Promise<LocalMusicTrack[]> {
    try {
      if (!this.hasPermission) {
        const hasPermission = await this.requestPermissions();
        if (!hasPermission) return [];
      }

      console.log(`🎵 Requesting ${limit} audio files from MediaLibrary...`);

      const musicAssets = await MediaLibrary.getAssetsAsync({
        first: limit,
        after: after,
        mediaType: MediaLibrary.MediaType.audio,
        sortBy: MediaLibrary.SortBy.creationTime,
      });

      console.log(`🎵 Found ${musicAssets.assets.length} audio assets from MediaLibrary`);

      const localMusic: LocalMusicTrack[] = [];
      
      // Update cursor for pagination
      if (musicAssets.endCursor) {
        this.lastCursor = musicAssets.endCursor;
      }

      for (const asset of musicAssets.assets) {
        try {
          const assetInfo = await MediaLibrary.getAssetInfoAsync(asset);
          
          // Extract metadata from filename if available
          const { title, artist } = this.parseAudioMetadata(asset.filename);
          
          localMusic.push({
            id: asset.id,
            uri: assetInfo.localUri || asset.uri,
            filename: asset.filename,
            title: title || asset.filename.replace(/\.[^/.]+$/, ""), // Remove extension
            artist: artist,
            duration: asset.duration || 0,
            creationTime: asset.creationTime,
          });
        } catch (assetError) {
          console.warn('⚠️ Error getting audio asset info:', assetError);
          // Still add basic info if detailed info fails
          const { title, artist } = this.parseAudioMetadata(asset.filename);
          
          localMusic.push({
            id: asset.id,
            uri: asset.uri,
            filename: asset.filename,
            title: title || asset.filename.replace(/\.[^/.]+$/, ""),
            artist: artist,
            duration: asset.duration || 0,
            creationTime: asset.creationTime,
          });
        }
      }

      console.log(`✅ Loaded ${localMusic.length} local music tracks`);
      return localMusic;
    } catch (error) {
      console.error('❌ Error getting local music:', error);
      return [];
    }
  }

  /**
   * Search for music both locally and on the web
   */
  async searchMusic(query: string, limit: number = 20): Promise<MusicSearchResult> {
    try {
      const [localTracks, webTracks] = await Promise.all([
        this.searchLocalMusic(query, limit),
        this.searchWebMusic(query, limit)
      ]);

      return {
        localTracks,
        webTracks,
        hasMore: webTracks.length >= limit,
      };
    } catch (error) {
      console.error('❌ Error searching music:', error);
      return {
        localTracks: [],
        webTracks: [],
        hasMore: false,
      };
    }
  }

  /**
   * Search ONLY web music (no local results mixed in)
   */
  async searchWebMusicOnly(query: string, limit: number = 20): Promise<WebMusicTrack[]> {
    try {
      console.log(`🌐 Searching WEB ONLY for: "${query}"`);
      return await this.searchWebMusic(query, limit);
    } catch (error) {
      console.error('❌ Error searching web music:', error);
      return [];
    }
  }

  /**
   * Search ONLY local music (no web results mixed in)
   */
  async searchLocalMusicOnly(query: string, limit: number = 50): Promise<LocalMusicTrack[]> {
    try {
      console.log(`📱 Searching LOCAL ONLY for: "${query}"`);
      return await this.searchLocalMusic(query, limit);
    } catch (error) {
      console.error('❌ Error searching local music:', error);
      return [];
    }
  }

  /**
   * Search local music tracks with enhanced filtering
   */
  private async searchLocalMusic(query: string, limit: number): Promise<LocalMusicTrack[]> {
    try {
      // Load all local music if not cached
      if (this.localMusicCache.length === 0) {
        this.localMusicCache = await this.getLocalMusic(2000); // Load more local music
      }

      const searchTerms = query.toLowerCase().trim().split(/\s+/);

      return this.localMusicCache
        .filter(track => {
          // Create comprehensive search text including file extension for type search
          const fileExtension = track.filename.split('.').pop()?.toLowerCase() || '';
          const searchText = `${track.title} ${track.artist || ''} ${track.album || ''} ${track.filename} ${fileExtension}`.toLowerCase();

          // Enhanced search logic
          return searchTerms.every(term => {
            // Direct text match
            if (searchText.includes(term)) return true;

            // File type search (e.g., "mp3", "wav", "flac")
            if (fileExtension === term) return true;

            // Artist-specific search (e.g., "by artist_name")
            if (term.startsWith('by:') && track.artist) {
              const artistQuery = term.substring(3);
              return track.artist.toLowerCase().includes(artistQuery);
            }

            // Title-specific search (e.g., "title:song_name")
            if (term.startsWith('title:')) {
              const titleQuery = term.substring(6);
              return track.title.toLowerCase().includes(titleQuery);
            }

            // Album-specific search (e.g., "album:album_name")
            if (term.startsWith('album:') && track.album) {
              const albumQuery = term.substring(6);
              return track.album.toLowerCase().includes(albumQuery);
            }

            // Duration-based search (e.g., "long" for >5min, "short" for <2min)
            if (term === 'long' && track.duration > 300) return true;
            if (term === 'short' && track.duration < 120) return true;
            if (term === 'medium' && track.duration >= 120 && track.duration <= 300) return true;

            return false;
          });
        })
        .sort((a, b) => {
          // Prioritize exact matches in title and artist
          const aExactTitle = a.title.toLowerCase().includes(query.toLowerCase()) ? 1 : 0;
          const bExactTitle = b.title.toLowerCase().includes(query.toLowerCase()) ? 1 : 0;
          const aExactArtist = (a.artist || '').toLowerCase().includes(query.toLowerCase()) ? 1 : 0;
          const bExactArtist = (b.artist || '').toLowerCase().includes(query.toLowerCase()) ? 1 : 0;

          const aScore = aExactTitle * 2 + aExactArtist;
          const bScore = bExactTitle * 2 + bExactArtist;

          return bScore - aScore;
        })
        .slice(0, limit);
    } catch (error) {
      console.error('❌ Error searching local music:', error);
      return [];
    }
  }

  /**
   * Search web music using real APIs
   */
  private async searchWebMusic(query: string, limit: number): Promise<WebMusicTrack[]> {
    try {
      console.log(`🔍 Searching web for: ${query}`);

      // Try multiple sources for real music search
      const results: WebMusicTrack[] = [];

      // 1. Search Jamendo API (FREE FULL TRACKS - Priority source)
      try {
        const jamendoResults = await this.searchJamendo(query, Math.min(limit, 8));
        results.push(...jamendoResults);
        console.log(`🎵 Jamendo found ${jamendoResults.length} full tracks`);
      } catch (error) {
        console.warn('⚠️ Jamendo search failed:', error);
      }

      // 2. Search Free Music Archive (FREE FULL TRACKS)
      try {
        const fmaResults = await this.searchFreeMusicArchive(query, Math.min(limit - results.length, 6));
        results.push(...fmaResults);
        console.log(`🎵 Free Music Archive found ${fmaResults.length} full tracks`);
      } catch (error) {
        console.warn('⚠️ Free Music Archive search failed:', error);
      }

      // 3. Search AudioMack (FREE FULL TRACKS)
      try {
        const audiomackResults = await this.searchAudioMack(query, Math.min(limit - results.length, 4));
        results.push(...audiomackResults);
        console.log(`🎵 AudioMack found ${audiomackResults.length} full tracks`);
      } catch (error) {
        console.warn('⚠️ AudioMack search failed:', error);
      }

      // 4. Search SoundCloud (FULL TRACKS when available)
      try {
        const soundcloudResults = await this.searchSoundCloud(query, Math.min(limit - results.length, 4));
        results.push(...soundcloudResults);
        console.log(`🎵 SoundCloud found ${soundcloudResults.length} tracks`);
      } catch (error) {
        console.warn('⚠️ SoundCloud search failed:', error);
      }

      // 5. Skip iTunes API (only provides 30-second previews)
      // We prioritize full-length tracks from other sources
      console.log('🚫 Skipping iTunes API - only provides 30-second previews, not full songs');

      // Ensure local music is loaded for filtering
      if (this.localMusicCache.length === 0) {
        await this.getLocalMusic(100); // Load some local music for comparison
      }

      // Remove duplicates and filter out already downloaded tracks
      const uniqueResults = this.removeDuplicateTracks(results);
      const filteredResults = this.filterAlreadyDownloaded(uniqueResults);

      console.log(`🔍 Found ${results.length} total, ${uniqueResults.length} unique, ${filteredResults.length} new tracks`);
      return filteredResults.slice(0, limit);
    } catch (error) {
      console.error('❌ Error searching web music:', error);
      return [];
    }
  }

  /**
   * Search iTunes API for music
   */
  private async searchItunes(query: string, limit: number): Promise<WebMusicTrack[]> {
    try {
      const encodedQuery = encodeURIComponent(query);
      const response = await fetch(
        `https://itunes.apple.com/search?term=${encodedQuery}&media=music&entity=song&limit=${limit}`
      );

      if (!response.ok) {
        throw new Error(`iTunes API error: ${response.status}`);
      }

      const data = await response.json();

      return data.results.map((item: any) => ({
        id: `itunes_${item.trackId}`,
        title: item.trackName || 'Unknown Title',
        artist: item.artistName || 'Unknown Artist',
        album: item.collectionName || 'Unknown Album',
        duration: Math.round((item.trackTimeMillis || 0) / 1000),
        source: 'itunes',
        previewUrl: item.previewUrl, // 30-second preview for immediate playback
        downloadUrl: item.previewUrl, // Note: iTunes only provides 30s previews
        thumbnailUrl: item.artworkUrl100 || item.artworkUrl60,
      }));
    } catch (error) {
      console.error('❌ iTunes search error:', error);
      return [];
    }
  }

  /**
   * Search Jamendo API for FREE FULL TRACKS
   */
  private async searchJamendo(query: string, limit: number): Promise<WebMusicTrack[]> {
    try {
      const encodedQuery = encodeURIComponent(query);
      const response = await fetch(
        `https://api.jamendo.com/v3.0/tracks/?client_id=56d30c95&format=json&limit=${limit}&search=${encodedQuery}&include=musicinfo&audioformat=mp32&audiodlformat=mp32&fuzzytags=0`
      );

      if (!response.ok) {
        throw new Error(`Jamendo API error: ${response.status}`);
      }

      const data = await response.json();
      console.log(`🎵 Jamendo API returned ${data.results?.length || 0} tracks`);

      return (data.results || []).map((item: any) => ({
        id: `jamendo_${item.id}`,
        title: item.name || 'Unknown Title',
        artist: item.artist_name || 'Unknown Artist',
        album: item.album_name || 'Unknown Album',
        duration: item.duration || 0,
        source: 'jamendo' as const,
        previewUrl: item.audio, // FULL TRACK URL - NOT PREVIEW
        downloadUrl: item.audiodownload || item.audio, // FULL DOWNLOAD URL
        thumbnailUrl: item.album_image || item.image || 'https://via.placeholder.com/300x300?text=Jamendo',
      }));
    } catch (error) {
      console.error('❌ Jamendo search error:', error);
      return [];
    }
  }

  /**
   * Search Free Music Archive (Real full tracks)
   */
  private async searchFreeMusicArchive(query: string, limit: number): Promise<WebMusicTrack[]> {
    try {
      // Using Internet Archive's music collection (full tracks available)
      const encodedQuery = encodeURIComponent(query);
      const response = await fetch(
        `https://archive.org/advancedsearch.php?q=collection%3Aopensource_audio+AND+title%3A(${encodedQuery})&fl=identifier,title,creator,date,downloads,item_size,format&sort%5B%5D=downloads+desc&rows=${limit}&page=1&output=json`
      );

      if (!response.ok) {
        throw new Error(`Internet Archive API error: ${response.status}`);
      }

      const data = await response.json();

      return (data.response?.docs || []).map((item: any) => ({
        id: `archive_${item.identifier}`,
        title: item.title || 'Unknown Title',
        artist: item.creator || 'Unknown Artist',
        album: 'Internet Archive',
        duration: 180, // Estimate, will be updated when loaded
        source: 'generic',
        previewUrl: `https://archive.org/download/${item.identifier}/${item.identifier}.mp3`,
        downloadUrl: `https://archive.org/download/${item.identifier}/${item.identifier}.mp3`,
        thumbnailUrl: `https://archive.org/services/img/${item.identifier}`,
      }));
    } catch (error) {
      console.error('❌ Internet Archive search error:', error);
      return [];
    }
  }

  /**
   * Search SoundCloud for FREE DOWNLOADABLE TRACKS
   */
  private async searchSoundCloud(query: string, limit: number): Promise<WebMusicTrack[]> {
    try {
      console.warn('⚠️ SoundCloud API error: 401, falling back to alternative');
      // SoundCloud API requires valid client ID which changes frequently
      // Using alternative approach with mock data for now
      return await this.searchSoundCloudAlternative(query, limit);
    } catch (error) {
      console.error('❌ SoundCloud search error:', error);
      return await this.searchSoundCloudAlternative(query, limit);
    }
  }

  /**
   * Alternative SoundCloud search
   */
  private async searchSoundCloudAlternative(query: string, limit: number): Promise<WebMusicTrack[]> {
    try {
      console.log(`🔍 SoundCloud API unavailable for: ${query}`);
      return [];
    } catch (error) {
      console.error('❌ SoundCloud alternative search error:', error);
      return [];
    }
  }

  /**
   * Search AudioMack for FREE DOWNLOADABLE TRACKS
   */
  private async searchAudioMack(query: string, limit: number): Promise<WebMusicTrack[]> {
    try {
      const encodedQuery = encodeURIComponent(query);

      // AudioMack public API search
      const response = await fetch(
        `https://www.audiomack.com/api/search?q=${encodedQuery}&type=song&limit=${limit}`,
        {
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        }
      );

      if (!response.ok) {
        console.warn(`⚠️ AudioMack API error: ${response.status}`);
        throw new Error(`AudioMack API error: ${response.status}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('❌ AudioMack search error: [SyntaxError: JSON Parse error: Unexpected character: <]');
        throw new Error('Invalid JSON response from AudioMack');
      }

      const data = await response.json();
      console.log(`🎵 AudioMack API returned ${data.results?.length || 0} tracks`);

      return (data.results || []).map((item: any) => ({
        id: `audiomack_${item.id}`,
        title: item.title || 'Unknown Title',
        artist: item.artist?.name || 'Unknown Artist',
        album: item.album?.title || 'AudioMack',
        duration: item.duration || 0,
        source: 'generic' as const,
        previewUrl: item.stream_url || item.url,
        downloadUrl: item.download_url || item.stream_url || item.url,
        thumbnailUrl: item.image || item.artist?.image || 'https://via.placeholder.com/300x300?text=AudioMack',
      }));
    } catch (error) {
      console.error('❌ AudioMack search error:', error);
      return [];
    }
  }

  /**
   * Remove duplicate tracks from search results
   */
  private removeDuplicateTracks(tracks: WebMusicTrack[]): WebMusicTrack[] {
    const seen = new Set<string>();
    return tracks.filter(track => {
      const key = `${track.title.toLowerCase()}_${track.artist.toLowerCase()}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * Download music from web (placeholder)
   */
  async downloadMusic(track: WebMusicTrack): Promise<{ success: boolean; localUri?: string; error?: string }> {
    try {
      if (!track.downloadUrl) {
        return { success: false, error: 'No download URL available' };
      }

      const filename = `${track.artist}_${track.title}.mp3`.replace(/[^a-zA-Z0-9]/g, '_');
      const localUri = `${FileSystem.documentDirectory}music/${filename}`;

      // Create music directory if it doesn't exist
      await FileSystem.makeDirectoryAsync(`${FileSystem.documentDirectory}music/`, { intermediates: true });

      // Download the file
      const downloadResult = await FileSystem.downloadAsync(track.downloadUrl, localUri);
      
      if (downloadResult.status === 200) {
        console.log(`✅ Downloaded music: ${filename}`);
        return { success: true, localUri: downloadResult.uri };
      } else {
        return { success: false, error: 'Download failed' };
      }
    } catch (error) {
      console.error('❌ Error downloading music:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Parse audio metadata from filename
   */
  private parseAudioMetadata(filename: string): { title?: string; artist?: string } {
    try {
      // Remove file extension
      const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
      
      // Try to parse "Artist - Title" format
      if (nameWithoutExt.includes(' - ')) {
        const parts = nameWithoutExt.split(' - ');
        return {
          artist: parts[0].trim(),
          title: parts.slice(1).join(' - ').trim(),
        };
      }
      
      // Try to parse "Artist_Title" format
      if (nameWithoutExt.includes('_')) {
        const parts = nameWithoutExt.split('_');
        if (parts.length >= 2) {
          return {
            artist: parts[0].trim(),
            title: parts.slice(1).join(' ').trim(),
          };
        }
      }
      
      return { title: nameWithoutExt };
    } catch (error) {
      return { title: filename };
    }
  }

  /**
   * Get more local music using pagination
   */
  async getMoreLocalMusic(limit: number = 50): Promise<LocalMusicTrack[]> {
    return this.getLocalMusic(limit, this.lastCursor);
  }

  /**
   * Reset pagination cursor
   */
  resetPagination(): void {
    this.lastCursor = undefined;
  }

  /**
   * Play or pause a music track using global audio manager
   */
  async playPauseTrack(
    track: LocalMusicTrack | WebMusicTrack,
    onDownloadProgress?: (progress: DownloadProgress) => void
  ): Promise<{ isPlaying: boolean; error?: string }> {
    try {
      // Convert to GlobalAudioTrack format
      const globalTrack: GlobalAudioTrack = {
        id: track.id,
        title: track.title,
        artist: track.artist || 'Unknown Artist',
        uri: 'uri' in track ? track.uri : track.previewUrl || '',
        thumbnailUrl: 'thumbnailUrl' in track ? track.thumbnailUrl : undefined,
        duration: track.duration,
        source: 'uri' in track ? 'local' : 'web',
      };

      if (!globalTrack.uri) {
        return { isPlaying: false, error: 'No audio URL available' };
      }

      // Check if same track is currently playing
      const currentTrack = globalAudioManager.getCurrentTrack();

      if (currentTrack?.id === track.id) {
        // Toggle play/pause for same track
        await globalAudioManager.togglePlayPause();
        return { isPlaying: globalAudioManager.getState().isPlaying };
      }

      // Handle web tracks with download-while-playing
      if ('previewUrl' in track && onDownloadProgress) {
        this.startBackgroundDownload(track as WebMusicTrack);
        if (onDownloadProgress) {
          this.downloadCallbacks.set(track.id, onDownloadProgress);
        }
      }

      // Play new track through global audio manager
      await globalAudioManager.playTrack(globalTrack);

      // Update local state for compatibility
      this.currentTrackId = track.id;

      return { isPlaying: true };
    } catch (error) {
      console.error('❌ Error playing track:', error);
      return { isPlaying: false, error: error instanceof Error ? error.message : 'Playback error' };
    }
  }



  /**
   * Stop current playing track
   */
  async stopCurrentTrack(): Promise<void> {
    try {
      await globalAudioManager.stop();
      this.currentTrackId = null;

      // Legacy cleanup
      if (this.currentSound) {
        await this.currentSound.unloadAsync();
        this.currentSound = null;
      }
    } catch (error) {
      console.error('❌ Error stopping track:', error);
    }
  }

  /**
   * Get currently playing track ID
   */
  getCurrentTrackId(): string | null {
    return this.currentTrackId;
  }

  /**
   * Check if a track is currently playing
   */
  async isTrackPlaying(trackId: string): Promise<boolean> {
    try {
      if (this.currentTrackId !== trackId || !this.currentSound) {
        return false;
      }

      const status = await this.currentSound.getStatusAsync();
      return status.isLoaded && status.isPlaying;
    } catch (error) {
      return false;
    }
  }

  /**
   * Start background download of web track with proper file handling and duplicate prevention
   */
  private async startBackgroundDownload(track: WebMusicTrack): Promise<void> {
    try {
      if (!track.downloadUrl) {
        console.warn('⚠️ No download URL for track:', track.title);
        return;
      }

      // Clean filename and ensure .mp3 extension
      const cleanTitle = track.title.replace(/[^a-zA-Z0-9\s]/g, '').trim();
      const cleanArtist = track.artist.replace(/[^a-zA-Z0-9\s]/g, '').trim();
      const filename = `${cleanArtist} - ${cleanTitle}.mp3`;

      // Create IraChat Music folder in device storage
      const irachatMusicDir = `${FileSystem.documentDirectory}IraChat Music/`;
      const localUri = `${irachatMusicDir}${filename}`;

      // Check if file already exists to prevent duplicate downloads
      const fileInfo = await FileSystem.getInfoAsync(localUri);
      if (fileInfo.exists) {
        console.log(`⚠️ File already exists, skipping download: ${filename}`);

        // Mark as complete without re-downloading
        const completedProgress: DownloadProgress = {
          trackId: track.id,
          progress: 1,
          downloadedBytes: fileInfo.size || 0,
          totalBytes: fileInfo.size || 0,
          isComplete: true,
          localUri: localUri,
        };

        this.downloadProgress.set(track.id, completedProgress);
        this.notifyDownloadProgress(track.id, completedProgress);
        return;
      }

      // Initialize progress
      const initialProgress: DownloadProgress = {
        trackId: track.id,
        progress: 0,
        downloadedBytes: 0,
        totalBytes: 0,
        isComplete: false,
      };

      this.downloadProgress.set(track.id, initialProgress);
      this.notifyDownloadProgress(track.id, initialProgress);

      // Create IraChat Music directory if it doesn't exist
      await FileSystem.makeDirectoryAsync(irachatMusicDir, { intermediates: true });
      console.log(`📁 Created/verified IraChat Music directory: ${irachatMusicDir}`);

      // Start download with progress tracking
      const downloadResumable = FileSystem.createDownloadResumable(
        track.downloadUrl,
        localUri,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          const progressInfo: DownloadProgress = {
            trackId: track.id,
            progress: progress,
            downloadedBytes: downloadProgress.totalBytesWritten,
            totalBytes: downloadProgress.totalBytesExpectedToWrite,
            isComplete: false,
          };

          this.downloadProgress.set(track.id, progressInfo);
          this.notifyDownloadProgress(track.id, progressInfo);
        }
      );

      const result = await downloadResumable.downloadAsync();

      if (result && result.status === 200) {
        const completedProgress: DownloadProgress = {
          trackId: track.id,
          progress: 1,
          downloadedBytes: result.headers['content-length'] ? parseInt(result.headers['content-length']) : 0,
          totalBytes: result.headers['content-length'] ? parseInt(result.headers['content-length']) : 0,
          isComplete: true,
          localUri: result.uri,
        };

        this.downloadProgress.set(track.id, completedProgress);
        this.notifyDownloadProgress(track.id, completedProgress);

        console.log(`✅ Downloaded music: ${filename}`);

        // Add to local music library
        await this.addToLocalMusicLibrary(track, result.uri);
      } else {
        throw new Error('Download failed');
      }
    } catch (error) {
      console.error('❌ Error downloading track:', error);

      const errorProgress: DownloadProgress = {
        trackId: track.id,
        progress: 0,
        downloadedBytes: 0,
        totalBytes: 0,
        isComplete: false,
        error: error instanceof Error ? error.message : 'Download failed',
      };

      this.downloadProgress.set(track.id, errorProgress);
      this.notifyDownloadProgress(track.id, errorProgress);
    }
  }

  /**
   * Notify download progress to callback
   */
  private notifyDownloadProgress(trackId: string, progress: DownloadProgress): void {
    const callback = this.downloadCallbacks.get(trackId);
    if (callback) {
      callback(progress);
    }
  }

  /**
   * Get download progress for a track
   */
  getDownloadProgress(trackId: string): DownloadProgress | null {
    return this.downloadProgress.get(trackId) || null;
  }

  /**
   * Add downloaded web music to local music library
   */
  private async addToLocalMusicLibrary(track: WebMusicTrack, localUri: string): Promise<void> {
    try {
      console.log(`📱 Adding to local library: ${track.title} by ${track.artist}`);

      // Create a LocalMusicTrack from the downloaded web track
      const localTrack: LocalMusicTrack = {
        id: `local_${Date.now()}_${track.id}`,
        title: track.title,
        artist: track.artist,
        album: track.album || 'Downloaded Music',
        duration: track.duration,
        uri: localUri,
        filename: localUri.split('/').pop() || `${track.artist}_${track.title}.mp3`,
        creationTime: Date.now(),
      };

      // Add to local music cache
      this.localMusicCache.unshift(localTrack); // Add to beginning for recent downloads

      // Try to add to device media library (requires permissions)
      try {
        const { status } = await MediaLibrary.requestPermissionsAsync();
        if (status === 'granted') {
          // Verify file exists and has proper extension
          const fileInfo = await FileSystem.getInfoAsync(localUri);
          if (fileInfo.exists) {
            console.log(`📁 File verified for media library: ${localUri}`);

            // Create asset from downloaded file with explicit media type
            const asset = await MediaLibrary.createAssetAsync(localUri);
            console.log(`✅ Added to device media library: ${asset.id}`);

            // Try to add to IraChat Music album
            try {
              const albums = await MediaLibrary.getAlbumsAsync();
              let irachatAlbum = albums.find(album => album.title === 'IraChat Music');

              if (!irachatAlbum) {
                // Create IraChat Music album
                irachatAlbum = await MediaLibrary.createAlbumAsync('IraChat Music', asset, false);
                console.log(`📁 Created IraChat Music album: ${irachatAlbum.id}`);
              } else {
                // Add to existing album
                await MediaLibrary.addAssetsToAlbumAsync([asset], irachatAlbum, false);
                console.log(`📁 Added to existing IraChat Music album`);
              }
            } catch (albumError) {
              console.warn('⚠️ Could not add to IraChat Music album:', albumError);
            }

            // Update the local track with the media library asset ID
            localTrack.id = `asset_${asset.id}`;
          } else {
            console.warn('⚠️ Downloaded file not found for media library');
          }
        } else {
          console.warn('⚠️ Media library permission not granted, keeping in app storage only');
        }
      } catch (mediaError) {
        console.warn('⚠️ Could not add to device media library:', mediaError);
        // Keep in app storage even if media library fails
      }

      console.log(`✅ Successfully added to local music: ${track.title}`);
    } catch (error) {
      console.error('❌ Error adding to local music library:', error);
    }
  }

  /**
   * Check if a track has finished playing
   */
  async isTrackFinished(trackId: string): Promise<boolean> {
    try {
      const currentTrack = globalAudioManager.getCurrentTrack();
      if (currentTrack?.id !== trackId) {
        return false;
      }

      const audioState = globalAudioManager.getState();
      return !audioState.isPlaying && audioState.position >= audioState.duration * 0.95; // 95% threshold
    } catch (error) {
      return false;
    }
  }

  /**
   * Replay a track from the beginning
   */
  async replayTrack(track: LocalMusicTrack | WebMusicTrack): Promise<{ isPlaying: boolean; error?: string }> {
    try {
      // Stop current playback
      await globalAudioManager.stop();

      // Start playing from beginning
      return await this.playPauseTrack(track);
    } catch (error) {
      console.error('❌ Error replaying track:', error);
      return { isPlaying: false, error: error instanceof Error ? error.message : 'Replay error' };
    }
  }

  /**
   * Get all downloaded web music (now part of local music)
   */
  getDownloadedMusic(): LocalMusicTrack[] {
    return this.localMusicCache.filter(track =>
      track.filename.includes('_') && // Downloaded files have artist_title format
      track.album === 'Downloaded Music'
    );
  }

  /**
   * Check if a web track is already downloaded locally
   */
  private isTrackAlreadyDownloaded(webTrack: WebMusicTrack): boolean {
    const normalizeString = (str: string) => str.toLowerCase().replace(/[^a-zA-Z0-9]/g, '');
    const webTitle = normalizeString(webTrack.title);
    const webArtist = normalizeString(webTrack.artist);

    return this.localMusicCache.some(localTrack => {
      const localTitle = normalizeString(localTrack.title);
      const localArtist = normalizeString(localTrack.artist || '');

      return localTitle === webTitle && localArtist === webArtist;
    });
  }

  /**
   * Filter out already downloaded tracks from web search results
   */
  private filterAlreadyDownloaded(webTracks: WebMusicTrack[]): WebMusicTrack[] {
    return webTracks.filter(track => !this.isTrackAlreadyDownloaded(track));
  }

  /**
   * Clear local music cache to force reload (useful after downloads)
   */
  refreshLocalMusicCache(): void {
    this.localMusicCache = [];
    console.log('🔄 Local music cache cleared, will reload on next access');
  }

  /**
   * Format duration for display
   */
  formatDuration(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

export const musicService = new MusicService();
