/**
 * Utility functions for parsing and formatting prices
 */

export interface ParsedPrice {
  value: number;
  formatted: string;
  isValid: boolean;
}

/**
 * Parse price input that can include K, M suffixes
 * Examples: "50K" -> 50000, "1.5M" -> 1500000, "250000" -> 250000
 */
export function parsePrice(input: string): ParsedPrice {
  if (!input || typeof input !== 'string') {
    return { value: 0, formatted: '', isValid: false };
  }

  // Remove whitespace and convert to uppercase
  const cleanInput = input.trim().toUpperCase();
  
  // Handle empty input
  if (!cleanInput) {
    return { value: 0, formatted: '', isValid: false };
  }

  // Check for K (thousands) suffix
  if (cleanInput.endsWith('K')) {
    const numberPart = cleanInput.slice(0, -1);
    const parsed = parseFloat(numberPart);
    
    if (isNaN(parsed) || parsed < 0) {
      return { value: 0, formatted: input, isValid: false };
    }
    
    const value = parsed * 1000;
    return {
      value,
      formatted: formatPrice(value),
      isValid: true,
    };
  }

  // Check for M (millions) suffix
  if (cleanInput.endsWith('M')) {
    const numberPart = cleanInput.slice(0, -1);
    const parsed = parseFloat(numberPart);
    
    if (isNaN(parsed) || parsed < 0) {
      return { value: 0, formatted: input, isValid: false };
    }
    
    const value = parsed * 1000000;
    return {
      value,
      formatted: formatPrice(value),
      isValid: true,
    };
  }

  // Check for B (billions) suffix
  if (cleanInput.endsWith('B')) {
    const numberPart = cleanInput.slice(0, -1);
    const parsed = parseFloat(numberPart);
    
    if (isNaN(parsed) || parsed < 0) {
      return { value: 0, formatted: input, isValid: false };
    }
    
    const value = parsed * 1000000000;
    return {
      value,
      formatted: formatPrice(value),
      isValid: true,
    };
  }

  // Handle regular numbers (with or without decimals)
  const parsed = parseFloat(cleanInput.replace(/,/g, ''));
  
  if (isNaN(parsed) || parsed < 0) {
    return { value: 0, formatted: input, isValid: false };
  }

  return {
    value: parsed,
    formatted: formatPrice(parsed),
    isValid: true,
  };
}

/**
 * Format a numeric price value for display
 */
export function formatPrice(value: number): string {
  if (value === 0) return '0';
  
  // For very large numbers, use M/B notation
  if (value >= 1000000000) {
    const billions = value / 1000000000;
    return billions % 1 === 0 ? `${billions}B` : `${billions.toFixed(1)}B`;
  }
  
  if (value >= 1000000) {
    const millions = value / 1000000;
    return millions % 1 === 0 ? `${millions}M` : `${millions.toFixed(1)}M`;
  }
  
  if (value >= 1000) {
    const thousands = value / 1000;
    return thousands % 1 === 0 ? `${thousands}K` : `${thousands.toFixed(1)}K`;
  }
  
  return value.toString();
}

/**
 * Format price with currency symbol
 */
export function formatPriceWithCurrency(value: number, currency: string): string {
  const formattedValue = formatPrice(value);
  
  switch (currency) {
    case 'USD':
      return `$${formattedValue}`;
    case 'EUR':
      return `€${formattedValue}`;
    case 'GBP':
      return `£${formattedValue}`;
    case 'KES':
      return `KSh ${formattedValue}`;
    case 'TZS':
      return `TSh ${formattedValue}`;
    case 'UGX':
    default:
      return `UGX ${formattedValue}`;
  }
}

/**
 * Get currency symbol
 */
export function getCurrencySymbol(currency: string): string {
  switch (currency) {
    case 'USD':
      return '$';
    case 'EUR':
      return '€';
    case 'GBP':
      return '£';
    case 'KES':
      return 'KSh';
    case 'TZS':
      return 'TSh';
    case 'UGX':
    default:
      return 'UGX';
  }
}

/**
 * Validate if a price string is valid
 */
export function isValidPrice(input: string): boolean {
  return parsePrice(input).isValid;
}

/**
 * Get price suggestions based on input
 */
export function getPriceSuggestions(input: string): string[] {
  const cleanInput = input.trim().toLowerCase();
  
  if (!cleanInput) {
    return ['1K', '5K', '10K', '50K', '100K'];
  }
  
  // If input is a number, suggest K and M versions
  const parsed = parseFloat(cleanInput);
  if (!isNaN(parsed) && parsed > 0) {
    const suggestions = [];
    
    if (parsed < 1000) {
      suggestions.push(`${parsed}K`);
      if (parsed <= 10) {
        suggestions.push(`${parsed}M`);
      }
    }
    
    // Add some common suggestions
    suggestions.push('1K', '5K', '10K', '50K', '100K', '1M');
    
    return suggestions.slice(0, 5);
  }
  
  return ['1K', '5K', '10K', '50K', '100K'];
}

/**
 * Convert price to base currency value for storage/comparison
 */
export function priceToNumber(priceString: string): number {
  return parsePrice(priceString).value;
}

/**
 * Smart price input formatter - formats as user types
 */
export function formatPriceInput(input: string): string {
  const parsed = parsePrice(input);
  if (parsed.isValid && parsed.value > 0) {
    return parsed.formatted;
  }
  return input;
}
