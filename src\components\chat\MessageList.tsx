// 🚀 MESSAGE LIST COMPONENT
// Displays chat messages with real-time updates

import React, { useRef, useEffect } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { MessageBubble } from './MessageBubble';
import { TypingIndicator } from './TypingIndicator';
import { DateSeparator } from './DateSeparator';
import { RealMessage } from '../../services/realTimeMessagingService';

// Use RealMessage type for consistency
type ChatMessage = RealMessage;

interface TypingUser {
  id: string;
  name: string;
}

interface MessageListProps {
  messages: ChatMessage[];
  currentUserId: string;
  typingUsers: TypingUser[];
  onMessagePress: (_message: ChatMessage) => void;
  onMessageLongPress: (_message: ChatMessage) => void;
  onReply: (_message: ChatMessage) => void;
  onEdit: (_message: ChatMessage) => void;
  onDelete: (_messageId: string) => void;
  onForward: (_message: ChatMessage) => void;
  isSelectionMode: boolean;
  selectedMessages: string[];
  onSelectMessage: (_messageId: string) => void;
  isDarkMode?: boolean;
}

const COLORS = {
  background: '#f8f9fa',
  white: '#ffffff',
};

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  currentUserId,
  typingUsers,
  onMessagePress,
  onMessageLongPress,
  onReply,
  onEdit,
  onDelete,
  onForward,
  isSelectionMode,
  selectedMessages,
  onSelectMessage,
  isDarkMode = false,
}) => {
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    if (messages.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  }, [messages.length]);

  const shouldShowDateSeparator = (currentMessage: ChatMessage, previousMessage: ChatMessage | null) => {
    if (!previousMessage) return true;

    const currentDate = currentMessage.timestamp?.toDate?.() || new Date(currentMessage.timestamp);
    const previousDate = previousMessage.timestamp?.toDate?.() || new Date(previousMessage.timestamp);

    // Show separator if messages are on different days
    return currentDate.toDateString() !== previousDate.toDateString();
  };

  const renderMessage = ({ item, index }: { item: ChatMessage; index: number }) => {
    // Enhanced sender ID comparison with multiple fallbacks
    const normalizedSenderId = String(item.senderId || '').trim();
    const normalizedCurrentUserId = String(currentUserId || '').trim();

    // Try multiple comparison methods to handle different ID formats
    const isOwnMessage = Boolean(
      normalizedSenderId === normalizedCurrentUserId ||
      item.senderId === currentUserId ||
      (normalizedSenderId && normalizedCurrentUserId &&
       normalizedSenderId.toLowerCase() === normalizedCurrentUserId.toLowerCase())
    );

    // Enhanced debug logging for ALL messages
    console.log(`🔍 Message ${index} alignment:`, {
      messageSenderId: item.senderId,
      currentUserId: currentUserId,
      normalizedSenderId,
      normalizedCurrentUserId,
      isOwnMessage: isOwnMessage,
      messageContent: item.content?.substring(0, 20) + '...',
      senderIdType: typeof item.senderId,
      currentUserIdType: typeof currentUserId,
      strictEqual: item.senderId === currentUserId,
      timestamp: item.timestamp,
      senderName: item.senderName
    });
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
    
    const showSenderName = !isOwnMessage && (
      !previousMessage || 
      previousMessage.senderId !== item.senderId
    );
    
    // Always show timestamp, but determine if it should be prominent
    const showTimestamp = true; // Always show timestamps
    const isProminentTimestamp = !nextMessage ||
      nextMessage.senderId !== item.senderId ||
      (nextMessage.timestamp?.toDate?.() || new Date(nextMessage.timestamp)).getTime() -
      (item.timestamp?.toDate?.() || new Date(item.timestamp)).getTime() > 120000; // 2 minutes

    const showDateSeparator = shouldShowDateSeparator(item, previousMessage);

    return (
      <View>
        {showDateSeparator && (
          <DateSeparator
            date={item.timestamp?.toDate?.() || new Date(item.timestamp)}
            isDarkMode={isDarkMode}
          />
        )}
        <MessageBubble
          message={item}
          isOwnMessage={isOwnMessage}
          showSenderName={showSenderName}
          showTimestamp={showTimestamp}
          isProminentTimestamp={isProminentTimestamp}
          onPress={() => onMessagePress(item)}
          onLongPress={() => onMessageLongPress(item)}
          onReply={() => onReply(item)}
          onEdit={() => onEdit(item)}
          onDelete={() => onDelete(item.id)}
          onForward={() => onForward(item)}
          isSelectionMode={isSelectionMode}
          isSelected={selectedMessages.includes(item.id)}
          onSelect={() => onSelectMessage(item.id)}
          isDarkMode={isDarkMode}
        />
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;
    
    return (
      <View style={styles.typingContainer}>
        <TypingIndicator users={typingUsers} />
      </View>
    );
  };

  const getItemLayout = (_: any, index: number) => ({
    length: 80, // Estimated height
    offset: 80 * index,
    index,
  });

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        getItemLayout={getItemLayout}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={20}
        ListFooterComponent={renderTypingIndicator}
        onScrollToIndexFailed={(info) => {
          console.warn('Scroll to index failed:', info);
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Let the beautiful wallpaper show through
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    minHeight: SCREEN_HEIGHT * 0.6,
  },
  typingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
});
