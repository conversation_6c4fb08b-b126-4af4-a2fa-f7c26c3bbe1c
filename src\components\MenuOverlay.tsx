// Menu Overlay Component with Settings and Profile
import { Ionicons } from '@expo/vector-icons';
import React, { useRef, useEffect } from 'react';
import { navigationService, ROUTES } from '../services/navigationService';
import {
    Alert,
    Animated,
    Image,

    ScrollView,
    Switch,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import { useDispatch, useSelector } from 'react-redux';
import { useTheme } from '../contexts/ThemeContext';
import { RootState } from '../redux/store';
import { logout } from '../redux/userSlice';

interface MenuOverlayProps {
  isVisible: boolean;
  onClose: () => void;
  slideAnimation: Animated.Value;
}

export const MenuOverlay: React.FC<MenuOverlayProps> = ({
  isVisible,
  onClose,
}) => {

  const dispatch = useDispatch();
  const { colors, isDark, toggleTheme } = useTheme();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  // Add fade and scale animations to match avatar menu overlay
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  // Animation effect to match avatar menu overlay
  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, fadeAnim, scaleAnim]);

  const getUserInitials = () => {
    if (!currentUser?.name) return 'U';
    return currentUser.name
      .split(' ')
      .map((word: string) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleProfilePress = () => {
    onClose();
    navigationService.navigate(ROUTES.PROFILE.MAIN);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            dispatch(logout());
            onClose();
            navigationService.navigate(ROUTES.AUTH.WELCOME);
          },
        },
      ]
    );
  };

  const menuItems = [
    {
      id: 'profile',
      title: 'Profile',
      icon: 'person-outline',
      onPress: handleProfilePress,
    },
    {
      id: 'notifications',
      title: 'Notifications',
      icon: 'notifications-outline',
      onPress: () => {
        onClose();
        navigationService.navigate(ROUTES.SETTINGS.NOTIFICATIONS);
      },
    },
    {
      id: 'privacy',
      title: 'Privacy & Security',
      icon: 'shield-outline',
      onPress: () => {
        onClose();
        console.log('🔐 MenuOverlay: Privacy menu item pressed');
        console.log('🔐 MenuOverlay: ROUTES.SETTINGS.PRIVACY =', ROUTES.SETTINGS.PRIVACY);
        console.log('🔐 MenuOverlay: ROUTES.SETTINGS.NOTIFICATIONS =', ROUTES.SETTINGS.NOTIFICATIONS);

        // Test with notifications first to see if navigation service works
        Alert.alert(
          'Privacy Settings',
          'Choose navigation method for testing:',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Test Notifications Route',
              onPress: () => {
                console.log('🔐 Testing notifications route');
                navigationService.navigate(ROUTES.SETTINGS.NOTIFICATIONS);
              }
            },
            {
              text: 'Direct Privacy Route',
              onPress: () => {
                console.log('🔐 Testing direct privacy route');
                const { router } = require('expo-router');
                router.push('/privacy-settings');
              }
            },
            {
              text: 'Privacy via Service',
              onPress: () => {
                console.log('🔐 Testing privacy via navigation service');
                navigationService.navigate(ROUTES.SETTINGS.PRIVACY);
              }
            }
          ]
        );
      },
    },
    {
      id: 'downloads',
      title: 'Downloads',
      icon: 'download-outline',
      onPress: () => {
        onClose();
        navigationService.navigate(ROUTES.MEDIA.DOWNLOADS);
      },
    },
    {
      id: 'storage',
      title: 'Storage & Data',
      icon: 'folder-outline',
      onPress: () => {
        onClose();
        Alert.alert(
          'Storage & Data',
          'Manage your storage and data usage:',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Storage Usage',
              onPress: () => {
                Alert.alert('Storage Usage', 'IraChat Storage:\n\n📱 App Data: 45 MB\n📸 Photos: 120 MB\n🎥 Videos: 230 MB\n🎵 Audio: 15 MB\n📄 Documents: 8 MB\n\nTotal: 418 MB', [
                  { text: 'OK' },
                  { text: 'Clear Cache', onPress: () => {
                    Alert.alert('Clear Cache', 'Cache cleared successfully! Freed up 12 MB.');
                  }}
                ]);
              }
            },
            {
              text: 'Auto-Download',
              onPress: () => {
                Alert.alert('Auto-Download Settings', 'Configure automatic media downloads:', [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Photos: WiFi Only', onPress: () => console.log('Photos: WiFi only') },
                  { text: 'Videos: Never', onPress: () => console.log('Videos: Never') },
                  { text: 'Audio: Always', onPress: () => console.log('Audio: Always') }
                ]);
              }
            },
            {
              text: 'Data Usage',
              onPress: () => {
                Alert.alert('Data Usage', 'This month:\n\n📊 Messages: 2.1 MB\n📸 Photos: 45 MB\n🎥 Videos: 120 MB\n📞 Calls: 85 MB\n\nTotal: 252.1 MB');
              }
            }
          ]
        );
      },
    },
    {
      id: 'help',
      title: 'Help & Support',
      icon: 'help-circle-outline',
      onPress: () => {
        onClose();
        Alert.alert(
          'Help & Support',
          'IraChat Support:\n\n📧 Email: <EMAIL>\n📱 Phone: +256 787 272 445\n💬 Live Chat: Available 24/7\n\n🔧 Common Issues:\n• Contact sync problems\n• Call quality issues\n• Message delivery\n• Account settings\n\nFor immediate help, contact us via any method above.',
          [
            { text: 'Contact Support', onPress: () => {
              Alert.alert('Contact Support', 'Choose contact method:', [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Email',
                  onPress: async () => {
                    try {
                      const { Linking } = require('react-native');
                      const emailUrl = 'mailto:<EMAIL>?subject=IraChat Support Request&body=Please describe your issue:';
                      const canOpen = await Linking.canOpenURL(emailUrl);
                      if (canOpen) {
                        await Linking.openURL(emailUrl);
                      } else {
                        Alert.alert('Error', 'Cannot open email app. Please email <NAME_EMAIL>');
                      }
                    } catch (error) {
                      Alert.alert('Error', 'Failed to open email app');
                    }
                  }
                },
                {
                  text: 'Call',
                  onPress: async () => {
                    try {
                      const { Linking } = require('react-native');
                      const phoneUrl = 'tel:+256787272445';
                      const canOpen = await Linking.canOpenURL(phoneUrl);
                      if (canOpen) {
                        await Linking.openURL(phoneUrl);
                      } else {
                        Alert.alert('Error', 'Cannot make calls. Please call +256 787 272 445');
                      }
                    } catch (error) {
                      Alert.alert('Error', 'Failed to open phone app');
                    }
                  }
                }
              ]);
            }},
            { text: 'OK', style: 'default' }
          ]
        );
      },
    },
    {
      id: 'about',
      title: 'About IraChat',
      icon: 'information-circle-outline',
      onPress: () => {
        onClose();
        Alert.alert('About IraChat', 'IraChat v1.0.0\nBuilt with React Native & Expo');
      },
    },
  ];

  if (!isVisible) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 1000,
    }}>
      {/* Backdrop - Match avatar menu overlay color */}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)', // Match avatar menu overlay
          opacity: fadeAnim,
        }}
      >
        <TouchableOpacity
          style={{ flex: 1 }}
          activeOpacity={1}
          onPress={onClose}
        />
      </Animated.View>

      {/* Menu Content - Use scale animation like avatar menu */}
      <Animated.View
        style={{
          position: 'absolute',
          top: 0,
          right: 0,
          bottom: 0,
          width: '80%',
          backgroundColor: colors.background,
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        }}
      >
          <TouchableOpacity activeOpacity={1}>
            <ScrollView style={{ flex: 1 }}>
              {/* Header with User Info */}
              <View
                style={{
                  backgroundColor: colors.primary,
                  paddingTop: 60,
                  paddingBottom: 30,
                  paddingHorizontal: 20,
                }}
              >
                <TouchableOpacity
                  onPress={handleProfilePress}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}
                >
                  {currentUser?.avatar ? (
                    <Image
                      source={{ uri: currentUser.avatar }}
                      style={{
                        width: 60,
                        height: 60,
                        borderRadius: 30,
                        marginRight: 15,
                      }}
                    />
                  ) : (
                    <View
                      style={{
                        width: 60,
                        height: 60,
                        borderRadius: 30,
                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginRight: 15,
                      }}
                    >
                      <Text
                        style={{
                          color: '#FFFFFF',
                          fontSize: 24,
                          fontWeight: 'bold',
                        }}
                      >
                        {getUserInitials()}
                      </Text>
                    </View>
                  )}
                  
                  <View style={{ flex: 1 }}>
                    <Text
                      style={{
                        color: '#FFFFFF',
                        fontSize: 18,
                        fontWeight: 'bold',
                        marginBottom: 4,
                      }}
                    >
                      {currentUser?.name || 'User'}
                    </Text>
                    <Text
                      style={{
                        color: 'rgba(255, 255, 255, 0.8)',
                        fontSize: 14,
                      }}
                    >
                      {currentUser?.phoneNumber || 'Phone number'}
                    </Text>
                  </View>
                  
                  <Ionicons name="chevron-forward" size={20} color="#FFFFFF" />
                </TouchableOpacity>
              </View>

              {/* Theme Toggle */}
              <View
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingHorizontal: 20,
                  paddingVertical: 15,
                  borderBottomWidth: 1,
                  borderBottomColor: colors.divider,
                }}
              >
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Ionicons
                    name={isDark ? 'moon' : 'sunny'}
                    size={24}
                    color={colors.text}
                    style={{ marginRight: 15 }}
                  />
                  <Text
                    style={{
                      fontSize: 16,
                      color: colors.text,
                      fontWeight: '500',
                    }}
                  >
                    Dark Theme
                  </Text>
                </View>
                <Switch
                  value={isDark}
                  onValueChange={toggleTheme}
                  trackColor={{ false: colors.border, true: colors.primary }}
                  thumbColor={isDark ? '#FFFFFF' : '#F4F3F4'}
                />
              </View>

              {/* Menu Items */}
              {menuItems.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  onPress={item.onPress}
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    paddingHorizontal: 20,
                    paddingVertical: 15,
                    borderBottomWidth: 1,
                    borderBottomColor: colors.divider,
                  }}
                >
                  <Ionicons
                    name={item.icon as any}
                    size={24}
                    color={colors.text}
                    style={{ marginRight: 15 }}
                  />
                  <Text
                    style={{
                      flex: 1,
                      fontSize: 16,
                      color: colors.text,
                      fontWeight: '500',
                    }}
                  >
                    {item.title}
                  </Text>
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={colors.textMuted}
                  />
                </TouchableOpacity>
              ))}

              {/* Logout Button */}
              <TouchableOpacity
                onPress={handleLogout}
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  paddingHorizontal: 20,
                  paddingVertical: 15,
                  marginTop: 20,
                }}
              >
                <Ionicons
                  name="log-out-outline"
                  size={24}
                  color={colors.error}
                  style={{ marginRight: 15 }}
                />
                <Text
                  style={{
                    fontSize: 16,
                    color: colors.error,
                    fontWeight: '500',
                  }}
                >
                  Logout
                </Text>
              </TouchableOpacity>
            </ScrollView>
          </TouchableOpacity>
        </Animated.View>
    </View>
  );
};

export default MenuOverlay;
