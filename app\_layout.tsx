import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect, useState } from "react";
import { View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { AuthInitializer } from "../src/components/AuthInitializer";
import { AuthNavigator } from "../src/components/AuthNavigator";
import { AuthNavigator as ConsolidatedAuthNavigator } from "../src/navigation/AuthNavigator";
import { ThemeProvider } from "../src/contexts/ThemeContext";
// import { PrivacyLockProvider } from "../src/contexts/PrivacyLockContext";
// import AppLockWrapper from "../src/components/privacy/AppLockWrapper";

import ErrorBoundary from "../src/components/ErrorBoundary";
import { persistor, store } from "../src/redux/store";
import { waitForAuth } from "../src/services/firebaseSimple";
// backgroundCallService removed - push notifications disabled
import { realTimeSignalingService } from "../src/services/realTimeSignaling";
import { soundService } from "../src/services/soundService";
import { quickNavigationCheck } from "../src/utils/navigationTest";
import { runNavigationValidation } from "../src/utils/navigationValidator";
import { runNavigationFixVerification } from "../src/utils/navigationFixVerification";
import { testPrivacyLockSystem } from "../src/utils/privacyLockTest";


// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

// IraChat App starting

export default function RootLayout() {
  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    async function prepare() {
      try {
        // Wait for Firebase Auth to initialize
        await waitForAuth();

        // Initialize network state manager first (required by other services)
        const { networkStateManager } = await import('../src/services/networkStateManager');
        await networkStateManager.initialize();

        // Check and fix database health before initializing services
        try {
          console.log('🔍 Checking database health...');
          const { databaseResetService } = await import('../src/utils/databaseReset');
          const diagnostics = await databaseResetService.getDatabaseDiagnostics();

          console.log('📊 Database diagnostics:', diagnostics);

          if (!diagnostics.canCreateDatabase) {
            console.log('🔧 Database issues detected, attempting reset...');
            await databaseResetService.resetAllDatabases();
            console.log('✅ Database reset completed');
          }
        } catch (dbHealthError) {
          console.warn('⚠️ Database health check failed:', dbHealthError);
          // Continue with app initialization - database will be handled later
        }

        // Initialize IraChat Offline Engine for offline functionality
        const { iraChatOfflineEngine } = await import('../src/services/iraChatOfflineEngine');
        await iraChatOfflineEngine.initialize({
          enableBackgroundSync: true,
          debugMode: __DEV__,
        });

        // Initialize sound service
        await soundService.initialize();

        // Initialize message preloading and last message sync
        const { messagePreloadService } = await import('../src/services/messagePreloadService');
        const { lastMessageSyncService } = await import('../src/services/lastMessageSyncService');

        // Start preloading messages in background (don't await to avoid blocking app startup)
        const currentUser = store.getState().user.currentUser;
        if (currentUser?.id) {
          console.log('🚀 Starting background message preloading...');
          messagePreloadService.preloadAllChatMessages(currentUser.id).catch(error => {
            console.error('❌ Background message preloading failed:', error);
          });

          // Sync last messages in background
          lastMessageSyncService.syncAllChatLastMessages(currentUser.id).catch(error => {
            console.error('❌ Background last message sync failed:', error);
          });
        }

        // Background call service removed - push notifications disabled

        // Initialize real-time signaling

        // Validate and cleanup wallpapers
        try {
          const { wallpaperService } = await import('../src/services/wallpaperService');
          await wallpaperService.validateAndCleanupWallpapers();
        } catch (error) {
          console.warn('⚠️ Wallpaper validation failed:', error);
        }

        // Run navigation health check and validation in development
        if (__DEV__) {
          quickNavigationCheck();
          runNavigationValidation();
          runNavigationFixVerification();

          // Test privacy lock system
          testPrivacyLockSystem();
        }
      } catch (error) {
        console.warn("❌ Error during initialization:", error);
      } finally {
        setAppIsReady(true);
      }
    }

    prepare();

    // Cleanup on unmount
    return () => {
      soundService.cleanup();
      // backgroundCallService.cleanup(); // removed - push notifications disabled
      realTimeSignalingService.cleanup();
    };
  }, []);

  useEffect(() => {
    if (appIsReady) {
      SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady) {
    return null;
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <StatusBar style="light" translucent />
        {/* Status bar background view for edge-to-edge */}
        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: 50, // Covers status bar area
            backgroundColor: '#000000',
            zIndex: -1,
          }}
        />
        {/* Removed absolute positioned status bar background that was hiding the logo */}
        <GestureHandlerRootView
          style={{ flex: 1 }}
          accessible={true}
          accessibilityLabel="IraChat application root"
        >
          <Provider store={store}>
            <ThemeProvider>
              <AuthInitializer>
                <PersistGate
                loading={null}
                persistor={persistor}
              >
                {/* Consolidated Auth Navigator - handles all auth-based navigation */}
                <ConsolidatedAuthNavigator />
                <AuthNavigator>
                <Stack
                screenOptions={{
                  headerShown: false,
                  animation: "none",
                }}
              >
                {/* Auth screens */}
                <Stack.Screen name="(auth)" options={{ headerShown: false }} />

                {/* Main tab navigation */}
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />

                {/* Real Call Screens */}
                <Stack.Screen
                  name="real-call"
                  options={{
                    headerShown: false,
                    presentation: "fullScreenModal",
                    animation: "fade",
                  }}
                />
                <Stack.Screen
                  name="incoming-call-real"
                  options={{
                    headerShown: false,
                    presentation: "fullScreenModal",
                    animation: "fade",
                  }}
                />

                {/* Call modal screens */}
                <Stack.Screen
                  name="call"
                  options={{
                    headerShown: false,
                    presentation: "fullScreenModal",
                    animation: "fade",
                  }}
                />
                <Stack.Screen
                  name="incoming-call"
                  options={{
                    headerShown: false,
                    presentation: "fullScreenModal",
                    animation: "fade",
                  }}
                />
                <Stack.Screen
                  name="video-call"
                  options={{
                    headerShown: false,
                    presentation: "fullScreenModal",
                    animation: "fade",
                  }}
                />
                <Stack.Screen
                  name="video-call-safe"
                  options={{
                    headerShown: false,
                    presentation: "fullScreenModal",
                    animation: "fade",
                  }}
                />
                <Stack.Screen
                  name="voice-call"
                  options={{
                    headerShown: false,
                    presentation: "fullScreenModal",
                    animation: "fade",
                  }}
                />

                {/* Settings and other screens */}
                <Stack.Screen name="account-settings" options={{ headerShown: false }} />
                <Stack.Screen name="theme-settings" options={{ headerShown: false }} />
                <Stack.Screen name="privacy-settings" options={{ headerShown: false }} />
                <Stack.Screen name="notifications-settings" options={{ headerShown: false }} />
                <Stack.Screen name="help" options={{ headerShown: false }} />
                <Stack.Screen name="about" options={{ headerShown: false }} />
                <Stack.Screen name="export-data" options={{ headerShown: false }} />

                {/* Chat and contact screens */}
                <Stack.Screen name="contacts" options={{ headerShown: false }} />
                <Stack.Screen name="fast-contacts" options={{ headerShown: false }} />
                <Stack.Screen name="new-chat" options={{ headerShown: false }} />
                <Stack.Screen name="individual-chat" options={{ headerShown: false }} />
                <Stack.Screen name="group-chat" options={{ headerShown: false }} />
                <Stack.Screen name="enhanced-group-chat" options={{ headerShown: false }} />
                <Stack.Screen name="chat-management" options={{ headerShown: false }} />
                <Stack.Screen name="create-group" options={{ headerShown: false }} />
                <Stack.Screen name="group-settings" options={{ headerShown: false }} />
                <Stack.Screen name="select-group-members" options={{ headerShown: false }} />
                <Stack.Screen name="edit-profile" options={{ headerShown: false }} />

                {/* Media screens */}
                <Stack.Screen name="camera" options={{ headerShown: false }} />
                <Stack.Screen name="media-gallery" options={{ headerShown: false }} />
                <Stack.Screen name="downloaded-media" options={{ headerShown: false }} />



                {/* Social and Updates screens */}
                <Stack.Screen name="social-feed" options={{ headerShown: false }} />
                <Stack.Screen name="create-update" options={{ headerShown: false }} />

                {/* Search screens */}
                <Stack.Screen name="global-search" options={{ headerShown: false }} />

                {/* Organization screens */}
                <Stack.Screen name="archives" options={{ headerShown: false }} />
                <Stack.Screen name="pinned-messages" options={{ headerShown: false }} />
                <Stack.Screen name="invite-friends" options={{ headerShown: false }} />

                {/* Help screens */}
                <Stack.Screen name="help-support" options={{ headerShown: false }} />

                {/* Root level routes are auto-registered by Expo Router */}

                {/* Dynamic route screens */}
                <Stack.Screen name="chat" options={{ headerShown: false }} />
                <Stack.Screen name="update" options={{ headerShown: false }} />
                </Stack>
                </AuthNavigator>
                </PersistGate>
              </AuthInitializer>
            </ThemeProvider>
          </Provider>
        </GestureHandlerRootView>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}
