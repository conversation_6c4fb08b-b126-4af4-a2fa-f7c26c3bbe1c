import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Audio } from 'expo-av';
import * as DocumentPicker from 'expo-document-picker';
import { AudioCaption } from '../../types/Update';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface AudioCaptionRecorderProps {
  visible: boolean;
  onClose: () => void;
  onRecordingComplete: (audioCaption: AudioCaption) => void;
  maxDuration?: number; // in seconds, default 30
  type?: 'voice' | 'music';
}

export const AudioCaptionRecorder: React.FC<AudioCaptionRecorderProps> = ({
  visible,
  onClose,
  onRecordingComplete,
  maxDuration = 30,
  type = 'voice',
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showModeSelector, setShowModeSelector] = useState(true);
  const [selectedMode, setSelectedMode] = useState<'voice' | 'music' | null>(null);

  const recording = useRef<Audio.Recording | null>(null);
  const recordingTimer = useRef<NodeJS.Timeout | null>(null);
  const slideAnim = useRef(new Animated.Value(SCREEN_WIDTH)).current;
  const recordButtonScale = useRef(new Animated.Value(1)).current;
  const waveformAnim = useRef(new Animated.Value(0)).current;

  // Request audio permissions
  useEffect(() => {
    if (visible) {
      requestPermissions();
      // Slide in animation
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      // Slide out animation
      Animated.spring(slideAnim, {
        toValue: SCREEN_WIDTH,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  }, [visible]);

  const requestPermissions = async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      setHasPermission(status === 'granted');
      
      if (status === 'granted') {
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true,
          playsInSilentModeIOS: true,
        });
      }
    } catch (error) {
      console.error('Error requesting audio permissions:', error);
      setHasPermission(false);
    }
  };

  const startRecording = async () => {
    if (!hasPermission) {
      Alert.alert('Permission Required', 'Please grant microphone permission to record audio captions.');
      return;
    }

    try {
      setIsRecording(true);
      setRecordingDuration(0);

      // Animate record button
      Animated.spring(recordButtonScale, {
        toValue: 1.2,
        useNativeDriver: true,
        tension: 100,
        friction: 5,
      }).start();

      // Start waveform animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(waveformAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(waveformAnim, {
            toValue: 0,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Create and start recording
      const newRecording = new Audio.Recording();
      await newRecording.prepareToRecordAsync({
        android: {
          extension: '.m4a',
          outputFormat: 2, // MPEG_4
          audioEncoder: 3, // AAC
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: 'kAudioFormatMPEG4AAC',
          audioQuality: 0x60, // High quality
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
        web: {
          mimeType: 'audio/webm;codecs=opus',
          bitsPerSecond: 128000,
        },
      });

      await newRecording.startAsync();
      recording.current = newRecording;

      // Start timer
      recordingTimer.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 1;
          if (newDuration >= maxDuration) {
            stopRecording();
          }
          return newDuration;
        });
      }, 1000);

    } catch (error) {
      console.error('Error starting recording:', error);
      Alert.alert('Error', 'Failed to start recording. Please try again.');
      setIsRecording(false);
    }
  };

  const stopRecording = async () => {
    if (!recording.current || !isRecording) return;

    try {
      setIsProcessing(true);

      // Stop animations
      waveformAnim.stopAnimation();
      Animated.spring(recordButtonScale, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 5,
      }).start();

      // Clear timer
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }

      // Stop recording
      await recording.current.stopAndUnloadAsync();
      const uri = recording.current.getURI();
      
      if (uri) {
        // Create audio caption object
        const audioCaption: AudioCaption = {
          id: `audio_${Date.now()}`,
          url: uri,
          duration: recordingDuration,
          type,
          volume: 1.0,
        };

        onRecordingComplete(audioCaption);
      }

      // Reset state
      recording.current = null;
      setIsRecording(false);
      setRecordingDuration(0);
      setIsProcessing(false);
      onClose();

    } catch (error) {
      console.error('Error stopping recording:', error);
      Alert.alert('Error', 'Failed to save recording. Please try again.');
      setIsRecording(false);
      setIsProcessing(false);
    }
  };

  const cancelRecording = async () => {
    if (recording.current) {
      try {
        await recording.current.stopAndUnloadAsync();
      } catch (error) {
        console.error('Error canceling recording:', error);
      }
      recording.current = null;
    }

    if (recordingTimer.current) {
      clearInterval(recordingTimer.current);
      recordingTimer.current = null;
    }

    waveformAnim.stopAnimation();
    setIsRecording(false);
    setRecordingDuration(0);
    setIsProcessing(false);
    onClose();
  };

  const selectMusicFromLibrary = async () => {
    try {
      setIsProcessing(true);

      const result = await DocumentPicker.getDocumentAsync({
        type: 'audio/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        // Create audio caption object for music
        const audioCaption: AudioCaption = {
          id: `music_${Date.now()}`,
          url: asset.uri,
          duration: 30, // Default duration, could be detected from file
          type: 'music',
          volume: 1.0,
        };

        onRecordingComplete(audioCaption);
        onClose();
      }
    } catch (error) {
      console.error('Error selecting music:', error);
      Alert.alert('Error', 'Failed to select music. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!visible) return null;

  return (
    <View style={styles.overlay}>
      <Animated.View 
        style={[
          styles.container,
          { transform: [{ translateX: slideAnim }] }
        ]}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={cancelRecording}
            disabled={isProcessing}
          >
            <Ionicons name="close" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.title}>
            {type === 'voice' ? 'Voice Caption' : 'Audio Caption'}
          </Text>
          <View style={styles.placeholder} />
        </View>

        {/* Mode Selection */}
        {showModeSelector && !selectedMode && (
          <View style={styles.modeSelector}>
            <Text style={styles.modeSelectorTitle}>Choose Audio Type</Text>

            <TouchableOpacity
              style={styles.modeButton}
              onPress={() => {
                setSelectedMode('voice');
                setShowModeSelector(false);
              }}
            >
              <Ionicons name="mic" size={32} color="#667eea" />
              <Text style={styles.modeButtonText}>Record Voice</Text>
              <Text style={styles.modeButtonSubtext}>Record your voice as caption</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.modeButton}
              onPress={() => {
                setSelectedMode('music');
                selectMusicFromLibrary();
              }}
            >
              <Ionicons name="musical-notes" size={32} color="#667eea" />
              <Text style={styles.modeButtonText}>Choose Music</Text>
              <Text style={styles.modeButtonSubtext}>Select from your music library</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Recording Area */}
        {!showModeSelector && selectedMode === 'voice' && (
          <View style={styles.recordingArea}>
          {/* Duration Display */}
          <Text style={styles.duration}>
            {formatDuration(recordingDuration)} / {formatDuration(maxDuration)}
          </Text>

          {/* Waveform Visualization */}
          {isRecording && (
            <View style={styles.waveformContainer}>
              {[...Array(20)].map((_, index) => (
                <Animated.View
                  key={index}
                  style={[
                    styles.waveformBar,
                    {
                      transform: [{
                        scaleY: waveformAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.3, Math.random() * 2 + 0.5],
                        })
                      }]
                    }
                  ]}
                />
              ))}
            </View>
          )}

          {/* Record Button */}
          <TouchableOpacity
            style={styles.recordButtonContainer}
            onPress={isRecording ? stopRecording : startRecording}
            disabled={!hasPermission || isProcessing}
          >
            <Animated.View
              style={[
                styles.recordButton,
                { transform: [{ scale: recordButtonScale }] },
                isRecording && styles.recordButtonActive
              ]}
            >
              <Ionicons
                name={isRecording ? "stop" : "mic"}
                size={32}
                color="#FFFFFF"
              />
            </Animated.View>
          </TouchableOpacity>

          {/* Instructions */}
          <Text style={styles.instructions}>
            {!hasPermission
              ? 'Microphone permission required'
              : isRecording
              ? 'Tap to stop recording'
              : `Tap to start recording ${type === 'voice' ? 'voice' : 'audio'} caption`
            }
          </Text>
        </View>
        )}
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    zIndex: 1000,
  },
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  placeholder: {
    width: 40,
  },
  recordingArea: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  duration: {
    fontSize: 24,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 40,
  },
  waveformContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    marginBottom: 40,
  },
  waveformBar: {
    width: 3,
    height: 20,
    backgroundColor: '#667eea',
    marginHorizontal: 1,
    borderRadius: 1.5,
  },
  recordButtonContainer: {
    marginBottom: 40,
  },
  recordButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#667eea',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  recordButtonActive: {
    backgroundColor: '#ff4444',
  },
  instructions: {
    fontSize: 16,
    color: '#999',
    textAlign: 'center',
    lineHeight: 22,
  },
  modeSelector: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  modeSelectorTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 40,
    textAlign: 'center',
  },
  modeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    marginVertical: 12,
    width: '100%',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  modeButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 12,
  },
  modeButtonSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
    textAlign: 'center',
  },
});
