import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  SafeAreaView,
  TouchableOpacity,
  Image,
  Dimensions,
  FlatList,
  StatusBar,
} from 'react-native';
import { VideoView, useVideoPlayer } from 'expo-video';
import { Ionicons } from '@expo/vector-icons';
import {
  Gesture,
  GestureDetector,
  GestureHandlerRootView,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';

const COLORS = {
  primary: '#1DA1F2',
  background: '#000000',
  text: '#FFFFFF',
  textSecondary: '#657786',
  overlay: 'rgba(0, 0, 0, 0.8)',
};

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MediaItem {
  id: string;
  uri: string;
  type: 'photo' | 'video';
  duration?: number;
}

interface EnhancedMediaViewerProps {
  visible: boolean;
  mediaItems: MediaItem[];
  initialIndex: number;
  onClose: () => void;
}

export const EnhancedMediaViewer: React.FC<EnhancedMediaViewerProps> = ({
  visible,
  mediaItems,
  initialIndex,
  onClose,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [showControls, setShowControls] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const videoPlayer = useVideoPlayer('', (player) => {
    player.loop = false;
  });

  // Update video source when current index changes
  useEffect(() => {
    const currentItem = mediaItems[currentIndex];
    if (currentItem?.type === 'video') {
      try {
        videoPlayer.replaceAsync(currentItem.uri);
        setIsPlaying(false);
      } catch (error) {
        console.warn('VideoPlayer replaceAsync error:', error);
      }
    } else {
      // Pause video when switching to non-video item
      try {
        videoPlayer.pause();
        setIsPlaying(false);
      } catch (error) {
        console.warn('VideoPlayer pause error:', error);
      }
    }
  }, [currentIndex, mediaItems, videoPlayer]);

  // Listen to video player status changes
  useEffect(() => {
    const playingSubscription = videoPlayer.addListener('playingChange', (event) => {
      setIsPlaying(event.isPlaying);
    });

    const statusSubscription = videoPlayer.addListener('statusChange', (event) => {
      // Allow replaying when video ends
      if (event.status === 'readyToPlay') {
        // Video is ready to play again after ending
      }
    });

    return () => {
      playingSubscription?.remove();
      statusSubscription?.remove();
    };
  }, [videoPlayer]);

  // Pause video when modal is closed or component unmounts
  useEffect(() => {
    if (!visible) {
      try {
        // Check if player is still valid before pausing
        if (videoPlayer && typeof videoPlayer.pause === 'function') {
          videoPlayer.pause();
          setIsPlaying(false);
        }
      } catch (error) {
        // Silently handle cleanup errors - player might already be released
      }
    }
  }, [visible, videoPlayer]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      try {
        // Check if player is still valid before pausing
        if (videoPlayer && typeof videoPlayer.pause === 'function') {
          videoPlayer.pause();
        }
      } catch (error) {
        // Silently handle cleanup errors - player might already be released
      }
    };
  }, [videoPlayer]);

  // Handle initial scroll to the correct index safely
  useEffect(() => {
    if (visible && mediaItems.length > 0 && initialIndex > 0) {
      // Delay the scroll to ensure FlatList is fully rendered
      const timer = setTimeout(() => {
        try {
          flatListRef.current?.scrollToIndex({
            index: initialIndex,
            animated: false, // No animation for initial positioning
            viewPosition: 0.5
          });
        } catch (scrollError) {
          console.warn('⚠️ Initial scroll failed, using offset fallback:', scrollError);
          // Fallback to scrollToOffset
          flatListRef.current?.scrollToOffset({
            offset: initialIndex * screenWidth,
            animated: false
          });
        }
      }, 100); // Small delay to ensure FlatList is ready

      return () => clearTimeout(timer);
    }
  }, [visible, initialIndex, mediaItems.length]);

  // Animation values for zoom and pan
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const savedScale = useSharedValue(1);

  // Pinch gesture for zoom
  const pinchGesture = Gesture.Pinch()
    .onStart(() => {
      savedScale.value = scale.value;
    })
    .onUpdate((event) => {
      scale.value = savedScale.value * event.scale;
    })
    .onEnd(() => {
      if (scale.value < 1) {
        scale.value = withSpring(1);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
      } else if (scale.value > 3) {
        scale.value = withSpring(3);
      }
      savedScale.value = scale.value;
    });

  // Pan gesture for moving when zoomed
  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      if (scale.value > 1) {
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      }
    })
    .onEnd(() => {
      // Add bounds checking here if needed
    });

  // Combined gesture
  const combinedGesture = Gesture.Simultaneous(pinchGesture, panGesture);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  const resetZoom = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    savedScale.value = 1;
  };

  const toggleControls = () => {
    setShowControls(!showControls);
  };



  const handleVideoPlayback = () => {
    if (mediaItems[currentIndex]?.type === 'video') {
      try {
        // Check if player is still valid before using
        if (videoPlayer && typeof videoPlayer.pause === 'function') {
          if (isPlaying) {
            videoPlayer.pause();
          } else {
            // Check if video has ended and replay from beginning
            if (videoPlayer.currentTime >= videoPlayer.duration && videoPlayer.duration > 0) {
              videoPlayer.currentTime = 0;
            }
            videoPlayer.play();
          }
          setIsPlaying(!isPlaying);
        }
      } catch (error) {
        // Silently handle playback errors - player might be released
      }
    }
  };

  const handleClose = () => {
    // Pause video before closing
    try {
      // Check if player is still valid before pausing
      if (videoPlayer && typeof videoPlayer.pause === 'function') {
        videoPlayer.pause();
        setIsPlaying(false);
      }
    } catch (error) {
      // Silently handle close errors - player might be released
    }
    onClose();
  };

  const renderMediaItem = ({ item, index }: { item: MediaItem; index: number }) => {
    const isCurrentItem = index === currentIndex;

    if (item.type === 'video') {
      return (
        <View style={styles.mediaContainer}>
          {isCurrentItem && (
            <VideoView
              player={videoPlayer}
              style={styles.media}
              contentFit="contain"
              allowsFullscreen={false}
              allowsPictureInPicture={false}
              showsTimecodes={false}
              requiresLinearPlayback={false}
              nativeControls={false}
            />
          )}

          {/* Invisible overlay for tap detection */}
          <TouchableOpacity
            style={styles.videoOverlay}
            onPress={toggleControls}
            activeOpacity={1}
          />

          {/* Video Controls */}
          {isCurrentItem && showControls && (
            <TouchableOpacity
              style={styles.playButton}
              onPress={handleVideoPlayback}
            >
              <Ionicons
                name={isPlaying ? 'pause' : 'play'}
                size={60}
                color={COLORS.text}
              />
            </TouchableOpacity>
          )}
        </View>
      );
    }

    return (
      <View style={styles.mediaContainer}>
        <GestureHandlerRootView style={{ flex: 1 }}>
          <GestureDetector gesture={combinedGesture}>
            <Animated.View style={animatedStyle}>
              <TouchableOpacity onPress={toggleControls} activeOpacity={1}>
                <Image
                  source={{ uri: item.uri }}
                  style={styles.media}
                  resizeMode="contain"
                />
              </TouchableOpacity>
            </Animated.View>
          </GestureDetector>
        </GestureHandlerRootView>
      </View>
    );
  };

  const onViewableItemsChanged = useCallback(({ viewableItems }: any) => {
    console.log('📱 Viewable items changed:', viewableItems.map((item: any) => item.index));
    if (viewableItems.length > 0) {
      const newIndex = viewableItems[0].index;
      console.log('📱 New index:', newIndex, 'Current index:', currentIndex);
      if (newIndex !== currentIndex && newIndex !== null && newIndex !== undefined) {
        // Pause current video before switching
        try {
          videoPlayer.pause();
        } catch (error) {
          console.warn('VideoPlayer pause error on index change:', error);
        }
        setCurrentIndex(newIndex);
        resetZoom();
        setIsPlaying(false);
      }
    }
  }, [currentIndex, videoPlayer, resetZoom]);

  const onMomentumScrollEnd = (event: any) => {
    const contentOffset = event.nativeEvent.contentOffset.x;
    const newIndex = Math.round(contentOffset / screenWidth);
    console.log('📱 Momentum scroll end - calculated index:', newIndex, 'current:', currentIndex);

    if (newIndex !== currentIndex && newIndex >= 0 && newIndex < mediaItems.length) {
      try {
        videoPlayer.pause();
      } catch (error) {
        console.warn('VideoPlayer pause error on momentum end:', error);
      }
      setCurrentIndex(newIndex);
      resetZoom();
      setIsPlaying(false);
    }
  };

  return (
    <Modal visible={visible} animationType="fade" presentationStyle="fullScreen">
      <StatusBar hidden />
      <SafeAreaView style={styles.container}>
        {/* Header Controls */}
        {showControls && (
          <View style={styles.header}>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <Ionicons name="close" size={28} color={COLORS.text} />
            </TouchableOpacity>
            
            <Text style={styles.counter}>
              {currentIndex + 1} of {mediaItems.length}
            </Text>
            
            <TouchableOpacity onPress={resetZoom} style={styles.resetButton}>
              <Ionicons name="refresh-outline" size={24} color={COLORS.text} />
            </TouchableOpacity>
          </View>
        )}

        {/* Media Carousel */}
        <FlatList
          ref={flatListRef}
          data={mediaItems}
          renderItem={renderMediaItem}
          keyExtractor={(item) => item.id}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          initialScrollIndex={initialIndex}
          onViewableItemsChanged={onViewableItemsChanged}
          onMomentumScrollEnd={onMomentumScrollEnd}
          viewabilityConfig={{
            itemVisiblePercentThreshold: 80,
            minimumViewTime: 100
          }}
          getItemLayout={(_, index) => ({
            length: screenWidth,
            offset: screenWidth * index,
            index,
          })}
          onScrollToIndexFailed={(info) => {
            console.warn('⚠️ ScrollToIndex failed:', info);
            // Fallback: scroll to the closest valid offset
            const offset = Math.min(info.index * screenWidth, (mediaItems.length - 1) * screenWidth);
            flatListRef.current?.scrollToOffset({ offset, animated: true });
          }}
          removeClippedSubviews={false}
          scrollEventThrottle={16}
        />



        {/* Bottom Indicator */}
        {showControls && mediaItems.length > 1 && (
          <View style={styles.indicator}>
            {mediaItems.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.dot,
                  index === currentIndex && styles.activeDot,
                ]}
              />
            ))}
          </View>
        )}
        </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: COLORS.overlay,
  },
  closeButton: {
    padding: 8,
  },
  counter: {
    color: COLORS.text,
    fontSize: 16,
    fontWeight: '500',
  },
  resetButton: {
    padding: 8,
  },
  mediaContainer: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  media: {
    width: screenWidth,
    height: screenHeight * 0.8,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -30 }, { translateY: -30 }],
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 40,
    padding: 10,
    zIndex: 10,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 5,
  },

  indicator: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activeDot: {
    backgroundColor: COLORS.text,
    width: 10,
    height: 10,
    borderRadius: 5,
  },
});
