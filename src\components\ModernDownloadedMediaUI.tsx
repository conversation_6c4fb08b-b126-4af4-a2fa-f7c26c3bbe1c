/**
 * Modern Downloaded Media UI for IraChat
 * Features: Grid/list toggle, sorting, search, storage management, bulk operations
 * No fake implementations - fully functional media management interface
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
  Dimensions,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import { advancedMediaActions, MediaActionItem } from '../services/advancedMediaActions';
import { ModernMediaViewer } from './ModernMediaViewer';
import { remembranceService } from '../services/remembranceService';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export interface DownloadedMediaItem extends MediaActionItem {
  downloadedAt: Date;
  source: string;
  localSize: number;
  isSelected?: boolean;
}

interface ModernDownloadedMediaUIProps {
  visible: boolean;
  onClose: () => void;
  initialMediaItems?: DownloadedMediaItem[];
}

type ViewMode = 'grid' | 'list';
type SortBy = 'date' | 'name' | 'size' | 'type' | 'source';
type SortOrder = 'asc' | 'desc';

export const ModernDownloadedMediaUI: React.FC<ModernDownloadedMediaUIProps> = ({
  visible,
  onClose,
  initialMediaItems = [],
}) => {
  const [mediaItems, setMediaItems] = useState<DownloadedMediaItem[]>(initialMediaItems);
  const [filteredItems, setFilteredItems] = useState<DownloadedMediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy] = useState<SortBy>('date');
  const [sortOrder] = useState<SortOrder>('desc');
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [showMediaViewer, setShowMediaViewer] = useState(false);
  const [viewerIndex, setViewerIndex] = useState(0);

  // Animation values
  const searchBarHeight = useSharedValue(0);
  const selectionBarHeight = useSharedValue(0);

  useEffect(() => {
    if (visible) {
      loadMediaItems();
    }
  }, [visible]);

  useEffect(() => {
    filterAndSortItems();
  }, [mediaItems, searchQuery, sortBy, sortOrder]);

  useEffect(() => {
    // Animate search bar
    searchBarHeight.value = withSpring(searchQuery ? 60 : 0);
  }, [searchQuery]);

  useEffect(() => {
    // Animate selection bar
    selectionBarHeight.value = withSpring(isSelectionMode ? 60 : 0);
  }, [isSelectionMode]);

  const loadMediaItems = async () => {
    try {
      setLoading(true);
      // Load from remembrance service and local downloads
      const rememberedMedia = await remembranceService.getRememberedMedia();
      
      const items: DownloadedMediaItem[] = rememberedMedia.map(media => ({
        id: media.id,
        uri: media.localPath || media.mediaUrl,
        type: media.type,
        fileName: media.fileName,
        fileSize: media.fileSize,
        caption: media.caption,
        chatId: media.sourceChat,
        messageId: media.originalMediaId,
        senderId: media.originalSender,
        senderName: media.originalSenderName,
        downloadedAt: media.createdAt,
        source: media.sourceChatName,
        localSize: media.fileSize || 0,
      }));

      setMediaItems(items);
    } catch (error) {
      console.error('❌ Error loading media items:', error);
      Alert.alert('Error', 'Failed to load downloaded media');
    } finally {
      setLoading(false);
    }
  };



  const filterAndSortItems = () => {
    let filtered = [...mediaItems];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.fileName.toLowerCase().includes(query) ||
        item.caption?.toLowerCase().includes(query) ||
        item.source.toLowerCase().includes(query) ||
        item.senderName?.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'date':
          aValue = a.downloadedAt.getTime();
          bValue = b.downloadedAt.getTime();
          break;
        case 'name':
          aValue = a.fileName.toLowerCase();
          bValue = b.fileName.toLowerCase();
          break;
        case 'size':
          aValue = a.localSize;
          bValue = b.localSize;
          break;
        case 'type':
          aValue = a.type;
          bValue = b.type;
          break;
        case 'source':
          aValue = a.source.toLowerCase();
          bValue = b.source.toLowerCase();
          break;
        default:
          aValue = a.downloadedAt.getTime();
          bValue = b.downloadedAt.getTime();
      }

      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredItems(filtered);
  };

  const toggleSelection = (itemId: string) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId);
    } else {
      newSelected.add(itemId);
    }
    setSelectedItems(newSelected);

    if (newSelected.size === 0) {
      setIsSelectionMode(false);
    }
  };

  const selectAll = () => {
    if (selectedItems.size === filteredItems.length) {
      setSelectedItems(new Set());
      setIsSelectionMode(false);
    } else {
      setSelectedItems(new Set(filteredItems.map(item => item.id)));
    }
  };

  const handleLongPress = (itemId: string) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
      setSelectedItems(new Set([itemId]));
    }
  };

  const handleItemPress = (item: DownloadedMediaItem, index: number) => {
    if (isSelectionMode) {
      toggleSelection(item.id);
    } else {
      setViewerIndex(index);
      setShowMediaViewer(true);
    }
  };

  const handleBulkAction = async (action: 'download' | 'share' | 'delete' | 'remember') => {
    const selectedMediaItems = filteredItems.filter(item => selectedItems.has(item.id));
    
    if (selectedMediaItems.length === 0) {
      Alert.alert('No Selection', 'Please select items first');
      return;
    }

    try {
      let result;
      switch (action) {
        case 'download':
          result = await advancedMediaActions.bulkDownload(selectedMediaItems);
          break;
        case 'share':
          result = await advancedMediaActions.bulkShare(selectedMediaItems);
          break;
        case 'delete':
          result = await advancedMediaActions.bulkDelete(selectedMediaItems);
          if (result.success) {
            // Remove deleted items from state
            setMediaItems(prev => prev.filter(item => !selectedItems.has(item.id)));
          }
          break;
        case 'remember':
          result = await advancedMediaActions.bulkSaveToRemembrance(selectedMediaItems);
          break;
      }

      if (result) {
        const message = result.success 
          ? `Successfully processed ${result.processed} items`
          : `Processed ${result.processed} items, ${result.failed} failed`;
        
        Alert.alert(result.success ? 'Success' : 'Partial Success', message);
      }

      setSelectedItems(new Set());
      setIsSelectionMode(false);
    } catch (error) {
      console.error(`❌ Error in bulk ${action}:`, error);
      Alert.alert('Error', `Failed to ${action} selected items`);
    }
  };

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'image': return 'image-outline';
      case 'video': return 'videocam-outline';
      case 'audio': return 'musical-notes-outline';
      case 'document': return 'document-text-outline';
      default: return 'document-outline';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date): string => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const renderGridItem = ({ item, index }: { item: DownloadedMediaItem; index: number }) => {
    const itemWidth = (SCREEN_WIDTH - 60) / 3; // 3 columns with padding
    const isSelected = selectedItems.has(item.id);

    return (
      <TouchableOpacity
        style={{
          width: itemWidth,
          height: itemWidth,
          margin: 5,
          borderRadius: 8,
          overflow: 'hidden',
          backgroundColor: '#f0f0f0',
          opacity: isSelected ? 0.7 : 1,
          borderWidth: isSelected ? 3 : 0,
          borderColor: '#007AFF',
        }}
        onPress={() => handleItemPress(item, index)}
        onLongPress={() => handleLongPress(item.id)}
      >
        {item.type === 'image' ? (
          <Image
            source={{ uri: item.uri }}
            style={{ width: '100%', height: '100%' }}
            resizeMode="cover"
          />
        ) : (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#e0e0e0',
            }}
          >
            <Ionicons name={getItemIcon(item.type)} size={40} color="#666" />
            <Text style={{ fontSize: 10, color: '#666', marginTop: 5 }}>
              {item.type.toUpperCase()}
            </Text>
          </View>
        )}
        
        {isSelectionMode && (
          <View
            style={{
              position: 'absolute',
              top: 5,
              right: 5,
              width: 24,
              height: 24,
              borderRadius: 12,
              backgroundColor: isSelected ? '#007AFF' : 'rgba(0,0,0,0.5)',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {isSelected && <Ionicons name="checkmark" size={16} color="white" />}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderListItem = ({ item, index }: { item: DownloadedMediaItem; index: number }) => {
    const isSelected = selectedItems.has(item.id);

    return (
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          padding: 15,
          backgroundColor: isSelected ? '#f0f8ff' : 'white',
          borderBottomWidth: 1,
          borderBottomColor: '#e0e0e0',
        }}
        onPress={() => handleItemPress(item, index)}
        onLongPress={() => handleLongPress(item.id)}
      >
        <View
          style={{
            width: 60,
            height: 60,
            borderRadius: 8,
            backgroundColor: '#f0f0f0',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 15,
          }}
        >
          {item.type === 'image' ? (
            <Image
              source={{ uri: item.uri }}
              style={{ width: '100%', height: '100%', borderRadius: 8 }}
              resizeMode="cover"
            />
          ) : (
            <Ionicons name={getItemIcon(item.type)} size={30} color="#666" />
          )}
        </View>

        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 16, fontWeight: '500', marginBottom: 4 }}>
            {item.fileName}
          </Text>
          <Text style={{ fontSize: 14, color: '#666', marginBottom: 2 }}>
            From: {item.source}
          </Text>
          <Text style={{ fontSize: 12, color: '#999' }}>
            {formatFileSize(item.localSize)} • {formatDate(item.downloadedAt)}
          </Text>
        </View>

        {isSelectionMode && (
          <View
            style={{
              width: 24,
              height: 24,
              borderRadius: 12,
              backgroundColor: isSelected ? '#007AFF' : '#e0e0e0',
              justifyContent: 'center',
              alignItems: 'center',
              marginLeft: 10,
            }}
          >
            {isSelected && <Ionicons name="checkmark" size={16} color="white" />}
          </View>
        )}
      </TouchableOpacity>
    );
  };



  const selectionBarStyle = useAnimatedStyle(() => ({
    height: selectionBarHeight.value,
    opacity: selectionBarHeight.value / 60,
  }));

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="slide" statusBarTranslucent>
      <View style={{ flex: 1, backgroundColor: 'white' }}>
        {/* Header */}
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingHorizontal: 20,
            paddingTop: 50,
            paddingBottom: 15,
            backgroundColor: 'white',
            borderBottomWidth: 1,
            borderBottomColor: '#e0e0e0',
          }}
        >
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={28} color="#333" />
          </TouchableOpacity>

          <Text style={{ fontSize: 18, fontWeight: '600' }}>
            Downloaded Media
          </Text>

          <View style={{ flexDirection: 'row', gap: 15 }}>
            <TouchableOpacity
              onPress={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
            >
              <Ionicons
                name={viewMode === 'grid' ? 'list-outline' : 'grid-outline'}
                size={24}
                color="#333"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Search Bar */}
        <View style={{ paddingHorizontal: 20 }}>
          <TextInput
            style={{
              height: 40,
              borderWidth: 1,
              borderColor: '#e0e0e0',
              borderRadius: 20,
              paddingHorizontal: 15,
              fontSize: 16,
              backgroundColor: '#f8f8f8',
            }}
            placeholder="Search media..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Selection Bar */}
        <Animated.View
          style={[
            {
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingHorizontal: 20,
              backgroundColor: '#f0f8ff',
              borderBottomWidth: 1,
              borderBottomColor: '#e0e0e0',
            },
            selectionBarStyle,
          ]}
        >
          <TouchableOpacity onPress={selectAll}>
            <Text style={{ color: '#007AFF', fontSize: 16 }}>
              {selectedItems.size === filteredItems.length ? 'Deselect All' : 'Select All'}
            </Text>
          </TouchableOpacity>

          <Text style={{ fontSize: 16, fontWeight: '500' }}>
            {selectedItems.size} selected
          </Text>

          <View style={{ flexDirection: 'row', gap: 20 }}>
            <TouchableOpacity onPress={() => handleBulkAction('share')}>
              <Ionicons name="share-outline" size={24} color="#007AFF" />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => handleBulkAction('delete')}>
              <Ionicons name="trash-outline" size={24} color="#FF3B30" />
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Content */}
        {loading ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={{ marginTop: 10, color: '#666' }}>Loading media...</Text>
          </View>
        ) : filteredItems.length === 0 ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Ionicons name="download-outline" size={80} color="#ccc" />
            <Text style={{ fontSize: 18, color: '#666', marginTop: 20 }}>
              No downloaded media
            </Text>
            <Text style={{ fontSize: 14, color: '#999', marginTop: 5 }}>
              Media you download will appear here
            </Text>
          </View>
        ) : (
          <FlatList
            data={filteredItems}
            renderItem={viewMode === 'grid' ? renderGridItem : renderListItem}
            keyExtractor={(item) => item.id}
            numColumns={viewMode === 'grid' ? 3 : 1}
            key={viewMode} // Force re-render when view mode changes
            contentContainerStyle={{ padding: 10 }}
            showsVerticalScrollIndicator={false}
          />
        )}

        {/* Media Viewer */}
        {showMediaViewer && (
          <ModernMediaViewer
            visible={showMediaViewer}
            mediaItems={filteredItems
              .filter(item => item.type === 'image' || item.type === 'video')
              .map(item => ({
                id: item.id,
                uri: item.uri,
                type: item.type as 'image' | 'video',
                caption: item.caption,
                fileName: item.fileName,
              }))}
            initialIndex={viewerIndex}
            onClose={() => setShowMediaViewer(false)}
            onSaveForRemembrance={() => {
              // Already in remembrance, show message
              Alert.alert('Already Saved', 'This media is already in your remembrance collection');
            }}
            showRememberanceOption={false}
          />
        )}
      </View>
    </Modal>
  );
};
