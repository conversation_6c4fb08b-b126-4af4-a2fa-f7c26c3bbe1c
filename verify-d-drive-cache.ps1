# PowerShell script to verify D: drive cache setup
Write-Host "Verifying D: drive cache configuration..." -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

# Check if cache directories exist
$cacheDirectories = @(
    "D:\gradle-cache",
    "D:\android-build-cache", 
    "D:\metro-cache",
    "D:\npm-cache",
    "D:\yarn-cache",
    "D:\temp",
    "D:\expo-cache",
    "D:\react-native-cache"
)

Write-Host "`nChecking cache directories..." -ForegroundColor Yellow
$allDirsExist = $true
foreach ($dir in $cacheDirectories) {
    if (Test-Path $dir) {
        $size = (Get-ChildItem $dir -Recurse -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
        $sizeStr = if ($size) { " ($([math]::Round($size/1MB, 2)) MB)" } else { " (empty)" }
        Write-Host "✅ $dir$sizeStr" -ForegroundColor Green
    } else {
        Write-Host "❌ $dir (missing)" -ForegroundColor Red
        $allDirsExist = $false
    }
}

# Check environment variables
Write-Host "`nChecking environment variables..." -ForegroundColor Yellow
$envVars = @{
    "GRADLE_USER_HOME" = "D:\gradle-cache"
    "TEMP" = "D:\temp"
    "TMP" = "D:\temp"
    "NPM_CONFIG_CACHE" = "D:\npm-cache"
    "YARN_CACHE_FOLDER" = "D:\yarn-cache"
    "EXPO_CACHE_DIR" = "D:\expo-cache"
}

$allEnvVarsSet = $true
foreach ($var in $envVars.GetEnumerator()) {
    $currentValue = [Environment]::GetEnvironmentVariable($var.Key, [EnvironmentVariableTarget]::User)
    if ($currentValue -eq $var.Value) {
        Write-Host "✅ $($var.Key) = $currentValue" -ForegroundColor Green
    } else {
        Write-Host "❌ $($var.Key) = $currentValue (expected: $($var.Value))" -ForegroundColor Red
        $allEnvVarsSet = $false
    }
}

# Check Android SDK
Write-Host "`nChecking Android SDK..." -ForegroundColor Yellow
$androidHome = [Environment]::GetEnvironmentVariable("ANDROID_HOME", [EnvironmentVariableTarget]::User)
if ($androidHome -and (Test-Path $androidHome)) {
    Write-Host "✅ ANDROID_HOME = $androidHome" -ForegroundColor Green
    
    $adbPath = Join-Path $androidHome "platform-tools\adb.exe"
    if (Test-Path $adbPath) {
        Write-Host "✅ ADB found at: $adbPath" -ForegroundColor Green
    } else {
        Write-Host "❌ ADB not found at: $adbPath" -ForegroundColor Red
    }
} else {
    Write-Host "❌ ANDROID_HOME not set or invalid: $androidHome" -ForegroundColor Red
}

# Check configuration files
Write-Host "`nChecking configuration files..." -ForegroundColor Yellow

# Check .npmrc
if (Test-Path ".npmrc") {
    $npmrcContent = Get-Content ".npmrc" -Raw
    if ($npmrcContent -like "*D:\\npm-cache*") {
        Write-Host "✅ .npmrc configured for D: drive" -ForegroundColor Green
    } else {
        Write-Host "❌ .npmrc not configured for D: drive" -ForegroundColor Red
    }
} else {
    Write-Host "❌ .npmrc file missing" -ForegroundColor Red
}

# Check gradle.properties
if (Test-Path "android\gradle.properties") {
    $gradleContent = Get-Content "android\gradle.properties" -Raw
    if ($gradleContent -like "*D:\\gradle-cache*") {
        Write-Host "✅ gradle.properties configured for D: drive" -ForegroundColor Green
    } else {
        Write-Host "❌ gradle.properties not configured for D: drive" -ForegroundColor Red
    }
} else {
    Write-Host "❌ gradle.properties file missing" -ForegroundColor Red
}

# Check metro.config.js
if (Test-Path "metro.config.js") {
    $metroContent = Get-Content "metro.config.js" -Raw
    if ($metroContent -like "*D:\\metro-cache*") {
        Write-Host "✅ metro.config.js configured for D: drive" -ForegroundColor Green
    } else {
        Write-Host "❌ metro.config.js not configured for D: drive" -ForegroundColor Red
    }
} else {
    Write-Host "❌ metro.config.js file missing" -ForegroundColor Red
}

# Summary
Write-Host "`n=========================================" -ForegroundColor Cyan
if ($allDirsExist -and $allEnvVarsSet) {
    Write-Host "✅ D: drive cache setup is COMPLETE!" -ForegroundColor Green
    Write-Host "All build cache will now go to D: drive." -ForegroundColor Green
} else {
    Write-Host "❌ D: drive cache setup is INCOMPLETE!" -ForegroundColor Red
    Write-Host "Please run setup-d-drive-complete.bat to fix issues." -ForegroundColor Yellow
}
Write-Host "=========================================" -ForegroundColor Cyan
