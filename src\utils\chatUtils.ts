import { UnifiedChatItem } from '../components/ModernChatItem';
import { Dimensions } from 'react-native';

// Get device dimensions for responsive chat utilities
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

// Chat message interfaces
export interface ChatMessage {
  id: string;
  text?: string;
  senderId: string;
  senderName?: string;
  timestamp: Date;
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'location' | 'contact';
  mediaUrl?: string;
  fileName?: string;
  fileSize?: number;
  duration?: number;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  replyTo?: string;
  isEdited?: boolean;
  reactions?: { [emoji: string]: string[] };
  mentions?: string[];
  isForwarded?: boolean;
}

export interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: Date;
  role?: 'admin' | 'member';
}

export interface ChatInfo {
  id: string;
  name?: string;
  description?: string;
  avatar?: string;
  isGroup: boolean;
  participants: ChatParticipant[];
  createdAt: Date;
  createdBy: string;
  settings: {
    muteNotifications: boolean;
    disappearingMessages: boolean;
    readReceipts: boolean;
    allowMediaDownload: boolean;
  };
}

// 🚀 ENHANCED UTILITY FUNCTIONS WITH PERFORMANCE OPTIMIZATION AND MOBILE SUPPORT
export const formatTime = (date: Date): string => {
  try {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);

    // Use shorter formats for small devices
    if (isSmallDevice) {
      if (minutes < 1) return "now";
      if (minutes < 60) return `${minutes}m`;
      if (hours < 24) return `${hours}h`;
      if (days === 1) return "1d";
      if (days < 7) return `${days}d`;
      if (weeks < 4) return `${weeks}w`;
      if (months < 12) return `${months}mo`;
      return date.getFullYear().toString();
    }

    // Full format for larger devices
    if (minutes < 1) return "now";
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days === 1) return "yesterday";
    if (days < 7) return `${days} days ago`;
    if (weeks === 1) return "1 week ago";
    if (weeks < 4) return `${weeks} weeks ago`;
    if (months === 1) return "1 month ago";
    if (months < 12) return `${months} months ago`;
    return date.toLocaleDateString();
  } catch (error) {
    return "unknown";
  }
};

// Enhanced message preview with better type handling and mobile optimization
export const getMessagePreview = (item: UnifiedChatItem): string => {
  try {
    if (item.isTyping) return "typing...";

    // Use shorter previews for small devices
    const shortFormat = isSmallDevice;

    const messageType = item.lastMessageType as string;

    switch (messageType) {
      case "image":
        return shortFormat ? "📸" : "📸 Photo";
      case "video":
        return shortFormat ? "🎥" : "🎥 Video";
      case "voice":
      case "audio":
        return shortFormat ? "🎵" : "🎵 Voice message";
      case "document":
        return shortFormat ? "📄" : "📄 Document";
      case "location":
        return shortFormat ? "📍" : "📍 Location";
      case "contact":
        return shortFormat ? "👤" : "👤 Contact";
      case "sticker":
        return shortFormat ? "😊" : "😊 Sticker";
      case "gif":
        return shortFormat ? "🎬" : "🎬 GIF";
      default:
        const message = item.lastMessage || "No messages yet";
        // Truncate long messages on small devices
        if (shortFormat && message.length > 25) {
          return message.substring(0, 22) + "...";
        }
        return message;
    }
  } catch (error) {
    return "No messages yet";
  }
};

// Get message preview with sender name for groups
export const getGroupMessagePreview = (item: UnifiedChatItem, currentUserId: string): string => {
  try {
    if (item.isTyping) {
      const typingUser = (item as any).typingUsers?.[0];
      return typingUser ? `${typingUser} is typing...` : "typing...";
    }

    if (!item.isGroup) {
      return getMessagePreview(item);
    }

    const senderName = (item as any).lastMessageSender;
    const preview = getMessagePreview(item);

    if (!senderName || senderName === currentUserId) {
      return preview.startsWith("📸") || preview.startsWith("🎥") ||
             preview.startsWith("🎵") || preview.startsWith("📄") ||
             preview.startsWith("📍") || preview.startsWith("👤") ||
             preview.startsWith("😊") || preview.startsWith("🎬")
        ? `You: ${preview}`
        : preview === "No messages yet" ? preview : `You: ${preview}`;
    }

    const displayName = isSmallDevice && senderName.length > 8
      ? senderName.substring(0, 8) + "..."
      : senderName;

    return `${displayName}: ${preview}`;
  } catch (error) {
    return getMessagePreview(item);
  }
};

// Get appropriate icon for message type with enhanced support
export const getMessageTypeIcon = (type?: string): string | null => {
  switch (type) {
    case 'image': return 'image';
    case 'video': return 'videocam';
    case 'voice':
    case 'audio': return 'musical-notes';
    case 'document': return 'document';
    case 'location': return 'location';
    case 'contact': return 'person';
    case 'sticker': return 'happy';
    case 'gif': return 'film';
    default: return null;
  }
};

// Enhanced connection status indicator with more states
export const getConnectionStatusColor = (connectionStatus: 'connected' | 'connecting' | 'disconnected' | 'reconnecting' | 'failed'): string => {
  switch (connectionStatus) {
    case 'connected': return '#10B981'; // Green
    case 'connecting': return '#F59E0B'; // Amber
    case 'reconnecting': return '#8B5CF6'; // Purple
    case 'disconnected': return '#EF4444'; // Red
    case 'failed': return '#DC2626'; // Dark red
    default: return '#9CA3AF'; // Gray
  }
};

// Get connection status text
export const getConnectionStatusText = (connectionStatus: string): string => {
  switch (connectionStatus) {
    case 'connected': return 'Connected';
    case 'connecting': return 'Connecting...';
    case 'reconnecting': return 'Reconnecting...';
    case 'disconnected': return 'Disconnected';
    case 'failed': return 'Connection failed';
    default: return 'Unknown';
  }
};

// Performance optimized chat item key extractor with timestamp
export const keyExtractor = (item: UnifiedChatItem): string => {
  return `${item.id}_${item.lastMessageAt || Date.now()}`;
};

// Responsive item layout for better performance
export const getItemLayout = (_data: UnifiedChatItem[] | null | undefined, index: number) => {
  const itemHeight = isTablet ? 88 : isSmallDevice ? 72 : 80;
  return {
    length: itemHeight,
    offset: itemHeight * index,
    index,
  };
};

// Get responsive chat item height
export const getChatItemHeight = (isCompact: boolean = false): number => {
  if (isCompact) {
    return isSmallDevice ? 60 : isTablet ? 72 : 68;
  }
  return isSmallDevice ? 72 : isTablet ? 88 : 80;
};

/**
 * Format message timestamp for display
 */
export const formatMessageTime = (timestamp: Date): string => {
  try {
    const now = new Date();
    const messageDate = new Date(timestamp);
    const isToday = messageDate.toDateString() === now.toDateString();
    const isYesterday = new Date(now.getTime() - 86400000).toDateString() === messageDate.toDateString();

    if (isToday) {
      // Detect device's time format preference
      const deviceUses24Hour = (() => {
        try {
          const testDate = new Date(2023, 0, 1, 13, 0, 0);
          const timeString = testDate.toLocaleTimeString();
          const has12HourIndicator = /AM|PM|am|pm/i.test(timeString);
          return !has12HourIndicator;
        } catch {
          return false;
        }
      })();

      return messageDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        hour12: !deviceUses24Hour // Use device's time format preference
      });
    } else if (isYesterday) {
      return isSmallDevice ? 'Yesterday' : `Yesterday ${messageDate.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit'
      })}`;
    } else {
      return isSmallDevice
        ? messageDate.toLocaleDateString([], { month: 'short', day: 'numeric' })
        : messageDate.toLocaleDateString([], {
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          });
    }
  } catch (error) {
    return '';
  }
};

/**
 * Get message status icon
 */
export const getMessageStatusIcon = (status: string): string => {
  switch (status) {
    case 'sending': return 'time-outline';
    case 'sent': return 'checkmark-outline';
    case 'delivered': return 'checkmark-done-outline';
    case 'read': return 'checkmark-done';
    case 'failed': return 'close-circle-outline';
    default: return 'time-outline';
  }
};

/**
 * Get message status color
 */
export const getMessageStatusColor = (status: string): string => {
  switch (status) {
    case 'sending': return '#9CA3AF';
    case 'sent': return '#9CA3AF';
    case 'delivered': return '#10B981';
    case 'read': return '#3B82F6';
    case 'failed': return '#EF4444';
    default: return '#9CA3AF';
  }
};

/**
 * Check if message is from current user
 */
export const isOwnMessage = (message: ChatMessage, currentUserId: string): boolean => {
  return message.senderId === currentUserId;
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

/**
 * Format audio/video duration
 */
export const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

/**
 * Generate message ID
 */
export const generateMessageId = (senderId: string, chatId: string): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `msg_${timestamp}_${senderId}_${chatId}_${random}`;
};

// Offline message management
/**
 * Save message to offline storage
 */
export const saveMessageOffline = async (message: ChatMessage, chatId: string): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync(`
      INSERT OR REPLACE INTO offline_messages (
        id, chatId, text, senderId, senderName, timestamp, type,
        mediaUrl, fileName, fileSize, duration, status, replyTo,
        isEdited, reactions, mentions, isForwarded, localPath
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      message.id,
      chatId,
      message.text || '',
      message.senderId,
      message.senderName || '',
      message.timestamp.getTime(),
      message.type,
      message.mediaUrl || null,
      message.fileName || null,
      message.fileSize || null,
      message.duration || null,
      message.status,
      message.replyTo || null,
      message.isEdited ? 1 : 0,
      JSON.stringify(message.reactions || {}),
      JSON.stringify(message.mentions || []),
      message.isForwarded ? 1 : 0,
      null // localPath for media files
    ]);
  } catch (error) {
    // Offline save failed - continue
  }
};

/**
 * Get offline messages for a chat
 */
export const getOfflineMessages = async (chatId: string, limit: number = 50): Promise<ChatMessage[]> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const result = await database.getAllAsync(`
      SELECT * FROM offline_messages
      WHERE chatId = ?
      ORDER BY timestamp DESC
      LIMIT ?
    `, [chatId, limit]);

    return result.map((row: any) => ({
      id: row.id,
      text: row.text,
      senderId: row.senderId,
      senderName: row.senderName,
      timestamp: new Date(row.timestamp),
      type: row.type,
      mediaUrl: row.mediaUrl,
      fileName: row.fileName,
      fileSize: row.fileSize,
      duration: row.duration,
      status: row.status,
      replyTo: row.replyTo,
      isEdited: row.isEdited === 1,
      reactions: JSON.parse(row.reactions || '{}'),
      mentions: JSON.parse(row.mentions || '[]'),
      isForwarded: row.isForwarded === 1,
    }));
  } catch (error) {
    return [];
  }
};

/**
 * Update message status in offline storage
 */
export const updateOfflineMessageStatus = async (messageId: string, status: string): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync(`
      UPDATE offline_messages
      SET status = ?
      WHERE id = ?
    `, [status, messageId]);
  } catch (error) {
    // Update failed - continue
  }
};

/**
 * Delete message from offline storage
 */
export const deleteOfflineMessage = async (messageId: string): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync('DELETE FROM offline_messages WHERE id = ?', [messageId]);
  } catch (error) {
    // Delete failed - continue
  }
};

/**
 * Get pending messages (failed or sending)
 */
export const getPendingMessages = async (chatId?: string): Promise<ChatMessage[]> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const query = chatId
      ? 'SELECT * FROM offline_messages WHERE chatId = ? AND (status = "sending" OR status = "failed") ORDER BY timestamp ASC'
      : 'SELECT * FROM offline_messages WHERE (status = "sending" OR status = "failed") ORDER BY timestamp ASC';

    const params = chatId ? [chatId] : [];
    const result = await database.getAllAsync(query, params);

    return result.map((row: any) => ({
      id: row.id,
      text: row.text,
      senderId: row.senderId,
      senderName: row.senderName,
      timestamp: new Date(row.timestamp),
      type: row.type,
      mediaUrl: row.mediaUrl,
      fileName: row.fileName,
      fileSize: row.fileSize,
      duration: row.duration,
      status: row.status,
      replyTo: row.replyTo,
      isEdited: row.isEdited === 1,
      reactions: JSON.parse(row.reactions || '{}'),
      mentions: JSON.parse(row.mentions || '[]'),
      isForwarded: row.isForwarded === 1,
    }));
  } catch (error) {
    return [];
  }
};

/**
 * Clean old offline messages (older than 30 days)
 */
export const cleanOldOfflineMessages = async (): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

    await database.runAsync(`
      DELETE FROM offline_messages
      WHERE timestamp < ? AND status != "sending" AND status != "failed"
    `, [thirtyDaysAgo]);
  } catch (error) {
    // Cleanup failed - continue
  }
};

/**
 * Search messages in offline storage
 */
export const searchOfflineMessages = async (chatId: string, query: string): Promise<ChatMessage[]> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const searchQuery = `%${query.toLowerCase()}%`;
    const result = await database.getAllAsync(`
      SELECT * FROM offline_messages
      WHERE chatId = ? AND LOWER(text) LIKE ?
      ORDER BY timestamp DESC
      LIMIT 50
    `, [chatId, searchQuery]);

    return result.map((row: any) => ({
      id: row.id,
      text: row.text,
      senderId: row.senderId,
      senderName: row.senderName,
      timestamp: new Date(row.timestamp),
      type: row.type,
      mediaUrl: row.mediaUrl,
      fileName: row.fileName,
      fileSize: row.fileSize,
      duration: row.duration,
      status: row.status,
      replyTo: row.replyTo,
      isEdited: row.isEdited === 1,
      reactions: JSON.parse(row.reactions || '{}'),
      mentions: JSON.parse(row.mentions || '[]'),
      isForwarded: row.isForwarded === 1,
    }));
  } catch (error) {
    return [];
  }
};

/**
 * Get chat statistics
 */
export const getChatStatistics = async (chatId: string): Promise<{
  totalMessages: number;
  mediaMessages: number;
  todayMessages: number;
  unreadMessages: number;
}> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    // Total messages
    const totalResult = await database.getAllAsync(`
      SELECT COUNT(*) as count FROM offline_messages WHERE chatId = ?
    `, [chatId]);

    // Media messages
    const mediaResult = await database.getAllAsync(`
      SELECT COUNT(*) as count FROM offline_messages
      WHERE chatId = ? AND type IN ('image', 'video', 'audio', 'document')
    `, [chatId]);

    // Today's messages
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayResult = await database.getAllAsync(`
      SELECT COUNT(*) as count FROM offline_messages
      WHERE chatId = ? AND timestamp >= ?
    `, [chatId, todayStart.getTime()]);

    return {
      totalMessages: (totalResult[0] as any)?.count || 0,
      mediaMessages: (mediaResult[0] as any)?.count || 0,
      todayMessages: (todayResult[0] as any)?.count || 0,
      unreadMessages: 0, // This would need to be calculated based on read receipts
    };
  } catch (error) {
    return {
      totalMessages: 0,
      mediaMessages: 0,
      todayMessages: 0,
      unreadMessages: 0,
    };
  }
};

/**
 * Check if chat has offline messages
 */
export const hasOfflineMessages = async (chatId: string): Promise<boolean> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const result = await database.getAllAsync(`
      SELECT COUNT(*) as count FROM offline_messages WHERE chatId = ? LIMIT 1
    `, [chatId]);

    return ((result[0] as any)?.count || 0) > 0;
  } catch (error) {
    return false;
  }
};

/**
 * Validate message content
 */
export const validateMessage = (message: Partial<ChatMessage>): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  if (!message.senderId) {
    errors.push('Sender ID is required');
  }

  if (!message.type) {
    errors.push('Message type is required');
  }

  if (message.type === 'text' && (!message.text || message.text.trim().length === 0)) {
    errors.push('Text content is required for text messages');
  }

  if (message.type !== 'text' && !message.mediaUrl) {
    errors.push('Media URL is required for non-text messages');
  }

  if (message.text && message.text.length > 4000) {
    errors.push('Message text is too long (max 4000 characters)');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};
