import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  Alert,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants/theme';
import { BusinessPost } from '../../types/Business';

interface PriceAdjustmentModalProps {
  visible: boolean;
  post: BusinessPost | null;
  onClose: () => void;
  onPriceUpdate: (newPrice: number, oldPrice: number) => void;
}

export const PriceAdjustmentModal: React.FC<PriceAdjustmentModalProps> = ({
  visible,
  post,
  onClose,
  onPriceUpdate,
}) => {
  const [newPrice, setNewPrice] = useState('');
  const [adjustmentType, setAdjustmentType] = useState<'increase' | 'decrease'>('decrease');

  const currentPrice = typeof post?.price === 'number' ? post.price : parseFloat(post?.price || '0');

  const handleSubmit = () => {
    const price = parseFloat(newPrice);
    
    if (isNaN(price) || price <= 0) {
      Alert.alert('Invalid Price', 'Please enter a valid price amount');
      return;
    }

    if (adjustmentType === 'decrease' && price >= currentPrice) {
      Alert.alert('Invalid Reduction', 'New price must be lower than current price');
      return;
    }

    if (adjustmentType === 'increase' && price <= currentPrice) {
      Alert.alert('Invalid Increase', 'New price must be higher than current price');
      return;
    }

    onPriceUpdate(price, currentPrice);
    setNewPrice('');
    onClose();
  };

  const handleClose = () => {
    setNewPrice('');
    onClose();
  };

  if (!post) return null;

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Adjust Price</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.content}>
          <Text style={styles.productTitle}>{post.title}</Text>
          
          <View style={styles.currentPriceSection}>
            <Text style={styles.label}>Current Price:</Text>
            <Text style={styles.currentPrice}>
              {post.currency} {currentPrice.toLocaleString()}
            </Text>
          </View>

          <View style={styles.adjustmentTypeSection}>
            <Text style={styles.label}>Adjustment Type:</Text>
            <View style={styles.typeButtons}>
              <TouchableOpacity
                style={[
                  styles.typeButton,
                  adjustmentType === 'decrease' && styles.typeButtonActive
                ]}
                onPress={() => setAdjustmentType('decrease')}
              >
                <Ionicons 
                  name="trending-down-outline" 
                  size={20} 
                  color={adjustmentType === 'decrease' ? '#FFFFFF' : COLORS.error} 
                />
                <Text style={[
                  styles.typeButtonText,
                  adjustmentType === 'decrease' && styles.typeButtonTextActive
                ]}>
                  Reduce Price
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.typeButton,
                  adjustmentType === 'increase' && styles.typeButtonActive
                ]}
                onPress={() => setAdjustmentType('increase')}
              >
                <Ionicons 
                  name="trending-up-outline" 
                  size={20} 
                  color={adjustmentType === 'increase' ? '#FFFFFF' : COLORS.success} 
                />
                <Text style={[
                  styles.typeButtonText,
                  adjustmentType === 'increase' && styles.typeButtonTextActive
                ]}>
                  Increase Price
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.newPriceSection}>
            <Text style={styles.label}>New Price:</Text>
            <View style={styles.priceInputContainer}>
              <Text style={styles.currency}>{post.currency}</Text>
              <TextInput
                style={styles.priceInput}
                value={newPrice}
                onChangeText={setNewPrice}
                placeholder="Enter new price"
                keyboardType="numeric"
                placeholderTextColor={COLORS.textSecondary}
              />
            </View>
          </View>

          {newPrice && !isNaN(parseFloat(newPrice)) && (
            <View style={styles.previewSection}>
              <Text style={styles.label}>Preview:</Text>
              <View style={styles.pricePreview}>
                <Text style={styles.oldPriceStrikethrough}>
                  {post.currency} {currentPrice.toLocaleString()}
                </Text>
                <Text style={[
                  styles.newPricePreview,
                  { color: adjustmentType === 'decrease' ? COLORS.error : COLORS.success }
                ]}>
                  {post.currency} {parseFloat(newPrice).toLocaleString()}
                </Text>
              </View>
            </View>
          )}

          <TouchableOpacity
            style={[
              styles.submitButton,
              { backgroundColor: adjustmentType === 'decrease' ? COLORS.error : COLORS.success }
            ]}
            onPress={handleSubmit}
          >
            <Ionicons 
              name={adjustmentType === 'decrease' ? "trending-down-outline" : "trending-up-outline"} 
              size={20} 
              color="#FFFFFF" 
            />
            <Text style={styles.submitButtonText}>
              {adjustmentType === 'decrease' ? 'Reduce' : 'Increase'} Price
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  productTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 24,
    textAlign: 'center',
  },
  currentPriceSection: {
    marginBottom: 24,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 8,
  },
  currentPrice: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.success,
  },
  adjustmentTypeSection: {
    marginBottom: 24,
  },
  typeButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
    gap: 8,
  },
  typeButtonActive: {
    backgroundColor: COLORS.primary,
    borderColor: COLORS.primary,
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text,
  },
  typeButtonTextActive: {
    color: '#FFFFFF',
  },
  newPriceSection: {
    marginBottom: 24,
  },
  priceInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 12,
    paddingHorizontal: 12,
    backgroundColor: COLORS.surface,
  },
  currency: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.text,
    marginRight: 8,
  },
  priceInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    paddingVertical: 12,
  },
  previewSection: {
    marginBottom: 24,
  },
  pricePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  oldPriceStrikethrough: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textDecorationLine: 'line-through',
  },
  newPricePreview: {
    fontSize: 18,
    fontWeight: '600',
  },
  submitButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
    marginTop: 'auto',
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
