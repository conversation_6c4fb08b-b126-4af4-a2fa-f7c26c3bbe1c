// 🚀 PARTNER PROFILE PAGE - Complete User Profile View
// Displays partner's profile information, media, and actions

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  StatusBar,
  Dimensions,
  useColorScheme,
  Modal,
  Alert,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { realTimeMessagingService } from '../services/realTimeMessagingService';
import { userProfileService } from '../services/userProfileService';

const { width, height } = Dimensions.get('window');

// Theme colors
const LIGHT_THEME = {
  background: '#FFFFFF',
  headerBackground: '#517DA2',
  headerText: '#FFFFFF',
  cardBackground: '#F8F9FA',
  text: '#000000',
  secondaryText: '#666666',
  divider: '#E1E1E1',
  buttonBackground: '#4A90E2',
  buttonText: '#FFFFFF',
};

const DARK_THEME = {
  background: '#0E1621',
  headerBackground: '#1C2733',
  headerText: '#FFFFFF',
  cardBackground: '#182533',
  text: '#FFFFFF',
  secondaryText: '#8E8E93',
  divider: '#2B3A4A',
  buttonBackground: '#4A90E2',
  buttonText: '#FFFFFF',
};

interface PartnerProfile {
  id: string;
  name: string;
  avatar?: string;
  phoneNumber?: string;
  bio?: string;
  isOnline: boolean;
  lastSeen?: Date;
  joinedDate?: Date;
  sharedMedia: MediaItem[];
  mutualGroups: string[];
}

interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnail?: string;
  timestamp: Date;
}

interface PartnerProfilePageProps {
  partnerId: string;
  currentUserId: string;
  onBack?: () => void;
  onStartChat?: (partnerId: string) => void;
  isDarkMode?: boolean;
}

export const PartnerProfilePage: React.FC<PartnerProfilePageProps> = ({
  partnerId,
  currentUserId,
  onBack,
  onStartChat,
  isDarkMode,
}) => {
  const router = useRouter();
  const systemColorScheme = useColorScheme();
  
  // Determine theme
  const isUsingDarkMode = isDarkMode !== undefined ? isDarkMode : systemColorScheme === 'dark';
  const theme = isUsingDarkMode ? DARK_THEME : LIGHT_THEME;
  
  // State
  const [profile, setProfile] = useState<PartnerProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showAvatarModal, setShowAvatarModal] = useState(false);
  const [selectedMediaTab, setSelectedMediaTab] = useState<'media' | 'docs' | 'links'>('media');
  const [isBlocked, setIsBlocked] = useState(false);
  const [isMuted, setIsMuted] = useState(false);

  // Load profile data
  useEffect(() => {
    loadPartnerProfile();
  }, [partnerId]);

  const loadPartnerProfile = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Load partner profile from service
      const profileData = await userProfileService.getUserProfile(partnerId);
      const sharedMedia = await userProfileService.getSharedMedia(partnerId, currentUserId);
      const mutualGroups = await userProfileService.getMutualGroups(partnerId, currentUserId);
      
      setProfile({
        ...profileData,
        sharedMedia,
        mutualGroups,
      });
      
      // Check if user is blocked or muted
      const blockStatus = await userProfileService.getBlockStatus(partnerId, currentUserId);
      const muteStatus = await userProfileService.getMuteStatus(partnerId, currentUserId);
      
      setIsBlocked(blockStatus);
      setIsMuted(muteStatus);
      
    } catch (error) {
      console.error('Failed to load partner profile:', error);
      Alert.alert('Error', 'Failed to load profile');
    } finally {
      setIsLoading(false);
    }
  }, [partnerId, currentUserId]);

  // Actions
  const handleStartChat = useCallback(() => {
    if (onStartChat) {
      onStartChat(partnerId);
    } else {
      router.push(`/chat/${partnerId}`);
    }
  }, [partnerId, onStartChat, router]);

  const handleCall = useCallback(() => {
    Alert.alert('Voice Call', `Call ${profile?.name}?`, [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Call', onPress: () => console.log('Starting voice call...') },
    ]);
  }, [profile?.name]);

  const handleVideoCall = useCallback(() => {
    Alert.alert('Video Call', `Video call ${profile?.name}?`, [
      { text: 'Cancel', style: 'cancel' },
      { text: 'Call', onPress: () => console.log('Starting video call...') },
    ]);
  }, [profile?.name]);

  const handleBlock = useCallback(async () => {
    try {
      if (isBlocked) {
        await userProfileService.unblockUser(partnerId, currentUserId);
        setIsBlocked(false);
        Alert.alert('Success', `${profile?.name} has been unblocked`);
      } else {
        Alert.alert('Block User', `Block ${profile?.name}?`, [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Block', 
            style: 'destructive',
            onPress: async () => {
              await userProfileService.blockUser(partnerId, currentUserId);
              setIsBlocked(true);
            }
          },
        ]);
      }
    } catch (error) {
      console.error('Failed to toggle block status:', error);
      Alert.alert('Error', 'Failed to update block status');
    }
  }, [isBlocked, partnerId, currentUserId, profile?.name]);

  const handleMute = useCallback(async () => {
    try {
      if (isMuted) {
        await userProfileService.unmuteUser(partnerId, currentUserId);
        setIsMuted(false);
        Alert.alert('Success', `${profile?.name} has been unmuted`);
      } else {
        await userProfileService.muteUser(partnerId, currentUserId);
        setIsMuted(true);
        Alert.alert('Success', `${profile?.name} has been muted`);
      }
    } catch (error) {
      console.error('Failed to toggle mute status:', error);
      Alert.alert('Error', 'Failed to update mute status');
    }
  }, [isMuted, partnerId, currentUserId, profile?.name]);

  const renderMediaItem = ({ item }: { item: MediaItem }) => (
    <TouchableOpacity style={styles.mediaItem}>
      <Image source={{ uri: item.thumbnail || item.url }} style={styles.mediaThumbnail} />
      {item.type === 'video' && (
        <View style={styles.playIcon}>
          <Ionicons name="play" size={16} color="#FFFFFF" />
        </View>
      )}
    </TouchableOpacity>
  );

  const getStatusText = () => {
    if (!profile) return '';
    
    if (profile.isOnline) {
      return 'Online';
    } else if (profile.lastSeen) {
      const now = new Date();
      const lastSeen = new Date(profile.lastSeen);
      const diffMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
      
      if (diffMinutes < 1) {
        return 'Last seen just now';
      } else if (diffMinutes < 60) {
        return `Last seen ${diffMinutes} minutes ago`;
      } else if (diffMinutes < 1440) {
        const hours = Math.floor(diffMinutes / 60);
        return `Last seen ${hours} hour${hours > 1 ? 's' : ''} ago`;
      } else {
        return `Last seen ${lastSeen.toLocaleDateString()}`;
      }
    }
    
    return 'Last seen recently';
  };

  if (isLoading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <StatusBar 
          barStyle={isUsingDarkMode ? 'light-content' : 'dark-content'} 
          backgroundColor={theme.headerBackground}
        />
        <View style={[styles.header, { backgroundColor: theme.headerBackground }]}>
          <TouchableOpacity onPress={onBack || (() => router.back())} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={theme.headerText} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.headerText }]}>Profile</Text>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={[styles.loadingText, { color: theme.text }]}>Loading profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!profile) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        <StatusBar 
          barStyle={isUsingDarkMode ? 'light-content' : 'dark-content'} 
          backgroundColor={theme.headerBackground}
        />
        <View style={[styles.header, { backgroundColor: theme.headerBackground }]}>
          <TouchableOpacity onPress={onBack || (() => router.back())} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={theme.headerText} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.headerText }]}>Profile</Text>
        </View>
        <View style={styles.errorContainer}>
          <Text style={[styles.errorText, { color: theme.text }]}>Profile not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <>
      <StatusBar 
        barStyle={isUsingDarkMode ? 'light-content' : 'dark-content'} 
        backgroundColor={theme.headerBackground}
      />
      <SafeAreaView style={[styles.container, { backgroundColor: theme.background }]}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: theme.headerBackground }]}>
          <TouchableOpacity onPress={onBack || (() => router.back())} style={styles.backButton}>
            <Ionicons name="arrow-back" size={24} color={theme.headerText} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: theme.headerText }]}>Profile</Text>
          <TouchableOpacity style={styles.moreButton}>
            <Ionicons name="ellipsis-vertical" size={24} color={theme.headerText} />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Profile Info */}
          <View style={[styles.profileSection, { backgroundColor: theme.cardBackground }]}>
            <TouchableOpacity 
              onPress={() => setShowAvatarModal(true)}
              style={styles.avatarContainer}
            >
              {profile.avatar ? (
                <Image source={{ uri: profile.avatar }} style={styles.avatar} />
              ) : (
                <View style={[styles.avatarPlaceholder, { backgroundColor: theme.buttonBackground }]}>
                  <Text style={[styles.avatarText, { color: theme.buttonText }]}>
                    {profile.name.charAt(0).toUpperCase()}
                  </Text>
                </View>
              )}
            </TouchableOpacity>
            
            <Text style={[styles.profileName, { color: theme.text }]}>{profile.name}</Text>
            <Text style={[styles.profileStatus, { color: theme.secondaryText }]}>
              {getStatusText()}
            </Text>
            
            {profile.bio && (
              <Text style={[styles.profileBio, { color: theme.secondaryText }]}>
                {profile.bio}
              </Text>
            )}
          </View>

          {/* Action Buttons */}
          <View style={[styles.actionsSection, { backgroundColor: theme.cardBackground }]}>
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: theme.buttonBackground }]}
              onPress={handleStartChat}
            >
              <Ionicons name="chatbubble" size={20} color={theme.buttonText} />
              <Text style={[styles.actionButtonText, { color: theme.buttonText }]}>Message</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: theme.buttonBackground }]}
              onPress={handleCall}
            >
              <Ionicons name="call" size={20} color={theme.buttonText} />
              <Text style={[styles.actionButtonText, { color: theme.buttonText }]}>Call</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.actionButton, { backgroundColor: theme.buttonBackground }]}
              onPress={handleVideoCall}
            >
              <Ionicons name="videocam" size={20} color={theme.buttonText} />
              <Text style={[styles.actionButtonText, { color: theme.buttonText }]}>Video</Text>
            </TouchableOpacity>
          </View>

          {/* Profile Details */}
          <View style={[styles.detailsSection, { backgroundColor: theme.cardBackground }]}>
            {profile.phoneNumber && (
              <View style={styles.detailItem}>
                <Ionicons name="call" size={20} color={theme.secondaryText} />
                <Text style={[styles.detailText, { color: theme.text }]}>{profile.phoneNumber}</Text>
              </View>
            )}
            
            {profile.joinedDate && (
              <View style={styles.detailItem}>
                <Ionicons name="calendar" size={20} color={theme.secondaryText} />
                <Text style={[styles.detailText, { color: theme.text }]}>
                  Joined {profile.joinedDate.toLocaleDateString()}
                </Text>
              </View>
            )}
          </View>

          {/* Shared Media */}
          <View style={[styles.mediaSection, { backgroundColor: theme.cardBackground }]}>
            <Text style={[styles.sectionTitle, { color: theme.text }]}>Shared Media</Text>
            
            <View style={styles.mediaTabs}>
              <TouchableOpacity 
                style={[styles.mediaTab, selectedMediaTab === 'media' && styles.activeMediaTab]}
                onPress={() => setSelectedMediaTab('media')}
              >
                <Text style={[styles.mediaTabText, { color: theme.text }]}>Media</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.mediaTab, selectedMediaTab === 'docs' && styles.activeMediaTab]}
                onPress={() => setSelectedMediaTab('docs')}
              >
                <Text style={[styles.mediaTabText, { color: theme.text }]}>Docs</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.mediaTab, selectedMediaTab === 'links' && styles.activeMediaTab]}
                onPress={() => setSelectedMediaTab('links')}
              >
                <Text style={[styles.mediaTabText, { color: theme.text }]}>Links</Text>
              </TouchableOpacity>
            </View>
            
            <FlatList
              data={profile.sharedMedia.filter(item => 
                selectedMediaTab === 'media' ? ['image', 'video'].includes(item.type) :
                selectedMediaTab === 'docs' ? item.type === 'document' : []
              )}
              renderItem={renderMediaItem}
              numColumns={3}
              scrollEnabled={false}
              style={styles.mediaGrid}
            />
          </View>

          {/* Settings */}
          <View style={[styles.settingsSection, { backgroundColor: theme.cardBackground }]}>
            <TouchableOpacity style={styles.settingItem} onPress={handleMute}>
              <Ionicons 
                name={isMuted ? "volume-mute" : "volume-high"} 
                size={20} 
                color={theme.secondaryText} 
              />
              <Text style={[styles.settingText, { color: theme.text }]}>
                {isMuted ? 'Unmute' : 'Mute'} Notifications
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.settingItem} onPress={handleBlock}>
              <Ionicons 
                name={isBlocked ? "checkmark-circle" : "ban"} 
                size={20} 
                color={isBlocked ? "#4CAF50" : "#F44336"} 
              />
              <Text style={[styles.settingText, { color: isBlocked ? "#4CAF50" : "#F44336" }]}>
                {isBlocked ? 'Unblock' : 'Block'} User
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Avatar Modal */}
        <Modal
          visible={showAvatarModal}
          transparent
          animationType="fade"
          onRequestClose={() => setShowAvatarModal(false)}
        >
          <View style={styles.avatarModalOverlay}>
            <TouchableOpacity 
              style={styles.avatarModalClose}
              onPress={() => setShowAvatarModal(false)}
            >
              <Ionicons name="close" size={30} color="#FFFFFF" />
            </TouchableOpacity>
            
            {profile.avatar ? (
              <Image source={{ uri: profile.avatar }} style={styles.fullScreenAvatar} />
            ) : (
              <View style={[styles.fullScreenAvatarPlaceholder, { backgroundColor: theme.buttonBackground }]}>
                <Text style={[styles.fullScreenAvatarText, { color: theme.buttonText }]}>
                  {profile.name.charAt(0).toUpperCase()}
                </Text>
              </View>
            )}
          </View>
        </Modal>
      </SafeAreaView>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  backButton: {
    marginRight: 16,
    padding: 4,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: '600',
  },
  moreButton: {
    padding: 4,
  },
  content: {
    flex: 1,
  },
  profileSection: {
    alignItems: 'center',
    paddingVertical: 32,
    marginBottom: 16,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: 48,
    fontWeight: '600',
  },
  profileName: {
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 4,
  },
  profileStatus: {
    fontSize: 16,
    marginBottom: 8,
  },
  profileBio: {
    fontSize: 16,
    textAlign: 'center',
    paddingHorizontal: 32,
    lineHeight: 22,
  },
  actionsSection: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    marginBottom: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    minWidth: 100,
    justifyContent: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  detailsSection: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
  },
  detailText: {
    fontSize: 16,
    marginLeft: 16,
  },
  mediaSection: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  mediaTabs: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  mediaTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 16,
    borderRadius: 20,
  },
  activeMediaTab: {
    backgroundColor: 'rgba(74, 144, 226, 0.2)',
  },
  mediaTabText: {
    fontSize: 14,
    fontWeight: '500',
  },
  mediaGrid: {
    marginTop: 8,
  },
  mediaItem: {
    width: (width - 60) / 3,
    height: (width - 60) / 3,
    margin: 2,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mediaThumbnail: {
    width: '100%',
    height: '100%',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -8 }, { translateY: -8 }],
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 8,
    padding: 4,
  },
  settingsSection: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    marginBottom: 32,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
  },
  settingText: {
    fontSize: 16,
    marginLeft: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
  },
  avatarModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarModalClose: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
    padding: 10,
  },
  fullScreenAvatar: {
    width: width * 0.8,
    height: width * 0.8,
    borderRadius: 20,
  },
  fullScreenAvatarPlaceholder: {
    width: width * 0.8,
    height: width * 0.8,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenAvatarText: {
    fontSize: 120,
    fontWeight: '600',
  },
});
