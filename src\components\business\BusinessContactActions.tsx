import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Linking,
  Platform,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const COLORS = {
  primary: '#1DA1F2',
  background: '#FFFFFF',
  text: '#14171A',
  textSecondary: '#657786',
  border: '#E1E8ED',
  surface: '#F7F9FA',
  success: '#17BF63',
  warning: '#FFAD1F',
  error: '#E0245E',
};

interface BusinessContactActionsProps {
  businessName: string;
  phone?: string;
  email?: string;
  whatsapp?: string;
  style?: any;
  size?: 'small' | 'medium' | 'large';
}

export const BusinessContactActions: React.FC<BusinessContactActionsProps> = ({
  businessName,
  phone,
  email,
  whatsapp,
  style,
  size = 'medium',
}) => {
  const handlePhoneCall = async () => {
    if (!phone) {
      Alert.alert('No Phone Number', 'This business has not provided a phone number.');
      return;
    }

    const phoneNumber = phone.replace(/[^\d+]/g, ''); // Clean phone number
    const phoneUrl = `tel:${phoneNumber}`;

    try {
      const supported = await Linking.canOpenURL(phoneUrl);
      if (supported) {
        Alert.alert(
          'Call Business',
          `Do you want to call ${businessName}?\n\n${phone}`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Call Now',
              onPress: () => Linking.openURL(phoneUrl),
            },
          ]
        );
      } else {
        Alert.alert('Error', 'Phone calls are not supported on this device.');
      }
    } catch (error) {
      console.error('❌ Error making phone call:', error);
      Alert.alert('Error', 'Failed to make phone call.');
    }
  };

  const handleWhatsApp = async () => {
    const whatsappNumber = whatsapp || phone;
    if (!whatsappNumber) {
      Alert.alert('No WhatsApp Number', 'This business has not provided a WhatsApp number.');
      return;
    }

    // Clean and format WhatsApp number
    let cleanNumber = whatsappNumber.replace(/[^\d]/g, '');
    
    // Add country code if not present (assuming Uganda +256)
    if (!cleanNumber.startsWith('256') && cleanNumber.startsWith('0')) {
      cleanNumber = '256' + cleanNumber.substring(1);
    } else if (!cleanNumber.startsWith('256') && !cleanNumber.startsWith('+')) {
      cleanNumber = '256' + cleanNumber;
    }

    const message = `Hello ${businessName}, I'm interested in your product on IraChat.`;
    const whatsappUrl = `whatsapp://send?phone=${cleanNumber}&text=${encodeURIComponent(message)}`;
    const whatsappWebUrl = `https://wa.me/${cleanNumber}?text=${encodeURIComponent(message)}`;

    try {
      const supported = await Linking.canOpenURL(whatsappUrl);
      if (supported) {
        await Linking.openURL(whatsappUrl);
      } else {
        // Fallback to WhatsApp Web
        await Linking.openURL(whatsappWebUrl);
      }
    } catch (error) {
      console.error('❌ Error opening WhatsApp:', error);
      Alert.alert('Error', 'Failed to open WhatsApp. Please make sure WhatsApp is installed.');
    }
  };

  const handleEmail = async () => {
    if (!email) {
      Alert.alert('No Email', 'This business has not provided an email address.');
      return;
    }

    const subject = `Inquiry about your product - ${businessName}`;
    const body = `Hello ${businessName},\n\nI'm interested in your product listed on IraChat.\n\nBest regards`;
    const emailUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

    try {
      const supported = await Linking.canOpenURL(emailUrl);
      if (supported) {
        await Linking.openURL(emailUrl);
      } else {
        Alert.alert('Error', 'Email is not supported on this device.');
      }
    } catch (error) {
      console.error('❌ Error opening email:', error);
      Alert.alert('Error', 'Failed to open email client.');
    }
  };

  const handleSMS = async () => {
    if (!phone) {
      Alert.alert('No Phone Number', 'This business has not provided a phone number.');
      return;
    }

    const phoneNumber = phone.replace(/[^\d+]/g, '');
    const message = `Hello ${businessName}, I'm interested in your product on IraChat.`;
    
    let smsUrl: string;
    if (Platform.OS === 'ios') {
      smsUrl = `sms:${phoneNumber}&body=${encodeURIComponent(message)}`;
    } else {
      smsUrl = `sms:${phoneNumber}?body=${encodeURIComponent(message)}`;
    }

    try {
      const supported = await Linking.canOpenURL(smsUrl);
      if (supported) {
        await Linking.openURL(smsUrl);
      } else {
        Alert.alert('Error', 'SMS is not supported on this device.');
      }
    } catch (error) {
      console.error('❌ Error opening SMS:', error);
      Alert.alert('Error', 'Failed to open SMS.');
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small':
        return { width: 24, height: 24, iconSize: 12 }; // Further reduced to fit all buttons
      case 'large':
        return { width: 40, height: 40, iconSize: 20 }; // Further reduced to fit all buttons
      default:
        return { width: 32, height: 32, iconSize: 16 }; // Further reduced to fit all buttons
    }
  };

  const buttonSize = getButtonSize();

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={[styles.scrollContainer, style]}
      contentContainerStyle={styles.container}
    >
      {/* Phone Call */}
      {phone && (
        <TouchableOpacity
          style={[styles.actionButton, styles.phoneButton, { width: buttonSize.width, height: buttonSize.height }]}
          onPress={handlePhoneCall}
        >
          <Ionicons name="call" size={buttonSize.iconSize} color="white" />
        </TouchableOpacity>
      )}

      {/* WhatsApp */}
      {(whatsapp || phone) && (
        <TouchableOpacity
          style={[styles.actionButton, styles.whatsappButton, { width: buttonSize.width, height: buttonSize.height }]}
          onPress={handleWhatsApp}
        >
          <Ionicons name="logo-whatsapp" size={buttonSize.iconSize} color="white" />
        </TouchableOpacity>
      )}

      {/* SMS */}
      {phone && (
        <TouchableOpacity
          style={[styles.actionButton, styles.smsButton, { width: buttonSize.width, height: buttonSize.height }]}
          onPress={handleSMS}
        >
          <Ionicons name="chatbubble" size={buttonSize.iconSize} color="white" />
        </TouchableOpacity>
      )}

      {/* Email */}
      {email && (
        <TouchableOpacity
          style={[styles.actionButton, styles.emailButton, { width: buttonSize.width, height: buttonSize.height }]}
          onPress={handleEmail}
        >
          <Ionicons name="mail" size={buttonSize.iconSize} color="white" />
        </TouchableOpacity>
      )}
    </ScrollView>
  );
};

// Compact version for inline use
export const BusinessContactButton: React.FC<BusinessContactActionsProps & { 
  type: 'phone' | 'whatsapp' | 'email' | 'sms' 
}> = ({
  businessName,
  phone,
  email,
  whatsapp,
  type,
  style,
}) => {
  const contactActions = {
    phone: phone,
    whatsapp: whatsapp || phone,
    email: email,
    sms: phone,
  };

  if (!contactActions[type]) {
    return null;
  }

  const buttonConfig = {
    phone: { icon: 'call', color: '#17BF63', label: 'Call' },
    whatsapp: { icon: 'logo-whatsapp', color: '#25D366', label: 'WhatsApp' },
    email: { icon: 'mail', color: '#1DA1F2', label: 'Email' },
    sms: { icon: 'chatbubble', color: '#FFAD1F', label: 'SMS' },
  };

  const config = buttonConfig[type];

  const handlePress = async () => {
    switch (type) {
      case 'phone':
        await handlePhoneCall();
        break;
      case 'whatsapp':
        await handleWhatsApp();
        break;
      case 'email':
        await handleEmail();
        break;
      case 'sms':
        await handleSMS();
        break;
    }
  };

  const handlePhoneCall = async () => {
    if (!phone) {
      Alert.alert('No Phone Number', 'This business has not provided a phone number.');
      return;
    }

    const phoneNumber = phone.replace(/[^\d+]/g, ''); // Clean phone number
    const phoneUrl = `tel:${phoneNumber}`;

    try {
      const supported = await Linking.canOpenURL(phoneUrl);
      if (supported) {
        Alert.alert(
          'Call Business',
          `Do you want to call ${businessName}?\n\n${phone}`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Call Now',
              onPress: () => Linking.openURL(phoneUrl),
            },
          ]
        );
      } else {
        Alert.alert('Error', 'Phone calls are not supported on this device.');
      }
    } catch (error) {
      console.error('❌ Error making phone call:', error);
      Alert.alert('Error', 'Failed to make phone call.');
    }
  };

  const handleWhatsApp = async () => {
    const whatsappNumber = whatsapp || phone;
    if (!whatsappNumber) {
      Alert.alert('No WhatsApp Number', 'This business has not provided a WhatsApp number.');
      return;
    }

    // Clean and format WhatsApp number
    let cleanNumber = whatsappNumber.replace(/[^\d]/g, '');

    // Add country code if not present (assuming Uganda +256)
    if (!cleanNumber.startsWith('256') && cleanNumber.startsWith('0')) {
      cleanNumber = '256' + cleanNumber.substring(1);
    } else if (!cleanNumber.startsWith('256') && !cleanNumber.startsWith('+')) {
      cleanNumber = '256' + cleanNumber;
    }

    const message = `Hello ${businessName}, I'm interested in your product on IraChat.`;
    const whatsappUrl = `whatsapp://send?phone=${cleanNumber}&text=${encodeURIComponent(message)}`;
    const whatsappWebUrl = `https://wa.me/${cleanNumber}?text=${encodeURIComponent(message)}`;

    try {
      const supported = await Linking.canOpenURL(whatsappUrl);
      if (supported) {
        await Linking.openURL(whatsappUrl);
      } else {
        // Fallback to WhatsApp Web
        await Linking.openURL(whatsappWebUrl);
      }
    } catch (error) {
      console.error('❌ Error opening WhatsApp:', error);
      Alert.alert('Error', 'Failed to open WhatsApp. Please make sure WhatsApp is installed.');
    }
  };

  const handleEmail = async () => {
    if (!email) {
      Alert.alert('No Email', 'This business has not provided an email address.');
      return;
    }

    const subject = `Inquiry about your product - ${businessName}`;
    const body = `Hello ${businessName},\n\nI'm interested in your product listed on IraChat.\n\nBest regards`;
    const emailUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

    try {
      const supported = await Linking.canOpenURL(emailUrl);
      if (supported) {
        await Linking.openURL(emailUrl);
      } else {
        Alert.alert('Error', 'Email is not supported on this device.');
      }
    } catch (error) {
      console.error('❌ Error opening email:', error);
      Alert.alert('Error', 'Failed to open email client.');
    }
  };

  const handleSMS = async () => {
    if (!phone) {
      Alert.alert('No Phone Number', 'This business has not provided a phone number.');
      return;
    }

    const phoneNumber = phone.replace(/[^\d+]/g, '');
    const message = `Hello ${businessName}, I'm interested in your product on IraChat.`;

    let smsUrl: string;
    if (Platform.OS === 'ios') {
      smsUrl = `sms:${phoneNumber}&body=${encodeURIComponent(message)}`;
    } else {
      smsUrl = `sms:${phoneNumber}?body=${encodeURIComponent(message)}`;
    }

    try {
      const supported = await Linking.canOpenURL(smsUrl);
      if (supported) {
        await Linking.openURL(smsUrl);
      } else {
        Alert.alert('Error', 'SMS is not supported on this device.');
      }
    } catch (error) {
      console.error('❌ Error opening SMS:', error);
      Alert.alert('Error', 'Failed to open SMS.');
    }
  };

  return (
    <TouchableOpacity
      style={[styles.compactButton, { backgroundColor: config.color }, style]}
      onPress={handlePress}
    >
      <Ionicons name={config.icon as any} size={16} color="white" />
      <Text style={styles.compactButtonText}>{config.label}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 0, // Don't grow beyond content
    maxHeight: 50, // Limit height to button size
  },
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8, // Restore normal gap since we have horizontal scrolling
    paddingHorizontal: 4, // Add some padding for better scrolling
  },
  actionButton: {
    borderRadius: 20, // Restore normal border radius
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    // Remove flex properties since we're using ScrollView
    minWidth: 40, // Restore minimum width for better touch targets
    minHeight: 40, // Restore minimum height for better touch targets
  },
  phoneButton: {
    backgroundColor: '#17BF63',
  },
  whatsappButton: {
    backgroundColor: '#25D366',
  },
  smsButton: {
    backgroundColor: '#FFAD1F',
  },
  emailButton: {
    backgroundColor: '#1DA1F2',
  },
  compactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  compactButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});
