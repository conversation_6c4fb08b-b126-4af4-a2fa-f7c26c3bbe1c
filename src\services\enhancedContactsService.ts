/**
 * Enhanced Contacts Service
 * Loads contacts from both device contacts and Firestore users
 * Provides unified contact management with lazy loading support
 */

import * as Contacts from "expo-contacts";
import { collection, getDocs, query, where, limit, startAfter, orderBy, DocumentSnapshot } from "firebase/firestore";
import { Platform } from "react-native";
import { db } from "./firebaseSimple";
import { User } from "../types";
import { normalizePhoneNumber } from "../utils/phoneNumberUtils";
import contactCacheService from "./contactCacheService";

export interface EnhancedContact {
  id: string;
  name: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  primaryEmail?: string;
  company?: string;
  jobTitle?: string;
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: Date | string;
  userId?: string;
  isIraChatUser: boolean;
  profilePhotoVisible?: boolean;
  initials?: string;
  source: 'device' | 'firestore' | 'manual';
  bio?: string;
  status?: string;
  username?: string;
}

class EnhancedContactsService {
  private deviceContactsCache: EnhancedContact[] | null = null;
  private firestoreUsersCache: EnhancedContact[] | null = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private isLoading = false;
  private hasContactsPermission = false;

  // Pagination for Firestore users
  private lastFirestoreDoc: DocumentSnapshot | null = null;
  private hasMoreFirestoreUsers = true;
  private readonly FIRESTORE_BATCH_SIZE = 50;

  /**
   * Request contacts permission
   */
  async requestContactsPermission(): Promise<boolean> {
    try {
      if (Platform.OS === "web") {
        console.log("🌐 Web platform - contacts not available");
        return false;
      }

      const { status } = await Contacts.requestPermissionsAsync();
      this.hasContactsPermission = status === "granted";
      console.log(`📱 Contacts permission: ${this.hasContactsPermission ? 'granted' : 'denied'}`);
      return this.hasContactsPermission;
    } catch (error) {
      console.error("❌ Error requesting contacts permission:", error);
      return false;
    }
  }

  /**
   * Load device contacts
   */
  async loadDeviceContacts(): Promise<EnhancedContact[]> {
    try {
      if (!this.hasContactsPermission) {
        const granted = await this.requestContactsPermission();
        if (!granted) {
          console.log("📱 No contacts permission - skipping device contacts");
          return [];
        }
      }

      console.log("📱 Loading device contacts...");
      const { data } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.ID,
          Contacts.Fields.Name,
          Contacts.Fields.FirstName,
          Contacts.Fields.LastName,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
          Contacts.Fields.Image,
          Contacts.Fields.ImageAvailable,
          Contacts.Fields.Company,
          Contacts.Fields.JobTitle,
        ],
        sort: Contacts.SortTypes.FirstName,
      });

      console.log(`📱 Raw device contacts fetched: ${data.length}`);

      const processedContacts = data
        .map((contact) => {
          try {
            // Extract name information
            const firstName = contact.firstName || undefined;
            const lastName = contact.lastName || undefined;
            const fullName = contact.name || `${contact.firstName || ''} ${contact.lastName || ''}`.trim() || 'Unknown Contact';

            // Extract primary phone number
            const primaryPhone = contact.phoneNumbers?.[0]?.number?.replace(/[^\d+]/g, '') || '';
            
            // Extract primary email
            const primaryEmail = contact.emails?.[0]?.email || '';

            // Skip contacts without phone or email
            if (!primaryPhone && !primaryEmail) {
              return null;
            }

            return {
              id: contact.id || `device_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
              name: fullName,
              firstName,
              lastName,
              phoneNumber: primaryPhone || undefined,
              primaryEmail: primaryEmail || undefined,
              company: contact.company || undefined,
              jobTitle: contact.jobTitle || undefined,
              avatar: contact.imageAvailable ? contact.image?.uri : undefined,
              isIraChatUser: false, // Will be updated when cross-referencing with Firestore
              source: 'device' as const,
              initials: fullName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2),
            } as EnhancedContact;
          } catch (error) {
            console.error('Error processing device contact:', error);
            return null;
          }
        })
        .filter((contact): contact is EnhancedContact => contact !== null);

      console.log(`📱 Processed device contacts: ${processedContacts.length}`);
      this.deviceContactsCache = processedContacts;
      return processedContacts;
    } catch (error) {
      console.error("❌ Error loading device contacts:", error);
      return [];
    }
  }

  /**
   * 🚫 PRIVACY PROTECTION: Global user fetching DISABLED
   *
   * ISSUE: This method was fetching ALL IraChat users globally (1M+ users),
   * which is a massive privacy violation and performance issue.
   *
   * PROPER BEHAVIOR: Only show contacts that are:
   * 1. In the user's phone contacts AND
   * 2. Registered on IraChat
   *
   * This prevents users from seeing random strangers and protects privacy.
   */
  async loadFirestoreUsers(loadMore = false): Promise<EnhancedContact[]> {
    console.log('🚫 Global user fetching DISABLED for privacy protection');
    console.log('✅ Only showing mutual contacts (phone contacts + IraChat users)');

    // Return empty array - we only show mutual contacts now
    this.firestoreUsersCache = [];
    this.hasMoreFirestoreUsers = false;
    return [];
  }

  /**
   * Smart cross-reference using cache and only checking new contacts
   */
  private async smartCrossReferenceContacts(deviceContacts: EnhancedContact[]): Promise<EnhancedContact[]> {
    console.log("🔄 Smart cross-referencing device contacts with cached data...");

    // Detect contact changes
    const changes = await contactCacheService.detectContactChanges(deviceContacts);

    // Get cached IraChat contacts
    const cachedIraChatContacts = await contactCacheService.getCachedIraChatContacts();

    // Start with device contacts
    let processedContacts = [...deviceContacts];

    // Apply cached IraChat data to existing contacts
    processedContacts = processedContacts.map(contact => {
      // Check if this contact is cached as IraChat user
      const cachedData = cachedIraChatContacts.find(cached =>
        (contact.phoneNumber && cached.phoneNumber === contact.phoneNumber) ||
        (contact.primaryEmail && cached.primaryEmail === contact.primaryEmail)
      );

      if (cachedData) {
        console.log(`📦 Using cached IraChat data for: ${contact.name}`);
        return {
          ...contact,
          isIraChatUser: true,
          userId: cachedData.userId,
          avatar: cachedData.avatar || contact.avatar,
          isOnline: cachedData.isOnline,
          lastSeen: cachedData.lastSeen,
          bio: cachedData.bio,
          status: cachedData.status,
          username: cachedData.username,
        };
      }

      return contact;
    });

    // Only check new/modified contacts against Firestore
    const contactsToCheck = [...changes.newContacts, ...changes.modifiedContacts];

    if (contactsToCheck.length > 0) {
      console.log(`🔍 Checking ${contactsToCheck.length} new/modified contacts against Firestore...`);

      // Check these contacts against Firestore
      const checkedContacts = await this.checkContactsAgainstFirestore(contactsToCheck);

      // Update the processed contacts with Firestore results
      checkedContacts.forEach(checkedContact => {
        const index = processedContacts.findIndex(c => c.id === checkedContact.id);
        if (index !== -1) {
          processedContacts[index] = checkedContact;
        }
      });
    }

    // Add cached Firestore-only users that aren't in device contacts
    const devicePhones = new Set(deviceContacts.map(c => c.phoneNumber).filter(Boolean));
    const deviceEmails = new Set(deviceContacts.map(c => c.primaryEmail).filter((e): e is string => Boolean(e)).map(e => e.toLowerCase()));

    const firestoreOnlyUsers = cachedIraChatContacts.filter(cached => {
      const hasPhoneMatch = cached.phoneNumber && devicePhones.has(cached.phoneNumber);
      const hasEmailMatch = cached.primaryEmail && deviceEmails.has(cached.primaryEmail.toLowerCase());
      return !hasPhoneMatch && !hasEmailMatch && cached.source === 'firestore';
    });

    const allContacts = [...processedContacts, ...firestoreOnlyUsers];

    // Update device contacts hash in cache
    await contactCacheService.updateDeviceContactsHash(deviceContacts);

    console.log(`🔄 Smart cross-reference complete: ${allContacts.filter(c => c.isIraChatUser).length} IraChat users, ${contactsToCheck.length} newly checked`);

    return allContacts;
  }

  /**
   * Check specific contacts against Firestore and cache results
   */
  private async checkContactsAgainstFirestore(contacts: EnhancedContact[]): Promise<EnhancedContact[]> {
    if (contacts.length === 0) return [];

    console.log(`🔍 Checking ${contacts.length} contacts against Firestore...`);

    try {
      // Build query for phone numbers and emails
      const phoneNumbers: string[] = [];
      const emails: string[] = [];

      contacts.forEach(contact => {
        if (contact.phoneNumber) {
          const normalized = normalizePhoneNumber(contact.phoneNumber);
          phoneNumbers.push(...normalized);
        }
        if (contact.primaryEmail) {
          emails.push(contact.primaryEmail.toLowerCase());
        }
      });

      // Query Firestore for matching users
      const firestoreUsers: EnhancedContact[] = [];

      if (phoneNumbers.length > 0) {
        const phoneQuery = query(
          collection(db, 'users'),
          where('phoneNumber', 'in', phoneNumbers.slice(0, 10)) // Firestore limit
        );
        const phoneSnapshot = await getDocs(phoneQuery);

        phoneSnapshot.docs.forEach(doc => {
          const userData = doc.data() as User;
          firestoreUsers.push({
            id: doc.id,
            name: userData.name || userData.displayName || 'IraChat User',
            firstName: userData.name?.split(' ')[0] || '',
            lastName: userData.name?.split(' ').slice(1).join(' ') || '',
            phoneNumber: userData.phoneNumber,
            primaryEmail: userData.email,
            avatar: userData.avatar || userData.photoURL,
            isOnline: userData.isOnline,
            lastSeen: userData.lastSeen ? new Date(userData.lastSeen) : undefined,
            userId: doc.id,
            isIraChatUser: true,
            source: 'firestore' as const,
            bio: userData.bio,
            status: userData.status,
            username: userData.username,
            initials: (userData.name || userData.displayName || 'IU').split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2),
          });
        });
      }

      if (emails.length > 0) {
        const emailQuery = query(
          collection(db, 'users'),
          where('email', 'in', emails.slice(0, 10)) // Firestore limit
        );
        const emailSnapshot = await getDocs(emailQuery);

        emailSnapshot.docs.forEach(doc => {
          const userData = doc.data() as User;
          // Avoid duplicates
          if (!firestoreUsers.find(u => u.id === doc.id)) {
            firestoreUsers.push({
              id: doc.id,
              name: userData.name || userData.displayName || 'IraChat User',
              firstName: userData.name?.split(' ')[0] || '',
              lastName: userData.name?.split(' ').slice(1).join(' ') || '',
              phoneNumber: userData.phoneNumber,
              primaryEmail: userData.email,
              avatar: userData.avatar || userData.photoURL,
              isOnline: userData.isOnline,
              lastSeen: userData.lastSeen ? new Date(userData.lastSeen) : undefined,
              userId: doc.id,
              isIraChatUser: true,
              source: 'firestore' as const,
              bio: userData.bio,
              status: userData.status,
              username: userData.username,
              initials: (userData.name || userData.displayName || 'IU').split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2),
            });
          }
        });
      }

      // Create lookup maps
      const phoneToFirestoreUser = new Map<string, EnhancedContact>();
      const emailToFirestoreUser = new Map<string, EnhancedContact>();

      firestoreUsers.forEach(user => {
        if (user.phoneNumber) {
          const normalizedPhones = normalizePhoneNumber(user.phoneNumber);
          normalizedPhones.forEach(phone => {
            phoneToFirestoreUser.set(phone, user);
          });
        }
        if (user.primaryEmail) {
          emailToFirestoreUser.set(user.primaryEmail.toLowerCase(), user);
        }
      });

      // Update contacts with Firestore data and cache results
      const updatedContacts = await Promise.all(contacts.map(async contact => {
        let firestoreMatch: EnhancedContact | undefined;

        // Check phone number match
        if (contact.phoneNumber) {
          const normalizedPhones = normalizePhoneNumber(contact.phoneNumber);
          for (const phone of normalizedPhones) {
            firestoreMatch = phoneToFirestoreUser.get(phone);
            if (firestoreMatch) break;
          }
        }

        // Check email match if no phone match
        if (!firestoreMatch && contact.primaryEmail) {
          firestoreMatch = emailToFirestoreUser.get(contact.primaryEmail.toLowerCase());
        }

        if (firestoreMatch) {
          const updatedContact = {
            ...contact,
            isIraChatUser: true,
            userId: firestoreMatch.userId,
            avatar: firestoreMatch.avatar || contact.avatar,
            isOnline: firestoreMatch.isOnline,
            lastSeen: firestoreMatch.lastSeen,
            bio: firestoreMatch.bio,
            status: firestoreMatch.status,
            username: firestoreMatch.username,
          };

          // Cache as IraChat user
          await contactCacheService.cacheIraChatContact(updatedContact, firestoreMatch.id);

          return updatedContact;
        } else {
          // Cache as non-IraChat user
          await contactCacheService.cacheNonIraChatContact(contact);
          return contact;
        }
      }));

      console.log(`🔍 Firestore check complete: ${updatedContacts.filter(c => c.isIraChatUser).length}/${contacts.length} are IraChat users`);

      return updatedContacts;
    } catch (error) {
      console.error('❌ Error checking contacts against Firestore:', error);

      // Cache all as non-IraChat users on error
      await Promise.all(contacts.map(contact =>
        contactCacheService.cacheNonIraChatContact(contact)
      ));

      return contacts;
    }
  }

  /**
   * Get all contacts with smart caching (device + cached Firestore + new checks)
   */
  async getAllContacts(): Promise<EnhancedContact[]> {
    try {
      // Initialize cache service
      await contactCacheService.initialize();

      if (this.isLoading) {
        console.log("⏳ Already loading contacts...");
        await new Promise(resolve => setTimeout(resolve, 100));
        return this.deviceContactsCache || [];
      }

      this.isLoading = true;

      // Load device contacts
      const deviceContacts = await this.loadDeviceContacts();

      // Use smart cross-referencing with cache
      const allContacts = await this.smartCrossReferenceContacts(deviceContacts);

      // 🚫 DISABLED: Periodic Firestore sync for privacy protection
      // This was fetching all global users which violates privacy
      console.log("🚫 Global Firestore sync DISABLED for privacy protection");
      console.log("✅ Using phone contact matching only");

      // Clean up expired cache entries
      await contactCacheService.clearExpiredEntries();

      // Sort: IraChat users first, then alphabetically
      const sortedContacts = allContacts.sort((a, b) => {
        if (a.isIraChatUser && !b.isIraChatUser) return -1;
        if (!a.isIraChatUser && b.isIraChatUser) return 1;
        return a.name.localeCompare(b.name);
      });

      this.cacheTimestamp = Date.now();
      this.isLoading = false;

      // Log cache statistics
      const cacheStats = await contactCacheService.getCacheStats();
      console.log(`✅ Smart contacts loaded: ${sortedContacts.length} total (${sortedContacts.filter(c => c.isIraChatUser).length} IraChat users)`);
      console.log(`📦 Cache stats: ${cacheStats.totalContacts} cached, ${cacheStats.iraChatContacts} IraChat, ${cacheStats.expiredContacts} expired`);

      return sortedContacts;
    } catch (error) {
      console.error("❌ Error loading all contacts:", error);
      this.isLoading = false;
      return [];
    }
  }

  /**
   * Load more Firestore users (for pagination)
   */
  async loadMoreFirestoreUsers(): Promise<EnhancedContact[]> {
    return this.loadFirestoreUsers(true);
  }

  /**
   * Check if cache is valid
   */
  private isCacheValid(): boolean {
    return (Date.now() - this.cacheTimestamp) < this.CACHE_DURATION;
  }

  /**
   * Clear cache and force refresh
   */
  async refreshContacts(): Promise<EnhancedContact[]> {
    console.log("🔄 Force refreshing contacts and clearing cache...");

    // Clear local service cache
    this.deviceContactsCache = null;
    this.firestoreUsersCache = null;
    this.cacheTimestamp = 0;
    this.lastFirestoreDoc = null;
    this.hasMoreFirestoreUsers = true;

    // Clear persistent cache
    await contactCacheService.clearCache();

    return this.getAllContacts();
  }

  /**
   * Search contacts
   */
  async searchContacts(query: string): Promise<EnhancedContact[]> {
    const contacts = await this.getAllContacts();
    const searchTerm = query.toLowerCase();

    return contacts.filter(contact =>
      contact.name.toLowerCase().includes(searchTerm) ||
      contact.phoneNumber?.includes(query) ||
      contact.primaryEmail?.toLowerCase().includes(searchTerm) ||
      contact.username?.toLowerCase().includes(searchTerm) ||
      contact.company?.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Get only IraChat users
   */
  async getIraChatUsers(): Promise<EnhancedContact[]> {
    const contacts = await this.getAllContacts();
    return contacts.filter(contact => contact.isIraChatUser);
  }

  /**
   * Get contact by ID
   */
  async getContactById(id: string): Promise<EnhancedContact | null> {
    const contacts = await this.getAllContacts();
    return contacts.find(contact => contact.id === id) || null;
  }

  /**
   * Manually check a specific contact against Firestore
   * Useful when user adds a new contact and wants to check if they're on IraChat
   */
  async checkSingleContactAgainstFirestore(contact: EnhancedContact): Promise<EnhancedContact> {
    console.log(`🔍 Manually checking contact ${contact.name} against Firestore...`);

    // First check cache
    const cached = await contactCacheService.getCachedContact(contact.phoneNumber, contact.primaryEmail);
    if (cached) {
      console.log(`📦 Found cached data for ${contact.name}`);
      return cached.contact;
    }

    // Check against Firestore
    const [checkedContact] = await this.checkContactsAgainstFirestore([contact]);
    return checkedContact;
  }

  /**
   * Force sync a specific contact with Firestore (bypass cache)
   */
  async forceSyncContact(contact: EnhancedContact): Promise<EnhancedContact> {
    console.log(`🔄 Force syncing contact ${contact.name} with Firestore...`);

    const [syncedContact] = await this.checkContactsAgainstFirestore([contact]);
    return syncedContact;
  }

  /**
   * Get cache statistics for debugging
   */
  async getCacheStatistics(): Promise<any> {
    return await contactCacheService.getCacheStats();
  }

  /**
   * Background sync to keep IraChat contacts up to date
   * This runs periodically to sync cached contacts with latest Firestore data
   */
  async backgroundSync(): Promise<void> {
    try {
      console.log("🔄 Starting background sync...");

      // Get cached IraChat contacts
      const cachedIraChatContacts = await contactCacheService.getCachedIraChatContacts();

      if (cachedIraChatContacts.length === 0) {
        console.log("📦 No cached IraChat contacts to sync");
        return;
      }

      // Get fresh data for cached IraChat users
      const userIds = cachedIraChatContacts
        .map(c => c.userId)
        .filter(Boolean) as string[];

      if (userIds.length > 0) {
        // Query Firestore for updated user data
        const updatedUsers: EnhancedContact[] = [];

        // Process in batches of 10 (Firestore limit)
        for (let i = 0; i < userIds.length; i += 10) {
          const batch = userIds.slice(i, i + 10);

          try {
            const userQuery = query(
              collection(db, 'users'),
              where('__name__', 'in', batch)
            );
            const snapshot = await getDocs(userQuery);

            snapshot.docs.forEach(doc => {
              const userData = doc.data() as User;
              updatedUsers.push({
                id: doc.id,
                name: userData.name || userData.displayName || 'IraChat User',
                firstName: userData.name?.split(' ')[0] || '',
                lastName: userData.name?.split(' ').slice(1).join(' ') || '',
                phoneNumber: userData.phoneNumber,
                primaryEmail: userData.email,
                avatar: userData.avatar || userData.photoURL,
                isOnline: userData.isOnline,
                lastSeen: userData.lastSeen ? new Date(userData.lastSeen) : undefined,
                userId: doc.id,
                isIraChatUser: true,
                source: 'firestore' as const,
                bio: userData.bio,
                status: userData.status,
                username: userData.username,
                initials: (userData.name || userData.displayName || 'IU').split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2),
              });
            });
          } catch (batchError) {
            console.warn(`⚠️ Error syncing batch ${i}-${i + 10}:`, batchError);
          }
        }

        // Update cache with fresh data
        for (const updatedUser of updatedUsers) {
          await contactCacheService.cacheIraChatContact(updatedUser, updatedUser.id);
        }

        console.log(`✅ Background sync completed: updated ${updatedUsers.length}/${cachedIraChatContacts.length} IraChat contacts`);
      }

      await contactCacheService.updateLastFullSync();
    } catch (error) {
      console.error("❌ Background sync failed:", error);
    }
  }

  /**
   * Initialize the service and start background processes
   */
  async initialize(): Promise<void> {
    try {
      await contactCacheService.initialize();

      // Start background sync if needed
      const needsSync = await contactCacheService.needsFullSync();
      if (needsSync) {
        // Run background sync without blocking
        this.backgroundSync().catch(error =>
          console.warn("⚠️ Background sync failed during initialization:", error)
        );
      }

      console.log("✅ Enhanced contacts service initialized");
    } catch (error) {
      console.error("❌ Failed to initialize enhanced contacts service:", error);
    }
  }

  /**
   * Clean up expired cache and optimize storage
   */
  async optimizeCache(): Promise<void> {
    try {
      console.log("🧹 Optimizing contact cache...");

      await contactCacheService.clearExpiredEntries();

      const stats = await contactCacheService.getCacheStats();
      console.log(`✅ Cache optimized: ${stats.totalContacts} contacts, ${stats.expiredContacts} expired entries removed`);
    } catch (error) {
      console.error("❌ Cache optimization failed:", error);
    }
  }
}

// Create and export service instance
export const enhancedContactsService = new EnhancedContactsService();
export default enhancedContactsService;
