/**
 * Document Service
 * Handles document storage and retrieval from documents collection
 */

import { db } from './firebase';
import { 
  doc, 
  setDoc, 
  getDoc, 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  serverTimestamp,
  updateDoc
} from 'firebase/firestore';

export interface DocumentFile {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  mimeType: string;
  downloadUrl: string;
  thumbnailUrl?: string; // For PDF previews, etc.
  isDownloaded: boolean;
  downloadedAt?: Date;
  uploadProgress?: number;
  isUploaded: boolean;
  uploadedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

class DocumentService {
  // Save document to documents collection
  async saveDocument(documentData: Omit<DocumentFile, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const documentRef = doc(collection(db, 'documents'));
      const document: DocumentFile = {
        ...documentData,
        id: documentRef.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await setDoc(documentRef, {
        ...document,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Document saved to documents collection');
      return documentRef.id;
    } catch (error) {
      console.error('❌ Failed to save document:', error);
      throw error;
    }
  }

  // Get document by ID
  async getDocument(documentId: string): Promise<DocumentFile | null> {
    try {
      const documentDoc = await getDoc(doc(db, 'documents', documentId));
      
      if (!documentDoc.exists()) {
        return null;
      }
      
      const data = documentDoc.data();
      return {
        ...data,
        id: documentDoc.id,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        downloadedAt: data.downloadedAt?.toDate(),
        uploadedAt: data.uploadedAt?.toDate(),
      } as DocumentFile;
    } catch (error) {
      console.error('Failed to get document:', error);
      return null;
    }
  }

  // Get documents for a chat
  async getDocumentsForChat(chatId: string, limitCount: number = 50): Promise<DocumentFile[]> {
    try {
      const documentsRef = collection(db, 'documents');
      const documentsQuery = query(
        documentsRef,
        where('chatId', '==', chatId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const snapshot = await getDocs(documentsQuery);
      const documents: DocumentFile[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        documents.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          downloadedAt: data.downloadedAt?.toDate(),
          uploadedAt: data.uploadedAt?.toDate(),
        } as DocumentFile);
      });
      
      return documents;
    } catch (error) {
      console.error('Failed to get documents for chat:', error);
      return [];
    }
  }

  // Mark document as downloaded
  async markDocumentAsDownloaded(documentId: string): Promise<void> {
    try {
      const documentRef = doc(db, 'documents', documentId);
      await updateDoc(documentRef, {
        isDownloaded: true,
        downloadedAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      
      console.log('✅ Document marked as downloaded');
    } catch (error) {
      console.error('Failed to mark document as downloaded:', error);
      throw error;
    }
  }

  // Update document upload progress
  async updateDocumentUploadProgress(documentId: string, progress: number): Promise<void> {
    try {
      const documentRef = doc(db, 'documents', documentId);
      await updateDoc(documentRef, {
        uploadProgress: progress,
        updatedAt: serverTimestamp(),
      });
      
      if (progress >= 100) {
        await updateDoc(documentRef, {
          isUploaded: true,
          uploadedAt: serverTimestamp(),
        });
      }
    } catch (error) {
      console.error('Failed to update document upload progress:', error);
      throw error;
    }
  }

  // Get documents by sender
  async getDocumentsBySender(senderId: string, limitCount: number = 50): Promise<DocumentFile[]> {
    try {
      const documentsRef = collection(db, 'documents');
      const documentsQuery = query(
        documentsRef,
        where('senderId', '==', senderId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const snapshot = await getDocs(documentsQuery);
      const documents: DocumentFile[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        documents.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          downloadedAt: data.downloadedAt?.toDate(),
          uploadedAt: data.uploadedAt?.toDate(),
        } as DocumentFile);
      });
      
      return documents;
    } catch (error) {
      console.error('Failed to get documents by sender:', error);
      return [];
    }
  }

  // Get documents by file type
  async getDocumentsByType(fileType: string, limitCount: number = 50): Promise<DocumentFile[]> {
    try {
      const documentsRef = collection(db, 'documents');
      const documentsQuery = query(
        documentsRef,
        where('fileType', '==', fileType),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const snapshot = await getDocs(documentsQuery);
      const documents: DocumentFile[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        documents.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          downloadedAt: data.downloadedAt?.toDate(),
          uploadedAt: data.uploadedAt?.toDate(),
        } as DocumentFile);
      });
      
      return documents;
    } catch (error) {
      console.error('Failed to get documents by type:', error);
      return [];
    }
  }

  // Delete document
  async deleteDocument(documentId: string): Promise<void> {
    try {
      const documentRef = doc(db, 'documents', documentId);
      await updateDoc(documentRef, {
        isDeleted: true,
        deletedAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      
      console.log('✅ Document deleted');
    } catch (error) {
      console.error('Failed to delete document:', error);
      throw error;
    }
  }

  // Get total storage used by user
  async getTotalStorageUsed(userId: string): Promise<number> {
    try {
      const documentsRef = collection(db, 'documents');
      const userDocsQuery = query(
        documentsRef,
        where('senderId', '==', userId)
      );
      
      const snapshot = await getDocs(userDocsQuery);
      let totalSize = 0;
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        totalSize += data.fileSize || 0;
      });
      
      return totalSize;
    } catch (error) {
      console.error('Failed to get total storage used:', error);
      return 0;
    }
  }
}

export const documentService = new DocumentService();
