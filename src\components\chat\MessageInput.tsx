// 🚀 MESSAGE INPUT COMPONENT
// Handles message composition, voice recording, and media attachments

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';
import { COLORS } from '../../constants/theme';

interface MessageInputProps {
  onSendMessage: (_text: string) => void;
  onSendVoiceMessage: (_audioUri: string, _duration: number) => void;
  onSendMedia: (_mediaUri: string, _type: 'image' | 'video') => void;
  onOpenMediaComposer?: () => void;
  isTyping: boolean;
  onTypingStart: () => void;
  onTypingStop: () => void;
  replyingTo?: any;
  onCancelReply: () => void;
  isDarkMode?: boolean;
  inputBackgroundColor?: string;
  keyboardAppearance?: 'light' | 'dark' | 'default';
}

const getColors = (isDarkMode: boolean) => ({
  primary: '#87CEEB',
  background: isDarkMode ? '#2D2D2D' : '#f8f9fa',
  text: isDarkMode ? '#FFFFFF' : '#333',
  textMuted: isDarkMode ? '#AAAAAA' : '#666',
  border: isDarkMode ? '#404040' : '#e1e5e9',
  white: '#ffffff',
});

export const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  onSendVoiceMessage,
  onSendMedia,
  onOpenMediaComposer,
  isTyping,
  onTypingStart,
  onTypingStop,
  replyingTo,
  onCancelReply,
  isDarkMode = false,
  inputBackgroundColor,
  keyboardAppearance = 'default',
}) => {
  const COLORS = getColors(isDarkMode);
  const [messageText, setMessageText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [isLongPressing, setIsLongPressing] = useState(false);

  const recordingAnim = useRef(new Animated.Value(1)).current;
  const attachmentAnim = useRef(new Animated.Value(0)).current;
  const durationInterval = useRef<NodeJS.Timeout | null>(null);
  const longPressTimer = useRef<NodeJS.Timeout | null>(null);

  const handleSend = () => {
    if (messageText.trim()) {
      onSendMessage(messageText.trim());
      setMessageText('');
      onTypingStop();
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const handleTextChange = (text: string) => {
    console.log('📝 Text changed:', { text, length: text.length, trimmed: text.trim().length });
    setMessageText(text);
    if (text.trim().length > 0 && !isTyping) {
      onTypingStart();
    } else if (text.trim().length === 0 && isTyping) {
      onTypingStop();
    }
  };

  const startRecording = async () => {
    try {
      // Request permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Microphone permission is required to record voice messages.');
        return;
      }

      setIsRecording(true);
      setRecordingDuration(0);

      // Start animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(recordingAnim, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(recordingAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Start recording
      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(newRecording);

      // Start duration timer
      durationInterval.current = setInterval(() => {
        setRecordingDuration(prev => prev + 1);
      }, 1000);

      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    } catch (error) {
      console.error('❌ Error starting recording:', error);
      Alert.alert('Error', 'Failed to start recording');
      setIsRecording(false);
    }
  };

  const stopRecording = async () => {
    try {
      setIsRecording(false);
      recordingAnim.stopAnimation();
      recordingAnim.setValue(1);

      // Clear duration timer
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
        durationInterval.current = null;
      }

      if (recording) {
        await recording.stopAndUnloadAsync();
        const uri = recording.getURI();

        if (uri) {
          onSendVoiceMessage(uri, recordingDuration);
        }

        setRecording(null);
      }

      setRecordingDuration(0);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('❌ Error stopping recording:', error);
      Alert.alert('Error', 'Failed to stop recording');
    }
  };

  const toggleAttachmentMenu = () => {
    const toValue = showAttachmentMenu ? 0 : 1;
    setShowAttachmentMenu(!showAttachmentMenu);
    
    Animated.spring(attachmentAnim, {
      toValue,
      useNativeDriver: true,
    }).start();
  };

  const selectImage = () => {
    try {
      setShowAttachmentMenu(false);
      if (onOpenMediaComposer) {
        onOpenMediaComposer();
      } else {
        // Fallback to direct image picker if MediaComposer is not available
        handleDirectImagePicker();
      }
    } catch (error) {
      console.error('❌ Error opening media composer:', error);
      Alert.alert('Error', 'Failed to open media selector');
    }
  };

  const selectVideo = () => {
    try {
      setShowAttachmentMenu(false);
      if (onOpenMediaComposer) {
        onOpenMediaComposer();
      } else {
        // Fallback to direct video picker if MediaComposer is not available
        handleDirectVideoPicker();
      }
    } catch (error) {
      console.error('❌ Error opening media composer:', error);
      Alert.alert('Error', 'Failed to open media selector');
    }
  };

  // Fallback direct image picker
  const handleDirectImagePicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant photo library permission to select images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        onSendMedia(result.assets[0].uri, 'image');
      }
    } catch (error) {
      console.error('❌ Error selecting image:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  // Fallback direct video picker
  const handleDirectVideoPicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant photo library permission to select videos.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['videos'],
        allowsEditing: true,
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        onSendMedia(result.assets[0].uri, 'video');
      }
    } catch (error) {
      console.error('❌ Error selecting video:', error);
      Alert.alert('Error', 'Failed to select video');
    }
  };

  // Dynamic container style with theme-based background
  const containerStyle = [
    styles.container,
    {
      backgroundColor: inputBackgroundColor || (isDarkMode
        ? 'rgba(55, 55, 55, 0.9)'
        : 'rgba(248, 249, 250, 0.9)')
    }
  ];

  return (
    <View style={containerStyle}>
      {replyingTo && (
        <View style={styles.replyContainer}>
          <View style={styles.replyContent}>
            <Text style={styles.replyLabel}>Replying to {replyingTo.senderName}</Text>
            <Text style={styles.replyText} numberOfLines={1}>
              {replyingTo.text}
            </Text>
          </View>
          <TouchableOpacity onPress={onCancelReply} style={styles.cancelReply}>
            <Ionicons name="close" size={20} color={COLORS.textMuted} />
          </TouchableOpacity>
        </View>
      )}
      
      <View style={[styles.inputContainer, { backgroundColor: inputBackgroundColor || (isDarkMode ? 'rgba(55, 55, 55, 0.9)' : 'rgba(248, 249, 250, 0.9)') }]}>
        <TouchableOpacity
          style={styles.attachmentButton}
          onPress={toggleAttachmentMenu}
        >
          <Ionicons name="add" size={24} color={COLORS.primary} />
        </TouchableOpacity>

        <TextInput
          style={[
            styles.textInput,
            {
              color: COLORS.text,
            }
          ]}
          placeholder="Type a message..."
          placeholderTextColor={COLORS.textMuted}
          value={messageText}
          onChangeText={handleTextChange}
          multiline
          maxLength={1000}
          keyboardAppearance={keyboardAppearance}
        />

        {(() => {
          const hasText = messageText.trim().length > 0;
          console.log('🎤 Button render decision:', { messageText, hasText, isRecording });

          if (hasText) {
            return (
              <TouchableOpacity style={styles.sendButton} onPress={handleSend}>
                <Ionicons name="send" size={20} color="white" />
              </TouchableOpacity>
            );
          } else {
            return (
              <TouchableOpacity
                style={[styles.voiceButton, isRecording && styles.recording]}
                onPress={isRecording ? stopRecording : startRecording}
              >
                <Animated.View style={{ transform: [{ scale: recordingAnim }] }}>
                  <Ionicons
                    name={isRecording ? "stop" : "mic"}
                    size={20}
                    color="white"
                  />
                </Animated.View>
                {isRecording && (
                  <View style={styles.recordingIndicator}>
                    <Text style={styles.recordingText}>
                      {Math.floor(recordingDuration / 60)}:{String(recordingDuration % 60).padStart(2, '0')}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          }
        })()}
      </View>

      {showAttachmentMenu && (
        <Animated.View 
          style={[
            styles.attachmentMenu,
            { transform: [{ scale: attachmentAnim }] }
          ]}
        >
          <TouchableOpacity style={styles.attachmentOption} onPress={selectImage}>
            <Ionicons name="image" size={24} color={COLORS.primary} />
            <Text style={styles.attachmentText}>Photo</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.attachmentOption} onPress={selectVideo}>
            <Ionicons name="videocam" size={24} color={COLORS.primary} />
            <Text style={styles.attachmentText}>Video</Text>
          </TouchableOpacity>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 4, // Reduced from 8 to 4
    paddingBottom: 2, // Reduced from 4 to 2
  },
  replyContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  replyContent: {
    flex: 1,
  },
  replyLabel: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: 'bold',
  },
  replyText: {
    fontSize: 14,
    color: COLORS.text,
    marginTop: 2,
  },
  cancelReply: {
    padding: 4,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    // backgroundColor removed - will be set dynamically
    borderRadius: 25,
    paddingHorizontal: 12,
    paddingVertical: 8, // Reduced from 12 to 8
    minHeight: 44, // Reduced from 56 to 44
  },
  attachmentButton: {
    padding: 8,
    marginRight: 8,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: COLORS.text,
    maxHeight: 120,
    minHeight: 40,
    paddingVertical: 10,
    paddingHorizontal: 4,
    textAlignVertical: 'center',
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 22,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    marginBottom: 2,
  },
  voiceButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 22,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
    marginBottom: 2,
  },
  recording: {
    backgroundColor: '#ff4444',
  },
  attachmentMenu: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
    backgroundColor: COLORS.background,
    borderRadius: 12,
    marginTop: 8,
  },
  attachmentOption: {
    alignItems: 'center',
    padding: 16,
  },
  attachmentText: {
    fontSize: 12,
    color: COLORS.text,
    marginTop: 4,
  },
  recordingIndicator: {
    position: 'absolute',
    top: -30,
    left: -20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  recordingText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
  },
});
