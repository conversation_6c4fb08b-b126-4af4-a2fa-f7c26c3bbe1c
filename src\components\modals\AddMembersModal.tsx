import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  Pressable,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface Contact {
  id: string;
  name: string;
  phoneNumber?: string;
  email?: string;
  avatar?: string;
  isRegistered: boolean;
}

interface GroupMember {
  id: string;
  name: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member';
  isOnline: boolean;
  lastSeen?: Date;
  joinedAt: Date;
}

interface AddMembersModalProps {
  visible: boolean;
  onClose: () => void;
  currentMembers: GroupMember[];
  onAddMembers?: (memberIds: string[]) => void;
  currentUserRole: 'owner' | 'admin' | 'member';
}

export const AddMembersModal: React.FC<AddMembersModalProps> = ({
  visible,
  onClose,
  currentMembers,
  onAddMembers,
  currentUserRole,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);

  const canAddMembers = currentUserRole === 'owner' || currentUserRole === 'admin';

  // Load real contacts from Firebase
  useEffect(() => {
    const loadContacts = async () => {
      try {
        // TODO: Implement real contacts service
        // For now, return empty array to encourage real contact integration
        const availableContacts: Contact[] = [];
        setContacts(availableContacts);
      } catch (error) {
        console.error('❌ Error loading contacts:', error);
        setContacts([]);
      }
    };

    loadContacts();
  }, [currentMembers]);

  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredContacts(contacts);
      return;
    }

    const filtered = contacts.filter(contact =>
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.phoneNumber?.includes(searchQuery) ||
      contact.email?.toLowerCase().includes(searchQuery.toLowerCase())
    );

    setFilteredContacts(filtered);
  }, [searchQuery, contacts]);

  const handleContactToggle = (contactId: string) => {
    setSelectedContacts(prev =>
      prev.includes(contactId)
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  const handleAddMembers = () => {
    if (!canAddMembers) {
      Alert.alert('Permission Denied', 'Only admins can add members');
      return;
    }

    if (selectedContacts.length === 0) {
      Alert.alert('No Selection', 'Please select at least one contact to add');
      return;
    }

    Alert.alert(
      'Add Members',
      `Add ${selectedContacts.length} member(s) to the group?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Add',
          onPress: () => {
            onAddMembers?.(selectedContacts);
            setSelectedContacts([]);
            onClose();
          },
        },
      ]
    );
  };

  const renderContact = ({ item }: { item: Contact }) => {
    const isSelected = selectedContacts.includes(item.id);

    return (
      <TouchableOpacity
        onPress={() => handleContactToggle(item.id)}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 16,
          borderBottomWidth: 1,
          borderBottomColor: '#E5E7EB',
          backgroundColor: isSelected ? 'rgba(135, 206, 235, 0.1)' : 'transparent',
        }}
      >
        {/* Avatar */}
        {item.avatar ? (
          <Image
            source={{ uri: item.avatar }}
            style={{ width: 50, height: 50, borderRadius: 25, marginRight: 16 }}
          />
        ) : (
          <View
            style={{
              width: 50,
              height: 50,
              borderRadius: 25,
              backgroundColor: '#87CEEB',
              justifyContent: 'center',
              alignItems: 'center',
              marginRight: 16,
            }}
          >
            <Text style={{ color: 'white', fontSize: 18, fontWeight: 'bold' }}>
              {item.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}

        {/* Contact Info */}
        <View style={{ flex: 1 }}>
          <Text style={{ fontSize: 16, fontWeight: '500', color: '#333' }}>
            {item.name}
          </Text>
          {item.phoneNumber && (
            <Text style={{ fontSize: 14, color: '#666', marginTop: 2 }}>
              {item.phoneNumber}
            </Text>
          )}
          {item.email && (
            <Text style={{ fontSize: 14, color: '#666', marginTop: 2 }}>
              {item.email}
            </Text>
          )}
        </View>

        {/* Selection Indicator */}
        <View
          style={{
            width: 24,
            height: 24,
            borderRadius: 12,
            borderWidth: 2,
            borderColor: isSelected ? '#87CEEB' : '#E5E7EB',
            backgroundColor: isSelected ? '#87CEEB' : 'transparent',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {isSelected && (
            <Ionicons name="checkmark" size={16} color="white" />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={{ flex: 1, backgroundColor: 'rgba(0,0,0,0.5)' }}>
        <View
          style={{
            flex: 1,
            backgroundColor: 'white',
            marginTop: 50,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
          }}
        >
          {/* Header */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: 16,
              borderBottomWidth: 1,
              borderBottomColor: '#E5E7EB',
            }}
          >
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#333" />
            </TouchableOpacity>
            
            <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#333' }}>
              Add Members
            </Text>

            <TouchableOpacity
              onPress={handleAddMembers}
              disabled={selectedContacts.length === 0 || !canAddMembers}
              style={{
                paddingHorizontal: 16,
                paddingVertical: 8,
                borderRadius: 20,
                backgroundColor: selectedContacts.length > 0 && canAddMembers ? '#87CEEB' : '#E5E7EB',
              }}
            >
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: '600',
                  color: selectedContacts.length > 0 && canAddMembers ? 'white' : '#999',
                }}
              >
                Add ({selectedContacts.length})
              </Text>
            </TouchableOpacity>
          </View>

          {/* Search Input */}
          <View style={{ padding: 16 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#F8F9FA',
                borderRadius: 12,
                paddingHorizontal: 16,
                paddingVertical: 12,
              }}
            >
              <Ionicons name="search" size={20} color="#999" />
              <TextInput
                placeholder="Search contacts..."
                placeholderTextColor="#999"
                value={searchQuery}
                onChangeText={setSearchQuery}
                style={{
                  flex: 1,
                  marginLeft: 12,
                  fontSize: 16,
                  color: '#333',
                }}
              />
              {searchQuery.length > 0 && (
                <TouchableOpacity onPress={() => setSearchQuery('')}>
                  <Ionicons name="close-circle" size={20} color="#999" />
                </TouchableOpacity>
              )}
            </View>
          </View>

          {/* Contacts List */}
          {filteredContacts.length === 0 ? (
            <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
              <Ionicons name="people-outline" size={64} color="#E5E7EB" />
              <Text style={{ fontSize: 16, color: '#999', marginTop: 16 }}>
                {searchQuery.trim() ? 'No contacts found' : 'No contacts available'}
              </Text>
            </View>
          ) : (
            <FlatList
              data={filteredContacts}
              keyExtractor={(item) => item.id}
              renderItem={renderContact}
              style={{ flex: 1 }}
            />
          )}

          {!canAddMembers && (
            <View
              style={{
                backgroundColor: '#FEF3C7',
                padding: 16,
                margin: 16,
                borderRadius: 12,
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Ionicons name="information-circle" size={20} color="#F59E0B" />
              <Text style={{ marginLeft: 12, fontSize: 14, color: '#92400E', flex: 1 }}>
                Only group admins can add new members
              </Text>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};
