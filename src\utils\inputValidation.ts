// 🔒 Enhanced Input Validation and Sanitization Utilities for IraChat
// Comprehensive security-focused input validation with mobile optimization
import { Dimensions } from 'react-native';

// Get device dimensions for responsive validation
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  sanitized?: string;
  warnings?: string[];
  deviceOptimized?: boolean;
}

// Enhanced validation options for mobile
export interface MobileValidationOptions {
  strictMode?: boolean;
  allowEmoji?: boolean;
  maxLengthMobile?: number;
  maxLengthTablet?: number;
  compactErrors?: boolean;
}

// Security patterns to detect and prevent
const SECURITY_PATTERNS = {
  // XSS patterns
  XSS: [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe/gi,
    /<object/gi,
    /<embed/gi,
  ],
  
  // SQL injection patterns
  SQL_INJECTION: [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
    /('|(\\')|(;)|(--)|(\|)|(\*)|(%)|(\+))/gi,
  ],
  
  // Path traversal
  PATH_TRAVERSAL: [
    /\.\.\//gi,
    /\.\.\\/gi,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
  ],
  
  // Command injection
  COMMAND_INJECTION: [
    /(\||&|;|\$\(|\`)/gi,
    /(rm\s|del\s|format\s)/gi,
  ],
};

// Content sanitization
export class ContentSanitizer {
  /**
   * Sanitize text content for safe display
   */
  static sanitizeText(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;')
      .trim();
  }

  /**
   * Sanitize HTML content (strip all HTML tags)
   */
  static sanitizeHTML(input: string): string {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .replace(/<[^>]*>/g, '') // Remove all HTML tags
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')
      .trim();
  }

  /**
   * Sanitize URL input
   */
  static sanitizeURL(input: string): ValidationResult {
    if (!input || typeof input !== 'string') {
      return { isValid: false, error: 'URL is required' };
    }

    try {
      const url = new URL(input);
      
      // Only allow HTTP and HTTPS protocols
      if (!['http:', 'https:'].includes(url.protocol)) {
        return { isValid: false, error: 'Only HTTP and HTTPS URLs are allowed' };
      }

      // Check for suspicious patterns
      if (this.containsSuspiciousPatterns(input)) {
        return { isValid: false, error: 'URL contains invalid characters' };
      }

      return { isValid: true, sanitized: url.toString() };
    } catch (_error) {
      return { isValid: false, error: 'Invalid URL format' };
    }
  }

  /**
   * Check for suspicious patterns in input
   */
  static containsSuspiciousPatterns(input: string): boolean {
    const allPatterns = [
      ...SECURITY_PATTERNS.XSS,
      ...SECURITY_PATTERNS.SQL_INJECTION,
      ...SECURITY_PATTERNS.PATH_TRAVERSAL,
      ...SECURITY_PATTERNS.COMMAND_INJECTION,
    ];

    return allPatterns.some(pattern => pattern.test(input));
  }
}

// Enhanced message validation with mobile optimization
export class MessageValidator {
  private static readonly MAX_MESSAGE_LENGTH_MOBILE = 2000; // Shorter for small devices
  private static readonly MAX_MESSAGE_LENGTH_TABLET = 5000;
  private static readonly MAX_MESSAGE_LENGTH_DESKTOP = 10000;
  private static readonly MIN_MESSAGE_LENGTH = 1;

  static validate(message: string, options: MobileValidationOptions = {}): ValidationResult {
    if (!message || typeof message !== 'string') {
      const error = options.compactErrors || isSmallDevice ? 'Message required' : 'Message is required';
      return { isValid: false, error };
    }

    const warnings: string[] = [];

    // Get device-appropriate max length
    const maxLength = options.maxLengthMobile && isSmallDevice
      ? options.maxLengthMobile
      : options.maxLengthTablet && isTablet
      ? options.maxLengthTablet
      : isSmallDevice
      ? this.MAX_MESSAGE_LENGTH_MOBILE
      : isTablet
      ? this.MAX_MESSAGE_LENGTH_TABLET
      : this.MAX_MESSAGE_LENGTH_DESKTOP;

    // Length validation
    if (message.length < this.MIN_MESSAGE_LENGTH) {
      const error = options.compactErrors || isSmallDevice ? 'Too short' : 'Message is too short';
      return { isValid: false, error };
    }

    if (message.length > maxLength) {
      const error = options.compactErrors || isSmallDevice
        ? `Max ${maxLength} chars`
        : `Message is too long (max ${maxLength} characters)`;
      return { isValid: false, error };
    }

    // Warn if approaching limit on small devices
    if (isSmallDevice && message.length > maxLength * 0.8) {
      warnings.push(`${maxLength - message.length} characters remaining`);
    }

    // Emoji validation
    if (!options.allowEmoji && /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(message)) {
      if (options.strictMode) {
        return { isValid: false, error: 'Emojis not allowed' };
      } else {
        warnings.push('Consider using text instead of emojis for better compatibility');
      }
    }

    // Security validation
    if (ContentSanitizer.containsSuspiciousPatterns(message)) {
      const error = options.compactErrors || isSmallDevice
        ? 'Invalid content'
        : 'Message contains invalid content';
      return { isValid: false, error };
    }

    // Sanitize the message
    const sanitized = ContentSanitizer.sanitizeText(message);

    return {
      isValid: true,
      sanitized,
      warnings: warnings.length > 0 ? warnings : undefined,
      deviceOptimized: true
    };
  }

  /**
   * Get recommended message length for current device
   */
  static getRecommendedMaxLength(): number {
    return isSmallDevice
      ? this.MAX_MESSAGE_LENGTH_MOBILE
      : isTablet
      ? this.MAX_MESSAGE_LENGTH_TABLET
      : this.MAX_MESSAGE_LENGTH_DESKTOP;
  }
}

// Username validation
export class UsernameValidator {
  private static readonly MIN_LENGTH = 3;
  private static readonly MAX_LENGTH = 20;
  // Updated pattern to allow Unicode characters including emojis, but must start with capital letter and no spaces
  private static readonly VALID_PATTERN = /^[A-Z][\p{L}\p{N}\p{Emoji}_]*$/u;
  private static readonly RESERVED_USERNAMES = [
    'admin', 'administrator', 'root', 'system', 'support', 'help',
    'api', 'www', 'mail', 'email', 'test', 'demo', 'guest',
    'irachat', 'moderator', 'mod', 'staff', 'official',
  ];

  static validate(username: string): ValidationResult {
    if (!username || typeof username !== 'string') {
      return { isValid: false, error: 'Username is required' };
    }

    const trimmed = username.trim();

    // Length validation
    if (trimmed.length < this.MIN_LENGTH) {
      return { isValid: false, error: `Username must be at least ${this.MIN_LENGTH} characters` };
    }

    if (trimmed.length > this.MAX_LENGTH) {
      return { isValid: false, error: `Username must be no more than ${this.MAX_LENGTH} characters` };
    }

    // No spaces allowed (must be one word)
    if (/\s/.test(trimmed)) {
      return { isValid: false, error: 'Username must be one word (no spaces)' };
    }

    // Must start with capital letter
    if (!/^[A-Z]/.test(trimmed)) {
      return { isValid: false, error: 'Username must start with a capital letter' };
    }

    // Pattern validation - allow letters, numbers, emojis, underscores
    if (!this.VALID_PATTERN.test(trimmed)) {
      return { isValid: false, error: 'Username must start with capital letter and contain only letters, numbers, emojis, and underscores (no spaces)' };
    }

    // Reserved username check (case insensitive)
    if (this.RESERVED_USERNAMES.includes(trimmed.toLowerCase())) {
      return { isValid: false, error: 'This username is not available' };
    }

    return { isValid: true, sanitized: trimmed };
  }
}

// Name validation
export class NameValidator {
  private static readonly MIN_LENGTH = 2;
  private static readonly MAX_LENGTH = 50;
  // Allow Unicode characters including emojis, letters, numbers, spaces, and basic punctuation
  private static readonly VALID_PATTERN = /^[\p{L}\p{N}\p{Emoji}\s._-]+$/u;

  static validate(name: string): ValidationResult {
    if (!name || typeof name !== 'string') {
      return { isValid: false, error: 'Name is required' };
    }

    const trimmed = name.trim();

    // Length validation
    if (trimmed.length < this.MIN_LENGTH) {
      return { isValid: false, error: `Name must be at least ${this.MIN_LENGTH} characters` };
    }

    if (trimmed.length > this.MAX_LENGTH) {
      return { isValid: false, error: `Name must be no more than ${this.MAX_LENGTH} characters` };
    }

    // Pattern validation - allow letters, numbers, emojis, spaces, and basic punctuation
    if (!this.VALID_PATTERN.test(trimmed)) {
      return { isValid: false, error: 'Name can contain letters, numbers, emojis, spaces, and basic punctuation' };
    }

    return { isValid: true, sanitized: trimmed };
  }
}

// Bio validation
export class BioValidator {
  private static readonly MAX_LENGTH = 200;
  // Allow Unicode characters including emojis, letters, numbers, spaces, and most punctuation
  private static readonly VALID_PATTERN = /^[\p{L}\p{N}\p{Emoji}\s.,!?;:'"()[\]{}@#$%^&*+=<>\/\\|`~_-]*$/u;

  static validate(bio: string): ValidationResult {
    if (!bio || typeof bio !== 'string') {
      return { isValid: true, sanitized: '' }; // Bio is optional
    }

    const trimmed = bio.trim();

    // Length validation
    if (trimmed.length > this.MAX_LENGTH) {
      return { isValid: false, error: `Bio must be no more than ${this.MAX_LENGTH} characters` };
    }

    // Pattern validation - allow letters, numbers, emojis, spaces, and punctuation
    if (trimmed.length > 0 && !this.VALID_PATTERN.test(trimmed)) {
      return { isValid: false, error: 'Bio can contain letters, numbers, emojis, spaces, and punctuation' };
    }

    return { isValid: true, sanitized: trimmed };
  }
}

// Phone number validation (enhanced)
export class PhoneValidator {
  private static readonly E164_PATTERN = /^\+[1-9]\d{1,14}$/;
  private static readonly SUSPICIOUS_PATTERNS = [
    /^\+1{10,}$/, // Too many 1s
    /^\+(\d)\1{9,}$/, // Repeated digits
    /^\+999\d+$/, // Test numbers
    /^\+000\d+$/, // Invalid area codes
    /^\+1234567890$/, // Common test number
  ];

  static validate(phoneNumber: string): ValidationResult {
    if (!phoneNumber || typeof phoneNumber !== 'string') {
      return { isValid: false, error: 'Phone number is required' };
    }

    // Sanitize input
    const sanitized = phoneNumber.replace(/[^\d+]/g, '');
    
    // Ensure it starts with +
    const formatted = sanitized.startsWith('+') ? sanitized : '+' + sanitized;

    // E.164 format validation
    if (!this.E164_PATTERN.test(formatted)) {
      return { isValid: false, error: 'Invalid phone number format. Use international format (+1234567890)' };
    }

    // Check for suspicious patterns
    for (const pattern of this.SUSPICIOUS_PATTERNS) {
      if (pattern.test(formatted)) {
        return { isValid: false, error: 'Invalid phone number' };
      }
    }

    // Length validation
    const digits = formatted.replace(/\D/g, '');
    if (digits.length < 7 || digits.length > 15) {
      return { isValid: false, error: 'Phone number length is invalid' };
    }

    return { isValid: true, sanitized: formatted };
  }
}

// Enhanced file validation with mobile optimization
export class FileValidator {
  private static readonly ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  private static readonly ALLOWED_VIDEO_TYPES = ['video/mp4', 'video/webm', 'video/ogg'];
  private static readonly ALLOWED_AUDIO_TYPES = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a'];
  private static readonly ALLOWED_DOCUMENT_TYPES = ['application/pdf', 'text/plain'];

  // Mobile-optimized file size limits
  private static readonly MAX_FILE_SIZE_MOBILE = 25 * 1024 * 1024; // 25MB for mobile
  private static readonly MAX_FILE_SIZE_TABLET = 50 * 1024 * 1024; // 50MB for tablet
  private static readonly MAX_FILE_SIZE_DESKTOP = 100 * 1024 * 1024; // 100MB for desktop

  private static readonly MAX_IMAGE_SIZE_MOBILE = 5 * 1024 * 1024; // 5MB
  private static readonly MAX_IMAGE_SIZE_TABLET = 10 * 1024 * 1024; // 10MB
  private static readonly MAX_IMAGE_SIZE_DESKTOP = 20 * 1024 * 1024; // 20MB

  private static readonly MAX_VIDEO_SIZE_MOBILE = 50 * 1024 * 1024; // 50MB
  private static readonly MAX_VIDEO_SIZE_TABLET = 100 * 1024 * 1024; // 100MB
  private static readonly MAX_VIDEO_SIZE_DESKTOP = 200 * 1024 * 1024; // 200MB

  static validateFile(
    file: File,
    type: 'image' | 'video' | 'audio' | 'document',
    options: MobileValidationOptions = {}
  ): ValidationResult {
    if (!file) {
      const error = options.compactErrors || isSmallDevice ? 'File required' : 'File is required';
      return { isValid: false, error };
    }

    const warnings: string[] = [];

    // Get device-appropriate size limits
    let maxSize = this.getMaxFileSize(type);

    if (file.size > maxSize) {
      const sizeMB = Math.round(maxSize / 1024 / 1024);
      const error = options.compactErrors || isSmallDevice
        ? `Max ${sizeMB}MB`
        : `File size exceeds ${sizeMB}MB limit`;
      return { isValid: false, error };
    }

    // Warn if file is large on mobile
    if (isSmallDevice && file.size > maxSize * 0.7) {
      warnings.push('Large file may take time to upload on mobile');
    }

    // Type validation
    let allowedTypes: string[] = [];
    switch (type) {
      case 'image':
        allowedTypes = this.ALLOWED_IMAGE_TYPES;
        break;
      case 'video':
        allowedTypes = this.ALLOWED_VIDEO_TYPES;
        break;
      case 'audio':
        allowedTypes = this.ALLOWED_AUDIO_TYPES;
        break;
      case 'document':
        allowedTypes = this.ALLOWED_DOCUMENT_TYPES;
        break;
    }

    if (!allowedTypes.includes(file.type)) {
      const error = options.compactErrors || isSmallDevice
        ? 'File type not allowed'
        : `File type ${file.type} is not allowed`;
      return { isValid: false, error };
    }

    // Filename validation
    const filename = file.name;
    if (!/^[a-zA-Z0-9._-]+$/.test(filename)) {
      const error = options.compactErrors || isSmallDevice
        ? 'Invalid filename'
        : 'Filename contains invalid characters';
      return { isValid: false, error };
    }

    // Check filename length
    if (filename.length > 100) {
      const error = options.compactErrors || isSmallDevice
        ? 'Filename too long'
        : 'Filename is too long (max 100 characters)';
      return { isValid: false, error };
    }

    return {
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined,
      deviceOptimized: true
    };
  }

  /**
   * Get maximum file size for current device and file type
   */
  private static getMaxFileSize(type: 'image' | 'video' | 'audio' | 'document'): number {
    if (type === 'image') {
      return isSmallDevice
        ? this.MAX_IMAGE_SIZE_MOBILE
        : isTablet
        ? this.MAX_IMAGE_SIZE_TABLET
        : this.MAX_IMAGE_SIZE_DESKTOP;
    }

    if (type === 'video') {
      return isSmallDevice
        ? this.MAX_VIDEO_SIZE_MOBILE
        : isTablet
        ? this.MAX_VIDEO_SIZE_TABLET
        : this.MAX_VIDEO_SIZE_DESKTOP;
    }

    // For audio and documents
    return isSmallDevice
      ? this.MAX_FILE_SIZE_MOBILE
      : isTablet
      ? this.MAX_FILE_SIZE_TABLET
      : this.MAX_FILE_SIZE_DESKTOP;
  }

  /**
   * Get recommended file size limits for current device
   */
  static getRecommendedLimits(): Record<string, number> {
    return {
      image: this.getMaxFileSize('image'),
      video: this.getMaxFileSize('video'),
      audio: this.getMaxFileSize('audio'),
      document: this.getMaxFileSize('document'),
    };
  }
}

// General input validator
export class InputValidator {
  /**
   * Validate any input with custom rules
   */
  static validate(
    input: any,
    rules: {
      required?: boolean;
      type?: 'string' | 'number' | 'boolean' | 'array' | 'object';
      minLength?: number;
      maxLength?: number;
      pattern?: RegExp;
      custom?: (_value: any) => ValidationResult;
    }
  ): ValidationResult {
    // Required validation
    if (rules.required && (input === null || input === undefined || input === '')) {
      return { isValid: false, error: 'This field is required' };
    }

    // Type validation
    if (input !== null && input !== undefined && rules.type) {
      const actualType = Array.isArray(input) ? 'array' : typeof input;
      if (actualType !== rules.type) {
        return { isValid: false, error: `Expected ${rules.type}, got ${actualType}` };
      }
    }

    // String-specific validations
    if (typeof input === 'string') {
      if (rules.minLength && input.length < rules.minLength) {
        return { isValid: false, error: `Minimum length is ${rules.minLength}` };
      }

      if (rules.maxLength && input.length > rules.maxLength) {
        return { isValid: false, error: `Maximum length is ${rules.maxLength}` };
      }

      if (rules.pattern && !rules.pattern.test(input)) {
        return { isValid: false, error: 'Invalid format' };
      }
    }

    // Custom validation
    if (rules.custom) {
      return rules.custom(input);
    }

    return { isValid: true, sanitized: input };
  }
}

/**
 * Mobile-optimized validation helper functions
 */
export class MobileValidationHelper {
  /**
   * Get device-appropriate error message
   */
  static getErrorMessage(fullMessage: string, compactMessage: string): string {
    return isSmallDevice ? compactMessage : fullMessage;
  }

  /**
   * Check if current device should use compact validation
   */
  static shouldUseCompactValidation(): boolean {
    return isSmallDevice;
  }

  /**
   * Get validation options optimized for current device
   */
  static getDeviceOptimizedOptions(): MobileValidationOptions {
    return {
      compactErrors: isSmallDevice,
      allowEmoji: true,
      maxLengthMobile: MessageValidator.getRecommendedMaxLength(),
      strictMode: false
    };
  }

  /**
   * Validate multiple inputs with device optimization
   */
  static validateBatch(inputs: {
    value: string;
    type: 'phone' | 'message' | 'username';
    options?: MobileValidationOptions;
  }[]): ValidationResult[] {
    const deviceOptions = this.getDeviceOptimizedOptions();

    return inputs.map(input => {
      const options = { ...deviceOptions, ...input.options };

      switch (input.type) {
        case 'phone':
          return PhoneValidator.validate(input.value);
        case 'message':
          return MessageValidator.validate(input.value, options);
        case 'username':
          return UsernameValidator.validate(input.value);
        default:
          return { isValid: false, error: 'Unknown validation type' };
      }
    });
  }
}

/**
 * Offline validation cache for better performance
 */
export class ValidationCache {
  private static cache = new Map<string, ValidationResult>();
  private static readonly MAX_CACHE_SIZE = isSmallDevice ? 50 : 100;

  /**
   * Get cached validation result
   */
  static get(key: string): ValidationResult | null {
    return this.cache.get(key) || null;
  }

  /**
   * Set cached validation result
   */
  static set(key: string, result: ValidationResult): void {
    // Clear old entries if cache is full
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    this.cache.set(key, result);
  }

  /**
   * Clear validation cache
   */
  static clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  static getStats(): { size: number; maxSize: number; hitRate?: number } {
    return {
      size: this.cache.size,
      maxSize: this.MAX_CACHE_SIZE
    };
  }
}

// Export default object with all validators
export default {
  MessageValidator,
  UsernameValidator,
  PhoneValidator,
  FileValidator,
  ContentSanitizer,
  InputValidator,
  MobileValidationHelper,
  ValidationCache,
};
