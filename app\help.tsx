import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { navigationService, ROUTES } from '../src/services/navigationService';
import { FloatingActionButton, QuickNavActions } from '../src/components/NavigationHelper';

export default function HelpScreen() {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const faqData = [
    {
      id: '1',
      question: 'How do I start a new chat?',
      answer: 'Tap the "+" button on the Chats tab, then select "New Chat" and choose a contact from your list.',
    },
    {
      id: '2',
      question: 'How do I create a group chat?',
      answer: 'Tap the "+" button on the Chats tab, select "New Group", add members, and give your group a name.',
    },
    {
      id: '3',
      question: 'How do I send photos and videos?',
      answer: 'In any chat, tap the camera icon to take a photo/video or the attachment icon to select from your gallery.',
    },
    {
      id: '4',
      question: 'How do I make voice and video calls?',
      answer: 'Open a chat and tap the phone icon for voice calls or video icon for video calls.',
    },
    {
      id: '5',
      question: 'How do I change my profile picture?',
      answer: 'Go to Settings > Profile, tap on your current profile picture, and select a new one.',
    },
    {
      id: '6',
      question: 'How do I backup my chats?',
      answer: 'Go to Settings > Chats > Chat Backup to backup your messages to cloud storage.',
    },
    {
      id: '7',
      question: 'How can I contact IraChat support?',
      answer: 'You can reach us via:\n• Email: <EMAIL>\n• Phone: +256 787 272 445\n• Website: https://irachat.app/support\n• Live chat available 24/7 in the app',
    },
    {
      id: '8',
      question: 'What are IraChat\'s business hours?',
      answer: 'Our support team is available 24/7 for urgent issues. For general inquiries, our business hours are Monday-Friday 8AM-6PM EAT (East Africa Time).',
    },
    {
      id: '9',
      question: 'How do I report a problem or bug?',
      answer: 'Go to Settings > Help & Support > Report a Problem, or contact us <NAME_EMAIL> with detailed information about the issue.',
    },
  ];

  const helpCategories = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      icon: 'rocket-outline',
      description: 'Learn the basics of using IraChat',
    },
    {
      id: 'messaging',
      title: 'Messaging',
      icon: 'chatbubble-outline',
      description: 'Send messages, photos, and files',
    },
    {
      id: 'calls',
      title: 'Voice & Video Calls',
      icon: 'call-outline',
      description: 'Make high-quality calls',
    },
    {
      id: 'groups',
      title: 'Groups',
      icon: 'people-outline',
      description: 'Create and manage group chats',
    },
    {
      id: 'privacy',
      title: 'Privacy & Security',
      icon: 'shield-outline',
      description: 'Keep your conversations secure',
    },
    {
      id: 'troubleshooting',
      title: 'Troubleshooting',
      icon: 'build-outline',
      description: 'Fix common issues',
    },
  ];

  const filteredFAQ = faqData.filter(
    item =>
      item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'Choose how you\'d like to contact our support team:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Email',
          onPress: async () => {
            try {
              const emailUrl = 'mailto:<EMAIL>?subject=IraChat Support Request&body=Please describe your issue:';
              const canOpen = await Linking.canOpenURL(emailUrl);
              if (canOpen) {
                await Linking.openURL(emailUrl);
              } else {
                Alert.alert('Error', 'Cannot open email app. Please email <NAME_EMAIL>');
              }
            } catch (_error) {
              Alert.alert('Error', 'Failed to open email app');
            }
          }
        },
        {
          text: 'Website',
          onPress: async () => {
            try {
              const websiteUrl = 'https://irachat.app/support';
              const canOpen = await Linking.canOpenURL(websiteUrl);
              if (canOpen) {
                await Linking.openURL(websiteUrl);
              } else {
                Alert.alert('Error', 'Cannot open website. Please visit https://irachat.app/support');
              }
            } catch (_error) {
              Alert.alert('Error', 'Failed to open website');
            }
          }
        },
        {
          text: 'Phone',
          onPress: async () => {
            try {
              const phoneUrl = 'tel:+256787272445';
              const canOpen = await Linking.canOpenURL(phoneUrl);
              if (canOpen) {
                await Linking.openURL(phoneUrl);
              } else {
                Alert.alert('Error', 'Cannot make calls. Please call +256 787272445');
              }
            } catch (_error) {
              Alert.alert('Error', 'Failed to open phone app');
            }
          }
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigationService.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Help & Support</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Search */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search help topics..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        {/* Help Categories */}
        {!searchQuery && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Help Categories</Text>
            {helpCategories.map((category) => (
              <TouchableOpacity key={category.id} style={styles.categoryCard}>
                <View style={styles.categoryIcon}>
                  <Ionicons name={category.icon as any} size={24} color="#667eea" />
                </View>
                <View style={styles.categoryContent}>
                  <Text style={styles.categoryTitle}>{category.title}</Text>
                  <Text style={styles.categoryDescription}>{category.description}</Text>
                </View>
                <Ionicons name="chevron-forward" size={20} color="#CCC" />
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* FAQ Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {searchQuery ? 'Search Results' : 'Frequently Asked Questions'}
          </Text>
          {filteredFAQ.map((faq) => (
            <TouchableOpacity
              key={faq.id}
              style={styles.faqCard}
              onPress={() => setExpandedFAQ(expandedFAQ === faq.id ? null : faq.id)}
            >
              <View style={styles.faqHeader}>
                <Text style={styles.faqQuestion}>{faq.question}</Text>
                <Ionicons
                  name={expandedFAQ === faq.id ? 'chevron-up' : 'chevron-down'}
                  size={20}
                  color="#667eea"
                />
              </View>
              {expandedFAQ === faq.id && (
                <Text style={styles.faqAnswer}>{faq.answer}</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Contact Support */}
        <TouchableOpacity style={styles.supportButton} onPress={handleContactSupport}>
          <Ionicons name="headset" size={24} color="#FFFFFF" />
          <Text style={styles.supportButtonText}>Contact Support</Text>
        </TouchableOpacity>

        {/* App Info */}
        <View style={styles.appInfo}>
          <Text style={styles.appInfoText}>IraChat v1.0.2</Text>
          <Text style={styles.appInfoText}>Need more help? Visit our website</Text>
        </View>

        {/* Quick Navigation Links */}
        <View style={styles.quickLinks}>
          <TouchableOpacity
            style={styles.quickLinkButton}
            onPress={() => navigationService.navigate(ROUTES.HELP.ABOUT)}
          >
            <Ionicons name="information-circle-outline" size={20} color="#667eea" />
            <Text style={styles.quickLinkText}>About IraChat</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickLinkButton}
            onPress={() => navigationService.navigate(ROUTES.SETTINGS.PRIVACY)}
          >
            <Ionicons name="shield-outline" size={20} color="#667eea" />
            <Text style={styles.quickLinkText}>Privacy Policy</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickLinkButton}
            onPress={() => navigationService.openSettings()}
          >
            <Ionicons name="settings-outline" size={20} color="#667eea" />
            <Text style={styles.quickLinkText}>App Settings</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Floating Action Button for Quick Help Actions */}
      <FloatingActionButton
        actions={[
          {
            icon: 'call-outline',
            label: 'Call Support',
            onPress: () => Linking.openURL('tel:0787272445'),
            color: '#10B981',
          },
          {
            icon: 'mail-outline',
            label: 'Email Support',
            onPress: () => Linking.openURL('mailto:<EMAIL>'),
            color: '#3B82F6',
          },
          QuickNavActions.support,
        ]}
        mainAction={{
          icon: 'headset-outline',
          onPress: handleContactSupport,
          backgroundColor: '#667eea',
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    backgroundColor: '#667eea',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    color: '#FFFFFF',
    fontSize: 20,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 20,
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  categoryCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F0F4FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryContent: {
    flex: 1,
  },
  categoryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  categoryDescription: {
    fontSize: 14,
    color: '#666',
  },
  faqCard: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  faqQuestion: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  faqAnswer: {
    marginTop: 12,
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  supportButton: {
    backgroundColor: '#667eea',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  supportButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  appInfo: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  appInfoText: {
    fontSize: 14,
    color: '#999',
    marginBottom: 4,
  },
  quickLinks: {
    marginTop: 20,
    marginBottom: 20,
  },
  quickLinkButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  quickLinkText: {
    marginLeft: 12,
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
});
