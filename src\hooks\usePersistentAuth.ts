// 🔐 PERSISTENT AUTHENTICATION HOOK
// React hook for managing persistent authentication state with automatic session restoration

import { useState, useEffect, useCallback } from 'react';
import { useDispatch } from "react-redux";
import { appInitializationService } from '../services/appInitializationService';
import { logout, setLoading, setUser } from "../redux/userSlice";
import { User } from '../types';
import NetInfo from '@react-native-community/netinfo';

interface PersistentAuthState {
  isLoading: boolean;
  isLoggedIn: boolean;
  user: User | null;
  isInitialized: boolean;
  shouldShowOnboarding: boolean;
  error: string | null;
}

interface PersistentAuthActions {
  login: (user: User) => Promise<void>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  clearError: () => void;
}

export const usePersistentAuth = (): [PersistentAuthState, PersistentAuthActions] => {
  const dispatch = useDispatch();
  const [authState, setAuthState] = useState<PersistentAuthState>({
    isLoading: true,
    isLoggedIn: false,
    user: null,
    isInitialized: false,
    shouldShowOnboarding: false,
    error: null,
  });

  /**
   * Initialize app and restore session
   */
  const initializeApp = useCallback(async () => {
    try {
      console.log('🚀 Initializing persistent authentication...');
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      dispatch(setLoading(true));

      const result = await appInitializationService.initialize();

      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        isLoggedIn: result.isLoggedIn,
        user: result.user || null,
        isInitialized: true,
        shouldShowOnboarding: result.shouldShowOnboarding,
        error: result.error || null,
      }));

      // Update Redux state
      if (result.user) {
        dispatch(setUser(result.user));
        console.log('✅ User session restored:', result.user.name);
      } else {
        dispatch(logout());
        console.log('📭 No session to restore');
      }

      // Setup monitoring
      appInitializationService.setupNetworkMonitoring();
      appInitializationService.setupAuthStateMonitoring();

    } catch (error) {
      console.error('❌ Failed to initialize persistent auth:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        isInitialized: true,
        error: error instanceof Error ? error.message : 'Initialization failed',
      }));
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Handle user login with persistence
   */
  const login = useCallback(async (user: User) => {
    try {
      console.log('👤 Logging in user with persistence:', user.name);
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      dispatch(setLoading(true));

      await appInitializationService.handleUserLogin(user);

      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        isLoggedIn: true,
        user,
        shouldShowOnboarding: false,
      }));

      dispatch(setUser(user));
      console.log('✅ User logged in with persistent session');
    } catch (error) {
      console.error('❌ Persistent login failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Login failed',
      }));
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Handle user logout with session cleanup
   */
  const logoutUser = useCallback(async () => {
    try {
      console.log('👋 Logging out user and clearing session...');
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
      dispatch(setLoading(true));

      await appInitializationService.handleManualLogout();

      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        isLoggedIn: false,
        user: null,
      }));

      dispatch(logout());
      console.log('✅ User logged out and session cleared');
    } catch (error) {
      console.error('❌ Logout failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Logout failed',
      }));
      dispatch(setLoading(false));
    }
  }, [dispatch]);

  /**
   * Refresh session and validate tokens
   */
  const refreshSession = useCallback(async () => {
    try {
      console.log('🔄 Refreshing persistent session...');
      setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

      const user = await appInitializationService.refreshSession();

      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        isLoggedIn: !!user,
        user,
      }));

      if (user) {
        dispatch(setUser(user));
        console.log('✅ Session refreshed successfully');
      } else {
        // Don't automatically log out on session refresh failure
        // This could be due to temporary network issues
        console.log('⚠️ Session refresh failed - keeping user logged in');
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
          // Keep existing login state - be very lenient
          isLoggedIn: true, // Force keep logged in
        }));
      }
    } catch (error) {
      console.error('❌ Session refresh failed:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        isLoggedIn: false,
        user: null,
        error: error instanceof Error ? error.message : 'Session refresh failed',
      }));
      dispatch(logout());
    }
  }, [dispatch]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  /**
   * Handle network connectivity changes
   */
  const handleNetworkChange = useCallback((isConnected: boolean) => {
    if (!isConnected && authState.isLoggedIn) {
      // User is offline but logged in - they can still use the app
      console.log('📡 User is offline but session is maintained');
    } else if (isConnected && !authState.isLoggedIn && authState.isInitialized) {
      // User came back online and is not logged in - check if session can be restored
      console.log('📡 User came back online - checking session');
      refreshSession();
    }
  }, [authState.isLoggedIn, authState.isInitialized, refreshSession]);

  /**
   * Setup network monitoring
   */
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      handleNetworkChange(state.isConnected || false);
    });

    return unsubscribe;
  }, [handleNetworkChange]);

  /**
   * Initialize app on mount
   */
  useEffect(() => {
    initializeApp();
  }, [initializeApp]);

  /**
   * Monitor auth state changes and validate session periodically
   * DISABLED: Frequent checks were causing unnecessary logouts
   */
  useEffect(() => {
    if (!authState.isInitialized) return;

    // Completely disable auth state monitoring to prevent logout loops
    console.log('🔍 Auth state monitoring disabled to prevent logout loops');

    // const checkAuthState = () => {
    //   const currentUser = appInitializationService.getCurrentUser();
    //   const isLoggedIn = appInitializationService.isUserLoggedIn();

    //   if (authState.isLoggedIn !== isLoggedIn) {
    //     console.log('🔍 Auth state changed - updating state');
    //     setAuthState(prev => ({
    //       ...prev,
    //       isLoggedIn,
    //       user: currentUser,
    //     }));

    //     if (currentUser) {
    //       dispatch(setUser(currentUser));
    //     } else {
    //       dispatch(logout());
    //     }
    //   }
    // };

    // Disable frequent auth state checks to prevent constant logouts
    // const interval = setInterval(checkAuthState, 300000);

    return () => {
      // No interval to clear since we disabled it
      // clearInterval(interval);
    };
  }, [authState.isInitialized, authState.isLoggedIn, dispatch]);

  /**
   * Handle app foreground/background state changes
   */
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active' && authState.isInitialized) {
        // App came to foreground - refresh session to ensure it's still valid
        console.log('📱 App came to foreground - validating session');
        refreshSession();
      }
    };

    // Set up AppState listener for foreground/background detection
    const { AppState } = require('react-native');
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [authState.isInitialized, refreshSession]);

  return [
    authState,
    {
      login,
      logout: logoutUser,
      refreshSession,
      clearError,
    },
  ];
};

/**
 * Hook for checking if user is authenticated with persistent session
 */
export const useIsPersistentlyAuthenticated = (): boolean => {
  const [authState] = usePersistentAuth();
  return authState.isLoggedIn && !authState.isLoading && authState.isInitialized;
};

/**
 * Hook for getting current user from persistent session
 */
export const usePersistentUser = (): User | null => {
  const [authState] = usePersistentAuth();
  return authState.user;
};

/**
 * Hook for persistent authentication loading state
 */
export const usePersistentAuthLoading = (): boolean => {
  const [authState] = usePersistentAuth();
  return authState.isLoading;
};

/**
 * Hook for checking if app should show onboarding
 */
export const useShouldShowOnboarding = (): boolean => {
  const [authState] = usePersistentAuth();
  return authState.shouldShowOnboarding && !authState.isLoggedIn;
};
