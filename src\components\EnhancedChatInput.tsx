import { Ionicons } from "@expo/vector-icons";
import * as DocumentPicker from "expo-document-picker";
import * as ImagePicker from "expo-image-picker";
import { useRef, useState } from "react";
import { navigationService } from "../services/navigationService";
import {
  Alert,
  Animated,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import VoiceMessageRecorder from "./VoiceMessageRecorder";

interface EnhancedChatInputProps {
  onSendMessage: (_message: string) => void;
  onSendMedia: (_uri: string, _type: "image" | "video") => void;
  onSendVoice: (_uri: string, _duration: number) => void;
  onSendFile: (_uri: string, _name: string, _type: string) => void;
  placeholder?: string;
  chatId?: string;
}

export default function EnhancedChatInput({
  onSendMessage,
  onSendMedia,
  onSendVoice,
  onSendFile,
  placeholder = "Type a message...",
  chatId,
}: EnhancedChatInputProps) {
  const [message, setMessage] = useState("");
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);

  // Note: chatId is available for future use (e.g., direct message service integration)
  // Currently using callback props for message handling
  console.log("EnhancedChatInput initialized for chat:", chatId);

  const attachmentMenuAnim = useRef(new Animated.Value(0)).current;

  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message.trim());
      setMessage("");
    }
  };

  const toggleAttachmentMenu = () => {
    const toValue = showAttachmentMenu ? 0 : 1;
    setShowAttachmentMenu(!showAttachmentMenu);

    Animated.spring(attachmentMenuAnim, {
      toValue,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const handleCamera = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Camera permission is required to take photos.",
        );
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ["images", "videos"],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        onSendMedia(asset.uri, asset.type === "video" ? "video" : "image");
      }
    } catch (error) {
      console.error("Error taking photo:", error);
      Alert.alert("Error", "Failed to take photo");
    }
    setShowAttachmentMenu(false);
  };

  const handleGallery = () => {
    navigationService.openMediaGallery();
    setShowAttachmentMenu(false);
  };

  const handleDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: "*/*",
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        onSendFile(
          asset.uri,
          asset.name,
          asset.mimeType || "application/octet-stream",
        );
      }
    } catch (error) {
      console.error("Error picking document:", error);
      Alert.alert("Error", "Failed to pick document");
    }
    setShowAttachmentMenu(false);
  };

  const handleLocation = () => {
    Alert.alert("Location Sharing", "Location sharing will be available soon");
    setShowAttachmentMenu(false);
  };

  const handleVoiceMessage = (uri: string, duration: number) => {
    onSendVoice(uri, duration);
    setShowVoiceRecorder(false);
  };

  const AttachmentButton = ({
    icon,
    label,
    onPress,
    color = "#3B82F6",
  }: {
    icon: string;
    label: string;
    onPress: () => void;
    color?: string;
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className="items-center py-3 px-4 bg-white rounded-lg shadow-sm border border-gray-200"
    >
      <View
        className="w-12 h-12 rounded-full items-center justify-center mb-2"
        style={{ backgroundColor: `${color}20` }}
      >
        <Ionicons name={icon as any} size={24} color={color} />
      </View>
      <Text className="text-xs text-gray-600 font-medium">{label}</Text>
    </TouchableOpacity>
  );

  if (showVoiceRecorder) {
    return (
      <VoiceMessageRecorder
        onSendVoiceMessage={handleVoiceMessage}
        onCancel={() => setShowVoiceRecorder(false)}
      />
    );
  }

  return (
    <View className="bg-white border-t border-gray-200">
      {/* Attachment Menu */}
      {showAttachmentMenu && (
        <Animated.View
          style={{
            transform: [
              {
                translateY: attachmentMenuAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [100, 0],
                }),
              },
            ],
            opacity: attachmentMenuAnim,
          }}
          className="px-4 py-3 bg-gray-50 border-b border-gray-200"
        >
          <View className="flex-row justify-around">
            <AttachmentButton
              icon="camera"
              label="Camera"
              onPress={handleCamera}
              color="#10B981"
            />
            <AttachmentButton
              icon="images"
              label="Gallery"
              onPress={handleGallery}
              color="#3B82F6"
            />
            <AttachmentButton
              icon="document"
              label="Document"
              onPress={handleDocument}
              color="#F59E0B"
            />
            <AttachmentButton
              icon="location"
              label="Location"
              onPress={handleLocation}
              color="#EF4444"
            />
          </View>
        </Animated.View>
      )}

      {/* Input Row */}
      <View className="flex-row items-end px-4 py-3">
        {/* Attachment Button */}
        <TouchableOpacity
          onPress={toggleAttachmentMenu}
          className="w-10 h-10 items-center justify-center mr-2"
        >
          <Ionicons
            name={showAttachmentMenu ? "close" : "add"}
            size={24}
            color="#6B7280"
          />
        </TouchableOpacity>

        {/* Text Input */}
        <View className="flex-1 bg-gray-100 rounded-full px-4 py-3 mr-2">
          <TextInput
            value={message}
            onChangeText={setMessage}
            placeholder={placeholder}
            placeholderTextColor="#9CA3AF"
            multiline
            maxLength={1000}
            className="text-gray-800 text-base max-h-32"
            style={{ minHeight: 24, textAlignVertical: 'center' }}
          />
        </View>

        {/* Send/Voice Button */}
        {message.trim() ? (
          <TouchableOpacity
            onPress={handleSend}
            className="w-12 h-12 bg-blue-500 rounded-full items-center justify-center"
            style={{ marginBottom: 2 }}
          >
            <Ionicons name="send" size={22} color="white" />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            onPress={() => setShowVoiceRecorder(true)}
            className="w-12 h-12 bg-blue-500 rounded-full items-center justify-center"
            style={{ marginBottom: 2 }}
          >
            <Ionicons name="mic" size={22} color="white" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
}
