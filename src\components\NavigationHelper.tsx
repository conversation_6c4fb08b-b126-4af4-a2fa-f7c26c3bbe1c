/**
 * 🧭 NAVIGATION HELPER COMPONENT
 * Universal navigation component for seamless routing throughout IraChat
 * Updated with proper IraChat Sky Blue branding and enhanced functionality
 */

import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { navigationService } from '../services/navigationService';

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#FFFFFF',   // White
  surface: '#F8FAFC',      // Very Light Gray
  text: '#2C3E50',         // Dark Blue Gray
  textSecondary: '#5D6D7E', // Medium Blue Gray
  textMuted: '#85929E',    // Light Blue Gray
  border: '#E5E7EB',       // Light Gray
  success: '#32CD32',      // Lime Green
  warning: '#FFA500',      // Orange
  error: '#FF6B6B',        // Light Red
  shadow: 'rgba(0, 0, 0, 0.1)',
  overlay: 'rgba(0, 0, 0, 0.5)',
};

interface NavigationHelperProps {
  currentScreen?: string;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  customActions?: {
    icon: string;
    label: string;
    onPress: () => void;
    color?: string;
  }[];
}

export const NavigationHelper: React.FC<NavigationHelperProps> = ({
  currentScreen,
  showBackButton = true,
  showHomeButton = false,
  customActions = [],
}) => {
  return (
    <View style={styles.container}>
      {/* Current Screen Indicator */}
      {currentScreen && (
        <View style={styles.screenIndicator}>
          <Text style={styles.screenIndicatorText}>{currentScreen}</Text>
        </View>
      )}

      {/* Back Button */}
      {showBackButton && (
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => navigationService.goBack()}
          accessibilityLabel="Go Back"
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.primary} />
          <Text style={styles.navButtonText}>Back</Text>
        </TouchableOpacity>
      )}

      {/* Home Button */}
      {showHomeButton && (
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => navigationService.goHome()}
          accessibilityLabel="Go Home"
        >
          <Ionicons name="home" size={24} color={COLORS.primary} />
          <Text style={styles.navButtonText}>Home</Text>
        </TouchableOpacity>
      )}

      {/* Custom Actions */}
      {customActions.map((action, index) => (
        <TouchableOpacity
          key={index}
          style={styles.navButton}
          onPress={action.onPress}
          accessibilityLabel={action.label}
        >
          <Ionicons
            name={action.icon as any}
            size={24}
            color={action.color || COLORS.primary}
          />
          <Text style={[styles.navButtonText, { color: action.color || COLORS.primary }]}>
            {action.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

// 🚀 QUICK NAVIGATION ACTIONS
export const QuickNavActions = {
  // Chat Actions
  newChat: {
    icon: 'chatbubble-outline',
    label: 'New Chat',
    onPress: () => navigationService.openNewChat(),
  },
  
  createGroup: {
    icon: 'people-outline',
    label: 'Create Group',
    onPress: () => navigationService.createGroup(),
  },

  // Call Actions
  videoCall: (contactId: string, contactName: string) => ({
    icon: 'videocam-outline',
    label: 'Video Call',
    onPress: () => navigationService.startVideoCall(contactId, contactName),
  }),

  voiceCall: (contactId: string, contactName: string) => ({
    icon: 'call-outline',
    label: 'Voice Call',
    onPress: () => navigationService.startVoiceCall(contactId, contactName),
  }),

  // Media Actions
  camera: {
    icon: 'camera-outline',
    label: 'Camera',
    onPress: () => navigationService.openCamera(),
  },

  gallery: {
    icon: 'images-outline',
    label: 'Gallery',
    onPress: () => navigationService.openMediaGallery(),
  },

  // Search Actions
  search: {
    icon: 'search-outline',
    label: 'Search',
    onPress: () => navigationService.openGlobalSearch(),
  },

  contacts: {
    icon: 'people-circle-outline',
    label: 'Contacts',
    onPress: () => navigationService.openContacts(),
  },

  // Settings Actions
  settings: {
    icon: 'settings-outline',
    label: 'Settings',
    onPress: () => navigationService.openSettings(),
  },

  profile: {
    icon: 'person-outline',
    label: 'Profile',
    onPress: () => navigationService.openProfile(),
  },

  // Help Actions
  help: {
    icon: 'help-circle-outline',
    label: 'Help',
    onPress: () => navigationService.openHelp(),
  },

  support: {
    icon: 'headset-outline',
    label: 'Support',
    onPress: () => navigationService.openSupport(),
  },
};

// 📱 FLOATING ACTION BUTTON COMPONENT
interface FloatingActionButtonProps {
  actions: {
    icon: string;
    label: string;
    onPress: () => void;
    color?: string;
    backgroundColor?: string;
  }[];
  mainAction?: {
    icon: string;
    onPress: () => void;
    backgroundColor?: string;
  };
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  actions,
  mainAction,
}) => {
  const [isExpanded, setIsExpanded] = React.useState(false);

  const handleMainPress = () => {
    if (mainAction) {
      mainAction.onPress();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  return (
    <View style={styles.fabContainer}>
      {/* Expanded Actions */}
      {isExpanded && (
        <View style={styles.fabActions}>
          {actions.map((action, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.fabAction,
                { backgroundColor: action.backgroundColor || COLORS.background }
              ]}
              onPress={() => {
                action.onPress();
                setIsExpanded(false);
              }}
              accessibilityLabel={action.label}
            >
              <Ionicons
                name={action.icon as any}
                size={20}
                color={action.color || COLORS.primary}
              />
            </TouchableOpacity>
          ))}
        </View>
      )}

      {/* Main FAB */}
      <TouchableOpacity
        style={[
          styles.fab,
          { backgroundColor: mainAction?.backgroundColor || COLORS.primary }
        ]}
        onPress={handleMainPress}
        accessibilityLabel={mainAction ? "Main Action" : "More Actions"}
      >
        <Ionicons
          name={mainAction?.icon as any || (isExpanded ? 'close' : 'add')}
          size={24}
          color={COLORS.background}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: COLORS.background,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  screenIndicator: {
    backgroundColor: COLORS.primaryLight,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 12,
  },
  screenIndicatorText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.primaryDark,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 16,
    borderRadius: 8,
    backgroundColor: COLORS.surface,
    borderWidth: 1,
    borderColor: COLORS.primaryLight,
  },
  navButtonText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
  },
  fabContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    alignItems: 'center',
  },
  fabActions: {
    marginBottom: 16,
    alignItems: 'center',
  },
  fabAction: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    elevation: 4,
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: COLORS.primaryLight,
  },
  fab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 6,
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    borderWidth: 2,
    borderColor: COLORS.primaryLight,
  },
});

export default NavigationHelper;
