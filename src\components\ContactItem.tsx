import { Ionicons } from "@expo/vector-icons";
import { Image, Text, TouchableOpacity, View } from "react-native";
import { useMemo } from "react";
import { Contact, formatLastSeen } from "../services/contactsService";

interface ContactItemProps {
  contact: Contact;
  onPress: (contact: Contact) => void;
}

export default function ContactItem({ contact: _contact, onPress }: ContactItemProps) {
  // FIXED: Move all hooks to the top before any early returns
  // FIXED: Use proper online status logic with fallbacks
  const isOnline = useMemo(() => {
    if (!_contact) return false;

    // Priority 1: Use explicit isOnline boolean if available
    if (typeof _contact.isOnline === 'boolean') {
      return _contact.isOnline;
    }

    // Priority 2: Fallback to lastSeen-based calculation
    if (_contact.lastSeen) {
      const now = new Date();
      const lastSeenDate = _contact.lastSeen instanceof Date ? _contact.lastSeen : new Date(_contact.lastSeen);
      const diffMs = now.getTime() - lastSeenDate.getTime();
      const diffMins = Math.floor(diffMs / (1000 * 60));
      return diffMins < 1; // Online if last seen less than 1 minute ago
    }

    return false; // Default to offline
  }, [_contact?.isOnline, _contact?.lastSeen]);

  // FIXED: Memoize formatted last seen to avoid duplicate calculations
  const lastSeenText = useMemo(() => {
    if (!_contact?.lastSeen) return '';
    return formatLastSeen(_contact.lastSeen);
  }, [_contact?.lastSeen]);

  // FIXED: Better status/phone display logic
  const secondaryText = useMemo(() => {
    if (!_contact) return '';
    if (_contact.isIraChatUser && _contact.status) {
      return _contact.status;
    }
    return _contact.phoneNumber || _contact.email || "No contact info";
  }, [_contact?.isIraChatUser, _contact?.status, _contact?.phoneNumber, _contact?.email]);

  // FIXED: Add input validation and error handling AFTER hooks
  if (!_contact) {
    console.error('ContactItem: contact prop is required');
    return null;
  }

  if (!_contact.id || !_contact.name) {
    console.error('ContactItem: contact missing required fields', _contact);
    return null;
  }

  // FIXED: Safe name handling with fallback
  const displayName = _contact.name || "Unknown Contact";
  const nameInitial = displayName.charAt(0).toUpperCase() || "?";

  // FIXED: Safe press handler with error handling
  const handlePress = () => {
    try {
      if (onPress && typeof onPress === 'function') {
        onPress(_contact);
      }
    } catch (error) {
      console.error('Error handling contact press:', error);
    }
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 14,
        backgroundColor: '#FFFFFF',
        borderBottomWidth: 1,
        borderBottomColor: '#87CEEB', // Sky blue bottom border
        marginHorizontal: 0,
      }}
      activeOpacity={0.8}
    >
      {/* Avatar with better positioning */}
      <View style={{
        position: 'relative',
        marginRight: 14,
      }}>
        {_contact.avatar ? (
          <Image
            source={{ uri: _contact.avatar }}
            style={{
              width: 48,
              height: 48,
              borderRadius: 24,
            }}
            resizeMode="cover"
            onError={() => {
              // FIXED: Handle avatar loading errors gracefully
              console.warn('Failed to load avatar for contact:', displayName);
            }}
          />
        ) : (
          <View style={{
            width: 48,
            height: 48,
            borderRadius: 24,
            backgroundColor: "#667eea",
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            <Text style={{
              color: 'white',
              fontSize: 18,
              fontWeight: '700',
            }}>
              {nameInitial}
            </Text>
          </View>
        )}

        {/* Online indicator */}
        {isOnline && (
          <View style={{
            position: 'absolute',
            bottom: -2,
            right: -2,
            width: 16,
            height: 16,
            borderRadius: 8,
            backgroundColor: "#10B981",
            borderWidth: 2,
            borderColor: 'white',
          }} />
        )}
      </View>

      {/* Contact Info - Better aligned */}
      <View style={{
        flex: 1,
        justifyContent: 'center',
        paddingRight: 12,
      }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#1F2937',
          marginBottom: 4,
        }}>
          {displayName}
        </Text>

        {/* Username */}
        {_contact.username && (
          <Text style={{
            fontSize: 13,
            color: "#667eea",
            marginBottom: 2,
            fontWeight: '500',
          }}>
            @{_contact.username}
          </Text>
        )}

        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
        }}>
          <Text style={{
            fontSize: 13,
            color: '#6B7280',
            flex: 1,
          }}>
            {secondaryText}
          </Text>

          {/* FIXED: Always show status, with different styling for online/offline */}
          <Text style={{
            fontSize: 11,
            color: isOnline ? '#10B981' : '#9CA3AF',
            fontWeight: isOnline ? '600' : '400',
          }}>
            {isOnline ? 'Online' : lastSeenText}
          </Text>
        </View>
      </View>

      {/* Chat Icon - Better styled */}
      <View style={{
        alignItems: 'center',
        justifyContent: 'center',
      }}>
        <View style={{
          backgroundColor: '#E0F2FE',
          borderRadius: 20,
          padding: 8,
          marginBottom: 4,
        }}>
          <Image
            source={require('../../assets/images/LOGO.png')}
            style={{ width: 18, height: 18, tintColor: '#0EA5E9' }}
            resizeMode="contain"
          />
        </View>
        <Text style={{
          fontSize: 11,
          color: '#0EA5E9',
          fontWeight: '600',
        }}>
          IraChat
        </Text>
      </View>
    </TouchableOpacity>
  );
}
