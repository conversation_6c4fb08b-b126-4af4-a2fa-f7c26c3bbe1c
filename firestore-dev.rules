rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // DEVELOPMENT RULES - More permissive for testing
    // Deploy these rules to fix permission-denied errors
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    // Users collection - allow authenticated users to read/write their own data
    match /users/{userId} {
      allow read: if true; // Allow public read for contact discovery
      allow write, create, update: if isAuthenticated();
      allow delete: if isOwner(userId);
    }
    
    // Updates collection - PERMISSIVE RULES FOR DEVELOPMENT
    match /updates/{updateId} {
      // Allow all authenticated users to read updates
      allow read: if true; // Public read access
      
      // Allow authenticated users to create updates
      allow create: if isAuthenticated();
      
      // Allow authenticated users to update any update (for likes, views, etc.)
      allow update: if isAuthenticated();
      
      // Allow users to delete their own updates
      allow delete: if isAuthenticated();
    }
    
    // Comments on updates
    match /updates/{updateId}/comments/{commentId} {
      allow read: if true;
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // Chats collection - participants can access
    match /chats/{chatId} {
      allow read, write: if isAuthenticated();
      allow create: if isAuthenticated();
    }
    
    // Messages in chats
    match /chats/{chatId}/messages/{messageId} {
      allow read, write: if isAuthenticated();
      allow create: if isAuthenticated();
    }
    
    // Stories collection
    match /stories/{storyId} {
      allow read: if true;
      allow write, create, update: if isAuthenticated();
      allow delete: if isAuthenticated();
    }
    
    // User profiles
    match /userProfiles/{userId} {
      allow read: if true;
      allow write, create, update: if isAuthenticated();
      allow delete: if isOwner(userId);
    }
    
    // Contacts
    match /contacts/{contactId} {
      allow read, write: if isAuthenticated();
    }
    
    // Media uploads
    match /media/{mediaId} {
      allow read: if true;
      allow write, create, update, delete: if isAuthenticated();
    }
    
    // Analytics and tracking (optional)
    match /analytics/{document=**} {
      allow read, write: if isAuthenticated();
    }
    
    // Notifications
    match /notifications/{notificationId} {
      allow read, write: if isAuthenticated();
    }
    
    // Settings
    match /settings/{userId} {
      allow read, write: if isAuthenticated();
    }
    
    // Fallback rule - allow authenticated users access to any collection
    // This is very permissive but good for development
    match /{document=**} {
      allow read: if true; // Public read for most content
      allow write, create, update, delete: if isAuthenticated();
    }
  }
}
