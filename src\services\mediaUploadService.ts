// 📤 REAL MEDIA UPLOAD SERVICE - Actually uploads to Firebase!
// This service handles uploading photos and videos to Firebase Storage

import { getDownloadURL, ref, uploadBytesResumable } from 'firebase/storage';
import { storage } from './firebaseSimple';

export interface UploadProgress {
  progress: number; // 0-100
  bytesTransferred: number;
  totalBytes: number;
}

export interface UploadResult {
  success: boolean;
  downloadURL?: string;
  error?: string;
  metadata?: {
    size: number;
    contentType: string;
    timeCreated: string;
  };
}

class MediaUploadService {
  /**
   * Upload media file to Firebase Storage
   */
  async uploadMedia(
    uri: string,
    userId: string,
    type: 'photo' | 'video',
    onProgress?: (_progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Check Firebase authentication first
      const { auth } = await import('./firebaseSimple');
      if (!auth?.currentUser) {
        return {
          success: false,
          error: 'User not authenticated for media upload',
        };
      }

      // Verify user can upload (matches current user)
      if (auth.currentUser.uid !== userId) {
        return {
          success: false,
          error: 'User can only upload their own media',
        };
      }

      // Convert URI to blob
      const response = await fetch(uri);
      if (!response.ok) {
        return {
          success: false,
          error: 'Failed to fetch media file',
        };
      }

      const blob = await response.blob();
      if (!blob || blob.size === 0) {
        return {
          success: false,
          error: 'Invalid media file',
        };
      }

      // Generate unique filename
      const timestamp = Date.now();
      const extension = type === 'photo' ? 'jpg' : 'mp4';
      const filename = `${type}_${timestamp}.${extension}`;

      // Create storage reference
      const storageRef = ref(storage, `updates/${userId}/${filename}`);

      // Start upload with progress tracking
      const uploadTask = uploadBytesResumable(storageRef, blob);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            // Progress callback
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            console.log(`📤 Upload progress: ${progress.toFixed(1)}%`);
            
            if (onProgress) {
              onProgress({
                progress: Math.round(progress),
                bytesTransferred: snapshot.bytesTransferred,
                totalBytes: snapshot.totalBytes,
              });
            }
          },
          (error) => {
            // Error callback with detailed error handling
            let errorMessage = 'Upload failed';

            if (error.code === 'storage/unauthorized') {
              errorMessage = 'Unauthorized: Check Firebase Storage rules';
            } else if (error.code === 'storage/canceled') {
              errorMessage = 'Upload was canceled';
            } else if (error.code === 'storage/unknown') {
              errorMessage = 'Unknown storage error - check authentication and storage rules';
            } else if (error.code === 'storage/object-not-found') {
              errorMessage = 'File not found';
            } else if (error.code === 'storage/bucket-not-found') {
              errorMessage = 'Storage bucket not found';
            } else if (error.code === 'storage/project-not-found') {
              errorMessage = 'Project not found';
            } else if (error.code === 'storage/quota-exceeded') {
              errorMessage = 'Storage quota exceeded';
            } else if (error.code === 'storage/unauthenticated') {
              errorMessage = 'User not authenticated';
            } else if (error.code === 'storage/retry-limit-exceeded') {
              errorMessage = 'Upload retry limit exceeded';
            } else if (error.code === 'storage/invalid-checksum') {
              errorMessage = 'File checksum mismatch';
            } else if (error.message) {
              errorMessage = error.message;
            }

            reject({
              success: false,
              error: errorMessage,
              code: error.code,
            });
          },
          async () => {
            // Success callback
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              
              console.log('✅ Upload completed:', downloadURL);
              
              resolve({
                success: true,
                downloadURL,
                metadata: {
                  size: uploadTask.snapshot.totalBytes,
                  contentType: uploadTask.snapshot.metadata.contentType || '',
                  timeCreated: uploadTask.snapshot.metadata.timeCreated || new Date().toISOString(),
                },
              });
            } catch (error: any) {
              console.error('❌ Failed to get download URL:', error);
              reject({
                success: false,
                error: 'Failed to get download URL',
              });
            }
          }
        );
      });
    } catch (error: any) {
      console.error('❌ Media upload error:', error);
      return {
        success: false,
        error: error.message || 'Failed to upload media',
      };
    }
  }

  /**
   * Upload profile picture
   */
  async uploadProfilePicture(
    uri: string,
    userId: string,
    onProgress?: (_progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      console.log('📤 Uploading profile picture...', uri);

      const response = await fetch(uri);
      const blob = await response.blob();

      const filename = `profile_${Date.now()}.jpg`;
      const storageRef = ref(storage, `profiles/${userId}/${filename}`);

      const uploadTask = uploadBytesResumable(storageRef, blob);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            if (onProgress) {
              onProgress({
                progress: Math.round(progress),
                bytesTransferred: snapshot.bytesTransferred,
                totalBytes: snapshot.totalBytes,
              });
            }
          },
          (error) => {
            console.error('❌ Profile picture upload failed:', error);
            reject({
              success: false,
              error: error.message || 'Profile picture upload failed',
            });
          },
          async () => {
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              console.log('✅ Profile picture uploaded:', downloadURL);
              
              resolve({
                success: true,
                downloadURL,
                metadata: {
                  size: uploadTask.snapshot.totalBytes,
                  contentType: uploadTask.snapshot.metadata.contentType || '',
                  timeCreated: uploadTask.snapshot.metadata.timeCreated || new Date().toISOString(),
                },
              });
            } catch (_error: any) {
              reject({
                success: false,
                error: 'Failed to get download URL',
              });
            }
          }
        );
      });
    } catch (error: any) {
      console.error('❌ Profile picture upload error:', error);
      return {
        success: false,
        error: error.message || 'Failed to upload profile picture',
      };
    }
  }

  /**
   * Upload chat media (photos/videos in messages)
   */
  async uploadChatMedia(
    uri: string,
    chatId: string,
    userId: string,
    type: 'photo' | 'video',
    onProgress?: (_progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      console.log('📤 Uploading chat media...', { uri, chatId, type });

      const response = await fetch(uri);
      const blob = await response.blob();

      const timestamp = Date.now();
      const extension = type === 'photo' ? 'jpg' : 'mp4';
      const filename = `${type}_${timestamp}.${extension}`;
      
      const storageRef = ref(storage, `chats/${chatId}/media/${filename}`);

      const uploadTask = uploadBytesResumable(storageRef, blob);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            if (onProgress) {
              onProgress({
                progress: Math.round(progress),
                bytesTransferred: snapshot.bytesTransferred,
                totalBytes: snapshot.totalBytes,
              });
            }
          },
          (error) => {
            console.error('❌ Chat media upload failed:', error);
            reject({
              success: false,
              error: error.message || 'Chat media upload failed',
            });
          },
          async () => {
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              console.log('✅ Chat media uploaded:', downloadURL);
              
              resolve({
                success: true,
                downloadURL,
                metadata: {
                  size: uploadTask.snapshot.totalBytes,
                  contentType: uploadTask.snapshot.metadata.contentType || '',
                  timeCreated: uploadTask.snapshot.metadata.timeCreated || new Date().toISOString(),
                },
              });
            } catch (_error: any) {
              reject({
                success: false,
                error: 'Failed to get download URL',
              });
            }
          }
        );
      });
    } catch (error: any) {
      console.error('❌ Chat media upload error:', error);
      return {
        success: false,
        error: error.message || 'Failed to upload chat media',
      };
    }
  }

  /**
   * Get file size from URI
   */
  async getFileSize(uri: string): Promise<number> {
    try {
      const response = await fetch(uri, { method: 'HEAD' });
      const contentLength = response.headers.get('content-length');
      return contentLength ? parseInt(contentLength, 10) : 0;
    } catch (error) {
      console.error('❌ Failed to get file size:', error);
      return 0;
    }
  }

  /**
   * Validate file size (max 50MB for videos, 10MB for photos)
   */
  async validateFileSize(uri: string, type: 'photo' | 'video'): Promise<{ valid: boolean; error?: string }> {
    try {
      const fileSize = await this.getFileSize(uri);
      const maxSize = type === 'video' ? 50 * 1024 * 1024 : 10 * 1024 * 1024; // 50MB for video, 10MB for photo
      
      if (fileSize > maxSize) {
        const maxSizeMB = type === 'video' ? '50MB' : '10MB';
        return {
          valid: false,
          error: `File size exceeds ${maxSizeMB} limit`,
        };
      }
      
      return { valid: true };
    } catch (_error) {
      return {
        valid: false,
        error: 'Failed to validate file size',
      };
    }
  }
}

// Export singleton instance
export const mediaUploadService = new MediaUploadService();
export default mediaUploadService;
