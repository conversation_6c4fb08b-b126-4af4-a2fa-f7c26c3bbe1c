import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { View, ActivityIndicator } from 'react-native';
import privacyLockService, { LockType, LockConfig } from '../services/privacyLockService';

interface PrivacyLockContextType {
  isLocked: boolean;
  lockConfig: LockConfig;
  isLoading: boolean;
  unlockApp: (credential?: string) => Promise<boolean>;
  lockApp: () => Promise<void>;
  refreshLockState: () => Promise<void>;
  isSystemAuthAvailable: boolean;
}

const PrivacyLockContext = createContext<PrivacyLockContextType | undefined>(undefined);

interface PrivacyLockProviderProps {
  children: ReactNode;
}

export const PrivacyLockProvider: React.FC<PrivacyLockProviderProps> = ({ children }) => {
  console.log('🔐 PrivacyLockProvider: Component created');

  const [isLocked, setIsLocked] = useState(false);
  const [lockConfig, setLockConfig] = useState<LockConfig>({
    lockType: LockType.NONE,
    autoLockDuration: 300000, // 5 minutes
    useSystemAuth: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSystemAuthAvailable, setIsSystemAuthAvailable] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    console.log('🔐 PrivacyLockProvider: Initializing...');
    initializePrivacyLock();
    setupLockListener();

    return () => {
      console.log('🔐 PrivacyLockProvider: Cleaning up...');
      // Cleanup is handled by the service
    };
  }, []);

  const initializePrivacyLock = async () => {
    try {
      setIsLoading(true);
      
      // Load current configuration
      const config = await privacyLockService.getLockConfig();
      setLockConfig(config);
      
      // Check if app is currently locked
      const locked = await privacyLockService.isLocked();
      setIsLocked(locked);
      
      // Check system auth availability
      const systemAuthAvailable = await privacyLockService.isSystemAuthAvailable();
      setIsSystemAuthAvailable(systemAuthAvailable);
      
      console.log('🔐 Privacy lock initialized:', {
        lockType: config.lockType,
        isLocked: locked,
        systemAuthAvailable
      });
    } catch (error) {
      console.error('❌ Error initializing privacy lock:', error);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  };

  const setupLockListener = () => {
    // Listen for lock state changes from the service
    const unsubscribe = privacyLockService.addLockListener((locked: boolean) => {
      console.log('🔐 Lock state changed:', locked);
      setIsLocked(locked);
    });

    return unsubscribe;
  };

  const unlockApp = async (credential?: string): Promise<boolean> => {
    try {
      let success = false;
      
      if (lockConfig.lockType === LockType.SYSTEM) {
        success = await privacyLockService.authenticateWithSystem();
      } else if (credential) {
        success = await privacyLockService.unlockApp(credential);
      }
      
      if (success) {
        setIsLocked(false);
        console.log('🔓 App unlocked successfully');
      }
      
      return success;
    } catch (error) {
      console.error('❌ Error unlocking app:', error);
      return false;
    }
  };

  const lockApp = async (): Promise<void> => {
    try {
      await privacyLockService.lockApp();
      setIsLocked(true);
      console.log('🔒 App locked successfully');
    } catch (error) {
      console.error('❌ Error locking app:', error);
    }
  };

  const refreshLockState = async (): Promise<void> => {
    try {
      const config = await privacyLockService.getLockConfig();
      setLockConfig(config);
      
      const locked = await privacyLockService.isLocked();
      setIsLocked(locked);
      
      const systemAuthAvailable = await privacyLockService.isSystemAuthAvailable();
      setIsSystemAuthAvailable(systemAuthAvailable);
    } catch (error) {
      console.error('❌ Error refreshing lock state:', error);
    }
  };

  const contextValue: PrivacyLockContextType = {
    isLocked,
    lockConfig,
    isLoading,
    unlockApp,
    lockApp,
    refreshLockState,
    isSystemAuthAvailable,
  };

  // Show loading screen while initializing
  if (!isInitialized) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#667eea' }}>
        <ActivityIndicator size="large" color="#FFFFFF" />
      </View>
    );
  }

  return (
    <PrivacyLockContext.Provider value={contextValue}>
      {children}
    </PrivacyLockContext.Provider>
  );
};

export const usePrivacyLock = (): PrivacyLockContextType => {
  const context = useContext(PrivacyLockContext);
  if (context === undefined) {
    console.error('❌ usePrivacyLock must be used within a PrivacyLockProvider');
    console.error('❌ Current component tree might not have PrivacyLockProvider wrapped properly');
    console.error('❌ Providing fallback values to prevent crash');

    // Return fallback values to prevent app crash
    return {
      isLocked: false,
      lockConfig: {
        lockType: LockType.NONE,
        autoLockDuration: 300000,
        useSystemAuth: false,
      },
      isLoading: false,
      unlockApp: async () => false,
      lockApp: async () => {},
      refreshLockState: async () => {},
      isSystemAuthAvailable: false,
    };
  }
  return context;
};

export default PrivacyLockContext;
