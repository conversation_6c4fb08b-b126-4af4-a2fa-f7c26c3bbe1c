import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
  Modal,
  SafeAreaView,
  Share,
  Linking,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BusinessProfile, BusinessPost } from '../../types/Business';
import { useTheme } from '../../contexts/ThemeContext';
import { businessService } from '../../services/businessService';

const { width: screenWidth } = Dimensions.get('window');

interface BusinessProfilePageProps {
  visible: boolean;
  businessProfile: BusinessProfile;
  onClose: () => void;
  onPostPress: (post: BusinessPost) => void;
  isOwner?: boolean;
  onEditBusiness?: () => void;
  onDeleteBusiness?: (businessId: string) => void;
  onAddProduct?: () => void;
}

export const BusinessProfilePage: React.FC<BusinessProfilePageProps> = ({
  visible,
  businessProfile,
  onClose,
  onPostPress,
  isOwner = false,
  onEditBusiness,
  onDeleteBusiness,
  onAddProduct,
}) => {
  const { colors: COLORS } = useTheme();
  const [businessPosts, setBusinessPosts] = useState<BusinessPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showOptionsMenu, setShowOptionsMenu] = useState(false);
  const [showFullScreenImage, setShowFullScreenImage] = useState(false);
  const [fullScreenImageUri, setFullScreenImageUri] = useState<string>('');

  const handleDeleteBusiness = async () => {
    if (onDeleteBusiness) {
      onDeleteBusiness(businessProfile.id);
    }
  };

  const handleShareBusiness = async () => {
    try {
      // Create business link (you can customize this URL structure)
      const businessLink = `https://irachat.app/business/${businessProfile.id}`;

      // Create share content
      const shareContent = {
        title: `Check out ${businessProfile.businessName} on IraChat!`,
        message: `🏢 ${businessProfile.businessName}\n${businessProfile.businessType}\n\n${businessProfile.description || 'Discover amazing products and services!'}\n\n📱 View on IraChat: ${businessLink}`,
        url: businessLink,
      };

      // Show share options
      Alert.alert(
        'Share Business',
        'How would you like to share this business?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Share Link',
            onPress: async () => {
              try {
                const result = await Share.share({
                  message: shareContent.message,
                  url: shareContent.url,
                  title: shareContent.title,
                });

                if (result.action === Share.sharedAction) {
                  Alert.alert('Success', 'Business link shared successfully!');
                }
              } catch (error) {
                console.error('Error sharing business link:', error);
                Alert.alert('Error', 'Failed to share business link');
              }
            }
          },
          {
            text: 'Copy Link',
            onPress: async () => {
              try {
                // Copy to clipboard (you'll need to install @react-native-clipboard/clipboard)
                // For now, we'll show the link in an alert
                Alert.alert(
                  'Business Link',
                  businessLink,
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Open Link',
                      onPress: () => {
                        Linking.openURL(businessLink).catch(() => {
                          Alert.alert('Error', 'Could not open link');
                        });
                      }
                    }
                  ]
                );
              } catch (error) {
                console.error('Error copying business link:', error);
                Alert.alert('Error', 'Failed to copy business link');
              }
            }
          },
          {
            text: 'Share In-App',
            onPress: () => {
              // TODO: Implement in-app sharing functionality
              Alert.alert('Coming Soon', 'In-app sharing will be available soon!');
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error in handleShareBusiness:', error);
      Alert.alert('Error', 'Failed to share business');
    }
  };

  useEffect(() => {
    if (visible && businessProfile) {
      loadBusinessPosts();
    }
  }, [visible, businessProfile]);

  const loadBusinessPosts = async () => {
    try {
      setIsLoading(true);
      const posts = await businessService.getBusinessPosts(businessProfile.id);
      setBusinessPosts(posts);
    } catch (error) {
      console.error('Error loading business posts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAction = (action: string) => {
    switch (action) {
      case 'hide':
        Alert.alert('Hide Business', 'This business will be hidden from your feed.', [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Hide', onPress: () => console.log('Hide business') }
        ]);
        break;
      case 'block':
        Alert.alert('Block Business', 'This business will be blocked and you won\'t see their posts.', [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Block', style: 'destructive', onPress: () => console.log('Block business') }
        ]);
        break;
      case 'not_interested':
        Alert.alert('Not Interested', 'We\'ll show you fewer posts like this.', [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Confirm', onPress: () => console.log('Not interested') }
        ]);
        break;
      case 'report':
        Alert.alert('Report Business', 'Report this business for inappropriate content or behavior.', [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Report', style: 'destructive', onPress: () => console.log('Report business') }
        ]);
        break;
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: COLORS.background,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: COLORS.text,
    },
    coverPhoto: {
      width: '100%',
      height: 200,
      backgroundColor: COLORS.surface,
    },
    coverPhotoPlaceholder: {
      justifyContent: 'center',
      alignItems: 'center',
    },
    placeholderText: {
      fontSize: 14,
      color: COLORS.textSecondary,
      marginTop: 8,
    },
    profileSection: {
      padding: 16,
      alignItems: 'center',
      marginTop: -50,
    },
    profilePhoto: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: COLORS.surface,
      borderWidth: 4,
      borderColor: COLORS.background,
    },
    businessName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: COLORS.text,
      marginTop: 12,
      textAlign: 'center',
    },
    businessType: {
      fontSize: 16,
      color: COLORS.primary,
      marginTop: 4,
      textTransform: 'uppercase',
      fontWeight: '600',
    },
    businessDescription: {
      fontSize: 14,
      color: COLORS.textSecondary,
      marginTop: 8,
      textAlign: 'center',
      lineHeight: 20,
    },
    contactSection: {
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
    },
    contactSectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: COLORS.text,
      marginBottom: 12,
    },
    contactItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 12,
      paddingHorizontal: 12,
      marginVertical: 4,
      backgroundColor: COLORS.surface,
      borderRadius: 8,
      gap: 12,
    },
    contactText: {
      flex: 1,
      fontSize: 14,
      color: COLORS.text,
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
    },
    statItem: {
      alignItems: 'center',
    },
    statNumber: {
      fontSize: 20,
      fontWeight: 'bold',
      color: COLORS.text,
    },
    statLabel: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginTop: 4,
    },
    actionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
    },
    actionButton: {
      alignItems: 'center',
      paddingVertical: 8,
      paddingHorizontal: 16,
      borderRadius: 8,
      backgroundColor: COLORS.surface,
    },
    primaryActionButton: {
      backgroundColor: COLORS.primary,
    },
    actionButtonText: {
      fontSize: 12,
      color: COLORS.text,
      marginTop: 4,
    },
    primaryActionButtonText: {
      color: '#fff',
    },
    postsSection: {
      flex: 1,
      padding: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: COLORS.text,
      marginBottom: 16,
    },
    postsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    postGridItem: {
      width: (screenWidth - 48) / 2,
      height: 150,
      marginBottom: 16,
      borderRadius: 8,
      overflow: 'hidden',
      backgroundColor: COLORS.surface,
    },
    postImage: {
      width: '100%',
      height: 100,
      backgroundColor: COLORS.surface,
    },
    postInfo: {
      padding: 8,
      flex: 1,
    },
    postTitle: {
      fontSize: 12,
      fontWeight: '600',
      color: COLORS.text,
    },
    postPrice: {
      fontSize: 10,
      color: COLORS.primary,
      fontWeight: 'bold',
      marginTop: 4,
    },
    emptyState: {
      alignItems: 'center',
      paddingVertical: 40,
    },
    emptyStateText: {
      fontSize: 16,
      color: COLORS.textSecondary,
      marginTop: 8,
    },
    // Menu styles
    menuOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-start',
      alignItems: 'flex-end',
      paddingTop: 60,
      paddingRight: 16,
    },
    menuContainer: {
      backgroundColor: COLORS.background,
      borderRadius: 12,
      paddingVertical: 8,
      minWidth: 200,
      maxWidth: 280,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 5,
      borderWidth: 1,
      borderColor: COLORS.border,
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      gap: 12,
    },
    menuItemText: {
      fontSize: 16,
      color: COLORS.text,
      flex: 1,
    },
    menuSeparator: {
      height: 1,
      backgroundColor: COLORS.border,
      marginVertical: 4,
    },
    // Full screen image styles
    fullScreenContainer: {
      flex: 1,
      backgroundColor: '#000000',
      justifyContent: 'center',
      alignItems: 'center',
    },
    fullScreenCloseButton: {
      position: 'absolute',
      top: 50,
      right: 20,
      zIndex: 1000,
      padding: 10,
      borderRadius: 20,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    fullScreenScrollView: {
      flex: 1,
      width: '100%',
    },
    fullScreenContent: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    fullScreenImage: {
      width: screenWidth,
      height: screenWidth,
      maxWidth: '100%',
      maxHeight: '100%',
    },
  });

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="arrow-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Business Profile</Text>
          <TouchableOpacity onPress={() => setShowOptionsMenu(true)}>
            <Ionicons name="ellipsis-horizontal" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>

        <ScrollView style={{ flex: 1 }}>
          {/* Cover Photo - Always Show Section */}
          <TouchableOpacity
            onPress={() => {
              if (businessProfile.coverPhoto) {
                setFullScreenImageUri(businessProfile.coverPhoto);
                setShowFullScreenImage(true);
              }
            }}
            disabled={!businessProfile.coverPhoto}
          >
            <Image
              source={businessProfile.coverPhoto ? { uri: businessProfile.coverPhoto } : undefined}
              style={styles.coverPhoto}
            />
          </TouchableOpacity>

          {/* Profile Section */}
          <View style={styles.profileSection}>
            {/* Profile Photo (Business Logo) - Clickable */}
            <TouchableOpacity
              onPress={() => {
                if (businessProfile.logo) {
                  setFullScreenImageUri(businessProfile.logo);
                  setShowFullScreenImage(true);
                }
              }}
              disabled={!businessProfile.logo}
            >
              <Image
                source={businessProfile.logo ? { uri: businessProfile.logo } : undefined}
                style={styles.profilePhoto}
              />
            </TouchableOpacity>
            <Text style={styles.businessName}>{businessProfile.businessName}</Text>
            <Text style={styles.businessType}>{businessProfile.businessType}</Text>
            {businessProfile.description && (
              <Text style={styles.businessDescription}>{businessProfile.description}</Text>
            )}
          </View>

          {/* Contact Information Section */}
          <View style={styles.contactSection}>
            <Text style={styles.contactSectionTitle}>Contact Information</Text>

            {/* Email */}
            {businessProfile.contactInfo.email && (
              <TouchableOpacity
                style={styles.contactItem}
                onPress={() => {
                  Linking.openURL(`mailto:${businessProfile.contactInfo.email}`);
                }}
              >
                <Ionicons name="mail" size={20} color={COLORS.primary} />
                <Text style={styles.contactText}>{businessProfile.contactInfo.email}</Text>
                <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
              </TouchableOpacity>
            )}

            {/* Primary Phone */}
            {businessProfile.contactInfo.primaryPhone && (
              <TouchableOpacity
                style={styles.contactItem}
                onPress={() => {
                  const phoneNumber = businessProfile.contactInfo.primaryPhone.replace(/[^\d+]/g, '');
                  Linking.openURL(`tel:${phoneNumber}`);
                }}
              >
                <Ionicons name="call" size={20} color={COLORS.success} />
                <Text style={styles.contactText}>{businessProfile.contactInfo.primaryPhone}</Text>
                <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
              </TouchableOpacity>
            )}

            {/* Secondary Phone */}
            {businessProfile.contactInfo.secondaryPhone && (
              <TouchableOpacity
                style={styles.contactItem}
                onPress={() => {
                  const phoneNumber = businessProfile.contactInfo.secondaryPhone!.replace(/[^\d+]/g, '');
                  Linking.openURL(`tel:${phoneNumber}`);
                }}
              >
                <Ionicons name="call-outline" size={20} color={COLORS.success} />
                <Text style={styles.contactText}>{businessProfile.contactInfo.secondaryPhone}</Text>
                <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
              </TouchableOpacity>
            )}

            {/* WhatsApp */}
            {businessProfile.contactInfo.whatsappNumber && (
              <TouchableOpacity
                style={styles.contactItem}
                onPress={() => {
                  const whatsappNumber = businessProfile.contactInfo.whatsappNumber!.replace(/[^\d+]/g, '');
                  Linking.openURL(`https://wa.me/${whatsappNumber}`);
                }}
              >
                <Ionicons name="logo-whatsapp" size={20} color="#25D366" />
                <Text style={styles.contactText}>{businessProfile.contactInfo.whatsappNumber}</Text>
                <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
              </TouchableOpacity>
            )}

            {/* Website */}
            {businessProfile.website && (
              <TouchableOpacity
                style={styles.contactItem}
                onPress={() => {
                  let url = businessProfile.website!;
                  if (!url.startsWith('http://') && !url.startsWith('https://')) {
                    url = `https://${url}`;
                  }
                  Linking.openURL(url);
                }}
              >
                <Ionicons name="globe" size={20} color={COLORS.primary} />
                <Text style={styles.contactText}>{businessProfile.website}</Text>
                <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
              </TouchableOpacity>
            )}

            {/* Location */}
            {businessProfile.location.address && (
              <TouchableOpacity
                style={styles.contactItem}
                onPress={() => {
                  const address = `${businessProfile.location.address}, ${businessProfile.location.city}, ${businessProfile.location.district}`;
                  const encodedAddress = encodeURIComponent(address);
                  Linking.openURL(`https://maps.google.com/?q=${encodedAddress}`);
                }}
              >
                <Ionicons name="location" size={20} color={COLORS.error} />
                <Text style={styles.contactText}>
                  {businessProfile.location.address}
                  {businessProfile.location.city && `, ${businessProfile.location.city}`}
                  {businessProfile.location.district && `, ${businessProfile.location.district}`}
                </Text>
                <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
              </TouchableOpacity>
            )}

            {/* Social Media Links */}
            {businessProfile.socialMedia && (
              <>
                {/* Facebook */}
                {businessProfile.socialMedia.facebook && (
                  <TouchableOpacity
                    style={styles.contactItem}
                    onPress={() => {
                      let url = businessProfile.socialMedia!.facebook!;
                      if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = `https://${url}`;
                      }
                      Linking.openURL(url);
                    }}
                  >
                    <Ionicons name="logo-facebook" size={20} color="#1877F2" />
                    <Text style={styles.contactText}>Facebook</Text>
                    <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
                  </TouchableOpacity>
                )}

                {/* Instagram */}
                {businessProfile.socialMedia.instagram && (
                  <TouchableOpacity
                    style={styles.contactItem}
                    onPress={() => {
                      let url = businessProfile.socialMedia!.instagram!;
                      if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = `https://${url}`;
                      }
                      Linking.openURL(url);
                    }}
                  >
                    <Ionicons name="logo-instagram" size={20} color="#E4405F" />
                    <Text style={styles.contactText}>Instagram</Text>
                    <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
                  </TouchableOpacity>
                )}

                {/* Twitter */}
                {businessProfile.socialMedia.twitter && (
                  <TouchableOpacity
                    style={styles.contactItem}
                    onPress={() => {
                      let url = businessProfile.socialMedia!.twitter!;
                      if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = `https://${url}`;
                      }
                      Linking.openURL(url);
                    }}
                  >
                    <Ionicons name="logo-twitter" size={20} color="#1DA1F2" />
                    <Text style={styles.contactText}>Twitter</Text>
                    <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
                  </TouchableOpacity>
                )}

                {/* LinkedIn */}
                {businessProfile.socialMedia.linkedin && (
                  <TouchableOpacity
                    style={styles.contactItem}
                    onPress={() => {
                      let url = businessProfile.socialMedia!.linkedin!;
                      if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = `https://${url}`;
                      }
                      Linking.openURL(url);
                    }}
                  >
                    <Ionicons name="logo-linkedin" size={20} color="#0A66C2" />
                    <Text style={styles.contactText}>LinkedIn</Text>
                    <Ionicons name="open-outline" size={16} color={COLORS.textSecondary} />
                  </TouchableOpacity>
                )}
              </>
            )}
          </View>

          {/* Statistics */}
          <View style={styles.statsContainer}>
            <TouchableOpacity style={styles.statItem}>
              <Ionicons name="grid-outline" size={20} color={COLORS.primary} />
              <Text style={styles.statNumber}>{businessProfile.totalPosts || 0}</Text>
              <Text style={styles.statLabel}>Posts</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.statItem}>
              <Ionicons name="eye-outline" size={20} color={COLORS.primary} />
              <Text style={styles.statNumber}>{businessProfile.totalViews || 0}</Text>
              <Text style={styles.statLabel}>Views</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.statItem}>
              <Ionicons name="heart-outline" size={20} color={COLORS.primary} />
              <Text style={styles.statNumber}>{businessProfile.totalLikes || 0}</Text>
              <Text style={styles.statLabel}>Likes</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.statItem}>
              <Ionicons name="share-outline" size={20} color={COLORS.primary} />
              <Text style={styles.statNumber}>{businessProfile.totalShares || 0}</Text>
              <Text style={styles.statLabel}>Shares</Text>
            </TouchableOpacity>
          </View>



          {/* Posts Grid */}
          <View style={styles.postsSection}>
            <Text style={styles.sectionTitle}>Posts ({businessPosts.length})</Text>
            {businessPosts.length > 0 ? (
              <View style={styles.postsGrid}>
                {businessPosts.map((post) => (
                  <TouchableOpacity
                    key={post.id}
                    style={styles.postGridItem}
                    onPress={() => onPostPress(post)}
                  >
                    <Image
                      source={post.media?.[0] ? { uri: post.media[0].url } : undefined}
                      style={styles.postImage}
                    />
                    <View style={styles.postInfo}>
                      <Text style={styles.postTitle}>{post.title}</Text>
                      {post.price && (
                        <Text style={styles.postPrice}>
                          {post.currency} {post.price.toLocaleString()}
                        </Text>
                      )}
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            ) : (
              <View style={styles.emptyState}>
                <Ionicons name="grid-outline" size={48} color={COLORS.textSecondary} />
                <Text style={styles.emptyStateText}>No posts yet</Text>
              </View>
            )}
          </View>
        </ScrollView>

        {/* Options Menu Modal */}
        <Modal
          visible={showOptionsMenu}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowOptionsMenu(false)}
        >
          <TouchableOpacity
            style={styles.menuOverlay}
            activeOpacity={1}
            onPress={() => setShowOptionsMenu(false)}
          >
            <View style={styles.menuContainer}>
              {isOwner ? (
                <>
                  {/* Owner Options */}
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowOptionsMenu(false);
                      if (onEditBusiness) {
                        onEditBusiness();
                      }
                    }}
                  >
                    <Ionicons name="create-outline" size={20} color={COLORS.text} />
                    <Text style={styles.menuItemText}>Edit Profile</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowOptionsMenu(false);
                      if (onAddProduct) {
                        onAddProduct();
                      }
                    }}
                  >
                    <Ionicons name="add-circle-outline" size={20} color={COLORS.text} />
                    <Text style={styles.menuItemText}>Add Product</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowOptionsMenu(false);
                      handleShareBusiness();
                    }}
                  >
                    <Ionicons name="share-outline" size={20} color={COLORS.text} />
                    <Text style={styles.menuItemText}>Share Business</Text>
                  </TouchableOpacity>



                  <View style={styles.menuSeparator} />

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowOptionsMenu(false);
                      Alert.alert(
                        'Delete Business',
                        `Are you sure you want to delete "${businessProfile.businessName}"? This action cannot be undone and will permanently delete all business posts, statistics, and information.`,
                        [
                          { text: 'Cancel', style: 'cancel' },
                          {
                            text: 'Delete',
                            style: 'destructive',
                            onPress: () => handleDeleteBusiness()
                          }
                        ]
                      );
                    }}
                  >
                    <Ionicons name="trash-outline" size={20} color={COLORS.error} />
                    <Text style={[styles.menuItemText, { color: COLORS.error }]}>Delete Business</Text>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  {/* Visitor Options */}
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowOptionsMenu(false);
                      handleAction('hide');
                    }}
                  >
                    <Ionicons name="eye-off-outline" size={20} color={COLORS.text} />
                    <Text style={styles.menuItemText}>Hide Business</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowOptionsMenu(false);
                      handleAction('not_interested');
                    }}
                  >
                    <Ionicons name="thumbs-down-outline" size={20} color={COLORS.text} />
                    <Text style={styles.menuItemText}>Not Interested</Text>
                  </TouchableOpacity>

                  <View style={styles.menuSeparator} />

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowOptionsMenu(false);
                      handleAction('report');
                    }}
                  >
                    <Ionicons name="flag-outline" size={20} color={COLORS.error} />
                    <Text style={[styles.menuItemText, { color: COLORS.error }]}>Report Business</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowOptionsMenu(false);
                      handleAction('block');
                    }}
                  >
                    <Ionicons name="ban-outline" size={20} color={COLORS.error} />
                    <Text style={[styles.menuItemText, { color: COLORS.error }]}>Block Business</Text>
                  </TouchableOpacity>
                </>
              )}
            </View>
          </TouchableOpacity>
        </Modal>

        {/* Full Screen Image Modal */}
        <Modal
          visible={showFullScreenImage}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowFullScreenImage(false)}
        >
          <View style={styles.fullScreenContainer}>
            <TouchableOpacity
              style={styles.fullScreenCloseButton}
              onPress={() => setShowFullScreenImage(false)}
            >
              <Ionicons name="close" size={30} color="#FFFFFF" />
            </TouchableOpacity>
            <ScrollView
              style={styles.fullScreenScrollView}
              contentContainerStyle={styles.fullScreenContent}
              maximumZoomScale={3}
              minimumZoomScale={1}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
            >
              <Image
                source={{ uri: fullScreenImageUri }}
                style={styles.fullScreenImage}
                resizeMode="contain"
              />
            </ScrollView>
          </View>
        </Modal>
      </SafeAreaView>
    </Modal>
  );
};
