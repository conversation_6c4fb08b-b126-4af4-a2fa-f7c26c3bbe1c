import { Alert, Animated, Platform, ToastAndroid, Dimensions } from "react-native";
import { shake } from "./animations";

// Get device dimensions for responsive error handling
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

// Enhanced error severity levels
export type ErrorSeverity = "critical" | "error" | "warning" | "info" | "debug";

// Error categories for better handling
export type ErrorCategory =
  | "network"
  | "auth"
  | "storage"
  | "validation"
  | "permission"
  | "media"
  | "sync"
  | "offline"
  | "unknown";

// Error context interface
export interface ErrorContext {
  userId?: string;
  action?: string;
  component?: string;
  timestamp?: number;
  isOffline?: boolean;
  retryCount?: number;
  field?: string;
  required?: string;
  groupId?: string;
  chatId?: string;
  messageId?: string;
  isBlocked?: boolean;
  fileSize?: number;
  maxSize?: number;
  fileType?: string;
  allowedTypes?: string[];
  width?: number;
  height?: number;
  maxDimension?: number;
  maxVideoSize?: number;
  maxAudioSize?: number;
  codeLength?: number;
  invalidCode?: string;
  additionalData?: any;
}

// Enhanced AppError class with more context
export class AppError extends Error {
  public readonly timestamp: number;
  public readonly isRetryable: boolean;
  public readonly context: ErrorContext;

  constructor(
    message: string,
    public readonly code: string,
    public readonly severity: ErrorSeverity = "error",
    public readonly category: ErrorCategory = "unknown",
    context: ErrorContext = {},
    isRetryable: boolean = false
  ) {
    super(message);
    this.name = "AppError";
    this.timestamp = Date.now();
    this.isRetryable = isRetryable;
    this.context = {
      timestamp: this.timestamp,
      ...context
    };
  }

  // Create a user-friendly message
  getUserMessage(): string {
    if (this.context.isOffline) {
      return `${this.message} (Offline mode)`;
    }
    return this.message;
  }

  // Check if error should be logged
  shouldLog(): boolean {
    return this.severity !== "debug" && this.severity !== "info";
  }

  // Convert to JSON for logging/storage
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      category: this.category,
      timestamp: this.timestamp,
      isRetryable: this.isRetryable,
      context: this.context,
      stack: this.stack
    };
  }
}

// Offline error logging
/**
 * Log error to offline storage for later analysis
 */
const logErrorOffline = async (error: AppError): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync(`
      INSERT INTO error_logs (
        id, message, code, severity, category, timestamp,
        context, stack, isRetryable, handled
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      `error_${error.timestamp}_${Math.random().toString(36).substring(2, 8)}`,
      error.message,
      error.code,
      error.severity,
      error.category,
      error.timestamp,
      JSON.stringify(error.context),
      error.stack || '',
      error.isRetryable ? 1 : 0,
      1 // marked as handled
    ]);
  } catch (logError) {
    // Logging failed - continue without logging
  }
};

/**
 * Enhanced error handler with offline support and mobile optimization
 */
export const handleError = async (
  error: Error | AppError,
  shakeAnimation?: Animated.Value,
  context?: ErrorContext
): Promise<void> => {
  // Convert to AppError if needed
  const appError = error instanceof AppError
    ? error
    : new AppError(
        error.message || "An unexpected error occurred",
        "UNEXPECTED_ERROR",
        "error",
        "unknown",
        context
      );

  // Log error offline if needed
  if (appError.shouldLog()) {
    await logErrorOffline(appError);
  }

  // Get responsive message based on device size
  const getDisplayMessage = (message: string): string => {
    if (isSmallDevice && message.length > 60) {
      return message.substring(0, 57) + "...";
    }
    return message;
  };

  // Get appropriate title based on severity
  const getTitle = (severity: ErrorSeverity): string => {
    switch (severity) {
      case "critical": return isSmallDevice ? "Critical" : "Critical Error";
      case "error": return "Error";
      case "warning": return "Warning";
      case "info": return "Info";
      case "debug": return "Debug";
      default: return "Error";
    }
  };

  // Handle different severity levels
  switch (appError.severity) {
    case "critical":
    case "error":
      const errorMessage = getDisplayMessage(appError.getUserMessage());
      if (Platform.OS === "android") {
        ToastAndroid.show(errorMessage, ToastAndroid.LONG);
      } else {
        Alert.alert(getTitle(appError.severity), errorMessage, [
          { text: "OK" },
          ...(appError.isRetryable ? [{ text: "Retry", onPress: () => retryAction(appError) }] : [])
        ]);
      }
      if (shakeAnimation) {
        shake(shakeAnimation).start();
      }
      break;

    case "warning":
      const warningMessage = getDisplayMessage(appError.getUserMessage());
      if (Platform.OS === "android") {
        ToastAndroid.show(warningMessage, ToastAndroid.SHORT);
      } else {
        Alert.alert(getTitle(appError.severity), warningMessage);
      }
      break;

    case "info":
      const infoMessage = getDisplayMessage(appError.getUserMessage());
      if (Platform.OS === "android") {
        ToastAndroid.show(infoMessage, ToastAndroid.SHORT);
      } else {
        Alert.alert(getTitle(appError.severity), infoMessage);
      }
      break;

    case "debug":
      // Debug messages are only logged, not shown to user
      break;
  }
};

/**
 * Retry action for retryable errors
 */
const retryAction = async (error: AppError): Promise<void> => {
  try {
    // This would integrate with the specific action that failed
    const retryCount = (error.context.retryCount || 0) + 1;

    // Prevent infinite retries
    if (retryCount > 3) {
      throw new AppError(
        "Maximum retry attempts reached",
        "MAX_RETRIES_EXCEEDED",
        "error",
        error.category,
        { ...error.context, retryCount }
      );
    }

    // Update context with retry count
    error.context.retryCount = retryCount;

    // Could trigger the original action here based on error category
    switch (error.category) {
      case "network":
        // Retry network operation
        break;
      case "sync":
        // Retry sync operation
        break;
      default:
        throw new AppError(
          "Retry not supported for this error type",
          "RETRY_NOT_SUPPORTED",
          "warning"
        );
    }
  } catch (retryError) {
    // Handle retry failure
    await handleError(retryError as Error, undefined, {
      ...error.context,
      retryCount: (error.context.retryCount || 0) + 1
    });
  }
};

/**
 * Enhanced error boundary creator
 */
export const createErrorBoundary = (error: Error, context?: ErrorContext) => {
  return new AppError(
    error.message || "An unexpected error occurred",
    "UNEXPECTED_ERROR",
    "critical",
    "unknown",
    context,
    false
  );
};

/**
 * Create network error with offline context
 */
export const createNetworkError = (message: string, isOffline: boolean = false) => {
  return new AppError(
    isOffline ? `${message} (Offline mode)` : message,
    "NETWORK_ERROR",
    "error",
    "network",
    { isOffline },
    true // Network errors are retryable
  );
};

/**
 * Create validation error
 */
export const createValidationError = (message: string, field?: string) => {
  return new AppError(
    message,
    "VALIDATION_ERROR",
    "warning",
    "validation",
    { field }
  );
};

/**
 * Create permission error
 */
export const createPermissionError = (action: string, required?: string) => {
  return new AppError(
    `Permission denied for action: ${action}${required ? `. Required: ${required}` : ''}`,
    "PERMISSION_DENIED",
    "error",
    "permission",
    { action, required }
  );
};

/**
 * Enhanced group action validation with better error context
 */
export const validateGroupAction = (
  action: string,
  isAdmin: boolean,
  isBlocked: boolean,
  userId?: string,
  groupId?: string
) => {
  if (isBlocked) {
    throw new AppError(
      "You are blocked from performing actions in this group",
      "BLOCKED_USER",
      "error",
      "permission",
      { action, userId, groupId, isBlocked: true }
    );
  }

  const adminOnlyActions = [
    "lock", "unlock", "block", "unblock", "deleteMessage",
    "removeUser", "promoteUser", "demoteUser", "changeSettings"
  ];

  if (!isAdmin && adminOnlyActions.includes(action)) {
    throw new AppError(
      isSmallDevice
        ? "Admin only action"
        : "Only group admins can perform this action",
      "PERMISSION_DENIED",
      "error",
      "permission",
      { action, userId, groupId, required: "admin" }
    );
  }
};

/**
 * Enhanced message validation with better error context
 */
export const validateMessage = (message: string, chatId?: string, userId?: string) => {
  if (!message || typeof message !== 'string') {
    throw createValidationError(
      "Message must be a valid string",
      "message"
    );
  }

  if (!message.trim()) {
    throw createValidationError(
      "Message cannot be empty",
      "message"
    );
  }

  const maxLength = isSmallDevice ? 5000 : 10000; // Shorter limit for small devices
  if (message.length > maxLength) {
    throw createValidationError(
      isSmallDevice
        ? `Message too long (max ${maxLength} chars)`
        : `Message is too long. Maximum length is ${maxLength} characters`,
      "message"
    );
  }

  // Check for potentially harmful content
  const suspiciousPatterns = [
    /javascript:/i,
    /<script/i,
    /data:text\/html/i
  ];

  if (suspiciousPatterns.some(pattern => pattern.test(message))) {
    throw new AppError(
      "Message contains potentially harmful content",
      "SUSPICIOUS_CONTENT",
      "error",
      "validation",
      { chatId, userId, field: "message" }
    );
  }
};

/**
 * Enhanced media upload validation with mobile optimization
 */
export const validateMediaUpload = (file: any, isOffline: boolean = false) => {
  if (!file) {
    throw createValidationError("No file selected", "file");
  }

  // Validate file object structure
  if (typeof file !== 'object' || (!file.size && !file.fileSize)) {
    throw createValidationError("Invalid file object", "file");
  }

  const fileSize = file.size || file.fileSize || 0;

  // Adjust max size based on device and connection
  const maxSize = isSmallDevice
    ? 50 * 1024 * 1024  // 50MB for small devices
    : isOffline
    ? 25 * 1024 * 1024  // 25MB when offline
    : 100 * 1024 * 1024; // 100MB normally

  if (fileSize > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024));
    throw new AppError(
      isSmallDevice
        ? `File too large (max ${maxSizeMB}MB)`
        : `File is too large. Maximum size is ${maxSizeMB}MB`,
      "FILE_TOO_LARGE",
      "warning",
      "media",
      { fileSize, maxSize, isOffline }
    );
  }

  // Enhanced file type validation
  const allowedTypes = [
    "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
    "video/mp4", "video/mov", "video/avi", "video/webm",
    "audio/mp3", "audio/wav", "audio/aac", "audio/ogg",
    "application/pdf", "text/plain"
  ];

  const fileType = file.type || file.mimeType || '';

  if (!allowedTypes.includes(fileType.toLowerCase())) {
    throw new AppError(
      isSmallDevice
        ? "Unsupported file type"
        : "Invalid file type. Supported: Images, Videos, Audio, PDF, Text",
      "INVALID_FILE_TYPE",
      "warning",
      "media",
      { fileType, allowedTypes }
    );
  }

  // Additional validation for specific file types
  if (fileType.startsWith('image/')) {
    validateImageFile(file);
  } else if (fileType.startsWith('video/')) {
    validateVideoFile(file, isOffline);
  } else if (fileType.startsWith('audio/')) {
    validateAudioFile(file);
  }
};

/**
 * Validate image file specifics
 */
const validateImageFile = (file: any) => {
  // Check image dimensions if available
  if (file.width && file.height) {
    const maxDimension = isTablet ? 4096 : 2048;
    if (file.width > maxDimension || file.height > maxDimension) {
      throw new AppError(
        `Image dimensions too large. Maximum: ${maxDimension}x${maxDimension}`,
        "IMAGE_TOO_LARGE",
        "warning",
        "media",
        { width: file.width, height: file.height, maxDimension }
      );
    }
  }
};

/**
 * Validate video file specifics
 */
const validateVideoFile = (file: any, isOffline: boolean) => {
  // Stricter limits for video when offline
  const maxVideoSize = isOffline ? 10 * 1024 * 1024 : 50 * 1024 * 1024;
  const fileSize = file.size || file.fileSize || 0;

  if (fileSize > maxVideoSize) {
    const maxSizeMB = Math.round(maxVideoSize / (1024 * 1024));
    throw new AppError(
      `Video file too large. Maximum: ${maxSizeMB}MB${isOffline ? ' (offline)' : ''}`,
      "VIDEO_TOO_LARGE",
      "warning",
      "media",
      { fileSize, maxVideoSize, isOffline }
    );
  }
};

/**
 * Validate audio file specifics
 */
const validateAudioFile = (file: any) => {
  const maxAudioSize = 10 * 1024 * 1024; // 10MB for audio
  const fileSize = file.size || file.fileSize || 0;

  if (fileSize > maxAudioSize) {
    throw new AppError(
      "Audio file too large. Maximum: 10MB",
      "AUDIO_TOO_LARGE",
      "warning",
      "media",
      { fileSize, maxAudioSize }
    );
  }
};

/**
 * Enhanced group invite validation
 */
export const validateGroupInvite = (inviteCode: string, groupId?: string) => {
  if (!inviteCode || typeof inviteCode !== 'string') {
    throw new AppError(
      "Invalid invite code",
      "INVALID_INVITE",
      "warning",
      "validation",
      { field: "inviteCode", groupId }
    );
  }

  const trimmedCode = inviteCode.trim();

  if (trimmedCode.length < 6 || trimmedCode.length > 12) {
    throw new AppError(
      "Invalid invite code format (6-12 characters)",
      "INVALID_INVITE_FORMAT",
      "warning",
      "validation",
      { field: "inviteCode", groupId, codeLength: trimmedCode.length }
    );
  }

  // Check for valid characters (alphanumeric)
  if (!/^[a-zA-Z0-9]+$/.test(trimmedCode)) {
    throw new AppError(
      "Invite code contains invalid characters",
      "INVALID_INVITE_CHARS",
      "warning",
      "validation",
      { field: "inviteCode", groupId, invalidCode: trimmedCode }
    );
  }
};

/**
 * Enhanced network error handler with offline context
 */
export const handleNetworkError = (error: Error, isOffline: boolean = false): never => {
  const isNetworkError = error.message.toLowerCase().includes("network") ||
                        error.message.toLowerCase().includes("connection") ||
                        error.message.toLowerCase().includes("timeout");

  if (isNetworkError || isOffline) {
    throw createNetworkError(
      isOffline
        ? "You're offline. Changes will sync when connection is restored"
        : "Network error. Please check your connection and try again",
      isOffline
    );
  }
  throw error;
};

/**
 * Enhanced authentication error handler
 */
export const handleAuthError = (error: Error, userId?: string): never => {
  const isAuthError = error.message.toLowerCase().includes("auth") ||
                     error.message.toLowerCase().includes("unauthorized") ||
                     error.message.toLowerCase().includes("forbidden");

  if (isAuthError) {
    throw new AppError(
      isSmallDevice
        ? "Please log in again"
        : "Authentication error. Please log in again",
      "AUTH_ERROR",
      "error",
      "auth",
      { userId }
    );
  }
  throw error;
};

/**
 * Enhanced storage error handler with offline support
 */
export const handleStorageError = (error: Error, isOffline: boolean = false): never => {
  const isStorageError = error.message.toLowerCase().includes("storage") ||
                        error.message.toLowerCase().includes("quota") ||
                        error.message.toLowerCase().includes("disk");

  if (isStorageError) {
    throw new AppError(
      isOffline
        ? "Storage error. Data saved locally until online"
        : isSmallDevice
        ? "Storage error. Try again"
        : "Storage error. Please try again or contact support",
      "STORAGE_ERROR",
      "error",
      "storage",
      { isOffline }
    );
  }
  throw error;
};

// Offline error management
/**
 * Get offline error logs for sync
 */
export const getOfflineErrorLogs = async (): Promise<AppError[]> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const result = await database.getAllAsync(`
      SELECT * FROM error_logs
      WHERE handled = 1
      ORDER BY timestamp DESC
      LIMIT 100
    `);

    return result.map((row: any) => {
      const error = new AppError(
        row.message,
        row.code,
        row.severity,
        row.category,
        JSON.parse(row.context || '{}'),
        row.isRetryable === 1
      );
      error.stack = row.stack;
      return error;
    });
  } catch (error) {
    return [];
  }
};

/**
 * Clear old error logs (older than 7 days)
 */
export const cleanOldErrorLogs = async (): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

    await database.runAsync(`
      DELETE FROM error_logs
      WHERE timestamp < ?
    `, [sevenDaysAgo]);
  } catch (error) {
    // Cleanup failed - continue
  }
};

/**
 * Get error statistics
 */
export const getErrorStatistics = async (): Promise<{
  total: number;
  byCategory: Record<string, number>;
  bySeverity: Record<string, number>;
  recent: number;
}> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    // Total errors
    const totalResult = await database.getAllAsync('SELECT COUNT(*) as count FROM error_logs');

    // By category
    const categoryResult = await database.getAllAsync(`
      SELECT category, COUNT(*) as count
      FROM error_logs
      GROUP BY category
    `);

    // By severity
    const severityResult = await database.getAllAsync(`
      SELECT severity, COUNT(*) as count
      FROM error_logs
      GROUP BY severity
    `);

    // Recent (last 24 hours)
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    const recentResult = await database.getAllAsync(`
      SELECT COUNT(*) as count
      FROM error_logs
      WHERE timestamp > ?
    `, [oneDayAgo]);

    const byCategory: Record<string, number> = {};
    categoryResult.forEach((row: any) => {
      byCategory[row.category] = row.count;
    });

    const bySeverity: Record<string, number> = {};
    severityResult.forEach((row: any) => {
      bySeverity[row.severity] = row.count;
    });

    return {
      total: (totalResult[0] as any)?.count || 0,
      byCategory,
      bySeverity,
      recent: (recentResult[0] as any)?.count || 0,
    };
  } catch (error) {
    return {
      total: 0,
      byCategory: {},
      bySeverity: {},
      recent: 0,
    };
  }
};
