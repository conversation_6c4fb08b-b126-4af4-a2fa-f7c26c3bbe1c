import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  AccessibilityInfo,
  Animated,
  Image,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS, SPACING } from "../styles/iraChatDesignSystem";
import { GroupChat } from "../types/groupChat";
import { ResponsiveScale, DeviceInfo, ResponsiveTypography, ResponsiveSpacing } from "../utils/responsiveUtils";

interface GroupHeaderProps {
  group: GroupChat;
  unreadCount?: number;
  typingMembers?: string[];
  recordingMembers?: string[];
  onlineMembers?: string[];
  onBackPress: () => void;
  onInfoPress: () => void;
  onSettingsPress: () => void;
}

const GroupHeader: React.FC<GroupHeaderProps> = ({
  group,
  unreadCount = 0,
  typingMembers = [],
  recordingMembers = [],
  onlineMembers = [],
  onBackPress,
  onInfoPress,
  onSettingsPress,
}) => {
  const pulseAnim = React.useRef(new Animated.Value(1)).current;
  const typingAnim = React.useRef(new Animated.Value(0)).current;
  const [isScreenReaderEnabled, setIsScreenReaderEnabled] =
    React.useState(false);

  React.useEffect(() => {
    const checkScreenReader = async () => {
      const enabled = await AccessibilityInfo.isScreenReaderEnabled();
      setIsScreenReaderEnabled(enabled);
    };
    checkScreenReader();
  }, []);

  React.useEffect(() => {
    if (unreadCount > 0) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ).start();
    } else {
      pulseAnim.setValue(1);
    }
  }, [unreadCount]);

  React.useEffect(() => {
    if (typingMembers.length > 0 || recordingMembers.length > 0) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(typingAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(typingAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ).start();
    } else {
      typingAnim.setValue(0);
    }
  }, [typingMembers, recordingMembers]);

  const renderStatusIndicator = () => {
    if (recordingMembers.length > 0) {
      return (
        <View style={styles.statusContainer}>
          <Ionicons
            name="mic"
            size={DeviceInfo.isTablet ? 20 : 16}
            color={IRACHAT_COLORS.error}
            accessibilityLabel="Recording indicator"
          />
          <Text
            style={[
              styles.statusText,
              DeviceInfo.isSmallPhone && styles.statusTextSmall,
            ]}
            numberOfLines={1}
          >
            {recordingMembers.length === 1
              ? `${recordingMembers[0]} is recording...`
              : `${recordingMembers.length} members recording...`}
          </Text>
        </View>
      );
    }

    if (typingMembers.length > 0) {
      return (
        <View style={styles.statusContainer}>
          <Animated.View
            style={[
              styles.typingIndicator,
              {
                opacity: typingAnim,
              },
            ]}
          >
            <Text
              style={[
                styles.typingDots,
                DeviceInfo.isSmallPhone && styles.typingDotsSmall,
              ]}
            >
              ...
            </Text>
          </Animated.View>
          <Text
            style={[
              styles.statusText,
              DeviceInfo.isSmallPhone && styles.statusTextSmall,
            ]}
            numberOfLines={1}
          >
            {typingMembers.length === 1
              ? `${typingMembers[0]} is typing`
              : `${typingMembers.length} members typing`}
          </Text>
        </View>
      );
    }

    if (onlineMembers.length > 0) {
      return (
        <View style={styles.statusContainer}>
          <View
            style={[
              styles.onlineIndicator,
              DeviceInfo.isSmallPhone && styles.onlineIndicatorSmall,
            ]}
          />
          <Text
            style={[
              styles.statusText,
              DeviceInfo.isSmallPhone && styles.statusTextSmall,
            ]}
            numberOfLines={1}
          >
            {onlineMembers.length}{" "}
            {onlineMembers.length === 1 ? "member" : "members"} online
          </Text>
        </View>
      );
    }

    return null;
  };

  const getAccessibilityLabel = () => {
    const labels = [];
    labels.push(`Group: ${group.name}`);
    labels.push(`${group.members.length} members`);
    if (unreadCount > 0) {
      labels.push(`${unreadCount} unread messages`);
    }
    if (typingMembers.length > 0) {
      labels.push(`${typingMembers.length} members typing`);
    }
    if (recordingMembers.length > 0) {
      labels.push(`${recordingMembers.length} members recording`);
    }
    if (onlineMembers.length > 0) {
      labels.push(`${onlineMembers.length} members online`);
    }
    return labels.join(", ");
  };

  return (
    <View
      style={styles.container}
      accessible={isScreenReaderEnabled}
      accessibilityLabel={getAccessibilityLabel()}
      accessibilityRole="header"
    >
      <View style={styles.leftSection}>
        <TouchableOpacity
          onPress={onBackPress}
          style={styles.backButton}
          accessibilityLabel="Go back"
          accessibilityRole="button"
        >
          <Ionicons
            name="arrow-back"
            size={DeviceInfo.isTablet ? 28 : 24}
            color={IRACHAT_COLORS.text}
          />
        </TouchableOpacity>

        <TouchableOpacity
          onPress={onInfoPress}
          style={styles.groupInfo}
          accessibilityLabel={`View ${group.name} details`}
          accessibilityRole="button"
        >
          {(group.photo || group.groupPhoto) ? (
            <Image
              source={{ uri: group.photo || group.groupPhoto }}
              style={[
                styles.groupPhoto,
                DeviceInfo.isTablet && styles.groupPhotoTablet,
                DeviceInfo.isSmallPhone && styles.groupPhotoSmall,
              ]}
            />
          ) : (
            <View
              style={[
                styles.groupPhotoPlaceholder,
                DeviceInfo.isTablet && styles.groupPhotoTablet,
                DeviceInfo.isSmallPhone && styles.groupPhotoSmall,
              ]}
            >
              <Text
                style={[
                  styles.groupInitials,
                  DeviceInfo.isTablet && styles.groupInitialsTablet,
                  DeviceInfo.isSmallPhone && styles.groupInitialsSmall,
                ]}
              >
                {group.name.substring(0, 2).toUpperCase()}
              </Text>
            </View>
          )}
          <View style={styles.groupDetails}>
            <Text
              style={[
                styles.groupName,
                DeviceInfo.isTablet && styles.groupNameTablet,
                DeviceInfo.isSmallPhone && styles.groupNameSmall,
              ]}
              numberOfLines={1}
            >
              {group.name}
            </Text>
            <View style={styles.groupStatus}>
              <Text
                style={[
                  styles.memberCount,
                  DeviceInfo.isTablet && styles.memberCountTablet,
                  DeviceInfo.isSmallPhone && styles.memberCountSmall,
                ]}
              >
                {group.members.length}{" "}
                {group.members.length === 1 ? "member" : "members"}
              </Text>
              {renderStatusIndicator()}
            </View>
          </View>
        </TouchableOpacity>
      </View>

      <View style={styles.rightSection}>
        {unreadCount > 0 && (
          <Animated.View
            style={[
              styles.unreadBadge,
              DeviceInfo.isTablet && styles.unreadBadgeTablet,
              DeviceInfo.isSmallPhone && styles.unreadBadgeSmall,
              {
                transform: [{ scale: pulseAnim }],
              },
            ]}
          >
            <Text
              style={[
                styles.unreadCount,
                DeviceInfo.isTablet && styles.unreadCountTablet,
                DeviceInfo.isSmallPhone && styles.unreadCountSmall,
              ]}
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Text>
          </Animated.View>
        )}
        <TouchableOpacity
          onPress={onSettingsPress}
          style={styles.settingsButton}
          accessibilityLabel="Group settings"
          accessibilityRole="button"
        >
          <Ionicons
            name="ellipsis-vertical"
            size={DeviceInfo.isTablet ? 28 : 24}
            color={IRACHAT_COLORS.text}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.sm,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
    minHeight: DeviceInfo.isSmallPhone ? 56 : 64, // Ensure minimum touch target for mobile
    ...SHADOWS.sm,
  },
  leftSection: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  rightSection: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    padding: ResponsiveSpacing.sm,
    marginRight: ResponsiveSpacing.sm,
    minWidth: 44, // Minimum touch target for mobile accessibility
    minHeight: 44,
    justifyContent: "center",
    alignItems: "center",
  },
  groupInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    paddingVertical: ResponsiveSpacing.xs, // Better touch area for mobile
    borderRadius: ResponsiveScale.borderRadius(8),
  },
  groupPhoto: {
    width: ResponsiveScale.wp(10),
    height: ResponsiveScale.wp(10),
    borderRadius: ResponsiveScale.wp(5),
    marginRight: ResponsiveSpacing.md,
  },
  groupPhotoTablet: {
    width: ResponsiveScale.wp(8),
    height: ResponsiveScale.wp(8),
    borderRadius: ResponsiveScale.wp(4),
  },
  groupPhotoSmall: {
    width: ResponsiveScale.wp(8),
    height: ResponsiveScale.wp(8),
    borderRadius: ResponsiveScale.wp(4),
  },
  groupPhotoPlaceholder: {
    width: ResponsiveScale.wp(10),
    height: ResponsiveScale.wp(10),
    borderRadius: ResponsiveScale.wp(5),
    backgroundColor: IRACHAT_COLORS.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: ResponsiveSpacing.md,
  },
  groupPhotoPlaceholderTablet: {
    width: ResponsiveScale.wp(8),
    height: ResponsiveScale.wp(8),
    borderRadius: ResponsiveScale.wp(4),
  },
  groupPhotoPlaceholderSmall: {
    width: ResponsiveScale.wp(8),
    height: ResponsiveScale.wp(8),
    borderRadius: ResponsiveScale.wp(4),
  },
  groupInitials: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontSize: ResponsiveTypography.fontSize.lg,
    fontWeight: "bold",
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  groupInitialsTablet: {
    fontSize: ResponsiveTypography.fontSize.md,
  },
  groupInitialsSmall: {
    fontSize: ResponsiveTypography.fontSize.sm,
  },
  groupDetails: {
    flex: 1,
  },
  groupName: {
    fontSize: ResponsiveTypography.fontSize.xl,
    fontWeight: "600",
    color: IRACHAT_COLORS.text,
    marginBottom: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  groupNameTablet: {
    fontSize: ResponsiveTypography.fontSize.lg,
  },
  groupNameSmall: {
    fontSize: ResponsiveTypography.fontSize.md,
  },
  groupStatus: {
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
  },
  memberCount: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    marginRight: ResponsiveSpacing.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  memberCountTablet: {
    fontSize: ResponsiveTypography.fontSize.xs,
  },
  memberCountSmall: {
    fontSize: ResponsiveTypography.fontSize.xs,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    marginLeft: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  statusTextSmall: {
    fontSize: ResponsiveTypography.fontSize.xs,
  },
  typingIndicator: {
    flexDirection: "row",
    alignItems: "center",
  },
  typingDots: {
    fontSize: ResponsiveTypography.fontSize.xl,
    color: IRACHAT_COLORS.textSecondary,
    marginLeft: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  typingDotsSmall: {
    fontSize: ResponsiveTypography.fontSize.lg,
  },
  onlineIndicator: {
    width: ResponsiveScale.wp(2),
    height: ResponsiveScale.wp(2),
    borderRadius: ResponsiveScale.wp(1),
    backgroundColor: IRACHAT_COLORS.online,
    marginRight: ResponsiveSpacing.xs,
  },
  onlineIndicatorSmall: {
    width: ResponsiveScale.wp(1.5),
    height: ResponsiveScale.wp(1.5),
    borderRadius: ResponsiveScale.wp(0.75),
  },
  settingsButton: {
    padding: ResponsiveSpacing.sm,
    minWidth: 44, // Minimum touch target for mobile accessibility
    minHeight: 44,
    justifyContent: "center",
    alignItems: "center",
  },
  unreadBadge: {
    backgroundColor: IRACHAT_COLORS.error,
    borderRadius: ResponsiveScale.wp(3),
    minWidth: ResponsiveScale.wp(6),
    height: ResponsiveScale.wp(6),
    justifyContent: "center",
    alignItems: "center",
    marginRight: ResponsiveSpacing.sm,
    paddingHorizontal: ResponsiveSpacing.xs,
    ...SHADOWS.sm,
  },
  unreadBadgeTablet: {
    minWidth: ResponsiveScale.wp(5),
    height: ResponsiveScale.wp(5),
    borderRadius: ResponsiveScale.wp(2.5),
  },
  unreadBadgeSmall: {
    minWidth: ResponsiveScale.wp(4),
    height: ResponsiveScale.wp(4),
    borderRadius: ResponsiveScale.wp(2),
  },
  unreadCount: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontSize: ResponsiveTypography.fontSize.xs,
    fontWeight: "bold",
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  unreadCountTablet: {
    fontSize: ResponsiveTypography.fontSize.xs,
  },
  unreadCountSmall: {
    fontSize: ResponsiveTypography.fontSize.xs,
  },
});

export default GroupHeader;
