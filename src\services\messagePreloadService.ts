/**
 * Message Preloading Service
 * Preloads recent messages for all chats when the app starts
 * This ensures instant message loading when chats are opened
 */

import { collection, query, orderBy, limit, getDocs, where } from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { memoryCacheService } from './memoryCache';
import { networkStateManager } from './networkStateManager';

interface PreloadedMessage {
  id: string;
  chatId: string;
  content: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  type: 'text' | 'image' | 'video' | 'audio' | 'file';
  status: 'sent' | 'delivered' | 'read';
}

interface ChatPreloadInfo {
  chatId: string;
  lastMessageTime: Date;
  messageCount: number;
}

class MessagePreloadService {
  private static instance: MessagePreloadService;
  private isPreloading = false;
  private preloadedChats = new Set<string>();
  private readonly PRELOAD_MESSAGE_COUNT = 20; // Preload last 20 messages per chat
  private readonly MAX_CHATS_TO_PRELOAD = 50; // Preload for top 50 most recent chats

  static getInstance(): MessagePreloadService {
    if (!MessagePreloadService.instance) {
      MessagePreloadService.instance = new MessagePreloadService();
    }
    return MessagePreloadService.instance;
  }

  /**
   * Start preloading messages for all user chats
   */
  async preloadAllChatMessages(userId: string): Promise<void> {
    if (this.isPreloading) {
      console.log('⏳ Message preloading already in progress');
      return;
    }

    this.isPreloading = true;
    console.log('🚀 Starting message preloading for user:', userId);

    try {
      // Get user's most recent chats
      const recentChats = await this.getUserRecentChats(userId);
      console.log(`📱 Found ${recentChats.length} recent chats to preload`);

      // Preload messages for each chat in parallel (but limited)
      const batchSize = 5; // Process 5 chats at a time to avoid overwhelming
      for (let i = 0; i < recentChats.length; i += batchSize) {
        const batch = recentChats.slice(i, i + batchSize);
        await Promise.all(
          batch.map(chat => this.preloadChatMessages(chat.chatId))
        );
        
        // Small delay between batches to prevent overwhelming the system
        if (i + batchSize < recentChats.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      console.log(`✅ Message preloading completed for ${recentChats.length} chats`);
    } catch (error) {
      console.error('❌ Error during message preloading:', error);
    } finally {
      this.isPreloading = false;
    }
  }

  /**
   * Preload messages for a specific chat
   */
  async preloadChatMessages(chatId: string): Promise<void> {
    if (this.preloadedChats.has(chatId)) {
      return; // Already preloaded
    }

    try {
      console.log(`📥 Preloading messages for chat: ${chatId}`);

      // Try to load from Firebase if online
      let messages: PreloadedMessage[] = [];
      
      if (networkStateManager.isOnline()) {
        messages = await this.loadMessagesFromFirebase(chatId);
        
        // Cache messages offline for instant access
        if (messages.length > 0) {
          await this.cacheMessagesOffline(chatId, messages);
        }
      } else {
        // Load from offline cache
        messages = await this.loadMessagesFromCache(chatId);
      }

      // Store in memory cache for instant access
      if (messages.length > 0) {
        const cacheKey = `preloaded_messages_${chatId}`;
        memoryCacheService.setSettings(cacheKey, messages);
        this.preloadedChats.add(chatId);
        
        console.log(`✅ Preloaded ${messages.length} messages for chat: ${chatId}`);
      }

    } catch (error) {
      console.error(`❌ Error preloading messages for chat ${chatId}:`, error);
    }
  }

  /**
   * Get preloaded messages for a chat (instant access)
   */
  getPreloadedMessages(chatId: string): PreloadedMessage[] {
    const cacheKey = `preloaded_messages_${chatId}`;
    return memoryCacheService.getSettings(cacheKey) as PreloadedMessage[] || [];
  }

  /**
   * Check if messages are preloaded for a chat
   */
  isPreloaded(chatId: string): boolean {
    return this.preloadedChats.has(chatId);
  }

  /**
   * Get user's recent chats for preloading
   */
  private async getUserRecentChats(userId: string): Promise<ChatPreloadInfo[]> {
    try {
      if (networkStateManager.isOnline()) {
        // Load from Firebase
        const chatsRef = collection(db, 'chats');
        const q = query(
          chatsRef,
          where('participants', 'array-contains', userId),
          orderBy('lastMessageTime', 'desc'),
          limit(this.MAX_CHATS_TO_PRELOAD)
        );

        const snapshot = await getDocs(q);
        return snapshot.docs.map(doc => {
          const data = doc.data();
          return {
            chatId: doc.id,
            lastMessageTime: data.lastMessageTime?.toDate() || new Date(),
            messageCount: data.messageCount || 0,
          };
        });
      } else {
        // Load from offline database
        return this.getRecentChatsFromOffline(userId);
      }
    } catch (error) {
      console.error('❌ Error getting recent chats:', error);
      return [];
    }
  }

  /**
   * Load recent chats from offline database
   */
  private async getRecentChatsFromOffline(userId: string): Promise<ChatPreloadInfo[]> {
    try {
      if (!offlineDatabaseService.isReady()) return [];

      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT DISTINCT chatId, MAX(timestamp) as lastMessageTime, COUNT(*) as messageCount
        FROM messages 
        WHERE chatId IN (
          SELECT id FROM chats WHERE participants LIKE '%${userId}%'
        )
        GROUP BY chatId
        ORDER BY lastMessageTime DESC
        LIMIT ?
      `, [this.MAX_CHATS_TO_PRELOAD]);

      return result.map((row: any) => ({
        chatId: row.chatId,
        lastMessageTime: new Date(row.lastMessageTime),
        messageCount: row.messageCount,
      }));
    } catch (error) {
      console.error('❌ Error getting recent chats from offline:', error);
      return [];
    }
  }

  /**
   * Load messages from Firebase
   */
  private async loadMessagesFromFirebase(chatId: string): Promise<PreloadedMessage[]> {
    try {
      const messagesRef = collection(db, `chats/${chatId}/messages`);
      const q = query(
        messagesRef,
        orderBy('timestamp', 'desc'),
        limit(this.PRELOAD_MESSAGE_COUNT)
      );

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          chatId,
          content: data.content || data.text || '',
          senderId: data.senderId || '',
          senderName: data.senderName || '',
          timestamp: data.timestamp?.toDate() || new Date(),
          type: data.type || 'text',
          status: data.status || 'sent',
        };
      }).reverse(); // Reverse to get chronological order
    } catch (error) {
      console.error('❌ Error loading messages from Firebase:', error);
      return [];
    }
  }

  /**
   * Load messages from offline cache
   */
  private async loadMessagesFromCache(chatId: string): Promise<PreloadedMessage[]> {
    try {
      if (!offlineDatabaseService.isReady()) return [];

      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT * FROM messages
        WHERE chatId = ? AND isDeleted = 0
        ORDER BY timestamp DESC
        LIMIT ?
      `, [chatId, this.PRELOAD_MESSAGE_COUNT]);

      return result.map((row: any) => ({
        id: row.id,
        chatId: row.chatId,
        content: row.content || row.text || '',
        senderId: row.senderId || '',
        senderName: row.senderName || '',
        timestamp: new Date(row.timestamp),
        type: row.type || 'text',
        status: row.status || 'sent',
      })).reverse();
    } catch (error) {
      console.error('❌ Error loading messages from cache:', error);
      return [];
    }
  }

  /**
   * Cache messages offline for future access
   */
  private async cacheMessagesOffline(chatId: string, messages: PreloadedMessage[]): Promise<void> {
    try {
      if (!offlineDatabaseService.isReady()) return;

      const db = offlineDatabaseService.getDatabase();
      
      for (const message of messages) {
        await db.runAsync(`
          INSERT OR REPLACE INTO messages (
            id, chatId, content, senderId, senderName, timestamp, type, status, isDeleted
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0)
        `, [
          message.id,
          message.chatId,
          message.content,
          message.senderId,
          message.senderName,
          message.timestamp.getTime(),
          message.type,
          message.status
        ]);
      }
    } catch (error) {
      console.error('❌ Error caching messages offline:', error);
    }
  }

  /**
   * Clear preloaded data (for memory management)
   */
  clearPreloadedData(): void {
    this.preloadedChats.clear();
    console.log('🧹 Cleared preloaded message data');
  }
}

export const messagePreloadService = MessagePreloadService.getInstance();
