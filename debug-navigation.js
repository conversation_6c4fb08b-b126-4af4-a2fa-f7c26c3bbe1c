// Debug Navigation Issues
console.log('🔍 Debugging Navigation Issues');
console.log('==============================');

// Check if the routes exist
const fs = require('fs');
const path = require('path');

function checkRouteExists(routePath) {
  // Convert route path to file path
  let filePath = routePath;
  
  // Handle special cases
  if (routePath === '/(tabs)/index') {
    filePath = 'app/(tabs)/index.tsx';
  } else if (routePath === '/(tabs)') {
    filePath = 'app/(tabs)/_layout.tsx';
  } else if (routePath.startsWith('/(tabs)/')) {
    const fileName = routePath.replace('/(tabs)/', '');
    filePath = `app/(tabs)/${fileName}.tsx`;
  } else if (routePath.startsWith('/(auth)/')) {
    const fileName = routePath.replace('/(auth)/', '');
    filePath = `app/(auth)/${fileName}.tsx`;
  }
  
  const exists = fs.existsSync(filePath);
  console.log(`${exists ? '✅' : '❌'} Route: ${routePath} -> File: ${filePath} (${exists ? 'EXISTS' : 'MISSING'})`);
  return exists;
}

console.log('\n📋 Checking Critical Routes:');
console.log('-----------------------------');

// Check main navigation routes
const criticalRoutes = [
  '/(tabs)',
  '/(tabs)/index',
  '/(tabs)/groups',
  '/(tabs)/business',
  '/(tabs)/calls',
  '/(tabs)/updates',
  '/(auth)/welcome',
  '/(auth)/sign-in',
  '/(auth)/email-sign-in',
  '/(auth)/email-register'
];

let allRoutesExist = true;
criticalRoutes.forEach(route => {
  const exists = checkRouteExists(route);
  if (!exists) allRoutesExist = false;
});

console.log('\n📋 Checking App Structure:');
console.log('---------------------------');

// Check app directory structure
const appDir = 'app';
if (fs.existsSync(appDir)) {
  console.log('✅ app/ directory exists');
  
  const appContents = fs.readdirSync(appDir);
  console.log('📁 app/ contents:', appContents);
  
  // Check tabs directory
  const tabsDir = path.join(appDir, '(tabs)');
  if (fs.existsSync(tabsDir)) {
    console.log('✅ app/(tabs)/ directory exists');
    const tabsContents = fs.readdirSync(tabsDir);
    console.log('📁 app/(tabs)/ contents:', tabsContents);
  } else {
    console.log('❌ app/(tabs)/ directory missing');
  }
  
  // Check auth directory
  const authDir = path.join(appDir, '(auth)');
  if (fs.existsSync(authDir)) {
    console.log('✅ app/(auth)/ directory exists');
    const authContents = fs.readdirSync(authDir);
    console.log('📁 app/(auth)/ contents:', authContents);
  } else {
    console.log('❌ app/(auth)/ directory missing');
  }
} else {
  console.log('❌ app/ directory missing');
}

console.log('\n📋 Navigation Service Analysis:');
console.log('-------------------------------');

// Check navigation service routes
const navigationServicePath = 'src/services/navigationService.ts';
if (fs.existsSync(navigationServicePath)) {
  console.log('✅ Navigation service exists');
  
  const content = fs.readFileSync(navigationServicePath, 'utf8');
  
  // Extract ROUTES.TABS.CHATS value
  const chatsRouteMatch = content.match(/CHATS:\s*['"`]([^'"`]+)['"`]/);
  if (chatsRouteMatch) {
    const chatsRoute = chatsRouteMatch[1];
    console.log('📍 ROUTES.TABS.CHATS =', chatsRoute);
    checkRouteExists(chatsRoute);
  }
  
  // Check for navigateToMainApp method
  if (content.includes('navigateToMainApp')) {
    console.log('✅ navigateToMainApp method exists');
  } else {
    console.log('❌ navigateToMainApp method missing');
  }
} else {
  console.log('❌ Navigation service missing');
}

console.log('\n🎯 Summary:');
console.log('-----------');
if (allRoutesExist) {
  console.log('✅ All critical routes exist');
  console.log('💡 Navigation issue might be in:');
  console.log('   - Authentication state management');
  console.log('   - Route resolution timing');
  console.log('   - Navigation stack conflicts');
  console.log('   - Expo Router configuration');
} else {
  console.log('❌ Some critical routes are missing');
  console.log('💡 Fix missing routes first');
}

console.log('\n📝 Recommended Actions:');
console.log('1. Check console logs during navigation');
console.log('2. Verify authentication state is properly set');
console.log('3. Test navigation with simple router.replace("/(tabs)")');
console.log('4. Check for any route guards or middleware');
console.log('5. Verify Expo Router configuration');
