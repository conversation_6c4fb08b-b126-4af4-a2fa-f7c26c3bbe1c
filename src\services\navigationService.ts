/**
 * 🧭 IRACHAT NAVIGATION SERVICE
 * Centralized navigation system for seamless routing throughout the app
 */

import { router } from 'expo-router';
import { validateRoute, logAllRoutes, getRouteCategory } from '../utils/routeValidation';
import { runNavigationValidation } from '../utils/navigationValidator';

export interface NavigationParams {
  [key: string]: string | number | boolean | undefined;
}

// Debug flag - set to true to enable detailed navigation logging
const DEBUG_NAVIGATION = __DEV__;

// 📱 ALL IRACHAT ROUTES - Comprehensive route definitions matching actual file structure
export const ROUTES = {
  // 🏠 Main App Routes
  HOME: '/',
  INDEX: '/index',

  // 🔐 Authentication Routes (all verified to exist) - INCLUDES SIGN IN SYSTEM
  AUTH: {
    WELCOME: '/(auth)/welcome',
    SIGN_IN: '/(auth)/sign-in', // Sign in method selection
    EMAIL_SIGN_IN: '/(auth)/email-sign-in', // Email sign in
    PHONE_SIGN_IN: '/(auth)/phone-sign-in', // Phone sign in
    EMAIL_REGISTER: '/(auth)/email-register', // Email registration
    EMAIL_REGISTER_INFO: '/(auth)/email-register-info', // Email registration info screen
    INFO: '/(auth)/register-info', // Registration info screen (explains phone requirement)
    PHONE_REGISTER: '/(auth)/phone-register',
    REGISTER: '/(auth)/register', // Auth group register (main registration form)
    INDEX: '/(auth)/index', // Auth index - redirects to welcome
  },

  // 📝 Registration Routes - FIXED: All registration routes in auth group
  REGISTER: {
    INFO: '/(auth)/register-info', // Registration info screen (explains phone requirement)
    PHONE: '/(auth)/phone-register', // Phone registration in auth group
    MAIN: '/(auth)/register', // Main registration form in auth group
  },

  // 📋 Tab Routes - 5 Main Tabs (Profile/Settings in overlay menu)
  TABS: {
    CHATS: '/(tabs)/index',
    GROUPS: '/(tabs)/groups',
    BUSINESS: '/(tabs)/business', // Business marketplace tab
    CALLS: '/(tabs)/calls',
    STORIES: '/(tabs)/updates', // Stories content (video feed)
    PROFILE: '/(tabs)/profile', // Hidden tab - accessed via overlay menu
    SETTINGS_TAB: '/(tabs)/settings', // Legacy tab settings (deprecated)
  },

  // 💬 Chat & Messaging Routes (verified to exist)
  CHAT: {
    INDIVIDUAL: (chatId: string) => `/chat/${chatId}`,
    GROUP: (groupId: string) => `/group-chat?groupId=${groupId}`,
    GROUP_CHAT: '/group-chat', // Added direct group chat route
    NEW_CHAT: '/new-chat',
    CHAT_MANAGEMENT: '/chat-management',
    ENHANCED_GROUP: '/enhanced-group-chat',
    INDIVIDUAL_CHAT: '/individual-chat',
  },

  // 👥 Group Routes (verified to exist)
  GROUP: {
    CREATE: '/create-group',
    SETTINGS: (groupId: string) => `/group-settings?groupId=${groupId}`,
    SELECT_MEMBERS: '/select-group-members',
    CHAT: (groupId: string) => `/group-chat?groupId=${groupId}`,
    CHAT_DIRECT: '/group-chat', // Added direct group chat route
  },

  // 📞 Call Routes (verified to exist)
  CALL: {
    VIDEO: (contactId: string, contactName: string) =>
      `/video-call?contactId=${contactId}&contactName=${encodeURIComponent(contactName)}`,
    VOICE: (contactId: string, contactName: string) =>
      `/voice-call?contactId=${contactId}&contactName=${encodeURIComponent(contactName)}`,
    INCOMING: (callId: string) => `/incoming-call?callId=${callId}`,
    INCOMING_REAL: (callId: string) => `/incoming-call-real?callId=${callId}`,
    REAL_CALL: '/real-call',
    SAFE_VIDEO: '/video-call-safe',
    CALL: '/call',
  },

  // 👤 Profile & Account Routes (verified to exist)
  PROFILE: {
    MAIN: '/(tabs)/profile',
    VIEW: (userId: string) => `/profile/${userId}`,
    EDIT: '/edit-profile',
  },


  // 🔍 Search & Discovery Routes (verified to exist)
  SEARCH: {
    GLOBAL: '/global-search',
    CONTACTS: '/contacts',
    FAST_CONTACTS: '/fast-contacts',
  },

  // 📷 Media & Files Routes (verified to exist)
  MEDIA: {
    CAMERA: '/camera',
    GALLERY: '/media-gallery',
    DOWNLOADS: '/downloaded-media',
  },

  // ⚙️ Settings & Preferences Routes (verified to exist)
  SETTINGS: {
    MAIN: '/settings', // Updated to point to standalone settings page
    ACCOUNT: '/account-settings',
    PRIVACY: '/privacy-settings',
    NOTIFICATIONS: '/notifications-settings',
    THEME: '/theme-settings',
    EXPORT_DATA: '/export-data',
    CHAT_MANAGEMENT: '/chat-management',
  },

  // ℹ️ Help & Support Routes (verified to exist)
  HELP: {
    SUPPORT: '/help-support',
    HELP: '/help',
    ABOUT: '/about',
  },

  // 📂 Organization Routes (verified to exist)
  ORGANIZATION: {
    ARCHIVES: '/archives',
    PINNED_MESSAGES: '/pinned-messages',
    INVITE_FRIENDS: '/invite-friends',
  },
} as const;

// 🧭 NAVIGATION SERVICE CLASS
class NavigationService {
  private navigationHistory: string[] = [];
  private maxHistorySize = 10;

  /**
   * Add route to navigation history for loop detection
   */
  private addToHistory(route: string) {
    this.navigationHistory.push(route);
    if (this.navigationHistory.length > this.maxHistorySize) {
      this.navigationHistory.shift();
    }
  }



  /**
   * Validate if a route exists in our route definitions
   */
  private isValidRoute(route: string): boolean {
    const allRoutes = this.getAllRoutes();
    return allRoutes.includes(route) || route.startsWith('/') || route.includes('(');
  }

  /**
   * Get all defined routes for validation
   */
  private getAllRoutes(): string[] {
    const routes: string[] = [];
    const extractRoutes = (obj: any) => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          routes.push(obj[key]);
        } else if (typeof obj[key] === 'object') {
          extractRoutes(obj[key]);
        }
      }
    };
    extractRoutes(ROUTES);
    return routes;
  }

  /**
   * Navigate to a specific route with improved error handling
   */
  navigate(route: string, params?: NavigationParams) {
    try {
      // Validate route
      if (!route || route.trim() === '') {
        console.error('❌ Navigation error: Empty route provided');
        return;
      }

      // Navigation loop detection disabled to prevent forced redirects
      // The consolidated AuthNavigator now handles all auth-based navigation

      // Add to history for loop detection
      this.addToHistory(route);

      // Enhanced route validation with detailed feedback
      if (DEBUG_NAVIGATION) {
        const validation = validateRoute(route);
        if (!validation.exists) {
          console.warn(`⚠️ Navigation warning: Route "${route}" not found in ROUTES constant`);
          console.warn(`📂 Route category: ${getRouteCategory(route)}`);
          if (validation.suggestions.length > 0) {
            console.warn(`💡 Suggestions: ${validation.suggestions.join(', ')}`);
          }
        } else {
          console.log(`✅ Route validation passed for: ${route}`);
        }
      }

      if (params) {
        const queryString = new URLSearchParams(
          Object.entries(params).reduce((acc, [key, value]) => {
            if (value !== undefined) {
              acc[key] = String(value);
            }
            return acc;
          }, {} as Record<string, string>)
        ).toString();

        const separator = route.includes('?') ? '&' : '?';
        const fullRoute = `${route}${separator}${queryString}`;
        console.log(`🧭 Full route with params: ${fullRoute}`);
        router.push(fullRoute as any);
      } else {
        console.log(`🧭 Attempting to navigate to: ${route}`);
        router.push(route as any);
      }

      console.log(`✅ Navigation successful to: ${route}`);
    } catch (error) {
      console.error('❌ Navigation error:', error);
      console.error(`Failed to navigate to: ${route}`);

      // Fallback navigation to prevent app crash
      try {
        router.push(ROUTES.TABS.CHATS as any);
        console.log('🔄 Fallback navigation to chats successful');
      } catch (fallbackError) {
        console.error('❌ Fallback navigation also failed:', fallbackError);
      }
    }
  }

  /**
   * Replace current route with improved error handling
   */
  replace(route: string, params?: NavigationParams) {
    try {
      console.log(`🔄 Replacing route with: ${route}`, params ? `with params: ${JSON.stringify(params)}` : '');

      // Validate route
      if (!route || route.trim() === '') {
        console.error('❌ Replace error: Empty route provided');
        return;
      }

      // Check if route is valid (optional warning, not blocking)
      if (!this.isValidRoute(route)) {
        console.warn(`⚠️ Replace warning: Route "${route}" may not be defined in ROUTES constant`);
      }

      if (params) {
        const queryString = new URLSearchParams(
          Object.entries(params).reduce((acc, [key, value]) => {
            if (value !== undefined) {
              acc[key] = String(value);
            }
            return acc;
          }, {} as Record<string, string>)
        ).toString();

        const separator = route.includes('?') ? '&' : '?';
        const fullRoute = `${route}${separator}${queryString}`;
        console.log(`🔄 Full replace route with params: ${fullRoute}`);
        router.replace(fullRoute as any);
      } else {
        router.replace(route as any);
      }

      console.log(`✅ Route replacement successful to: ${route}`);
    } catch (error) {
      console.error('❌ Navigation replace error:', error);
      console.error(`Failed to replace with route: ${route}`);

      // Fallback navigation to prevent app crash
      try {
        router.replace(ROUTES.TABS.CHATS as any);
        console.log('🔄 Fallback replace to chats successful');
      } catch (fallbackError) {
        console.error('❌ Fallback replace also failed:', fallbackError);
      }
    }
  }

  /**
   * Go back to previous screen with improved error handling
   */
  goBack() {
    try {
      console.log('⬅️ Attempting to navigate back');

      if (router.canGoBack()) {
        router.back();
        console.log('✅ Navigation back successful');
      } else {
        console.log('⚠️ Cannot go back, navigating to home instead');
        // Fallback to home if can't go back
        this.navigateToMainApp();
      }
    } catch (error) {
      console.error('❌ Navigation back error:', error);
      console.log('🔄 Fallback to home screen after back navigation error');

      // Try multiple fallback strategies to prevent infinite loops
      try {
        this.navigateToMainApp();
      } catch (fallbackError) {
        console.error('❌ Main app navigation also failed, trying welcome screen');
        try {
          this.navigateToWelcome();
        } catch (welcomeError) {
          console.error('❌ All navigation fallbacks failed:', welcomeError);
        }
      }
    }
  }

  /**
   * Navigate to home/main screen
   */
  goHome() {
    this.navigate(ROUTES.TABS.CHATS);
  }

  /**
   * Navigate to home screen (alias for goHome)
   */
  navigateToHome() {
    this.goHome();
  }

  /**
   * Reset navigation stack and go to a specific route
   */
  reset(route: string, params?: NavigationParams) {
    try {
      console.log(`🔄 Resetting navigation stack to: ${route}`);

      // First navigate to the route
      this.replace(route, params);

      console.log(`✅ Navigation stack reset to: ${route}`);
    } catch (error) {
      console.error('❌ Navigation reset error:', error);
      this.replace(ROUTES.TABS.CHATS);
    }
  }

  /**
   * Check if we can navigate back
   */
  canGoBack(): boolean {
    try {
      return router.canGoBack();
    } catch (error) {
      console.error('❌ Error checking if can go back:', error);
      return false;
    }
  }

  /**
   * Navigate with animation type (for future enhancement)
   */
  navigateWithAnimation(route: string, params?: NavigationParams, animationType?: 'slide' | 'fade' | 'modal') {
    // For now, just use regular navigation
    // In the future, this could be enhanced with custom animations
    console.log(`🎬 Navigating with ${animationType || 'default'} animation to: ${route}`);
    this.navigate(route, params);
  }

  // 🔐 AUTHENTICATION NAVIGATION HELPERS
  navigateToWelcome() {
    console.log('🔐 Navigating to welcome screen');
    try {
      router.replace(ROUTES.AUTH.WELCOME as any);
      console.log('✅ Welcome navigation successful');
    } catch (error) {
      console.error('❌ Welcome navigation failed:', error);
      // Fallback to direct route
      try {
        router.replace('/(auth)/welcome' as any);
        console.log('✅ Welcome navigation fallback successful');
      } catch (fallbackError) {
        console.error('❌ Welcome navigation fallback failed:', fallbackError);
      }
    }
  }

  navigateToSignIn() {
    console.log('🔐 Navigating to sign in screen');
    try {
      router.push(ROUTES.AUTH.SIGN_IN as any);
      console.log('✅ Sign in navigation successful');
    } catch (error) {
      console.error('❌ Sign in navigation failed:', error);
    }
  }

  navigateToEmailSignIn() {
    console.log('🔐 Navigating to email sign in screen');
    this.navigate(ROUTES.AUTH.EMAIL_SIGN_IN);
  }

  navigateToPhoneSignIn() {
    console.log('🔐 Navigating to phone sign in screen');
    this.navigate(ROUTES.AUTH.PHONE_SIGN_IN);
  }

  navigateToEmailRegister() {
    console.log('🔐 Navigating to email register screen');
    this.navigate(ROUTES.AUTH.EMAIL_REGISTER);
  }

  navigateToEmailRegisterInfo() {
    console.log('🔐 Navigating to email register info screen');
    this.navigate(ROUTES.AUTH.EMAIL_REGISTER_INFO);
  }

  navigateToRegisterInfo() {
    console.log('🔐 Navigating to register info screen');
    this.navigate(ROUTES.AUTH.INFO);
  }

  navigateToPhoneRegister() {
    console.log('🔐 Navigating to phone register screen');
    this.navigate(ROUTES.AUTH.PHONE_REGISTER);
  }

  navigateToRegister() {
    console.log('🔐 Navigating to register screen');
    this.navigate(ROUTES.AUTH.REGISTER);
  }



  navigateToMainApp() {
    console.log('🔐 Navigating to main app after authentication');
    console.log('🔄 Replacing route with:', ROUTES.TABS.CHATS);

    try {
      // Use replace instead of reset to avoid navigation stack issues
      router.replace(ROUTES.TABS.CHATS as any);
      console.log('✅ Route replacement successful to:', ROUTES.TABS.CHATS);
    } catch (error) {
      console.error('❌ Failed to navigate to main app:', error);

      // Try alternative navigation methods
      try {
        console.log('🔄 Trying alternative navigation to /(tabs)');
        router.replace('/(tabs)' as any);
        console.log('✅ Alternative navigation successful');
      } catch (altError) {
        console.error('❌ Alternative navigation also failed:', altError);

        // Last resort - try push navigation
        try {
          console.log('🔄 Trying push navigation as last resort');
          router.push('/(tabs)' as any);
          console.log('✅ Push navigation successful');
        } catch (pushError) {
          console.error('❌ All navigation methods failed:', pushError);
        }
      }
    }
  }

  /**
   * Navigate to authentication flow (logout)
   */
  navigateToAuth() {
    console.log('🔐 Navigating to authentication flow');
    this.replace(ROUTES.AUTH.WELCOME);
  }

  // 💬 CHAT NAVIGATION HELPERS
  openChat(chatId: string, isGroup: boolean = false, groupName?: string) {
    if (isGroup) {
      // For group chats, we need both groupId and groupName
      if (groupName) {
        this.navigate('/group-chat', {
          groupId: chatId,
          groupName: groupName
        });
      } else {
        // Fallback to basic route if no group name provided
        this.navigate(ROUTES.CHAT.GROUP(chatId));
      }
    } else {
      this.navigate(ROUTES.CHAT.INDIVIDUAL(chatId));
    }
  }

  openNewChat() {
    this.navigate(ROUTES.CHAT.NEW_CHAT);
  }

  // Enhanced group chat navigation with proper parameters
  openGroupChat(groupId: string, groupName: string, groupAvatar?: string, isAdmin?: boolean) {
    this.navigate('/group-chat', {
      groupId,
      groupName,
      groupAvatar: groupAvatar || '',
      isAdmin: isAdmin ? 'true' : 'false'
    });
  }

  // 👥 GROUP NAVIGATION HELPERS
  createGroup() {
    this.navigate(ROUTES.GROUP.CREATE);
  }

  openGroupSettings(groupId: string) {
    this.navigate(ROUTES.GROUP.SETTINGS(groupId));
  }

  // 📞 CALL NAVIGATION HELPERS
  startVideoCall(contactId: string, contactName: string) {
    this.navigate(ROUTES.CALL.VIDEO(contactId, contactName));
  }

  startVoiceCall(contactId: string, contactName: string) {
    this.navigate(ROUTES.CALL.VOICE(contactId, contactName));
  }

  // 👤 PROFILE NAVIGATION HELPERS
  openProfile() {
    console.log('🔄 Opening profile screen');
    this.navigate(ROUTES.PROFILE.MAIN);
  }

  editProfile() {
    this.navigate(ROUTES.PROFILE.EDIT);
  }

  openSettings() {
    this.navigate(ROUTES.SETTINGS.MAIN);
  }

  // 🔍 SEARCH NAVIGATION HELPERS
  openGlobalSearch() {
    this.navigate(ROUTES.SEARCH.GLOBAL);
  }

  openContacts() {
    this.navigate(ROUTES.SEARCH.CONTACTS);
  }

  // 📷 MEDIA NAVIGATION HELPERS
  openCamera() {
    this.navigate(ROUTES.MEDIA.CAMERA);
  }

  openMediaGallery() {
    this.navigate(ROUTES.MEDIA.GALLERY);
  }

  // 📱 UPDATE NAVIGATION HELPERS
  openUpdate(updateId: string) {
    console.log(`📱 Opening update with ID: ${updateId}`);
    this.navigate(`/update/${updateId}`);
  }

  // 💰 BUSINESS NAVIGATION HELPERS
  openBusiness() {
    console.log('💰 Opening business marketplace');
    this.navigate(ROUTES.TABS.BUSINESS);
  }

  // ℹ️ HELP NAVIGATION HELPERS
  openHelp() {
    this.navigate(ROUTES.HELP.HELP);
  }

  openSupport() {
    this.navigate(ROUTES.HELP.SUPPORT);
  }

  openAbout() {
    this.navigate(ROUTES.HELP.ABOUT);
  }

  // 🐛 DEBUG HELPERS
  /**
   * Log all available routes for debugging
   */
  debugLogAllRoutes() {
    if (DEBUG_NAVIGATION) {
      logAllRoutes();
    }
  }

  /**
   * Run comprehensive navigation validation
   */
  validateNavigation() {
    if (DEBUG_NAVIGATION) {
      runNavigationValidation();
    }
  }

  /**
   * Get navigation statistics
   */
  getNavigationStats() {
    const allRoutes = this.getAllRoutes();
    return {
      totalRoutes: allRoutes.length,
      authRoutes: allRoutes.filter(route => route.includes('(auth)')).length,
      tabRoutes: allRoutes.filter(route => route.includes('(tabs)')).length,
      chatRoutes: allRoutes.filter(route => route.includes('/chat')).length,
      callRoutes: allRoutes.filter(route => route.includes('/call')).length,
    };
  }
}

// Export singleton instance
export const navigationService = new NavigationService();
export default navigationService;
