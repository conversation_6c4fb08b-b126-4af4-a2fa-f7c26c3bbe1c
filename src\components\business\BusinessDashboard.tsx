import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  FlatList,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BusinessProfile, BusinessPost } from '../../types/Business';
import { businessService } from '../../services/businessService';

const COLORS = {
  primary: '#1DA1F2',
  background: '#FFFFFF',
  text: '#14171A',
  textSecondary: '#657786',
  border: '#E1E8ED',
  surface: '#F7F9FA',
  success: '#17BF63',
  warning: '#FFAD1F',
  error: '#E0245E',
};

interface BusinessDashboardProps {
  businessProfile: BusinessProfile;
  onAddPost: () => void;
  onEditProfile: () => void;
}

export const BusinessDashboard: React.FC<BusinessDashboardProps> = ({
  businessProfile,
  onAddPost,
  onEditProfile,
}) => {
  const [myPosts, setMyPosts] = useState<BusinessPost[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    loadMyPosts();
  }, [businessProfile.id]);

  const loadMyPosts = async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }

    try {
      // Load posts for this business
      const posts = await businessService.getBusinessPosts(businessProfile.id);
      setMyPosts(posts);
    } catch (error) {
      console.error('❌ Error loading business posts:', error);
      Alert.alert('Error', 'Failed to load your posts');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleDeletePost = async (postId: string) => {
    Alert.alert(
      'Delete Post',
      'Are you sure you want to delete this post?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await businessService.deletePost(postId);
              setMyPosts(prev => prev.filter(post => post.id !== postId));
              Alert.alert('Success', 'Post deleted successfully');
            } catch (error) {
              console.error('❌ Error deleting post:', error);
              Alert.alert('Error', 'Failed to delete post');
            }
          },
        },
      ]
    );
  };

  const renderStatsCard = (title: string, value: number, icon: string, color: string) => (
    <View style={[styles.statsCard, { borderLeftColor: color }]}>
      <View style={styles.statsContent}>
        <View style={styles.statsHeader}>
          <Ionicons name={icon as any} size={24} color={color} />
          <Text style={styles.statsValue}>{value.toLocaleString()}</Text>
        </View>
        <Text style={styles.statsTitle}>{title}</Text>
      </View>
    </View>
  );

  const renderPostItem = ({ item: post }: { item: BusinessPost }) => (
    <View style={styles.postCard}>
      <View style={styles.postHeader}>
        <View style={styles.postInfo}>
          <Text style={styles.postTitle} numberOfLines={1}>
            {post.title}
          </Text>
          <Text style={styles.postDate}>
            {post.createdAt.toLocaleDateString()}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeletePost(post.id)}
        >
          <Ionicons name="trash-outline" size={20} color={COLORS.error} />
        </TouchableOpacity>
      </View>
      
      <Text style={styles.postDescription} numberOfLines={2}>
        {post.description}
      </Text>
      
      <View style={styles.postStats}>
        <View style={styles.statItem}>
          <Ionicons name="eye-outline" size={16} color={COLORS.textSecondary} />
          <Text style={styles.statText}>{post.views || 0}</Text>
        </View>
        <View style={styles.statItem}>
          <Ionicons name="heart-outline" size={16} color={COLORS.textSecondary} />
          <Text style={styles.statText}>{post.likes.length}</Text>
        </View>
        <View style={styles.statItem}>
          <Ionicons name="share-outline" size={16} color={COLORS.textSecondary} />
          <Text style={styles.statText}>{post.shares || 0}</Text>
        </View>
        {post.price && (
          <View style={styles.priceTag}>
            <Text style={styles.priceText}>
              {post.currency} {post.price.toLocaleString()}
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  return (
    <ScrollView 
      style={styles.container}
      refreshControl={
        <RefreshControl
          refreshing={isRefreshing}
          onRefresh={() => loadMyPosts(true)}
          colors={[COLORS.primary]}
        />
      }
    >
      {/* Business Info Header */}
      <View style={styles.businessHeader}>
        <View style={styles.businessInfo}>
          <Text style={styles.businessName}>{businessProfile.businessName}</Text>
          <Text style={styles.businessType}>{businessProfile.businessType}</Text>
          <Text style={styles.businessLocation}>
            {businessProfile.location.city}, {businessProfile.location.district}
          </Text>
        </View>
        <TouchableOpacity style={styles.editButton} onPress={onEditProfile}>
          <Ionicons name="pencil-outline" size={20} color={COLORS.primary} />
          <Text style={styles.editButtonText}>Edit</Text>
        </TouchableOpacity>
      </View>

      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        {renderStatsCard('Total Posts', businessProfile.totalPosts || myPosts.length, 'document-text-outline', COLORS.primary)}
        {renderStatsCard('Total Views', businessProfile.totalViews || 0, 'eye-outline', COLORS.success)}
        {renderStatsCard('Total Likes', businessProfile.totalLikes || 0, 'heart-outline', COLORS.error)}
        {renderStatsCard('Total Shares', businessProfile.totalShares || 0, 'share-outline', COLORS.warning)}
      </View>

      {/* Quick Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={onAddPost}>
          <Ionicons name="add-circle-outline" size={24} color={COLORS.primary} />
          <Text style={styles.actionButtonText}>Add New Product</Text>
        </TouchableOpacity>
      </View>

      {/* My Posts */}
      <View style={styles.postsSection}>
        <Text style={styles.sectionTitle}>My Products ({myPosts.length})</Text>
        {isLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading your posts...</Text>
          </View>
        ) : myPosts.length > 0 ? (
          <FlatList
            data={myPosts}
            renderItem={renderPostItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.emptyContainer}>
            <Ionicons name="document-outline" size={48} color={COLORS.textSecondary} />
            <Text style={styles.emptyText}>No products yet</Text>
            <Text style={styles.emptySubtext}>Add your first product to get started</Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  businessHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 16,
    backgroundColor: COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  businessInfo: {
    flex: 1,
  },
  businessName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 4,
  },
  businessType: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 2,
  },
  businessLocation: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  editButtonText: {
    marginLeft: 4,
    fontSize: 14,
    color: COLORS.primary,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    gap: 12,
  },
  statsCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: COLORS.background,
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statsContent: {
    alignItems: 'center',
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.text,
    marginLeft: 8,
  },
  statsTitle: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  actionsContainer: {
    padding: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.surface,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  actionButtonText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.primary,
  },
  postsSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: 16,
  },
  postCard: {
    backgroundColor: COLORS.background,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  postHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  postInfo: {
    flex: 1,
  },
  postTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  postDate: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  deleteButton: {
    padding: 4,
  },
  postDescription: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  postStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  priceTag: {
    marginLeft: 'auto',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  priceText: {
    fontSize: 12,
    fontWeight: '600',
    color: 'white',
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  emptyContainer: {
    padding: 32,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginTop: 4,
    textAlign: 'center',
  },
});
