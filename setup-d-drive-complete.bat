@echo off
echo =====================================================
echo COMPLETE D: DRIVE CACHE SETUP FOR IRACHAT
echo =====================================================
echo.
echo This script will:
echo 1. Set up all cache directories on D: drive
echo 2. Configure environment variables
echo 3. Clean existing C: drive cache
echo 4. Set up Android SDK paths
echo.
pause

echo.
echo Step 1: Running PowerShell setup script...
echo =====================================================
powershell -ExecutionPolicy Bypass -File "setup-d-drive-cache.ps1"

echo.
echo Step 2: Setting environment variables for current session...
echo =====================================================

REM Set Android SDK to D: drive (common locations)
set ANDROID_SDK_ROOT=D:\Android\Sdk
set ANDROID_HOME=D:\Android\Sdk

REM If Android SDK is not on D:, try other common locations
if not exist "%ANDROID_SDK_ROOT%" (
    echo Android SDK not found at D:\Android\Sdk, trying other locations...
    if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
        set ANDROID_SDK_ROOT=C:\Users\<USER>\AppData\Local\Android\Sdk
        set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
        echo Found Android SDK at: %ANDROID_SDK_ROOT%
    ) else if exist "C:\Android\Sdk" (
        set ANDROID_SDK_ROOT=C:\Android\Sdk
        set ANDROID_HOME=C:\Android\Sdk
        echo Found Android SDK at: %ANDROID_SDK_ROOT%
    ) else (
        echo WARNING: Android SDK not found in common locations!
        echo Please install Android SDK or update the path in this script.
    )
) else (
    echo Found Android SDK at: %ANDROID_SDK_ROOT%
)

REM Set Java Home
set JAVA_HOME=C:\Program Files\Java\jdk-23

REM Force all cache and temp directories to D: drive
set GRADLE_USER_HOME=D:\gradle-cache
set TMPDIR=D:\temp
set TEMP=D:\temp
set TMP=D:\temp
set NPM_CONFIG_CACHE=D:\npm-cache
set NPM_CONFIG_TMP=D:\temp
set YARN_CACHE_FOLDER=D:\yarn-cache
set EXPO_CACHE_DIR=D:\expo-cache

REM Set PATH with Android tools
set PATH=%PATH%;%ANDROID_SDK_ROOT%\platform-tools;%ANDROID_SDK_ROOT%\tools;%ANDROID_SDK_ROOT%\tools\bin;%JAVA_HOME%\bin

echo.
echo Step 3: Verifying setup...
echo =====================================================
echo ANDROID_HOME=%ANDROID_HOME%
echo ANDROID_SDK_ROOT=%ANDROID_SDK_ROOT%
echo JAVA_HOME=%JAVA_HOME%
echo GRADLE_USER_HOME=%GRADLE_USER_HOME%
echo TMPDIR=%TMPDIR%
echo TEMP=%TEMP%
echo TMP=%TMP%
echo NPM_CONFIG_CACHE=%NPM_CONFIG_CACHE%
echo YARN_CACHE_FOLDER=%YARN_CACHE_FOLDER%
echo EXPO_CACHE_DIR=%EXPO_CACHE_DIR%

echo.
echo Step 4: Testing ADB...
echo =====================================================
adb version

echo.
echo =====================================================
echo SETUP COMPLETE!
echo =====================================================
echo.
echo All cache and build files will now go to D: drive!
echo.
echo Next steps:
echo 1. Close this terminal
echo 2. Open a new terminal
echo 3. Run: npx expo start --clear
echo 4. Run: npx expo run:android
echo.
echo To verify cache is working on D: drive:
echo - Check D:\gradle-cache for Gradle cache
echo - Check D:\metro-cache for Metro cache  
echo - Check D:\npm-cache for NPM cache
echo.
pause
