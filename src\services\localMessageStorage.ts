import * as SQLite from 'expo-sqlite';
import { Message } from '../types';

export interface LocalMessage extends Message {
  localId?: string;
  syncStatus: 'pending' | 'synced' | 'failed';
  createdAt: number;
  updatedAt: number;

  // Additional properties not in base Message interface
  chatId?: string;
  mediaThumbnail?: string;
  duration?: number; // for audio/video
  callType?: "voice" | "video";
  callStatus?: "outgoing" | "incoming" | "ended" | "cancelled" | "missed";
  callDuration?: number; // in seconds
  isDeleted?: boolean;
  deletedAt?: Date | number;
}

class LocalMessageStorageService {
  private db: SQLite.SQLiteDatabase | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.db = await SQLite.openDatabaseAsync('irachat_messages.db');
      await this.createTables();
      this.isInitialized = true;
      console.log('✅ Local message storage initialized');
    } catch (error) {
      console.error('❌ Failed to initialize local message storage:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Messages table
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS messages (
        localId TEXT PRIMARY KEY,
        id TEXT,
        chatId TEXT NOT NULL,
        text TEXT,
        senderId TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        status TEXT NOT NULL,
        type TEXT NOT NULL,
        mediaUrl TEXT,
        mediaThumbnail TEXT,
        duration INTEGER,
        fileName TEXT,
        fileSize INTEGER,
        callType TEXT,
        callStatus TEXT,
        callDuration INTEGER,
        isPinned INTEGER DEFAULT 0,
        pinnedAt INTEGER,
        pinnedBy TEXT,
        replyToMessageId TEXT,
        replyToText TEXT,
        replyToSenderName TEXT,
        replyToType TEXT,
        reactions TEXT,
        syncStatus TEXT NOT NULL DEFAULT 'pending',
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL,
        isEdited INTEGER DEFAULT 0,
        editedAt INTEGER,
        isDeleted INTEGER DEFAULT 0,
        deletedAt INTEGER
      );
    `);

    // Create indexes for better performance
    await this.db.execAsync(`
      CREATE INDEX IF NOT EXISTS idx_messages_chatId ON messages(chatId);
      CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp);
      CREATE INDEX IF NOT EXISTS idx_messages_syncStatus ON messages(syncStatus);
      CREATE INDEX IF NOT EXISTS idx_messages_senderId ON messages(senderId);
    `);

    // Chat metadata table for tracking sync status
    await this.db.execAsync(`
      CREATE TABLE IF NOT EXISTS chat_metadata (
        chatId TEXT PRIMARY KEY,
        lastSyncTimestamp INTEGER,
        unreadCount INTEGER DEFAULT 0,
        lastMessageId TEXT,
        lastMessageTimestamp INTEGER,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      );
    `);

    console.log('✅ Database tables created successfully');
  }

  async saveMessage(message: LocalMessage): Promise<string> {
    if (!this.db) await this.initialize();

    const localId = message.localId || `local_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const now = Date.now();

    try {
      await this.db!.runAsync(`
        INSERT OR REPLACE INTO messages (
          localId, id, chatId, text, senderId, timestamp, status, type,
          mediaUrl, mediaThumbnail, duration, fileName, fileSize,
          callType, callStatus, callDuration, isPinned, pinnedAt, pinnedBy,
          replyToMessageId, replyToText, replyToSenderName, replyToType,
          reactions, syncStatus, createdAt, updatedAt, isEdited, editedAt,
          isDeleted, deletedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        localId,
        message.id || null,
        message.chatId || '',
        message.text || null,
        message.senderId,
        typeof message.timestamp === 'object' ? message.timestamp.getTime() : message.timestamp,
        message.status,
        message.type,
        message.mediaUrl || null,
        message.mediaThumbnail || null,
        message.duration || null,
        message.fileName || null,
        message.fileSize || null,
        message.callType || null,
        message.callStatus || null,
        message.callDuration || null,
        message.isPinned ? 1 : 0,
        message.pinnedAt ? (typeof message.pinnedAt === 'object' ? message.pinnedAt.getTime() : message.pinnedAt) : null,
        message.pinnedBy || null,
        message.replyTo?.messageId || null,
        message.replyTo?.text || null,
        message.replyTo?.senderName || null,
        message.replyTo?.type || null,
        message.reactions ? JSON.stringify(message.reactions) : null,
        message.syncStatus,
        message.createdAt || now,
        now,
        message.isEdited ? 1 : 0,
        message.editedAt || null,
        message.isDeleted ? 1 : 0,
        message.deletedAt || null
      ]);

      console.log('✅ Message saved locally:', localId);
      return localId;
    } catch (error) {
      console.error('❌ Failed to save message locally:', error);
      throw error;
    }
  }

  async getMessages(chatId: string, limit: number = 50, offset: number = 0): Promise<LocalMessage[]> {
    if (!this.db) await this.initialize();

    try {
      const result = await this.db!.getAllAsync(`
        SELECT * FROM messages 
        WHERE chatId = ? AND isDeleted = 0
        ORDER BY timestamp ASC
        LIMIT ? OFFSET ?
      `, [chatId, limit, offset]);

      return result.map(row => this.rowToMessage(row as any));
    } catch (error) {
      console.error('❌ Failed to get messages from local storage:', error);
      return [];
    }
  }

  async getPendingMessages(): Promise<LocalMessage[]> {
    if (!this.db) await this.initialize();

    try {
      const result = await this.db!.getAllAsync(`
        SELECT * FROM messages 
        WHERE syncStatus = 'pending'
        ORDER BY timestamp ASC
      `);

      return result.map(row => this.rowToMessage(row as any));
    } catch (error) {
      console.error('❌ Failed to get pending messages:', error);
      return [];
    }
  }

  async updateMessageSyncStatus(localId: string, syncStatus: 'pending' | 'synced' | 'failed', firebaseId?: string): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      await this.db!.runAsync(`
        UPDATE messages 
        SET syncStatus = ?, id = COALESCE(?, id), updatedAt = ?
        WHERE localId = ?
      `, [syncStatus, firebaseId || null, Date.now(), localId]);

      console.log('✅ Message sync status updated:', localId, syncStatus);
    } catch (error) {
      console.error('❌ Failed to update message sync status:', error);
      throw error;
    }
  }

  async deleteMessage(localId: string, softDelete: boolean = true): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      if (softDelete) {
        await this.db!.runAsync(`
          UPDATE messages 
          SET isDeleted = 1, deletedAt = ?, updatedAt = ?
          WHERE localId = ?
        `, [Date.now(), Date.now(), localId]);
      } else {
        await this.db!.runAsync(`
          DELETE FROM messages WHERE localId = ?
        `, [localId]);
      }

      console.log('✅ Message deleted locally:', localId);
    } catch (error) {
      console.error('❌ Failed to delete message locally:', error);
      throw error;
    }
  }

  async updateMessage(localId: string, updates: Partial<LocalMessage>): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);
      values.push(Date.now(), localId);

      await this.db!.runAsync(`
        UPDATE messages 
        SET ${setClause}, updatedAt = ?
        WHERE localId = ?
      `, values);

      console.log('✅ Message updated locally:', localId);
    } catch (error) {
      console.error('❌ Failed to update message locally:', error);
      throw error;
    }
  }

  async clearChat(chatId: string): Promise<void> {
    if (!this.db) await this.initialize();

    try {
      await this.db!.runAsync(`
        UPDATE messages 
        SET isDeleted = 1, deletedAt = ?, updatedAt = ?
        WHERE chatId = ?
      `, [Date.now(), Date.now(), chatId]);

      console.log('✅ Chat cleared locally:', chatId);
    } catch (error) {
      console.error('❌ Failed to clear chat locally:', error);
      throw error;
    }
  }

  private rowToMessage(row: any): LocalMessage {
    return {
      localId: row.localId,
      id: row.id,
      chatId: row.chatId,
      text: row.text,
      senderId: row.senderId,
      timestamp: new Date(row.timestamp),
      status: row.status,
      type: row.type,
      mediaUrl: row.mediaUrl,
      mediaThumbnail: row.mediaThumbnail,
      duration: row.duration,
      fileName: row.fileName,
      fileSize: row.fileSize,
      callType: row.callType,
      callStatus: row.callStatus,
      callDuration: row.callDuration,
      isPinned: Boolean(row.isPinned),
      pinnedAt: row.pinnedAt ? new Date(row.pinnedAt) : undefined,
      pinnedBy: row.pinnedBy,
      replyTo: row.replyToMessageId ? {
        messageId: row.replyToMessageId,
        text: row.replyToText,
        senderName: row.replyToSenderName,
        type: row.replyToType,
      } : undefined,
      reactions: row.reactions ? JSON.parse(row.reactions) : undefined,
      syncStatus: row.syncStatus,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      isEdited: Boolean(row.isEdited),
      editedAt: row.editedAt ? new Date(row.editedAt) : undefined,
      isDeleted: Boolean(row.isDeleted),
      deletedAt: row.deletedAt ? new Date(row.deletedAt) : undefined,
    };
  }
}

export const localMessageStorage = new LocalMessageStorageService();
