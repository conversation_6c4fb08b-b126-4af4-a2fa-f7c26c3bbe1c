import { FirebaseApp, getApp, getApps, initializeApp } from "firebase/app";
import {
  Auth,
  getAuth,
  initializeAuth
} from "firebase/auth";
import { getFirestore, Firestore } from "firebase/firestore";
import { getStorage, FirebaseStorage } from "firebase/storage";
import { getFunctions, Functions } from "firebase/functions";
import { Platform } from "react-native";

import { firebaseConfig } from "../config/firebase";
import { crashPreventionService } from "./crashPrevention";

// ✅ Validate Firebase config
const validateConfig = () => {
  const required = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
  const missing = required.filter(key => !firebaseConfig[key as keyof typeof firebaseConfig]);

  if (missing.length > 0) {
    throw new Error(`❌ Missing Firebase config: ${missing.join(', ')}`);
  }
};

let firebaseApp: FirebaseApp;
let auth: Auth;
let db: Firestore;
let storage: FirebaseStorage;
let functions: Functions;

try {
  validateConfig();

  // ✅ Initialize crash prevention first
  crashPreventionService.initialize();

  // ✅ Initialize App
  if (getApps().length === 0) {
    firebaseApp = initializeApp(firebaseConfig);
  } else {
    firebaseApp = getApp();
  }

  // ✅ Auth initialization for React Native
  try {
    // Try to get existing auth instance first
    try {
      auth = getAuth(firebaseApp);
      console.log('✅ Firebase Auth retrieved from existing instance');
    } catch {
      // If no existing instance, initialize auth (persistence is automatic in React Native)
      auth = initializeAuth(firebaseApp);
      console.log('✅ Firebase Auth initialized with automatic persistence');
    }
  } catch (authError: any) {
    console.error('❌ Firebase Auth initialization failed:', authError.message);
    throw new Error(`Firebase Auth initialization failed: ${authError.message}`);
  }

  // ✅ Firestore, Storage, Functions
  db = getFirestore(firebaseApp);
  storage = getStorage(firebaseApp);
  functions = getFunctions(firebaseApp);

  // ✅ Set up auth state monitoring for debugging and crash prevention
  if (auth) {
    auth.onAuthStateChanged((user) => {
      try {
        if (user) {
          console.log('🔐 Auth state: User signed in -', user.uid);
          // Verify user object is valid
          if (!user.uid) {
            console.error('❌ Invalid user object: missing UID');
          }
        } else {
          console.log('🔐 Auth state: User signed out');
        }
      } catch (error) {
        console.error('❌ Error in auth state change handler:', error);
      }
    });

    // Add error handler for auth errors
    auth.onIdTokenChanged((user) => {
      try {
        if (user) {
          console.log('🔑 ID token refreshed for user:', user.uid);
        }
      } catch (error) {
        console.error('❌ Error in ID token change handler:', error);
      }
    });
  }

} catch (error: any) {
  console.error('🔥 Firebase init failed:', error.message);
  console.error('🔥 Full error:', error);

  // In development, provide more detailed error info
  if (__DEV__) {
    console.error('🔥 Firebase config:', firebaseConfig);
    console.error('🔥 Platform:', Platform.OS);
  }

  throw error; // let the app crash visibly in dev
}

// ✅ Export services
export { firebaseApp as app, auth, db, storage, functions };
export { db as firestore };

// ✅ Auth helpers
export const getAuthInstance = (): Auth => {
  if (!auth) throw new Error("Firebase Auth not initialized");
  return auth;
};

export const getCurrentUserSafely = () => {
  try {
    return auth?.currentUser || null;
  } catch {
    return null;
  }
};

export const isAuthReady = (): boolean => !!auth;

export const waitForAuth = async (timeoutMs = 3000): Promise<Auth> => {
  return new Promise((resolve) => {
    if (auth) return resolve(auth);

    const timeout = setTimeout(() => {
      console.warn('⚠️ Auth timeout after', timeoutMs, 'ms');
      resolve(null as any);
    }, timeoutMs);

    const check = () => {
      if (auth) {
        clearTimeout(timeout);
        resolve(auth);
      } else {
        setTimeout(check, 200);
      }
    };
    check();
  });
};

// ✅ Debug function to check auth state
export const debugAuthState = () => {
  console.log('🔍 Firebase Auth Debug Info:');
  console.log('  - Auth instance exists:', !!auth);
  console.log('  - Current user:', auth?.currentUser?.uid || 'None');
  console.log('  - Auth ready:', isAuthReady());
  console.log('  - Platform:', Platform.OS);
  console.log('  - App name:', firebaseApp?.name || 'Unknown');

  if (auth?.currentUser) {
    console.log('  - User email:', auth.currentUser.email);
    console.log('  - User phone:', auth.currentUser.phoneNumber);
    console.log('  - Email verified:', auth.currentUser.emailVerified);
  }
};

// ✅ Platform info for logging/debugging
export const getPlatformInfo = () => ({
  platform: Platform.OS,
  authReady: isAuthReady(),
  persistence: "AsyncStorage (React Native)",
  appName: firebaseApp?.name || "Unknown",
  version: "1.0.0",
});
