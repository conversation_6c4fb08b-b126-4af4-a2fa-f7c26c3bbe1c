/**
 * Wallpaper Picker Component for IraChat
 * Allows users to select and customize chat wallpapers
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  Image,
  Alert,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { LinearGradient } from 'expo-linear-gradient';
import { wallpaperService, WallpaperConfig } from '../services/wallpaperService';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface WallpaperPickerProps {
  visible: boolean;
  onClose: () => void;
  chatId: string;
  isGroupChat: boolean;
  currentWallpaper?: WallpaperConfig | null;
  onWallpaperChange?: (config: WallpaperConfig) => void;
}

const PRESET_GRADIENTS = [
  { id: 'blue', colors: ['#87CEEB', '#4682B4'], name: 'Ocean Blue' },
  { id: 'purple', colors: ['#9C27B0', '#673AB7'], name: 'Purple Dream' },
  { id: 'green', colors: ['#4CAF50', '#2E7D32'], name: 'Forest Green' },
  { id: 'orange', colors: ['#FF9800', '#F57C00'], name: 'Sunset Orange' },
  { id: 'pink', colors: ['#E91E63', '#C2185B'], name: 'Rose Pink' },
  { id: 'teal', colors: ['#009688', '#00695C'], name: 'Teal Wave' },
];

const PRESET_PATTERNS = [
  { id: 'dots', name: 'Polka Dots', preview: '⚪' },
  { id: 'stripes', name: 'Stripes', preview: '📏' },
  { id: 'geometric', name: 'Geometric', preview: '🔷' },
  { id: 'floral', name: 'Floral', preview: '🌸' },
];

const SOLID_COLORS = [
  { id: 'black', color: '#000000', name: 'Black' },
  { id: 'white', color: '#FFFFFF', name: 'White' },
  { id: 'gray', color: '#424242', name: 'Dark Gray' },
  { id: 'blue', color: '#2196F3', name: 'Blue' },
  { id: 'green', color: '#4CAF50', name: 'Green' },
  { id: 'red', color: '#F44336', name: 'Red' },
];

export const WallpaperPicker: React.FC<WallpaperPickerProps> = ({
  visible,
  onClose,
  chatId,
  isGroupChat,
  currentWallpaper,
  onWallpaperChange,
}) => {
  const [selectedTab, setSelectedTab] = useState<'default' | 'custom'>('default'); // Fixed: Only Default and Custom tabs
  const [isLoading, setIsLoading] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<WallpaperConfig | null>(currentWallpaper || null);

  // Reset state when modal opens
  useEffect(() => {
    if (visible) {
      console.log('🎨 Wallpaper picker opened, resetting state');
      setIsLoading(false);
      setSelectedTab('default');
      setSelectedConfig(currentWallpaper || null);
    }
  }, [visible, currentWallpaper]);

  useEffect(() => {
    setSelectedConfig(currentWallpaper || null);
  }, [currentWallpaper]);

  const handleCloseModal = () => {
    console.log('🎨 Closing wallpaper picker and clearing selection');
    setSelectedConfig(null); // Clear the selected config
    setIsLoading(false); // Reset loading state
    onClose();
  };

  const handleSaveWallpaper = async () => {
    if (!selectedConfig) {
      Alert.alert('No Selection', 'Please select a wallpaper first.');
      return;
    }

    console.log('🎨 Saving wallpaper:', selectedConfig);
    setIsLoading(true);

    try {
      // Add timeout to prevent infinite loading
      const savePromise = wallpaperService.saveWallpaperForChat(chatId, isGroupChat, selectedConfig);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Wallpaper save timeout')), 10000)
      );

      await Promise.race([savePromise, timeoutPromise]);
      console.log('🎨 Wallpaper saved successfully');

      // Call the change handler
      onWallpaperChange?.(selectedConfig);

      console.log('🎨 Modal closing...');

      // Reset states and close modal
      handleCloseModal();

      console.log('🎨 Modal closed and config cleared');
    } catch (error) {
      console.error('🎨 Failed to save wallpaper:', error);
      setIsLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      Alert.alert('Error', `Failed to save wallpaper: ${errorMessage}. Please try again.`);
    }
  };

  const handleCustomImagePicker = async () => {
    if (isLoading) {
      console.log('Gallery picker already in progress, ignoring...');
      return;
    }

    try {
      setIsLoading(true);

      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant permission to access your photo library.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [9, 16],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        // Convert to base64 for better storage and reliability
        console.log('🎨 Converting image to base64...');
        const base64 = await FileSystem.readAsStringAsync(asset.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        const config: WallpaperConfig = {
          type: 'custom',
          customUri: asset.uri, // Keep URI for immediate preview
          customBase64: base64, // Store base64 for persistence
        };
        setSelectedConfig(config);
        console.log('🎨 Custom wallpaper selected and converted to base64');
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderTabContent = () => {
    switch (selectedTab) {
      case 'default':
        return (
          <TouchableOpacity
            style={[styles.wallpaperOption, selectedConfig?.type === 'default' && styles.selectedOption]}
            onPress={() => {
              setSelectedConfig({ type: 'default' });
              // Fixed: Clear custom wallpaper when default is selected
              wallpaperService.clearWallpaperForChat(chatId);
            }}
          >
            <View style={styles.defaultWallpaper}>
              <Text style={styles.defaultText}>Default</Text>
            </View>
            <Text style={styles.optionName}>Default Wallpaper</Text>
          </TouchableOpacity>
        );

      case 'custom': // Fixed: Removed solid case
        return (
          <View style={styles.customSection}>
            {selectedConfig?.type === 'custom' && selectedConfig.customUri ? (
              <Image
                source={{ uri: selectedConfig.customUri }}
                style={styles.customPreview}
                resizeMode="cover" // Fixed: Maintain aspect ratio
              />
            ) : (
              <View style={styles.customPlaceholder}>
                <Ionicons name="image-outline" size={48} color="#87CEEB" />
                <Text style={styles.customPlaceholderText}>
                  Tap "Custom" tab to select an image from your gallery
                </Text>
              </View>
            )}
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCloseModal}>
            <Ionicons name="close" size={24} color="#87CEEB" />
          </TouchableOpacity>
          <Text style={styles.title}>Choose Wallpaper</Text>
          <TouchableOpacity onPress={handleSaveWallpaper} disabled={isLoading}>
            {isLoading ? (
              <ActivityIndicator size="small" color="#87CEEB" />
            ) : (
              <Text style={styles.saveButton}>Save</Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.tabs}>
          {(['default', 'custom'] as const).map((tab) => ( // Fixed: Only Default and Custom tabs
            <TouchableOpacity
              key={tab}
              style={[styles.tab, selectedTab === tab && styles.activeTab]}
              onPress={() => {
                if (isLoading) return; // Prevent multiple calls

                setSelectedTab(tab);
                if (tab === 'custom') {
                  // Directly open gallery when Custom tab is tapped
                  setTimeout(() => {
                    handleCustomImagePicker();
                  }, 100); // Small delay to prevent multiple calls
                }
              }}
            >
              <Text style={[styles.tabText, selectedTab === tab && styles.activeTabText]}>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.content}>
          {renderTabContent()}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#2A2A2A',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  saveButton: {
    fontSize: 16,
    fontWeight: '600',
    color: '#87CEEB',
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: '#1A1A1A',
    paddingHorizontal: 16,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#87CEEB',
  },
  tabText: {
    fontSize: 14,
    color: '#888888',
  },
  activeTabText: {
    color: '#87CEEB',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  wallpaperOption: {
    alignItems: 'center',
    marginRight: 16,
    padding: 8,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedOption: {
    borderColor: '#87CEEB',
  },
  defaultWallpaper: {
    width: 80,
    height: 120,
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  defaultText: {
    color: '#FFFFFF',
    fontSize: 12,
  },
  gradientPreview: {
    width: 80,
    height: 120,
    borderRadius: 8,
  },
  patternPreview: {
    width: 80,
    height: 120,
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  patternEmoji: {
    fontSize: 24,
  },
  colorPreview: {
    width: 80,
    height: 120,
    borderRadius: 8,
  },
  optionName: {
    marginTop: 8,
    fontSize: 12,
    color: '#FFFFFF',
    textAlign: 'center',
  },
  customSection: {
    alignItems: 'center',
  },
  customButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginBottom: 16,
  },
  customButtonText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#87CEEB',
  },
  customPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  customPlaceholderText: {
    marginTop: 16,
    fontSize: 14,
    color: '#888888',
    textAlign: 'center',
    lineHeight: 20,
  },
  customPreview: {
    width: SCREEN_WIDTH - 32,
    height: (SCREEN_WIDTH - 32) * 1.5, // Fixed: Portrait aspect ratio (3:2)
    borderRadius: 8,
    resizeMode: 'cover', // Fixed: Maintain aspect ratio and crop if needed
  },
});
