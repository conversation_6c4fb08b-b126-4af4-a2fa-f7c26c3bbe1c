// Sign In Screen for IraChat - Choose sign-in method
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
  Text,
  TouchableOpacity,
  View,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { AnimatedButton } from '../../src/components/ui/AnimatedButton';
import { IraChatWallpaper } from '../../src/components/ui/IraChatWallpaper';
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from '../../src/styles/iraChatDesignSystem';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const SignInScreen: React.FC = () => {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleEmailSignIn = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push('/(auth)/email-sign-in');
    setTimeout(() => setIsNavigating(false), 1000);
  };

  const handlePhoneSignIn = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.push('/(auth)/phone-sign-in');
    setTimeout(() => setIsNavigating(false), 1000);
  };

  const handleBackToWelcome = () => {
    if (isNavigating) return;
    setIsNavigating(true);
    router.back();
    setTimeout(() => setIsNavigating(false), 1000);
  };

  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#000000" />
      <IraChatWallpaper />
      
      <SafeAreaView style={styles.safeArea}>
        <Animated.View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBackToWelcome}
              disabled={isNavigating}
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>

            <View style={styles.logoContainer}>
              <Text style={styles.logoText}>IraChat</Text>
            </View>
          </View>

          {/* Welcome Back Message */}
          <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeTitle}>Welcome Back!</Text>
            <Text style={styles.welcomeSubtitle}>
              Sign in to your IraChat account
            </Text>
          </View>

          {/* Sign In Options */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.signInButton, styles.emailButton]}
              onPress={handleEmailSignIn}
              disabled={isNavigating}
            >
              <Ionicons name="mail" size={20} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={styles.buttonText}>
                {isNavigating ? "Loading..." : "Sign In with Email"}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.signInButton, styles.phoneButton]}
              onPress={handlePhoneSignIn}
              disabled={isNavigating}
            >
              <Ionicons name="call" size={20} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={styles.buttonText}>
                {isNavigating ? "Loading..." : "Sign In with Phone"}
              </Text>
            </TouchableOpacity>
          </View>

          {/* Create Account Link */}
          <View style={styles.createAccountContainer}>
            <Text style={styles.createAccountText}>Don't have an account? </Text>
            <TouchableOpacity 
              onPress={() => router.push('/(auth)/welcome')}
              disabled={isNavigating}
            >
              <Text style={styles.createAccountLink}>Create Account</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  safeArea: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.md,
    justifyContent: 'space-between',
    paddingVertical: SPACING.lg,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: SPACING.sm,
    marginBottom: SPACING.lg,
  },
  backButton: {
    padding: SPACING.sm,
    marginRight: SPACING.md,
  },
  logoContainer: {
    flex: 1,
    alignItems: 'center',
    marginRight: 40, // Offset for back button
  },
  logoText: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontFamily: TYPOGRAPHY.fontFamilyBold,
  },
  welcomeContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
    paddingHorizontal: SPACING.md,
  },
  welcomeTitle: {
    fontSize: TYPOGRAPHY.fontSize['3xl'],
    color: '#FFFFFF',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: SPACING.sm,
    fontFamily: TYPOGRAPHY.fontFamilyBold,
  },
  welcomeSubtitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  buttonContainer: {
    paddingHorizontal: SPACING.md,
    gap: SPACING.md,
    marginBottom: SPACING.xl,
  },
  signInButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: BORDER_RADIUS.md,
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
    minHeight: 50,
  },
  buttonIcon: {
    marginRight: SPACING.sm,
  },
  buttonText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: '#FFFFFF',
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  emailButton: {
    marginBottom: SPACING.sm,
  },
  phoneButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  createAccountContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: SPACING.lg,
    paddingHorizontal: SPACING.md,
  },
  createAccountText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  createAccountLink: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: '#FFFFFF',
    fontWeight: 'bold',
    textDecorationLine: 'underline',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
});

export default SignInScreen;
