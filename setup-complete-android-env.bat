@echo off
echo ========================================
echo Complete Android Development Setup
echo ========================================
echo.

REM Set Android SDK path
set ANDROID_HOME=F:\Android\Sdk
set ANDROID_SDK_ROOT=F:\Android\Sdk
echo ✓ ANDROID_HOME set to: %ANDROID_HOME%

REM Add Android tools to PATH
set PATH=%PATH%;F:\Android\Sdk\platform-tools;F:\Android\Sdk\cmdline-tools\latest\bin;F:\Android\Sdk\emulator;F:\Android\Sdk\build-tools
echo ✓ Android tools added to PATH

REM Check if Java is installed
echo.
echo Checking Java installation...
java -version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Java is installed
    java -version
) else (
    echo ❌ Java is NOT installed or not in PATH
    echo.
    echo Please install Java JDK 17 or later from:
    echo https://adoptium.net/temurin/releases/
    echo.
    echo After installing Java, set JAVA_HOME to the JDK installation directory
    echo Example: set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
    echo.
    pause
    goto :end
)

REM Set JAVA_HOME if not set
if "%JAVA_HOME%"=="" (
    echo.
    echo ❌ JAVA_HOME is not set
    echo Please set JAVA_HOME to your JDK installation directory
    echo Example: set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-********-hotspot
    echo.
    pause
    goto :end
) else (
    echo ✓ JAVA_HOME is set to: %JAVA_HOME%
)

REM Test ADB
echo.
echo Testing ADB connection...
adb version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ ADB is working
    adb devices
) else (
    echo ❌ ADB is not working
    goto :end
)

REM Test Gradle
echo.
echo Testing Gradle...
cd android
gradlew.bat --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ Gradle is working
) else (
    echo ❌ Gradle test failed
)
cd ..

echo.
echo ========================================
echo Environment setup complete!
echo ========================================
echo.
echo You can now run: npm run android
echo.

:end
pause
