/**
 * Comprehensive In-Memory Caching System for IraChat
 * Provides LRU caching with memory management and cache invalidation
 * Similar to <PERSON>s<PERSON>pp's in-memory caching for instant access
 */

import { Message, Chat, Contact, User } from '../types';

interface CacheItem<T> {
  data: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number; // Estimated size in bytes
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  totalSize: number;
  itemCount: number;
}

interface CacheConfig {
  maxSize: number; // Maximum cache size in bytes
  maxItems: number; // Maximum number of items
  ttl: number; // Time to live in milliseconds
  cleanupInterval: number; // Cleanup interval in milliseconds
}

class LRUCache<T> {
  private cache = new Map<string, CacheItem<T>>();
  private accessOrder = new Map<string, number>(); // key -> access order
  private currentAccessOrder = 0;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalSize: 0,
    itemCount: 0,
  };

  constructor(private config: CacheConfig) {
    // Start periodic cleanup
    setInterval(() => this.cleanup(), config.cleanupInterval);
  }

  set(key: string, value: T): void {
    const size = this.estimateSize(value);
    const now = Date.now();
    
    // Remove existing item if present
    if (this.cache.has(key)) {
      this.delete(key);
    }

    // Check if we need to evict items
    this.evictIfNeeded(size);

    const item: CacheItem<T> = {
      data: value,
      timestamp: now,
      accessCount: 1,
      lastAccessed: now,
      size,
    };

    this.cache.set(key, item);
    this.accessOrder.set(key, ++this.currentAccessOrder);
    this.stats.totalSize += size;
    this.stats.itemCount++;
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      this.stats.misses++;
      return null;
    }

    // Check TTL
    if (Date.now() - item.timestamp > this.config.ttl) {
      this.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access info
    item.accessCount++;
    item.lastAccessed = Date.now();
    this.accessOrder.set(key, ++this.currentAccessOrder);
    this.stats.hits++;

    return item.data;
  }

  delete(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    this.cache.delete(key);
    this.accessOrder.delete(key);
    this.stats.totalSize -= item.size;
    this.stats.itemCount--;
    return true;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    if (!item) return false;

    // Check TTL
    if (Date.now() - item.timestamp > this.config.ttl) {
      this.delete(key);
      return false;
    }

    return true;
  }

  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
    this.stats.totalSize = 0;
    this.stats.itemCount = 0;
  }

  getStats(): CacheStats {
    return { ...this.stats };
  }

  private evictIfNeeded(newItemSize: number): void {
    // Evict by size
    while (this.stats.totalSize + newItemSize > this.config.maxSize && this.cache.size > 0) {
      this.evictLRU();
    }

    // Evict by count
    while (this.stats.itemCount >= this.config.maxItems && this.cache.size > 0) {
      this.evictLRU();
    }
  }

  private evictLRU(): void {
    let oldestKey: string | null = null;
    let oldestOrder = Infinity;

    for (const [key, order] of this.accessOrder) {
      if (order < oldestOrder) {
        oldestOrder = order;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
      this.stats.evictions++;
    }
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, item] of this.cache) {
      if (now - item.timestamp > this.config.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.delete(key));
  }

  private estimateSize(value: T): number {
    try {
      return JSON.stringify(value).length * 2; // Rough estimate (UTF-16)
    } catch {
      return 1000; // Default size if serialization fails
    }
  }
}

/**
 * Memory Cache Service - Central caching system for IraChat
 */
class MemoryCacheService {
  private messageCache: LRUCache<Message>;
  private chatCache: LRUCache<Chat>;
  private contactCache: LRUCache<Contact>;
  private userCache: LRUCache<User>;
  private mediaCache: LRUCache<string>; // For media URLs/base64
  private settingsCache: LRUCache<any>;

  constructor() {
    // Configure different caches with appropriate sizes
    const messageCacheConfig: CacheConfig = {
      maxSize: 50 * 1024 * 1024, // 50MB for messages
      maxItems: 10000,
      ttl: 24 * 60 * 60 * 1000, // 24 hours
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
    };

    const chatCacheConfig: CacheConfig = {
      maxSize: 10 * 1024 * 1024, // 10MB for chats
      maxItems: 1000,
      ttl: 24 * 60 * 60 * 1000, // 24 hours
      cleanupInterval: 5 * 60 * 1000,
    };

    const contactCacheConfig: CacheConfig = {
      maxSize: 20 * 1024 * 1024, // 20MB for contacts
      maxItems: 5000,
      ttl: 7 * 24 * 60 * 60 * 1000, // 7 days
      cleanupInterval: 10 * 60 * 1000, // 10 minutes
    };

    const userCacheConfig: CacheConfig = {
      maxSize: 15 * 1024 * 1024, // 15MB for users
      maxItems: 2000,
      ttl: 6 * 60 * 60 * 1000, // 6 hours
      cleanupInterval: 5 * 60 * 1000,
    };

    const mediaCacheConfig: CacheConfig = {
      maxSize: 100 * 1024 * 1024, // 100MB for media
      maxItems: 1000,
      ttl: 7 * 24 * 60 * 60 * 1000, // 7 days
      cleanupInterval: 15 * 60 * 1000, // 15 minutes
    };

    const settingsCacheConfig: CacheConfig = {
      maxSize: 5 * 1024 * 1024, // 5MB for settings
      maxItems: 500,
      ttl: 24 * 60 * 60 * 1000, // 24 hours
      cleanupInterval: 10 * 60 * 1000,
    };

    this.messageCache = new LRUCache<Message>(messageCacheConfig);
    this.chatCache = new LRUCache<Chat>(chatCacheConfig);
    this.contactCache = new LRUCache<Contact>(contactCacheConfig);
    this.userCache = new LRUCache<User>(userCacheConfig);
    this.mediaCache = new LRUCache<string>(mediaCacheConfig);
    this.settingsCache = new LRUCache<any>(settingsCacheConfig);

    console.log('✅ Memory cache service initialized');
  }

  // Message caching methods
  setMessage(messageId: string, message: Message): void {
    this.messageCache.set(messageId, message);
  }

  getMessage(messageId: string): Message | null {
    return this.messageCache.get(messageId);
  }

  deleteMessage(messageId: string): boolean {
    return this.messageCache.delete(messageId);
  }

  // Chat caching methods
  setChat(chatId: string, chat: Chat): void {
    this.chatCache.set(chatId, chat);
  }

  getChat(chatId: string): Chat | null {
    return this.chatCache.get(chatId);
  }

  deleteChat(chatId: string): boolean {
    return this.chatCache.delete(chatId);
  }

  // Contact caching methods
  setContact(contactId: string, contact: Contact): void {
    this.contactCache.set(contactId, contact);
  }

  getContact(contactId: string): Contact | null {
    return this.contactCache.get(contactId);
  }

  deleteContact(contactId: string): boolean {
    return this.contactCache.delete(contactId);
  }

  // User caching methods
  setUser(userId: string, user: User): void {
    this.userCache.set(userId, user);
  }

  getUser(userId: string): User | null {
    return this.userCache.get(userId);
  }

  deleteUser(userId: string): boolean {
    return this.userCache.delete(userId);
  }

  // Media caching methods
  setMedia(mediaId: string, mediaData: string): void {
    this.mediaCache.set(mediaId, mediaData);
  }

  getMedia(mediaId: string): string | null {
    return this.mediaCache.get(mediaId);
  }

  deleteMedia(mediaId: string): boolean {
    return this.mediaCache.delete(mediaId);
  }

  // Settings caching methods
  setSettings(key: string, settings: any): void {
    this.settingsCache.set(key, settings);
  }

  getSettings(key: string): any | null {
    return this.settingsCache.get(key);
  }

  deleteSettings(key: string): boolean {
    return this.settingsCache.delete(key);
  }

  // Bulk operations
  setMessages(messages: Message[]): void {
    messages.forEach(message => {
      if (message.id) {
        this.setMessage(message.id, message);
      }
    });
  }

  getMessages(messageIds: string[]): (Message | null)[] {
    return messageIds.map(id => this.getMessage(id));
  }

  // Cache management
  clearAll(): void {
    this.messageCache.clear();
    this.chatCache.clear();
    this.contactCache.clear();
    this.userCache.clear();
    this.mediaCache.clear();
    this.settingsCache.clear();
    console.log('🧹 All caches cleared');
  }

  clearExpired(): void {
    // Cleanup is handled automatically by each cache
    console.log('🧹 Expired cache items cleaned up');
  }

  getOverallStats(): Record<string, CacheStats> {
    return {
      messages: this.messageCache.getStats(),
      chats: this.chatCache.getStats(),
      contacts: this.contactCache.getStats(),
      users: this.userCache.getStats(),
      media: this.mediaCache.getStats(),
      settings: this.settingsCache.getStats(),
    };
  }

  // Memory pressure handling
  handleMemoryPressure(): void {
    console.log('⚠️ Memory pressure detected, clearing non-essential caches');
    
    // Clear media cache first (largest)
    this.mediaCache.clear();
    
    // Clear older messages
    const messageStats = this.messageCache.getStats();
    if (messageStats.itemCount > 5000) {
      // Force eviction of half the messages
      for (let i = 0; i < messageStats.itemCount / 2; i++) {
        // This would require implementing a method to get LRU keys
        // For now, we'll just clear the entire cache
        this.messageCache.clear();
        break;
      }
    }
    
    console.log('✅ Memory pressure handling completed');
  }
}

// Export singleton instance
export const memoryCacheService = new MemoryCacheService();
export default memoryCacheService;
