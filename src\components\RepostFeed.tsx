// 🔄 REAL-TIME REPOST FEED COMPONENT
// Shows user's reposts with live Firestore updates

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { repostService } from '../services/repostService';
import { auth } from '../services/firebaseSimple';
import { formatTimeAgo } from '../utils/dateUtils';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface RepostItem {
  id: string;
  mediaUrl: string;
  type: 'photo' | 'video';
  caption: string;
  timestamp: any;
  originalAuthor: string;
  originalCaption?: string;
  likes: string[];
  views: string[];
  shares: string[];
}

interface RepostFeedProps {
  onRepostPress?: (repost: RepostItem) => void;
  maxItems?: number;
}

export const RepostFeed: React.FC<RepostFeedProps> = ({
  onRepostPress,
  maxItems = 20
}) => {
  const [reposts, setReposts] = useState<RepostItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const currentUser = auth?.currentUser;
    if (!currentUser) {
      setError('Please sign in to view reposts');
      setLoading(false);
      return;
    }

    console.log('📡 Setting up real-time repost feed for user:', currentUser.uid);

    // Subscribe to real-time repost updates
    const unsubscribe = repostService.subscribeToRepostFeed(
      currentUser.uid,
      (updatedReposts) => {
        console.log('🔄 Received repost updates:', updatedReposts.length);
        setReposts(updatedReposts.slice(0, maxItems));
        setLoading(false);
        setError(null);
      }
    );

    // Cleanup subscription on unmount
    return () => {
      console.log('🔌 Cleaning up repost feed subscription');
      unsubscribe();
    };
  }, [maxItems]);

  const handleRepostPress = (repost: RepostItem) => {
    if (onRepostPress) {
      onRepostPress(repost);
    } else {
      // Default action - show repost details
      Alert.alert(
        'Repost Details',
        `Originally by @${repost.originalAuthor}\n\n${repost.originalCaption || 'No caption'}`,
        [{ text: 'OK' }]
      );
    }
  };

  const handleDeleteRepost = (repostId: string) => {
    Alert.alert(
      'Delete Repost',
      'Are you sure you want to delete this repost?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement delete functionality in repostService
              console.log('🗑️ Deleting repost:', repostId);
              Alert.alert('Success', 'Repost deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete repost');
            }
          }
        }
      ]
    );
  };

  const renderRepostItem = ({ item }: { item: RepostItem }) => (
    <TouchableOpacity
      style={styles.repostItem}
      onPress={() => handleRepostPress(item)}
      activeOpacity={0.8}
    >
      {/* Media thumbnail */}
      <View style={styles.mediaContainer}>
        <Image source={{ uri: item.mediaUrl }} style={styles.mediaThumbnail} />
        {item.type === 'video' && (
          <View style={styles.videoIndicator}>
            <Ionicons name="play" size={20} color="#FFFFFF" />
          </View>
        )}
        <View style={styles.repostBadge}>
          <Ionicons name="repeat" size={12} color="#FFFFFF" />
        </View>
      </View>

      {/* Content */}
      <View style={styles.contentContainer}>
        <Text style={styles.caption} numberOfLines={2}>
          {item.caption}
        </Text>
        <Text style={styles.originalAuthor}>
          Originally by @{item.originalAuthor}
        </Text>
        <Text style={styles.timestamp}>
          {formatTimeAgo(item.timestamp?.toDate?.() || new Date())}
        </Text>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Ionicons name="heart" size={14} color="#FF3040" />
            <Text style={styles.statText}>{item.likes?.length || 0}</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="eye" size={14} color="#666" />
            <Text style={styles.statText}>{item.views?.length || 0}</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="share" size={14} color="#666" />
            <Text style={styles.statText}>{item.shares?.length || 0}</Text>
          </View>
        </View>
      </View>

      {/* Actions */}
      <TouchableOpacity
        style={styles.deleteButton}
        onPress={() => handleDeleteRepost(item.id)}
      >
        <Ionicons name="trash-outline" size={18} color="#666" />
      </TouchableOpacity>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#667eea" />
        <Text style={styles.loadingText}>Loading reposts...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="alert-circle" size={48} color="#FF3040" />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  if (reposts.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Ionicons name="repeat" size={48} color="#666" />
        <Text style={styles.emptyText}>No reposts yet</Text>
        <Text style={styles.emptySubtext}>
          Repost media to stories to see them here
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="repeat" size={20} color="#667eea" />
        <Text style={styles.headerText}>Your Reposts</Text>
        <Text style={styles.countText}>({reposts.length})</Text>
      </View>

      <FlatList
        data={reposts}
        renderItem={renderRepostItem}
        keyExtractor={(item) => item.id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5E5',
  },
  headerText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginLeft: 8,
  },
  countText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 4,
  },
  listContainer: {
    padding: 16,
  },
  repostItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  mediaContainer: {
    position: 'relative',
    marginRight: 12,
  },
  mediaThumbnail: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#F0F0F0',
  },
  videoIndicator: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  repostBadge: {
    position: 'absolute',
    bottom: -4,
    right: -4,
    backgroundColor: '#667eea',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },
  caption: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  originalAuthor: {
    fontSize: 12,
    color: '#667eea',
    marginBottom: 4,
  },
  timestamp: {
    fontSize: 11,
    color: '#999',
    marginBottom: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  statText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  deleteButton: {
    padding: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3040',
    textAlign: 'center',
    marginTop: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
});
