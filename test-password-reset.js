// Test Password Reset Functionality
require('dotenv').config();

console.log('🔧 Loading Firebase modules...');

const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword, sendPasswordResetEmail, deleteUser } = require('firebase/auth');

console.log('✅ Firebase modules loaded successfully');

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID
};

async function testPasswordReset() {
  console.log('🧪 Testing Password Reset System');
  console.log('=================================');

  try {
    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    
    console.log('✅ Firebase initialized successfully');
    console.log('📧 Auth Domain:', firebaseConfig.authDomain);
    
    // Create a test user first
    const testEmail = `test-reset-${Date.now()}@example.com`;
    const testPassword = 'TestPassword123!';
    
    console.log('\n📋 Step 1: Creating test user for password reset...');
    console.log('📧 Test email:', testEmail);
    
    const userCredential = await createUserWithEmailAndPassword(auth, testEmail, testPassword);
    const user = userCredential.user;
    
    console.log('✅ Test user created successfully');
    console.log('👤 User UID:', user.uid);
    console.log('📧 User email:', user.email);
    
    console.log('\n📋 Step 2: Testing password reset email...');
    
    try {
      await sendPasswordResetEmail(auth, testEmail);
      console.log('✅ Password reset email sent successfully!');
      console.log('📧 Reset email sent to:', testEmail);
      console.log('🔗 Check your email for the password reset link');
      
    } catch (resetError) {
      console.error('❌ Failed to send password reset email:', resetError);
      console.error('❌ Error code:', resetError.code);
      console.error('❌ Error message:', resetError.message);
      
      if (resetError.code === 'auth/user-not-found') {
        console.log('💡 Suggestion: User not found (this shouldn\'t happen since we just created the user)');
      } else if (resetError.code === 'auth/invalid-email') {
        console.log('💡 Suggestion: Check if the email format is valid');
      } else if (resetError.code === 'auth/too-many-requests') {
        console.log('💡 Suggestion: Too many requests, try again later');
      } else {
        console.log('💡 Suggestion: Check Firebase Authentication settings in console');
        console.log('💡 Make sure password reset is enabled');
      }
    }
    
    console.log('\n📋 Step 3: Testing password reset with non-existent email...');
    
    try {
      const nonExistentEmail = `nonexistent-${Date.now()}@example.com`;
      await sendPasswordResetEmail(auth, nonExistentEmail);
      console.log('✅ Password reset for non-existent email handled gracefully');
      console.log('📧 This is expected behavior - Firebase doesn\'t reveal if email exists');
      
    } catch (nonExistentError) {
      console.log('ℹ️ Password reset for non-existent email:', nonExistentError.code);
      // This is expected - Firebase may or may not throw an error for non-existent emails
    }
    
    console.log('\n📋 Step 4: Testing invalid email format...');
    
    try {
      await sendPasswordResetEmail(auth, 'invalid-email-format');
      console.log('⚠️ Unexpected: Invalid email format was accepted');
      
    } catch (invalidError) {
      if (invalidError.code === 'auth/invalid-email') {
        console.log('✅ Invalid email format correctly rejected');
      } else {
        console.log('❌ Unexpected error for invalid email:', invalidError.code);
      }
    }
    
    console.log('\n📋 Step 5: Cleaning up test user...');
    
    try {
      await user.delete();
      console.log('✅ Test user deleted successfully');
    } catch (deleteError) {
      console.error('❌ Failed to delete test user:', deleteError);
      console.log('⚠️ You may need to manually delete the user from Firebase Console');
    }
    
    console.log('\n🎉 Password reset test completed successfully!');
    console.log('📧 Key findings:');
    console.log('   - Password reset emails can be sent to existing users');
    console.log('   - Invalid email formats are properly rejected');
    console.log('   - Non-existent emails are handled gracefully');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('❌ Error code:', error.code);
    console.error('❌ Error message:', error.message);
    
    if (error.code === 'auth/email-already-in-use') {
      console.log('💡 Test email already exists, this might affect the test');
    } else if (error.code === 'auth/weak-password') {
      console.log('💡 Password is too weak, but this shouldn\'t happen with our test password');
    } else if (error.code === 'auth/invalid-email') {
      console.log('💡 Invalid email format');
    } else {
      console.log('💡 Check Firebase configuration and network connection');
    }
  }
}

// Run the test
testPasswordReset().catch(console.error);
