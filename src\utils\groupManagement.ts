import {
  collection,
  doc,
  getDoc,
  getDocs,
  limit,
  orderBy,
  query,
  serverTimestamp,
  setDoc
} from 'firebase/firestore';
import { Alert, Share, Dimensions } from "react-native";
import * as Clipboard from 'expo-clipboard';
import { db } from '../services/firebaseSimple';
import { GroupChat } from "../types/groupChat";
import { GroupMemberPreferences } from "../types/index";

// Get device dimensions for responsive group management
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;

// Group action result interface
export interface GroupActionResult {
  success: boolean;
  message: string;
  error?: string;
  isOffline?: boolean;
}

// Group statistics interface
export interface GroupStats {
  memberCount: number;
  messageCount: number;
  mediaCount: number;
  adminCount: number;
  onlineMembers?: number;
  lastActivity?: Date;
}

// Recent activity interface
export interface RecentActivity {
  type: "message" | "member" | "media" | "admin";
  timestamp: number;
  description: string;
  userId?: string;
  userName?: string;
}

export const defaultMemberPreferences: GroupMemberPreferences = {
  notifications: {
    messages: true,
    mentions: true,
    reactions: true,
  },
  privacy: {
    readReceipts: true,
    lastSeen: true,
    profilePhoto: true,
  },
  media: {
    autoDownload: true,
    quality: "medium" as const,
  },
  isMuted: false,
  isArchived: false,
  isLocked: false,
  hiddenMessages: [],
  hiddenUpdates: [],
};

/**
 * Enhanced group action handler with mobile optimization and offline support
 */
export const handleGroupAction = async (
  action: string,
  groupId: string,
  userId: string,
  isAdmin: boolean,
  onAction: (_action: string, _groupId: string, _userId: string) => Promise<void>,
): Promise<GroupActionResult> => {
  try {
    // Get responsive alert titles and messages
    const getAlertTitle = (actionType: string): string => {
      const titles: Record<string, string> = {
        mute: isSmallDevice ? "Muted" : "Group Muted",
        unmute: isSmallDevice ? "Unmuted" : "Group Unmuted",
        archive: isSmallDevice ? "Archived" : "Group Archived",
        unarchive: isSmallDevice ? "Unarchived" : "Group Unarchived",
        lock: isSmallDevice ? "Locked" : "Group Locked",
        unlock: isSmallDevice ? "Unlocked" : "Group Unlocked",
        block: isSmallDevice ? "Blocked" : "Member Blocked",
        unblock: isSmallDevice ? "Unblocked" : "Member Unblocked",
        report: isSmallDevice ? "Reported" : "Group Reported",
        unreport: isSmallDevice ? "Report Removed" : "Report Removed",
      };
      return titles[actionType] || "Action Complete";
    };

    switch (action) {
      case "mute":
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("mute"),
          isSmallDevice
            ? "Notifications disabled"
            : "You will no longer receive notifications from this group.",
        );
        break;
      case "unmute":
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("unmute"),
          isSmallDevice
            ? "Notifications enabled"
            : "You will now receive notifications from this group.",
        );
        break;
      case "archive":
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("archive"),
          isSmallDevice ? "Group archived" : "This group has been archived."
        );
        break;
      case "unarchive":
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("unarchive"),
          isSmallDevice ? "Group unarchived" : "This group has been unarchived."
        );
        break;
      case "lock":
        if (!isAdmin) {
          Alert.alert(
            isSmallDevice ? "Access Denied" : "Permission Denied",
            isSmallDevice ? "Admin only" : "Only group admins can lock the group.",
          );
          return { success: false, message: "Permission denied" };
        }
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("lock"),
          isSmallDevice
            ? "Admin-only messaging"
            : "Only admins can send messages in this group now.",
        );
        break;
      case "unlock":
        if (!isAdmin) {
          Alert.alert(
            isSmallDevice ? "Access Denied" : "Permission Denied",
            isSmallDevice ? "Admin only" : "Only group admins can unlock the group.",
          );
          return { success: false, message: "Permission denied" };
        }
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("unlock"),
          isSmallDevice
            ? "All can message"
            : "All members can now send messages in this group.",
        );
        break;
      case "block":
        if (!isAdmin) {
          Alert.alert(
            isSmallDevice ? "Access Denied" : "Permission Denied",
            isSmallDevice ? "Admin only" : "Only group admins can block members.",
          );
          return { success: false, message: "Permission denied" };
        }
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("block"),
          isSmallDevice
            ? "Member blocked"
            : "This member can no longer participate in the group until unblocked by an admin.",
        );
        break;
      case "unblock":
        if (!isAdmin) {
          Alert.alert(
            isSmallDevice ? "Access Denied" : "Permission Denied",
            isSmallDevice ? "Admin only" : "Only group admins can unblock members.",
          );
          return { success: false, message: "Permission denied" };
        }
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("unblock"),
          isSmallDevice
            ? "Member unblocked"
            : "This member can now participate in the group again.",
        );
        break;
      case "report":
        await onAction(action, groupId, userId);
        Alert.alert(
          getAlertTitle("report"),
          isSmallDevice
            ? "Report submitted"
            : "Thank you for your report. We will review it shortly.",
        );
        break;
      case "unreport":
        await onAction(action, groupId, userId);
        Alert.alert(getAlertTitle("unreport"), "Your report has been removed.");
        break;
      case "share":
        const shareResult = await shareGroupLink(groupId);
        return shareResult;
      case "copyLink":
        const copyResult = await copyGroupLink(groupId);
        return copyResult;
      default:
        throw new Error("Invalid action");
    }

    return { success: true, message: "Action completed successfully" };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Failed to perform the requested action.";
    Alert.alert("Error", errorMessage);
    return {
      success: false,
      message: errorMessage,
      error: errorMessage
    };
  }
};

/**
 * Enhanced content hiding with mobile optimization
 */
export const handleContentHide = async (
  type: "message" | "update",
  contentId: string,
  userId: string,
  onHide: (
    _type: "message" | "update",
    _contentId: string,
    _userId: string,
  ) => Promise<void>,
): Promise<GroupActionResult> => {
  try {
    await onHide(type, contentId, userId);

    const title = isSmallDevice ? "Hidden" : "Content Hidden";
    const message = isSmallDevice
      ? `${type} hidden`
      : `This ${type} has been hidden from your view. You can unhide it in group settings.`;

    Alert.alert(title, message);

    return {
      success: true,
      message: `${type} hidden successfully`
    };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Failed to hide the content.";
    Alert.alert("Error", errorMessage);
    return {
      success: false,
      message: errorMessage,
      error: errorMessage
    };
  }
};

/**
 * Enhanced admin action handler with mobile optimization
 */
export const handleAdminAction = async (
  action: "deleteMessage" | "editSettings" | "addAdmin" | "removeAdmin",
  groupId: string,
  targetId: string,
  isAdmin: boolean,
  onAction: (
    _action: string,
    _groupId: string,
    _targetId: string,
  ) => Promise<void>,
): Promise<GroupActionResult> => {
  if (!isAdmin) {
    const title = isSmallDevice ? "Access Denied" : "Permission Denied";
    const message = isSmallDevice ? "Admin only" : "Only group admins can perform this action.";
    Alert.alert(title, message);
    return { success: false, message: "Permission denied" };
  }

  try {
    const getActionMessage = (actionType: string): { title: string; message: string } => {
      const messages: Record<string, { title: string; message: string }> = {
        deleteMessage: {
          title: isSmallDevice ? "Deleted" : "Message Deleted",
          message: isSmallDevice ? "Message removed" : "The message has been deleted for all members."
        },
        editSettings: {
          title: isSmallDevice ? "Updated" : "Settings Updated",
          message: isSmallDevice ? "Settings saved" : "Group settings have been updated."
        },
        addAdmin: {
          title: isSmallDevice ? "Admin Added" : "Admin Added",
          message: isSmallDevice ? "Member promoted" : "The member has been promoted to admin."
        },
        removeAdmin: {
          title: isSmallDevice ? "Admin Removed" : "Admin Removed",
          message: isSmallDevice ? "Member demoted" : "The member has been demoted from admin."
        }
      };
      return messages[actionType] || { title: "Action Complete", message: "Action completed" };
    };

    switch (action) {
      case "deleteMessage":
        await onAction(action, groupId, targetId);
        const deleteMsg = getActionMessage("deleteMessage");
        Alert.alert(deleteMsg.title, deleteMsg.message);
        break;
      case "editSettings":
        await onAction(action, groupId, targetId);
        const settingsMsg = getActionMessage("editSettings");
        Alert.alert(settingsMsg.title, settingsMsg.message);
        break;
      case "addAdmin":
        await onAction(action, groupId, targetId);
        const addMsg = getActionMessage("addAdmin");
        Alert.alert(addMsg.title, addMsg.message);
        break;
      case "removeAdmin":
        await onAction(action, groupId, targetId);
        const removeMsg = getActionMessage("removeAdmin");
        Alert.alert(removeMsg.title, removeMsg.message);
        break;
      default:
        throw new Error("Invalid admin action");
    }

    return { success: true, message: "Admin action completed successfully" };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Failed to perform the requested action.";
    Alert.alert("Error", errorMessage);
    return {
      success: false,
      message: errorMessage,
      error: errorMessage
    };
  }
};

/**
 * Enhanced new member notification with error handling
 */
export const notifyNewMember = async (
  groupId: string,
  memberId: string,
  onNotify: (_groupId: string, _memberId: string) => Promise<void>,
): Promise<GroupActionResult> => {
  try {
    await onNotify(groupId, memberId);
    // The notification will be handled by the notification system
    return { success: true, message: "Member notification sent" };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Failed to notify new member";
    return {
      success: false,
      message: errorMessage,
      error: errorMessage
    };
  }
};

/**
 * Enhanced group link sharing with mobile optimization
 */
export const shareGroupLink = async (groupId: string): Promise<GroupActionResult> => {
  try {
    const groupLink = await generateGroupLink(groupId);
    const message = isSmallDevice
      ? `Join my IraChat group! ${groupLink}`
      : `Join my group on IraChat! ${groupLink}`;

    await Share.share({
      message,
      url: groupLink,
    });

    return { success: true, message: "Group link shared successfully" };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Failed to share group link.";
    Alert.alert("Error", errorMessage);
    return {
      success: false,
      message: errorMessage,
      error: errorMessage
    };
  }
};

/**
 * Enhanced group link copying with mobile optimization
 */
export const copyGroupLink = async (groupId: string): Promise<GroupActionResult> => {
  try {
    const groupLink = await generateGroupLink(groupId);
    await Clipboard.setStringAsync(groupLink);

    const title = isSmallDevice ? "Copied" : "Success";
    const message = isSmallDevice ? "Link copied" : "Group link copied to clipboard";
    Alert.alert(title, message);

    return { success: true, message: "Group link copied to clipboard" };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Failed to copy group link.";
    Alert.alert("Error", errorMessage);
    return {
      success: false,
      message: errorMessage,
      error: errorMessage
    };
  }
};

/**
 * Enhanced group link generation with offline support
 */
export const generateGroupLink = async (groupId: string): Promise<string> => {
  try {
    // Generate a unique invite link for the group
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 11);
    const inviteCode = `${groupId}_${timestamp}_${random}`;

    // Store invite code in Firebase for validation
    if (db) {
      await setDoc(doc(db, "groupInvites", inviteCode), {
        groupId,
        createdAt: serverTimestamp(),
        expiresAt: new Date(timestamp + 7 * 24 * 60 * 60 * 1000), // 7 days
        isActive: true,
        deviceType: isSmallDevice ? 'mobile' : 'desktop'
      });
    } else {
      // Store offline for later sync
      try {
        const { offlineDatabaseService } = await import('../services/offlineDatabase');
        const database = offlineDatabaseService.getDatabase();

        await database.runAsync(`
          INSERT OR REPLACE INTO pending_group_invites
          (invite_code, group_id, created_at, expires_at, is_active, device_type)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [inviteCode, groupId, timestamp, timestamp + 7 * 24 * 60 * 60 * 1000, 1, isSmallDevice ? 'mobile' : 'desktop']);
      } catch (offlineError) {
        // Offline storage failed - continue with link generation
      }
    }

    return `https://irachat.app/join/${inviteCode}`;
  } catch (error) {
    // Removed console.error
    throw new Error("Failed to generate group invite link");
  }
};

/**
 * Enhanced group details fetching with offline support and mobile optimization
 */
export const getGroupDetails = async (
  groupId: string,
  userId: string,
): Promise<{
  info: GroupChat;
  stats: GroupStats;
  recentActivity: RecentActivity[];
}> => {
  try {
    if (!db) {
      // Try to get from offline storage
      try {
        const { offlineDatabaseService } = await import('../services/offlineDatabase');
        const database = offlineDatabaseService.getDatabase();

        const groupResult = await database.getAllAsync(`
          SELECT * FROM cached_groups WHERE id = ?
        `, [groupId]);

        if (groupResult.length === 0) {
          throw new Error("Group not found");
        }

        const groupData = groupResult[0] as any;
        const groupInfo = {
          id: groupId,
          ...JSON.parse(groupData.data || '{}')
        } as GroupChat;

        // Get offline stats
        const messageResult = await database.getAllAsync(`
          SELECT COUNT(*) as count FROM cached_messages WHERE chat_id = ?
        `, [groupId]);

        const mediaResult = await database.getAllAsync(`
          SELECT COUNT(*) as count FROM cached_messages
          WHERE chat_id = ? AND type IN ('image', 'video', 'audio', 'document')
        `, [groupId]);

        const stats: GroupStats = {
          memberCount: groupInfo.members?.length || 0,
          messageCount: (messageResult[0] as any)?.count || 0,
          mediaCount: (mediaResult[0] as any)?.count || 0,
          adminCount: groupInfo.admins?.length || 0,
          lastActivity: new Date(groupData.last_activity || Date.now())
        };

        return {
          info: groupInfo,
          stats,
          recentActivity: []
        };
      } catch (offlineError) {
        throw new Error("Firebase not available and no offline data");
      }
    }

    // Get group info from Firebase
    const groupDoc = await getDoc(doc(db, "groups", groupId));
    if (!groupDoc.exists()) {
      throw new Error("Group not found");
    }

    const groupInfo = { id: groupId, ...groupDoc.data() } as GroupChat;

    // Get group statistics
    const messagesSnapshot = await getDocs(collection(db, "groups", groupId, "messages"));
    const mediaMessages = messagesSnapshot.docs.filter(doc =>
      doc.data().type && ["image", "video", "audio", "document"].includes(doc.data().type)
    );

    // Count online members (simplified for demo)
    const onlineMembers = Math.floor((groupInfo.members?.length || 0) * 0.3); // Estimate

    const stats: GroupStats = {
      memberCount: groupInfo.members?.length || 0,
      messageCount: messagesSnapshot.size,
      mediaCount: mediaMessages.length,
      adminCount: groupInfo.admins?.length || 0,
      onlineMembers,
      lastActivity: new Date()
    };

    // Get recent activity (limit based on device)
    const activityLimit = isSmallDevice ? 5 : 10;
    const recentMessagesQuery = query(
      collection(db, "groups", groupId, "messages"),
      orderBy("timestamp", "desc"),
      limit(activityLimit)
    );

    const recentSnapshot = await getDocs(recentMessagesQuery);
    const recentActivity: RecentActivity[] = recentSnapshot.docs.map(doc => {
      const data = doc.data();
      const maxLength = isSmallDevice ? 30 : 50;

      return {
        type: data.type === "text" ? "message" as const : "media" as const,
        timestamp: data.timestamp?.toMillis() || Date.now(),
        description: data.type === "text"
          ? `${data.content?.substring(0, maxLength)}${data.content?.length > maxLength ? '...' : ''}`
          : `${data.type} shared`,
        userId: data.senderId,
        userName: data.senderName
      };
    });

    // Cache the data offline for future use
    try {
      const { offlineDatabaseService } = await import('../services/offlineDatabase');
      const database = offlineDatabaseService.getDatabase();

      await database.runAsync(`
        INSERT OR REPLACE INTO cached_groups
        (id, data, last_activity, timestamp)
        VALUES (?, ?, ?, ?)
      `, [groupId, JSON.stringify(groupInfo), Date.now(), Date.now()]);
    } catch (cacheError) {
      // Cache failed - continue
    }

    return {
      info: groupInfo,
      stats,
      recentActivity,
    };
  } catch (error) {
    // Removed console.error
    throw error;
  }
};

/**
 * Enhanced group invite handling with offline support and mobile optimization
 */
export const handleGroupInvite = async (
  inviteCode: string,
  userId: string,
  onJoin: (_groupId: string, _userId: string) => Promise<void>,
): Promise<GroupActionResult> => {
  try {
    if (!db) {
      // Try to handle offline invite
      try {
        const { offlineDatabaseService } = await import('../services/offlineDatabase');
        const database = offlineDatabaseService.getDatabase();

        // Store pending invite for when online
        await database.runAsync(`
          INSERT OR REPLACE INTO pending_group_joins
          (invite_code, user_id, timestamp)
          VALUES (?, ?, ?)
        `, [inviteCode, userId, Date.now()]);

        const title = isSmallDevice ? "Saved" : "Invite Saved";
        const message = isSmallDevice
          ? "Will join when online"
          : "Invite saved. You'll join the group when connection is restored.";
        Alert.alert(title, message);

        return {
          success: true,
          message: "Invite saved for offline processing",
          isOffline: true
        };
      } catch (offlineError) {
        throw new Error("Firebase not available and offline storage failed");
      }
    }

    // Validate invite code
    const inviteDoc = await getDoc(doc(db, "groupInvites", inviteCode));
    if (!inviteDoc.exists()) {
      throw new Error("Invalid invite code");
    }

    const inviteData = inviteDoc.data();
    if (!inviteData.isActive || new Date() > inviteData.expiresAt.toDate()) {
      throw new Error("Invite code has expired");
    }

    const groupId = inviteData.groupId;

    // Check if group exists
    const groupDoc = await getDoc(doc(db, "groups", groupId));
    if (!groupDoc.exists()) {
      throw new Error("Group no longer exists");
    }

    // Check if user is already a member
    const groupData = groupDoc.data();
    if (groupData.members && groupData.members.includes(userId)) {
      const title = isSmallDevice ? "Already Member" : "Already a Member";
      const message = isSmallDevice ? "You're in this group" : "You are already a member of this group.";
      Alert.alert(title, message);
      return { success: true, message: "Already a member" };
    }

    await onJoin(groupId, userId);

    const title = isSmallDevice ? "Joined" : "Success";
    const message = isSmallDevice ? "Group joined!" : "You have joined the group successfully!";
    Alert.alert(title, message);

    return { success: true, message: "Successfully joined the group" };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Failed to join the group.";
    Alert.alert("Error", errorMessage);
    return {
      success: false,
      message: errorMessage,
      error: errorMessage
    };
  }
};

/**
 * Sync offline group actions when connection is restored
 */
export const syncOfflineGroupActions = async (): Promise<GroupActionResult> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    // Sync pending invites
    const pendingInvites = await database.getAllAsync(`
      SELECT * FROM pending_group_invites ORDER BY created_at ASC
    `);

    // Sync pending joins
    const pendingJoins = await database.getAllAsync(`
      SELECT * FROM pending_group_joins ORDER BY timestamp ASC
    `);

    let syncedCount = 0;

    // Process pending invites
    for (const inviteRow of pendingInvites) {
      try {
        const invite = inviteRow as any;
        if (db) {
          await setDoc(doc(db, "groupInvites", invite.invite_code), {
            groupId: invite.group_id,
            createdAt: new Date(invite.created_at),
            expiresAt: new Date(invite.expires_at),
            isActive: invite.is_active === 1,
            deviceType: invite.device_type
          });

          // Remove from offline storage
          await database.runAsync(`
            DELETE FROM pending_group_invites WHERE invite_code = ?
          `, [invite.invite_code]);

          syncedCount++;
        }
      } catch (syncError) {
        // Skip this invite and continue
      }
    }

    return {
      success: true,
      message: `Synced ${syncedCount} offline actions`
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to sync offline actions",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
};

/**
 * Get group member preferences with offline support
 */
export const getGroupMemberPreferences = async (
  groupId: string,
  userId: string
): Promise<GroupMemberPreferences> => {
  try {
    // Try to get from offline storage first
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const result = await database.getAllAsync(`
      SELECT preferences FROM group_member_preferences
      WHERE group_id = ? AND user_id = ?
    `, [groupId, userId]);

    if (result.length > 0) {
      return JSON.parse((result[0] as any).preferences);
    }

    // Return default preferences
    return defaultMemberPreferences;
  } catch (error) {
    return defaultMemberPreferences;
  }
};

/**
 * Update group member preferences with offline support
 */
export const updateGroupMemberPreferences = async (
  groupId: string,
  userId: string,
  preferences: Partial<GroupMemberPreferences>
): Promise<GroupActionResult> => {
  try {
    const currentPrefs = await getGroupMemberPreferences(groupId, userId);
    const updatedPrefs = { ...currentPrefs, ...preferences };

    // Store offline
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync(`
      INSERT OR REPLACE INTO group_member_preferences
      (group_id, user_id, preferences, timestamp)
      VALUES (?, ?, ?, ?)
    `, [groupId, userId, JSON.stringify(updatedPrefs), Date.now()]);

    return {
      success: true,
      message: "Preferences updated successfully"
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to update preferences",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
};

// Export default object with all functions
export default {
  defaultMemberPreferences,
  handleGroupAction,
  handleContentHide,
  handleAdminAction,
  notifyNewMember,
  shareGroupLink,
  copyGroupLink,
  generateGroupLink,
  getGroupDetails,
  handleGroupInvite,
  syncOfflineGroupActions,
  getGroupMemberPreferences,
  updateGroupMemberPreferences,
};
