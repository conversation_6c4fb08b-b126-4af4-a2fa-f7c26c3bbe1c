/**
 * Enhanced Pagination utilities for IraChat with mobile optimization and offline support
 */
import { Dimensions } from 'react-native';

// Get device dimensions for responsive pagination
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

export interface PaginationOptions {
  page?: number;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  // Mobile-specific options
  mobileLimit?: number;
  tabletLimit?: number;
  adaptiveLimit?: boolean;
  cacheResults?: boolean;
  offlineMode?: boolean;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextPage?: number;
    prevPage?: number;
  };
}

export interface InfiniteScrollOptions {
  initialLimit?: number;
  loadMoreLimit?: number;
  threshold?: number; // How close to the end before loading more
  // Mobile-specific options
  mobileInitialLimit?: number;
  mobileLoadMoreLimit?: number;
  tabletInitialLimit?: number;
  tabletLoadMoreLimit?: number;
  adaptiveLimits?: boolean;
  cachePages?: boolean;
  offlineSupport?: boolean;
  preloadNext?: boolean; // Preload next page for smoother scrolling
}

export interface InfiniteScrollState<T> {
  data: T[];
  loading: boolean;
  hasMore: boolean;
  error?: string;
  page: number;
  total: number;
}

/**
 * Enhanced pagination parameters with mobile optimization
 */
export const createPaginationParams = (options: PaginationOptions = {}) => {
  const {
    page = 1,
    limit: baseLimit = 20,
    mobileLimit = 10,
    tabletLimit = 15,
    adaptiveLimit = true,
    offset,
    sortBy = "timestamp",
    sortOrder = "desc",
    cacheResults = true,
    offlineMode = false,
  } = options;

  // Get device-appropriate limit
  let limit = baseLimit;
  if (adaptiveLimit) {
    if (isSmallDevice && mobileLimit) {
      limit = mobileLimit;
    } else if (isTablet && tabletLimit) {
      limit = tabletLimit;
    }
  }

  const actualOffset = offset !== undefined ? offset : (page - 1) * limit;

  return {
    page,
    limit,
    offset: actualOffset,
    sortBy,
    sortOrder,
    cacheResults,
    offlineMode,
    deviceType: isSmallDevice ? 'mobile' : isTablet ? 'tablet' : 'desktop',
  };
};

/**
 * Create pagination result from data and options
 */
export const createPaginationResult = <T>(
  data: T[],
  total: number,
  options: PaginationOptions = {},
): PaginationResult<T> => {
  const { page = 1, limit = 20 } = options;
  const totalPages = Math.ceil(total / limit);
  const hasNext = page < totalPages;
  const hasPrev = page > 1;

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
      nextPage: hasNext ? page + 1 : undefined,
      prevPage: hasPrev ? page - 1 : undefined,
    },
  };
};

/**
 * Calculate pagination info
 */
export const calculatePagination = (
  currentPage: number,
  totalItems: number,
  itemsPerPage: number,
) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage - 1, totalItems - 1);

  return {
    currentPage,
    totalPages,
    totalItems,
    itemsPerPage,
    startIndex,
    endIndex,
    hasNext: currentPage < totalPages,
    hasPrev: currentPage > 1,
    isFirst: currentPage === 1,
    isLast: currentPage === totalPages,
  };
};

/**
 * Get page numbers for pagination UI
 */
export const getPageNumbers = (
  currentPage: number,
  totalPages: number,
  maxVisible: number = 5,
): (number | string)[] => {
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  const pages: (number | string)[] = [];
  const halfVisible = Math.floor(maxVisible / 2);

  // Always show first page
  pages.push(1);

  let startPage = Math.max(2, currentPage - halfVisible);
  let endPage = Math.min(totalPages - 1, currentPage + halfVisible);

  // Adjust if we're near the beginning
  if (currentPage <= halfVisible + 1) {
    endPage = Math.min(totalPages - 1, maxVisible - 1);
  }

  // Adjust if we're near the end
  if (currentPage >= totalPages - halfVisible) {
    startPage = Math.max(2, totalPages - maxVisible + 2);
  }

  // Add ellipsis if needed
  if (startPage > 2) {
    pages.push("...");
  }

  // Add middle pages
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }

  // Add ellipsis if needed
  if (endPage < totalPages - 1) {
    pages.push("...");
  }

  // Always show last page (if more than 1 page)
  if (totalPages > 1) {
    pages.push(totalPages);
  }

  return pages;
};

/**
 * Infinite scroll utilities
 */
export class InfiniteScrollManager<T> {
  private state: InfiniteScrollState<T>;
  private options: InfiniteScrollOptions;
  private loadFunction: (
    _page: number,
    _limit: number,
  ) => Promise<{ data: T[]; total: number }>;

  constructor(
    loadFunction: (
      _page: number,
      _limit: number,
    ) => Promise<{ data: T[]; total: number }>,
    options: InfiniteScrollOptions = {},
  ) {
    this.loadFunction = loadFunction;

    // Get device-appropriate defaults
    const getInitialLimit = () => {
      if (options.adaptiveLimits !== false) {
        if (isSmallDevice && options.mobileInitialLimit) return options.mobileInitialLimit;
        if (isTablet && options.tabletInitialLimit) return options.tabletInitialLimit;
        if (isSmallDevice) return 10; // Smaller default for mobile
        if (isTablet) return 15; // Medium default for tablet
      }
      return options.initialLimit || 20;
    };

    const getLoadMoreLimit = () => {
      if (options.adaptiveLimits !== false) {
        if (isSmallDevice && options.mobileLoadMoreLimit) return options.mobileLoadMoreLimit;
        if (isTablet && options.tabletLoadMoreLimit) return options.tabletLoadMoreLimit;
        if (isSmallDevice) return 10; // Smaller default for mobile
        if (isTablet) return 15; // Medium default for tablet
      }
      return options.loadMoreLimit || 20;
    };

    this.options = {
      initialLimit: getInitialLimit(),
      loadMoreLimit: getLoadMoreLimit(),
      threshold: isSmallDevice ? 0.9 : 0.8, // Load earlier on mobile for smoother experience
      adaptiveLimits: true,
      cachePages: true,
      offlineSupport: true,
      preloadNext: !isSmallDevice, // Only preload on larger devices
      ...options,
    };

    this.state = {
      data: [],
      loading: false,
      hasMore: true,
      page: 0,
      total: 0,
    };
  }

  async loadInitial(): Promise<InfiniteScrollState<T>> {
    this.state.loading = true;
    this.state.error = undefined;

    try {
      const result = await this.loadFunction(1, this.options.initialLimit!);

      this.state = {
        data: result.data,
        loading: false,
        hasMore: result.data.length === this.options.initialLimit,
        page: 1,
        total: result.total,
      };
    } catch (error) {
      this.state.loading = false;
      this.state.error =
        error instanceof Error ? error.message : "Failed to load data";
    }

    return { ...this.state };
  }

  async loadMore(): Promise<InfiniteScrollState<T>> {
    if (this.state.loading || !this.state.hasMore) {
      return { ...this.state };
    }

    this.state.loading = true;
    this.state.error = undefined;

    try {
      const nextPage = this.state.page + 1;
      const result = await this.loadFunction(
        nextPage,
        this.options.loadMoreLimit!,
      );

      this.state = {
        data: [...this.state.data, ...result.data],
        loading: false,
        hasMore: result.data.length === this.options.loadMoreLimit,
        page: nextPage,
        total: result.total,
      };
    } catch (error) {
      this.state.loading = false;
      this.state.error =
        error instanceof Error ? error.message : "Failed to load more data";
    }

    return { ...this.state };
  }

  shouldLoadMore(
    scrollPosition: number,
    contentHeight: number,
    containerHeight: number,
  ): boolean {
    if (this.state.loading || !this.state.hasMore) {
      return false;
    }

    const scrollPercentage = (scrollPosition + containerHeight) / contentHeight;
    return scrollPercentage >= this.options.threshold!;
  }

  reset(): void {
    this.state = {
      data: [],
      loading: false,
      hasMore: true,
      page: 0,
      total: 0,
    };
  }

  getState(): InfiniteScrollState<T> {
    return { ...this.state };
  }
}

/**
 * Hook-like utilities for React components
 */
export const usePaginationHelpers = () => {
  const goToPage = (
    currentPage: number,
    targetPage: number,
    totalPages: number,
  ) => {
    if (
      targetPage < 1 ||
      targetPage > totalPages ||
      targetPage === currentPage
    ) {
      return currentPage;
    }
    return targetPage;
  };

  const goToNextPage = (currentPage: number, totalPages: number) => {
    return goToPage(currentPage, currentPage + 1, totalPages);
  };

  const goToPrevPage = (currentPage: number, totalPages: number) => {
    return goToPage(currentPage, currentPage - 1, totalPages);
  };

  const goToFirstPage = () => 1;

  const goToLastPage = (totalPages: number) => totalPages;

  return {
    goToPage,
    goToNextPage,
    goToPrevPage,
    goToFirstPage,
    goToLastPage,
  };
};

// Enhanced exports for UpdatesScreen compatibility with mobile optimization
export const handleRefresh = async (
  setRefreshing: (_refreshing: boolean) => void,
  loadData: () => Promise<void>,
  onError?: (_error: Error) => void,
): Promise<{ success: boolean; error?: string }> => {
  try {
    setRefreshing(true);
    await loadData();
    return { success: true };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Error refreshing data";
    if (onError) {
      onError(error instanceof Error ? error : new Error(errorMessage));
    }
    return { success: false, error: errorMessage };
  } finally {
    setRefreshing(false);
  }
};

export const loadMoreUpdates = async (
  hasMore: boolean,
  isLoading: boolean,
  loadData: () => Promise<void>,
  onError?: (_error: Error) => void,
): Promise<{ success: boolean; error?: string }> => {
  if (!hasMore || isLoading) {
    return { success: false, error: "No more data to load or already loading" };
  }

  try {
    await loadData();
    return { success: true };
  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Error loading more updates";
    if (onError) {
      onError(error instanceof Error ? error : new Error(errorMessage));
    }
    return { success: false, error: errorMessage };
  }
};

/**
 * Mobile-optimized pagination cache for offline support
 */
export class PaginationCache<T> {
  private static caches = new Map<string, Map<string, any>>();
  private cacheKey: string;
  private maxCacheSize: number;

  constructor(cacheKey: string, maxCacheSize: number = isSmallDevice ? 20 : 50) {
    this.cacheKey = cacheKey;
    this.maxCacheSize = maxCacheSize;

    if (!PaginationCache.caches.has(cacheKey)) {
      PaginationCache.caches.set(cacheKey, new Map());
    }
  }

  /**
   * Cache pagination result
   */
  set(page: number, data: PaginationResult<T>): void {
    const cache = PaginationCache.caches.get(this.cacheKey)!;
    const key = `page_${page}`;

    // Clear old entries if cache is full
    if (cache.size >= this.maxCacheSize) {
      const firstKey = cache.keys().next().value;
      if (firstKey) {
        cache.delete(firstKey);
      }
    }

    cache.set(key, {
      ...data,
      cachedAt: Date.now(),
    });
  }

  /**
   * Get cached pagination result
   */
  get(page: number, maxAge: number = 5 * 60 * 1000): PaginationResult<T> | null {
    const cache = PaginationCache.caches.get(this.cacheKey)!;
    const key = `page_${page}`;
    const cached = cache.get(key);

    if (!cached) return null;

    // Check if cache is still valid
    if (Date.now() - cached.cachedAt > maxAge) {
      cache.delete(key);
      return null;
    }

    return cached;
  }

  /**
   * Clear cache
   */
  clear(): void {
    const cache = PaginationCache.caches.get(this.cacheKey);
    if (cache) {
      cache.clear();
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; maxSize: number; keys: string[] } {
    const cache = PaginationCache.caches.get(this.cacheKey);
    return {
      size: cache?.size || 0,
      maxSize: this.maxCacheSize,
      keys: cache ? Array.from(cache.keys()) : [],
    };
  }
}

/**
 * Get device-appropriate pagination limits
 */
export const getDevicePaginationLimits = () => {
  return {
    initial: isSmallDevice ? 10 : isTablet ? 15 : 20,
    loadMore: isSmallDevice ? 10 : isTablet ? 15 : 20,
    maxVisible: isSmallDevice ? 3 : isTablet ? 5 : 7,
    threshold: isSmallDevice ? 0.9 : 0.8,
  };
};

/**
 * Create mobile-optimized infinite scroll options
 */
export const createMobileInfiniteScrollOptions = (
  customOptions: Partial<InfiniteScrollOptions> = {}
): InfiniteScrollOptions => {
  const defaults = getDevicePaginationLimits();

  return {
    initialLimit: defaults.initial,
    loadMoreLimit: defaults.loadMore,
    threshold: defaults.threshold,
    adaptiveLimits: true,
    cachePages: true,
    offlineSupport: true,
    preloadNext: !isSmallDevice,
    ...customOptions,
  };
};

/**
 * Batch pagination operations for better performance
 */
export const batchPaginationOperations = async <T>(
  operations: (() => Promise<PaginationResult<T>>)[],
  concurrency: number = isSmallDevice ? 2 : 4
): Promise<PaginationResult<T>[]> => {
  const results: PaginationResult<T>[] = [];

  for (let i = 0; i < operations.length; i += concurrency) {
    const batch = operations.slice(i, i + concurrency);
    const batchResults = await Promise.allSettled(batch.map(op => op()));

    batchResults.forEach(result => {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        // Add empty result for failed operations
        results.push({
          data: [],
          pagination: {
            page: 1,
            limit: 0,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        });
      }
    });
  }

  return results;
};

export default {
  createPaginationParams,
  createPaginationResult,
  calculatePagination,
  getPageNumbers,
  InfiniteScrollManager,
  usePaginationHelpers,
  handleRefresh,
  loadMoreUpdates,
  PaginationCache,
  getDevicePaginationLimits,
  createMobileInfiniteScrollOptions,
  batchPaginationOperations,
};
