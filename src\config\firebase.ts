// 🔒 Secure Firebase Configuration using Environment Service
import { environmentConfig } from './environment';

// Firebase configuration - SECURE: Uses validated environment variables
export const firebaseConfig = environmentConfig.firebase;

// Configuration is already validated in environment.ts during load
export const isFirebaseConfigValid = true;

// Firebase services are exported from firebaseSimple.ts directly

// Re-export Firestore functions
export {
  doc,
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  onSnapshot,
  query,
  orderBy,
  serverTimestamp,
  where,
  limit,
  getDocs,
  getDoc,
  setDoc,
  arrayUnion,
  arrayRemove,
  increment,
  Timestamp
} from 'firebase/firestore';
