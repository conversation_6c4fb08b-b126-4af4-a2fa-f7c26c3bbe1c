# 🔥 Firebase Setup Instructions for IraChat

## Step 1: Firebase Login

Open a new terminal/command prompt and run:

```bash
firebase login
```

This will:
1. Open your web browser
2. Ask you to sign in to Google/Firebase
3. Grant permissions to Firebase CLI
4. Return to terminal with success message

## Step 2: Set Firebase Project

After successful login, set your project:

```bash
firebase use irachat-production
```

## Step 3: Deploy Security Rules

Deploy Firestore and Storage rules:

```bash
# Deploy Firestore rules
firebase deploy --only firestore:rules

# Deploy Storage rules  
firebase deploy --only storage

# Or deploy both at once
firebase deploy --only firestore:rules,storage
```

## Step 4: Verify Deployment

Check that rules are deployed:

```bash
firebase firestore:rules:get
```

## Alternative: Manual Rule Deployment

If CLI doesn't work, you can deploy rules manually:

### Firestore Rules (Manual):
1. Go to: https://console.firebase.google.com/project/irachat-production
2. Navigate to: Firestore Database → Rules
3. Copy content from `firestore.rules` file
4. Paste into the rules editor
5. Click "Publish"

### Storage Rules (Manual):
1. Go to: https://console.firebase.google.com/project/irachat-production  
2. Navigate to: Storage → Rules
3. Copy content from `storage.rules` file
4. Paste into the rules editor
5. Click "Publish"

## Step 5: Test Configuration

After deploying rules, test the configuration:

```bash
node test-firebase.js
```

## Troubleshooting

### If login fails:
```bash
firebase logout
firebase login --reauth
```

### If project not found:
```bash
firebase projects:list
firebase use --add
```

### If rules deployment fails:
- Check internet connection
- Verify you have owner/editor permissions on the Firebase project
- Try deploying rules manually via Firebase Console

## Next Steps

After Firebase rules are deployed:

1. **Start Development Server:**
   ```bash
   npm run dev
   ```

2. **Test Real Functionality:**
   - User registration
   - Messaging
   - Calling
   - File uploads

3. **Build for Device Testing:**
   ```bash
   eas build --profile development --platform android
   ```

## Important Notes

- ✅ Your `.env` file is already configured
- ✅ `google-services.json` is in the right location
- ✅ All package names are consistent
- ✅ Firebase connection is working
- 🔄 Only security rules deployment remains

Once rules are deployed, your IraChat backend will be 100% ready for production use!
