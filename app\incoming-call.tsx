// Incoming Call Screen Route
import React from 'react';
import { View } from 'react-native';
import { IncomingCallScreen } from '../src/screens/IncomingCallScreen';
import { useRealCallManager } from '../src/hooks/useRealCallManager';
import { auth } from '../src/services/firebaseSimple';

export default function IncomingCallScreenPage() {
  const currentUser = auth?.currentUser;
  const { callState } = useRealCallManager();

  if (!callState.currentCall) {
    return <View style={{ flex: 1, backgroundColor: '#000' }} />;
  }

  return (
    <IncomingCallScreen
      callId={callState.currentCall.id}
      callerId={callState.currentCall.callerId}
      callerName={callState.currentCall.callerName}
      callType={callState.currentCall.type}
      callerAvatar={callState.currentCall.callerAvatar}
    />
  );
}
