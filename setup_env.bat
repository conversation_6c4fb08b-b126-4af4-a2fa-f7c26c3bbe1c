@echo off
echo Setting up Android development environment variables...

REM Set Android SDK path
set ANDROID_HOME=F:\Android\Sdk
set ANDROID_SDK_ROOT=F:\Android\Sdk

REM Set Java path (check if Android Studio JBR exists, otherwise use system Java)
if exist "F:\Android\Android Studio\jbr" (
    set JAVA_HOME=F:\Android\Android Studio\jbr
) else (
    echo Android Studio JBR not found, using system Java
)

REM Add Android tools to PATH
set PATH=%PATH%;%ANDROID_HOME%\platform-tools
set PATH=%PATH%;%ANDROID_HOME%\tools
set PATH=%PATH%;%ANDROID_HOME%\tools\bin
set PATH=%PATH%;%ANDROID_HOME%\emulator

REM Display current settings
echo.
echo Environment variables set:
echo ANDROID_HOME=%ANDROID_HOME%
echo JAVA_HOME=%JAVA_HOME%
echo.
echo Testing adb...
adb version

echo.
echo Environment setup complete!
echo You can now run: npm run android
