{"name": "irachat", "version": "1.0.2", "description": "A fully functional React Native messaging app built with Expo, Firebase, and NativeWind", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "build": "eas build --platform all", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:preview": "eas build --platform all --profile preview", "build:production": "eas build --platform all --profile production", "submit": "eas submit", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "dev": "expo start --dev-client", "dev:android": "expo start --dev-client --android", "dev:ios": "expo start --dev-client --ios", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:mobile": "node mobile-only-test.js", "test:responsive": "node mobile-responsiveness-test.js", "prebuild": "expo prebuild", "prebuild:clean": "expo prebuild --clean", "prebuild:android": "expo prebuild --platform android", "prebuild:ios": "expo prebuild --platform ios", "setup-android": "echo 'Setting up Android development environment...' && echo 'Please install Android Studio and set ANDROID_HOME'", "setup-ios": "echo 'Setting up iOS development environment...' && echo 'Please install Xcode from App Store'", "clean": "npx expo start --clear", "reset": "rm -rf node_modules && npm install && npx expo start --clear", "fix-metro": "node clear-cache.js", "debug-db": "node -e \"require('./src/utils/databaseUtils').debugDatabase()\"", "mobile-cleanup": "node mobile-cleanup-organizer.js"}, "dependencies": {"@config-plugins/react-native-webrtc": "^12.0.0", "@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/netinfo": "^11.3.1", "@react-native-community/slider": "4.5.6", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/auth": "^22.4.0", "@react-native-picker/picker": "^2.8.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.3.3", "@reduxjs/toolkit": "^2.2.7", "@types/crypto-js": "^4.2.2", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "crypto-js": "^4.2.0", "expo": "^53.0.20", "expo-asset": "~11.1.5", "expo-audio": "^0.4.8", "expo-av": "~15.1.7", "expo-blur": "~14.1.5", "expo-build-properties": "^0.14.8", "expo-camera": "~16.1.11", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.6", "expo-contacts": "~14.2.5", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.5", "expo-font": "~13.3.1", "expo-haptics": "^14.1.4", "expo-image": "^2.4.0", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-intent-launcher": "~12.1.5", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-local-authentication": "^16.0.5", "expo-location": "~18.1.6", "expo-media-library": "~17.1.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-sharing": "^13.1.5", "expo-splash-screen": "^0.30.10", "expo-sqlite": "^15.2.14", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-task-manager": "^13.1.6", "expo-video": "~2.2.2", "expo-video-thumbnails": "~9.1.3", "firebase": "^11.0.2", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "^0.79.5", "react-native-callkeep": "^4.3.12", "react-native-gesture-handler": "~2.24.0", "react-native-incall-manager": "^4.0.1", "react-native-keep-awake": "^4.0.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "^5.5.0", "react-native-screens": "~4.11.1", "react-native-sound": "^0.11.2", "react-native-svg": "^15.11.2", "react-native-webrtc": "^118.0.0", "react-redux": "^9.1.2", "redux-persist": "^6.0.0", "tailwindcss": "^3.3.0", "expo-background-fetch": "~13.1.6", "expo-crypto": "~14.1.5", "expo-screen-orientation": "~8.1.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "dotenv": "^17.2.0", "eslint": "^8.57.0", "eslint-config-expo": "~9.2.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.7.0", "metro": "^0.82.4", "typescript": "~5.8.3"}, "keywords": ["react-native", "expo", "firebase", "messaging", "chat", "mobile-app", "android", "ios", "nativewind", "typescript", "mobile-only"], "author": "IraChat Team", "license": "MIT", "private": true, "expo": {"install": {"exclude": ["react-native-safe-area-context"]}, "doctor": {"reactNativeDirectoryCheck": {"exclude": ["react-native-webrtc", "redux-persist", "ajv", "ajv-keywords", "firebase", "metro"], "listUnknownPackages": false}}}}