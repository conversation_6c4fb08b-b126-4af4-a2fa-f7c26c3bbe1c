// 🚀 ULTIMATE GROUP CHAT ROOM
// Complete group messaging experience with all advanced features
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  FlatList,
  Alert,
  SafeAreaView,
  Image,
  TextInput,
  Modal,
  Animated,
  KeyboardAvoidingView,
  Platform,
  Vibration,
  StatusBar,
  Pressable,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { navigationService, ROUTES } from '../services/navigationService';
import { Group } from '../types/Group';
import { Audio } from 'expo-av'; // Fixed: Added for voice recording
import { MessageBubble } from './ComprehensiveGroupMessageUI';
import { ComprehensiveGroupInfoPage } from './ComprehensiveGroupInfoPage';
import { MostActiveMemberSystem } from './MostActiveMemberSystem';
import { MediaViewer } from './MediaViewer';
import { BORDER_RADIUS, SHADOWS } from '../styles/iraChatDesignSystem';
import { ResponsiveSpacing, ResponsiveTypography, ComponentSizes, DeviceInfo } from '../utils/responsiveUtils';
import { chatClearService } from '../services/chatClearService';

import { ChatExport } from './ChatExport';
import MessageSearch from './MessageSearch';
import { MessageSelectionProvider, useMessageSelection } from '../contexts/MessageSelectionContext';
import { voiceMessageService } from '../services/voiceMessageService';
import { realMediaUploadService } from '../services/realMediaUploadService';
import { audioManager } from '../services/audioManager';

import FloatingEmojiBar, { useFloatingEmojiBar } from './chat/FloatingEmojiBar';

import PinnedMessageBar from './chat/PinnedMessageBar';

// Singleton Recording Manager to prevent conflicts
class RecordingManager {
  private static instance: RecordingManager | null = null;
  private currentRecording: Audio.Recording | null = null;
  private isCleaningUp: boolean = false;

  static getInstance(): RecordingManager {
    if (!RecordingManager.instance) {
      RecordingManager.instance = new RecordingManager();
    }
    return RecordingManager.instance;
  }

  async cleanup(): Promise<void> {
    if (this.isCleaningUp) {
      console.log('⚠️ Cleanup already in progress, waiting...');
      // Wait for current cleanup to finish
      while (this.isCleaningUp) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      return;
    }

    this.isCleaningUp = true;
    try {
      if (this.currentRecording) {
        console.log('🧹 Cleaning up recording...');
        const status = await this.currentRecording.getStatusAsync();
        if (status.canRecord || status.isRecording) {
          await this.currentRecording.stopAndUnloadAsync();
        }
        this.currentRecording = null;
        console.log('✅ Recording cleaned up successfully');
      }
    } catch (error: any) {
      if (!error.message?.includes('does not exist')) {
        console.warn('⚠️ Recording cleanup error:', error);
      }
      this.currentRecording = null; // Force reset on error
    } finally {
      this.isCleaningUp = false;
    }
  }

  async createRecording(): Promise<Audio.Recording> {
    // Always cleanup first
    await this.cleanup();

    // Wait a bit to ensure cleanup is complete
    await new Promise(resolve => setTimeout(resolve, 100));

    const recording = new Audio.Recording();
    this.currentRecording = recording;
    return recording;
  }

  getCurrentRecording(): Audio.Recording | null {
    return this.currentRecording;
  }

  clearRecording(): void {
    this.currentRecording = null;
  }
}

const recordingManager = RecordingManager.getInstance();

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#4A90E2',      // Softer Blue - easier on eyes
  primaryDark: '#357ABD',  // Darker Blue
  primaryLight: '#6BA3E8', // Lighter Blue
  secondary: '#5A9FD4',    // Muted Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#E8E8E8',         // Softer White - less harsh
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#4CAF50',      // Softer Green
  warning: '#FF9800',      // Softer Orange
  error: '#F44336',        // Softer Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
  border: '#3A3A3A',       // Border color
};

// Enhanced Message Interface (from ComprehensiveGroupMessageUI)
interface GroupMessage {
  id: string;
  text?: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice' | 'call' | 'location' | 'contact' | 'poll' | 'announcement';
  mediaUrl?: string;
  mediaThumbnail?: string;
  duration?: number;
  fileName?: string;
  fileSize?: number;
  mentions?: string[];
  isAnnouncement?: boolean;
  announcementPriority?: 'low' | 'medium' | 'high';
  replyTo?: {
    messageId: string;
    text: string;
    senderName: string;
    type: string;
    mediaUrl?: string;
  };
  reactions?: {
    [emoji: string]: {
      users: string[];
      count: number;
    };
  };
  threadReplies?: GroupMessage[];
  threadCount?: number;
  isEdited?: boolean;
  editedAt?: Date;
  isDeleted?: boolean;
  deletedAt?: Date;
  isForwarded?: boolean;
  forwardedFrom?: string;
  isPinned?: boolean;
  pinnedBy?: string;
  pinnedAt?: Date;
  readBy?: {
    userId: string;
    userName: string;
    readAt: Date;
  }[];
}

interface TypingUser {
  userId: string;
  userName: string;
  timestamp: Date;
}

interface UltimateGroupChatRoomProps {
  group?: Group;
  groupId?: string;
  groupName?: string;
  groupAvatar?: string;
  currentUserId: string;
  currentUserName?: string;
  currentUserAvatar?: string;
  isAdmin?: boolean;
  onBack?: () => void;
}

export const UltimateGroupChatRoom: React.FC<UltimateGroupChatRoomProps> = ({
  group,
  groupId,
  groupName,
  groupAvatar,
  currentUserId,
  currentUserName,
  currentUserAvatar,
  isAdmin = false,
  onBack,
}) => {
  const _router = useRouter();
  const insets = useSafeAreaInsets();

  // Debug group data
  console.log('🔍 Group Chat Room Props:', {
    group: group,
    groupId: groupId,
    groupName: groupName,
    groupAvatar: groupAvatar,
  });

  // ==================== PROPS HANDLING ====================

  // Handle both group object and individual props for backward compatibility
  const actualGroup: Group = group || {
    id: groupId || 'unknown',
    name: groupName || 'Unknown Group',
    avatar: groupAvatar || '',
    description: '',
    type: 'public' as const,
    createdAt: new Date(),
    createdBy: currentUserId,
    members: [{
      id: currentUserId,
      userId: currentUserId,
      userName: currentUserName || 'Unknown User',
      userAvatar: undefined,
      role: 'owner' as const,
      joinedAt: new Date(),
      isOnline: true,
      lastSeen: new Date(),
      permissions: ['send_messages', 'add_members', 'remove_members', 'edit_info', 'pin_messages', 'delete_messages'],
      customTitle: 'Creator',
      invitedBy: undefined,
    }],
    memberCount: 1,
    admins: [currentUserId],
    currentUserRole: isAdmin ? 'admin' : 'owner', // Set current user role based on isAdmin prop
    settings: {
      onlyAdminsCanSendMessages: false,
      onlyAdminsCanAddMembers: false,
      allowMemberInvites: true,
      requireApprovalToJoin: false,
      showMemberList: true,
      allowForwarding: true,
      allowScreenshots: true,
      muteNotifications: false,
      mentionNotifications: true,
      disappearingMessages: false,
      disappearingMessagesDuration: 24,
      readReceipts: true,
      typingIndicators: true,
      allowPhotos: true,
      allowVideos: true,
      allowDocuments: true,
      allowVoiceMessages: true,
      allowStickers: true,
      allowGifs: true,
      slowMode: false,
      slowModeDelay: 0,
      maxMembers: 1000,
      autoDeleteMessages: false,
      autoDeleteDuration: 30,
    },
    // Add missing required properties with defaults
    isVerified: false,
    ownerId: currentUserId,
    ownerName: currentUserName || 'Unknown',
    onlineCount: 1,
    lastActivity: new Date(),
    stats: {
      totalMembers: 0,
      onlineMembers: 0,
      totalMessages: 0,
      messagesThisWeek: 0,
      mediaShared: 0,
      mostActiveMembers: [],
      peakOnlineTime: {
        hour: 0,
        count: 0,
      },
      joinRate: 0,
      leaveRate: 0,
    },
    invites: [],
    pinnedMessages: [],
    mutedMembers: [],
    bannedMembers: [],
    isMuted: false,
    isPinned: false,
    unreadCount: 0,
    mentionCount: 0,
    hasBot: false,
    tags: [],
    trustScore: 100,
    reportCount: 0,
    isReported: false,
    isFlagged: false,
  } as Group;

  // Debug actualGroup
  console.log('🔍 Actual Group Data:', {
    id: actualGroup.id,
    name: actualGroup.name,
    avatar: actualGroup.avatar,
    hasGroupObject: !!group,
    groupAvatar: groupAvatar,
  });

  // ==================== STATE MANAGEMENT ====================
  
  const [messages, setMessages] = useState<GroupMessage[]>([]);
  const [messageText, setMessageText] = useState('');
  const [isLoading, setIsLoading] = useState(false); // Start with false for better UX
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [replyingTo, setReplyingTo] = useState<GroupMessage | null>(null);
  const [editingMessage, setEditingMessage] = useState<string | null>(null);
  const [showGroupInfo, setShowGroupInfo] = useState(false);
  const [showMostActive, setShowMostActive] = useState(false);
  const [showEmojiPickerModal, setShowEmojiPickerModal] = useState(false);
  const [showAttachments, setShowAttachments] = useState(false);
  const [pinnedMessages, setPinnedMessages] = useState<GroupMessage[]>([]);
  const [showPinnedMessages, setShowPinnedMessages] = useState(false);

  // MISSING CRITICAL INTERACTIONS
  const [showMessageSearch, setShowMessageSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<GroupMessage[]>([]);
  const [highlightedMessageId, setHighlightedMessageId] = useState<string | null>(null);
  const [searchType, setSearchType] = useState<'all' | 'text' | 'media' | 'files' | 'date'>('all');
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [recording, setRecording] = useState<Audio.Recording | null>(null); // Fixed: Added actual recording object
  const [recordingUri, setRecordingUri] = useState<string | null>(null); // Fixed: Added recording URI
  const [hasRecordingToSend, setHasRecordingToSend] = useState(false); // Track if there's a recording ready to send
  const [isStoppingRecording, setIsStoppingRecording] = useState(false); // Prevent multiple stops
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null); // Timer reference for cleanup
  const [showMediaGallery, setShowMediaGallery] = useState(false);
  const [showGroupCall, setShowGroupCall] = useState(false);
  const [showPollCreator, setShowPollCreator] = useState(false);
  // Location sharing removed to avoid Google Maps API costs
  const [showContactPicker, setShowContactPicker] = useState(false);
  const [showMessageScheduler, setShowMessageScheduler] = useState(false);
  const [showTranslator, setShowTranslator] = useState(false);
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);

  const [showChatExport, setShowChatExport] = useState(false);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [showForwardModal, setShowForwardModal] = useState(false);
  const [showThreadView, setShowThreadView] = useState(false);
  const [selectedThread, setSelectedThread] = useState<GroupMessage | null>(null);
  const [showMentionPicker, setShowMentionPicker] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [showQuickReactions, setShowQuickReactions] = useState(false);
  const [quickReactionMessageId, setQuickReactionMessageId] = useState<string | null>(null);
  const [showMessageInfo, setShowMessageInfo] = useState(false);
  const [selectedMessageInfo, setSelectedMessageInfo] = useState<GroupMessage | null>(null);
  const [showGroupSettings, setShowGroupSettings] = useState(false);
  const [showAvatarZoom, setShowAvatarZoom] = useState(false);

  // New state for implemented features
  const [pollQuestion, setPollQuestion] = useState('');
  const [pollOptions, setPollOptions] = useState(['', '']);
  const [contacts, setContacts] = useState<any[]>([]);
  const [scheduledMessageText, setScheduledMessageText] = useState('');
  const [scheduledDate, setScheduledDate] = useState<Date | null>(null);
  const [scheduledTime, setScheduledTime] = useState<Date | null>(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTimePicker, setShowTimePicker] = useState(false);
  const [availableChats, setAvailableChats] = useState<any[]>([]);
  const [selectedForwardChats, setSelectedForwardChats] = useState<string[]>([]);
  const [threadMessages, setThreadMessages] = useState<GroupMessage[]>([]);
  const [threadReplyText, setThreadReplyText] = useState('');
  const [_uploading, _setUploading] = useState(false);
  const [showMembersList, setShowMembersList] = useState(false);
  const [showAnnouncementCreator, setShowAnnouncementCreator] = useState(false);
  const [showSlowModeTimer, setShowSlowModeTimer] = useState(false);
  const [slowModeTimeLeft, setSlowModeTimeLeft] = useState(0);
  const [showTypingMembers, setShowTypingMembers] = useState(false);
  const [showOnlineMembers, setShowOnlineMembers] = useState(false);
  const [showMessageReactions, setShowMessageReactions] = useState(false);
  const [selectedReactionMessage, setSelectedReactionMessage] = useState<GroupMessage | null>(null);
  const [showGroupInvite, setShowGroupInvite] = useState(false);

  // Message Actions and Reactions State (matching individual chat)
  const [showMessageActions, setShowMessageActions] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<GroupMessage | null>(null);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [swipeGestureEnabled, setSwipeGestureEnabled] = useState(true);
  const [messageSwipeStates, setMessageSwipeStates] = useState<{[key: string]: number}>({});
  const [longPressedMessage, setLongPressedMessage] = useState<GroupMessage | null>(null);
  const [showGroupQRCode, setShowGroupQRCode] = useState(false);
  const [_showGroupBackup, _setShowGroupBackup] = useState(false);
  const [_showGroupAnalytics, _setShowGroupAnalytics] = useState(false);

  // Media viewer state
  const [showMediaViewer, setShowMediaViewer] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<{
    url: string;
    type: 'image' | 'video' | 'audio' | 'document';
    fileName?: string;
    fileSize?: number;
    senderName?: string;
    timestamp?: Date;
  } | null>(null);
  const [_showMessageDrafts, _setShowMessageDrafts] = useState(false);
  const [_drafts, _setDrafts] = useState<{[key: string]: string}>({});
  const [_showAutoReply, _setShowAutoReply] = useState(false);
  const [_showMessageTemplates, _setShowMessageTemplates] = useState(false);
  const [_showChatThemes, _setShowChatThemes] = useState(false);
  const [_showMessageEffects, _setShowMessageEffects] = useState(false);
  const [_showGroupGames, _setShowGroupGames] = useState(false);
  const [_showGroupBot, _setShowGroupBot] = useState(false);
  const [_showGroupEvents, _setShowGroupEvents] = useState(false);
  const [_showGroupTasks, _setShowGroupTasks] = useState(false);
  const [_showGroupNotes, _setShowGroupNotes] = useState(false);
  const [_showGroupFiles, _setShowGroupFiles] = useState(false);
  const [_showGroupLinks, _setShowGroupLinks] = useState(false);
  const [_showGroupPolls, _setShowGroupPolls] = useState(false);
  const [_showGroupReminders, _setShowGroupReminders] = useState(false);
  const [_showGroupCalendar, _setShowGroupCalendar] = useState(false);
  const [_showGroupWhiteboard, _setShowGroupWhiteboard] = useState(false);
  const [_showGroupScreenShare, _setShowGroupScreenShare] = useState(false);
  const [_showGroupLiveLocation, _setShowGroupLiveLocation] = useState(false);
  const [_showGroupPayments, _setShowGroupPayments] = useState(false);
  const [_showGroupMarketplace, _setShowGroupMarketplace] = useState(false);
  const [_showGroupForum, _setShowGroupForum] = useState(false);
  const [_showGroupWiki, _setShowGroupWiki] = useState(false);
  const [_showGroupBookmarks, _setShowGroupBookmarks] = useState(false);
  const [_showGroupHashtags, _setShowGroupHashtags] = useState(false);
  const [_showGroupMentions, _setShowGroupMentions] = useState(false);
  const [_showGroupNotifications, _setShowGroupNotifications] = useState(false);
  const [_showGroupPrivacy, _setShowGroupPrivacy] = useState(false);
  const [_showGroupSecurity, _setShowGroupSecurity] = useState(false);
  const [_showGroupModeration, _setShowGroupModeration] = useState(false);
  const [_showGroupReports, _setShowGroupReports] = useState(false);
  const [_showGroupBans, _setShowGroupBans] = useState(false);
  const [_showGroupWarnings, _setShowGroupWarnings] = useState(false);
  // Removed unused group management states for production

  // Animation refs
  const headerAnim = useRef(new Animated.Value(1)).current;
  // inputAnim removed - unused

  // Floating emoji bar hook
  const {
    emojiBarState,
    showEmojiBar,
    hideEmojiBar,
  } = useFloatingEmojiBar();
  const typingAnim = useRef(new Animated.Value(0)).current;

  // Common emojis for reactions (matching individual chat)
  const commonEmojis = ["❤️", "😂", "😮", "😢", "😡", "👍"];

  // Refs
  const flatListRef = useRef<FlatList>(null);
  const inputRef = useRef<TextInput>(null);
  const typingTimer = useRef<NodeJS.Timeout | null>(null);

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    console.log('🔄 Loading messages for group:', actualGroup.id);
    loadMessages();
    loadPinnedMessages();

    // Cleanup function to unsubscribe from listeners
    return () => {
      console.log('🧹 Cleaning up message listeners');
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [actualGroup.id]);

  // Fixed: Reload pinned messages when messages change
  useEffect(() => {
    if (messages.length > 0) {
      const pinned = messages.filter(msg => msg.isPinned);
      if (pinned.length !== pinnedMessages.length) {
        loadPinnedMessages();
      }
    }
  }, [messages]);



  // VOICE RECORDING CLEANUP
  useEffect(() => {
    return () => {
      // Cleanup recording when component unmounts
      recordingManager.cleanup().catch((error) => {
        console.warn('⚠️ Recording cleanup error on unmount (non-critical):', error);
      });
    };
  }, []);

  // AUDIO PLAYBACK CLEANUP
  useEffect(() => {
    return () => {
      // Cleanup audio when component unmounts or chat changes
      console.log('🧹 Cleaning up audio on chat room unmount');
      audioManager.stopAll();
    };
  }, [actualGroup.id]);

  // VOICE RECORDING MODAL CLEANUP
  useEffect(() => {
    if (!showVoiceRecorder) {
      // Clean up recording when modal is closed, but with a delay to allow sending
      const cleanup = async () => {
        // Use recording manager for cleanup
        await recordingManager.cleanup();

        // Reset recording states but keep URI for a short time to allow sending
        setRecording(null);
        setIsRecording(false);
        setIsPaused(false);
        setRecordingDuration(0);
        setHasRecordingToSend(false);

        // Reset URI after a delay to allow sending to complete
        setTimeout(() => {
          setRecordingUri(null);
          console.log('🧹 Recording URI reset after delay');
        }, 2000); // 2 second delay

        console.log('🧹 Recording states reset on modal close (URI kept temporarily)');
      };

      cleanup();
    }
  }, [showVoiceRecorder]);

  // AUTO-START RECORDING WHEN MODAL OPENS
  useEffect(() => {
    if (showVoiceRecorder && !isRecording) {
      console.log('🎤 Voice recorder modal opened - starting recording immediately...');
      console.log('🔍 Current states:', { showVoiceRecorder, isRecording, recording: !!recording, hasRecordingToSend });
      // Start recording immediately without delay
      setTimeout(() => {
        if (showVoiceRecorder && !isRecording) {
          handleStartRecording();
        }
      }, 50); // Very small delay to ensure modal is rendered
    }
  }, [showVoiceRecorder]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Cleanup recording and timer on unmount
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
      if (recording) {
        recording.stopAndUnloadAsync().catch(() => {
          // Ignore cleanup errors on unmount
        });
      }
    };
  }, []);

  useEffect(() => {
    // Animate typing indicator
    if (typingUsers.length > 0) {
      Animated.timing(typingAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(typingAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [typingUsers]);

  // ==================== DATA METHODS ====================

  // Store unsubscribe function for cleanup
  const unsubscribeRef = useRef<(() => void) | null>(null);

  const loadMessages = async () => {
    // Don't show loading for better UX
    try {
      console.log('📡 Setting up real-time message listener for group:', actualGroup.id);

      // First, try to load preloaded messages for instant display
      try {
        const { messagePreloadService } = await import('../services/messagePreloadService');
        const preloadedMessages = messagePreloadService.getPreloadedMessages(actualGroup.id);

        if (preloadedMessages.length > 0) {
          // Convert preloaded messages to GroupMessage format
          const convertedMessages: GroupMessage[] = preloadedMessages.map(msg => ({
            id: msg.id,
            text: msg.content,
            senderId: msg.senderId,
            senderName: msg.senderName,
            senderAvatar: '',
            timestamp: msg.timestamp,
            status: msg.status,
            type: msg.type === 'file' ? 'document' : msg.type as any,
            readBy: [],
          }));

          setMessages(convertedMessages);
          setIsLoading(false);
          console.log(`⚡ Loaded ${convertedMessages.length} preloaded group messages instantly`);

          // Scroll to bottom
          setTimeout(() => {
            flatListRef.current?.scrollToEnd({ animated: false });
          }, 100);
        }
      } catch (preloadError) {
        console.error('❌ Error loading preloaded messages:', preloadError);
      }

      // Also try to load from local database for offline messages
      try {
        const { iraChatOfflineEngine } = await import('../services/iraChatOfflineEngine');
        const localMessages = await iraChatOfflineEngine.getMessages(actualGroup.id, 50, 0);

        if (localMessages.length > 0) {
          // Convert local messages to GroupMessage format
          const convertedLocalMessages: GroupMessage[] = localMessages.map(msg => ({
            id: msg.id,
            text: msg.text || '',
            senderId: msg.senderId,
            senderName: msg.senderName || 'Unknown User',
            senderAvatar: '',
            timestamp: msg.timestamp,
            status: (msg.status as 'sending' | 'sent' | 'delivered' | 'read' | 'failed') || 'sent',
            type: msg.type === 'audio' ? 'voice' : msg.type as any, // Convert audio back to voice
            mediaUrl: msg.mediaUrl,
            duration: msg.duration,
            fileName: msg.fileName,
            fileSize: msg.fileSize,
            readBy: [],
          }));

          // Merge with existing messages and remove duplicates
          setMessages(prev => {
            const existingIds = new Set(prev.map(m => m.id));
            const newMessages = convertedLocalMessages.filter(m => !existingIds.has(m.id));
            return [...prev, ...newMessages].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
          });

          console.log(`💾 Loaded ${convertedLocalMessages.length} local messages from database`);
        }
      } catch (localError) {
        console.error('❌ Error loading local messages:', localError);
      }

      const { collection, query, orderBy, onSnapshot } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      // Unsubscribe from previous listener if exists
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }

      const messagesRef = collection(db, 'groups', actualGroup.id, 'messages');
      const messagesQuery = query(messagesRef, orderBy('timestamp', 'asc'));

      const unsubscribe = onSnapshot(messagesQuery, (snapshot) => {
        console.log('📨 Firebase snapshot received, changes:', snapshot.docChanges().length);

        const loadedMessages: GroupMessage[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();

          // Safe data extraction with fallbacks
          try {
            loadedMessages.push({
              id: doc.id,
              text: data.text || '',
              senderId: data.senderId || 'unknown',
              senderName: data.senderName || 'Unknown User',
              senderAvatar: data.senderAvatar || '',
              timestamp: data.timestamp?.toDate() || new Date(),
              status: data.status || 'sent',
              type: data.type || 'text',
              mediaUrl: data.mediaUrl,
              mediaThumbnail: data.mediaThumbnail,
              duration: data.duration,
              fileName: data.fileName,
              fileSize: data.fileSize,
              mentions: data.mentions,
              isAnnouncement: data.isAnnouncement,
              announcementPriority: data.announcementPriority,
              replyTo: data.replyTo,
              reactions: data.reactions,
              threadReplies: data.threadReplies,
              threadCount: data.threadCount,
              isEdited: data.isEdited,
              editedAt: data.editedAt?.toDate(),
              isDeleted: data.isDeleted,
              deletedAt: data.deletedAt?.toDate(),
              isForwarded: data.isForwarded,
              forwardedFrom: data.forwardedFrom,
              isPinned: data.isPinned,
              pinnedBy: data.pinnedBy,
              pinnedAt: data.pinnedAt?.toDate(),
              readBy: data.readBy,
            });
          } catch (docError) {
            console.error('❌ Error processing message document:', docError);
            // Skip this message and continue
          }
        });

        console.log('📨 Loaded messages from Firebase:', loadedMessages.length);
        setMessages(loadedMessages);

        // Scroll to bottom after loading (only if there are messages)
        if (loadedMessages.length > 0) {
          setTimeout(() => {
            flatListRef.current?.scrollToEnd({ animated: false });
          }, 100);
        }

        setIsLoading(false);
      }, (error) => {
        console.error('❌ Firebase listener error:', error);
        // Don't show error to user, just log it
        setIsLoading(false);
        setMessages([]); // Set empty messages array
      });

      // Store unsubscribe function for cleanup
      unsubscribeRef.current = unsubscribe;

    } catch (error) {
      console.error('❌ Error setting up message listener:', error);
      // Don't show alert, just log error and continue
      setIsLoading(false);
      setMessages([]); // Set empty messages array
    }
  };

  const loadPinnedMessages = async () => {
    try {
      // Fixed: Load pinned messages from Firebase
      const { collection, query, where, getDocs, orderBy } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      const messagesRef = collection(db, 'groups', actualGroup.id, 'messages');
      const pinnedQuery = query(
        messagesRef,
        where('isPinned', '==', true),
        orderBy('pinnedAt', 'desc')
      );

      const snapshot = await getDocs(pinnedQuery);
      const pinnedFromFirebase = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date(),
        pinnedAt: doc.data().pinnedAt?.toDate() || new Date(),
      })) as GroupMessage[];

      setPinnedMessages(pinnedFromFirebase);
      console.log('✅ [DEBUG] Loaded pinned messages from Firebase:', pinnedFromFirebase.length);
    } catch (error) {
      console.error('❌ Error loading pinned messages:', error);
      // Fallback to local filtering
      const pinned = messages.filter(msg => msg.isPinned);
      setPinnedMessages(pinned);
    }
  };

  const sendMessage = async () => {
    if (!messageText.trim() && !replyingTo && !editingMessage) return;

    // Handle editing existing message
    if (editingMessage) {
      try {
        const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');
        const { db } = await import('../services/firebaseSimple');

        const messageRef = doc(db, 'groups', actualGroup.id, 'messages', editingMessage);
        await updateDoc(messageRef, {
          text: messageText.trim(),
          isEdited: true,
          editedAt: serverTimestamp(),
        });

        // Update locally
        setMessages(prev => prev.map(msg =>
          msg.id === editingMessage
            ? { ...msg, text: messageText.trim(), isEdited: true, editedAt: new Date() }
            : msg
        ));

        setMessageText('');
        setEditingMessage(null);
        console.log('✅ Message edited successfully');
        return;
      } catch (error) {
        console.error('❌ Error editing message:', error);
        Alert.alert('Error', 'Failed to edit message. Please try again.');
        return;
      }
    }

    // Handle sending new message
    const newMessage: GroupMessage = {
      id: `msg_${Date.now()}`,
      text: messageText.trim(),
      senderId: currentUserId,
      senderName: currentUserName || 'User',
      senderAvatar: currentUserAvatar || '',
      timestamp: new Date(),
      status: 'sending',
      type: 'text',
      replyTo: replyingTo ? {
        messageId: replyingTo.id,
        text: replyingTo.text || '',
        senderName: replyingTo.senderName,
        type: replyingTo.type,
        mediaUrl: replyingTo.mediaUrl,
      } : undefined,
    };

    // Add message optimistically
    setMessages(prev => [...prev, newMessage]);
    setMessageText('');
    setReplyingTo(null);
    
    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);

    try {
      // Send to Firebase
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      // Clean the message data to remove undefined values
      const cleanMessageData: any = {
        text: newMessage.text || '',
        senderId: newMessage.senderId,
        senderName: newMessage.senderName,
        senderAvatar: newMessage.senderAvatar || '',
        timestamp: serverTimestamp(),
        status: 'sent',
        type: newMessage.type,
        groupId: actualGroup.id,
      };

      // Only add replyTo if it exists and has valid data
      if (newMessage.replyTo && newMessage.replyTo.messageId) {
        cleanMessageData.replyTo = {
          messageId: newMessage.replyTo.messageId,
          text: newMessage.replyTo.text || '',
          senderName: newMessage.replyTo.senderName || '',
          type: newMessage.replyTo.type || 'text',
        };

        // Only add mediaUrl if it exists
        if (newMessage.replyTo.mediaUrl) {
          cleanMessageData.replyTo.mediaUrl = newMessage.replyTo.mediaUrl;
        }
      }

      // Add optional fields only if they exist
      if (newMessage.mediaUrl) cleanMessageData.mediaUrl = newMessage.mediaUrl;
      if (newMessage.mediaThumbnail) cleanMessageData.mediaThumbnail = newMessage.mediaThumbnail;
      if (newMessage.duration) cleanMessageData.duration = newMessage.duration;
      if (newMessage.fileName) cleanMessageData.fileName = newMessage.fileName;
      if (newMessage.fileSize) cleanMessageData.fileSize = newMessage.fileSize;
      if (newMessage.mentions && newMessage.mentions.length > 0) cleanMessageData.mentions = newMessage.mentions;
      if (newMessage.isAnnouncement) cleanMessageData.isAnnouncement = newMessage.isAnnouncement;
      if (newMessage.announcementPriority) cleanMessageData.announcementPriority = newMessage.announcementPriority;
      if (newMessage.reactions) cleanMessageData.reactions = newMessage.reactions;

      console.log('📤 Sending clean message data to Firebase:', cleanMessageData);

      const docRef = await addDoc(collection(db, 'groups', actualGroup.id, 'messages'), cleanMessageData);
      console.log('✅ Message sent to Firebase with ID:', docRef.id);

      // Update status with Firebase document ID
      setMessages(prev => prev.map(msg =>
        msg.id === newMessage.id
          ? { ...msg, status: 'sent' as const, id: docRef.id }
          : msg
      ));

      // Update last message for the group
      try {
        const { lastMessageSyncService } = await import('../services/lastMessageSyncService');
        await lastMessageSyncService.updateChatLastMessage(
          actualGroup.id,
          newMessage.text || '',
          currentUserId,
          currentUserName || 'User',
          'text'
        );
      } catch (error) {
        console.error('❌ Failed to update group last message:', error);
      }
    } catch (error) {
      console.error('❌ Error sending message:', error);
      console.error('❌ Message data that failed:', newMessage);
      // Keep status as sending for retry
      setMessages(prev => prev.map(msg =>
        msg.id === newMessage.id
          ? { ...msg, status: 'sending' as const }
          : msg
      ));
      Alert.alert('Error', 'Failed to send message. Please try again.');
    }
  };

  const handleTyping = (text: string) => {
    setMessageText(text);
    
    if (!isTyping) {
      setIsTyping(true);
      // Notify others that user is typing
      // typingService.startTyping(group.id, currentUserId, currentUserName);
    }

    // Reset typing timer
    if (typingTimer.current) {
      clearTimeout(typingTimer.current);
    }

    typingTimer.current = setTimeout(() => {
      setIsTyping(false);
      // typingService.stopTyping(group.id, currentUserId);
    }, 2000);
  };

  const handleReply = (message: GroupMessage) => {
    setReplyingTo(message);
    setShowMessageActions(false);
    inputRef.current?.focus();
  };

  // Reaction Functions (adapted for group chat structure)
  const addReaction = async (messageId: string, emoji: string) => {
    if (!currentUserId) return;

    try {
      const { doc, updateDoc } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      const messageRef = doc(db, `groups/${actualGroup.id}/messages/${messageId}`);
      const message = messages.find((m) => m.id === messageId);

      if (message) {
        const currentReactions = message.reactions || {};
        const updatedReactions = { ...currentReactions };

        // Group chat uses emoji -> {users: [], count: number} structure
        if (updatedReactions[emoji]) {
          if (updatedReactions[emoji].users.includes(currentUserId)) {
            // Remove reaction
            updatedReactions[emoji].users = updatedReactions[emoji].users.filter(id => id !== currentUserId);
            updatedReactions[emoji].count = updatedReactions[emoji].users.length;
            if (updatedReactions[emoji].count === 0) {
              delete updatedReactions[emoji];
            }
          } else {
            // Add reaction
            updatedReactions[emoji].users.push(currentUserId);
            updatedReactions[emoji].count = updatedReactions[emoji].users.length;
          }
        } else {
          // New reaction
          updatedReactions[emoji] = {
            users: [currentUserId],
            count: 1,
          };
        }

        await updateDoc(messageRef, {
          reactions: updatedReactions,
        });
      }

      setShowMessageActions(false);
    } catch (error) {
      console.error("Error adding reaction:", error);
      Alert.alert("Error", "Failed to add reaction");
    }
  };

  const handleEditMessage = (message: GroupMessage) => {
    if (message.senderId === currentUserId && message.type === "text") {
      setEditingMessage(message.id);
      setMessageText(message.text || "");
      setShowMessageActions(false);
      inputRef.current?.focus();
    }
  };

  const handleDelete = async (messageId: string) => {
    Alert.alert(
      'Delete Message',
      'Are you sure you want to delete this message?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Update locally
              setMessages(prev => prev.map(msg => 
                msg.id === messageId 
                  ? { ...msg, isDeleted: true, deletedAt: new Date() }
                  : msg
              ));

              // Sync with backend
              // await groupMessageService.deleteMessage(messageId);
            } catch (error) {
              console.error('❌ Error deleting message:', error);
            }
          },
        },
      ]
    );
  };

  const handleForward = (messageId: string) => {
    const message = messages.find(msg => msg.id === messageId);
    if (message) {
      setSelectedMessages([messageId]);
      setShowForwardModal(true);
    }
  };

  const handlePin = async (messageId: string) => {
    try {
      console.log('📌 [DEBUG] Pinning/unpinning message:', messageId);

      const message = messages.find(msg => msg.id === messageId);
      if (!message) {
        console.error('❌ Message not found for pinning:', messageId);
        return;
      }

      const isPinned = message.isPinned;

      // Update locally first for immediate feedback
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? {
              ...msg,
              isPinned: !isPinned,
              pinnedBy: !isPinned ? currentUserId : undefined,
              pinnedAt: !isPinned ? new Date() : undefined,
            }
          : msg
      ));

      // Update pinned messages list
      if (!isPinned) {
        setPinnedMessages(prev => [...prev, { ...message, isPinned: true, pinnedBy: currentUserId, pinnedAt: new Date() }]);
        console.log('✅ [DEBUG] Message pinned locally');
      } else {
        setPinnedMessages(prev => prev.filter(msg => msg.id !== messageId));
        console.log('✅ [DEBUG] Message unpinned locally');
      }

      // Sync with Firebase
      try {
        const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');
        const { db } = await import('../services/firebaseSimple');

        const messageRef = doc(db, 'groups', actualGroup.id, 'messages', messageId);
        await updateDoc(messageRef, {
          isPinned: !isPinned,
          pinnedBy: !isPinned ? currentUserId : null,
          pinnedAt: !isPinned ? serverTimestamp() : null,
        });

        console.log('✅ [DEBUG] Message pin status synced to Firebase');

        // Fixed: Removed success alert message
      } catch (firebaseError) {
        console.error('❌ Firebase pin sync failed:', firebaseError);
        // Revert local changes if Firebase fails
        setMessages(prev => prev.map(msg =>
          msg.id === messageId
            ? {
                ...msg,
                isPinned: isPinned,
                pinnedBy: isPinned ? currentUserId : undefined,
                pinnedAt: isPinned ? new Date() : undefined,
              }
            : msg
        ));

        if (isPinned) {
          setPinnedMessages(prev => [...prev, message]);
        } else {
          setPinnedMessages(prev => prev.filter(msg => msg.id !== messageId));
        }

        Alert.alert('Error', 'Failed to sync pin status. Please try again.');
      }
    } catch (error) {
      console.error('❌ Error pinning message:', error);
      Alert.alert('Error', 'Failed to pin message. Please try again.');
    }
  };

  // New implemented functions
  const handleCreatePoll = async (question: string, options: string[]) => {
    try {
      if (!question.trim() || options.filter(opt => opt.trim()).length < 2) {
        Alert.alert('Error', 'Please provide a question and at least 2 options');
        return;
      }

      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      // Clean poll data to remove undefined values
      const cleanPollData: any = {
        type: 'poll',
        question: question.trim(),
        options: options.filter(opt => opt.trim()).map(opt => ({
          text: opt.trim(),
          votes: [],
          voteCount: 0,
        })),
        senderId: currentUserId,
        senderName: currentUserName || 'Unknown User',
        senderAvatar: currentUserAvatar || '',
        timestamp: serverTimestamp(),
        status: 'sent',
        groupId: actualGroup.id,
        totalVotes: 0,
        isActive: true,
      };

      console.log('📤 Sending clean poll data to Firebase:', cleanPollData);

      await addDoc(collection(db, 'groups', actualGroup.id, 'messages'), cleanPollData);
      setPollQuestion('');
      setPollOptions(['', '']);
      setShowPollCreator(false);
      Alert.alert('Success', 'Poll created successfully!');
    } catch (error) {
      console.error('❌ Error creating poll:', error);
      Alert.alert('Error', 'Failed to create poll. Please try again.');
    }
  };

  const loadContacts = async () => {
    try {
      // Load contacts from device
      const { getContactsAsync, requestPermissionsAsync } = await import('expo-contacts');

      const { status } = await requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Contacts permission is required to share contacts.');
        return;
      }

      const { data } = await getContactsAsync({
        fields: ['name', 'phoneNumbers', 'image'],
      });

      if (data.length > 0) {
        const formattedContacts = data.map(contact => ({
          id: contact.id,
          displayName: contact.name || 'Unknown',
          phoneNumber: contact.phoneNumbers?.[0]?.number || '',
          photoURL: contact.image?.uri || null,
        })).filter(contact => contact.phoneNumber); // Only contacts with phone numbers

        setContacts(formattedContacts);
      }
    } catch (error) {
      console.error('❌ Error loading contacts:', error);
      Alert.alert('Error', 'Failed to load contacts. Please try again.');
    }
  };

  const handleShareContact = async (contact: any) => {
    try {
      const contactMessage: GroupMessage = {
        id: `contact_${Date.now()}`,
        senderId: currentUserId,
        senderName: currentUserName || 'Unknown User',
        senderAvatar: currentUserAvatar,
        timestamp: new Date(),
        status: 'sending',
        type: 'contact',
        text: `Contact: ${contact.displayName}`,
        mediaUrl: contact.photoURL,
      };

      // Add message optimistically
      setMessages(prev => [...prev, contactMessage]);

      // Send to Firebase
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      // Clean the message data to remove undefined values
      const cleanMessageData: any = {
        text: contactMessage.text || '',
        senderId: contactMessage.senderId,
        senderName: contactMessage.senderName,
        senderAvatar: contactMessage.senderAvatar || '',
        timestamp: serverTimestamp(),
        status: 'sent',
        type: contactMessage.type,
        groupId: actualGroup.id,
        contact: {
          name: contact.displayName || 'Unknown Contact',
          phoneNumber: contact.phoneNumber || '',
        },
      };

      // Add optional fields only if they exist
      if (contact.photoURL) {
        cleanMessageData.contact.photoURL = contact.photoURL;
      }
      if (contactMessage.mediaUrl) {
        cleanMessageData.mediaUrl = contactMessage.mediaUrl;
      }

      console.log('📤 Sending clean contact message data to Firebase:', cleanMessageData);

      const docRef = await addDoc(collection(db, 'groups', actualGroup.id, 'messages'), cleanMessageData);
      console.log('✅ Contact message sent to Firebase with ID:', docRef.id);

      // Update status
      setMessages(prev => prev.map(msg =>
        msg.id === contactMessage.id
          ? { ...msg, status: 'sent' as const, id: docRef.id }
          : msg
      ));

      setShowContactPicker(false);
      Alert.alert('Success', 'Contact shared successfully!');
    } catch (error) {
      console.error('❌ Error sharing contact:', error);
      Alert.alert('Error', 'Failed to share contact. Please try again.');
    }
  };

  const handleScheduleMessage = async () => {
    try {
      if (!scheduledMessageText.trim() || !scheduledDate || !scheduledTime) {
        Alert.alert('Error', 'Please fill in all fields');
        return;
      }

      const scheduledDateTime = new Date(
        scheduledDate.getFullYear(),
        scheduledDate.getMonth(),
        scheduledDate.getDate(),
        scheduledTime.getHours(),
        scheduledTime.getMinutes()
      );

      if (scheduledDateTime <= new Date()) {
        Alert.alert('Error', 'Scheduled time must be in the future');
        return;
      }

      const { collection, addDoc } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      const scheduledMessage = {
        text: scheduledMessageText.trim(),
        senderId: currentUserId,
        senderName: currentUserName || 'You',
        groupId: actualGroup.id,
        scheduledFor: scheduledDateTime,
        isScheduled: true,
        createdAt: new Date(),
      };

      await addDoc(collection(db, 'scheduled_messages'), scheduledMessage);
      setScheduledMessageText('');
      setScheduledDate(null);
      setScheduledTime(null);
      setShowMessageScheduler(false);
      Alert.alert('Success', 'Message scheduled successfully!');
    } catch (error) {
      console.error('❌ Error scheduling message:', error);
      Alert.alert('Error', 'Failed to schedule message. Please try again.');
    }
  };

  const toggleForwardChat = (chatId: string) => {
    setSelectedForwardChats(prev =>
      prev.includes(chatId)
        ? prev.filter(id => id !== chatId)
        : [...prev, chatId]
    );
  };

  const handleForwardMessages = async () => {
    try {
      if (selectedForwardChats.length === 0 || selectedMessages.length === 0) {
        Alert.alert('Error', 'Please select messages and chats to forward to');
        return;
      }

      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      const messagesToForward = messages.filter(msg => selectedMessages.includes(msg.id));

      for (const chatId of selectedForwardChats) {
        for (const message of messagesToForward) {
          const forwardedMessage = {
            ...message,
            id: undefined, // Let Firestore generate new ID
            forwardedFrom: {
              groupId: actualGroup.id,
              groupName: actualGroup.name,
              originalSender: message.senderName,
            },
            senderId: currentUserId,
            senderName: currentUserName || 'You',
            timestamp: serverTimestamp(),
            groupId: chatId,
          };

          await addDoc(collection(db, 'group_messages'), forwardedMessage);
        }
      }

      setSelectedMessages([]);
      setSelectedForwardChats([]);
      setShowForwardModal(false);
      setIsSelectionMode(false);
      Alert.alert('Success', 'Messages forwarded successfully!');
    } catch (error) {
      console.error('❌ Error forwarding messages:', error);
      Alert.alert('Error', 'Failed to forward messages. Please try again.');
    }
  };

  const handleSendThreadReply = async () => {
    try {
      if (!threadReplyText.trim() || !selectedThread) {
        return;
      }

      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      const threadReply = {
        text: threadReplyText.trim(),
        senderId: currentUserId,
        senderName: currentUserName || 'You',
        timestamp: serverTimestamp(),
        threadId: selectedThread.id,
        groupId: actualGroup.id,
        type: 'thread_reply',
      };

      await addDoc(collection(db, 'message_threads'), threadReply);
      setThreadReplyText('');

      // Refresh thread messages
      loadThreadMessages(selectedThread.id);
    } catch (error) {
      console.error('❌ Error sending thread reply:', error);
      Alert.alert('Error', 'Failed to send reply. Please try again.');
    }
  };

  const loadThreadMessages = async (threadId: string) => {
    try {
      const { collection, query, where, orderBy, getDocs } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      const threadsQuery = query(
        collection(db, 'message_threads'),
        where('threadId', '==', threadId),
        orderBy('timestamp', 'asc')
      );

      const snapshot = await getDocs(threadsQuery);
      const threads = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as GroupMessage[];

      setThreadMessages(threads);
    } catch (error) {
      console.error('❌ Error loading thread messages:', error);
    }
  };

  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      hour12: undefined // Fixed: Let device determine 12/24 hour format
    });
  };

  const handleUserPress = (userId: string) => {
    // Validate userId before navigation
    if (!userId || userId.trim() === '') {
      console.warn('⚠️ Invalid userId provided for profile navigation:', userId);
      return;
    }

    console.log('👤 Navigating to user profile:', userId);
    try {
      // Check if it's the current user - navigate to own profile instead of user profile view
      if (userId === currentUserId) {
        console.log('👤 Current user clicked - navigating to own profile');
        navigationService.navigate(ROUTES.PROFILE.MAIN, {});
      } else {
        // Navigate to other user's profile
        navigationService.navigate(ROUTES.PROFILE.VIEW(userId), {});
      }
    } catch (error) {
      console.error('❌ Error navigating to profile:', error);
      console.error('Failed route:', userId === currentUserId ? ROUTES.PROFILE.MAIN : ROUTES.PROFILE.VIEW(userId));
    }
  };

  // ==================== MISSING CRITICAL INTERACTION HANDLERS ====================

  const handleMessageSearch = async (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const lowerQuery = query.toLowerCase();
      let results: GroupMessage[] = [];

      // Check if query is a date search (e.g., "2024-01-15" or "today" or "yesterday")
      const isDateSearch = /^\d{4}-\d{2}-\d{2}$/.test(query) ||
                          ['today', 'yesterday'].includes(lowerQuery);

      if (isDateSearch) {
        let targetDate: Date;
        if (lowerQuery === 'today') {
          targetDate = new Date();
        } else if (lowerQuery === 'yesterday') {
          targetDate = new Date();
          targetDate.setDate(targetDate.getDate() - 1);
        } else {
          targetDate = new Date(query);
        }

        results = messages.filter(msg => {
          const msgDate = new Date(msg.timestamp);
          return msgDate.toDateString() === targetDate.toDateString();
        });
      } else {
        // Enhanced search based on type
        results = messages.filter(msg => {
          // Text search
          const textMatch = msg.text?.toLowerCase().includes(lowerQuery) ||
                           msg.senderName.toLowerCase().includes(lowerQuery);

          // Media search
          const mediaMatch = (msg.type === 'image' || msg.type === 'video') &&
                             (msg.fileName?.toLowerCase().includes(lowerQuery) || textMatch);

          // File search
          const fileMatch = (msg.type === 'document' || msg.type === 'voice') &&
                           (msg.fileName?.toLowerCase().includes(lowerQuery) || textMatch);

          // Filter based on search type
          switch (searchType) {
            case 'text':
              return msg.type === 'text' && textMatch;
            case 'media':
              return (msg.type === 'image' || msg.type === 'video') && (mediaMatch || textMatch);
            case 'files':
              return (msg.type === 'document' || msg.type === 'voice') && (fileMatch || textMatch);
            case 'all':
            default:
              return textMatch || mediaMatch || fileMatch;
          }
        });
      }

      setSearchResults(results);
    } catch (error) {
      console.error('❌ Error searching messages:', error);
    }
  };

  const handleCameraCapture = async () => {
    try {
      setShowAttachments(false);

      // Request camera permissions
      const { launchCameraAsync, requestCameraPermissionsAsync } = await import('expo-image-picker');

      const permissionResult = await requestCameraPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'Camera permission is required to take photos.');
        return;
      }

      const result = await launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        await sendMediaMessage(asset.uri, asset.type === 'video' ? 'video' : 'image', asset.fileName || undefined);
      }
    } catch (error) {
      console.error('❌ Error capturing from camera:', error);
      Alert.alert('Error', 'Failed to capture from camera. Please try again.');
    }
  };

  const handleGalleryPicker = async () => {
    try {
      setShowAttachments(false);

      // Request media library permissions
      const { launchImageLibraryAsync, requestMediaLibraryPermissionsAsync } = await import('expo-image-picker');

      const permissionResult = await requestMediaLibraryPermissionsAsync();
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'Media library permission is required to select photos.');
        return;
      }

      const result = await launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        await sendMediaMessage(asset.uri, asset.type === 'video' ? 'video' : 'image', asset.fileName || undefined);
      }
    } catch (error) {
      console.error('❌ Error picking from gallery:', error);
      Alert.alert('Error', 'Failed to pick from gallery. Please try again.');
    }
  };

  const handleDocumentPicker = async () => {
    try {
      setShowAttachments(false);

      // Use document picker
      const { getDocumentAsync } = await import('expo-document-picker');

      const result = await getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        await sendDocumentMessage(asset.uri, asset.name, asset.size);
      }
    } catch (error) {
      console.error('❌ Error picking document:', error);
      Alert.alert('Error', 'Failed to pick document. Please try again.');
    }
  };

  const sendDocumentMessage = async (uri: string, fileName: string, fileSize?: number) => {
    try {
      const documentMessage: GroupMessage = {
        id: `doc_${Date.now()}`,
        senderId: currentUserId,
        senderName: currentUserName || 'Unknown User',
        senderAvatar: currentUserAvatar,
        timestamp: new Date(),
        status: 'sending',
        type: 'document',
        mediaUrl: uri,
        fileName: fileName,
        fileSize: fileSize,
      };

      // Add message optimistically
      setMessages(prev => [...prev, documentMessage]);

      // Send to Firebase
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      // Clean the message data to remove undefined values
      const cleanMessageData: any = {
        senderId: documentMessage.senderId,
        senderName: documentMessage.senderName,
        senderAvatar: documentMessage.senderAvatar || '',
        timestamp: serverTimestamp(),
        status: 'sent',
        type: documentMessage.type,
        groupId: actualGroup.id,
      };

      // Add optional fields only if they exist
      if (documentMessage.text) cleanMessageData.text = documentMessage.text;
      if (documentMessage.mediaUrl) cleanMessageData.mediaUrl = documentMessage.mediaUrl;
      if (documentMessage.fileName) cleanMessageData.fileName = documentMessage.fileName;
      if (documentMessage.fileSize) cleanMessageData.fileSize = documentMessage.fileSize;

      console.log('📤 Sending clean document message data to Firebase:', cleanMessageData);

      const docRef = await addDoc(collection(db, 'groups', actualGroup.id, 'messages'), cleanMessageData);
      console.log('✅ Document message sent to Firebase with ID:', docRef.id);

      // Update status
      setMessages(prev => prev.map(msg =>
        msg.id === documentMessage.id
          ? { ...msg, status: 'sent' as const, id: docRef.id }
          : msg
      ));
    } catch (error) {
      console.error('❌ Error sending document message:', error);
      Alert.alert('Error', 'Failed to send document. Please try again.');
    }
  };

  const sendMediaMessage = async (uri: string, type: 'image' | 'video', fileName?: string) => {
    try {
      const mediaMessage: GroupMessage = {
        id: `media_${Date.now()}`,
        senderId: currentUserId,
        senderName: currentUserName || 'Unknown User',
        senderAvatar: currentUserAvatar,
        timestamp: new Date(),
        status: 'sending',
        type: type,
        mediaUrl: uri,
        fileName: fileName || `${type}_${Date.now()}`,
      };

      // Add message optimistically
      setMessages(prev => [...prev, mediaMessage]);

      // Send to Firebase
      const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');
      const { db } = await import('../services/firebaseSimple');

      // Clean the message data to remove undefined values
      const cleanMessageData: any = {
        senderId: mediaMessage.senderId,
        senderName: mediaMessage.senderName,
        senderAvatar: mediaMessage.senderAvatar || '',
        timestamp: serverTimestamp(),
        status: 'sent',
        type: mediaMessage.type,
        groupId: actualGroup.id,
      };

      // Add optional fields only if they exist
      if (mediaMessage.text) cleanMessageData.text = mediaMessage.text;
      if (mediaMessage.mediaUrl) cleanMessageData.mediaUrl = mediaMessage.mediaUrl;
      if (mediaMessage.fileName) cleanMessageData.fileName = mediaMessage.fileName;
      if (mediaMessage.fileSize) cleanMessageData.fileSize = mediaMessage.fileSize;

      console.log('📤 Sending clean media message data to Firebase:', cleanMessageData);

      const docRef = await addDoc(collection(db, 'groups', actualGroup.id, 'messages'), cleanMessageData);
      console.log('✅ Media message sent to Firebase with ID:', docRef.id);

      // Update status
      setMessages(prev => prev.map(msg =>
        msg.id === mediaMessage.id
          ? { ...msg, status: 'sent' as const, id: docRef.id }
          : msg
      ));
    } catch (error) {
      console.error('❌ Error sending media message:', error);
      Alert.alert('Error', 'Failed to send media. Please try again.');
    }
  };

  const handleVoiceRecording = async () => {
    try {
      if (isRecording) {
        // Stop recording
        console.log('🛑 Stopping recording...');
        if (recording) {
          await recording.stopAndUnloadAsync();
          const uri = recording.getURI();
          console.log('✅ Recording stopped, URI:', uri);

          if (uri) {
            setRecordingUri(uri);

            // Create voice message with actual recording
            const voiceMessage: GroupMessage = {
              id: `voice_${Date.now()}`,
              senderId: currentUserId,
              senderName: currentUserName || 'Unknown User',
              senderAvatar: currentUserAvatar,
              timestamp: new Date(),
              status: 'sending',
              type: 'voice',
              duration: recordingDuration,
              mediaUrl: uri, // Fixed: Use actual recording URI
            };

            setMessages(prev => [...prev, voiceMessage]);
            console.log('📤 Voice message created:', voiceMessage);
          }
        }

        await cleanupRecording();
        setShowVoiceRecorder(false);
      } else {
        // Start recording using the improved function
        await handleStartRecording();
        setShowVoiceRecorder(true);
      }
    } catch (error) {
      console.error('❌ Error in voice recording:', error);
      Alert.alert('Error', 'Failed to record voice message. Please try again.');
      await cleanupRecording();
      setShowVoiceRecorder(false);
    }
  };

  const cleanupRecording = async () => {
    // Clear timer
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }

    // Use recording manager for cleanup
    await recordingManager.cleanup();

    // Reset local state
    setRecording(null);
    setIsRecording(false);
    setIsPaused(false);
    setRecordingDuration(0);

    // Reset states
    setIsRecording(false);
    setRecordingDuration(0);
    setRecording(null);
    setIsStoppingRecording(false);
  };

  const handleStartRecording = async () => {
    try {
      console.log('🎤 Starting recording...');

      // Request permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant microphone permission to record voice messages.');
        return;
      }

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Create recording using the manager (this handles cleanup automatically)
      const newRecording = await recordingManager.createRecording();
      await newRecording.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);
      await newRecording.startAsync();

      // Set local state
      setRecording(newRecording);
      setIsRecording(true);
      setRecordingDuration(0);

      // Start recording timer
      recordingTimerRef.current = setInterval(() => {
        setRecordingDuration(prev => {
          const newDuration = prev + 1;
          if (newDuration >= 300) { // Max 5 minutes
            handleStopRecording(); // Auto-stop
          }
          return newDuration;
        });
      }, 1000);

      console.log('✅ Recording started successfully');

    } catch (error: any) {
      console.error('❌ Error starting recording:', error);

      // Handle specific error cases with single retry
      if (error.message?.includes('Only one Recording object can be prepared') ||
          error.message?.includes('recording not started') ||
          error.message?.includes('Recorder does not exist')) {
        console.log('🔄 Recording conflict detected, cleaning up and retrying once...');

        // Force cleanup and retry once
        await recordingManager.cleanup();
        await new Promise(resolve => setTimeout(resolve, 500));

        try {
          const retryRecording = await recordingManager.createRecording();
          await retryRecording.prepareToRecordAsync(Audio.RecordingOptionsPresets.HIGH_QUALITY);
          await retryRecording.startAsync();

          setRecording(retryRecording);
          setIsRecording(true);
          setRecordingDuration(0);

          recordingTimerRef.current = setInterval(() => {
            setRecordingDuration(prev => {
              const newDuration = prev + 1;
              if (newDuration >= 300) {
                handleStopRecording();
              }
              return newDuration;
            });
          }, 1000);

          console.log('✅ Recording started successfully on retry');
          return;
        } catch (retryError) {
          console.error('❌ Retry failed:', retryError);
        }
      }

      Alert.alert('Error', 'Failed to start recording. Please try again.');
      await cleanupRecording();
    }
  };

  const handleStopRecording = async () => {
    // Prevent multiple stops
    if (isStoppingRecording) {
      console.log('⚠️ Already stopping recording, ignoring...');
      return;
    }

    try {
      setIsStoppingRecording(true);
      console.log('🛑 Stopping recording...');

      if (recording && isRecording) {
        console.log('📍 Stopping recording...');

        // Get URI before stopping to avoid null reference
        let uri = recording.getURI();
        console.log('📍 Recording URI before stop:', uri);
        console.log('📍 Recording duration:', recordingDuration);

        // Only stop if not already stopped
        try {
          await recording.stopAndUnloadAsync();
          console.log('✅ Recording stopped successfully');

          // Get URI again after stopping (sometimes it's only available after stop)
          if (!uri) {
            uri = recording.getURI();
            console.log('📍 Recording URI after stop:', uri);
          }
        } catch (stopError) {
          console.warn('⚠️ Recording may already be stopped:', stopError);
        }

        if (uri && recordingDuration > 0) { // Allow any duration > 0 for now
          setRecordingUri(uri);
          setHasRecordingToSend(true);
          console.log('✅ Recording ready to send:', uri);
          return uri; // Return the URI for immediate use
        } else {
          console.warn('⚠️ Recording not valid for sending:', { uri, duration: recordingDuration });
          // Don't throw error, just return null and let caller handle it
          return null;
        }
      }

      // Clean up recording state
      await cleanupRecording();
    } catch (error) {
      console.error('❌ Error stopping recording:', error);
      Alert.alert('Error', 'Failed to stop recording.');
      await cleanupRecording();
      setShowVoiceRecorder(false);
      setHasRecordingToSend(false);
      throw error; // Re-throw to handle in calling function
    } finally {
      setIsStoppingRecording(false);
    }
  };

  const handleSendVoiceMessage = async (providedUri?: string) => {
    try {
      const uriToUse = providedUri || recordingUri;
      console.log('📤 Attempting to send voice message...');
      console.log('📍 Recording URI:', uriToUse);
      console.log('📍 Recording Duration:', recordingDuration);

      if (!uriToUse) {
        console.error('❌ No recording URI available');
        Alert.alert('Error', 'No recording found. Please record a voice message first.');
        return;
      }

      if (recordingDuration <= 1) {
        console.error('❌ Recording too short:', recordingDuration);
        Alert.alert('Error', 'Recording is too short. Please record for at least 1 second.');
        return;
      }

      console.log('📤 Sending voice message...');

      // Create voice message with actual recording
      const voiceMessage: GroupMessage = {
        id: `voice_${Date.now()}`,
        senderId: currentUserId,
        senderName: currentUserName || 'Unknown User',
        senderAvatar: currentUserAvatar,
        timestamp: new Date(),
        status: 'sending',
        type: 'voice',
        duration: recordingDuration,
        mediaUrl: recordingUri || undefined, // Convert null to undefined for type compatibility
      };

      // Add to local messages immediately
      setMessages(prev => [...prev, voiceMessage]);
      console.log('📤 Voice message created locally:', voiceMessage);

      // Save to local database for persistence
      try {
        const { iraChatOfflineEngine } = await import('../services/iraChatOfflineEngine');
        const { offlineDatabaseService } = await import('../services/offlineDatabase');

        // Save to offline engine
        const offlineMessageId = await iraChatOfflineEngine.sendMessage(
          actualGroup.id,
          '', // No text for voice messages
          currentUserId,
          'audio', // Use 'audio' type for voice messages in offline engine
          {
            mediaUri: uriToUse, // Include URI in additionalData
            duration: recordingDuration,
            fileName: `voice_${Date.now()}.m4a`,
            fileSize: 0, // Will be updated after upload
            type: 'voice',
            senderName: currentUserName || 'Unknown User',
            senderAvatar: currentUserAvatar,
          }
        );

        // Also save to offline database for better persistence
        const db = offlineDatabaseService.getDatabase();
        await db.runAsync(`
          INSERT OR REPLACE INTO messages (
            id, chatId, senderId, text, type, mediaUrl, fileName, duration,
            timestamp, status, isDeleted, syncStatus, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          voiceMessage.id,
          actualGroup.id,
          currentUserId,
          '', // No text for voice messages
          'voice',
          uriToUse,
          `voice_${Date.now()}.m4a`,
          recordingDuration,
          new Date().getTime(),
          'sent',
          0, // not deleted
          'pending', // will be synced to Firebase
          new Date().getTime(),
          new Date().getTime()
        ]);

        console.log('✅ Voice message saved to local database and offline engine:', offlineMessageId);
      } catch (dbError) {
        console.warn('⚠️ Failed to save voice message to local database:', dbError);
        // Continue without local storage - not critical
      }

      // Close the voice recorder modal
      setShowVoiceRecorder(false);
      setRecordingDuration(0);
      setRecordingUri(null);
      setHasRecordingToSend(false);

      // Upload to Firebase immediately (not in background)
      try {
        console.log('🔄 Uploading voice message to Firebase...');

        // Check if file exists first
        const FileSystem = await import('expo-file-system');
        const fileInfo = await FileSystem.getInfoAsync(uriToUse);
        console.log('📁 Voice file info:', fileInfo);

        if (!fileInfo.exists) {
          throw new Error(`Voice file does not exist at URI: ${uriToUse}`);
        }

        // Upload audio file to Firebase Storage
        const uploadResult = await realMediaUploadService.uploadAudio(
          actualGroup.id,
          currentUserId,
          uriToUse,
          recordingDuration
        );

        console.log('✅ Voice message uploaded to Firebase Storage:', uploadResult.url);

        // Save to voiceMessages collection
        const voiceMessageId = await voiceMessageService.saveVoiceMessage({
          chatId: actualGroup.id,
          senderId: currentUserId,
          senderName: currentUserName || 'Unknown User',
          audioUrl: uploadResult.url,
          duration: recordingDuration,
          isPlayed: false,
        });

        console.log('✅ Voice message saved to voiceMessages collection:', voiceMessageId);

        // Send to group messages collection and real-time messaging
        try {
          const { realGroupService } = await import('../services/realGroupService');
          const { realTimeMessagingService } = await import('../services/realTimeMessagingService');

          const groupMessage = {
            senderId: currentUserId,
            senderName: currentUserName || 'Unknown User',
            senderAvatar: currentUserAvatar,
            content: '', // No text for voice messages
            type: 'voice',
            mediaUrl: uploadResult.url,
            duration: recordingDuration,
            fileName: `voice_${Date.now()}.m4a`,
            status: 'sent',
          };

          // Send to group messages collection
          await realGroupService.sendMessage(actualGroup.id, groupMessage);
          console.log('✅ Voice message sent to group messages collection');

          // Send via real-time messaging service to ensure delivery to receivers
          const messagingResult = await realTimeMessagingService.sendMessage(
            actualGroup.id,
            currentUserId,
            currentUserName || 'Unknown User',
            '', // No text content for voice messages
            'audio',
            uploadResult.url,
            currentUserAvatar
          );

          if (messagingResult.success) {
            console.log('✅ Voice message sent via real-time messaging service');
          } else {
            console.warn('⚠️ Real-time messaging failed:', messagingResult.error);
          }

        } catch (groupMessageError) {
          console.warn('⚠️ Failed to send voice message to group:', groupMessageError);
        }

        // Update local message with Firebase URL
        setMessages(prev => prev.map(msg =>
          msg.id === voiceMessage.id
            ? { ...msg, mediaUrl: uploadResult.url, status: 'sent' as const }
            : msg
        ));

      } catch (uploadError) {
        console.error('❌ Failed to upload voice message to Firebase:', uploadError);

        // Check if it's a Firebase Storage error
        const error = uploadError as any;
        if (error.code === 'storage/unknown' || error.message?.includes('Firebase Storage')) {
          console.log('🔄 Firebase Storage failed, keeping local file for offline playback...');

          // Update message status to indicate local-only
          setMessages(prev => prev.map(msg =>
            msg.id === voiceMessage.id
              ? { ...msg, status: 'sent' as const } // Keep as sent since it's saved locally
              : msg
          ));

          // Show user-friendly message
          Alert.alert(
            'Voice Message Sent',
            'Your voice message was saved locally. It will be uploaded when connection improves.',
            [{ text: 'OK' }]
          );
        } else {
          // Other errors - mark as failed
          setMessages(prev => prev.map(msg =>
            msg.id === voiceMessage.id
              ? { ...msg, status: 'failed' as const }
              : msg
          ));

          Alert.alert(
            'Upload Failed',
            'Failed to send voice message. Please try again.',
            [{ text: 'OK' }]
          );
        }
      }
    } catch (error) {
      console.error('❌ Error sending voice message:', error);
      Alert.alert('Error', 'Failed to send voice message.');
    }
  };

  const handleGroupCall = (type: 'audio' | 'video') => {
    Alert.alert(
      `Start ${type} call?`,
      `This will notify all ${group?.memberCount || 0} members`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start Call',
          onPress: () => {
            // Start group call
            const callMessage: GroupMessage = {
              id: `call_${Date.now()}`,
              text: `${currentUserName} started a ${type} call`,
              senderId: currentUserId,
              senderName: currentUserName || 'User',
              senderAvatar: currentUserAvatar,
              timestamp: new Date(),
              status: 'sent',
              type: 'call',
            };
            setMessages(prev => [...prev, callMessage]);
            setShowGroupCall(false);
          },
        },
      ]
    );
  };





  // Media handling functionality
  const handleMediaPress = (message: GroupMessage) => {
    console.log('📱 Opening media:', message.type, message.mediaUrl);

    if (!message.mediaUrl) {
      Alert.alert('Error', 'Media file not available');
      return;
    }

    let mediaType: 'image' | 'video' | 'audio' | 'document' = 'document';

    switch (message.type) {
      case 'image':
        mediaType = 'image';
        break;
      case 'video':
        mediaType = 'video';
        break;
      case 'audio':
        mediaType = 'audio';
        break;
      case 'document':
        // Determine file type from extension or MIME type
        const fileName = message.fileName || '';
        const extension = fileName.split('.').pop()?.toLowerCase();

        if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension || '')) {
          mediaType = 'image';
        } else if (['mp4', 'mov', 'avi', 'mkv'].includes(extension || '')) {
          mediaType = 'video';
        } else if (['mp3', 'wav', 'aac', 'm4a'].includes(extension || '')) {
          mediaType = 'audio';
        } else {
          mediaType = 'document';
        }
        break;
      default:
        mediaType = 'document';
    }

    setSelectedMedia({
      url: message.mediaUrl,
      type: mediaType,
      fileName: message.fileName,
      fileSize: message.fileSize,
      senderName: message.senderName,
      timestamp: message.timestamp,
    });

    setShowMediaViewer(true);
  };

  // Translation functionality removed per user request

  const _handleSelectMessage = (_messageId: string) => {
    if (isSelectionMode) {
      setSelectedMessages(prev =>
        prev.includes(_messageId)
          ? prev.filter(id => id !== _messageId)
          : [...prev, _messageId]
      );
    } else {
      setIsSelectionMode(true);
      setSelectedMessages([_messageId]);
    }
  };

  const handleBulkDelete = () => {
    Alert.alert(
      'Delete Messages',
      `Delete ${selectedMessages.length} selected messages?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            setMessages(prev => prev.filter(msg => !selectedMessages.includes(msg.id)));
            setSelectedMessages([]);
            setIsSelectionMode(false);
          },
        },
      ]
    );
  };

  const handleBulkForward = () => {
    setShowForwardModal(true);
  };

  const _handleOpenThread = (_message: GroupMessage) => {
    // Implementation for thread functionality
    console.log('Thread functionality not yet implemented');
  };

  const _handleMention = (_userId: string, _userName: string) => {
    // Implementation for mention functionality
    console.log('Mention functionality not yet implemented');
  };

  const _handleQuickReaction = (_messageId: string, _emoji: string) => {
    // Implementation for quick reaction functionality
    console.log('Quick reaction functionality not yet implemented');
  };

  const _handleShowMessageInfo = (_message: GroupMessage) => {
    // Implementation for message info functionality
    console.log('Message info functionality not yet implemented');
  };

  // ==================== MESSAGE ACTIONS AND REACTIONS ====================

  const handleMessageLongPress = (message: GroupMessage) => {
    console.log('🔗 [DEBUG] Message long pressed:', message.id);

    // Add highlighting effect on long press
    setHighlightedMessageId(message.id);

    // Remove highlight after animation
    setTimeout(() => {
      setHighlightedMessageId(null);
    }, 1500);

    // Only show actions for text messages or own messages (matching individual chat logic)
    if (message.senderId === currentUserId || message.type === "text") {
      setSelectedMessage(message);
      setShowMessageActions(true);
      Vibration.vibrate(50); // Haptic feedback
    }
  };

  const handleMessageSwipeLeft = (message: GroupMessage) => {
    console.log('👈 [DEBUG] Message swiped left (reply):', message.id);

    // Vibrate for haptic feedback
    Vibration.vibrate(30);

    // Set reply to this message
    handleReply(message);
  };

  const handleMessageSwipeRight = (message: GroupMessage, event: any) => {
    console.log('👉 [DEBUG] Message swiped right (react):', message.id);

    // Vibrate for haptic feedback
    Vibration.vibrate(30);

    // Get touch position for emoji picker
    const { pageX, pageY } = event.nativeEvent;

    // Show floating emoji bar
    showEmojiBar(message.id, { x: pageX, y: pageY });
  };

  const handleEmojiReaction = async (messageId: string, emoji: string) => {
    console.log('😀 [DEBUG] Adding emoji reaction:', emoji, 'to message:', messageId);

    // Hide emoji bar
    hideEmojiBar();

    // Add reaction using existing handler
    await addReaction(messageId, emoji);
  };



  const _handleCreateAnnouncement = (_text: string, _priority: 'low' | 'medium' | 'high') => {
    // Implementation for announcement functionality
    console.log('Announcement functionality not yet implemented');
  };

  // Draft management functions
  const _saveDraft = (_text: string) => {
    // Implementation for draft saving functionality
    console.log('Draft saving functionality not yet implemented');
  };

  const _loadDraft = () => {
    // Implementation for draft loading functionality
    console.log('Draft loading functionality not yet implemented');
    return '';
  };

  const _clearDraft = () => {
    // Implementation for draft clearing functionality
    console.log('Draft clearing functionality not yet implemented');
  };

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <Animated.View 
      style={[
        styles.header,
        { 
          paddingTop: insets.top,
          opacity: headerAnim,
        },
      ]}
    >
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            onPress={() => {
              console.log('🔙 Back button pressed');
              onBack?.();
            }}
            style={styles.backButton}
          >
            <Ionicons name="chevron-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.groupInfoButton}
            onPress={() => setShowGroupInfo(true)}
            activeOpacity={0.7}
          >
            <TouchableOpacity
              style={styles.groupAvatarContainer}
              onPress={() => setShowAvatarZoom(true)}
              activeOpacity={0.8}
            >
              <Image
                source={{
                  uri: actualGroup.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(actualGroup.name)}&background=87CEEB&color=fff&size=128`
                }}
                style={styles.groupAvatar}
                defaultSource={{ uri: `https://ui-avatars.com/api/?name=${encodeURIComponent(actualGroup.name)}&background=87CEEB&color=fff&size=128` }}
                onError={() => console.log('Group avatar failed to load')}
              />
            </TouchableOpacity>
            <View style={styles.groupHeaderInfo}>
              <Text style={styles.groupName} numberOfLines={1}>
                {actualGroup.name}
              </Text>
              <Text style={styles.groupStatus}>
                {actualGroup.onlineCount || 0} online • {actualGroup.memberCount || 0} members
              </Text>
            </View>
          </TouchableOpacity>

          <View style={styles.headerActions}>
            {/* Primary Actions - Always Visible */}
            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => {
                console.log('👥 Members list button pressed');
                setShowMembersList(true);
              }}
            >
              <Ionicons name="people" size={18} color={COLORS.text} />
            </TouchableOpacity>

            {/* More Actions Menu */}
            <TouchableOpacity
              style={styles.headerActionButton}
              onPress={() => {
                console.log('⚙️ Settings dropdown button pressed');
                setShowGroupSettings(true);
              }}
            >
              <Ionicons name="ellipsis-vertical" size={18} color={COLORS.text} />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </Animated.View>
  );

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;

    return (
      <Animated.View 
        style={[
          styles.typingContainer,
          {
            opacity: typingAnim,
            transform: [{
              translateY: typingAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [20, 0],
              }),
            }],
          },
        ]}
      >
        <View style={styles.typingBubble}>
          <View style={styles.typingDots}>
            <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
            <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
            <Animated.View style={[styles.typingDot, { opacity: typingAnim }]} />
          </View>
          <Text style={styles.typingText}>
            {typingUsers.length === 1 
              ? `${typingUsers[0].userName} is typing...`
              : `${typingUsers.length} people are typing...`
            }
          </Text>
        </View>
      </Animated.View>
    );
  };

  // Helper function to format date separators
  const formatDateSeparator = (date: Date): string => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const messageDate = new Date(date);

    // Reset time to compare dates only
    today.setHours(0, 0, 0, 0);
    yesterday.setHours(0, 0, 0, 0);
    messageDate.setHours(0, 0, 0, 0);

    if (messageDate.getTime() === today.getTime()) {
      return 'Today';
    } else if (messageDate.getTime() === yesterday.getTime()) {
      return 'Yesterday';
    } else {
      const daysDiff = Math.floor((today.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff < 7) {
        // Show day of week for messages within the last week
        return messageDate.toLocaleDateString('en-US', { weekday: 'long' });
      } else {
        // Show full date for older messages
        return messageDate.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: messageDate.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
        });
      }
    }
  };

  // Check if we need to show a date separator
  const shouldShowDateSeparator = (currentMessage: GroupMessage, previousMessage?: GroupMessage | null): boolean => {
    if (!previousMessage) return true; // Always show for first message

    const currentDate = new Date(currentMessage.timestamp);
    const previousDate = new Date(previousMessage.timestamp);

    // Reset time to compare dates only
    currentDate.setHours(0, 0, 0, 0);
    previousDate.setHours(0, 0, 0, 0);

    return currentDate.getTime() !== previousDate.getTime();
  };

  const renderMessage = ({ item: message, index }: { item: GroupMessage; index: number }) => {
    const isOwn = message.senderId === currentUserId;
    const previousMessage = index > 0 ? messages[index - 1] : null;
    const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
    const showDateSeparator = shouldShowDateSeparator(message, previousMessage);

    const showAvatar = !isOwn && (
      !nextMessage ||
      nextMessage.senderId !== message.senderId ||
      (nextMessage.timestamp.getTime() - message.timestamp.getTime()) > 5 * 60 * 1000
    );

    const showTimestamp = (
      !nextMessage ||
      nextMessage.senderId !== message.senderId ||
      (nextMessage.timestamp.getTime() - message.timestamp.getTime()) > 5 * 60 * 1000
    );

    if (message.isDeleted) {
      return (
        <>
          {/* Date Separator */}
          {showDateSeparator && (
            <View style={styles.dateSeparatorContainer}>
              <View style={styles.dateSeparatorLine} />
              <Text style={styles.dateSeparatorText}>
                {formatDateSeparator(message.timestamp)}
              </Text>
              <View style={styles.dateSeparatorLine} />
            </View>
          )}
          <View style={styles.deletedMessage}>
            <Text style={styles.deletedMessageText}>This message was deleted</Text>
          </View>
        </>
      );
    }

    return (
      <>
        {/* Date Separator */}
        {showDateSeparator && (
          <View style={styles.dateSeparatorContainer}>
            <View style={[styles.dateSeparatorLine, { backgroundColor: COLORS.border }]} />
            <Text style={[styles.dateSeparatorText, { color: COLORS.textSecondary, backgroundColor: COLORS.background }]}>
              {formatDateSeparator(message.timestamp)}
            </Text>
            <View style={[styles.dateSeparatorLine, { backgroundColor: COLORS.border }]} />
          </View>
        )}
        <MessageBubble
          message={message}
          isOwn={isOwn}
          showAvatar={showAvatar}
          showTimestamp={showTimestamp}
          currentUserId={currentUserId}
          isHighlighted={highlightedMessageId === message.id}
          onReply={handleReply}
          onReact={addReaction}
          onEdit={(messageId: string) => {
            const msg = messages.find(m => m.id === messageId);
            if (msg) handleEditMessage(msg);
          }}
          onDelete={handleDelete}
          onForward={handleForward}
          onPin={handlePin}
          onUserPress={handleUserPress}
          onNavigateToMessage={(messageId: string) => {
            // Scroll to the original message and highlight it
            const messageIndex = messages.findIndex(msg => msg.id === messageId);
            if (messageIndex !== -1 && flatListRef.current) {
              setHighlightedMessageId(messageId);
              flatListRef.current.scrollToIndex({
                index: messageIndex,
                animated: true,
                viewPosition: 0.5,
              });
              // Remove highlight after animation
              setTimeout(() => {
                setHighlightedMessageId(null);
              }, 2000);
            }
          }}
          onLongPress={() => handleMessageLongPress(message)}
          onSwipeLeft={handleMessageSwipeLeft}
          onSwipeRight={handleMessageSwipeRight}
          onEmojiReaction={handleEmojiReaction}
          enableSwipeGestures={swipeGestureEnabled}
          swipeState={messageSwipeStates[message.id] || 0}
        />
      </>
    );
  };

  const renderReplyPreview = () => {
    if (!replyingTo) return null;

    const renderReplyMedia = () => {
      if (replyingTo.type === 'text') return null;

      switch (replyingTo.type) {
        case 'image':
          return (
            <Image
              source={{ uri: replyingTo.mediaUrl }}
              style={styles.replyMediaThumbnail}
              resizeMode="cover"
            />
          );
        case 'video':
          return (
            <View style={styles.replyMediaContainer}>
              <Image
                source={{ uri: replyingTo.mediaUrl }}
                style={styles.replyMediaThumbnail}
                resizeMode="cover"
              />
              <View style={styles.replyMediaOverlay}>
                <Ionicons name="play" size={16} color="white" />
              </View>
            </View>
          );
        case 'audio':
          return (
            <View style={styles.replyMediaContainer}>
              <View style={[styles.replyMediaThumbnail, styles.replyAudioThumbnail]}>
                <Ionicons name="musical-notes" size={20} color={COLORS.primary} />
              </View>
            </View>
          );
        case 'document':
          return (
            <View style={styles.replyMediaContainer}>
              <View style={[styles.replyMediaThumbnail, styles.replyFileThumbnail]}>
                <Ionicons name="document" size={20} color={COLORS.primary} />
              </View>
            </View>
          );
        default:
          return null;
      }
    };

    return (
      <Animated.View style={styles.replyPreview}>
        <View style={styles.replyPreviewContent}>
          {renderReplyMedia()}
          <View style={styles.replyPreviewInfo}>
            <Text style={styles.replyPreviewSender}>
              Replying to {replyingTo.senderName}
            </Text>
            <Text style={styles.replyPreviewText} numberOfLines={1}>
              {replyingTo.type === 'text'
                ? replyingTo.text
                : replyingTo.text || `${replyingTo.type.charAt(0).toUpperCase() + replyingTo.type.slice(1)}`
              }
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => {
              setReplyingTo(null);
              Vibration.vibrate(30);
            }}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="close" size={20} color={COLORS.textMuted} />
          </TouchableOpacity>
        </View>
      </Animated.View>
    );
  };

  const renderAttachmentMenu = () => (
    <Modal visible={showAttachments} transparent animationType="slide">
      <TouchableOpacity
        style={styles.attachmentModalBackdrop}
        onPress={() => setShowAttachments(false)}
      >
        <View style={styles.attachmentModal}>
          <Text style={styles.attachmentModalTitle}>Share</Text>

          <View style={styles.attachmentGrid}>
            <TouchableOpacity style={styles.attachmentItem} onPress={handleCameraCapture}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.error }]}>
                <Ionicons name="camera" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Camera</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={handleGalleryPicker}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.success }]}>
                <Ionicons name="images" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Gallery</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={() => {
              setShowAttachments(false);
              console.log('🎤 Voice attachment pressed - starting recording directly...');
              handleStartRecording();
            }}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.primary }]}>
                <Ionicons name="mic" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Voice</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={handleDocumentPicker}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.warning }]}>
                <Ionicons name="document" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Document</Text>
            </TouchableOpacity>

            {/* Location sharing removed to avoid Google Maps API costs */}

            <TouchableOpacity style={styles.attachmentItem} onPress={() => {
              setShowAttachments(false);
              setShowContactPicker(true);
              loadContacts();
            }}>
              <View style={[styles.attachmentIcon, { backgroundColor: COLORS.primaryDark }]}>
                <Ionicons name="person" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Contact</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.attachmentItem} onPress={() => setShowPollCreator(true)}>
              <View style={[styles.attachmentIcon, { backgroundColor: '#9C27B0' }]}>
                <Ionicons name="bar-chart" size={24} color={COLORS.text} />
              </View>
              <Text style={styles.attachmentLabel}>Poll</Text>
            </TouchableOpacity>


          </View>
        </View>
      </TouchableOpacity>
    </Modal>
  );

  const renderVoiceRecorder = () => (
    <Modal visible={showVoiceRecorder} transparent animationType="slide">
      <TouchableOpacity
        style={styles.voiceRecorderBackdropTransparent}
        activeOpacity={1}
        onPress={async () => {
          // Close modal and stop recording when touching outside
          if (recording && isRecording) {
            try {
              await recording.stopAndUnloadAsync();
              console.log('🛑 Recording stopped by touching outside');
            } catch (error) {
              console.error('Error stopping recording on outside touch:', error);
            }
          }

          setShowVoiceRecorder(false);
          setIsRecording(false);
          setRecordingDuration(0);
          setRecording(null);
        }}
      >
        <TouchableOpacity
          style={styles.voiceRecorderModalBottom}
          activeOpacity={1}
          onPress={(e) => e.stopPropagation()}
        >
          <Text style={styles.voiceRecorderTitle}>
            {isRecording ? (isPaused ? 'Recording Paused' : 'Recording...') : 'Voice Message'}
          </Text>

          <View style={styles.voiceRecorderContent}>
            <View style={styles.voiceWaveform}>
              {Array.from({ length: 20 }).map((_, i) => (
                <Animated.View
                  key={i}
                  style={[
                    styles.waveformBar,
                    {
                      height: isRecording ? Math.random() * 40 + 10 : 5,
                      backgroundColor: isRecording ? COLORS.primary : COLORS.textMuted,
                    }
                  ]}
                />
              ))}
            </View>

            <Text style={styles.recordingDuration}>
              {Math.floor(recordingDuration / 60)}:{(recordingDuration % 60).toString().padStart(2, '0')}
            </Text>
          </View>

          <View style={styles.voiceRecorderActions}>
            <TouchableOpacity
              style={styles.voiceRecorderCancel}
              onPress={async () => {
                // Properly stop recording if it's active
                if (recording && isRecording) {
                  try {
                    await recording.stopAndUnloadAsync();
                    console.log('🛑 Recording cancelled and stopped');
                  } catch (error) {
                    console.error('Error stopping recording on cancel:', error);
                  }
                }

                setShowVoiceRecorder(false);
                setIsRecording(false);
                setIsPaused(false);
                setRecordingDuration(0);
                setRecording(null);
                setHasRecordingToSend(false);
              }}
            >
              <Ionicons name="close" size={24} color={COLORS.error} />
            </TouchableOpacity>

            {/* Pause/Resume Button */}
            {isRecording && (
              <TouchableOpacity
                style={[styles.voiceRecorderButton, { backgroundColor: isPaused ? COLORS.success : COLORS.warning }]}
                onPress={async () => {
                  if (recording) {
                    try {
                      if (isPaused) {
                        await recording.startAsync();
                        setIsPaused(false);
                        console.log('▶️ Recording resumed');
                      } else {
                        await recording.pauseAsync();
                        setIsPaused(true);
                        console.log('⏸️ Recording paused');
                      }
                    } catch (error) {
                      console.error('❌ Error pausing/resuming recording:', error);
                    }
                  }
                }}
              >
                <Ionicons
                  name={isPaused ? "play" : "pause"}
                  size={24}
                  color={COLORS.text}
                />
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.voiceRecorderSend}
              onPress={async () => {
                console.log('🔄 Send button pressed', { recordingDuration, isRecording, hasRecordingToSend, recordingUri });

                if (recordingDuration > 1) { // At least 1 second
                  console.log('📤 Sending voice message immediately...');
                  console.log('🔍 Current states before send:', { recordingDuration, isRecording, recordingUri, hasRecordingToSend });

                  let finalUri: string | null = recordingUri;

                  // Get URI directly from recording object if still recording
                  if (isRecording && recording) {
                    console.log('🛑 Getting URI from active recording...');
                    finalUri = recording.getURI();
                    console.log('📍 URI from active recording:', finalUri);

                    // Stop recording
                    try {
                      await recording.stopAndUnloadAsync();
                      console.log('✅ Recording stopped successfully');

                      // Get URI again after stopping (sometimes only available after stop)
                      if (!finalUri) {
                        finalUri = recording.getURI();
                        console.log('📍 URI after stopping:', finalUri);
                      }

                      setIsRecording(false);
                      setRecording(null);
                    } catch (error) {
                      console.error('❌ Failed to stop recording:', error);
                      Alert.alert('Error', 'Failed to stop recording. Please try again.');
                      return;
                    }
                  }

                  // Ensure we have a valid URI
                  if (!finalUri) {
                    console.error('❌ No recording URI available');
                    console.error('🔍 Debug info:', {
                      recordingUri,
                      isRecording,
                      recording: !!recording,
                      recordingDuration,
                      finalUri
                    });
                    Alert.alert('Error', 'No recording found. Please try recording again.');
                    return;
                  }

                  console.log('📤 Sending voice message with URI:', finalUri);

                  // Close modal and send immediately with the captured URI
                  setShowVoiceRecorder(false);

                  // Send the message with the captured URI
                  handleSendVoiceMessage(finalUri);
                } else {
                  console.log('⚠️ Recording too short - need at least 1 second');
                  Alert.alert('Recording Too Short', 'Please record for at least 1 second before sending.');
                }
              }}
              disabled={recordingDuration < 1}
            >
              <Ionicons
                name="send"
                size={24}
                color={recordingDuration > 0 ? COLORS.success : COLORS.textMuted}
              />
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
    </Modal>
  );

  const renderMessageSearch = () => (
    <Modal visible={showMessageSearch} animationType="slide">
      <SafeAreaView style={styles.searchModal}>
        <View style={styles.searchHeader}>
          <TouchableOpacity onPress={() => setShowMessageSearch(false)}>
            <Ionicons name="chevron-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <TextInput
            style={styles.searchInput}
            placeholder="Search messages, media, files, or date (e.g., 'today', '2024-01-15')..."
            placeholderTextColor={COLORS.textMuted}
            value={searchQuery}
            onChangeText={handleMessageSearch}
            autoFocus
          />
        </View>

        {/* Search Type Filters */}
        <View style={styles.searchFilters}>
          {(['all', 'text', 'media', 'files'] as const).map((type) => (
            <TouchableOpacity
              key={type}
              style={[
                styles.searchFilterButton,
                searchType === type && styles.searchFilterButtonActive
              ]}
              onPress={() => {
                setSearchType(type);
                if (searchQuery.trim()) {
                  handleMessageSearch(searchQuery);
                }
              }}
            >
              <Text style={[
                styles.searchFilterText,
                searchType === type && styles.searchFilterTextActive
              ]}>
                {type === 'all' ? 'All' :
                 type === 'text' ? 'Messages' :
                 type === 'media' ? 'Media' :
                 'Files'}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <FlatList
          data={searchResults}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.searchResultItem}
              activeOpacity={0.7}
              onPress={() => {
                // Immediate visual feedback
                console.log('🔍 Search result tapped:', item.id);

                // Close search immediately for faster response
                setShowMessageSearch(false);
                setSearchQuery('');
                setSearchResults([]);

                // Find message index and scroll to it with highlighting
                const messageIndex = messages.findIndex(msg => msg.id === item.id);
                if (messageIndex !== -1) {
                  // Set highlighted message for animation
                  setHighlightedMessageId(item.id);

                  // Scroll to message immediately
                  setTimeout(() => {
                    flatListRef.current?.scrollToIndex({
                      index: messageIndex,
                      animated: true,
                      viewPosition: 0.5, // Center the message
                    });

                    // Remove highlight after animation
                    setTimeout(() => {
                      setHighlightedMessageId(null);
                    }, 2000);
                  }, 100); // Reduced delay for faster response
                }
              }}
            >
              <View style={styles.searchResultContent}>
                <Text style={styles.searchResultSender}>{item.senderName}</Text>
                <Text style={styles.searchResultText} numberOfLines={2}>
                  {item.type === 'text' ? item.text :
                   item.type === 'image' ? '📷 Image' :
                   item.type === 'video' ? '🎥 Video' :
                   item.type === 'document' ? `📄 ${item.fileName || 'Document'}` :
                   item.type === 'voice' ? '🎵 Voice message' : item.text}
                </Text>
                <Text style={styles.searchResultTime}>
                  {item.timestamp.toLocaleDateString()} • {item.timestamp.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: undefined // Fixed: Let device determine 12/24 hour format
                  })}
                </Text>
              </View>
              {item.type !== 'text' && (
                <View style={styles.searchResultTypeIcon}>
                  <Ionicons
                    name={
                      item.type === 'image' ? 'image' :
                      item.type === 'video' ? 'videocam' :
                      item.type === 'document' ? 'document' :
                      item.type === 'voice' ? 'mic' : 'chatbubble'
                    }
                    size={20}
                    color={COLORS.primary}
                  />
                </View>
              )}
            </TouchableOpacity>
          )}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={() => (
            <View style={styles.searchEmptyContainer}>
              <Ionicons name="search" size={64} color={COLORS.textMuted} />
              <Text style={styles.searchEmptyText}>
                {searchQuery ? 'No messages found' : 'Search messages in this group'}
              </Text>
            </View>
          )}
        />
      </SafeAreaView>
    </Modal>
  );

  const renderPinnedMessagesModal = () => (
    <Modal visible={showPinnedMessages} animationType="slide">
      <SafeAreaView style={styles.pinnedModal}>
        <View style={styles.pinnedHeader}>
          <TouchableOpacity onPress={() => setShowPinnedMessages(false)}>
            <Ionicons name="chevron-back" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.pinnedTitle}>Pinned Messages</Text>
          <View style={styles.pinnedCount}>
            <Text style={styles.pinnedCountText}>{pinnedMessages.length}</Text>
          </View>
        </View>

        <FlatList
          data={pinnedMessages}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.pinnedMessageItem}
              onPress={() => {
                // Fixed: Navigate to pinned message in chat
                setShowPinnedMessages(false);
                setHighlightedMessageId(item.id);

                // Find message index and scroll to it
                const messageIndex = messages.findIndex(msg => msg.id === item.id);
                if (messageIndex !== -1) {
                  setTimeout(() => {
                    flatListRef.current?.scrollToIndex({
                      index: messageIndex,
                      animated: true,
                      viewPosition: 0.5,
                    });

                    // Remove highlight after animation
                    setTimeout(() => {
                      setHighlightedMessageId(null);
                    }, 2000);
                  }, 300);
                }
              }}
              activeOpacity={0.7}
            >
              <View style={styles.pinnedMessageHeader}>
                <Image
                  source={{ uri: item.senderAvatar || 'https://via.placeholder.com/32' }}
                  style={styles.pinnedMessageAvatar}
                />
                <View style={styles.pinnedMessageInfo}>
                  <Text style={styles.pinnedMessageSender}>{item.senderName}</Text>
                  <Text style={styles.pinnedMessageTime}>
                    {item.timestamp.toLocaleDateString()}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={(e) => {
                    e.stopPropagation(); // Prevent triggering parent onPress
                    handlePin(item.id);
                  }}
                >
                  <Ionicons name="pin" size={20} color={COLORS.primary} />
                </TouchableOpacity>
              </View>
              <Text style={styles.pinnedMessageText}>{item.text}</Text>
            </TouchableOpacity>
          )}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={() => (
            <View style={styles.pinnedEmptyContainer}>
              <Ionicons name="pin" size={64} color={COLORS.textMuted} />
              <Text style={styles.pinnedEmptyText}>No pinned messages</Text>
            </View>
          )}
        />
      </SafeAreaView>
    </Modal>
  );

  const renderMessageInput = () => (
    <View style={styles.inputContainer}>
      {renderReplyPreview()}

      {/* Selection Mode Bar */}
      {isSelectionMode && (
        <View style={styles.selectionBar}>
          <TouchableOpacity onPress={() => { setIsSelectionMode(false); setSelectedMessages([]); }}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.selectionCount}>{selectedMessages.length} selected</Text>
          <View style={styles.selectionActions}>
            <TouchableOpacity onPress={handleBulkForward} style={styles.selectionAction}>
              <Ionicons name="arrow-forward" size={20} color={COLORS.primary} />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleBulkDelete} style={styles.selectionAction}>
              <Ionicons name="trash" size={20} color={COLORS.error} />
            </TouchableOpacity>
          </View>
        </View>
      )}

      <View style={styles.inputWrapper}>
        <TouchableOpacity
          style={styles.attachButton}
          onPress={() => setShowAttachments(true)}
        >
          <Ionicons name="add" size={24} color={COLORS.primary} />
        </TouchableOpacity>

        <TextInput
          ref={inputRef}
          style={styles.messageInputExpanded} // Fixed: Use expanded input style
          placeholder="Type a message..."
          placeholderTextColor={COLORS.textMuted}
          value={messageText}
          onChangeText={handleTyping}
          multiline
          maxLength={1000}
          keyboardAppearance="dark"
          selectionColor={COLORS.primary}
        />

        {/* Fixed: Voice/Send button toggle */}
        {messageText.trim() ? (
          <TouchableOpacity
            style={styles.sendButton}
            onPress={sendMessage}
          >
            <Ionicons name="send" size={20} color={COLORS.text} />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.voiceButton}
            onPress={() => {
              console.log('🎤 Voice button pressed - starting recording directly...');
              handleStartRecording();
            }}
          >
            <Ionicons name="mic" size={20} color={COLORS.text} />
          </TouchableOpacity>
        )}
      </View>



      {/* Chat Export */}
      <ChatExport
        visible={showChatExport}
        onClose={() => setShowChatExport(false)}
        chatId={group?.id || groupId || ''}
        chatName={group?.name || groupName || 'Group Chat'}
        isGroupChat={true}
        messages={messages} // Fixed: Pass current messages
      />

      {/* Message Search */}
      <MessageSearch
        visible={showMessageSearch}
        onClose={() => setShowMessageSearch(false)}
        chatId={group?.id || groupId || ''}
        onMessageSelect={(messageId: string) => {
          // Scroll to message or highlight it
          console.log('Selected message:', messageId);
        }}
      />
    </View>
  );

  return (
    <MessageSelectionProvider chatId={group?.id || actualGroup.id} currentUserId={currentUserId}>
      <SafeAreaView style={[styles.container, { backgroundColor: COLORS.background }]}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
          enabled={Platform.OS === 'ios'}
        >
        {renderHeader()}

        {/* Pinned Messages Bar */}
        {pinnedMessages.length > 0 && (
          <PinnedMessageBar
            pinnedMessages={pinnedMessages.map(msg => ({
              id: msg.id,
              content: msg.text || '',
              senderName: msg.senderName || 'Unknown',
              timestamp: msg.timestamp || new Date(),
              pinnedBy: msg.pinnedBy || currentUserId,
              pinnedAt: msg.pinnedAt || new Date(),
              senderId: msg.senderId,
              type: msg.type === 'voice' || msg.type === 'call' || msg.type === 'location' || msg.type === 'contact' || msg.type === 'poll' || msg.type === 'announcement' ? 'text' : msg.type,
            }))}
            onPress={(messageId) => {
              // Scroll to the pinned message
              const messageIndex = messages.findIndex(msg => msg.id === messageId);
              if (messageIndex !== -1 && flatListRef.current) {
                setHighlightedMessageId(messageId);
                flatListRef.current.scrollToIndex({
                  index: messageIndex,
                  animated: true,
                  viewPosition: 0.5,
                });
                // Remove highlight after animation
                setTimeout(() => {
                  setHighlightedMessageId(null);
                }, 2000);
              }
            }}
            onUnpin={(messageId) => handlePin(messageId)}
            onNavigateToMessage={(messageId) => {
              // Scroll to the pinned message
              const messageIndex = messages.findIndex(msg => msg.id === messageId);
              if (messageIndex !== -1 && flatListRef.current) {
                setHighlightedMessageId(messageId);
                flatListRef.current.scrollToIndex({
                  index: messageIndex,
                  animated: true,
                  viewPosition: 0.5,
                });
                // Remove highlight after animation
                setTimeout(() => {
                  setHighlightedMessageId(null);
                }, 2000);
              }
            }}
            currentUserId={currentUserId}
            isGroupChat={true}
            groupAdmins={actualGroup.admins || []}
          />
        )}

        {/* Messages List */}
        <View style={styles.messagesWrapper}>
          <FlatList
            ref={flatListRef}
            data={messages}
            renderItem={renderMessage}
            keyExtractor={(item) => item.id}
            style={styles.messagesList}
            contentContainerStyle={styles.messagesContent}
            showsVerticalScrollIndicator={false}
            inverted={false}
            onContentSizeChange={() => {
              flatListRef.current?.scrollToEnd({ animated: true });
            }}
            ListFooterComponent={renderTypingIndicator}
          />
        </View>

        {renderMessageInput()}

      {/* Group Info Modal */}
      <ComprehensiveGroupInfoPage
        visible={showGroupInfo}
        group={actualGroup}
        currentUserId={currentUserId}
        onClose={() => setShowGroupInfo(false)}
        onUserPress={handleUserPress}
        onEditGroup={() => {
          console.log('📝 Edit group functionality');
          // TODO: Implement edit group functionality
        }}
        onLeaveGroup={async () => {
          console.log('🚪 Leave group functionality');
          Alert.alert(
            'Leave Group',
            'Are you sure you want to leave this group? You will no longer receive messages or be able to participate.',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Leave',
                style: 'destructive',
                onPress: async () => {
                  try {
                    if (!groupId) {
                      Alert.alert('Error', 'Group ID not found');
                      return;
                    }

                    console.log('🚪 User leaving group:', groupId);

                    // Remove user from group members
                    const { doc, updateDoc, arrayRemove } = await import('firebase/firestore');
                    const { db } = await import('../services/firebaseSimple');

                    const groupRef = doc(db, 'groups', groupId);
                    await updateDoc(groupRef, {
                      members: arrayRemove(currentUserId),
                      memberCount: actualGroup.memberCount - 1,
                      onlineCount: Math.max(0, actualGroup.onlineCount - 1),
                      lastActivity: new Date(),
                    });

                    // Add leave activity log
                    const { collection, addDoc } = await import('firebase/firestore');
                    const activityRef = collection(db, 'groups', groupId, 'activity');
                    await addDoc(activityRef, {
                      type: 'member_left',
                      userId: currentUserId,
                      userName: currentUserName || 'Unknown User',
                      timestamp: new Date(),
                      description: `${currentUserName || 'A member'} left the group`,
                    });

                    console.log('✅ Successfully left group');
                    Alert.alert('Left Group', 'You have successfully left the group.', [
                      {
                        text: 'OK',
                        onPress: () => {
                          // Navigate back to previous screen
                          onBack?.();
                        }
                      }
                    ]);

                  } catch (error) {
                    console.error('❌ Error leaving group:', error);
                    Alert.alert('Error', 'Failed to leave group. Please try again.');
                  }
                }
              }
            ]
          );
        }}
        onDeleteGroup={() => {
          console.log('🗑️ Delete group functionality');
          Alert.alert(
            'Delete Group',
            'Are you sure you want to delete this group? This action cannot be undone.',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Delete',
                style: 'destructive',
                onPress: () => {
                  // TODO: Implement delete group functionality
                  console.log('Group deleted');
                }
              }
            ]
          );
        }}
        onManageMembers={() => {
          console.log('👥 Manage members functionality');
          // TODO: Implement manage members functionality
        }}
        onGroupSettings={() => {
          console.log('⚙️ Group settings functionality');
          // TODO: Implement group settings modal
          Alert.alert('Group Settings', 'Group settings functionality will be implemented soon.');
        }}
      />

      {/* Most Active Member Modal */}
      <MostActiveMemberSystem
        groupId={group?.id || groupId || ''}
        currentUserId={currentUserId}
        visible={showMostActive}
        onClose={() => setShowMostActive(false)}
        onUserPress={handleUserPress}
      />

      {/* ALL MISSING MODALS AND INTERACTIONS */}
      {renderAttachmentMenu()}
      {renderVoiceRecorder()}
      {renderMessageSearch()}
      {renderPinnedMessagesModal()}

      {/* Group Settings Dropdown Menu */}
      {showGroupSettings && (
        <TouchableOpacity
          style={styles.dropdownBackdrop}
          activeOpacity={1}
          onPress={() => setShowGroupSettings(false)}
        >
          <View style={styles.dropdownMenu}>
            {/* Search Messages */}
            <TouchableOpacity
              style={styles.dropdownOption}
              onPress={() => {
                setShowGroupSettings(false);
                setShowMessageSearch(true);
              }}
            >
              <Ionicons name="search" size={18} color={COLORS.primary} />
              <Text style={styles.dropdownOptionText}>Search Messages</Text>
            </TouchableOpacity>

            {/* Pinned Messages */}
            <TouchableOpacity
              style={styles.dropdownOption}
              onPress={() => {
                setShowGroupSettings(false);
                setShowPinnedMessages(true);
              }}
            >
              <Ionicons name="pin" size={18} color={COLORS.primary} />
              <Text style={styles.dropdownOptionText}>Pinned Messages</Text>
              {pinnedMessages.length > 0 && (
                <View style={styles.dropdownBadge}>
                  <Text style={styles.dropdownBadgeText}>{pinnedMessages.length}</Text>
                </View>
              )}
            </TouchableOpacity>

            {/* Most Active Members */}
            <TouchableOpacity
              style={styles.dropdownOption}
              onPress={() => {
                setShowGroupSettings(false);
                setShowMostActive(true);
              }}
            >
              <Ionicons name="trophy" size={18} color={COLORS.primary} />
              <Text style={styles.dropdownOptionText}>Most Active</Text>
            </TouchableOpacity>

            {/* Invite Members */}
            <TouchableOpacity
              style={styles.dropdownOption}
              onPress={() => {
                setShowGroupSettings(false);
                setShowGroupInvite(true);
              }}
            >
              <Ionicons name="person-add" size={18} color={COLORS.primary} />
              <Text style={styles.dropdownOptionText}>Invite Members</Text>
            </TouchableOpacity>





            {/* Export Chat */}
            <TouchableOpacity
              style={styles.dropdownOption}
              onPress={() => {
                setShowGroupSettings(false);
                setShowChatExport(true);
              }}
            >
              <Ionicons name="download" size={18} color={COLORS.primary} />
              <Text style={styles.dropdownOptionText}>Export Chat</Text>
            </TouchableOpacity>

            {/* Clear Chat */}
            <TouchableOpacity
              style={styles.dropdownOption}
              onPress={async () => {
                setShowGroupSettings(false);
                const result = await chatClearService.clearChatMessages(
                  group?.id || groupId || '',
                  currentUserId,
                  { clearMessages: true, clearMedia: true, clearAll: false }
                );
                if (result.success) {
                  setMessages([]);
                }
              }}
            >
              <Ionicons name="trash-outline" size={18} color={COLORS.error || '#FF6B6B'} />
              <Text style={[styles.dropdownOptionText, { color: COLORS.error || '#FF6B6B' }]}>Clear Chat</Text>
            </TouchableOpacity>



            {/* Group Info */}
            <TouchableOpacity
              style={styles.dropdownOption}
              onPress={() => {
                setShowGroupSettings(false);
                setShowGroupInfo(true);
              }}
            >
              <Ionicons name="information-circle" size={18} color={COLORS.primary} />
              <Text style={styles.dropdownOptionText}>Group Info</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      )}

      {/* Avatar Zoom Modal */}
      <Modal visible={showAvatarZoom} transparent animationType="fade">
        <View style={styles.avatarZoomBackdrop}>
          <TouchableOpacity
            style={styles.avatarZoomClose}
            onPress={() => setShowAvatarZoom(false)}
            activeOpacity={0.7}
          >
            <Ionicons name="close" size={30} color="white" />
          </TouchableOpacity>
          <View style={styles.avatarZoomContainer}>
            <Image
              source={{
                uri: group?.avatar || groupAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(group?.name || groupName || 'Group')}&background=87CEEB&color=fff&size=512`
              }}
              style={styles.avatarZoomImage}
              resizeMode="contain"
            />
            <Text style={styles.avatarZoomTitle}>
              {group?.name || groupName || 'Group Chat'}
            </Text>
          </View>
        </View>
      </Modal>

      {/* Members List Modal */}
      <Modal visible={showMembersList} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Group Members</Text>
            <TouchableOpacity onPress={() => setShowMembersList(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Announcement Creator Modal */}
      <Modal visible={showAnnouncementCreator} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Create Announcement</Text>
            <TouchableOpacity onPress={() => setShowAnnouncementCreator(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Slow Mode Timer Display */}
      {showSlowModeTimer && (
        <View style={styles.slowModeIndicator}>
          <Text style={styles.slowModeText}>Slow mode: {slowModeTimeLeft}s</Text>
        </View>
      )}

      {/* Typing Members Display */}
      {showTypingMembers && (
        <View style={styles.typingMembersIndicator}>
          <Text style={styles.typingMembersText}>Members typing...</Text>
        </View>
      )}

      {/* Online Members Display */}
      {showOnlineMembers && (
        <View style={styles.onlineMembersIndicator}>
          <Text style={styles.onlineMembersText}>Online members</Text>
        </View>
      )}

      {/* Message Reactions Modal */}
      <Modal visible={showMessageReactions} transparent animationType="fade">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Message Reactions</Text>
            <TouchableOpacity onPress={() => setShowMessageReactions(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Group Invite Modal */}
      <Modal visible={showGroupInvite} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Invite to Group</Text>
            <TouchableOpacity onPress={() => setShowGroupInvite(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Group QR Code Modal */}
      <Modal visible={showGroupQRCode} transparent animationType="slide">
        <View style={styles.modalBackdrop}>
          <View style={styles.modal}>
            <Text style={styles.modalTitle}>Group QR Code</Text>
            <TouchableOpacity onPress={() => setShowGroupQRCode(false)}>
              <Text style={styles.modalClose}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Group Call Modal */}
      <Modal visible={showGroupCall} transparent animationType="fade">
        <TouchableOpacity
          style={styles.callModalBackdrop}
          onPress={() => setShowGroupCall(false)}
        >
          <View style={styles.callModal}>
            <Text style={styles.callModalTitle}>Start Group Call</Text>
            <View style={styles.callOptions}>
              <TouchableOpacity
                style={styles.callOption}
                onPress={() => handleGroupCall('audio')}
              >
                <Ionicons name="call" size={32} color={COLORS.success} />
                <Text style={styles.callOptionText}>Audio Call</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.callOption}
                onPress={() => handleGroupCall('video')}
              >
                <Ionicons name="videocam" size={32} color={COLORS.primary} />
                <Text style={styles.callOptionText}>Video Call</Text>
              </TouchableOpacity>
            </View>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Poll Creator Modal */}
      <Modal visible={showPollCreator} animationType="slide">
        <SafeAreaView style={styles.pollModal}>
          <View style={styles.pollHeader}>
            <TouchableOpacity onPress={() => setShowPollCreator(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.pollTitle}>Create Poll</Text>
            <TouchableOpacity onPress={() => handleCreatePoll(pollQuestion, pollOptions)}>
              <Text style={styles.pollCreate}>Create</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.pollContent}>
            <TextInput
              style={styles.pollQuestionInput}
              placeholder="Enter your poll question..."
              value={pollQuestion}
              onChangeText={setPollQuestion}
              multiline
            />
            {pollOptions.map((option, index) => (
              <View key={index} style={styles.pollOptionRow}>
                <TextInput
                  style={styles.pollOptionInput}
                  placeholder={`Option ${index + 1}`}
                  value={option}
                  onChangeText={(text) => {
                    const newOptions = [...pollOptions];
                    newOptions[index] = text;
                    setPollOptions(newOptions);
                  }}
                />
                {pollOptions.length > 2 && (
                  <TouchableOpacity
                    onPress={() => {
                      const newOptions = pollOptions.filter((_, i) => i !== index);
                      setPollOptions(newOptions);
                    }}
                    style={styles.removeOptionButton}
                  >
                    <Ionicons name="close" size={20} color="#ff4444" />
                  </TouchableOpacity>
                )}
              </View>
            ))}
            <TouchableOpacity
              onPress={() => setPollOptions([...pollOptions, ''])}
              style={styles.addOptionButton}
            >
              <Text style={styles.addOptionText}>+ Add Option</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Location sharing functionality removed to avoid Google Maps API costs */}

      {/* Contact Picker Modal */}
      <Modal visible={showContactPicker} animationType="slide">
        <SafeAreaView style={styles.contactModal}>
          <View style={styles.contactHeader}>
            <TouchableOpacity onPress={() => setShowContactPicker(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.contactTitle}>Share Contact</Text>
          </View>
          <View style={styles.contactContent}>
            <FlatList
              data={contacts}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.contactItem}
                  onPress={() => handleShareContact(item)}
                >
                  <View style={styles.contactAvatar}>
                    {item.photoURL ? (
                      <Image source={{ uri: item.photoURL }} style={styles.contactAvatarImage} />
                    ) : (
                      <Ionicons name="person" size={24} color="#87CEEB" />
                    )}
                  </View>
                  <View style={styles.contactInfo}>
                    <Text style={styles.contactName}>{item.displayName || item.phoneNumber}</Text>
                    <Text style={styles.contactPhone}>{item.phoneNumber}</Text>
                  </View>
                </TouchableOpacity>
              )}
              style={styles.contactList}
            />
          </View>
        </SafeAreaView>
      </Modal>

      {/* Emoji Picker Modal */}
      <Modal visible={showEmojiPicker} transparent animationType="fade">
        <TouchableOpacity
          style={styles.emojiModalBackdrop}
          onPress={() => setShowEmojiPicker(false)}
        >
          <View style={styles.emojiModal}>
            <Text style={styles.emojiModalTitle}>Choose Emoji</Text>
            <View style={styles.emojiGrid}>
              {['😀', '😂', '😍', '🥰', '😎', '🤔', '😢', '😡', '👍', '👎', '❤️', '🔥', '💯', '🎉', '👏', '🙏'].map((emoji) => (
                <TouchableOpacity
                  key={emoji}
                  style={styles.emojiOption}
                  onPress={() => {
                    setMessageText(prev => prev + emoji);
                    setShowEmojiPicker(false);
                  }}
                >
                  <Text style={styles.emojiOptionText}>{emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Message Scheduler Modal */}
      <Modal visible={showMessageScheduler} animationType="slide">
        <SafeAreaView style={styles.schedulerModal}>
          <View style={styles.schedulerHeader}>
            <TouchableOpacity onPress={() => setShowMessageScheduler(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.schedulerTitle}>Schedule Message</Text>
          </View>
          <View style={styles.schedulerContent}>
            <TextInput
              style={styles.schedulerMessageInput}
              placeholder="Enter message to schedule..."
              value={scheduledMessageText}
              onChangeText={setScheduledMessageText}
              multiline
            />
            <TouchableOpacity
              style={styles.dateTimeButton}
              onPress={() => setShowDatePicker(true)}
            >
              <Ionicons name="calendar" size={20} color="#87CEEB" />
              <Text style={styles.dateTimeText}>
                {scheduledDate ? scheduledDate.toLocaleDateString() : 'Select Date'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.dateTimeButton}
              onPress={() => setShowTimePicker(true)}
            >
              <Ionicons name="time" size={20} color="#87CEEB" />
              <Text style={styles.dateTimeText}>
                {scheduledTime ? scheduledTime.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: undefined // Fixed: Let device determine 12/24 hour format
                }) : 'Select Time'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.scheduleButton}
              onPress={handleScheduleMessage}
              disabled={!scheduledMessageText || !scheduledDate || !scheduledTime}
            >
              <Text style={styles.scheduleButtonText}>Schedule Message</Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Forward Modal */}
      <Modal visible={showForwardModal} animationType="slide">
        <SafeAreaView style={styles.forwardModal}>
          <View style={styles.forwardHeader}>
            <TouchableOpacity onPress={() => setShowForwardModal(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.forwardTitle}>Forward Messages</Text>
          </View>
          <View style={styles.forwardContent}>
            <FlatList
              data={availableChats}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.forwardChatItem,
                    selectedForwardChats.includes(item.id) && styles.forwardChatSelected
                  ]}
                  onPress={() => toggleForwardChat(item.id)}
                >
                  <View style={styles.forwardChatAvatar}>
                    {item.photoURL ? (
                      <Image source={{ uri: item.photoURL }} style={styles.forwardChatAvatarImage} />
                    ) : (
                      <Ionicons name={item.type === 'group' ? 'people' : 'person'} size={24} color="#87CEEB" />
                    )}
                  </View>
                  <View style={styles.forwardChatInfo}>
                    <Text style={styles.forwardChatName}>{item.name}</Text>
                    <Text style={styles.forwardChatType}>{item.type === 'group' ? 'Group' : 'Individual'}</Text>
                  </View>
                  {selectedForwardChats.includes(item.id) && (
                    <Ionicons name="checkmark-circle" size={24} color="#87CEEB" />
                  )}
                </TouchableOpacity>
              )}
              style={styles.forwardChatList}
            />
            <TouchableOpacity
              style={styles.forwardButton}
              onPress={handleForwardMessages}
              disabled={selectedForwardChats.length === 0}
            >
              <Text style={styles.forwardButtonText}>
                Forward to {selectedForwardChats.length} chat{selectedForwardChats.length !== 1 ? 's' : ''}
              </Text>
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Thread View Modal */}
      <Modal visible={showThreadView} animationType="slide">
        <SafeAreaView style={styles.threadModal}>
          <View style={styles.threadHeader}>
            <TouchableOpacity onPress={() => setShowThreadView(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.threadTitle}>Thread</Text>
          </View>
          <View style={styles.threadContent}>
            {selectedThread && (
              <>
                <View style={styles.originalMessage}>
                  <Text style={styles.originalMessageLabel}>Original Message:</Text>
                  <Text style={styles.originalMessageText}>{selectedThread.text}</Text>
                  <Text style={styles.originalMessageAuthor}>
                    by {selectedThread.senderName} • {formatTime(selectedThread.timestamp)}
                  </Text>
                </View>
                <FlatList
                  data={threadMessages}
                  keyExtractor={(item) => item.id}
                  renderItem={({ item }) => (
                    <View style={styles.threadMessage}>
                      <View style={styles.threadMessageHeader}>
                        <Text style={styles.threadMessageAuthor}>{item.senderName}</Text>
                        <Text style={styles.threadMessageTime}>{formatTime(item.timestamp)}</Text>
                      </View>
                      <Text style={styles.threadMessageText}>{item.text}</Text>
                    </View>
                  )}
                  style={styles.threadMessagesList}
                />
                <View style={styles.threadReplyInput}>
                  <TextInput
                    style={styles.threadReplyTextInput}
                    placeholder="Reply to thread..."
                    value={threadReplyText}
                    onChangeText={setThreadReplyText}
                    multiline
                  />
                  <TouchableOpacity
                    style={styles.threadReplySendButton}
                    onPress={handleSendThreadReply}
                    disabled={!threadReplyText.trim()}
                  >
                    <Ionicons name="send" size={20} color="white" />
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </SafeAreaView>
      </Modal>

      {/* Message Info Modal */}
      <Modal visible={showMessageInfo} animationType="slide">
        <SafeAreaView style={styles.messageInfoModal}>
          <View style={styles.messageInfoHeader}>
            <TouchableOpacity onPress={() => setShowMessageInfo(false)}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
            <Text style={styles.messageInfoTitle}>Message Info</Text>
          </View>
          <View style={styles.messageInfoContent}>
            {selectedMessageInfo && (
              <>
                <View style={styles.messageInfoSection}>
                  <Text style={styles.messageInfoLabel}>Message:</Text>
                  <Text style={styles.messageInfoText}>{selectedMessageInfo.text}</Text>
                </View>
                <View style={styles.messageInfoSection}>
                  <Text style={styles.messageInfoLabel}>Sent by:</Text>
                  <Text style={styles.messageInfoText}>{selectedMessageInfo.senderName}</Text>
                </View>
                <View style={styles.messageInfoSection}>
                  <Text style={styles.messageInfoLabel}>Sent at:</Text>
                  <Text style={styles.messageInfoText}>{formatTime(selectedMessageInfo.timestamp)}</Text>
                </View>
                {selectedMessageInfo.editedAt && (
                  <View style={styles.messageInfoSection}>
                    <Text style={styles.messageInfoLabel}>Edited at:</Text>
                    <Text style={styles.messageInfoText}>{formatTime(selectedMessageInfo.editedAt)}</Text>
                  </View>
                )}
                <View style={styles.messageInfoSection}>
                  <Text style={styles.messageInfoLabel}>Read by:</Text>
                  {selectedMessageInfo.readBy && selectedMessageInfo.readBy.length > 0 ? (
                    selectedMessageInfo.readBy.map((reader: any) => (
                      <Text key={reader.userId} style={styles.messageInfoReadBy}>
                        {reader.userName} • {formatTime(reader.readAt)}
                      </Text>
                    ))
                  ) : (
                    <Text style={styles.messageInfoText}>Not read yet</Text>
                  )}
                </View>
              </>
            )}
          </View>
        </SafeAreaView>
      </Modal>

      {/* Message Actions Modal (matching individual chat exactly) */}
      <Modal
        visible={showMessageActions}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowMessageActions(false)}
      >
        <Pressable
          style={{
            flex: 1,
            backgroundColor: "rgba(0,0,0,0.5)",
            justifyContent: "center",
            alignItems: "center",
          }}
          onPress={() => setShowMessageActions(false)}
        >
          <View
            style={{
              backgroundColor: "#2D2D2D", // Dark grey background
              borderRadius: 12,
              padding: 20,
              margin: 20,
              minWidth: 250,
            }}
          >
            {/* Reactions */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-around",
                marginBottom: 20,
                paddingVertical: 10,
              }}
            >
              {commonEmojis.map((emoji) => (
                <TouchableOpacity
                  key={emoji}
                  onPress={() => {
                    selectedMessage && addReaction(selectedMessage.id, emoji);
                    setShowMessageActions(false); // Fixed: Close modal after reaction
                  }}
                  style={{
                    padding: 8,
                    borderRadius: 20,
                    backgroundColor: "#f8f9fa",
                  }}
                >
                  <Text style={{ fontSize: 20 }}>{emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Action Buttons */}
            <View style={{ gap: 10 }}>
              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  padding: 12,
                  borderRadius: 8,
                  backgroundColor: "#3a3a3a",
                }}
                onPress={() => {
                  selectedMessage && handleReply(selectedMessage);
                  setShowMessageActions(false); // Fixed: Close modal after action
                }}
              >
                <Ionicons name="arrow-undo" size={20} color="#fff" />
                <Text style={{ color: "#fff", marginLeft: 10, fontSize: 16 }}>
                  Reply
                </Text>
              </TouchableOpacity>

              {selectedMessage?.senderId === currentUserId && (
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    padding: 12,
                    borderRadius: 8,
                    backgroundColor: "#3a3a3a",
                  }}
                  onPress={() => {
                    selectedMessage && handleEditMessage(selectedMessage);
                    setShowMessageActions(false); // Fixed: Close modal after action
                  }}
                >
                  <Ionicons name="create" size={20} color="#fff" />
                  <Text style={{ color: "#fff", marginLeft: 10, fontSize: 16 }}>
                    Edit
                  </Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  padding: 12,
                  borderRadius: 8,
                  backgroundColor: "#3a3a3a",
                }}
                onPress={() => {
                  selectedMessage && handleForward(selectedMessage.id);
                  setShowMessageActions(false); // Fixed: Close modal after action
                }}
              >
                <Ionicons name="arrow-forward" size={20} color="#fff" />
                <Text style={{ color: "#fff", marginLeft: 10, fontSize: 16 }}>
                  Forward
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  padding: 12,
                  borderRadius: 8,
                  backgroundColor: "#3a3a3a",
                }}
                onPress={() => {
                  selectedMessage && handlePin(selectedMessage.id);
                  setShowMessageActions(false); // Fixed: Close modal after action
                }}
              >
                <Ionicons name="pin" size={20} color="#fff" />
                <Text style={{ color: "#fff", marginLeft: 10, fontSize: 16 }}>
                  {selectedMessage?.isPinned ? "Unpin" : "Pin"}
                </Text>
              </TouchableOpacity>

              {selectedMessage?.senderId === currentUserId && (
                <TouchableOpacity
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    padding: 12,
                    borderRadius: 8,
                    backgroundColor: "#d32f2f",
                  }}
                  onPress={() => {
                    selectedMessage && handleDelete(selectedMessage.id);
                    setShowMessageActions(false); // Fixed: Close modal after action
                  }}
                >
                  <Ionicons name="trash" size={20} color="#fff" />
                  <Text style={{ color: "#fff", marginLeft: 10, fontSize: 16 }}>
                    Delete
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </Pressable>
      </Modal>

      {/* Floating Emoji Bar */}
      {emojiBarState.visible && emojiBarState.messageId && (
        <FloatingEmojiBar
          messageId={emojiBarState.messageId}
          visible={emojiBarState.visible}
          position={emojiBarState.position}
          onEmojiSelect={(emoji, messageId) => {
            handleEmojiReaction(messageId, emoji);
            hideEmojiBar();
          }}
        />
      )}

      {/* Media Viewer Modal */}
      {showMediaViewer && selectedMedia && (
        <MediaViewer
          onClose={() => {
            setShowMediaViewer(false);
            setSelectedMedia(null);
          }}
          media={[{
            url: selectedMedia.url,
            type: selectedMedia.type === 'document' || selectedMedia.type === 'audio' ? 'file' : selectedMedia.type as 'image' | 'video' | 'file',
            caption: selectedMedia.fileName,
            fileType: selectedMedia.type,
            fileSize: selectedMedia.fileSize ? `${(selectedMedia.fileSize / 1024).toFixed(1)} KB` : undefined,
          }]}
          initialIndex={0}
        />
      )}
      </KeyboardAvoidingView>
    </SafeAreaView>
    </MessageSelectionProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  headerGradient: {
    paddingBottom: 8, // Reduced from 12
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12, // Reduced from 16
    paddingTop: 8, // Reduced from 12
    paddingBottom: 4, // Added to balance
    gap: 8, // Reduced from 12
    minHeight: 56, // Set minimum height for consistency
  },
  backButton: {
    padding: 6, // Reduced padding
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: 32,
    minHeight: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  groupInfoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8, // Reduced gap
    paddingVertical: 4, // Add some vertical padding for better touch target
  },
  groupAvatarContainer: {
    position: 'relative',
  },
  groupAvatar: {
    width: 36, // Fixed size instead of responsive
    height: 36,
    borderRadius: 18,
    borderWidth: 1.5, // Thinner border
    borderColor: COLORS.text,
  },
  groupHeaderInfo: {
    flex: 1,
    marginLeft: 4, // Small margin for better spacing
  },
  groupName: {
    fontSize: 16, // Fixed size for consistency
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2, // Reduced margin
    lineHeight: 18, // Better line height
  },
  groupStatus: {
    fontSize: 12, // Smaller status text
    color: '#E5E7EB', // More visible light gray instead of textSecondary
    lineHeight: 14,
    fontWeight: '600', // Make it bolder
  },
  headerActions: {
    flexDirection: 'row',
    gap: 4, // Reduced gap
    alignItems: 'center',
  },
  headerActionButton: {
    position: 'relative',
    padding: 8, // Fixed padding instead of SPACING.sm
    borderRadius: 16, // Smaller radius
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    minWidth: 32, // Ensure minimum touch target
    minHeight: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  pinnedBadge: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: COLORS.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pinnedBadgeText: {
    fontSize: 10,
    color: COLORS.text,
    fontWeight: '700',
  },
  messagesWrapper: {
    flex: 1,
    position: 'relative',
  },

  messagesList: {
    flex: 1,
    paddingHorizontal: ResponsiveSpacing.sm,
    backgroundColor: 'transparent',
  },
  messagesContent: {
    paddingVertical: ResponsiveSpacing.md,
  },
  dateSeparatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 16,
    paddingHorizontal: 20,
  },
  dateSeparatorLine: {
    flex: 1,
    height: 1,
  },
  dateSeparatorText: {
    fontSize: 12,
    fontWeight: '500',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  deletedMessage: {
    alignItems: 'center',
    paddingVertical: 8,
    marginVertical: 4,
  },
  deletedMessageText: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: COLORS.textMuted,
    fontStyle: 'italic',
  },
  typingContainer: {
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    minHeight: ComponentSizes.buttonHeight.small,
  },
  typingBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 8,
    alignSelf: 'flex-start',
    gap: 8,
  },
  typingDots: {
    flexDirection: 'row',
    gap: 4,
  },
  typingDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: COLORS.primary,
  },
  typingText: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  inputContainer: {
    backgroundColor: COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
    paddingBottom: Platform.OS === 'ios' ? 0 : 10,
  },
  replyPreview: {
    backgroundColor: COLORS.surfaceLight,
    borderTopWidth: 1,
    borderTopColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  replyPreviewContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  replyPreviewInfo: {
    flex: 1,
  },
  replyPreviewSender: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '600',
    marginBottom: 2,
  },
  replyPreviewText: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  replyMediaContainer: {
    position: 'relative',
    marginRight: 12,
  },
  replyMediaThumbnail: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: COLORS.surfaceLight,
  },
  replyMediaOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 8,
  },
  replyAudioThumbnail: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary + '20',
  },
  replyFileThumbnail: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary + '20',
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  attachButton: {
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageInput: {
    flex: 1,
    backgroundColor: COLORS.inputBackground,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    color: COLORS.text,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  messageInputExpanded: {
    flex: 1,
    backgroundColor: COLORS.inputBackground,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    color: COLORS.text,
    maxHeight: 100,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
    marginRight: 8, // Fixed: Add margin for spacing from voice/send button
  },
  voiceButton: {
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },

  // ==================== MISSING STYLES FOR ALL NEW COMPONENTS ====================

  // Selection Mode Styles
  selectionBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  selectionCount: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  selectionActions: {
    flexDirection: 'row',
    gap: 16,
  },
  selectionAction: {
    padding: 8,
  },

  // Attachment Menu Styles
  attachmentModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  attachmentModal: {
    backgroundColor: COLORS.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  attachmentModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 20,
  },
  attachmentGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    gap: 16,
  },
  attachmentItem: {
    alignItems: 'center',
    width: '22%',
  },
  attachmentIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  attachmentLabel: {
    fontSize: 12,
    color: COLORS.text,
    textAlign: 'center',
  },

  // Voice Recorder Styles
  voiceRecorderBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceRecorderBackdropTransparent: {
    flex: 1,
    justifyContent: 'flex-end', // Fixed: Position at bottom
    backgroundColor: 'rgba(0,0,0,0.3)', // Fixed: More transparent to see messages
  },
  voiceRecorderModal: {
    backgroundColor: COLORS.surface,
    borderRadius: 20,
    padding: 24,
    width: '80%',
    alignItems: 'center',
  },
  voiceRecorderModalBottom: {
    backgroundColor: COLORS.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    width: '100%',
    alignItems: 'center',
    maxHeight: '40%', // Fixed: Limit height to allow seeing messages above
  },
  voiceRecorderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 20,
  },
  voiceRecorderContent: {
    alignItems: 'center',
    marginBottom: 24,
  },
  voiceWaveform: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3,
    height: 50,
    marginBottom: 16,
  },
  waveformBar: {
    width: 4,
    backgroundColor: COLORS.primary,
    borderRadius: 2,
  },
  recordingDuration: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '600',
  },
  voiceRecorderActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 24,
  },
  voiceRecorderCancel: {
    backgroundColor: COLORS.error + '20',
    borderRadius: 24,
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceRecorderButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 32,
    width: 64,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceRecorderButtonActive: {
    backgroundColor: COLORS.error,
  },
  voiceRecorderSend: {
    backgroundColor: COLORS.success + '20',
    borderRadius: 24,
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Search Modal Styles
  searchModal: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  searchHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    backgroundColor: COLORS.inputBackground,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 16,
    color: COLORS.text,
  },
  searchResultItem: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
    alignItems: 'center',
  },
  searchResultContent: {
    flex: 1,
  },
  searchResultSender: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 4,
  },
  searchResultText: {
    fontSize: 16,
    color: COLORS.text,
    marginBottom: 4,
  },
  searchResultTime: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  searchResultTypeIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.surfaceLight,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  searchFilters: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  searchFilterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: COLORS.surfaceLight,
  },
  searchFilterButtonActive: {
    backgroundColor: COLORS.primary,
  },
  searchFilterText: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
  },
  searchFilterTextActive: {
    color: COLORS.text,
    fontWeight: '600',
  },
  searchEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  searchEmptyText: {
    fontSize: 16,
    color: COLORS.textMuted,
    textAlign: 'center',
    marginTop: 16,
  },

  // Pinned Messages Styles
  pinnedModal: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  pinnedHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  pinnedTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  pinnedCount: {
    backgroundColor: COLORS.primary,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  pinnedCountText: {
    fontSize: 12,
    color: COLORS.background,
    fontWeight: '600',
  },
  pinnedMessageItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  pinnedMessageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  pinnedMessageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
  pinnedMessageInfo: {
    flex: 1,
  },
  pinnedMessageSender: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.primary,
  },
  pinnedMessageTime: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  pinnedMessageText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 20,
  },
  pinnedEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  pinnedEmptyText: {
    fontSize: 16,
    color: COLORS.textMuted,
    textAlign: 'center',
    marginTop: 16,
  },

  // Call Modal Styles
  callModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  callModal: {
    backgroundColor: COLORS.surface,
    borderRadius: 20,
    padding: 24,
    width: '80%',
    alignItems: 'center',
  },
  callModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 24,
  },
  callOptions: {
    flexDirection: 'row',
    gap: 24,
  },
  callOption: {
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    backgroundColor: COLORS.surfaceLight,
    minWidth: 100,
  },
  callOptionText: {
    fontSize: 14,
    color: COLORS.text,
    marginTop: 8,
    fontWeight: '500',
  },

  // Generic Modal Styles
  pollModal: { flex: 1, backgroundColor: COLORS.background },
  pollHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  pollTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  pollCreate: { fontSize: 16, color: COLORS.primary, fontWeight: '600' },
  pollContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  pollComingSoon: { fontSize: 16, color: COLORS.textMuted },

  locationModal: { flex: 1, backgroundColor: COLORS.background },
  locationHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  locationTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  locationShare: { fontSize: 16, color: COLORS.primary, fontWeight: '600' },
  locationContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  locationComingSoon: { fontSize: 16, color: COLORS.textMuted },

  contactModal: { flex: 1, backgroundColor: COLORS.background },
  contactHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  contactTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  contactContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  contactComingSoon: { fontSize: 16, color: COLORS.textMuted },

  // Emoji Modal Styles
  emojiModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emojiModal: {
    backgroundColor: COLORS.surface,
    borderRadius: 20,
    padding: 20,
    width: '90%',
    maxHeight: '60%',
  },
  emojiModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 16,
  },
  emojiGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    gap: 8,
  },
  emojiOption: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 24,
    backgroundColor: COLORS.surfaceLight,
  },
  emojiOptionText: {
    fontSize: 24,
  },

  // More Generic Modal Styles
  schedulerModal: { flex: 1, backgroundColor: COLORS.background },
  schedulerHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  schedulerTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  schedulerContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  schedulerComingSoon: { fontSize: 16, color: COLORS.textMuted },

  forwardModal: { flex: 1, backgroundColor: COLORS.background },
  forwardHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  forwardTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  forwardContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  forwardComingSoon: { fontSize: 16, color: COLORS.textMuted },

  threadModal: { flex: 1, backgroundColor: COLORS.background },
  threadHeader: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 16, paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: COLORS.surfaceLight },
  threadTitle: { fontSize: 18, fontWeight: '600', color: COLORS.text },
  threadContent: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  threadComingSoon: { fontSize: 16, color: COLORS.textMuted },

  messageInfoModal: {
    flex: 1,
    backgroundColor: COLORS.background,
    // Animation properties handled by Animated.View
  },
  messageInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
    ...SHADOWS.sm,
  },
  messageInfoTitle: {
    fontSize: ResponsiveTypography.fontSize.lg,
    color: COLORS.text
  },
  messageInfoSection: {
    marginBottom: ResponsiveSpacing.md,
    paddingHorizontal: ResponsiveSpacing.md,
  },
  messageInfoLabel: {
    fontSize: ResponsiveTypography.fontSize.sm,
    fontWeight: '600',
    color: COLORS.textMuted,
    marginBottom: ResponsiveSpacing.xs,
  },
  messageInfoText: {
    fontSize: ResponsiveTypography.fontSize.md,
    color: COLORS.text,
    lineHeight: ResponsiveTypography.lineHeight.md,
  },
  messageInfoReadBy: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: COLORS.textMuted,
    marginBottom: ResponsiveSpacing.xs,
  },
  messageInfoContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: ResponsiveSpacing.lg,
  },
  messageInfoComingSoon: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: COLORS.textMuted
  },

  // Modal backdrop and animation styles
  modalBackdrop: {
    flex: 1,
    backgroundColor: COLORS.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    // Animation properties handled by Animated.View
  },
  modal: {
    backgroundColor: COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    padding: ResponsiveSpacing.lg,
    margin: ResponsiveSpacing.md,
    minWidth: DeviceInfo.isTablet ? '60%' : '80%',
    ...SHADOWS.lg,
    // Animation properties handled by Animated.View
  },
  modalTitle: {
    fontSize: ResponsiveTypography.fontSize.xl,
    color: COLORS.text,
    marginBottom: ResponsiveSpacing.md,
    textAlign: 'center',
  },
  modalClose: {
    fontSize: 16,
    color: COLORS.primary,
    textAlign: 'center',
    marginTop: 16,
  },

  // Missing styles
  slowModeIndicator: {
    backgroundColor: COLORS.warning,
    padding: 8,
    borderRadius: 8,
    margin: 8,
  },
  slowModeText: {
    color: COLORS.text,
    fontSize: 12,
    textAlign: 'center',
  },
  typingMembersIndicator: {
    backgroundColor: COLORS.primary,
    padding: 8,
    borderRadius: 8,
    margin: 8,
  },
  typingMembersText: {
    color: COLORS.text,
    fontSize: 12,
    textAlign: 'center',
  },
  onlineMembersIndicator: {
    backgroundColor: COLORS.success,
    padding: 8,
    borderRadius: 8,
    margin: 8,
  },
  onlineMembersText: {
    color: COLORS.text,
    fontSize: 12,
    textAlign: 'center',
  },
  // Poll Creator Styles
  pollQuestionInput: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    backgroundColor: COLORS.inputBackground,
    color: COLORS.text,
  },
  pollOptionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  pollOptionInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 8,
    marginRight: 8,
    backgroundColor: COLORS.inputBackground,
    color: COLORS.text,
  },
  removeOptionButton: {
    padding: 8,
    backgroundColor: COLORS.error,
    borderRadius: 6,
  },
  addOptionButton: {
    padding: 12,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  addOptionText: {
    color: COLORS.text,
    fontWeight: '600',
  },
  // Contact Sharing Styles
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  contactAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contactAvatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  contactPhone: {
    fontSize: 14,
    color: COLORS.textMuted,
  },
  contactList: {
    maxHeight: 300,
  },
  // Message Scheduler Styles
  schedulerMessageInput: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    backgroundColor: COLORS.inputBackground,
    color: COLORS.text,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  dateTimeButton: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    backgroundColor: COLORS.surface,
  },
  dateTimeText: {
    color: COLORS.text,
    fontSize: 16,
  },
  scheduleButton: {
    backgroundColor: COLORS.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  scheduleButtonText: {
    color: COLORS.text,
    fontWeight: '600',
    fontSize: 16,
  },
  // Forward Chat Styles
  forwardChatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  forwardChatSelected: {
    backgroundColor: COLORS.primary + '20',
  },
  forwardChatAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  forwardChatAvatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  forwardChatInfo: {
    flex: 1,
  },
  forwardChatName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  forwardChatType: {
    fontSize: 14,
    color: COLORS.textMuted,
  },
  forwardChatList: {
    maxHeight: 300,
  },
  forwardButton: {
    backgroundColor: COLORS.primary,
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  forwardButtonText: {
    color: COLORS.text,
    fontWeight: '600',
    fontSize: 16,
  },
  // Thread Message Styles
  originalMessage: {
    backgroundColor: COLORS.surface,
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.primary,
  },
  originalMessageLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 4,
  },
  originalMessageText: {
    fontSize: 14,
    color: COLORS.text,
    marginBottom: 4,
  },
  originalMessageAuthor: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  threadMessage: {
    backgroundColor: COLORS.surface,
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  threadMessageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  threadMessageAuthor: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
  },
  threadMessageTime: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  threadMessageText: {
    fontSize: 14,
    color: COLORS.text,
  },
  threadMessagesList: {
    maxHeight: 300,
  },
  threadReplyInput: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  threadReplyTextInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    backgroundColor: COLORS.inputBackground,
    color: COLORS.text,
  },
  threadReplySendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    padding: 8,
  },

  // Group Settings Dropdown Styles
  dropdownBackdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1000,
  },
  dropdownMenu: {
    position: 'absolute',
    top: 100, // Position below header with more space
    right: 12,
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    paddingVertical: 8,
    minWidth: 200,
    maxWidth: 250,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    borderWidth: 1,
    borderColor: COLORS.surfaceLight,
  },
  dropdownOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  dropdownOptionText: {
    flex: 1,
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
  },
  dropdownBadge: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 16,
    alignItems: 'center',
  },
  dropdownBadgeText: {
    fontSize: 10,
    color: COLORS.background,
    fontWeight: '600',
  },

  // Avatar Zoom Modal Styles
  avatarZoomBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarZoomClose: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1,
    padding: 10,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  avatarZoomContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarZoomImage: {
    width: 300,
    height: 300,
    borderRadius: 150,
    borderWidth: 3,
    borderColor: COLORS.primary,
  },
  avatarZoomTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: 'white',
    marginTop: 20,
    textAlign: 'center',
  },
});
