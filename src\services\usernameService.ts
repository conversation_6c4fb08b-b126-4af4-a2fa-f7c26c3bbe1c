// 🔥 REAL USERNAME SERVICE - COMPLETE FIREBASE INTEGRATION
// No mockups, no fake data - 100% real Firebase username functionality

import {
  collection,
  query,
  where,
  getDocs,
  doc,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { db, functions } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

// Real Username Interface
interface UsernameCheckResult {
  available: boolean;
  reason?: string;
}

interface UsernameUpdateResult {
  success: boolean;
  username?: string;
  error?: string;
}

class UsernameService {
  private isInitialized = false;
  private usernameCache: Map<string, { available: boolean; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();
      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.CACHE_DURATION;
  }

  // ==================== REAL USERNAME VALIDATION ====================
  
  validateUsername(username: string): { valid: boolean; error?: string } {
    if (!username || typeof username !== 'string') {
      return { valid: false, error: 'Username is required' };
    }

    const trimmed = username.trim();

    if (trimmed.length < 3) {
      return { valid: false, error: 'Username must be at least 3 characters' };
    }

    if (trimmed.length > 20) {
      return { valid: false, error: 'Username must be 20 characters or less' };
    }

    // No spaces allowed (must be one word)
    if (/\s/.test(trimmed)) {
      return { valid: false, error: 'Username must be one word (no spaces)' };
    }

    // Must start with capital letter
    if (!/^[A-Z]/.test(trimmed)) {
      return { valid: false, error: 'Username must start with a capital letter' };
    }

    // Allow letters, numbers, emojis, and underscores (but no spaces)
    const usernameRegex = /^[A-Z][\p{L}\p{N}\p{Emoji}_]*$/u;
    if (!usernameRegex.test(trimmed)) {
      return { valid: false, error: 'Username must start with capital letter and contain only letters, numbers, emojis, and underscores (no spaces)' };
    }
    
    // Don't allow usernames that start with numbers
    if (/^[0-9]/.test(trimmed)) {
      return { valid: false, error: 'Username cannot start with a number' };
    }
    
    // Reserved usernames
    const reserved = [
      'admin', 'administrator', 'root', 'system', 'support', 'help',
      'irachat', 'iratech', 'official', 'verified', 'staff', 'team',
      'api', 'www', 'mail', 'email', 'ftp', 'http', 'https', 'ssl',
      'user', 'users', 'guest', 'anonymous', 'null', 'undefined'
    ];
    
    if (reserved.includes(trimmed.toLowerCase())) {
      return { valid: false, error: 'This username is reserved' };
    }
    
    return { valid: true };
  }

  // ==================== REAL USERNAME AVAILABILITY CHECK ====================
  
  async checkUsernameAvailability(username: string): Promise<UsernameCheckResult> {
    try {
      // First validate format
      const validation = this.validateUsername(username);
      if (!validation.valid) {
        return { available: false, reason: validation.error };
      }

      const trimmedUsername = username.trim();

      // Check cache first
      const cached = this.usernameCache.get(trimmedUsername);
      if (cached && this.isCacheValid(cached.timestamp)) {
        return { available: cached.available, reason: cached.available ? undefined : 'Username is already taken' };
      }

      // Username checking requires online connection
      // Initialize network state manager if not already done
      try {
        await networkStateManager.initialize();
      } catch (error) {
        // Continue if initialization fails
      }

      if (!networkStateManager.isOnline()) {
        return { available: false, reason: 'Internet connection required to check username availability' };
      }

      // Use Cloud Function for server-side check (more reliable)
      try {
        const checkUsernameFunction = httpsCallable(functions, 'checkUsernameAvailability');
        const result = await checkUsernameFunction({ username: trimmedUsername });

        const checkResult = result.data as UsernameCheckResult;

        // Cache the result
        this.usernameCache.set(trimmedUsername, {
          available: checkResult.available,
          timestamp: Date.now()
        });

        return checkResult;
      } catch (cloudError) {
        // Fallback to client-side check
        return await this.checkUsernameClient(trimmedUsername);
      }
    } catch (error) {
      return { available: false, reason: 'Unable to check availability' };
    }
  }

  private async checkUsernameClient(username: string): Promise<UsernameCheckResult> {
    try {
      const usernameQuery = query(
        collection(db, 'users'),
        where('username', '==', username)
      );

      const snapshot = await getDocs(usernameQuery);
      const available = snapshot.empty;

      // Cache the result
      this.usernameCache.set(username, {
        available,
        timestamp: Date.now()
      });

      return {
        available,
        reason: available ? undefined : 'Username is already taken'
      };
    } catch (error) {
      return { available: false, reason: 'Unable to verify availability' };
    }
  }

  // ==================== REAL USERNAME UPDATE ====================
  
  async updateUsername(userId: string, newUsername: string): Promise<UsernameUpdateResult> {
    try {
      // Username updates require online connection
      if (!networkStateManager.isOnline()) {
        return { success: false, error: 'Internet connection required to update username' };
      }

      // Validate new username
      const validation = this.validateUsername(newUsername);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      const trimmedUsername = newUsername.trim();

      // Check availability
      const availability = await this.checkUsernameAvailability(trimmedUsername);
      if (!availability.available) {
        return { success: false, error: availability.reason || 'Username not available' };
      }

      // Use Cloud Function for server-side update (more reliable)
      try {
        const updateUsernameFunction = httpsCallable(functions, 'updateUsername');
        const result = await updateUsernameFunction({ newUsername: trimmedUsername });

        return result.data as UsernameUpdateResult;
      } catch (cloudError) {
        // Fallback to client-side update
        return await this.updateUsernameClient(userId, trimmedUsername);
      }
    } catch (error) {
      return { success: false, error: 'Failed to update username' };
    }
  }

  private async updateUsernameClient(userId: string, username: string): Promise<UsernameUpdateResult> {
    try {
      // Double-check availability before updating
      const availability = await this.checkUsernameClient(username);
      if (!availability.available) {
        return { success: false, error: availability.reason || 'Username not available' };
      }

      // Update user document
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        username,
        updatedAt: serverTimestamp(),
      });

      return { success: true, username };
    } catch (error) {
      return { success: false, error: 'Failed to update username' };
    }
  }

  // ==================== REAL USERNAME SUGGESTIONS ====================

  async generateUsernameSuggestions(baseName: string): Promise<string[]> {
    try {
      
      const suggestions: string[] = [];
      const baseUsername = baseName.toLowerCase().replace(/[^a-z0-9]/g, '');
      
      if (baseUsername.length < 3) {
        return ['user123', 'newuser', 'irachat_user'];
      }

      // Generate variations
      const variations = [
        baseUsername,
        `${baseUsername}_`,
        `${baseUsername}123`,
        `${baseUsername}_user`,
        `${baseUsername}${new Date().getFullYear()}`,
        `the_${baseUsername}`,
        `${baseUsername}_official`,
        `${baseUsername}${Math.floor(Math.random() * 100)}`,
        `${baseUsername}_${Math.floor(Math.random() * 1000)}`,
        `${baseUsername.substring(0, 10)}_${Date.now().toString().slice(-4)}`,
      ];

      // Check availability for each variation
      for (const variation of variations) {
        if (suggestions.length >= 5) break; // Limit to 5 suggestions
        
        const validation = this.validateUsername(variation);
        if (validation.valid) {
          const availability = await this.checkUsernameAvailability(variation);
          if (availability.available) {
            suggestions.push(variation);
          }
        }
      }

      // If we don't have enough suggestions, add some random ones
      while (suggestions.length < 3) {
        const randomSuffix = Math.floor(Math.random() * 10000);
        const randomUsername = `${baseUsername}${randomSuffix}`;
        
        const validation = this.validateUsername(randomUsername);
        if (validation.valid) {
          const availability = await this.checkUsernameAvailability(randomUsername);
          if (availability.available && !suggestions.includes(randomUsername)) {
            suggestions.push(randomUsername);
          }
        }
      }

      return suggestions;
    } catch (error) {
      return ['user123', 'newuser', 'irachat_user'];
    }
  }

  // ==================== REAL USERNAME SEARCH ====================

  async searchUsersByUsername(searchTerm: string, limit: number = 10): Promise<any[]> {
    try {
      
      if (!searchTerm || searchTerm.trim().length < 2) {
        return [];
      }

      const trimmedSearch = searchTerm.trim().toLowerCase();
      
      // Search for usernames that start with the search term
      const usersQuery = query(
        collection(db, 'users'),
        where('username', '>=', trimmedSearch),
        where('username', '<=', trimmedSearch + '\uf8ff')
      );

      const snapshot = await getDocs(usersQuery);
      const users = snapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter((user: any) => user.username?.toLowerCase().includes(trimmedSearch))
        .slice(0, limit);

      return users;
    } catch (error) {
      return [];
    }
  }

  // ==================== UTILITY METHODS ====================
  
  formatUsername(username: string): string {
    return username.trim().toLowerCase();
  }

  isValidUsernameFormat(username: string): boolean {
    return this.validateUsername(username).valid;
  }



  getUsernameFromDisplayName(displayName: string): string {
    const baseUsername = displayName.toLowerCase().replace(/[^a-z0-9]/g, '');
    return baseUsername.length >= 3 ? baseUsername : 'user';
  }
}

export const usernameService = new UsernameService();
