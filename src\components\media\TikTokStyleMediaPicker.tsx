import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Dimensions,
  StyleSheet,
  Animated,
  FlatList,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
// import { BlurView } from 'expo-blur'; // Optional - using regular View for compatibility

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface MediaAsset {
  id: string;
  uri: string;
  mediaType: 'photo' | 'video';
  width: number;
  height: number;
  duration?: number;
  creationTime: number;
}

interface TikTokStyleMediaPickerProps {
  visible: boolean;
  onClose: () => void;
  onMediaSelected: (uri: string, type: 'photo' | 'video') => void;
  onCameraPress: () => void;
  onTextPress?: () => void;
}

export const TikTokStyleMediaPicker: React.FC<TikTokStyleMediaPickerProps> = ({
  visible,
  onClose,
  onMediaSelected,
  onCameraPress,
  onTextPress,
}) => {
  const [galleryAssets, setGalleryAssets] = useState<MediaAsset[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<MediaAsset | null>(null);
  
  // Animation values
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const galleryHeightAnim = useRef(new Animated.Value(SCREEN_HEIGHT * 0.5)).current;
  const [galleryExpanded, setGalleryExpanded] = useState(false);

  // Load gallery assets
  const loadGalleryAssets = async () => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant media library permission to access your photos and videos.');
        return;
      }

      setIsLoading(true);
      const media = await MediaLibrary.getAssetsAsync({
        mediaType: ['photo', 'video'],
        sortBy: 'creationTime',
        first: 50,
      });

      const assets: MediaAsset[] = media.assets.map(asset => ({
        id: asset.id,
        uri: asset.uri,
        mediaType: asset.mediaType === 'video' ? 'video' : 'photo',
        width: asset.width,
        height: asset.height,
        duration: asset.duration,
        creationTime: asset.creationTime,
      }));

      setGalleryAssets(assets);
      if (assets.length > 0) {
        setSelectedAsset(assets[0]);
      }
    } catch (error) {
      console.error('Error loading gallery assets:', error);
      Alert.alert('Error', 'Failed to load gallery. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Animation effects
  useEffect(() => {
    if (visible) {
      loadGalleryAssets();
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Handle gallery expansion
  const toggleGalleryExpansion = () => {
    const targetHeight = galleryExpanded ? SCREEN_HEIGHT * 0.5 : SCREEN_HEIGHT * 0.85;
    
    Animated.spring(galleryHeightAnim, {
      toValue: targetHeight,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
    
    setGalleryExpanded(!galleryExpanded);
  };

  // Handle asset selection
  const handleAssetSelect = (asset: MediaAsset) => {
    setSelectedAsset(asset);
  };

  // Handle use selected media
  const handleUseMedia = () => {
    if (selectedAsset) {
      onMediaSelected(selectedAsset.uri, selectedAsset.mediaType);
      onClose();
    }
  };

  // Render gallery item
  const renderGalleryItem = ({ item }: { item: MediaAsset }) => (
    <TouchableOpacity
      style={[
        styles.galleryItem,
        selectedAsset?.id === item.id && styles.selectedGalleryItem,
      ]}
      onPress={() => handleAssetSelect(item)}
    >
      <Image source={{ uri: item.uri }} style={styles.galleryItemImage} />
      {item.mediaType === 'video' && (
        <View style={styles.videoIndicator}>
          <Ionicons name="play" size={16} color="white" />
          {item.duration && (
            <Text style={styles.videoDuration}>
              {Math.floor(item.duration / 60)}:{(item.duration % 60).toFixed(0).padStart(2, '0')}
            </Text>
          )}
        </View>
      )}
      {selectedAsset?.id === item.id && (
        <View style={styles.selectedOverlay}>
          <Ionicons name="checkmark-circle" size={24} color="#667eea" />
        </View>
      )}
    </TouchableOpacity>
  );

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Background blur */}
        <View style={[StyleSheet.absoluteFill, { backgroundColor: 'rgba(0, 0, 0, 0.8)' }]} />
        
        <Animated.View
          style={[
            styles.content,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={28} color="white" />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Create Story</Text>
            <View style={styles.headerSpacer} />
          </View>

          {/* Media Preview */}
          <View style={styles.previewContainer}>
            {selectedAsset ? (
              <Image source={{ uri: selectedAsset.uri }} style={styles.previewImage} />
            ) : (
              <View style={styles.previewPlaceholder}>
                <Ionicons name="image-outline" size={64} color="#666" />
                <Text style={styles.previewPlaceholderText}>Select media from gallery</Text>
              </View>
            )}
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <TouchableOpacity style={styles.actionButton} onPress={onCameraPress}>
              <Ionicons name="camera" size={24} color="white" />
              <Text style={styles.actionButtonText}>Camera</Text>
            </TouchableOpacity>
            
            {onTextPress && (
              <TouchableOpacity style={styles.actionButton} onPress={onTextPress}>
                <Ionicons name="text" size={24} color="white" />
                <Text style={styles.actionButtonText}>Text</Text>
              </TouchableOpacity>
            )}
            
            {selectedAsset && (
              <TouchableOpacity style={styles.useButton} onPress={handleUseMedia}>
                <Text style={styles.useButtonText}>Use This</Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Gallery */}
          <Animated.View
            style={[
              styles.galleryContainer,
              { height: galleryHeightAnim },
            ]}
          >
            {/* Gallery Header */}
            <TouchableOpacity
              style={styles.galleryHeader}
              onPress={toggleGalleryExpansion}
            >
              <View style={styles.galleryHandle} />
              <Text style={styles.galleryTitle}>Recent</Text>
              <Ionicons
                name={galleryExpanded ? "chevron-down" : "chevron-up"}
                size={20}
                color="white"
              />
            </TouchableOpacity>

            {/* Gallery Grid */}
            <FlatList
              data={galleryAssets}
              renderItem={renderGalleryItem}
              keyExtractor={(item) => item.id}
              numColumns={3}
              contentContainerStyle={styles.galleryGrid}
              showsVerticalScrollIndicator={false}
            />
          </Animated.View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  content: {
    flex: 1,
    backgroundColor: 'black',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  headerSpacer: {
    width: 44,
  },
  previewContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#1a1a1a',
  },
  previewImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  previewPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewPlaceholderText: {
    color: '#666',
    fontSize: 16,
    marginTop: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '500',
  },
  useButton: {
    flex: 1,
    backgroundColor: '#667eea',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  useButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  galleryContainer: {
    backgroundColor: '#1a1a1a',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  galleryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  galleryHandle: {
    width: 40,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    position: 'absolute',
    top: 8,
    left: '50%',
    marginLeft: -20,
  },
  galleryTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  galleryGrid: {
    padding: 4,
  },
  galleryItem: {
    flex: 1,
    aspectRatio: 1,
    margin: 2,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  selectedGalleryItem: {
    borderWidth: 3,
    borderColor: '#667eea',
  },
  galleryItemImage: {
    width: '100%',
    height: '100%',
  },
  videoIndicator: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    gap: 2,
  },
  videoDuration: {
    color: 'white',
    fontSize: 10,
    fontWeight: '500',
  },
  selectedOverlay: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
  },
});
