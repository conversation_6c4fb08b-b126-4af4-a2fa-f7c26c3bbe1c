// Firebase WebRTC Signaling Service
import {
  addDoc,
  collection,
  doc,
  onSnapshot,
  setDoc,
  deleteDoc,
  query,
  orderBy,

} from "firebase/firestore";
import { RTCSessionDescription, RTCIceCandidate } from "react-native-webrtc";
import { db } from "./firebaseSimple";
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface SignalingData {
  offer?: {
    sdp: string;
    type: string;
  };
  answer?: {
    sdp: string;
    type: string;
  };
  iceCandidate?: {
    candidate: string;
    sdpMLineIndex: number | null;
    sdpMid: string | null;
  };
}

export interface SignalingCallbacks {
  onOffer?: (_offer: RTCSessionDescription) => void;
  onAnswer?: (_answer: RTCSessionDescription) => void;
  onIceCandidate?: (_candidate: RTCIceCandidate) => void;
  onError?: (_error: Error) => void;
}

class SignalingService {
  private unsubscribers: (() => void)[] = [];
  private isInitialized = false;
  private offlineSignalingQueue: Map<string, any[]> = new Map();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();

      // Set up network state listener for offline sync
      networkStateManager.addListener('signalingService', (networkState) => {
        if (networkState.isConnected) {
          this.syncOfflineSignaling();
        }
      });

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async syncOfflineSignaling(): Promise<void> {
    try {
      // Sync any queued signaling data when back online
      for (const [callId, queuedData] of this.offlineSignalingQueue.entries()) {
        for (const signalingData of queuedData) {
          try {
            await this.syncSignalingToFirebase(callId, signalingData);
          } catch (error) {
            // Keep in queue for retry
            continue;
          }
        }
        // Clear successfully synced data
        this.offlineSignalingQueue.delete(callId);
      }
    } catch (error) {
      // Sync failed - will retry on next connection
    }
  }

  private async syncSignalingToFirebase(callId: string, data: any): Promise<void> {
    if (data.type === 'offer') {
      await this.sendOffer(callId, data.offer);
    } else if (data.type === 'answer') {
      await this.sendAnswer(callId, data.answer);
    } else if (data.type === 'iceCandidate') {
      await this.sendIceCandidate(callId, data.candidate);
    }
  }

  private async storeOfflineSignaling(callId: string, data: any): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO signaling_queue (
          callId, data, timestamp, synced
        ) VALUES (?, ?, ?, ?)
      `, [callId, JSON.stringify(data), Date.now(), 0]);

      // Also add to memory queue for immediate retry
      if (!this.offlineSignalingQueue.has(callId)) {
        this.offlineSignalingQueue.set(callId, []);
      }
      this.offlineSignalingQueue.get(callId)!.push(data);
    } catch (error) {
      // Offline storage failed
    }
  }

  /**
   * Send an offer through Firebase
   */
  async sendOffer(callId: string, offer: RTCSessionDescription): Promise<void> {
    try {
      if (networkStateManager.isOnline()) {
        await setDoc(
          doc(db, "calls", callId),
          {
            offer: {
              sdp: offer.sdp,
              type: offer.type,
            },
            timestamp: new Date(),
          },
          { merge: true }
        );
      } else {
        // Store offline for later sync
        await this.storeOfflineSignaling(callId, {
          type: 'offer',
          offer,
        });
      }
    } catch (error) {
      // If online fails, try offline storage
      try {
        await this.storeOfflineSignaling(callId, {
          type: 'offer',
          offer,
        });
      } catch (offlineError) {
        throw error;
      }
    }
  }

  /**
   * Send an answer through Firebase
   */
  async sendAnswer(callId: string, answer: RTCSessionDescription): Promise<void> {
    try {
      if (networkStateManager.isOnline()) {
        await setDoc(
          doc(db, "calls", callId),
          {
            answer: {
              sdp: answer.sdp,
              type: answer.type,
            },
            timestamp: new Date(),
          },
          { merge: true }
        );
      } else {
        // Store offline for later sync
        await this.storeOfflineSignaling(callId, {
          type: 'answer',
          answer,
        });
      }
    } catch (error) {
      // If online fails, try offline storage
      try {
        await this.storeOfflineSignaling(callId, {
          type: 'answer',
          answer,
        });
      } catch (offlineError) {
        throw error;
      }
    }
  }

  /**
   * Send an ICE candidate through Firebase
   */
  async sendIceCandidate(callId: string, candidate: RTCIceCandidate): Promise<void> {
    try {
      if (networkStateManager.isOnline()) {
        await addDoc(collection(db, "calls", callId, "iceCandidates"), {
          candidate: candidate.candidate,
          sdpMLineIndex: candidate.sdpMLineIndex,
          sdpMid: candidate.sdpMid,
          timestamp: new Date(),
        });
      } else {
        // Store offline for later sync
        await this.storeOfflineSignaling(callId, {
          type: 'iceCandidate',
          candidate,
        });
      }
    } catch (error) {
      // If online fails, try offline storage
      try {
        await this.storeOfflineSignaling(callId, {
          type: 'iceCandidate',
          candidate,
        });
      } catch (offlineError) {
        throw error;
      }
    }
  }

  /**
   * Listen for signaling data (offers, answers)
   */
  listenForSignaling(callId: string, callbacks: SignalingCallbacks): () => void {

    // Listen for offers and answers
    const unsubscribeCall = onSnapshot(
      doc(db, "calls", callId),
      (doc) => {
        if (doc.exists()) {
          const data = doc.data();

          // Handle incoming offer
          if (data.offer && callbacks.onOffer) {
            try {
              const offer = new RTCSessionDescription({
                sdp: data.offer.sdp,
                type: data.offer.type,
              });
              callbacks.onOffer(offer);
            } catch (error) {
              callbacks.onError?.(error as Error);
            }
          }

          // Handle incoming answer
          if (data.answer && callbacks.onAnswer) {
            try {
              const answer = new RTCSessionDescription({
                sdp: data.answer.sdp,
                type: data.answer.type,
              });
              callbacks.onAnswer(answer);
            } catch (error) {
              callbacks.onError?.(error as Error);
            }
          }
        }
      },
      (error) => {
        callbacks.onError?.(error);
      }
    );

    // Listen for ICE candidates
    const unsubscribeIce = onSnapshot(
      query(
        collection(db, "calls", callId, "iceCandidates"),
        orderBy("timestamp", "asc")
      ),
      (snapshot) => {
        snapshot.docChanges().forEach((change) => {
          if (change.type === "added" && callbacks.onIceCandidate) {
            try {
              const candidateData = change.doc.data();
              const candidate = new RTCIceCandidate({
                candidate: candidateData.candidate,
                sdpMLineIndex: candidateData.sdpMLineIndex,
                sdpMid: candidateData.sdpMid,
              });
              callbacks.onIceCandidate(candidate);
            } catch (error) {
              callbacks.onError?.(error as Error);
            }
          }
        });
      },
      (error) => {
        callbacks.onError?.(error);
      }
    );

    // Store unsubscribers
    this.unsubscribers.push(unsubscribeCall, unsubscribeIce);

    // Return cleanup function
    return () => {
      unsubscribeCall();
      unsubscribeIce();
      
      // Remove from stored unsubscribers
      this.unsubscribers = this.unsubscribers.filter(
        (unsub) => unsub !== unsubscribeCall && unsub !== unsubscribeIce
      );
    };
  }

  /**
   * Clean up all signaling listeners
   */
  cleanup(): void {
    this.unsubscribers.forEach((unsubscribe) => unsubscribe());
    this.unsubscribers = [];
  }

  /**
   * Delete signaling data for a call
   */
  async deleteSignalingData(callId: string): Promise<void> {
    try {
      // Delete the main call document (this will also delete subcollections)
      await deleteDoc(doc(db, "calls", callId));
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if a call document exists
   */
  async callExists(callId: string): Promise<boolean> {
    try {
      const { getDoc } = await import("firebase/firestore");
      const callDoc = await getDoc(doc(db, "calls", callId));
      return callDoc.exists();
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const signalingService = new SignalingService();
export default signalingService;
