/**
 * Media Full View Component for IraChat
 * Full-screen media viewer with caption overlay and controls
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Animated,
  Image,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Video, AVPlaybackStatus, ResizeMode } from 'expo-av';
import { useTheme } from '../../contexts/ThemeContext';

interface MediaFullViewProps {
  visible: boolean;
  onClose: () => void;
  mediaType: 'image' | 'video';
  mediaUri: string;
  caption?: string;
  fileName?: string;
  onShare?: () => void;
  onSave?: () => void;
  onDelete?: () => void;
}

export const MediaFullView: React.FC<MediaFullViewProps> = ({
  visible,
  onClose,
  mediaType,
  mediaUri,
  caption,
  fileName: _fileName, // Prefix with underscore to indicate intentionally unused
  onShare,
  onSave,
  onDelete,
}) => {
  const { } = useTheme(); // Keep for future theme usage
  const [showControls, setShowControls] = useState(true);
  const [showCaption, setShowCaption] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [captionExpanded, setCaptionExpanded] = useState(false);
  
  const controlsOpacity = useRef(new Animated.Value(1)).current;
  const captionOpacity = useRef(new Animated.Value(1)).current;
  const videoRef = useRef<Video>(null);
  const hideControlsTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (visible) {
      setShowControls(true);
      setShowCaption(true);
      resetHideControlsTimer();
    }
  }, [visible]);

  const resetHideControlsTimer = () => {
    if (hideControlsTimeout.current) {
      clearTimeout(hideControlsTimeout.current);
    }
    
    hideControlsTimeout.current = setTimeout(() => {
      hideControls();
    }, 3000);
  };

  const hideControls = () => {
    Animated.parallel([
      Animated.timing(controlsOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false, // Changed to false for consistency
      }),
      Animated.timing(captionOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false, // Changed to false for consistency
      }),
    ]).start(() => {
      setShowControls(false);
      setShowCaption(false);
    });
  };

  const showControlsAndCaption = () => {
    setShowControls(true);
    setShowCaption(true);

    Animated.parallel([
      Animated.timing(controlsOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false, // Changed to false for consistency
      }),
      Animated.timing(captionOpacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false, // Changed to false for consistency
      }),
    ]).start();

    resetHideControlsTimer();
  };

  const handleTap = () => {
    if (showControls) {
      hideControls();
    } else {
      showControlsAndCaption();
    }
  };

  const handleVideoPlaybackStatusUpdate = (status: AVPlaybackStatus) => {
    if (status.isLoaded) {
      setIsPlaying(status.isPlaying);
    }
  };

  const toggleVideoPlayback = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        await videoRef.current.playAsync();
      }
    }
  };

  const handleClose = () => {
    if (videoRef.current) {
      videoRef.current.pauseAsync();
    }
    onClose();
  };

  const toggleCaptionExpanded = () => {
    setCaptionExpanded(!captionExpanded);
  };

  const renderImage = () => (
    <TouchableOpacity
      style={styles.mediaContainer}
      onPress={handleTap}
      activeOpacity={1}
    >
      <Image
        source={{ uri: mediaUri }}
        style={styles.fullImage}
        resizeMode="contain"
      />
    </TouchableOpacity>
  );

  const renderVideo = () => (
    <TouchableOpacity
      style={styles.mediaContainer}
      onPress={handleTap}
      activeOpacity={1}
    >
      <Video
        ref={videoRef}
        source={{ uri: mediaUri }}
        style={styles.fullVideo}
        useNativeControls={false}
        resizeMode={ResizeMode.CONTAIN}
        shouldPlay={false}
        isLooping={false}
        onPlaybackStatusUpdate={handleVideoPlaybackStatusUpdate}
      />
      
      {/* Video play/pause overlay */}
      {showControls && (
        <Animated.View
          style={[
            styles.videoControlsOverlay,
            { opacity: controlsOpacity },
          ]}
        >
          <TouchableOpacity
            style={styles.playPauseButton}
            onPress={toggleVideoPlayback}
          >
            <Ionicons
              name={isPlaying ? 'pause' : 'play'}
              size={60}
              color="white"
            />
          </TouchableOpacity>
        </Animated.View>
      )}
    </TouchableOpacity>
  );

  const renderCaption = () => {
    if (!caption) return null;

    const shouldTruncate = caption.length > 100 && !captionExpanded;
    const displayCaption = shouldTruncate ? `${caption.substring(0, 100)}...` : caption;

    return (
      <Animated.View
        style={[
          styles.captionContainer,
          { opacity: captionOpacity },
        ]}
      >
        <View style={styles.captionBackground}>
          <Text style={styles.captionText}>{displayCaption}</Text>
          {caption.length > 100 && (
            <TouchableOpacity onPress={toggleCaptionExpanded}>
              <Text style={styles.captionToggle}>
                {captionExpanded ? 'Less' : 'More'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </Animated.View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="fade"
      presentationStyle="fullScreen"
      onRequestClose={handleClose}
    >
      <StatusBar hidden />
      <View style={[styles.container, { backgroundColor: 'black' }]}>
        {/* Top controls */}
        {showControls && (
          <Animated.View
            style={[
              styles.topControls,
              { opacity: controlsOpacity },
            ]}
          >
            <SafeAreaView style={styles.topControlsContent}>
              <TouchableOpacity
                style={styles.controlButton}
                onPress={handleClose}
              >
                <Ionicons name="close" size={28} color="white" />
              </TouchableOpacity>
              
              <View style={styles.topRightControls}>
                {onShare && (
                  <TouchableOpacity
                    style={styles.controlButton}
                    onPress={onShare}
                  >
                    <Ionicons name="share" size={24} color="white" />
                  </TouchableOpacity>
                )}
                
                {onSave && (
                  <TouchableOpacity
                    style={styles.controlButton}
                    onPress={onSave}
                  >
                    <Ionicons name="download" size={24} color="white" />
                  </TouchableOpacity>
                )}
                
                {onDelete && (
                  <TouchableOpacity
                    style={styles.controlButton}
                    onPress={onDelete}
                  >
                    <Ionicons name="trash" size={24} color="white" />
                  </TouchableOpacity>
                )}
              </View>
            </SafeAreaView>
          </Animated.View>
        )}

        {/* Media content */}
        {mediaType === 'image' ? renderImage() : renderVideo()}

        {/* Caption overlay */}
        {showCaption && renderCaption()}
      </View>
    </Modal>
  );
};

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaContainer: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImage: {
    width: screenWidth,
    height: screenHeight,
  },
  fullVideo: {
    width: screenWidth,
    height: screenHeight,
  },
  topControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  topControlsContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  topRightControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButton: {
    padding: 8,
    marginHorizontal: 4,
  },
  videoControlsOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playPauseButton: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captionContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  captionBackground: {
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 16,
    paddingBottom: 32,
  },
  captionText: {
    color: 'white',
    fontSize: 16,
    lineHeight: 22,
    textAlign: 'center',
  },
  captionToggle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    textDecorationLine: 'underline',
  },
});

export default MediaFullView;
