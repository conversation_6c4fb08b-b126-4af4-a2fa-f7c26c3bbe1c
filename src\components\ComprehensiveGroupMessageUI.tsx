// 💬 COMPREHENSIVE GROUP MESSAGE UI SYSTEM
// Complete message bubbles, reactions, threading, status indicators, and all message UI components
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Modal,
  Animated,
  Pressable,
  PanResponder,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';
import * as Haptics from 'expo-haptics';
import { audioManager } from '../services/audioManager';

// IraChat Branding Colors
const COLORS = {
  primary: '#4A90E2',      // Softer Blue - easier on eyes
  primaryDark: '#357ABD',  // Darker Blue
  primaryLight: '#6BA3E8', // Lighter Blue
  secondary: '#5A9FD4',    // Muted Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#E8E8E8',         // Softer White - less harsh
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#4CAF50',      // Softer Green
  warning: '#FF9800',      // Softer Orange
  error: '#F44336',        // Softer Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  inputBackground: 'rgba(255, 255, 255, 0.1)',
  myMessageBubble: '#2D3748',    // Dark gray-blue - much easier on eyes
  otherMessageBubble: '#1A202C', // Very dark gray - comfortable for reading
  reactionBackground: 'rgba(74, 144, 226, 0.2)',
};

// Enhanced Message Interface
interface GroupMessage {
  id: string;
  text?: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  timestamp: Date;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice' | 'call' | 'location' | 'contact' | 'poll' | 'announcement';
  mediaUrl?: string;
  mediaThumbnail?: string;
  duration?: number;
  fileName?: string;
  fileSize?: number;
  
  // Advanced Features
  mentions?: string[]; // User IDs mentioned
  isAnnouncement?: boolean;
  announcementPriority?: 'low' | 'medium' | 'high';
  
  // Reply System
  replyTo?: {
    messageId: string;
    text: string;
    senderName: string;
    type: string;
    mediaUrl?: string;
  };
  
  // Reactions System
  reactions?: {
    [emoji: string]: {
      users: string[];
      count: number;
    };
  };
  
  // Threading
  threadReplies?: GroupMessage[];
  threadCount?: number;
  
  // Editing & Deletion
  isEdited?: boolean;
  editedAt?: Date;
  isDeleted?: boolean;
  deletedAt?: Date;
  
  // Forwarding
  isForwarded?: boolean;
  forwardedFrom?: string;
  
  // Pinning
  isPinned?: boolean;
  pinnedBy?: string;
  pinnedAt?: Date;
}

interface MessageBubbleProps {
  message: GroupMessage;
  isOwn: boolean;
  showAvatar: boolean;
  showTimestamp: boolean;
  currentUserId: string;
  isHighlighted?: boolean; // Fixed: Added highlighting support
  onReply: (message: GroupMessage) => void;
  onReact: (messageId: string, emoji: string) => void;
  onEdit: (messageId: string) => void;
  onDelete: (messageId: string) => void;
  onForward: (messageId: string) => void;
  onPin: (messageId: string) => void;
  onUserPress: (userId: string) => void;
  onMediaPress?: (message: GroupMessage) => void;
  onNavigateToMessage?: (messageId: string) => void;
  // New message action props
  onLongPress?: () => void;
  onSwipeLeft?: (message: GroupMessage) => void;
  onSwipeRight?: (message: GroupMessage, event: any) => void;
  onEmojiReaction?: (messageId: string, emoji: string) => void;
  enableSwipeGestures?: boolean;
  swipeState?: number;
}

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showAvatar,
  showTimestamp,
  currentUserId,
  isHighlighted = false, // Fixed: Added highlighting parameter
  onReply,
  onReact,
  onEdit,
  onDelete,
  onForward,
  onPin,
  onUserPress,
  onMediaPress,
  onNavigateToMessage,
  onLongPress,
  onSwipeLeft,
  onSwipeRight,
  onEmojiReaction,
  enableSwipeGestures = true,
  swipeState = 0,
}) => {
  const [showReactions, setShowReactions] = useState(false);
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false); // Fixed: Added voice playback state
  const [playbackPosition, setPlaybackPosition] = useState(0); // Fixed: Added playback position
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const reactionAnim = useRef(new Animated.Value(0)).current;
  const highlightAnim = useRef(new Animated.Value(0)).current; // Fixed: Added highlight animation

  // Gesture handling
  const translateX = useRef(new Animated.Value(0)).current;
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return enableSwipeGestures && Math.abs(gestureState.dx) > 10;
      },
      onPanResponderMove: (_, gestureState) => {
        if (!enableSwipeGestures) return;

        // Limit swipe distance
        const maxSwipe = 80;
        const clampedDx = Math.max(-maxSwipe, Math.min(maxSwipe, gestureState.dx));
        translateX.setValue(clampedDx);
      },
      onPanResponderRelease: (event, gestureState) => {
        if (!enableSwipeGestures) return;

        const swipeThreshold = 50;

        if (gestureState.dx > swipeThreshold) {
          // Swipe right - show emoji reactions
          onSwipeRight?.(message, event);
        } else if (gestureState.dx < -swipeThreshold) {
          // Swipe left - reply
          onSwipeLeft?.(message);
        }

        // Reset position
        Animated.spring(translateX, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      },
    })
  ).current;

  const handleLongPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    // Use new onLongPress prop if provided, otherwise use legacy behavior
    if (onLongPress) {
      onLongPress();
    } else {
      setShowContextMenu(true);
    }

    Animated.sequence([
      Animated.timing(scaleAnim, { toValue: 0.95, duration: 100, useNativeDriver: true }),
      Animated.timing(scaleAnim, { toValue: 1, duration: 100, useNativeDriver: true }),
    ]).start();
  };

  const handleReaction = (emoji: string) => {
    // Use new onEmojiReaction prop if provided, otherwise use legacy onReact
    if (onEmojiReaction) {
      onEmojiReaction(message.id, emoji);
    } else {
      onReact(message.id, emoji);
    }

    setShowReactions(false);

    // Animate reaction
    Animated.sequence([
      Animated.timing(reactionAnim, { toValue: 1, duration: 300, useNativeDriver: true }),
      Animated.delay(1500),
      Animated.timing(reactionAnim, { toValue: 0, duration: 300, useNativeDriver: true }),
    ]).start();
  };

  // Register/unregister audio manager listener
  useEffect(() => {
    if (message.type === 'voice') {
      audioManager.registerVoiceMessageListener(message.id, (playing, position) => {
        setIsPlaying(playing);
        setPlaybackPosition(position);
      });

      return () => {
        audioManager.unregisterVoiceMessageListener(message.id);
      };
    }
  }, [message.id, message.type]);

  // Fixed: Voice playback functionality with global audio manager
  const handleVoicePlayback = async () => {
    try {
      if (!message.mediaUrl) {
        console.error('❌ No voice URL available');
        Alert.alert('Error', 'Voice message not available');
        return;
      }

      if (isPlaying) {
        // Pause current playback
        await audioManager.pauseVoiceMessage();
        console.log('⏸️ Voice playback paused');
      } else {
        // Check if this message is already the current one
        if (audioManager.isVoiceMessagePlaying(message.id)) {
          // Resume playback
          await audioManager.resumeVoiceMessage();
          console.log('▶️ Voice playback resumed');
        } else {
          // Start new playback
          await audioManager.playVoiceMessage(message.id, message.mediaUrl);
          console.log('▶️ Voice playback started');
        }
      }
    } catch (error) {
      console.error('❌ Voice playback error:', error);
      setIsPlaying(false);
      Alert.alert('Playback Error', 'Failed to play voice message. Please try again.');
    }
  };

  // Fixed: Highlight animation effect
  useEffect(() => {
    if (isHighlighted) {
      Animated.sequence([
        Animated.timing(highlightAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }),
        Animated.timing(highlightAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: false,
        }),
      ]).start();
    }
  }, [isHighlighted]);

  const renderMessageContent = () => {
    switch (message.type) {
      case 'text':
        return (
          <Text style={[styles.messageText, isOwn && styles.ownMessageText]}>
            {message.text}
          </Text>
        );
      
      case 'image':
        return (
          <TouchableOpacity
            style={styles.mediaContainer}
            onPress={() => onMediaPress?.(message)}
            onLongPress={() => onLongPress?.()} // Fixed: Added long press for media
            activeOpacity={0.8}
          >
            <Image source={{ uri: message.mediaUrl }} style={styles.messageImage} />
            {/* Download button for received images */}
            {!isOwn && (
              <TouchableOpacity style={styles.mediaDownloadButton}>
                <Ionicons name="download" size={20} color="white" />
              </TouchableOpacity>
            )}
            {/* Upload indicator for own images being sent */}
            {isOwn && message.status === 'sending' && (
              <View style={styles.mediaUploadIndicator}>
                <Ionicons name="cloud-upload" size={20} color="white" />
              </View>
            )}
            {message.text && (
              <Text style={[styles.messageText, styles.mediaCaption, isOwn && styles.ownMessageText]}>
                {message.text}
              </Text>
            )}
          </TouchableOpacity>
        );
      
      case 'video':
        return (
          <TouchableOpacity
            style={styles.mediaContainer}
            onPress={() => onMediaPress?.(message)}
            onLongPress={() => onLongPress?.()} // Fixed: Added long press for media
            activeOpacity={0.8}
          >
            <Video
              source={{ uri: message.mediaUrl! }}
              style={styles.messageVideo}
              resizeMode={ResizeMode.COVER}
              shouldPlay={false}
              useNativeControls={false}
            />
            <View style={styles.videoOverlay}>
              <Ionicons name="play-circle" size={40} color="rgba(255,255,255,0.8)" />
              {message.duration && (
                <Text style={styles.videoDuration}>
                  {Math.floor(message.duration / 60)}:{(message.duration % 60).toString().padStart(2, '0')}
                </Text>
              )}
            </View>
            {/* Download button for received videos */}
            {!isOwn && (
              <TouchableOpacity style={styles.mediaDownloadButton}>
                <Ionicons name="download" size={20} color="white" />
              </TouchableOpacity>
            )}
            {/* Upload indicator for own videos being sent */}
            {isOwn && message.status === 'sending' && (
              <View style={styles.mediaUploadIndicator}>
                <Ionicons name="cloud-upload" size={20} color="white" />
              </View>
            )}
            {message.text && (
              <Text style={[styles.messageText, styles.mediaCaption, isOwn && styles.ownMessageText]}>
                {message.text}
              </Text>
            )}
          </TouchableOpacity>
        );
      
      case 'voice':
        return (
          <View style={styles.voiceContainer}>
            <TouchableOpacity
              style={styles.voicePlayButton}
              onPress={handleVoicePlayback} // Fixed: Added playback functionality
            >
              <Ionicons
                name={isPlaying ? "pause" : "play"}
                size={20}
                color={isOwn ? COLORS.text : COLORS.primary}
              />
            </TouchableOpacity>
            <View style={styles.voiceWaveform}>
              {/* Fixed: Voice waveform visualization with proper width */}
              {Array.from({ length: 15 }).map((_, i) => {
                const progress = message.duration ? playbackPosition / message.duration : 0;
                const isActive = i < progress * 15;
                return (
                  <View
                    key={i}
                    style={[
                      styles.waveformBar,
                      {
                        height: Math.random() * 20 + 5,
                        backgroundColor: isActive
                          ? (isOwn ? COLORS.text : COLORS.primary)
                          : (isOwn ? 'rgba(255,255,255,0.3)' : 'rgba(135,206,235,0.3)')
                      }
                    ]}
                  />
                );
              })}
            </View>
            <Text style={[styles.voiceDuration, isOwn && styles.ownMessageText]}>
              {isPlaying
                ? `${Math.floor(playbackPosition / 60)}:${Math.floor(playbackPosition % 60).toString().padStart(2, '0')}`
                : message.duration
                  ? `${Math.floor(message.duration / 60)}:${(message.duration % 60).toString().padStart(2, '0')}`
                  : '0:00'
              }
            </Text>
          </View>
        );
      
      case 'document':
        return (
          <TouchableOpacity
            style={styles.documentContainer}
            onLongPress={() => onLongPress?.()} // Fixed: Added long press for documents
            activeOpacity={0.8}
          >
            <View style={styles.documentIcon}>
              <Ionicons name="document" size={24} color={isOwn ? COLORS.text : COLORS.primary} />
            </View>
            <View style={styles.documentInfo}>
              <Text style={[styles.documentName, isOwn && styles.ownMessageText]} numberOfLines={1}>
                {message.fileName || 'Document'}
              </Text>
              <Text style={[styles.documentSize, isOwn && styles.ownMessageText]}>
                {message.fileSize ? `${(message.fileSize / 1024 / 1024).toFixed(1)} MB` : 'Unknown size'}
              </Text>
            </View>
            {!isOwn && (
              <TouchableOpacity style={styles.downloadButton}>
                <Ionicons name="download" size={20} color={COLORS.primary} />
              </TouchableOpacity>
            )}
            {isOwn && message.status === 'sending' && (
              <TouchableOpacity style={styles.uploadButton}>
                <Ionicons name="cloud-upload" size={20} color={COLORS.text} />
              </TouchableOpacity>
            )}
          </TouchableOpacity>
        );
      
      case 'announcement':
        return (
          <View style={styles.announcementContainer}>
            <View style={styles.announcementHeader}>
              <Ionicons name="megaphone" size={16} color={COLORS.warning} />
              <Text style={styles.announcementLabel}>Announcement</Text>
              {message.announcementPriority && (
                <View style={[styles.priorityBadge, { backgroundColor: 
                  message.announcementPriority === 'high' ? COLORS.error :
                  message.announcementPriority === 'medium' ? COLORS.warning : COLORS.success
                }]}>
                  <Text style={styles.priorityText}>{message.announcementPriority.toUpperCase()}</Text>
                </View>
              )}
            </View>
            <Text style={[styles.messageText, styles.announcementText]}>
              {message.text}
            </Text>
          </View>
        );
      
      default:
        return (
          <Text style={[styles.messageText, isOwn && styles.ownMessageText]}>
            {message.text || 'Unsupported message type'}
          </Text>
        );
    }
  };

  const renderReplyPreview = () => {
    if (!message.replyTo) return null;

    const renderReplyMedia = () => {
      if (!message.replyTo || message.replyTo.type === 'text') return null;

      switch (message.replyTo.type) {
        case 'image':
          return (
            <Image
              source={{ uri: message.replyTo.mediaUrl }}
              style={styles.replyMediaThumbnail}
              resizeMode="cover"
            />
          );
        case 'video':
          return (
            <View style={styles.replyMediaContainer}>
              <Image
                source={{ uri: message.replyTo.mediaUrl }}
                style={styles.replyMediaThumbnail}
                resizeMode="cover"
              />
              <View style={styles.replyMediaOverlay}>
                <Ionicons name="play" size={12} color="white" />
              </View>
            </View>
          );
        case 'audio':
          return (
            <View style={[styles.replyMediaThumbnail, styles.replyAudioThumbnail]}>
              <Ionicons name="musical-notes" size={16} color={COLORS.primary} />
            </View>
          );
        case 'file':
          return (
            <View style={[styles.replyMediaThumbnail, styles.replyFileThumbnail]}>
              <Ionicons name="document" size={16} color={COLORS.primary} />
            </View>
          );
        default:
          return null;
      }
    };

    return (
      <TouchableOpacity
        style={[styles.replyPreview, {
          backgroundColor: isOwn ? 'rgba(255, 255, 255, 0.1)' : 'rgba(135, 206, 235, 0.1)'
        }]}
        onPress={() => {
          // Navigate to the original message
          if (onNavigateToMessage && message.replyTo?.messageId) {
            onNavigateToMessage(message.replyTo.messageId);
          }
        }}
        activeOpacity={0.7}
      >
        <View style={[styles.replyLine, { backgroundColor: isOwn ? COLORS.textMuted : COLORS.primary }]} />
        {renderReplyMedia()}
        <View style={styles.replyContent}>
          <Text
            style={[styles.replySender, { color: isOwn ? COLORS.textMuted : COLORS.primary }]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {message.replyTo.senderName}
          </Text>
          <Text
            style={[styles.replyText, { color: isOwn ? COLORS.textMuted : COLORS.text }]}
            numberOfLines={2}
            ellipsizeMode="tail"
          >
            {message.replyTo.type === 'text'
              ? (message.replyTo.text || '').toString()
              : (message.replyTo.text || `${message.replyTo.type.charAt(0).toUpperCase() + message.replyTo.type.slice(1)}`).toString()
            }
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderReactions = () => {
    if (!message.reactions || Object.keys(message.reactions).length === 0) return null;

    return (
      <View style={styles.reactionsContainer}>
        {Object.entries(message.reactions).map(([emoji, data]) => (
          <TouchableOpacity
            key={emoji}
            style={[
              styles.reactionBubble,
              data.users.includes(currentUserId) && styles.ownReaction
            ]}
            onPress={() => handleReaction(emoji)}
          >
            <Text style={styles.reactionEmoji}>{emoji}</Text>
            <Text style={styles.reactionCount}>{data.count}</Text>
          </TouchableOpacity>
        ))}
        <TouchableOpacity
          style={styles.addReactionButton}
          onPress={() => setShowReactions(true)}
        >
          <Ionicons name="add" size={12} color={COLORS.textMuted} />
        </TouchableOpacity>
      </View>
    );
  };

  const renderMessageStatus = () => {
    if (!isOwn) return null;

    const getStatusIcon = () => {
      switch (message.status) {
        case 'sending': return 'time-outline';
        case 'sent': return 'checkmark';
        case 'delivered': return 'checkmark-done';
        case 'read': return 'checkmark-done';
        default: return 'time-outline';
      }
    };

    const getStatusColor = () => {
      switch (message.status) {
        case 'sending': return COLORS.textMuted;
        case 'sent': return COLORS.textSecondary;
        case 'delivered': return COLORS.textSecondary;
        case 'read': return COLORS.primary;
        default: return COLORS.textMuted;
      }
    };

    return (
      <Ionicons 
        name={getStatusIcon() as any} 
        size={12} 
        color={getStatusColor()} 
        style={styles.statusIcon}
      />
    );
  };

  return (
    <Animated.View style={[styles.messageContainer, { transform: [{ scale: scaleAnim }] }]}>
      <View style={[
        message.replyTo ? styles.messageRowWithReply : styles.messageRow,
        isOwn && styles.ownMessageRow
      ]}>
        {/* Avatar */}
        {showAvatar && !isOwn && (
          <TouchableOpacity onPress={() => onUserPress(message.senderId)}>
            <Image
              source={{ uri: message.senderAvatar || 'https://via.placeholder.com/32' }}
              style={styles.messageAvatar}
            />
          </TouchableOpacity>
        )}

        {/* Message Bubble with Gesture Handling */}
        <Animated.View
          style={[
            { transform: [{ translateX }] }
          ]}
          {...(enableSwipeGestures ? panResponder.panHandlers : {})}
        >
          <Animated.View
            style={[
              styles.messageBubble,
              isOwn ? styles.ownMessageBubble : styles.otherMessageBubble,
              message.isAnnouncement && styles.announcementBubble,
              // Fixed: Add highlight animation background
              isHighlighted && {
                backgroundColor: highlightAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [
                    isOwn ? COLORS.primary : COLORS.surface,
                    '#b6a754ff' // Gold highlight color
                  ],
                }),
              },
            ]}
          >
            <Pressable
              style={{ flex: 1 }}
              onLongPress={handleLongPress}
              delayLongPress={500}
            >
          {/* Forwarded Indicator */}
          {message.isForwarded && (
            <View style={styles.forwardedIndicator}>
              <Ionicons name="arrow-forward" size={12} color={COLORS.textMuted} />
              <Text style={styles.forwardedText}>Forwarded</Text>
            </View>
          )}

          {/* Pinned Indicator */}
          {message.isPinned && (
            <View style={styles.pinnedIndicator}>
              <Ionicons name="pin" size={12} color={COLORS.primary} />
              <Text style={styles.pinnedText}>Pinned</Text>
            </View>
          )}

          {/* Sender Name (for group messages) */}
          {!isOwn && showAvatar && (
            <TouchableOpacity onPress={() => onUserPress(message.senderId)}>
              <Text style={styles.senderName}>{message.senderName}</Text>
            </TouchableOpacity>
          )}

          {/* Reply Preview */}
          {renderReplyPreview()}

          {/* Message Content */}
          {renderMessageContent()}

          {/* Message Footer */}
          <View style={styles.messageFooter}>
            {message.isEdited && (
              <Text style={[
                styles.editedText,
                { color: isOwn ? 'rgba(255,255,255,0.6)' : '#999999' }
              ]}>edited</Text>
            )}
            {showTimestamp && (
              <Text style={[
                styles.timestamp,
                { color: isOwn ? 'rgba(11, 236, 146, 0.7)' : '#2db50eff' }
              ]}>
                {message.timestamp.toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: undefined // Fixed: Let device determine 12/24 hour format
                })}
              </Text>
            )}
            {renderMessageStatus()}
          </View>

          {/* Reactions positioned at bottom right of message bubble */}
          {renderReactions()}
            </Pressable>
          </Animated.View>
        </Animated.View>

        {/* Spacer for alignment */}
        {!isOwn && <View style={styles.messageSpacer} />}
      </View>

      {/* Thread Indicator */}
      {message.threadCount && message.threadCount > 0 && (
        <TouchableOpacity style={[styles.threadIndicator, isOwn && styles.ownThreadIndicator]}>
          <Ionicons name="chatbubbles" size={14} color={COLORS.primary} />
          <Text style={styles.threadCount}>{message.threadCount} replies</Text>
        </TouchableOpacity>
      )}

      {/* Reaction Animation */}
      <Animated.View 
        style={[
          styles.reactionAnimation,
          {
            opacity: reactionAnim,
            transform: [{
              scale: reactionAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0.5, 1.5],
              }),
            }],
          },
        ]}
      >
        <Text style={styles.reactionAnimationEmoji}>❤️</Text>
      </Animated.View>

      {/* Quick Reactions Modal */}
      <Modal visible={showReactions} transparent animationType="fade">
        <TouchableOpacity 
          style={styles.reactionModalBackdrop} 
          onPress={() => setShowReactions(false)}
        >
          <View style={styles.reactionModal}>
            {['❤️', '😂', '😮', '😢', '😡', '👍', '👎', '🔥'].map((emoji) => (
              <TouchableOpacity
                key={emoji}
                style={styles.reactionOption}
                onPress={() => handleReaction(emoji)}
              >
                <Text style={styles.reactionOptionEmoji}>{emoji}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Context Menu Modal */}
      <Modal visible={showContextMenu} transparent animationType="fade">
        <TouchableOpacity 
          style={styles.contextMenuBackdrop} 
          onPress={() => setShowContextMenu(false)}
        >
          <View style={styles.contextMenu}>
            <TouchableOpacity style={styles.contextMenuItem} onPress={() => { onReply(message); setShowContextMenu(false); }}>
              <Ionicons name="arrow-undo" size={20} color={COLORS.text} />
              <Text style={styles.contextMenuText}>Reply</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.contextMenuItem} onPress={() => { onForward(message.id); setShowContextMenu(false); }}>
              <Ionicons name="arrow-forward" size={20} color={COLORS.text} />
              <Text style={styles.contextMenuText}>Forward</Text>
            </TouchableOpacity>
            
            {isOwn && (
              <TouchableOpacity style={styles.contextMenuItem} onPress={() => { onEdit(message.id); setShowContextMenu(false); }}>
                <Ionicons name="create" size={20} color={COLORS.text} />
                <Text style={styles.contextMenuText}>Edit</Text>
              </TouchableOpacity>
            )}
            
            <TouchableOpacity style={styles.contextMenuItem} onPress={() => { onPin(message.id); setShowContextMenu(false); }}>
              <Ionicons name="pin" size={20} color={COLORS.text} />
              <Text style={styles.contextMenuText}>{message.isPinned ? 'Unpin' : 'Pin'}</Text>
            </TouchableOpacity>
            
            {(isOwn || message.senderId === currentUserId) && (
              <TouchableOpacity style={[styles.contextMenuItem, styles.deleteMenuItem]} onPress={() => { onDelete(message.id); setShowContextMenu(false); }}>
                <Ionicons name="trash" size={20} color={COLORS.error} />
                <Text style={[styles.contextMenuText, styles.deleteMenuText]}>Delete</Text>
              </TouchableOpacity>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    marginVertical: 2,
    paddingHorizontal: 16,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    maxWidth: '85%',
  },
  messageRowWithReply: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    maxWidth: '92%', // Increased width for messages with replies
  },
  ownMessageRow: {
    alignSelf: 'flex-end',
    flexDirection: 'row-reverse',
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
    borderWidth: 1.5,
    borderColor: COLORS.primary,
  },
  messageBubble: {
    position: 'relative',
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxWidth: '100%',
    minWidth: 80, // Fixed: Ensure minimum width for message bubble
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  ownMessageBubble: {
    backgroundColor: COLORS.myMessageBubble,
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    backgroundColor: COLORS.otherMessageBubble,
    borderBottomLeftRadius: 4,
  },
  announcementBubble: {
    borderWidth: 2,
    borderColor: COLORS.warning,
    backgroundColor: COLORS.surface,
  },
  messageSpacer: {
    flex: 1,
  },
  senderName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#4A90E2', // Fixed: Distinct blue color for sender names
    marginBottom: 4,
  },
  messageText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 20,
  },
  ownMessageText: {
    color: COLORS.text, // Fixed: Use white text instead of black background color
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 4,
  },
  timestamp: {
    fontSize: 11,
    color: '#0aa545ff', // Fixed: Distinct gray color for timestamps
  },
  editedText: {
    fontSize: 11,
    color: COLORS.textMuted,
    fontStyle: 'italic',
  },
  statusIcon: {
    marginLeft: 2,
  },

  // Media Styles
  mediaContainer: {
    borderRadius: 8, // Fixed: Reduced border radius for tighter fit
    overflow: 'hidden',
    margin: 0, // Fixed: Remove any default margin
    padding: 0, // Fixed: Remove any default padding
  },
  messageImage: {
    width: 280,
    height: 280,
    borderRadius: 8, // Fixed: Reduced border radius to match container
    margin: 0, // Fixed: Remove margin for tighter fit
  },
  messageVideo: {
    width: 280,
    height: 280,
    borderRadius: 8, // Fixed: Reduced border radius to match container
    margin: 0, // Fixed: Remove margin for tighter fit
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  videoDuration: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    backgroundColor: 'rgba(0,0,0,0.7)',
    color: COLORS.text,
    fontSize: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  mediaCaption: {
    marginTop: 8,
  },

  // Voice Message Styles
  voiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    minWidth: 150,
    maxWidth: 250, // Fixed: Limit maximum width
  },
  voicePlayButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  voiceWaveform: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
    width: 120, // Fixed: Set fixed width instead of flex: 1 to prevent stretching
    height: 30, // Fixed: Set fixed height
    justifyContent: 'space-between', // Fixed: Distribute bars evenly
  },
  waveformBar: {
    width: 3,
    backgroundColor: COLORS.primary,
    borderRadius: 1.5,
    minHeight: 5, // Fixed: Ensure minimum height
  },
  voiceDuration: {
    fontSize: 12,
    color: COLORS.textMuted,
  },

  // Document Styles
  documentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    minWidth: 200,
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  documentInfo: {
    flex: 1,
  },
  documentName: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  documentSize: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  downloadButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  uploadButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaDownloadButton: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaUploadIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Announcement Styles
  announcementContainer: {
    gap: 8,
  },
  announcementHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  announcementLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.warning,
    textTransform: 'uppercase',
  },
  priorityBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '700',
    color: COLORS.background,
  },
  announcementText: {
    fontSize: 16,
    fontWeight: '500',
  },

  // Reply Styles
  replyPreview: {
    flexDirection: 'row',
    marginBottom: 8,
    paddingLeft: 8,
    alignItems: 'flex-start',
    minHeight: 40,
    minWidth: 200, // Fixed: Ensure minimum width for reply preview
    alignSelf: 'stretch', // Fixed: Take full available width
  },
  replyLine: {
    width: 3,
    borderRadius: 1.5,
    marginRight: 8,
    alignSelf: 'stretch',
  },
  replyContent: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    minWidth: 150, // Fixed: Ensure minimum width for reply content
  },
  replySender: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.primary,
    marginBottom: 2,
    flexShrink: 0,
  },
  replyText: {
    fontSize: 13,
    color: COLORS.textMuted,
    lineHeight: 16,
    flex: 1,
  },

  // Reactions Styles
  reactionsContainer: {
    position: 'absolute',
    bottom: -8,
    right: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
    zIndex: 10,
  },
  reactionBubble: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.reactionBackground,
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    gap: 4,
  },
  ownReaction: {
    backgroundColor: COLORS.primary,
  },
  reactionEmoji: {
    fontSize: 14,
  },
  reactionCount: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.text,
  },
  addReactionButton: {
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Thread Styles
  threadIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginLeft: 40,
    gap: 4,
  },
  ownThreadIndicator: {
    alignSelf: 'flex-end',
    marginRight: 40,
    marginLeft: 0,
  },
  threadCount: {
    fontSize: 12,
    color: COLORS.primary,
    fontWeight: '500',
  },

  // Forwarded Styles
  forwardedIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 4,
  },
  forwardedText: {
    fontSize: 11,
    color: COLORS.textMuted,
    fontStyle: 'italic',
  },

  // Pinned Styles
  pinnedIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 4,
  },
  pinnedText: {
    fontSize: 11,
    color: COLORS.primary,
    fontWeight: '500',
  },

  // Animation Styles
  reactionAnimation: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginLeft: -15,
    marginTop: -15,
  },
  reactionAnimationEmoji: {
    fontSize: 30,
  },

  // Modal Styles
  reactionModalBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  reactionModal: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  reactionOption: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
  },
  reactionOptionEmoji: {
    fontSize: 24,
  },

  // Context Menu Styles
  contextMenuBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contextMenu: {
    backgroundColor: COLORS.surface,
    borderRadius: 16,
    paddingVertical: 8,
    minWidth: 200,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  contextMenuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  contextMenuText: {
    fontSize: 16,
    color: COLORS.text,
    fontWeight: '500',
  },
  deleteMenuItem: {
    borderTopWidth: 1,
    borderTopColor: COLORS.surfaceLight,
  },
  deleteMenuText: {
    color: COLORS.error,
  },

  // Reply Media Styles
  replyMediaContainer: {
    position: 'relative',
    marginRight: 8,
  },
  replyMediaThumbnail: {
    width: 32,
    height: 32,
    borderRadius: 6,
    backgroundColor: COLORS.surfaceLight,
  },
  replyMediaOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 6,
  },
  replyAudioThumbnail: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary + '20',
  },
  replyFileThumbnail: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary + '20',
  },
});
