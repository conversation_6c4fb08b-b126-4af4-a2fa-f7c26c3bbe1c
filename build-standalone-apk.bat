@echo off
echo =====================================================
echo BUILDING STANDALONE APK FOR IRACHAT
echo =====================================================
echo.
echo This script will:
echo 1. Set up environment for D: drive cache
echo 2. Check EAS CLI login status
echo 3. Build standalone APK using EAS Build
echo 4. Download the APK when ready
echo 5. Provide installation instructions
echo.
pause

echo.
echo Step 1: Setting up D: drive cache environment...
echo =====================================================
call set-env.bat

echo.
echo Step 2: Checking EAS CLI installation and login...
echo =====================================================

REM Check if EAS CLI is installed
where eas >nul 2>nul
if %errorlevel% neq 0 (
    echo EAS CLI not found. Installing...
    npm install -g eas-cli
    if %errorlevel% neq 0 (
        echo Failed to install EAS CLI. Please install manually:
        echo npm install -g eas-cli
        pause
        exit /b 1
    )
)

echo EAS CLI found. Checking login status...
eas whoami
if %errorlevel% neq 0 (
    echo You are not logged in to EAS. Please login:
    echo.
    eas login
    if %errorlevel% neq 0 (
        echo Login failed. Please try again.
        pause
        exit /b 1
    )
)

echo.
echo Step 3: Building standalone APK...
echo =====================================================
echo Building APK with profile: standalone
echo This will create a production-ready APK that doesn't need Expo Go
echo.

eas build --platform android --profile standalone --non-interactive

if %errorlevel% neq 0 (
    echo.
    echo Build failed! Common issues:
    echo 1. Network connection problems
    echo 2. EAS account issues
    echo 3. Configuration errors
    echo.
    echo Try running manually: eas build --platform android --profile standalone
    pause
    exit /b 1
)

echo.
echo =====================================================
echo BUILD SUBMITTED SUCCESSFULLY!
echo =====================================================
echo.
echo Your standalone APK is being built in the cloud.
echo This usually takes 10-20 minutes.
echo.
echo To check build status:
echo   eas build:list
echo.
echo To download when ready:
echo   eas build:download --platform android --profile standalone
echo.
echo INSTALLATION INSTRUCTIONS:
echo =====================================================
echo 1. Download the APK when build completes
echo 2. Transfer APK to your phone via:
echo    - USB cable
echo    - Email attachment
echo    - Cloud storage (Google Drive, Dropbox)
echo    - ADB: adb install path/to/your-app.apk
echo.
echo 3. On your phone:
echo    - Enable "Install from unknown sources" in Settings
echo    - Tap the APK file to install
echo    - Grant permissions when prompted
echo.
echo 4. The app will install as "IraChat" and can be opened
echo    directly without Expo Go!
echo.
pause
