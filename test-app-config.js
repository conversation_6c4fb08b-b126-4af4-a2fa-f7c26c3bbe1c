#!/usr/bin/env node

// 🔥 APP CONFIGURATION TEST
// Tests that the app configuration is working without starting the full server

require('dotenv').config();

console.log('🔥 Testing IraChat App Configuration');
console.log('====================================\n');

// Test 1: Environment Variables
console.log('📋 Step 1: Testing Environment Variables...');
const requiredEnvVars = [
  'EXPO_PUBLIC_FIREBASE_API_KEY',
  'EXPO_PUBLIC_FIREBASE_PROJECT_ID',
  'EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET',
  'EXPO_PUBLIC_FIREBASE_APP_ID'
];

let envSuccess = true;
requiredEnvVars.forEach(varName => {
  if (process.env[varName]) {
    console.log(`✅ ${varName}: Configured`);
  } else {
    console.log(`❌ ${varName}: Missing`);
    envSuccess = false;
  }
});

// Test 2: Firebase Configuration Object
console.log('\n📋 Step 2: Testing Firebase Configuration...');
try {
  const firebaseConfig = {
    apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
    authDomain: process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN,
    projectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
    storageBucket: process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.EXPO_PUBLIC_FIREBASE_APP_ID
  };

  // Validate configuration
  if (firebaseConfig.apiKey && firebaseConfig.projectId && firebaseConfig.appId) {
    console.log('✅ Firebase configuration object is valid');
    console.log(`   Project: ${firebaseConfig.projectId}`);
    console.log(`   App ID: ${firebaseConfig.appId}`);
  } else {
    console.log('❌ Firebase configuration is incomplete');
    envSuccess = false;
  }
} catch (error) {
  console.log('❌ Error creating Firebase configuration:', error.message);
  envSuccess = false;
}

// Test 3: File Structure
console.log('\n📋 Step 3: Testing File Structure...');
const fs = require('fs');
const path = require('path');

const requiredFiles = [
  'android/app/google-services.json',
  'app.json',
  'app.config.js',
  'firebase.json',
  'firestore.rules',
  'storage.rules'
];

let filesSuccess = true;
requiredFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${filePath}: Exists`);
  } else {
    console.log(`❌ ${filePath}: Missing`);
    filesSuccess = false;
  }
});

// Test 4: Package Configuration
console.log('\n📋 Step 4: Testing Package Configuration...');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const appConfig = require('./app.config.js');
  
  console.log('✅ package.json: Valid');
  console.log('✅ app.config.js: Valid');
  console.log(`   App Name: ${appConfig.expo.name}`);
  console.log(`   Android Package: ${appConfig.expo.android.package}`);
  console.log(`   iOS Bundle: ${appConfig.expo.ios.bundleIdentifier}`);
} catch (error) {
  console.log('❌ Error reading package configuration:', error.message);
  filesSuccess = false;
}

// Test 5: Google Services Configuration
console.log('\n📋 Step 5: Testing Google Services...');
try {
  const googleServices = JSON.parse(fs.readFileSync('android/app/google-services.json', 'utf8'));
  
  if (googleServices.project_info && googleServices.client) {
    console.log('✅ google-services.json: Valid structure');
    console.log(`   Project ID: ${googleServices.project_info.project_id}`);
    console.log(`   Package Name: ${googleServices.client[0].android_client_info.package_name}`);
    
    // Check if package names match
    const appConfig = require('./app.config.js');
    if (googleServices.client[0].android_client_info.package_name === appConfig.expo.android.package) {
      console.log('✅ Package names match between google-services.json and app.config.js');
    } else {
      console.log('⚠️  Package name mismatch detected');
    }
  } else {
    console.log('❌ google-services.json: Invalid structure');
    filesSuccess = false;
  }
} catch (error) {
  console.log('❌ Error reading google-services.json:', error.message);
  filesSuccess = false;
}

// Final Results
console.log('\n🎯 Configuration Test Results:');
console.log('==============================');

if (envSuccess && filesSuccess) {
  console.log('✅ ALL TESTS PASSED!');
  console.log('\n🚀 Your IraChat configuration is ready!');
  console.log('\n📝 Next Steps:');
  console.log('1. Login to Firebase: firebase login');
  console.log('2. Deploy rules: firebase deploy --only firestore:rules,storage');
  console.log('3. Start development: npm run start');
  console.log('4. Build for device: eas build --profile development');
  
  console.log('\n🔗 Quick Links:');
  console.log('• Firebase Console: https://console.firebase.google.com/project/irachat-production');
  console.log('• Expo Dashboard: https://expo.dev/accounts/irachat/projects/irachat');
  
} else {
  console.log('❌ Some tests failed - please fix the issues above');
  
  if (!envSuccess) {
    console.log('\n🔧 Environment Issues:');
    console.log('• Check your .env file');
    console.log('• Ensure all Firebase variables are set');
  }
  
  if (!filesSuccess) {
    console.log('\n🔧 File Issues:');
    console.log('• Ensure all required files exist');
    console.log('• Check file permissions');
    console.log('• Verify JSON file syntax');
  }
}

console.log('\n💡 Note: This test bypasses Firebase CLI authentication');
console.log('   Your app configuration is tested independently of Firebase CLI login status');
