// 🖼️ FULL-FEATURED IMAGE VIEWER WITH ZOOM, PAN, AND GESTURES
// Complete WhatsApp-style image viewing experience

import React, { useState, useRef } from 'react';
import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  Animated,
  PanResponder,
  Dimensions,
  StatusBar,
  StyleSheet,
  Text,
} from 'react-native';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface ImageViewerProps {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
  caption?: string;
}

export const ImageViewer: React.FC<ImageViewerProps> = ({
  visible,
  imageUri,
  onClose,
  caption,
}) => {
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const scale = useRef(new Animated.Value(1)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const lastScale = useRef(1);
  const lastTranslateX = useRef(0);
  const lastTranslateY = useRef(0);

  // Get image dimensions when loaded
  const onImageLoad = (event: any) => {
    const { width: imgWidth, height: imgHeight } = event.nativeEvent.source;
    
    // Calculate scaled dimensions to fit screen
    const aspectRatio = imgWidth / imgHeight;
    let displayWidth = screenWidth;
    let displayHeight = screenWidth / aspectRatio;
    
    if (displayHeight > screenHeight * 0.8) {
      displayHeight = screenHeight * 0.8;
      displayWidth = displayHeight * aspectRatio;
    }
    
    setImageSize({ width: displayWidth, height: displayHeight });
  };

  // Pan responder for gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        // Store current values
        lastScale.current = (scale as any)._value;
        lastTranslateX.current = (translateX as any)._value;
        lastTranslateY.current = (translateY as any)._value;
      },
      onPanResponderMove: (evt, gestureState) => {
        const numberOfTouches = (evt.nativeEvent as any).numberOfTouches;
        
        if (numberOfTouches === 2) {
          // Pinch to zoom
          const distance = Math.sqrt(
            Math.pow(evt.nativeEvent.touches[0].pageX - evt.nativeEvent.touches[1].pageX, 2) +
            Math.pow(evt.nativeEvent.touches[0].pageY - evt.nativeEvent.touches[1].pageY, 2)
          );
          
          // Simple zoom calculation (you can make this more sophisticated)
          const newScale = Math.max(0.5, Math.min(3, lastScale.current * (distance / 200)));
          scale.setValue(newScale);
        } else if (numberOfTouches === 1 && lastScale.current > 1) {
          // Pan when zoomed in
          const maxTranslateX = (imageSize.width * lastScale.current - screenWidth) / 2;
          const maxTranslateY = (imageSize.height * lastScale.current - screenHeight) / 2;
          
          const newTranslateX = Math.max(
            -maxTranslateX,
            Math.min(maxTranslateX, lastTranslateX.current + gestureState.dx)
          );
          const newTranslateY = Math.max(
            -maxTranslateY,
            Math.min(maxTranslateY, lastTranslateY.current + gestureState.dy)
          );
          
          translateX.setValue(newTranslateX);
          translateY.setValue(newTranslateY);
        }
      },
      onPanResponderRelease: () => {
        // Snap back if zoomed out too much
        if ((scale as any)._value < 1) {
          Animated.parallel([
            Animated.spring(scale, { toValue: 1, useNativeDriver: true }),
            Animated.spring(translateX, { toValue: 0, useNativeDriver: true }),
            Animated.spring(translateY, { toValue: 0, useNativeDriver: true }),
          ]).start();
        }
      },
    })
  ).current;

  // Double tap to zoom
  const handleDoublePress = () => {
    const isZoomedIn = (scale as any)._value > 1;
    
    Animated.parallel([
      Animated.spring(scale, {
        toValue: isZoomedIn ? 1 : 2,
        useNativeDriver: true,
      }),
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
      }),
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
      }),
    ]).start();
  };

  // Reset zoom when modal opens
  const resetZoom = () => {
    scale.setValue(1);
    translateX.setValue(0);
    translateY.setValue(0);
    lastScale.current = 1;
    lastTranslateX.current = 0;
    lastTranslateY.current = 0;
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onShow={resetZoom}
      onRequestClose={onClose}
    >
      <StatusBar backgroundColor="black" barStyle="light-content" />
      <TouchableOpacity
        style={styles.container}
        activeOpacity={1}
        onPress={onClose}
      >
        {/* Image Container */}
        <View style={styles.imageContainer} {...panResponder.panHandlers}>
          <TouchableOpacity
            activeOpacity={1}
            onPress={handleDoublePress}
            style={styles.imageTouchable}
          >
            <Animated.View
              style={[
                styles.imageWrapper,
                {
                  transform: [
                    { scale },
                    { translateX },
                    { translateY },
                  ],
                },
              ]}
            >
              <Image
                source={{ uri: imageUri }}
                style={[
                  styles.image,
                  imageSize.width > 0 && {
                    width: imageSize.width,
                    height: imageSize.height,
                  },
                ]}
                onLoad={onImageLoad}
                resizeMode="contain"
              />
            </Animated.View>
          </TouchableOpacity>
        </View>

        {/* Caption */}
        {caption && (
          <View style={styles.captionContainer}>
            <Text style={styles.captionText}>{caption}</Text>
          </View>
        )}
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    flex: 1,
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginHorizontal: 16,
  },
  headerSpacer: {
    width: 40,
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageTouchable: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  imageWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: screenWidth,
    height: screenHeight * 0.8,
  },
  captionContainer: {
    position: 'absolute',
    bottom: 50,
    left: 16,
    right: 16,
    alignItems: 'center',
  },
  captionText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
});

export default ImageViewer;
