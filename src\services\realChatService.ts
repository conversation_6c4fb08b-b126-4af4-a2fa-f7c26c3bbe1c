import {
  doc,
  getDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  collection,
  query,
  where,
  getDocs,
  orderBy,
  limit,
  serverTimestamp,
  writeBatch,
  arrayUnion,
  arrayRemove,
  increment
} from 'firebase/firestore';
import {
  ref,
  deleteObject
} from 'firebase/storage';
import { db, storage } from './firebaseSimple';
import { offlineDatabaseService, LocalChat } from './offlineDatabase';
import { memoryCacheService } from './memoryCache';
import { networkStateManager } from './networkStateManager';
import { Chat } from '../types';

// Real Firebase Collections Structure
const COLLECTIONS = {
  CHATS: 'individual_chats',
  INDIVIDUAL_CHATS: 'individual_chats',
  MESSAGES: 'messages',
  MEDIA: 'shared_media',
  THREADS: 'message_threads',
  SCHEDULED: 'scheduled_messages',
  SETTINGS: 'chat_settings',
  TEMPLATES: 'message_templates',
  USERS: 'users',
  GROUPS: 'groups',
  // TRANSLATIONS removed per user request
};

export interface ChatData {
  id: string;
  name?: string;
  avatar?: string;
  participants: string[]; // Changed from participantIds to match Chat interface
  participantName?: string;
  participantAvatar?: string;
  isGroup: boolean;
  groupName?: string;
  groupAvatar?: string;
  lastMessage?: {
    text: string;
    timestamp: any;
    senderId: string;
  };
  messageCount: number;
  mediaCount: number;
  timestamp: string; // Added to match Chat interface
  createdAt: Date;
  updatedAt: Date;
}

export interface ChatMessage {
  id: string;
  chatId: string;
  senderId: string;
  text?: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice';
  mediaUrl?: string;
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read';
}

export interface ChatClearOptions {
  clearMessages: boolean;
  clearMedia: boolean;
  clearAll: boolean;
}

// Real Chat Interface
interface _RealChat {
  id: string;
  participants: string[];
  participantNames: { [userId: string]: string };
  participantAvatars: { [userId: string]: string };
  lastMessage: string;
  lastMessageTimestamp: any;
  messageCount: number;
  createdAt: any;
  updatedAt: any;
}

// Real Message Thread Interface
interface RealMessageThread {
  id: string;
  originalMessageId: string;
  chatId: string;
  replies: string[]; // Array of reply message IDs
  participantCount: number;
  createdAt: any;
}

// Real Scheduled Message Interface
interface RealScheduledMessage {
  id: string;
  chatId: string;
  senderId: string;
  content: string;
  type: string;
  mediaUrl?: string;
  scheduledFor: any;
  status: 'pending' | 'sent' | 'cancelled';
}

// Real Chat Settings Interface
interface _RealChatSettings {
  id: string;
  chatId: string;
  userId: string;
  notifications: boolean;
  encryption: boolean;
  readReceipts: boolean;
  typingIndicators: boolean;
  lastSeen: boolean;
}

class RealChatService {
  private isInitialized = false;
  private chatCache: Map<string, LocalChat> = new Map();
  private syncQueue: Set<string> = new Set();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();
      await this.loadChatsIntoCache();

      // Set up network state listener for sync
      networkStateManager.addListener('realChatService', this.handleNetworkStateChange.bind(this), 5);

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async loadChatsIntoCache(): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync('SELECT * FROM chats WHERE isDeleted = 0');

      result.forEach((row: any) => {
        const chat = this.rowToLocalChat(row);
        this.chatCache.set(chat.localId, chat);
        memoryCacheService.setChat(chat.localId, chat);
      });
    } catch (error) {
      // Continue without cache if loading fails
    }
  }

  private rowToLocalChat(row: any): LocalChat {
    return {
      localId: row.localId,
      id: row.id,
      name: row.name,
      avatar: row.avatar,
      isGroup: Boolean(row.isGroup),
      description: row.description,
      createdBy: row.createdBy,
      participants: row.participantIds ? JSON.parse(row.participantIds) : [],
      timestamp: row.timestamp || new Date().toISOString(),
      lastMessageId: row.lastMessageId,
      lastMessageText: row.lastMessageText,
      lastMessageTimestamp: row.lastMessageTime,
      unreadCount: row.unreadCount || 0,
      isPinned: Boolean(row.isPinned),
      pinnedAt: row.pinnedAt,
      isArchived: Boolean(row.isArchived),
      archivedAt: row.archivedAt,
      isMuted: Boolean(row.isMuted),
      mutedUntil: row.mutedUntil,
      syncStatus: {
        status: row.syncStatus || 'pending',
        lastSyncAttempt: row.lastSyncAttempt,
        retryCount: row.retryCount || 0,
        errorMessage: row.errorMessage,
      },
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      isDeleted: Boolean(row.isDeleted),
      deletedAt: row.deletedAt,
    };
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected && this.syncQueue.size > 0) {
      this.processSyncQueue();
    }
  }

  private async processSyncQueue(): Promise<void> {
    if (!networkStateManager.isOnline() || this.syncQueue.size === 0) return;

    const chatIds = Array.from(this.syncQueue);
    this.syncQueue.clear();

    for (const chatId of chatIds) {
      try {
        await this.syncChatWithFirebase(chatId);
      } catch (error) {
        // Re-add to queue for retry
        this.syncQueue.add(chatId);
      }
    }
  }

  private async syncChatWithFirebase(chatId: string): Promise<void> {
    const localChat = this.chatCache.get(chatId);
    if (!localChat) return;

    try {
      const chatRef = doc(db, 'chats', localChat.id || chatId);
      await setDoc(chatRef, {
        participants: localChat.participants,
        participantNames: {},
        participantAvatars: {},
        lastMessage: localChat.lastMessageText || '',
        lastMessageTimestamp: localChat.lastMessageTimestamp ? new Date(localChat.lastMessageTimestamp) : serverTimestamp(),
        messageCount: localChat.unreadCount,
        createdAt: new Date(localChat.createdAt),
        updatedAt: serverTimestamp(),
      });

      // Update sync status
      localChat.syncStatus.status = 'synced';
      localChat.syncStatus.lastSyncAttempt = Date.now();
      await this.updateChatInDatabase(localChat);
    } catch (error) {
      localChat.syncStatus.status = 'failed';
      localChat.syncStatus.retryCount++;
      await this.updateChatInDatabase(localChat);
      throw error;
    }
  }

  private async updateChatInDatabase(chat: LocalChat): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE chats SET
        syncStatus = ?, lastSyncAttempt = ?, retryCount = ?, errorMessage = ?, updatedAt = ?
      WHERE localId = ?
    `, [
      chat.syncStatus.status,
      chat.syncStatus.lastSyncAttempt || null,
      chat.syncStatus.retryCount,
      chat.syncStatus.errorMessage || null,
      Date.now(),
      chat.localId
    ]);
  }

  /**
   * Get all chats for a user (with offline support)
   */
  async getUserChats(userId: string): Promise<{ success: boolean; chats?: ChatData[]; error?: string }> {
    try {
      // Try offline first
      if (!networkStateManager.isOnline()) {
        return this.getUserChatsOffline(userId);
      }

      // Try online with fallback to offline
      try {
        const chatsRef = collection(db, 'chats');
        const q = query(
          chatsRef,
          where('participants', 'array-contains', userId),
          orderBy('updatedAt', 'desc')
        );

        const querySnapshot = await getDocs(q);
        const chats: ChatData[] = querySnapshot.docs.map(doc => {
          const rawChatData = doc.data() as _RealChat;

          return {
            id: doc.id,
            name: rawChatData.participantNames ? Object.values(rawChatData.participantNames)[0] : undefined,
            avatar: rawChatData.participantAvatars ? Object.values(rawChatData.participantAvatars)[0] : undefined,
            participants: rawChatData.participants || [],
            isGroup: rawChatData.participants ? rawChatData.participants.length > 2 : false,
            lastMessage: rawChatData.lastMessage ? {
              text: rawChatData.lastMessage,
              timestamp: rawChatData.lastMessageTimestamp,
              senderId: rawChatData.participants?.[0] || ''
            } : undefined,
            messageCount: rawChatData.messageCount || 0,
            mediaCount: 0,
            timestamp: rawChatData.createdAt?.toDate()?.toISOString() || new Date().toISOString(),
            createdAt: rawChatData.createdAt?.toDate() || new Date(),
            updatedAt: rawChatData.updatedAt?.toDate() || new Date(),
          };
        });

        // Cache the results offline
        await this.cacheChatsOffline(chats, userId);

        return { success: true, chats };
      } catch (onlineError) {
        // Fallback to offline
        return this.getUserChatsOffline(userId);
      }
    } catch (error) {
      return { success: false, error: 'Failed to get user chats' };
    }
  }

  private async getUserChatsOffline(userId: string): Promise<{ success: boolean; chats?: ChatData[]; error?: string }> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT * FROM chats
        WHERE participantIds LIKE ? AND isDeleted = 0
        ORDER BY updatedAt DESC
      `, [`%"${userId}"%`]);

      const chats: ChatData[] = result.map((row: any) => ({
        id: row.id || row.localId,
        name: row.name,
        avatar: row.avatar,
        participants: row.participantIds ? JSON.parse(row.participantIds) : [],
        isGroup: Boolean(row.isGroup),
        lastMessage: row.lastMessage ? {
          text: row.lastMessage,
          timestamp: row.lastMessageTime,
          senderId: row.lastMessageSender || ''
        } : undefined,
        messageCount: row.messageCount || 0,
        mediaCount: row.mediaCount || 0,
        timestamp: row.timestamp || new Date().toISOString(),
        createdAt: new Date(row.createdAt),
        updatedAt: new Date(row.updatedAt),
      }));

      return { success: true, chats };
    } catch (error) {
      return { success: false, error: 'Failed to get offline chats' };
    }
  }

  private async cacheChatsOffline(chats: ChatData[], _userId: string): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();

      for (const chat of chats) {
        await db.runAsync(`
          INSERT OR REPLACE INTO chats (
            id, name, avatar, isGroup,
            lastMessage, lastMessageTime, unreadCount,
            createdAt, updatedAt, isDeleted, syncStatus
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          chat.id,
          chat.name || null,
          chat.avatar || null,
          chat.isGroup ? 1 : 0,
          chat.lastMessage?.text || null,
          chat.lastMessage?.timestamp || null,
          chat.messageCount || 0,
          chat.createdAt.getTime(),
          chat.updatedAt.getTime(),
          0,
          'synced'
        ]);
      }
    } catch (error) {
      // Continue without caching if it fails
    }
  }

  /**
   * Get chat by ID (with offline support)
   */
  async getChatById(chatId: string): Promise<ChatData | null> {
    try {
      // Try cache first
      const cachedChat = memoryCacheService.getChat(chatId);
      if (cachedChat) {
        return this.localChatToChatData(cachedChat as LocalChat);
      }

      // Try offline database
      const localChat = await this.getChatFromDatabase(chatId);
      if (localChat) {
        memoryCacheService.setChat(chatId, localChat);
        return this.localChatToChatData(localChat);
      }

      // Try online if available
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', chatId);
          const chatDoc = await getDoc(chatRef);

          if (chatDoc.exists()) {
            const data = chatDoc.data();
            const chatData: ChatData = {
              id: chatDoc.id,
              name: data.name,
              avatar: data.avatar,
              participants: data.participants || [],
              isGroup: data.isGroup || false,
              lastMessage: data.lastMessage ? {
                text: data.lastMessage,
                timestamp: data.lastMessageTimestamp,
                senderId: data.lastMessageSenderId || ''
              } : undefined,
              messageCount: data.messageCount || 0,
              mediaCount: data.mediaCount || 0,
              timestamp: data.createdAt?.toDate()?.toISOString() || new Date().toISOString(),
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date(),
            };

            // Cache offline
            await this.cacheChatsOffline([chatData], '');
            return chatData;
          }
        } catch (onlineError) {
          // Continue to return null if offline data not found
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private async getChatFromDatabase(chatId: string): Promise<LocalChat | null> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getFirstAsync(`
        SELECT * FROM chats WHERE localId = ? OR id = ?
      `, [chatId, chatId]);

      if (result) {
        return this.rowToLocalChat(result as any);
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  private localChatToChatData(localChat: LocalChat): ChatData {
    return {
      id: localChat.id || localChat.localId,
      name: localChat.name,
      avatar: localChat.avatar,
      participants: localChat.participants,
      isGroup: localChat.isGroup,
      lastMessage: localChat.lastMessageText ? {
        text: localChat.lastMessageText,
        timestamp: localChat.lastMessageTimestamp,
        senderId: ''
      } : undefined,
      messageCount: localChat.unreadCount,
      mediaCount: 0,
      timestamp: localChat.timestamp,
      createdAt: new Date(localChat.createdAt),
      updatedAt: new Date(localChat.updatedAt),
    };
  }

  /**
   * Get messages for a specific chat (with offline support)
   */
  async getChatMessages(
    chatId: string,
    limitCount: number = 50
  ): Promise<{ success: boolean; messages?: ChatMessage[]; error?: string }> {
    try {
      // Try offline first
      if (!networkStateManager.isOnline()) {
        return this.getChatMessagesOffline(chatId, limitCount);
      }

      // Try online with fallback to offline
      try {
        const messagesRef = collection(db, `chats/${chatId}/messages`);
        const q = query(
          messagesRef,
          orderBy('timestamp', 'desc'),
          limit(limitCount)
        );

        const querySnapshot = await getDocs(q);
        const messages: ChatMessage[] = querySnapshot.docs.map(doc => ({
          id: doc.id,
          chatId,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate() || new Date(),
        })) as ChatMessage[];

        // Cache messages offline
        await this.cacheMessagesOffline(messages);

        return { success: true, messages: messages.reverse() };
      } catch (onlineError) {
        // Fallback to offline
        return this.getChatMessagesOffline(chatId, limitCount);
      }
    } catch (error) {
      return { success: false, error: 'Failed to get chat messages' };
    }
  }

  private async getChatMessagesOffline(chatId: string, limitCount: number): Promise<{ success: boolean; messages?: ChatMessage[]; error?: string }> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT * FROM messages
        WHERE chatId = ? AND isDeleted = 0
        ORDER BY timestamp DESC
        LIMIT ?
      `, [chatId, limitCount]);

      const messages: ChatMessage[] = result.map((row: any) => ({
        id: row.id || row.localId,
        chatId: row.chatId,
        senderId: row.senderId,
        text: row.text,
        type: row.type as any,
        mediaUrl: row.mediaUrl,
        timestamp: new Date(row.timestamp),
        status: row.status as any,
      }));

      return { success: true, messages: messages.reverse() };
    } catch (error) {
      return { success: false, error: 'Failed to get offline messages' };
    }
  }

  private async cacheMessagesOffline(messages: ChatMessage[]): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();

      for (const message of messages) {
        await db.runAsync(`
          INSERT OR REPLACE INTO messages (
            localId, id, chatId, senderId, text, type, mediaUrl,
            timestamp, status, isDeleted, syncStatus, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          message.id,
          message.id,
          message.chatId,
          message.senderId,
          message.text || null,
          message.type,
          message.mediaUrl || null,
          message.timestamp.getTime(),
          message.status,
          0,
          'synced',
          message.timestamp.getTime(),
          Date.now()
        ]);
      }
    } catch (error) {
      // Continue without caching if it fails
    }
  }

  /**
   * Clear all messages from a chat (with offline support)
   */
  async clearChatMessages(chatId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Clear offline messages first
      await this.clearChatMessagesOffline(chatId);

      // If online, also clear from Firebase
      if (networkStateManager.isOnline()) {
        try {
          const messagesRef = collection(db, `chats/${chatId}/messages`);
          const querySnapshot = await getDocs(messagesRef);

          const batch = writeBatch(db);

          querySnapshot.docs.forEach((doc) => {
            batch.delete(doc.ref);
          });

          await batch.commit();

          // Update chat metadata
          const chatRef = doc(db, 'chats', chatId);
          await updateDoc(chatRef, {
            messageCount: 0,
            lastMessage: null,
            updatedAt: serverTimestamp(),
          });
        } catch (onlineError) {
          // Continue if offline clearing succeeded
        }
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to clear chat messages' };
    }
  }

  private async clearChatMessagesOffline(chatId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    // Mark messages as deleted instead of actually deleting them
    await db.runAsync(`
      UPDATE messages SET isDeleted = 1, deletedAt = ? WHERE chatId = ?
    `, [Date.now(), chatId]);

    // Update chat metadata
    await db.runAsync(`
      UPDATE chats SET
        messageCount = 0,
        lastMessage = NULL,
        lastMessageTime = NULL,
        updatedAt = ?
      WHERE localId = ? OR id = ?
    `, [Date.now(), chatId, chatId]);
  }

  /**
   * Clear only media files from a chat (with offline support)
   */
  async clearChatMedia(chatId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Clear offline media first
      await this.clearChatMediaOffline(chatId);

      // If online, also clear from Firebase
      if (networkStateManager.isOnline()) {
        try {
          const messagesRef = collection(db, `chats/${chatId}/messages`);
          const q = query(
            messagesRef,
            where('type', 'in', ['image', 'video', 'audio', 'document', 'voice'])
          );

          const querySnapshot = await getDocs(q);
          const batch = writeBatch(db);

          querySnapshot.docs.forEach((doc) => {
            batch.delete(doc.ref);
          });

          await batch.commit();

          // Update media count
          const chatRef = doc(db, 'chats', chatId);
          await updateDoc(chatRef, {
            mediaCount: 0,
            updatedAt: serverTimestamp(),
          });
        } catch (onlineError) {
          // Continue if offline clearing succeeded
        }
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to clear chat media' };
    }
  }

  private async clearChatMediaOffline(chatId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    // Mark media messages as deleted
    await db.runAsync(`
      UPDATE messages SET isDeleted = 1, deletedAt = ?
      WHERE chatId = ? AND type IN ('image', 'video', 'audio', 'document', 'voice')
    `, [Date.now(), chatId]);

    // Update chat media count
    await db.runAsync(`
      UPDATE chats SET mediaCount = 0, updatedAt = ?
      WHERE localId = ? OR id = ?
    `, [Date.now(), chatId, chatId]);
  }

  /**
   * Clear entire chat (messages and metadata) (with offline support)
   */
  async clearChatCompletely(chatId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // First clear all messages
      const messagesResult = await this.clearChatMessages(chatId);
      if (!messagesResult.success) {
        return messagesResult;
      }

      // Clear offline chat
      await this.deleteChatOffline(chatId);

      // If online, also delete from Firebase
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', chatId);
          await deleteDoc(chatRef);
        } catch (onlineError) {
          // Continue if offline deletion succeeded
        }
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to clear chat completely' };
    }
  }

  private async deleteChatOffline(chatId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    // Mark chat as deleted
    await db.runAsync(`
      UPDATE chats SET isDeleted = 1, deletedAt = ?, updatedAt = ?
      WHERE localId = ? OR id = ?
    `, [Date.now(), Date.now(), chatId, chatId]);

    // Remove from cache
    this.chatCache.delete(chatId);
    memoryCacheService.deleteChat(chatId);
  }

  /**
   * Clear multiple chats with different options
   */
  async clearMultipleChats(
    chatIds: string[],
    options: ChatClearOptions
  ): Promise<{ success: boolean; results?: { [chatId: string]: boolean }; error?: string }> {
    try {
      const results: { [chatId: string]: boolean } = {};
      
      for (const chatId of chatIds) {
        try {
          if (options.clearAll) {
            const result = await this.clearChatCompletely(chatId);
            results[chatId] = result.success;
          } else if (options.clearMessages && options.clearMedia) {
            const result = await this.clearChatMessages(chatId);
            results[chatId] = result.success;
          } else if (options.clearMessages) {
            // Clear only text messages, keep media
            const messagesRef = collection(db, `chats/${chatId}/messages`);
            const q = query(messagesRef, where('type', '==', 'text'));
            const querySnapshot = await getDocs(q);
            
            const batch = writeBatch(db);
            querySnapshot.docs.forEach((doc) => {
              batch.delete(doc.ref);
            });
            await batch.commit();
            
            results[chatId] = true;
          } else if (options.clearMedia) {
            const result = await this.clearChatMedia(chatId);
            results[chatId] = result.success;
          }
        } catch (error) {
          console.error(`❌ Error clearing chat ${chatId}:`, error);
          results[chatId] = false;
        }
      }

      return { success: true, results };
    } catch (error) {
      return { success: false, error: 'Failed to clear multiple chats' };
    }
  }

  /**
   * Get chat storage statistics
   */
  async getChatStorageStats(userId: string): Promise<{ 
    success: boolean; 
    stats?: {
      totalChats: number;
      totalMessages: number;
      totalMediaFiles: number;
      estimatedStorageSize: number; // in MB
      oldestChat: Date;
      newestChat: Date;
    }; 
    error?: string 
  }> {
    try {
      const chatsResult = await this.getUserChats(userId);
      if (!chatsResult.success || !chatsResult.chats) {
        return { success: false, error: 'Failed to get user chats' };
      }

      const chats = chatsResult.chats;
      const totalChats = chats.length;
      const totalMessages = chats.reduce((sum, chat) => sum + chat.messageCount, 0);
      const totalMediaFiles = chats.reduce((sum, chat) => sum + chat.mediaCount, 0);
      
      // Estimate storage size (rough calculation)
      const estimatedStorageSize = Math.round(
        (totalMessages * 0.1) + // 0.1 KB per text message
        (totalMediaFiles * 2.5)   // 2.5 MB average per media file
      );

      const chatDates = chats.map(chat => chat.createdAt);
      const oldestChat = new Date(Math.min(...chatDates.map(date => date.getTime())));
      const newestChat = new Date(Math.max(...chatDates.map(date => date.getTime())));

      return {
        success: true,
        stats: {
          totalChats,
          totalMessages,
          totalMediaFiles,
          estimatedStorageSize,
          oldestChat,
          newestChat,
        }
      };
    } catch (error) {
      console.error('❌ Error getting chat storage stats:', error);
      return { success: false, error: 'Failed to get chat storage statistics' };
    }
  }

  /**
   * Archive a chat (hide from main list but keep data) (with offline support)
   */
  async archiveChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Archive offline first
      await this.archiveChatOffline(chatId, userId);

      // If online, also archive in Firebase
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', chatId);
          const chatDoc = await getDoc(chatRef);

          if (chatDoc.exists()) {
            const chatData = chatDoc.data();
            const archivedBy = chatData.archivedBy || [];

            if (!archivedBy.includes(userId)) {
              archivedBy.push(userId);

              await updateDoc(chatRef, {
                archivedBy,
                updatedAt: serverTimestamp(),
              });
            }
          }
        } catch (onlineError) {
          // Continue if offline archiving succeeded
        }
      } else {
        // Queue for sync when online
        this.syncQueue.add(chatId);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to archive chat' };
    }
  }

  private async archiveChatOffline(chatId: string, userId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      UPDATE chats SET
        isArchived = 1,
        archivedAt = ?,
        updatedAt = ?,
        syncStatus = 'pending'
      WHERE (localId = ? OR id = ?) AND participantIds LIKE ?
    `, [Date.now(), Date.now(), chatId, chatId, `%"${userId}"%`]);

    // Update cache
    const cachedChat = this.chatCache.get(chatId);
    if (cachedChat) {
      cachedChat.isArchived = true;
      cachedChat.archivedAt = Date.now();
      cachedChat.updatedAt = Date.now();
      cachedChat.syncStatus.status = 'pending';
      memoryCacheService.setChat(chatId, cachedChat);
    }
  }

  /**
   * Unarchive a chat (with offline support)
   */
  async unarchiveChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Unarchive offline first
      await this.unarchiveChatOffline(chatId, userId);

      // If online, also unarchive in Firebase
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', chatId);
          const chatDoc = await getDoc(chatRef);

          if (chatDoc.exists()) {
            const chatData = chatDoc.data();
            const archivedBy = (chatData.archivedBy || []).filter((id: string) => id !== userId);

            await updateDoc(chatRef, {
              archivedBy,
              updatedAt: serverTimestamp(),
            });
          }
        } catch (onlineError) {
          // Continue if offline unarchiving succeeded
        }
      } else {
        // Queue for sync when online
        this.syncQueue.add(chatId);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to unarchive chat' };
    }
  }

  private async unarchiveChatOffline(chatId: string, userId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      UPDATE chats SET
        isArchived = 0,
        archivedAt = NULL,
        updatedAt = ?,
        syncStatus = 'pending'
      WHERE (localId = ? OR id = ?) AND participantIds LIKE ?
    `, [Date.now(), chatId, chatId, `%"${userId}"%`]);

    // Update cache
    const cachedChat = this.chatCache.get(chatId);
    if (cachedChat) {
      cachedChat.isArchived = false;
      cachedChat.archivedAt = undefined;
      cachedChat.updatedAt = Date.now();
      cachedChat.syncStatus.status = 'pending';
      memoryCacheService.setChat(chatId, cachedChat);
    }
  }

  /**
   * Mute a chat for a user (with offline support)
   */
  async muteChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Mute offline first
      await this.muteChatOffline(chatId, userId);

      // If online, also mute in Firebase
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', chatId);
          const chatDoc = await getDoc(chatRef);

          if (chatDoc.exists()) {
            const chatData = chatDoc.data();
            const mutedBy = chatData.mutedBy || [];

            if (!mutedBy.includes(userId)) {
              mutedBy.push(userId);

              await updateDoc(chatRef, {
                mutedBy,
                [`mutedAt.${userId}`]: serverTimestamp(),
                updatedAt: serverTimestamp(),
              });
            }
          }
        } catch (onlineError) {
          // Continue if offline muting succeeded
        }
      } else {
        // Queue for sync when online
        this.syncQueue.add(chatId);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to mute chat' };
    }
  }

  private async muteChatOffline(chatId: string, userId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      UPDATE chats SET
        isMuted = 1,
        mutedUntil = NULL,
        updatedAt = ?,
        syncStatus = 'pending'
      WHERE (localId = ? OR id = ?) AND participantIds LIKE ?
    `, [Date.now(), chatId, chatId, `%"${userId}"%`]);

    // Update cache
    const cachedChat = this.chatCache.get(chatId);
    if (cachedChat) {
      cachedChat.isMuted = true;
      cachedChat.updatedAt = Date.now();
      cachedChat.syncStatus.status = 'pending';
      memoryCacheService.setChat(chatId, cachedChat);
    }
  }

  /**
   * Unmute a chat for a user (with offline support)
   */
  async unmuteChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Unmute offline first
      await this.unmuteChatOffline(chatId, userId);

      // If online, also unmute in Firebase
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', chatId);
          const chatDoc = await getDoc(chatRef);

          if (chatDoc.exists()) {
            const chatData = chatDoc.data();
            const mutedBy = (chatData.mutedBy || []).filter((id: string) => id !== userId);

            await updateDoc(chatRef, {
              mutedBy,
              [`mutedAt.${userId}`]: null,
              updatedAt: serverTimestamp(),
            });
          }
        } catch (onlineError) {
          // Continue if offline unmuting succeeded
        }
      } else {
        // Queue for sync when online
        this.syncQueue.add(chatId);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to unmute chat' };
    }
  }

  private async unmuteChatOffline(chatId: string, userId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      UPDATE chats SET
        isMuted = 0,
        mutedUntil = NULL,
        updatedAt = ?,
        syncStatus = 'pending'
      WHERE (localId = ? OR id = ?) AND participantIds LIKE ?
    `, [Date.now(), chatId, chatId, `%"${userId}"%`]);

    // Update cache
    const cachedChat = this.chatCache.get(chatId);
    if (cachedChat) {
      cachedChat.isMuted = false;
      cachedChat.mutedUntil = undefined;
      cachedChat.updatedAt = Date.now();
      cachedChat.syncStatus.status = 'pending';
      memoryCacheService.setChat(chatId, cachedChat);
    }
  }

  /**
   * Pin a chat for a user (with offline support)
   */
  async pinChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Pin offline first
      await this.pinChatOffline(chatId, userId);

      // If online, also pin in Firebase
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', chatId);
          const chatDoc = await getDoc(chatRef);

          if (chatDoc.exists()) {
            const chatData = chatDoc.data();
            const pinnedBy = chatData.pinnedBy || [];

            if (!pinnedBy.includes(userId)) {
              pinnedBy.push(userId);

              await updateDoc(chatRef, {
                pinnedBy,
                [`pinnedAt.${userId}`]: serverTimestamp(),
                updatedAt: serverTimestamp(),
              });
            }
          }
        } catch (onlineError) {
          // Continue if offline pinning succeeded
        }
      } else {
        // Queue for sync when online
        this.syncQueue.add(chatId);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to pin chat' };
    }
  }

  private async pinChatOffline(chatId: string, userId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      UPDATE chats SET
        isPinned = 1,
        pinnedAt = ?,
        updatedAt = ?,
        syncStatus = 'pending'
      WHERE (localId = ? OR id = ?) AND participantIds LIKE ?
    `, [Date.now(), Date.now(), chatId, chatId, `%"${userId}"%`]);

    // Update cache
    const cachedChat = this.chatCache.get(chatId);
    if (cachedChat) {
      cachedChat.isPinned = true;
      cachedChat.pinnedAt = Date.now();
      cachedChat.updatedAt = Date.now();
      cachedChat.syncStatus.status = 'pending';
      memoryCacheService.setChat(chatId, cachedChat);
    }
  }

  /**
   * Unpin a chat for a user (with offline support)
   */
  async unpinChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Unpin offline first
      await this.unpinChatOffline(chatId, userId);

      // If online, also unpin in Firebase
      if (networkStateManager.isOnline()) {
        try {
          const chatRef = doc(db, 'chats', chatId);
          const chatDoc = await getDoc(chatRef);

          if (chatDoc.exists()) {
            const chatData = chatDoc.data();
            const pinnedBy = (chatData.pinnedBy || []).filter((id: string) => id !== userId);

            await updateDoc(chatRef, {
              pinnedBy,
              [`pinnedAt.${userId}`]: null,
              updatedAt: serverTimestamp(),
            });
          }
        } catch (onlineError) {
          // Continue if offline unpinning succeeded
        }
      } else {
        // Queue for sync when online
        this.syncQueue.add(chatId);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to unpin chat' };
    }
  }

  private async unpinChatOffline(chatId: string, userId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      UPDATE chats SET
        isPinned = 0,
        pinnedAt = NULL,
        updatedAt = ?,
        syncStatus = 'pending'
      WHERE (localId = ? OR id = ?) AND participantIds LIKE ?
    `, [Date.now(), chatId, chatId, `%"${userId}"%`]);

    // Update cache
    const cachedChat = this.chatCache.get(chatId);
    if (cachedChat) {
      cachedChat.isPinned = false;
      cachedChat.pinnedAt = undefined;
      cachedChat.updatedAt = Date.now();
      cachedChat.syncStatus.status = 'pending';
      memoryCacheService.setChat(chatId, cachedChat);
    }
  }

  /**
   * Export chat data for backup
   */
  async exportChatData(
    chatId: string,
    format: 'json' | 'csv' | 'txt' = 'json'
  ): Promise<{ success: boolean; data?: string; error?: string }> {
    try {
      const messagesResult = await this.getChatMessages(chatId, 10000); // Get all messages
      if (!messagesResult.success || !messagesResult.messages) {
        return { success: false, error: 'Failed to get chat messages for export' };
      }

      const messages = messagesResult.messages;
      let exportData: string;

      switch (format) {
        case 'json':
          exportData = JSON.stringify(messages, null, 2);
          break;
        
        case 'csv':
          const csvHeader = 'Timestamp,Sender,Type,Content\n';
          const csvRows = messages.map(msg => 
            `"${msg.timestamp.toISOString()}","${msg.senderId}","${msg.type}","${msg.text || msg.mediaUrl || ''}"`
          ).join('\n');
          exportData = csvHeader + csvRows;
          break;
        
        case 'txt':
          exportData = messages.map(msg => 
            `[${msg.timestamp.toLocaleString()}] ${msg.senderId}: ${msg.text || `[${msg.type.toUpperCase()}]`}`
          ).join('\n');
          break;
        
        default:
          return { success: false, error: 'Unsupported export format' };
      }

      return { success: true, data: exportData };
    } catch (error) {
      return { success: false, error: 'Failed to export chat data' };
    }
  }

  /**
   * Clear all user data (for account deletion)
   */
  async clearAllUserData(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatsResult = await this.getUserChats(userId);
      if (!chatsResult.success || !chatsResult.chats) {
        return { success: false, error: 'Failed to get user chats' };
      }

      const chatIds = chatsResult.chats.map(chat => chat.id);
      
      // Clear all chats completely
      const clearResult = await this.clearMultipleChats(chatIds, {
        clearMessages: false,
        clearMedia: false,
        clearAll: true,
      });

      if (!clearResult.success) {
        return { success: false, error: 'Failed to clear user chats' };
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to clear all user data' };
    }
  }

  /**
   * Batch update multiple chat messages
   */
  async batchUpdateMessages(updates: { chatId: string; messageId: string; data: any }[]): Promise<{ success: boolean; error?: string }> {
    try {
      const batch = writeBatch(db);

      updates.forEach(({ chatId, messageId, data }) => {
        const messageRef = doc(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId, 'messages', messageId);
        batch.update(messageRef, {
          ...data,
          updatedAt: serverTimestamp()
        });
      });

      await batch.commit();
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to batch update messages' };
    }
  }

  /**
   * Add user to chat participants
   */
  async addChatParticipant(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatRef = doc(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId);
      await updateDoc(chatRef, {
        participants: arrayUnion(userId),
        participantCount: increment(1),
        updatedAt: serverTimestamp()
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to add participant' };
    }
  }

  /**
   * Remove user from chat participants
   */
  async removeChatParticipant(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatRef = doc(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId);
      await updateDoc(chatRef, {
        participants: arrayRemove(userId),
        participantCount: increment(-1),
        updatedAt: serverTimestamp()
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to remove participant' };
    }
  }

  /**
   * Delete media file from storage
   */
  async deleteMediaFile(mediaUrl: string): Promise<{ success: boolean; error?: string }> {
    try {
      const mediaRef = ref(storage, mediaUrl);
      await deleteObject(mediaRef);

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete media file' };
    }
  }

  /**
   * Increment message view count
   */
  async incrementMessageViews(chatId: string, messageId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const messageRef = doc(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId, 'messages', messageId);
      await updateDoc(messageRef, {
        viewCount: increment(1),
        lastViewedAt: serverTimestamp()
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to increment views' };
    }
  }

  /**
   * Schedule a message to be sent later
   */
  async scheduleMessage(
    chatId: string,
    senderId: string,
    content: string,
    scheduledFor: Date,
    type: 'text' | 'image' | 'video' | 'audio' | 'file' = 'text',
    mediaUrl?: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {

      const scheduledMessage: RealScheduledMessage = {
        id: `scheduled_${Date.now()}_${senderId}`,
        chatId,
        senderId,
        content,
        type,
        mediaUrl,
        scheduledFor: scheduledFor,
        status: 'pending'
      };

      const scheduledRef = doc(db, COLLECTIONS.SCHEDULED, scheduledMessage.id);
      await setDoc(scheduledRef, {
        ...scheduledMessage,
        scheduledFor: scheduledFor,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });

      return { success: true, messageId: scheduledMessage.id };
    } catch (error) {
      return { success: false, error: 'Failed to schedule message' };
    }
  }

  /**
   * Get chat media files
   */
  async getChatMedia(chatId: string): Promise<{ success: boolean; media?: any[]; error?: string }> {
    try {

      const messagesRef = collection(db, COLLECTIONS.INDIVIDUAL_CHATS, chatId, 'messages');
      const q = query(
        messagesRef,
        where('type', 'in', ['image', 'video', 'audio', 'file']),
        orderBy('timestamp', 'desc'),
        limit(100)
      );

      const snapshot = await getDocs(q);
      const media: any[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        if (data.mediaUrl) {
          media.push({
            id: doc.id,
            url: data.mediaUrl,
            type: data.type,
            timestamp: data.timestamp?.toDate() || new Date(),
            senderId: data.senderId,
            senderName: data.senderName
          });
        }
      });

      return { success: true, media };
    } catch (error) {
      return { success: false, error: 'Failed to load media' };
    }
  }

  /**
   * Create a message thread
   */
  async createMessageThread(messageId: string, chatId: string): Promise<string> {
    try {

      const threadData: RealMessageThread = {
        id: `thread_${Date.now()}_${messageId}`,
        originalMessageId: messageId,
        chatId,
        replies: [],
        participantCount: 0,
        createdAt: serverTimestamp()
      };

      const threadRef = doc(db, COLLECTIONS.THREADS, threadData.id);
      await setDoc(threadRef, threadData);

      return threadData.id;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get message thread data
   */
  async getMessageThread(threadId: string): Promise<any> {
    try {

      const threadRef = doc(db, COLLECTIONS.THREADS, threadId);
      const threadDoc = await getDoc(threadRef);

      if (threadDoc.exists()) {
        return { id: threadDoc.id, ...threadDoc.data() };
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get chat settings for a user
   */
  async getChatSettings(chatId: string, userId: string): Promise<{ success: boolean; settings?: _RealChatSettings; error?: string }> {
    try {

      const settingsRef = doc(db, COLLECTIONS.SETTINGS, `${chatId}_${userId}`);
      const settingsDoc = await getDoc(settingsRef);

      if (settingsDoc.exists()) {
        const settings = settingsDoc.data() as _RealChatSettings;
        return { success: true, settings };
      }

      // Return default settings if none exist
      const defaultSettings: _RealChatSettings = {
        id: `${chatId}_${userId}`,
        chatId,
        userId,
        notifications: true,
        encryption: false,
        readReceipts: true,
        typingIndicators: true,
        lastSeen: true,
      };

      return { success: true, settings: defaultSettings };
    } catch (error) {
      return { success: false, error: 'Failed to get chat settings' };
    }
  }

  /**
   * Update chat settings for a user
   */
  async updateChatSettings(
    chatId: string,
    userId: string,
    settings: Partial<Omit<_RealChatSettings, 'id' | 'chatId' | 'userId'>>
  ): Promise<{ success: boolean; error?: string }> {
    try {

      const settingsRef = doc(db, COLLECTIONS.SETTINGS, `${chatId}_${userId}`);
      const settingsData: _RealChatSettings = {
        id: `${chatId}_${userId}`,
        chatId,
        userId,
        notifications: settings.notifications ?? true,
        encryption: settings.encryption ?? false,
        readReceipts: settings.readReceipts ?? true,
        typingIndicators: settings.typingIndicators ?? true,
        lastSeen: settings.lastSeen ?? true,
      };

      await setDoc(settingsRef, {
        ...settingsData,
        updatedAt: serverTimestamp(),
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to update chat settings' };
    }
  }

  /**
   * Reset chat settings to default for a user
   */
  async resetChatSettings(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {

      const defaultSettings: _RealChatSettings = {
        id: `${chatId}_${userId}`,
        chatId,
        userId,
        notifications: true,
        encryption: false,
        readReceipts: true,
        typingIndicators: true,
        lastSeen: true,
      };

      const settingsRef = doc(db, COLLECTIONS.SETTINGS, `${chatId}_${userId}`);
      await setDoc(settingsRef, {
        ...defaultSettings,
        updatedAt: serverTimestamp(),
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to reset chat settings' };
    }
  }

  /**
   * Cleanup method
   */
  cleanup(): void {
    networkStateManager.removeListener('realChatService');
    this.chatCache.clear();
    this.syncQueue.clear();
    this.isInitialized = false;
  }
}






// Create and export the service instance
export const realChatService = new RealChatService();
