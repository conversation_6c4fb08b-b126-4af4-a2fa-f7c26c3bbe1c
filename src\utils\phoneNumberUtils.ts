/**
 * Phone number utilities for better matching and normalization
 * Handles different phone number formats for Uganda (+256)
 */

import { collection, query, where, getDocs, or } from 'firebase/firestore';
import { db } from '../services/firebaseSimple';
import { User } from '../types';

/**
 * Normalize phone number to different formats for better matching
 */
export const normalizePhoneNumber = (phoneNumber: string): string[] => {
  if (!phoneNumber) return [];

  // Remove all non-digit characters except +
  const cleaned = phoneNumber.replace(/[^\d+]/g, '');
  
  const variants: string[] = [];
  
  // Handle different input formats
  if (cleaned.startsWith('+256')) {
    const localNumber = cleaned.substring(4); // Remove +256
    variants.push(cleaned); // +256783835749
    variants.push('0' + localNumber); // 0783835749
    variants.push(localNumber); // 783835749
    variants.push('256' + localNumber); // 256783835749
  } else if (cleaned.startsWith('256')) {
    const localNumber = cleaned.substring(3); // Remove 256
    variants.push('+' + cleaned); // +256783835749
    variants.push('0' + localNumber); // 0783835749
    variants.push(localNumber); // 783835749
    variants.push(cleaned); // 256783835749
  } else if (cleaned.startsWith('0')) {
    const localNumber = cleaned.substring(1); // Remove 0
    variants.push(cleaned); // 0783835749
    variants.push(localNumber); // 783835749
    variants.push('+256' + localNumber); // +256783835749
    variants.push('256' + localNumber); // 256783835749
  } else {
    // Assume it's a local number without prefix
    variants.push(cleaned); // 783835749
    variants.push('0' + cleaned); // 0783835749
    variants.push('+256' + cleaned); // +256783835749
    variants.push('256' + cleaned); // 256783835749
  }
  
  // Remove duplicates and return
  return [...new Set(variants)];
};

/**
 * Enhanced function to find IraChat user by phone number with multiple format matching
 */
export const findIraChatUserByPhoneEnhanced = async (phoneNumber: string): Promise<User | null> => {
  try {
    console.log(`🔍 Enhanced search for phone number: ${phoneNumber}`);
    
    const phoneVariants = normalizePhoneNumber(phoneNumber);
    console.log(`📱 Searching with variants: ${phoneVariants.join(', ')}`);
    
    const usersRef = collection(db, 'users');
    
    // Try each variant
    for (const variant of phoneVariants) {
      const q = query(usersRef, where('phoneNumber', '==', variant));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        const userData = {
          id: doc.id,
          ...doc.data(),
        } as User;
        console.log(`✅ Found user with variant ${variant}: ${userData.name}`);
        return userData;
      }
    }
    
    console.log(`❌ No user found for any variant of ${phoneNumber}`);
    return null;
    
  } catch (error) {
    console.error('❌ Error in enhanced phone search:', error);
    return null;
  }
};

/**
 * Batch search for multiple phone numbers with enhanced matching
 */
export const findMultipleUsersByPhone = async (phoneNumbers: string[]): Promise<Map<string, User>> => {
  try {
    console.log(`🔍 Batch searching for ${phoneNumbers.length} phone numbers...`);
    
    const results = new Map<string, User>();
    const usersRef = collection(db, 'users');
    
    // Get all unique variants
    const allVariants = new Set<string>();
    const variantToOriginal = new Map<string, string>();
    
    phoneNumbers.forEach(phone => {
      const variants = normalizePhoneNumber(phone);
      variants.forEach(variant => {
        allVariants.add(variant);
        variantToOriginal.set(variant, phone);
      });
    });
    
    console.log(`📱 Total variants to search: ${allVariants.size}`);
    
    // Search in batches (Firestore 'in' query limit is 10)
    const variantArray = Array.from(allVariants);
    for (let i = 0; i < variantArray.length; i += 10) {
      const batch = variantArray.slice(i, i + 10);
      
      const q = query(usersRef, where('phoneNumber', 'in', batch));
      const snapshot = await getDocs(q);
      
      snapshot.docs.forEach(doc => {
        const userData = { id: doc.id, ...doc.data() } as User;
        const foundVariant = userData.phoneNumber;

        if (foundVariant) {
          const originalPhone = variantToOriginal.get(foundVariant);

          if (originalPhone) {
            results.set(originalPhone, userData);
            console.log(`✅ Found user for ${originalPhone}: ${userData.name}`);
          }
        }
      });
    }
    
    console.log(`📊 Found ${results.size} users out of ${phoneNumbers.length} searched`);
    return results;
    
  } catch (error) {
    console.error('❌ Error in batch phone search:', error);
    return new Map();
  }
};

/**
 * Debug function to test phone number matching
 */
export const debugPhoneNumberMatching = async (testPhone: string): Promise<void> => {
  try {
    console.log('🧪 === PHONE NUMBER MATCHING DEBUG ===');
    console.log(`📱 Testing phone number: ${testPhone}`);
    
    // Show all variants
    const variants = normalizePhoneNumber(testPhone);
    console.log(`🔄 Generated variants: ${variants.join(', ')}`);
    
    // Test each variant
    const usersRef = collection(db, 'users');
    
    for (const variant of variants) {
      console.log(`🔍 Testing variant: ${variant}`);
      
      const q = query(usersRef, where('phoneNumber', '==', variant));
      const snapshot = await getDocs(q);
      
      console.log(`  📊 Results: ${snapshot.size} users found`);
      
      snapshot.docs.forEach(doc => {
        const userData = doc.data() as User;
        console.log(`    ✅ User: ${userData.name} (${userData.phoneNumber})`);
      });
    }
    
    // Also check what's actually in the database
    console.log('🔍 Checking all users in database...');
    const allUsersSnapshot = await getDocs(usersRef);
    console.log(`📊 Total users in database: ${allUsersSnapshot.size}`);
    
    const matchingUsers: User[] = [];
    allUsersSnapshot.docs.forEach(doc => {
      const userData = doc.data() as User;
      if (userData.phoneNumber && userData.phoneNumber.includes('783835749')) {
        matchingUsers.push(userData);
        console.log(`  🎯 Potential match: ${userData.name} (${userData.phoneNumber})`);
      }
    });
    
    if (matchingUsers.length === 0) {
      console.log('❌ No users found with phone number containing 783835749');
      console.log('💡 This suggests the test user was not added successfully');
    }
    
    console.log('🧪 === DEBUG COMPLETE ===');
    
  } catch (error) {
    console.error('❌ Error in debug function:', error);
  }
};

/**
 * Check if a phone number exists in any format
 */
export const checkPhoneNumberExists = async (phoneNumber: string): Promise<{ exists: boolean; user?: User; matchedFormat?: string }> => {
  try {
    const variants = normalizePhoneNumber(phoneNumber);
    const usersRef = collection(db, 'users');
    
    for (const variant of variants) {
      const q = query(usersRef, where('phoneNumber', '==', variant));
      const snapshot = await getDocs(q);
      
      if (!snapshot.empty) {
        const doc = snapshot.docs[0];
        return {
          exists: true,
          user: { id: doc.id, ...doc.data() } as User,
          matchedFormat: variant
        };
      }
    }
    
    return { exists: false };
    
  } catch (error) {
    console.error('❌ Error checking phone number existence:', error);
    return { exists: false };
  }
};
