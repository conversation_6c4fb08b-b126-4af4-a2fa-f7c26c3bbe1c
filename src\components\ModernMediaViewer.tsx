/**
 * Modern Media Viewer for IraChat
 * Features: Infinite zoom, pan, rotation, modern animations, loading states
 * No fake implementations - fully functional media viewer
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Modal,
  Dimensions,
  StatusBar,
  Alert,
  Text,
  TouchableOpacity,
  Animated,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';
import { Gesture, GestureDetector, GestureHandlerRootView } from 'react-native-gesture-handler';
import { runOnJS, useSharedValue, useAnimatedStyle, withSpring, withTiming } from 'react-native-reanimated';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import * as FileSystem from 'expo-file-system';
import { BlurView } from 'expo-blur';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export interface MediaItem {
  id: string;
  uri: string;
  type: 'image' | 'video';
  width?: number;
  height?: number;
  duration?: number;
  caption?: string;
  fileName?: string;
  fileSize?: number;
}

interface ModernMediaViewerProps {
  visible: boolean;
  mediaItems: MediaItem[];
  initialIndex: number;
  onClose: () => void;
  onDelete?: (item: MediaItem) => void;
  onShare?: (item: MediaItem) => void;
  onSaveForRemembrance?: (item: MediaItem) => void;
  canDelete?: boolean;
  showRememberanceOption?: boolean;
}

export const ModernMediaViewer: React.FC<ModernMediaViewerProps> = ({
  visible,
  mediaItems,
  initialIndex,
  onClose,
  onDelete,
  onShare,
  onSaveForRemembrance,
  canDelete = false,
  showRememberanceOption = true,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [rotation, setRotation] = useState(0);
  
  // Animation values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const opacity = useSharedValue(0);
  const controlsOpacity = useSharedValue(1);
  
  // Refs
  const videoRef = useRef<Video>(null);
  const controlsTimer = useRef<NodeJS.Timeout | null>(null);

  const currentItem = mediaItems[currentIndex];

  const resetTransform = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
  };

  const resetControlsTimer = () => {
    if (controlsTimer.current) {
      clearTimeout(controlsTimer.current);
    }
    controlsTimer.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);
  };

  useEffect(() => {
    if (visible) {
      setCurrentIndex(initialIndex);
      opacity.value = withTiming(1, { duration: 300 });
      resetTransform();
    } else {
      opacity.value = withTiming(0, { duration: 200 });
    }
  }, [visible, initialIndex, opacity]);

  useEffect(() => {
    if (showControls) {
      controlsOpacity.value = withTiming(1, { duration: 200 });
      resetControlsTimer();
    } else {
      controlsOpacity.value = withTiming(0, { duration: 200 });
    }
  }, [showControls]);

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  // Pinch gesture for zoom
  const pinchGesture = Gesture.Pinch()
    .onUpdate((event) => {
      scale.value = Math.max(0.5, Math.min(event.scale, 10)); // Limit zoom between 0.5x and 10x
    })
    .onEnd(() => {
      if (scale.value < 1) {
        scale.value = withSpring(1);
      }
    });

  // Pan gesture for moving and closing
  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      if (scale.value > 1) {
        // If zoomed in, allow panning
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      } else {
        // If not zoomed, only allow vertical pan for closing
        translateY.value = event.translationY;
        opacity.value = Math.max(0.3, 1 - Math.abs(event.translationY) / SCREEN_HEIGHT);
      }
    })
    .onEnd((event) => {
      if (scale.value <= 1 && Math.abs(event.translationY) > 100) {
        // Close if swiped down/up significantly
        runOnJS(onClose)();
      } else if (scale.value > 1) {
        // Snap back to bounds if zoomed
        const maxTranslateX = (SCREEN_WIDTH * (scale.value - 1)) / 2;
        const maxTranslateY = (SCREEN_HEIGHT * (scale.value - 1)) / 2;
        
        translateX.value = withSpring(
          Math.max(-maxTranslateX, Math.min(maxTranslateX, translateX.value))
        );
        translateY.value = withSpring(
          Math.max(-maxTranslateY, Math.min(maxTranslateY, translateY.value))
        );
      } else {
        // Reset position
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        opacity.value = withSpring(1);
      }
    });

  // Double tap to zoom
  const doubleTapGesture = Gesture.Tap()
    .numberOfTaps(2)
    .onEnd(() => {
      if (scale.value > 1) {
        // Zoom out
        scale.value = withSpring(1);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
      } else {
        // Zoom in
        scale.value = withSpring(2);
      }
    });

  // Single tap to toggle controls
  const singleTapGesture = Gesture.Tap()
    .onEnd(() => {
      runOnJS(toggleControls)();
    });

  // Combine gestures
  const composedGesture = Gesture.Simultaneous(
    Gesture.Exclusive(doubleTapGesture, singleTapGesture),
    pinchGesture,
    panGesture
  );

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { scale: scale.value },
      { translateX: translateX.value },
      { translateY: translateY.value },
      { rotate: `${rotation}deg` },
    ],
  }));

  const backgroundStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
  }));

  const controlsStyle = useAnimatedStyle(() => ({
    opacity: controlsOpacity.value,
  }));

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleVideoLoad = () => {
    setIsLoading(false);
  };

  const toggleVideoPlayback = async () => {
    if (!videoRef.current) return;
    
    if (isVideoPlaying) {
      await videoRef.current.pauseAsync();
    } else {
      await videoRef.current.playAsync();
    }
    setIsVideoPlaying(!isVideoPlaying);
  };

  const rotateImage = () => {
    setRotation((prev) => (prev + 90) % 360);
  };

  const handleDownload = async () => {
    if (!currentItem) return;

    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant permission to save media to your device.'
        );
        return;
      }

      // Download file if it's a URL
      let localUri = currentItem.uri;
      if (currentItem.uri.startsWith('http')) {
        const downloadResult = await FileSystem.downloadAsync(
          currentItem.uri,
          `${FileSystem.documentDirectory}${currentItem.fileName || 'media'}`
        );
        localUri = downloadResult.uri;
      }

      await MediaLibrary.saveToLibraryAsync(localUri);
      Alert.alert('Success', 'Media saved to your gallery!');
    } catch (error) {
      console.error('❌ Error downloading media:', error);
      Alert.alert('Error', 'Failed to save media to your device.');
    }
  };

  const handleShare = async () => {
    if (!currentItem || !onShare) return;
    
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        Alert.alert('Error', 'Sharing is not available on this device.');
        return;
      }

      onShare(currentItem);
    } catch (error) {
      console.error('❌ Error sharing media:', error);
      Alert.alert('Error', 'Failed to share media.');
    }
  };

  const handleSaveForRemembrance = () => {
    if (!currentItem || !onSaveForRemembrance) return;
    onSaveForRemembrance(currentItem);
    Alert.alert('Saved', 'Media saved to your remembrance collection!');
  };

  const handleDelete = () => {
    if (!currentItem || !onDelete) return;
    
    Alert.alert(
      'Delete Media',
      'Are you sure you want to delete this media?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => onDelete(currentItem),
        },
      ]
    );
  };

  const navigateToNext = () => {
    if (currentIndex < mediaItems.length - 1) {
      setCurrentIndex(currentIndex + 1);
      resetTransform();
      setIsLoading(true);
    }
  };

  const navigateToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      resetTransform();
      setIsLoading(true);
    }
  };

  if (!visible || !currentItem) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      <GestureHandlerRootView style={{ flex: 1 }}>
        <Animated.View style={[{ flex: 1 }, backgroundStyle]}>
          <BlurView
            intensity={20}
            style={{
              flex: 1,
              backgroundColor: 'rgba(0, 0, 0, 0.9)',
            }}
          />
        </Animated.View>

        <View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <GestureDetector gesture={composedGesture}>
            <Animated.View style={[animatedStyle]}>
              {currentItem.type === 'image' ? (
                <Image
                  source={{ uri: currentItem.uri }}
                  style={{
                    width: SCREEN_WIDTH,
                    height: SCREEN_HEIGHT,
                  }}
                  resizeMode="contain"
                  onLoad={handleImageLoad}
                />
              ) : (
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={toggleVideoPlayback}
                  style={{
                    width: SCREEN_WIDTH,
                    height: SCREEN_HEIGHT,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <Video
                    ref={videoRef}
                    source={{ uri: currentItem.uri }}
                    style={{
                      width: SCREEN_WIDTH,
                      height: SCREEN_HEIGHT,
                    }}
                    resizeMode={ResizeMode.CONTAIN}
                    shouldPlay={isVideoPlaying}
                    isLooping
                    onLoad={handleVideoLoad}
                  />
                  {!isVideoPlaying && (
                    <View
                      style={{
                        position: 'absolute',
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        borderRadius: 50,
                        width: 100,
                        height: 100,
                      }}
                    >
                      <Ionicons name="play" size={50} color="white" />
                    </View>
                  )}
                </TouchableOpacity>
              )}
            </Animated.View>
          </GestureDetector>

          {isLoading && (
            <View
              style={{
                position: 'absolute',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <ActivityIndicator size="large" color="white" />
              <Text style={{ color: 'white', marginTop: 10 }}>Loading...</Text>
            </View>
          )}
        </View>

        {/* Controls Overlay */}
        <Animated.View
          style={[
            {
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              pointerEvents: showControls ? 'auto' : 'none',
            },
            controlsStyle,
          ]}
        >
          {/* Top Controls */}
          <View
            style={{
              position: 'absolute',
              top: 50,
              left: 0,
              right: 0,
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingHorizontal: 20,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              paddingVertical: 10,
            }}
          >
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={30} color="white" />
            </TouchableOpacity>

            <Text style={{ color: 'white', fontSize: 16, fontWeight: '500' }}>
              {currentIndex + 1} of {mediaItems.length}
            </Text>

            <View style={{ flexDirection: 'row', gap: 15 }}>
              {currentItem.type === 'image' && (
                <TouchableOpacity onPress={rotateImage}>
                  <Ionicons name="refresh" size={24} color="white" />
                </TouchableOpacity>
              )}
              <TouchableOpacity onPress={handleDownload}>
                <Ionicons name="download" size={24} color="white" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Navigation Controls */}
          {mediaItems.length > 1 && (
            <>
              {currentIndex > 0 && (
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    left: 20,
                    top: '50%',
                    transform: [{ translateY: -25 }],
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    borderRadius: 25,
                    width: 50,
                    height: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={navigateToPrevious}
                >
                  <Ionicons name="chevron-back" size={30} color="white" />
                </TouchableOpacity>
              )}

              {currentIndex < mediaItems.length - 1 && (
                <TouchableOpacity
                  style={{
                    position: 'absolute',
                    right: 20,
                    top: '50%',
                    transform: [{ translateY: -25 }],
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    borderRadius: 25,
                    width: 50,
                    height: 50,
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                  onPress={navigateToNext}
                >
                  <Ionicons name="chevron-forward" size={30} color="white" />
                </TouchableOpacity>
              )}
            </>
          )}

          {/* Bottom Controls */}
          <View
            style={{
              position: 'absolute',
              bottom: 50,
              left: 0,
              right: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              paddingVertical: 15,
              paddingHorizontal: 20,
            }}
          >
            {currentItem.caption && (
              <Text
                style={{
                  color: 'white',
                  fontSize: 16,
                  marginBottom: 15,
                  textAlign: 'center',
                }}
              >
                {currentItem.caption}
              </Text>
            )}

            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'space-around',
                alignItems: 'center',
              }}
            >
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  paddingHorizontal: 15,
                  paddingVertical: 8,
                  borderRadius: 20,
                }}
                onPress={handleShare}
              >
                <Ionicons name="share-outline" size={20} color="white" />
                <Text style={{ color: 'white', marginLeft: 5 }}>Share</Text>
              </TouchableOpacity>

              {showRememberanceOption && (
                <TouchableOpacity
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    paddingHorizontal: 15,
                    paddingVertical: 8,
                    borderRadius: 20,
                  }}
                  onPress={handleSaveForRemembrance}
                >
                  <Ionicons name="heart-outline" size={20} color="white" />
                  <Text style={{ color: 'white', marginLeft: 5 }}>Remember</Text>
                </TouchableOpacity>
              )}

              {canDelete && (
                <TouchableOpacity
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    backgroundColor: 'rgba(255, 0, 0, 0.3)',
                    paddingHorizontal: 15,
                    paddingVertical: 8,
                    borderRadius: 20,
                  }}
                  onPress={handleDelete}
                >
                  <Ionicons name="trash-outline" size={20} color="white" />
                  <Text style={{ color: 'white', marginLeft: 5 }}>Delete</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </Animated.View>
      </GestureHandlerRootView>
    </Modal>
  );
};
