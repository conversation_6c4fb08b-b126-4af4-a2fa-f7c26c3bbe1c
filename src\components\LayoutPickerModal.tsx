/**
 * Layout Picker Modal Component
 * WhatsApp-style layout selection for stories
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  TextInput,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { layoutPickerService, LayoutTemplate, LayoutCategory } from '../services/layoutPickerService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface LayoutPickerModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectLayout: (template: LayoutTemplate) => void;
}

export const LayoutPickerModal: React.FC<LayoutPickerModalProps> = ({
  visible,
  onClose,
  onSelectLayout,
}) => {
  const [categories, setCategories] = useState<LayoutCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('popular');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<LayoutTemplate[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    if (visible) {
      loadLayoutCategories();
    } else {
      // Clean up when modal closes
      setSearchQuery('');
      setSearchResults([]);
    }
  }, [visible]);

  const loadLayoutCategories = () => {
    const layoutCategories = layoutPickerService.getLayoutCategories();
    setCategories(layoutCategories);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      setIsSearching(true);
      const results = layoutPickerService.searchTemplates(query);
      setSearchResults(results);
      setIsSearching(false);
    } else {
      setSearchResults([]);
    }
  };

  const handleSelectLayout = (template: LayoutTemplate) => {
    if (template.isPremium) {
      // For now, show premium alert - could integrate with payment system
      alert('Premium Feature', 'This layout is available with IraChat Premium. Upgrade to unlock all creative layouts!');
      return;
    }
    
    onSelectLayout(template);
    onClose();
  };

  const renderTemplateItem = ({ item }: { item: LayoutTemplate }) => (
    <TouchableOpacity
      style={[
        styles.templateItem,
        item.isPremium && styles.premiumTemplateItem
      ]}
      onPress={() => handleSelectLayout(item)}
    >
      <View style={styles.templateThumbnail}>
        <Text style={styles.templateThumbnailText}>{item.thumbnail}</Text>
        {item.isPremium && (
          <View style={styles.premiumBadge}>
            <Ionicons name="diamond" size={12} color="#FFD700" />
          </View>
        )}
        {item.isPopular && !item.isPremium && (
          <View style={styles.popularBadge}>
            <Ionicons name="trending-up" size={12} color="#25D366" />
          </View>
        )}
      </View>
      
      <View style={styles.templateInfo}>
        <Text style={styles.templateName} numberOfLines={1}>
          {item.name}
        </Text>
        <Text style={styles.templateDescription} numberOfLines={2}>
          {item.description}
        </Text>
        <View style={styles.templateMeta}>
          <Text style={styles.templateMetaText}>
            {layoutPickerService.getAspectRatioString(item.aspectRatio)}
          </Text>
          {item.mediaSlots > 1 && (
            <>
              <Text style={styles.templateMetaDot}>•</Text>
              <Text style={styles.templateMetaText}>
                {item.mediaSlots} photos
              </Text>
            </>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCategoryTab = (category: LayoutCategory) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryTab,
        selectedCategory === category.id && styles.selectedCategoryTab
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <Text
        style={[
          styles.categoryTabText,
          selectedCategory === category.id && styles.selectedCategoryTabText
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );

  const getCurrentTemplates = (): LayoutTemplate[] => {
    if (searchQuery.trim()) {
      return searchResults;
    }
    
    const category = categories.find(cat => cat.id === selectedCategory);
    return category?.templates || [];
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#000000" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Choose Layout</Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search layouts..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor="#999999"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch('')}>
              <Ionicons name="close-circle" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>

        {/* Category Tabs */}
        {!searchQuery.trim() && (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.categoryTabsContainer}
            contentContainerStyle={styles.categoryTabs}
          >
            {categories.map(renderCategoryTab)}
          </ScrollView>
        )}

        {/* Templates List */}
        <FlatList
          data={getCurrentTemplates()}
          renderItem={renderTemplateItem}
          keyExtractor={(item) => item.id}
          style={styles.templatesList}
          showsVerticalScrollIndicator={false}
          numColumns={2}
          columnWrapperStyle={styles.templateRow}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              {isSearching ? (
                <ActivityIndicator size="large" color="#25D366" />
              ) : (
                <>
                  <Ionicons name="grid" size={48} color="#CCCCCC" />
                  <Text style={styles.emptyText}>
                    {searchQuery.trim() ? 'No layouts found' : 'No layouts available'}
                  </Text>
                </>
              )}
            </View>
          }
        />

        {/* Premium Upgrade Banner */}
        <View style={styles.premiumBanner}>
          <View style={styles.premiumBannerContent}>
            <Ionicons name="diamond" size={20} color="#FFD700" />
            <Text style={styles.premiumBannerText}>
              Unlock all creative layouts with Premium
            </Text>
          </View>
          <TouchableOpacity style={styles.premiumButton}>
            <Text style={styles.premiumButtonText}>Upgrade</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  closeButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#000000',
  },
  categoryTabsContainer: {
    maxHeight: 50,
  },
  categoryTabs: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
  },
  selectedCategoryTab: {
    backgroundColor: '#25D366',
  },
  categoryTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  selectedCategoryTabText: {
    color: '#FFFFFF',
  },
  templatesList: {
    flex: 1,
    paddingHorizontal: 8,
  },
  templateRow: {
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  templateItem: {
    width: (screenWidth - 48) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    marginVertical: 6,
    borderWidth: 1,
    borderColor: '#F0F0F0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  premiumTemplateItem: {
    borderColor: '#FFD700',
    borderWidth: 1.5,
  },
  templateThumbnail: {
    height: 80,
    backgroundColor: '#F8F8F8',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
    position: 'relative',
  },
  templateThumbnailText: {
    fontSize: 24,
  },
  premiumBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(255, 215, 0, 0.2)',
    borderRadius: 8,
    padding: 2,
  },
  popularBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(37, 211, 102, 0.2)',
    borderRadius: 8,
    padding: 2,
  },
  templateInfo: {
    flex: 1,
  },
  templateName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  templateDescription: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
  },
  templateMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  templateMetaText: {
    fontSize: 11,
    color: '#999999',
  },
  templateMetaDot: {
    fontSize: 11,
    color: '#999999',
    marginHorizontal: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#999999',
    marginTop: 12,
  },
  premiumBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFF9E6',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  premiumBannerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  premiumBannerText: {
    fontSize: 14,
    color: '#B8860B',
    marginLeft: 8,
    flex: 1,
  },
  premiumButton: {
    backgroundColor: '#FFD700',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
  },
  premiumButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#000000',
  },
});
