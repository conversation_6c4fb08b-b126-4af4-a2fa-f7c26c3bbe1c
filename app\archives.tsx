import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useState, useEffect, useCallback } from "react";
import {
  Alert,
  FlatList,
  Text,
  TouchableOpacity,
  View,
  RefreshControl,
} from "react-native";
import { localChatManagementService, LocalChatData } from "../src/services/localChatManagementService";
import { realTimeMessagingService } from "../src/services/realTimeMessagingService";
import { useAuth } from "../src/hooks/useAuth";
import { getCurrentUser } from "../src/services/authService";

interface ArchivedChat extends LocalChatData {
  participantName?: string;
  participantAvatar?: string;
  groupName?: string;
  groupAvatar?: string;
}

export default function ArchivesScreen(): React.JSX.Element {
  const router = useRouter();
  const { user: firebaseUser } = useAuth();
  const [archivedChats, setArchivedChats] = useState<ArchivedChat[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  // Load current user - sync with Firebase user
  useEffect(() => {
    const loadCurrentUser = async () => {
      try {
        // Use Firebase user if available, otherwise get from local storage
        if (firebaseUser) {
          console.log('🔥 Using Firebase user for archives:', firebaseUser.uid);
          setCurrentUser({ id: firebaseUser.uid, ...firebaseUser });
        } else {
          const user = await getCurrentUser();
          setCurrentUser(user);
        }
      } catch (error) {
        console.error('❌ Failed to load current user:', error);
      }
    };

    loadCurrentUser();
  }, [firebaseUser]);

  // Load archived chats from local storage only (no Firebase sync)
  const loadArchivedChats = useCallback(async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    try {
      console.log('� Loading archived chats locally for user:', currentUser.id);

      // Load archived chats from offline database (the actual data source)
      const { offlineDatabaseService } = await import('../src/services/offlineDatabase');
      await offlineDatabaseService.initialize();

      if (offlineDatabaseService.isReady()) {
        const db = offlineDatabaseService.getDatabase();
        const offlineArchivedChats = await db.getAllAsync(`
          SELECT * FROM chats WHERE isArchived = 1 ORDER BY updatedAt DESC
        `);

        // Convert to ArchivedChat format
        const convertedChats: ArchivedChat[] = offlineArchivedChats.map((chat: any) => ({
          id: chat.id,
          name: chat.name || chat.partnerName || 'Unknown Chat',
          avatar: chat.avatar || chat.partnerAvatar,
          lastMessage: chat.lastMessage || 'No messages',
          timestamp: new Date(chat.updatedAt || chat.createdAt || Date.now()),
          messageCount: 0,
          mediaCount: 0,
          isGroup: Boolean(chat.isGroup),
          participants: chat.partnerId ? [currentUser.id, chat.partnerId] : [currentUser.id],
          isArchived: true,
          isMuted: Boolean(chat.isMuted),
          isPinned: Boolean(chat.isPinned),
          isLocked: false,
          isHidden: false,
          isDeleted: false,
          archivedAt: new Date(chat.updatedAt || Date.now()),
          needsSync: false,
        }));

        console.log('📱 Loaded from offline database:', convertedChats.length, 'archived chats');
        setArchivedChats(convertedChats);
      } else {
        // Fallback to local chat management service
        const localChats = await localChatManagementService.getFilteredChats({
          includeArchived: true,
          includeDeleted: false,
          includeHidden: false,
        });

        // Filter only archived chats
        const archivedChats = localChats.filter(chat => chat.isArchived);
        console.log('📱 Loaded from local storage fallback:', archivedChats.length, 'archived chats');
        setArchivedChats(archivedChats);
      }
    } catch (error) {
      console.error('❌ Failed to load archived chats:', error);
      setArchivedChats([]);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [currentUser?.id]);

  useEffect(() => {
    if (currentUser?.id) {
      loadArchivedChats();
    }
  }, [currentUser?.id, loadArchivedChats]);

  const onRefresh = useCallback(() => {
    setRefreshing(true);
    loadArchivedChats();
  }, [loadArchivedChats]);

  const handleChatPress = (chat: ArchivedChat) => {
    Alert.alert(
      chat.name,
      `Archived on: ${chat.archivedAt?.toLocaleDateString() || 'Unknown'}\nLast message: ${chat.lastMessage}`,
      [
        { text: "Unarchive", onPress: () => handleUnarchiveChat(chat.id) },
        { text: "Delete Forever", style: "destructive", onPress: () => handleDeleteChat(chat.id) },
        { text: "Open Chat", onPress: () => handleOpenChat(chat) },
        { text: "Cancel", style: "cancel" }
      ]
    );
  };

  const handleUnarchiveChat = async (chatId: string) => {
    if (!currentUser?.id) return;

    try {
      console.log('📤 [DEBUG] Unarchiving chat:', chatId);

      // Unarchive in offline database (the actual data source)
      const { offlineDatabaseService } = await import('../src/services/offlineDatabase');
      await offlineDatabaseService.initialize();

      if (offlineDatabaseService.isReady()) {
        const db = offlineDatabaseService.getDatabase();
        await db.runAsync(`
          UPDATE chats SET
            isArchived = 0,
            updatedAt = ?
          WHERE id = ?
        `, [Date.now(), chatId]);

        console.log('✅ [DEBUG] Unarchived chat in offline DB:', chatId);
      }

      // Also unarchive using local service
      try {
        await localChatManagementService.unarchiveChats([chatId]);
      } catch (localError) {
        console.warn('⚠️ Local chat management unarchive failed:', localError);
      }

      // Also update in Firebase if online
      try {
        await realTimeMessagingService.unarchiveChat(chatId, currentUser.id);
      } catch (firebaseError) {
        console.warn('⚠️ Firebase unarchive failed:', firebaseError);
      }

      // Refresh the archived chats list
      await loadArchivedChats();

      Alert.alert('Success', 'Chat unarchived successfully');
      console.log('✅ [DEBUG] Successfully unarchived chat:', chatId);
    } catch (error) {
      console.error('❌ [DEBUG] Failed to unarchive chat:', error);
      Alert.alert('Error', 'Failed to unarchive chat');
    }
  };

  const handleDeleteChat = async (chatId: string) => {
    Alert.alert(
      'Delete Forever',
      'This will permanently delete this chat and all its messages. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete Forever',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement permanent chat deletion
              console.log('🗑️ [DEBUG] Permanently deleting chat:', chatId);
              Alert.alert('Info', 'Permanent deletion will be implemented soon');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete chat');
            }
          }
        }
      ]
    );
  };

  const handleOpenChat = (chat: ArchivedChat) => {
    // Navigate to the chat
    if (chat.isGroup) {
      router.push(`/group-chat/${chat.id}`);
    } else {
      router.push(`/chat/${chat.id}`);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    if (diffDays <= 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays <= 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  };

  const renderArchivedChat = ({ item }: { item: ArchivedChat }) => (
    <TouchableOpacity
      style={{
        backgroundColor: '#FFFFFF',
        marginHorizontal: 16,
        marginVertical: 4,
        padding: 16,
        borderRadius: 12,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
      }}
      onPress={() => handleChatPress(item)}
    >
      {/* Avatar */}
      <View style={{
        width: 50,
        height: 50,
        borderRadius: 25,
        backgroundColor: item.isGroup ? '#E5E7EB' : '#F3F4F6',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
      }}>
        {item.avatar ? (
          <Text style={{ fontSize: 20 }}>👤</Text>
        ) : (
          <Ionicons 
            name={item.isGroup ? "people" : "person"} 
            size={24} 
            color="#9CA3AF" 
          />
        )}
      </View>

      {/* Chat Info */}
      <View style={{ flex: 1 }}>
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 4 }}>
          <Text style={{
            fontSize: 16,
            fontWeight: '600',
            color: '#374151',
            flex: 1,
          }} numberOfLines={1}>
            {item.name}
          </Text>
          
          {/* Archived chats don't show unread count */}
        </View>

        <Text style={{
          fontSize: 14,
          color: '#6B7280',
          marginBottom: 4,
        }} numberOfLines={1}>
          {item.lastMessage}
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          {item.isGroup && (
            <Text style={{
              fontSize: 12,
              color: '#9CA3AF',
              marginRight: 8,
            }}>
              {item.participants.length} members •
            </Text>
          )}
          <Text style={{
            fontSize: 12,
            color: '#9CA3AF',
          }}>
            Archived {item.archivedAt ? formatDate(item.archivedAt.toISOString()) : 'Unknown'}
          </Text>
        </View>
      </View>

      {/* Archive Icon */}
      <View style={{
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: '#F3F4F6',
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 8,
      }}>
        <Ionicons name="archive" size={16} color="#6B7280" />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#F0F9FF' }}>
      {/* Header */}
      <View style={{
        backgroundColor: '#667eea',
        paddingTop: 55,
        paddingBottom: 8,
        paddingHorizontal: 20,
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={{ marginRight: 16, padding: 8 }}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: '#FFFFFF',
          }}>
            Archives
          </Text>
        </View>
      </View>

      {/* Info Banner */}
      <View style={{
        backgroundColor: '#E0F2FE',
        marginHorizontal: 16,
        marginTop: 16,
        padding: 12,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <Ionicons name="information-circle" size={20} color="#0284C7" style={{ marginRight: 8 }} />
        <Text style={{
          fontSize: 14,
          color: '#0C4A6E',
          flex: 1,
        }}>
          Archived chats are hidden from your main chat list but not deleted
        </Text>
      </View>

      {/* Archives List */}
      <FlatList
        data={archivedChats}
        renderItem={renderArchivedChat}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingVertical: 8 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={['#667eea']}
            tintColor={'#667eea'}
          />
        }
        ListEmptyComponent={
          loading ? (
            <View style={{
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 80,
            }}>
              <Ionicons name="refresh" size={32} color="#667eea" />
              <Text style={{
                fontSize: 16,
                color: '#667eea',
                marginTop: 12,
                fontWeight: '500',
              }}>
                Loading archived chats...
              </Text>
            </View>
          ) : (
            <View style={{
              alignItems: 'center',
              justifyContent: 'center',
              paddingVertical: 80,
            }}>
              <Ionicons name="archive-outline" size={64} color="#9CA3AF" />
              <Text style={{
                fontSize: 18,
                fontWeight: '600',
                color: '#6B7280',
                marginTop: 16,
                marginBottom: 8,
              }}>
                No Archived Chats
              </Text>
              <Text style={{
                fontSize: 14,
                color: '#9CA3AF',
                textAlign: 'center',
                paddingHorizontal: 40,
              }}>
                Chats you archive will appear here. They&apos;ll be hidden from your main chat list.
              </Text>
            </View>
          )
        }
      />
    </View>
  );
}
