/**
 * IraChat Business Advertising Platform Types
 * For businesses to advertise their products and services
 */

// ==================== USER TYPES ====================

export type UserType = 'client' | 'basic_subscriber' | 'premium_subscriber';

export interface BusinessUser {
  id: string;
  email: string;
  phoneNumber: string;
  fullName: string;
  userType: UserType;
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Subscription details
  subscriptionStatus: 'active' | 'expired' | 'cancelled';
  subscriptionStartDate?: Date;
  subscriptionEndDate?: Date;
  lastPaymentDate?: Date;
  
  // Business profile (only for subscribers)
  businessProfile?: BusinessProfile;
}

// ==================== BUSINESS PROFILE ====================

export interface BusinessProfile {
  id: string;
  userId: string;
  businessName: string;
  businessType: BusinessType;
  description: string;
  logo?: string;
  profilePhoto?: string; // Business profile photo
  coverImage?: string;
  coverPhoto?: string; // Business cover photo
  
  // Contact Information
  contactInfo: ContactInfo;
  
  // Location
  location: BusinessLocation;
  
  // Business Details
  establishedYear?: number;
  website?: string;
  socialMedia?: SocialMediaLinks;
  
  // Verification
  isVerified: boolean;
  verificationDate?: Date;
  verificationDocuments: string[]; // URLs to uploaded documents
  
  // Statistics
  totalPosts: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalShares: number;
  totalDownloads: number;
  
  // Settings
  allowDirectMessages: boolean;
  allowPhoneCalls: boolean;
  businessHours: BusinessHours[];
  
  createdAt: Date;
  updatedAt: Date;
}

export type BusinessType =
  // Retail & Commerce
  | 'retail' | 'wholesale' | 'supermarket' | 'convenience_store' | 'pharmacy' | 'bookstore'
  | 'electronics_store' | 'clothing_store' | 'jewelry_store' | 'furniture_store' | 'hardware_store'
  | 'gift_shop' | 'toy_store' | 'sports_store' | 'music_store' | 'art_gallery'

  // Food & Beverage
  | 'restaurant' | 'fast_food' | 'cafe' | 'bar' | 'bakery' | 'food_truck'
  | 'catering' | 'brewery' | 'ice_cream_shop' | 'pizza_place' | 'fine_dining'

  // Hospitality & Tourism
  | 'hotel' | 'motel' | 'resort' | 'hostel' | 'bed_breakfast' | 'vacation_rental'
  | 'travel_agency' | 'tour_operator' | 'car_rental' | 'event_venue'

  // Professional Services
  | 'law_firm' | 'accounting' | 'consulting' | 'marketing_agency' | 'advertising'
  | 'real_estate_agency' | 'insurance' | 'financial_services' | 'bank' | 'investment'
  | 'architecture' | 'engineering' | 'design_studio' | 'photography' | 'videography'

  // Healthcare & Wellness
  | 'hospital' | 'clinic' | 'dental_clinic' | 'veterinary' | 'pharmacy_medical'
  | 'physiotherapy' | 'mental_health' | 'spa' | 'massage_therapy' | 'fitness_center'
  | 'yoga_studio' | 'beauty_salon' | 'barbershop' | 'nail_salon'

  // Education & Training
  | 'school' | 'university' | 'college' | 'kindergarten' | 'daycare' | 'tutoring'
  | 'language_school' | 'driving_school' | 'music_school' | 'art_school'
  | 'vocational_training' | 'online_education'

  // Religious & Community
  | 'church' | 'mosque' | 'temple' | 'synagogue' | 'community_center'
  | 'non_profit' | 'charity' | 'social_services'

  // Technology & Digital
  | 'software_company' | 'web_development' | 'mobile_app_dev' | 'it_support'
  | 'cybersecurity' | 'data_analytics' | 'cloud_services' | 'telecommunications'

  // Manufacturing & Industrial
  | 'manufacturing' | 'construction' | 'contractor' | 'plumbing' | 'electrical'
  | 'carpentry' | 'painting' | 'roofing' | 'landscaping' | 'cleaning_services'

  // Transportation & Logistics
  | 'logistics' | 'shipping' | 'delivery' | 'taxi_service' | 'bus_company'
  | 'trucking' | 'moving_company' | 'courier'

  // Entertainment & Media
  | 'entertainment' | 'movie_theater' | 'music_venue' | 'nightclub' | 'casino'
  | 'amusement_park' | 'sports_club' | 'gym' | 'recreation_center'
  | 'media_production' | 'radio_station' | 'tv_station' | 'publishing'

  // Agriculture & Environment
  | 'agriculture' | 'farming' | 'livestock' | 'fishing' | 'forestry'
  | 'environmental_services' | 'waste_management' | 'recycling'

  // Government & Public
  | 'government' | 'public_services' | 'emergency_services' | 'postal_service'

  // Specialized Services
  | 'pet_services' | 'childcare' | 'elderly_care' | 'security_services'
  | 'maintenance_repair' | 'laundry_dry_cleaning' | 'storage_facility'

  // Other
  | 'other';

export interface ContactInfo {
  primaryPhone: string;
  secondaryPhone?: string;
  whatsappNumber?: string;
  email: string;
  alternativeEmail?: string;
}

export interface BusinessLocation {
  address: string;
  city: string;
  district: string;
  region: string;
  country: string;
  postalCode?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  landmark?: string;
  directions?: string;
}

export interface SocialMediaLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  youtube?: string;
  tiktok?: string;
  website?: string;
}

export interface BusinessHours {
  dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  isOpen: boolean;
  openTime?: string; // "09:00"
  closeTime?: string; // "17:00"
  breakStart?: string; // "12:00"
  breakEnd?: string; // "13:00"
}

// ==================== BUSINESS POSTS ====================

export interface BusinessPost {
  id: string;
  businessId: string;
  businessName: string;
  businessLogo?: string;
  businessType: BusinessType;
  isVerified: boolean;
  
  // Post Content
  title: string;
  description: string;
  media: BusinessMedia[];
  tags: string[];
  category: ProductCategory;
  
  // Product/Service Details
  price?: number;
  oldPrice?: number; // For strikethrough display
  currency: string;
  isNegotiable: boolean;
  priceHistory?: Array<{
    price: number;
    date: Date;
    reason: string;
  }>;
  availability: 'available' | 'out_of_stock' | 'discontinued';
  status: 'available' | 'out_of_stock' | 'new' | 'refurbished' | 'second_hand' | 'limited'; // New field

  // Location
  location: BusinessLocation;
  additionalLocations?: string[]; // Multiple locations

  // Contact Information
  contact: {
    phone: string;
    email: string;
  };

  // Product Details
  productDetails?: {
    warranty?: string;
    brand?: string;
    model?: string;
    condition?: string;
  };
  
  // Engagement
  views: number;
  likes: string[]; // User IDs who liked
  comments: BusinessComment[];
  shares: number;
  downloads: number;
  
  // Visibility
  isActive: boolean;
  isPinned: boolean;
  isPromoted: boolean;
  promotionEndsAt?: Date;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

export interface BusinessMedia {
  id: string;
  type: 'image' | 'video';
  url: string;
  thumbnailUrl?: string;
  duration?: number; // for videos, in seconds (max 180 = 3 minutes)
  width: number;
  height: number;
  fileSize: number;
  caption?: string;
  order: number;
}

export type ProductCategory =
  // Electronics & Technology
  | 'electronics' | 'computers' | 'smartphones' | 'tablets' | 'gaming' | 'audio_video'
  | 'cameras' | 'smart_home' | 'wearables' | 'accessories_tech'

  // Fashion & Apparel
  | 'fashion' | 'mens_clothing' | 'womens_clothing' | 'kids_clothing' | 'shoes'
  | 'bags_luggage' | 'jewelry' | 'watches' | 'sunglasses' | 'accessories_fashion'

  // Home & Living
  | 'home_garden' | 'furniture' | 'home_decor' | 'kitchen_dining' | 'bedding_bath'
  | 'lighting' | 'storage_organization' | 'garden_outdoor' | 'tools_hardware'
  | 'appliances' | 'cleaning_supplies'

  // Health & Beauty
  | 'beauty_health' | 'skincare' | 'makeup' | 'hair_care' | 'fragrances'
  | 'health_supplements' | 'medical_equipment' | 'fitness_equipment' | 'wellness'

  // Food & Beverages
  | 'food_drinks' | 'fresh_food' | 'packaged_food' | 'beverages' | 'alcohol'
  | 'organic_food' | 'specialty_food' | 'baby_food' | 'pet_food'

  // Automotive & Transportation
  | 'automotive' | 'cars' | 'motorcycles' | 'bicycles' | 'car_parts'
  | 'car_accessories' | 'tires' | 'automotive_services'

  // Sports & Recreation
  | 'sports_outdoors' | 'fitness' | 'outdoor_gear' | 'sports_equipment'
  | 'water_sports' | 'winter_sports' | 'team_sports' | 'individual_sports'

  // Entertainment & Media
  | 'books_media' | 'books' | 'movies_tv' | 'music' | 'video_games'
  | 'magazines' | 'digital_media' | 'collectibles'

  // Kids & Baby
  | 'toys_games' | 'baby_products' | 'kids_toys' | 'educational_toys'
  | 'baby_gear' | 'maternity' | 'kids_furniture'

  // Professional Services
  | 'services' | 'business_services' | 'professional_services' | 'personal_services'
  | 'home_services' | 'repair_maintenance' | 'cleaning_services' | 'security_services'

  // Education & Learning
  | 'education' | 'books_educational' | 'courses' | 'tutoring' | 'educational_materials'
  | 'school_supplies' | 'training_programs'

  // Real Estate & Property
  | 'real_estate' | 'residential' | 'commercial' | 'land' | 'rental_properties'
  | 'property_services' | 'construction_materials'

  // Agriculture & Farming
  | 'agriculture' | 'crops' | 'livestock' | 'farming_equipment' | 'seeds_plants'
  | 'fertilizers' | 'agricultural_services'

  // Business & Industrial
  | 'business_industrial' | 'office_supplies' | 'industrial_equipment'
  | 'raw_materials' | 'packaging' | 'safety_equipment'

  // Arts & Crafts
  | 'arts_crafts' | 'art_supplies' | 'craft_materials' | 'handmade' | 'vintage'
  | 'antiques' | 'musical_instruments'

  // Travel & Tourism
  | 'travel_tourism' | 'luggage' | 'travel_accessories' | 'maps_guides'
  | 'travel_services' | 'accommodation'

  // Pets & Animals
  | 'pets' | 'pet_supplies' | 'pet_food' | 'pet_accessories' | 'pet_services'
  | 'livestock_supplies'

  // Other
  | 'other';

export interface BusinessComment {
  id: string;
  postId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  comment: string;
  createdAt: Date;
  
  // Replies
  replies: BusinessCommentReply[];
}

export interface BusinessCommentReply {
  id: string;
  commentId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  reply: string;
  createdAt: Date;
}

// ==================== SUBSCRIPTION PLANS ====================

export interface SubscriptionPlan {
  id: string;
  name: string;
  type: UserType;
  price: number;
  currency: string;
  duration: number; // in days
  features: PlanFeature[];
  isActive: boolean;
  createdAt: Date;
}

export interface PlanFeature {
  id: string;
  name: string;
  description: string;
  isIncluded: boolean;
  limit?: number; // e.g., max posts per month
}

// Current subscription plans
export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'client',
    name: 'Client (Free)',
    type: 'client',
    price: 0,
    currency: 'USD',
    duration: 0, // Unlimited
    features: [
      { id: '1', name: 'View Business Posts', description: 'Browse all business advertisements', isIncluded: true },
      { id: '2', name: 'Like & Comment', description: 'Engage with business posts', isIncluded: true },
      { id: '3', name: 'Share Posts', description: 'Share business posts with others', isIncluded: true },
      { id: '4', name: 'Download Media', description: 'Download photos and videos', isIncluded: true },
      { id: '5', name: 'Chat with Businesses', description: 'Direct message business owners', isIncluded: true },
      { id: '6', name: 'Call Businesses', description: 'Make phone calls to businesses', isIncluded: true },
      { id: '7', name: 'Search & Filter', description: 'Find specific products and services', isIncluded: true },
      { id: '8', name: 'Zoom Media', description: 'Zoom in on photos and videos', isIncluded: true },
    ],
    isActive: true,
    createdAt: new Date(),
  },
  {
    id: 'basic',
    name: 'Basic Business',
    type: 'basic_subscriber',
    price: 10,
    currency: 'USD',
    duration: 30, // 30 days
    features: [
      { id: '1', name: 'All Client Features', description: 'Everything clients can do', isIncluded: true },
      { id: '2', name: 'Business Registration', description: 'Register your business profile', isIncluded: true },
      { id: '3', name: 'Post Products/Services', description: 'Upload photos and videos of your offerings', isIncluded: true, limit: 20 },
      { id: '4', name: 'Basic Analytics', description: 'View post performance metrics', isIncluded: true },
      { id: '5', name: 'Customer Messages', description: 'Receive and respond to customer inquiries', isIncluded: true },
      { id: '6', name: 'Business Hours', description: 'Set your operating hours', isIncluded: true },
      { id: '7', name: 'Contact Information', description: 'Display phone, email, location', isIncluded: true },
      { id: '8', name: 'Pin Posts', description: 'Pin important posts to top', isIncluded: false },
      { id: '9', name: 'Promote Posts', description: 'Boost post visibility', isIncluded: false },
      { id: '10', name: 'Advanced Analytics', description: 'Detailed insights and reports', isIncluded: false },
    ],
    isActive: true,
    createdAt: new Date(),
  },
  {
    id: 'premium',
    name: 'Premium Business',
    type: 'premium_subscriber',
    price: 25,
    currency: 'USD',
    duration: 30, // 30 days
    features: [
      { id: '1', name: 'All Basic Features', description: 'Everything basic subscribers get', isIncluded: true },
      { id: '2', name: 'Unlimited Posts', description: 'No limit on posts per month', isIncluded: true },
      { id: '3', name: 'Pin Posts', description: 'Pin up to 5 posts to top of profile', isIncluded: true, limit: 5 },
      { id: '4', name: 'Promote Posts', description: 'Boost post visibility in feeds', isIncluded: true, limit: 10 },
      { id: '5', name: 'Advanced Analytics', description: 'Detailed insights, demographics, trends', isIncluded: true },
      { id: '6', name: 'Priority Support', description: '24/7 customer support', isIncluded: true },
      { id: '7', name: 'Verification Badge', description: 'Blue checkmark for credibility', isIncluded: true },
      { id: '8', name: 'Custom Business Hours', description: 'Set complex schedules and breaks', isIncluded: true },
      { id: '9', name: 'Social Media Links', description: 'Link all your social media accounts', isIncluded: true },
      { id: '10', name: 'Featured Placement', description: 'Appear in featured businesses section', isIncluded: true },
      { id: '11', name: 'Bulk Upload', description: 'Upload multiple photos/videos at once', isIncluded: true },
      { id: '12', name: 'Video Thumbnails', description: 'Custom thumbnails for videos', isIncluded: true },
    ],
    isActive: true,
    createdAt: new Date(),
  },
];

// ==================== SEARCH & FILTER ====================

export interface SearchFilters {
  query?: string;
  category?: ProductCategory;
  businessType?: BusinessType;
  location?: {
    city?: string;
    district?: string;
    radius?: number; // in kilometers
  };
  priceRange?: {
    min?: number;
    max?: number;
  };
  availability?: 'available' | 'out_of_stock';
  isVerified?: boolean;
  sortBy?: 'newest' | 'oldest' | 'most_liked' | 'most_viewed' | 'price_low' | 'price_high';
}

export interface SearchResult {
  posts: BusinessPost[];
  totalCount: number;
  hasMore: boolean;
  nextPage?: number;
}

// ==================== ANALYTICS ====================

export interface BusinessAnalytics {
  businessId: string;
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  startDate: Date;
  endDate: Date;
  
  // Post Performance
  totalPosts: number;
  totalViews: number;
  totalLikes: number;
  totalComments: number;
  totalShares: number;
  totalDownloads: number;
  
  // Engagement Rates
  averageViewsPerPost: number;
  averageLikesPerPost: number;
  averageCommentsPerPost: number;
  engagementRate: number; // (likes + comments + shares) / views
  
  // Top Performing Posts
  topPosts: BusinessPost[];
  
  // Audience Insights (Premium only)
  audienceInsights?: {
    topLocations: { city: string; count: number }[];
    peakHours: { hour: number; engagement: number }[];
    topCategories: { category: ProductCategory; engagement: number }[];
  };
}

// ==================== API RESPONSE TYPES ====================

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasMore: boolean;
  };
  error?: string;
}
