/**
 * User Profile Service
 * Handles user profile data, shared media, and user interactions
 */

import { db } from './firebase';
import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  onSnapshot,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';

export interface UserProfile {
  id: string;
  name: string;
  avatar?: string;
  phoneNumber?: string;
  bio?: string;
  isOnline: boolean;
  lastSeen?: Date;
  joinedDate?: Date;
}

export interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'document';
  url: string;
  thumbnail?: string;
  timestamp: Date;
  chatId: string;
}

class UserProfileService {
  // Get user profile
  async getUserProfile(userId: string): Promise<UserProfile> {
    try {
      const userDoc = await getDoc(doc(db, 'userProfiles', userId));
      
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }
      
      const data = userDoc.data();
      return {
        id: userId,
        name: data.name || 'Unknown User',
        avatar: data.avatar,
        phoneNumber: data.phoneNumber,
        bio: data.bio,
        isOnline: data.isOnline || false,
        lastSeen: data.lastSeen ? (data.lastSeen.toDate ? data.lastSeen.toDate() : new Date(data.lastSeen)) : undefined,
        joinedDate: data.joinedDate ? (data.joinedDate.toDate ? data.joinedDate.toDate() : new Date(data.joinedDate)) : undefined,
      };
    } catch (error) {
      console.error('Failed to get user profile:', error);
      // Return default profile if user not found
      return {
        id: userId,
        name: 'Unknown User',
        isOnline: false,
      };
    }
  }

  // Update user profile
  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<void> {
    try {
      const userRef = doc(db, 'userProfiles', userId);
      await updateDoc(userRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Failed to update user profile:', error);
      throw error;
    }
  }

  // Set user online status
  async setUserOnlineStatus(userId: string, isOnline: boolean): Promise<void> {
    try {
      // Update user profile
      const userRef = doc(db, 'userProfiles', userId);
      const profileUpdates: any = {
        isOnline,
        updatedAt: serverTimestamp(),
      };

      if (!isOnline) {
        profileUpdates.lastSeen = serverTimestamp();
      }

      await updateDoc(userRef, profileUpdates);

      // Also update online status collection
      const statusRef = doc(db, 'onlineStatus', userId);
      const statusUpdates: any = {
        userId,
        isOnline,
        lastSeen: isOnline ? null : serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      await setDoc(statusRef, statusUpdates);
    } catch (error) {
      console.error('Failed to update online status:', error);
    }
  }

  // Subscribe to user online status
  subscribeToUserStatus(userId: string, callback: (profile: UserProfile) => void): () => void {
    const userRef = doc(db, 'userProfiles', userId);
    
    return onSnapshot(userRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data() as any;
        callback({
          id: userId,
          name: data.name || 'Unknown User',
          avatar: data.avatar,
          phoneNumber: data.phoneNumber,
          bio: data.bio,
          isOnline: data.isOnline || false,
          lastSeen: data.lastSeen ? (data.lastSeen.toDate ? data.lastSeen.toDate() : new Date(data.lastSeen)) : undefined,
          joinedDate: data.joinedDate ? (data.joinedDate.toDate ? data.joinedDate.toDate() : new Date(data.joinedDate)) : undefined,
        });
      }
    });
  }

  // Get shared media between two users
  async getSharedMedia(userId1: string, userId2: string): Promise<MediaItem[]> {
    try {
      const chatId = this.getChatId(userId1, userId2);

      // Query media collection for shared media
      const mediaRef = collection(db, 'media');
      const mediaQuery = query(
        mediaRef,
        where('chatId', '==', chatId),
        where('type', 'in', ['image', 'video', 'document']),
        orderBy('timestamp', 'desc'),
        limit(50)
      );

      const snapshot = await getDocs(mediaQuery);
      const mediaItems: MediaItem[] = [];

      snapshot.forEach((doc) => {
        const data = doc.data();
        mediaItems.push({
          id: doc.id,
          type: data.type,
          url: data.url,
          thumbnail: data.thumbnail,
          timestamp: data.timestamp.toDate(),
          chatId,
        });
      });

      return mediaItems;
    } catch (error) {
      console.error('Failed to get shared media:', error);
      return [];
    }
  }

  // Get mutual groups (placeholder for now)
  async getMutualGroups(userId1: string, userId2: string): Promise<string[]> {
    try {
      // This would query group memberships and find common groups
      // For now, return empty array
      return [];
    } catch (error) {
      console.error('Failed to get mutual groups:', error);
      return [];
    }
  }

  // Block user
  async blockUser(blockedUserId: string, blockingUserId: string): Promise<void> {
    try {
      const blockRef = doc(db, 'contactBlocks', `${blockingUserId}_${blockedUserId}`);
      await setDoc(blockRef, {
        blockingUserId,
        blockedUserId,
        blockedAt: serverTimestamp(),
        isActive: true,
      });
    } catch (error) {
      console.error('Failed to block user:', error);
      throw error;
    }
  }

  // Unblock user
  async unblockUser(blockedUserId: string, blockingUserId: string): Promise<void> {
    try {
      const unblockRef = doc(db, 'contactUnblocks', `${blockingUserId}_${blockedUserId}`);
      await setDoc(unblockRef, {
        blockingUserId,
        blockedUserId,
        unblockedAt: serverTimestamp(),
        isActive: true,
      });

      // Also update the block record
      const blockRef = doc(db, 'contactBlocks', `${blockingUserId}_${blockedUserId}`);
      await updateDoc(blockRef, {
        isActive: false,
        unblockedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Failed to unblock user:', error);
      throw error;
    }
  }

  // Get block status
  async getBlockStatus(userId: string, currentUserId: string): Promise<boolean> {
    try {
      const blockRef = doc(db, 'contactBlocks', `${currentUserId}_${userId}`);
      const blockDoc = await getDoc(blockRef);

      if (blockDoc.exists()) {
        const data = blockDoc.data();
        return data.isActive === true;
      }

      return false;
    } catch (error) {
      console.error('Failed to get block status:', error);
      return false;
    }
  }

  // Mute user
  async muteUser(mutedUserId: string, mutingUserId: string): Promise<void> {
    try {
      const muteRef = doc(db, 'users', mutingUserId, 'muted', mutedUserId);
      await setDoc(muteRef, {
        mutedAt: serverTimestamp(),
        mutedUserId,
      });
    } catch (error) {
      console.error('Failed to mute user:', error);
      throw error;
    }
  }

  // Unmute user
  async unmuteUser(mutedUserId: string, mutingUserId: string): Promise<void> {
    try {
      const muteRef = doc(db, 'users', mutingUserId, 'muted', mutedUserId);
      await setDoc(muteRef, {
        unmutedAt: serverTimestamp(),
        isMuted: false,
      });
    } catch (error) {
      console.error('Failed to unmute user:', error);
      throw error;
    }
  }

  // Get mute status
  async getMuteStatus(userId: string, currentUserId: string): Promise<boolean> {
    try {
      const muteRef = doc(db, 'users', currentUserId, 'muted', userId);
      const muteDoc = await getDoc(muteRef);
      
      if (muteDoc.exists()) {
        const data = muteDoc.data();
        return data.isMuted !== false; // Default to muted if not explicitly unmuted
      }
      
      return false;
    } catch (error) {
      console.error('Failed to get mute status:', error);
      return false;
    }
  }

  // Helper function to generate consistent chat ID
  private getChatId(userId1: string, userId2: string): string {
    return [userId1, userId2].sort().join('_');
  }

  // Initialize user profile (called when user first signs up)
  async initializeUserProfile(userId: string, userData: Partial<UserProfile>): Promise<void> {
    try {
      const userRef = doc(db, 'userProfiles', userId);
      await setDoc(userRef, {
        ...userData,
        id: userId,
        isOnline: true,
        joinedDate: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Failed to initialize user profile:', error);
      throw error;
    }
  }

  // Search users by name or phone
  async searchUsers(searchQuery: string, currentUserId: string): Promise<UserProfile[]> {
    try {
      const usersRef = collection(db, 'userProfiles');
      const nameQuery = query(
        usersRef,
        where('name', '>=', searchQuery),
        where('name', '<=', searchQuery + '\uf8ff'),
        limit(20)
      );

      const snapshot = await getDocs(nameQuery);
      const users: UserProfile[] = [];

      snapshot.forEach((doc) => {
        if (doc.id !== currentUserId) {
          const data = doc.data() as any;
          users.push({
            id: doc.id,
            name: data.name || 'Unknown User',
            avatar: data.avatar,
            phoneNumber: data.phoneNumber,
            bio: data.bio,
            isOnline: data.isOnline || false,
            lastSeen: data.lastSeen ? (data.lastSeen.toDate ? data.lastSeen.toDate() : new Date(data.lastSeen)) : undefined,
            joinedDate: data.joinedDate ? (data.joinedDate.toDate ? data.joinedDate.toDate() : new Date(data.joinedDate)) : undefined,
          });
        }
      });

      return users;
    } catch (error) {
      console.error('Failed to search users:', error);
      return [];
    }
  }
}

export const userProfileService = new UserProfileService();
