/**
 * 🧪 NAVIGATION TEST UTILITY
 * Test navigation routes and validate app routing configuration
 */

import { navigationService, ROUTES } from '../services/navigationService';
import { validateRoute } from './routeValidation';

export interface NavigationTestResult {
  route: string;
  success: boolean;
  error?: string;
  category: string;
}

/**
 * Test all defined routes to ensure they're valid
 */
export function testAllRoutes(): NavigationTestResult[] {
  const results: NavigationTestResult[] = [];
  
  function testRouteObject(obj: any, category: string = '') {
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        const route = obj[key];
        const validation = validateRoute(route);
        
        results.push({
          route,
          success: validation.isValid,
          error: validation.isValid ? undefined : validation.message,
          category: category || key
        });
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        testRouteObject(obj[key], category ? `${category}.${key}` : key);
      }
    }
  }
  
  testRouteObject(ROUTES);
  return results;
}

/**
 * Test critical navigation paths
 */
export function testCriticalPaths(): NavigationTestResult[] {
  const criticalRoutes = [
    { route: ROUTES.AUTH.WELCOME, category: 'Auth' },
    { route: ROUTES.AUTH.PHONE_REGISTER, category: 'Auth' },
    { route: ROUTES.AUTH.REGISTER, category: 'Auth' },
    { route: ROUTES.TABS.CHATS, category: 'Main' },
    { route: ROUTES.TABS.GROUPS, category: 'Main' },
    { route: ROUTES.TABS.BUSINESS, category: 'Main' },
    { route: ROUTES.TABS.CALLS, category: 'Main' },
    { route: ROUTES.TABS.STORIES, category: 'Main' },
  ];
  
  return criticalRoutes.map(({ route, category }) => {
    const validation = validateRoute(route);
    return {
      route,
      success: validation.isValid && validation.exists,
      error: validation.isValid ? undefined : validation.message,
      category
    };
  });
}

/**
 * Test navigation service methods
 */
export function testNavigationMethods(): { [key: string]: boolean } {
  const results: { [key: string]: boolean } = {};
  
  try {
    // Test if methods exist and are callable
    results.navigate = typeof navigationService.navigate === 'function';
    results.replace = typeof navigationService.replace === 'function';
    results.goBack = typeof navigationService.goBack === 'function';
    results.canGoBack = typeof navigationService.canGoBack === 'function';
    results.reset = typeof navigationService.reset === 'function';
    
    // Test auth helpers (NO LOGIN IN IRACHAT - users stay signed in)
    results.navigateToWelcome = typeof navigationService.navigateToWelcome === 'function';
    results.navigateToPhoneRegister = typeof navigationService.navigateToPhoneRegister === 'function';
    results.navigateToMainApp = typeof navigationService.navigateToMainApp === 'function';
    
    // Test chat helpers
    results.openChat = typeof navigationService.openChat === 'function';
    results.openNewChat = typeof navigationService.openNewChat === 'function';
    
    // Test call helpers
    results.startVideoCall = typeof navigationService.startVideoCall === 'function';
    results.startVoiceCall = typeof navigationService.startVoiceCall === 'function';
    
  } catch (error) {
    console.error('Error testing navigation methods:', error);
  }
  
  return results;
}

/**
 * Generate a navigation health report
 */
export function generateNavigationHealthReport(): {
  overall: 'healthy' | 'warning' | 'critical';
  summary: string;
  details: {
    totalRoutes: number;
    validRoutes: number;
    invalidRoutes: number;
    criticalPathsWorking: number;
    methodsWorking: number;
  };
  issues: string[];
} {
  const allRouteTests = testAllRoutes();
  const criticalPathTests = testCriticalPaths();
  const methodTests = testNavigationMethods();
  
  const validRoutes = allRouteTests.filter(test => test.success).length;
  const invalidRoutes = allRouteTests.length - validRoutes;
  const criticalPathsWorking = criticalPathTests.filter(test => test.success).length;
  const methodsWorking = Object.values(methodTests).filter(Boolean).length;
  
  const issues: string[] = [];
  
  // Check for issues
  if (invalidRoutes > 0) {
    issues.push(`${invalidRoutes} invalid routes found`);
  }
  
  if (criticalPathsWorking < criticalPathTests.length) {
    issues.push(`${criticalPathTests.length - criticalPathsWorking} critical paths not working`);
  }
  
  if (methodsWorking < Object.keys(methodTests).length) {
    issues.push(`${Object.keys(methodTests).length - methodsWorking} navigation methods not working`);
  }
  
  // Determine overall health
  let overall: 'healthy' | 'warning' | 'critical' = 'healthy';
  if (criticalPathsWorking < criticalPathTests.length || invalidRoutes > 5) {
    overall = 'critical';
  } else if (invalidRoutes > 0 || methodsWorking < Object.keys(methodTests).length) {
    overall = 'warning';
  }
  
  const summary = overall === 'healthy' 
    ? 'All navigation routes and methods are working correctly'
    : overall === 'warning'
    ? 'Some minor navigation issues detected'
    : 'Critical navigation issues detected that may affect app functionality';
  
  return {
    overall,
    summary,
    details: {
      totalRoutes: allRouteTests.length,
      validRoutes,
      invalidRoutes,
      criticalPathsWorking,
      methodsWorking
    },
    issues
  };
}

/**
 * Run a quick navigation health check (for development)
 */
export function quickNavigationCheck(): void {
  if (__DEV__) {
    console.log('🧪 Running Navigation Health Check...');
    
    const report = generateNavigationHealthReport();
    
    console.log(`📊 Navigation Health: ${report.overall.toUpperCase()}`);
    console.log(`📝 Summary: ${report.summary}`);
    console.log(`📈 Details:`, report.details);
    
    if (report.issues.length > 0) {
      console.warn('⚠️ Issues found:');
      report.issues.forEach(issue => console.warn(`  - ${issue}`));
    }
    
    // Test critical paths
    const criticalTests = testCriticalPaths();
    const failedCritical = criticalTests.filter(test => !test.success);
    
    if (failedCritical.length > 0) {
      console.error('❌ Failed Critical Paths:');
      failedCritical.forEach(test => {
        console.error(`  - ${test.route}: ${test.error}`);
      });
    } else {
      console.log('✅ All critical navigation paths are working');
    }

    // Test authentication flow
    console.log('\n🔐 Testing Authentication Flow:');
    testAuthenticationFlow();

    // Test tab navigation
    console.log('\n📋 Testing Tab Navigation:');
    testTabNavigation();
  }
}

/**
 * Test authentication flow navigation
 */
export function testAuthenticationFlow(): void {
  const authFlow = [
    { step: 'Welcome Screen', route: '/(auth)/welcome' },
    { step: 'Register Info', route: '/(auth)/register-info' },
    { step: 'Phone Register', route: '/(auth)/phone-register' },
    { step: 'Register Screen', route: '/(auth)/register' },
    { step: 'Login Screen', route: '/(auth)/index' },
  ];

  authFlow.forEach(step => {
    const validation = validateRoute(step.route);
    const status = validation.isValid ? '✅' : '❌';
    console.log(`${status} ${step.step}: ${step.route}`);
    if (!validation.isValid) {
      console.warn(`   Error: ${validation.message}`);
    }
  });
}

/**
 * Test tab navigation
 */
export function testTabNavigation(): void {
  const tabs = [
    { name: 'Chats Tab', route: '/(tabs)/index' },
    { name: 'Groups Tab', route: '/(tabs)/groups' },
    { name: 'Business Tab', route: '/(tabs)/business' },
    { name: 'Calls Tab', route: '/(tabs)/calls' },
    { name: 'Stories Tab', route: '/(tabs)/updates' },
    { name: 'Profile Tab', route: '/(tabs)/profile' },
    { name: 'Settings Tab', route: '/(tabs)/settings' },
  ];

  tabs.forEach(tab => {
    const validation = validateRoute(tab.route);
    const status = validation.isValid ? '✅' : '❌';
    console.log(`${status} ${tab.name}: ${tab.route}`);
    if (!validation.isValid) {
      console.warn(`   Error: ${validation.message}`);
    }
  });
}
