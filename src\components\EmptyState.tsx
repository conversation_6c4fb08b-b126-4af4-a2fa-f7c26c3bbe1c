/**
 * Beautiful Empty State Component for IraChat
 * Responsive design with animations and sky blue branding
 */

import React, { useRef, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS, ANIMATIONS } from '../styles/iraChatDesignSystem';
import { ResponsiveScale, ResponsiveSpacing, ResponsiveTypography, DeviceInfo } from '../utils/responsiveUtils';

interface EmptyStateProps {
  icon?: keyof typeof Ionicons.glyphMap;
  title: string;
  description?: string;
  actionText?: string;
  onActionPress?: () => void;
  animated?: boolean;
  variant?: 'default' | 'gradient' | 'minimal';
  style?: ViewStyle;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon = 'chatbubbles-outline',
  title,
  description,
  actionText,
  onActionPress,
  animated = true,
  variant = 'default',
  style,
}) => {
  // FIXED: Move all hooks to the top before any early returns
  // Beautiful animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const scaleAnimation = useRef(new Animated.Value(0.8)).current;
  const slideAnimation = useRef(new Animated.Value(30)).current;
  const animationRef = useRef<Animated.CompositeAnimation | null>(null);

  // FIXED: Safe action handler with validation
  const handleActionPress = useCallback(() => {
    try {
      if (onActionPress && typeof onActionPress === 'function') {
        onActionPress();
      }
    } catch (error) {
      console.error('Error in EmptyState action handler:', error);
    }
  }, [onActionPress]);

  // FIXED: Entrance animation with cleanup
  useEffect(() => {
    if (animated) {
      animationRef.current = Animated.sequence([
        Animated.delay(200),
        Animated.parallel([
          Animated.timing(fadeAnimation, {
            toValue: 1,
            duration: ANIMATIONS.normal,
            useNativeDriver: true,
          }),
          Animated.spring(scaleAnimation, {
            toValue: 1,
            tension: 50,
            friction: 8,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnimation, {
            toValue: 0,
            duration: ANIMATIONS.normal,
            useNativeDriver: true,
          }),
        ]),
      ]);
      animationRef.current.start();
    } else {
      fadeAnimation.setValue(1);
      scaleAnimation.setValue(1);
      slideAnimation.setValue(0);
    }

    // FIXED: Cleanup function to prevent memory leaks
    return () => {
      if (animationRef.current) {
        animationRef.current.stop();
      }
    };
  }, [animated, fadeAnimation, scaleAnimation, slideAnimation]);

  // FIXED: Input validation AFTER hooks
  if (!title || title.trim() === '') {
    console.error('EmptyState: title prop is required and cannot be empty');
    return null;
  }

  // FIXED: Proper variant handling with all three variants
  const renderIcon = () => {
    // Handle minimal variant
    if (variant === 'minimal') {
      return (
        <Animated.View
          style={[
            styles.iconContainer,
            {
              transform: [{ scale: scaleAnimation }],
            },
          ]}
        >
          <Ionicons
            name={icon}
            size={ResponsiveScale.iconSize(48)}
            color={IRACHAT_COLORS.textSecondary}
          />
        </Animated.View>
      );
    }

    return (
      <Animated.View
        style={[
          styles.iconContainer,
          {
            transform: [{ scale: scaleAnimation }],
          },
        ]}
      >
        {variant === 'gradient' ? (
          <LinearGradient
            colors={[IRACHAT_COLORS.primary, IRACHAT_COLORS.primaryDark]}
            style={styles.iconGradientBackground}
          >
            <Ionicons
              name={icon}
              size={ResponsiveScale.iconSize(64)}
              color={IRACHAT_COLORS.textOnPrimary}
            />
          </LinearGradient>
        ) : (
          <View style={styles.iconBackground}>
            <Ionicons
              name={icon}
              size={ResponsiveScale.iconSize(64)}
              color={IRACHAT_COLORS.primary}
            />
          </View>
        )}
      </Animated.View>
    );
  };

  // FIXED: Improved content rendering with proper variant handling
  const renderContent = () => (
    <Animated.View
      style={[
        styles.contentContainer,
        variant === 'minimal' && styles.contentContainerMinimal,
        {
          transform: [{ translateY: slideAnimation }],
        },
      ]}
    >
      <Text style={[
        styles.title,
        variant === 'minimal' && styles.titleMinimal
      ]}>
        {title}
      </Text>

      {description && description.trim() !== '' && (
        <Text style={[
          styles.description,
          variant === 'minimal' && styles.descriptionMinimal
        ]}>
          {description}
        </Text>
      )}

      {/* FIXED: Better action button logic - show if either actionText OR onActionPress exists */}
      {(actionText || onActionPress) && (
        <TouchableOpacity
          style={[
            styles.actionButton,
            variant === 'minimal' && styles.actionButtonMinimal
          ]}
          onPress={handleActionPress}
          activeOpacity={0.8}
          disabled={!onActionPress}
        >
          {variant === 'gradient' ? (
            <LinearGradient
              colors={[IRACHAT_COLORS.primary, IRACHAT_COLORS.primaryDark]}
              style={styles.actionButtonGradient}
            >
              <Text style={styles.actionButtonText}>
                {actionText || 'Action'}
              </Text>
            </LinearGradient>
          ) : variant === 'minimal' ? (
            <Text style={styles.actionButtonTextMinimal}>
              {actionText || 'Action'}
            </Text>
          ) : (
            <View style={styles.actionButtonDefault}>
              <Text style={styles.actionButtonText}>
                {actionText || 'Action'}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      )}
    </Animated.View>
  );

  // FIXED: Remove redundant opacity animation (already applied to content)
  const containerStyle = [
    styles.container,
    variant === 'minimal' && styles.containerMinimal,
    style
  ];

  return (
    <Animated.View style={[containerStyle, { opacity: fadeAnimation }]}>
      {variant !== 'minimal' && renderIcon()}
      {renderContent()}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.xl,
  },
  iconContainer: {
    marginBottom: ResponsiveSpacing.xl,
  },
  iconBackground: {
    width: ResponsiveScale.spacing(120),
    height: ResponsiveScale.spacing(120),
    borderRadius: ResponsiveScale.spacing(60),
    backgroundColor: `${IRACHAT_COLORS.primary}15`,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  iconGradientBackground: {
    width: ResponsiveScale.spacing(120),
    height: ResponsiveScale.spacing(120),
    borderRadius: ResponsiveScale.spacing(60),
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.md,
  },
  contentContainer: {
    alignItems: 'center',
    maxWidth: DeviceInfo.screenWidth * 0.8,
  },
  title: {
    fontSize: ResponsiveTypography.fontSize.xl,
    fontWeight: '600' as const,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.md,
  },
  description: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
    lineHeight: ResponsiveTypography.fontSize.base * 1.5,
    marginBottom: ResponsiveSpacing.xl,
  },
  actionButton: {
    marginTop: ResponsiveSpacing.md,
  },
  actionButtonDefault: {
    backgroundColor: IRACHAT_COLORS.primary,
    paddingHorizontal: ResponsiveSpacing.xl,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: ResponsiveScale.borderRadius(25),
    ...SHADOWS.sm,
  },
  actionButtonGradient: {
    paddingHorizontal: ResponsiveSpacing.xl,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: ResponsiveScale.borderRadius(25),
    ...SHADOWS.sm,
  },
  actionButtonText: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '500' as const,
    color: IRACHAT_COLORS.textOnPrimary,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
  },
  // FIXED: Added minimal variant styles
  containerMinimal: {
    paddingVertical: ResponsiveSpacing.lg,
  },
  contentContainerMinimal: {
    maxWidth: DeviceInfo.screenWidth * 0.9,
  },
  titleMinimal: {
    fontSize: ResponsiveTypography.fontSize.lg,
    marginBottom: ResponsiveSpacing.sm,
  },
  descriptionMinimal: {
    fontSize: ResponsiveTypography.fontSize.sm,
    marginBottom: ResponsiveSpacing.md,
  },
  actionButtonMinimal: {
    marginTop: ResponsiveSpacing.sm,
  },
  actionButtonTextMinimal: {
    fontSize: ResponsiveTypography.fontSize.sm,
    fontWeight: '500' as const,
    color: IRACHAT_COLORS.primary,
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
    textDecorationLine: 'underline',
  },
});

export default EmptyState;
