// 🎵 GLOBAL AUDIO MANAGER
// Manages all audio playback across the app with lock screen controls

import { Audio } from 'expo-av';
import * as MediaLibrary from 'expo-media-library';
import { Alert } from 'react-native';

export interface GlobalAudioTrack {
  id: string;
  title: string;
  artist: string;
  uri: string;
  thumbnailUrl?: string;
  duration?: number;
  source: 'local' | 'web' | 'voice' | 'music';
}

export interface AudioPlaybackState {
  isPlaying: boolean;
  currentTrack: GlobalAudioTrack | null;
  position: number;
  duration: number;
  isLoading: boolean;
}

type AudioStateListener = (state: AudioPlaybackState) => void;

class GlobalAudioManager {
  private static instance: GlobalAudioManager;
  private sound: Audio.Sound | null = null;
  private currentTrack: GlobalAudioTrack | null = null;
  private isPlaying = false;
  private position = 0;
  private duration = 0;
  private isLoading = false;
  private listeners: Set<AudioStateListener> = new Set();

  private constructor() {
    this.setupAudioMode();
  }

  static getInstance(): GlobalAudioManager {
    if (!GlobalAudioManager.instance) {
      GlobalAudioManager.instance = new GlobalAudioManager();
    }
    return GlobalAudioManager.instance;
  }

  /**
   * Setup audio mode for background playback with lock screen controls
   */
  private async setupAudioMode(): Promise<void> {
    try {
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: true,
        interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
        playThroughEarpieceAndroid: false,
      });

      // Enable background audio and lock screen controls
      await Audio.requestPermissionsAsync();
    } catch (error) {
      console.error('❌ Error setting up audio mode:', error);
    }
  }

  /**
   * Play or resume audio track
   */
  async playTrack(track: GlobalAudioTrack): Promise<void> {
    try {
      // Stop current track if different
      if (this.currentTrack?.id !== track.id) {
        await this.stop();
        this.currentTrack = track;
        this.isLoading = true;
        this.notifyListeners();

        // Load new track
        const { sound } = await Audio.Sound.createAsync(
          { uri: track.uri },
          {
            shouldPlay: true,
            isLooping: false,
            volume: 1.0,
          }
        );

        this.sound = sound;
        this.setupSoundCallbacks();
        this.setupLockScreenControls(track);
      } else if (this.sound) {
        // Resume current track
        await this.sound.playAsync();
      }

      this.isPlaying = true;
      this.isLoading = false;
      this.notifyListeners();
    } catch (error) {
      console.error('❌ Error playing track:', error);
      this.isLoading = false;
      this.notifyListeners();
    }
  }

  /**
   * Pause current track
   */
  async pause(): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.pauseAsync();
        this.isPlaying = false;
        this.notifyListeners();
      }
    } catch (error) {
      console.error('❌ Error pausing track:', error);
    }
  }

  /**
   * Stop and unload current track
   */
  async stop(): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.unloadAsync();
        this.sound = null;
      }
      this.currentTrack = null;
      this.isPlaying = false;
      this.position = 0;
      this.duration = 0;
      this.isLoading = false;
      this.notifyListeners();
    } catch (error) {
      console.error('❌ Error stopping track:', error);
    }
  }

  /**
   * Toggle play/pause
   */
  async togglePlayPause(): Promise<void> {
    if (this.isPlaying) {
      await this.pause();
    } else if (this.currentTrack) {
      await this.playTrack(this.currentTrack);
    }
  }

  /**
   * Seek to position
   */
  async seekTo(positionMillis: number): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.setPositionAsync(positionMillis);
        this.position = positionMillis;
        this.notifyListeners();
      }
    } catch (error) {
      console.error('❌ Error seeking:', error);
    }
  }

  /**
   * Setup sound callbacks for progress tracking
   */
  private setupSoundCallbacks(): void {
    if (!this.sound) return;

    this.sound.setOnPlaybackStatusUpdate((status) => {
      if (status.isLoaded) {
        this.position = status.positionMillis || 0;
        this.duration = status.durationMillis || 0;
        this.isPlaying = status.isPlaying || false;

        if (status.didJustFinish) {
          this.stop();
        }

        this.notifyListeners();
      }
    });
  }

  /**
   * Get current playback state
   */
  getState(): AudioPlaybackState {
    return {
      isPlaying: this.isPlaying,
      currentTrack: this.currentTrack,
      position: this.position,
      duration: this.duration,
      isLoading: this.isLoading,
    };
  }

  /**
   * Add state change listener
   */
  addListener(listener: AudioStateListener): void {
    this.listeners.add(listener);
  }

  /**
   * Remove state change listener
   */
  removeListener(listener: AudioStateListener): void {
    this.listeners.delete(listener);
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners(): void {
    const state = this.getState();
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('❌ Error in audio listener:', error);
      }
    });
  }

  /**
   * Check if any audio is currently playing
   */
  isAnyAudioPlaying(): boolean {
    return this.isPlaying && this.currentTrack !== null;
  }

  /**
   * Get current track info
   */
  getCurrentTrack(): GlobalAudioTrack | null {
    return this.currentTrack;
  }

  /**
   * Setup lock screen media controls
   */
  private async setupLockScreenControls(track: GlobalAudioTrack): Promise<void> {
    try {
      // This would typically use expo-av's media session or react-native-track-player
      // For now, we'll set up basic background audio support
      console.log(`🔒 Setting up lock screen controls for: ${track.title} by ${track.artist}`);

      // In a production app, you would integrate with:
      // - expo-av's Audio.setAudioModeAsync with media session
      // - react-native-track-player for full lock screen controls
      // - Platform-specific media session APIs

      // For now, the audio will continue playing in background
      // and can be controlled through the app's global audio player
    } catch (error) {
      console.error('❌ Error setting up lock screen controls:', error);
    }
  }

  /**
   * Format time for display
   */
  formatTime(milliseconds: number): string {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }
}

export const globalAudioManager = GlobalAudioManager.getInstance();
