#!/usr/bin/env node

// 🔥 IRACHAT CONFIGURATION VERIFICATION SCRIPT
// Verifies that all Firebase and Expo configurations are properly set up

const fs = require('fs');
const path = require('path');

console.log('🔥 IraChat Configuration Verification');
console.log('=====================================\n');

let hasErrors = false;

function checkError(condition, message) {
  if (!condition) {
    console.log(`❌ ${message}`);
    hasErrors = true;
  } else {
    console.log(`✅ ${message}`);
  }
}

function checkWarning(condition, message) {
  if (!condition) {
    console.log(`⚠️  ${message}`);
  } else {
    console.log(`✅ ${message}`);
  }
}

// Check .env file
console.log('📋 Checking Environment Configuration...');
const envExists = fs.existsSync('.env');
checkError(envExists, '.env file exists');

if (envExists) {
  const envContent = fs.readFileSync('.env', 'utf8');
  checkError(envContent.includes('EXPO_PUBLIC_FIREBASE_API_KEY=AIzaSyAegHkZvwnTt1s_J3QSlico6Rk6bDDaJH0'), 'Firebase API key is configured');
  checkError(envContent.includes('EXPO_PUBLIC_FIREBASE_PROJECT_ID=irachat-production'), 'Firebase project ID is configured');
  checkError(envContent.includes('EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=irachat-production.firebasestorage.app'), 'Firebase storage bucket is configured');
  checkError(envContent.includes('EXPO_PUBLIC_FIREBASE_APP_ID=1:1057313261320:android:ad54d524c2ea8979b24ebd'), 'Firebase app ID is configured');
}

console.log('\n📋 Checking Google Services File...');
const googleServicesExists = fs.existsSync('android/app/google-services.json');
checkError(googleServicesExists, 'google-services.json exists in android/app/');

if (googleServicesExists) {
  try {
    const googleServices = JSON.parse(fs.readFileSync('android/app/google-services.json', 'utf8'));
    checkError(true, 'google-services.json is valid JSON');

    // Check project info
    if (googleServices.project_info) {
      checkError(googleServices.project_info.project_id === 'irachat-production', 'Google Services project ID matches');
    } else {
      checkError(false, 'Google Services project_info section exists');
    }

    // Check client info
    if (googleServices.client && googleServices.client[0] && googleServices.client[0].android_client_info) {
      checkError(googleServices.client[0].android_client_info.package_name === 'IraChat.android', 'Google Services package name matches');
    } else {
      checkError(false, 'Google Services client info exists');
    }

    // Check API key
    if (googleServices.client && googleServices.client[0] && googleServices.client[0].api_key && googleServices.client[0].api_key[0]) {
      checkError(googleServices.client[0].api_key[0].current_key === 'AIzaSyAegHkZvwnTt1s_J3QSlico6Rk6bDDaJH0', 'Google Services API key matches');
    } else {
      checkError(false, 'Google Services API key exists');
    }
  } catch (error) {
    checkError(false, `google-services.json parsing failed - Error: ${error.message}`);
  }
}

console.log('\n📋 Checking App Configuration...');
const appConfigExists = fs.existsSync('app.config.js');
checkError(appConfigExists, 'app.config.js exists');

if (appConfigExists) {
  const appConfigContent = fs.readFileSync('app.config.js', 'utf8');
  checkError(appConfigContent.includes("package: 'IraChat.android'"), 'Android package name matches Google Services');
  checkError(appConfigContent.includes("bundleIdentifier: IS_DEV ? 'IraChat.ios.dev' : 'IraChat.ios'"), 'iOS bundle identifier is configured');
  checkError(appConfigContent.includes("projectId: 'irachat-production'"), 'EAS project ID matches Firebase');
}

console.log('\n📋 Checking Firebase Configuration Files...');
checkError(fs.existsSync('firebase.json'), 'firebase.json exists');
checkError(fs.existsSync('firestore.rules'), 'firestore.rules exists');
checkError(fs.existsSync('storage.rules'), 'storage.rules exists');
checkError(fs.existsSync('functions/src/index.ts'), 'Cloud Functions are configured');

console.log('\n📋 Checking Package Configuration...');
const packageJsonExists = fs.existsSync('package.json');
checkError(packageJsonExists, 'package.json exists');

if (packageJsonExists) {
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    checkError(packageJson.dependencies.firebase, 'Firebase SDK is installed');
    checkError(packageJson.dependencies['react-native-webrtc'], 'WebRTC is installed');
    checkError(packageJson.dependencies['@config-plugins/react-native-webrtc'], 'WebRTC config plugin is installed');
  } catch (error) {
    checkError(false, 'package.json is valid JSON');
  }
}

console.log('\n📋 Checking EAS Configuration...');
const easConfigExists = fs.existsSync('eas.json');
checkError(easConfigExists, 'eas.json exists');

console.log('\n📋 Security Checks...');
checkWarning(!fs.existsSync('.env.example'), '.env.example should be renamed to .env');
checkWarning(fs.existsSync('.gitignore'), '.gitignore exists to protect sensitive files');

if (fs.existsSync('.gitignore')) {
  const gitignoreContent = fs.readFileSync('.gitignore', 'utf8');
  checkWarning(gitignoreContent.includes('.env'), '.env is in .gitignore');
  checkWarning(gitignoreContent.includes('google-services.json'), 'google-services.json is in .gitignore (optional)');
}

console.log('\n🎯 Configuration Summary');
console.log('========================');

if (hasErrors) {
  console.log('❌ Configuration has errors that need to be fixed');
  console.log('\n🔧 Next Steps:');
  console.log('1. Fix the errors listed above');
  console.log('2. Run this script again to verify');
  console.log('3. Test the app with: npm run dev');
  process.exit(1);
} else {
  console.log('✅ All critical configurations are properly set up!');
  console.log('\n🚀 Your IraChat app is ready for:');
  console.log('• Development builds: npm run dev');
  console.log('• Production builds: npm run build:production');
  console.log('• Firebase deployment: firebase deploy');
  console.log('\n🔗 Useful links:');
  console.log('• Firebase Console: https://console.firebase.google.com/project/irachat-production');
  console.log('• Expo Dashboard: https://expo.dev/accounts/irachat/projects/irachat');
}
