import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Image,
  Pressable,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface GroupMember {
  id: string;
  name: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member';
  isOnline: boolean;
  lastSeen?: Date;
  joinedAt: Date;
}

interface MembersModalProps {
  visible: boolean;
  onClose: () => void;
  members: GroupMember[];
  currentUserRole: 'owner' | 'admin' | 'member';
  currentUserId?: string;
  onRemoveMember?: (userId: string, userName: string) => void;
  onPromoteMember?: (userId: string, userName: string) => void;
  onDemoteMember?: (userId: string, userName: string) => void;
}

export const MembersModal: React.FC<MembersModalProps> = ({
  visible,
  onClose,
  members,
  currentUserRole,
  currentUserId,
  onRemoveMember,
  onPromoteMember,
  onDemoteMember,
}) => {
  const [searchQuery, setSearchQuery] = useState('');

  const canManageMembers = currentUserRole === 'owner' || currentUserRole === 'admin';

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleMemberPress = (member: GroupMember) => {
    if (member.id === currentUserId) return;
    
    if (!canManageMembers) {
      // Just show member info for regular members
      Alert.alert(
        member.name,
        `Role: ${member.role}\nJoined: ${member.joinedAt.toLocaleDateString()}`,
        [{ text: 'OK' }]
      );
      return;
    }

    const actions = [];
    
    // Role management actions
    if (currentUserRole === 'owner' && member.role === 'member') {
      actions.push({
        text: 'Make Admin',
        onPress: () => onPromoteMember?.(member.id, member.name),
      });
    }
    
    if (currentUserRole === 'owner' && member.role === 'admin') {
      actions.push({
        text: 'Remove Admin',
        onPress: () => onDemoteMember?.(member.id, member.name),
      });
    }

    // Remove member action (owners can remove anyone except themselves, admins can remove regular members)
    if (member.role !== 'owner' && 
        (currentUserRole === 'owner' || 
         (currentUserRole === 'admin' && member.role === 'member'))) {
      actions.push({
        text: 'Remove from Group',
        style: 'destructive' as const,
        onPress: () => onRemoveMember?.(member.id, member.name),
      });
    }

    actions.push({ text: 'Cancel', style: 'cancel' as const });

    Alert.alert(member.name, 'Choose an action:', actions);
  };

  const renderMemberItem = ({ item: member }: { item: GroupMember }) => {
    const isCurrentUser = member.id === currentUserId;
    
    return (
      <TouchableOpacity
        onPress={() => handleMemberPress(member)}
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          paddingVertical: 12,
          paddingHorizontal: 20,
          borderBottomWidth: 1,
          borderBottomColor: '#E5E7EB',
        }}
        disabled={isCurrentUser && !canManageMembers}
      >
        {/* Avatar */}
        <View style={{ position: 'relative', marginRight: 12 }}>
          {member.avatar ? (
            <Image
              source={{ uri: member.avatar }}
              style={{ width: 50, height: 50, borderRadius: 25 }}
            />
          ) : (
            <View
              style={{
                width: 50,
                height: 50,
                borderRadius: 25,
                backgroundColor: '#87CEEB',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <Text style={{ color: 'white', fontSize: 18, fontWeight: 'bold' }}>
                {member.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          
          {/* Online indicator */}
          {member.isOnline && (
            <View
              style={{
                position: 'absolute',
                bottom: 2,
                right: 2,
                width: 14,
                height: 14,
                borderRadius: 7,
                backgroundColor: '#10B981',
                borderWidth: 2,
                borderColor: 'white',
              }}
            />
          )}
        </View>

        {/* Member info */}
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={{ fontSize: 16, fontWeight: '500', color: '#333' }}>
              {member.name}
              {isCurrentUser && ' (You)'}
            </Text>
            
            {/* Role badge */}
            {member.role !== 'member' && (
              <View
                style={{
                  marginLeft: 8,
                  paddingHorizontal: 8,
                  paddingVertical: 2,
                  borderRadius: 10,
                  backgroundColor: member.role === 'owner' ? '#F59E0B' : '#87CEEB',
                }}
              >
                <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>
                  {member.role.toUpperCase()}
                </Text>
              </View>
            )}
          </View>
          
          <Text style={{ fontSize: 12, color: '#666', marginTop: 2 }}>
            {member.isOnline ? 'Online' : 
             member.lastSeen ? `Last seen ${member.lastSeen.toLocaleDateString()}` : 
             'Offline'}
          </Text>
        </View>

        {/* Action indicator */}
        {canManageMembers && !isCurrentUser && (
          <Ionicons name="chevron-forward" size={20} color="#999" />
        )}
      </TouchableOpacity>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <Pressable
        style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'flex-end',
        }}
        onPress={onClose}
      >
        <View
          style={{
            backgroundColor: 'white',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            maxHeight: '80%',
          }}
        >
          {/* Header */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
              borderBottomWidth: 1,
              borderBottomColor: '#E5E7EB',
            }}
          >
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#333' }}>
              Members ({members.length})
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          {/* Search */}
          <View style={{ paddingHorizontal: 20, paddingVertical: 16 }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: '#F3F4F6',
                borderRadius: 12,
                paddingHorizontal: 12,
                paddingVertical: 8,
              }}
            >
              <Ionicons name="search" size={20} color="#9CA3AF" />
              <TextInput
                style={{
                  flex: 1,
                  marginLeft: 8,
                  fontSize: 16,
                  color: '#333',
                }}
                placeholder="Search members..."
                placeholderTextColor="#9CA3AF"
                value={searchQuery}
                onChangeText={setSearchQuery}
              />
            </View>
          </View>

          {/* Members list */}
          <FlatList
            data={filteredMembers}
            renderItem={renderMemberItem}
            keyExtractor={(item) => item.id}
            style={{ flex: 1 }}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={() => (
              <View style={{ padding: 40, alignItems: 'center' }}>
                <Ionicons name="people-outline" size={48} color="#9CA3AF" />
                <Text style={{ fontSize: 16, color: '#9CA3AF', marginTop: 12 }}>
                  No members found
                </Text>
              </View>
            )}
          />
        </View>
      </Pressable>
    </Modal>
  );
};
