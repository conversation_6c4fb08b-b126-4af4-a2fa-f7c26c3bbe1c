// 🔐 AUTHENTICATION PERSISTENCE HOOK
// React hook for managing persistent authentication state

import { useState, useEffect } from 'react';
import { useDispatch } from "react-redux";
import { useRouter } from 'expo-router';
import { onAuthStateChanged, User as FirebaseUser, getAuth } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { db, waitForAuth } from '../services/firebaseSimple';
import { authPersistenceService } from '../services/authPersistenceService';
import { logout, setLoading, setUser } from "../redux/userSlice";
import { User } from '../types';

export interface AuthPersistenceState {
  isInitializing: boolean;
  isAuthenticated: boolean;
  user: User | null;
}

/**
 * Custom hook that handles authentication persistence
 * Automatically checks for stored auth data on app launch
 * Manages Firebase auth state changes
 */
export const useAuthPersistence = (): AuthPersistenceState => {
  const dispatch = useDispatch();
  const [_isInitializing, _setIsInitializing] = useState(true);
  const [authState, setAuthState] = useState<AuthPersistenceState>({
    isInitializing: true,
    isAuthenticated: false,
    user: null,
  });

  // Removed problematic useSelector that was causing runtime errors

  useEffect(() => {
    let isMounted = true;
    let authCheckInterval: any = null;

    const initializeAuth = async () => {
      try {
        dispatch(setLoading(true));

        const authInstance = await waitForAuth(3000);

        // Use the newer authPersistenceService for session restoration
        const restoredUser = await authPersistenceService.restoreSession();

        if (restoredUser) {
          dispatch(setUser(restoredUser));

          if (isMounted) {
            setAuthState({
              isInitializing: false,
              isAuthenticated: true,
              user: restoredUser,
            });
          }
        } else {
          if (isMounted) {
            setAuthState({
              isInitializing: false,
              isAuthenticated: false,
              user: null,
            });
          }
        }

        if (!authInstance) {
          if (authCheckInterval) {
            clearInterval(authCheckInterval);
            authCheckInterval = null;
          }
          return null;
        }

        try {
          const unsubscribeAuth = onAuthStateChanged(
            authInstance,
            async (firebaseUser: FirebaseUser | null) => {
              if (!isMounted) return;

              try {
                if (firebaseUser) {


                  // Try to get additional user data from Firestore if available
                  let userData = {};
                  try {
                    if (db && typeof db === "object") {
                      const userDoc = await getDoc(
                        doc(db, "users", firebaseUser.uid),
                      );
                      userData = userDoc.exists() ? userDoc.data() : {};
                    }
                  } catch (firestoreError) {
                    // Silently handle Firestore errors
                  }

                  const user: User = {
                    id: firebaseUser.uid,
                    phoneNumber: firebaseUser.phoneNumber || "",
                    authMethod: 'phone' as const,
                    phoneVerified: true,
                    displayName:
                      firebaseUser.displayName || (userData as any).name || "",
                    name: (userData as any).name || "",
                    username: (userData as any).username || "",
                    avatar: (userData as any).avatar || "",
                    status:
                      (userData as any).status ||
                      (userData as any).bio ||
                      "I Love IraChat",
                    bio: (userData as any).bio || "I Love IraChat",
                    isOnline: true,
                    followersCount: (userData as any).followersCount || 0,
                    followingCount: (userData as any).followingCount || 0,
                    likesCount: (userData as any).likesCount || 0,
                  };

                  // Store session using the newer persistence service
                  await authPersistenceService.saveSession(user, firebaseUser);

                  // Update Redux store
                  dispatch(setUser(user));

                  // Track user authentication in Analytics (with error handling)
                  try {
                    // TODO: Add analytics tracking when analytics service is available
                  } catch (analyticsError) {
                    // Silently handle analytics errors
                  }

                  setAuthState({
                    isInitializing: false,
                    isAuthenticated: true,
                    user: user,
                  });

                } else {

                  // Clear session using the newer persistence service
                  await authPersistenceService.clearSession();
                  dispatch(logout());

                  setAuthState({
                    isInitializing: false,
                    isAuthenticated: false,
                    user: null,
                  });
                }
              } catch (error) {
                await authPersistenceService.clearSession();
                dispatch(logout());

                setAuthState({
                  isInitializing: false,
                  isAuthenticated: false,
                  user: null,
                });
              }
            },
          );

          // Store the unsubscribe function for cleanup
          return unsubscribeAuth;
        } catch (authListenerError) {
          return null;
        }
      } catch (error) {

        // Clear potentially corrupted data
        await authPersistenceService.clearSession();
        dispatch(logout());

        if (isMounted) {
          setAuthState({
            isInitializing: false,
            isAuthenticated: false,
            user: null,
          });
        }

        return null;
      } finally {
        if (isMounted) {
          _setIsInitializing(false);
          dispatch(setLoading(false));
        }
      }
    };

    // Initialize authentication and get unsubscribe function
    let unsubscribeAuth: (() => void) | null = null;

    initializeAuth()
      .then((unsubscribe) => {
        if (unsubscribe && isMounted) {
          unsubscribeAuth = unsubscribe;
        }
      })
      .catch(() => {
        // Silently handle initialization errors
      });

    // Set up periodic auth check for immediate registration detection
    let intervalCount = 0;
    const maxIntervalChecks = 50; // Maximum 50 checks (10 seconds at 200ms intervals)

    authCheckInterval = setInterval(async () => {
      if (!isMounted) return;

      intervalCount++;

      // Stop after maximum checks to prevent infinite loop
      if (intervalCount >= maxIntervalChecks) {
        if (authCheckInterval) {
          clearInterval(authCheckInterval);
          authCheckInterval = null;
        }
        return;
      }

      try {
        const restoredUser = await authPersistenceService.restoreSession();
        if (restoredUser && !authState.isAuthenticated) {
          dispatch(setUser(restoredUser));

          setAuthState({
            isInitializing: false,
            isAuthenticated: true,
            user: restoredUser,
          });

          // Clear interval once auth is detected
          if (authCheckInterval) {
            clearInterval(authCheckInterval);
            authCheckInterval = null;
          }
        } else if (!authState.isInitializing && !restoredUser && !authState.isAuthenticated) {
          // Clear interval if initialization is complete and no auth data exists
          if (authCheckInterval) {
            clearInterval(authCheckInterval);
            authCheckInterval = null;
          }
        }
      } catch (error) {
        // Silently handle auth check errors
      }
    }, 200); // Check every 200ms for immediate registration detection

    // Cleanup function
    return () => {
      isMounted = false;
      if (unsubscribeAuth) {
        unsubscribeAuth();
      }
      if (authCheckInterval) {
        clearInterval(authCheckInterval);
      }
    };
  }, [dispatch, authState.isAuthenticated]);

  return authState;
};

/**
 * Helper function to manually trigger logout
 */
export const useLogout = () => {
  const dispatch = useDispatch();
  const router = useRouter();

  return async () => {
    try {
      dispatch(logout());

      const authInstance = getAuth();

      if (authInstance && authInstance.signOut) {
        await authInstance.signOut();
      }

      await authPersistenceService.clearSession();

      setTimeout(() => {
        try {
          router.replace("/welcome");
        } catch (navError) {
          router.push("/welcome");
        }
      }, 100);
    } catch (error) {
      dispatch(logout());
      await authPersistenceService.clearSession();

      setTimeout(() => {
        try {
          router.replace("/welcome");
        } catch (navError) {
          router.push("/welcome");
        }
      }, 100);
    }
  };
};
