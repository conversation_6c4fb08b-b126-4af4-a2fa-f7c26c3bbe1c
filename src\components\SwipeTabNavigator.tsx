// Simplified Tab Navigator for IraChat
import { useRouter, useSegments } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { navigationService } from '../services/navigationService';

// Tab configuration for IraChat - 5 tabs (standardized order)
const TABS = [
  { name: 'Chats', route: '/(tabs)/', icon: 'chatbubbles' },
  { name: 'Groups', route: '/(tabs)/groups', icon: 'people' },
  { name: 'Business', route: '/(tabs)/business', icon: 'storefront' },
  { name: 'Calls', route: '/(tabs)/calls', icon: 'call' },
  { name: 'Stories', route: '/(tabs)/updates', icon: 'camera' },
];

interface SwipeTabNavigatorProps {
  children: React.ReactNode;
}

export const SwipeTabNavigator: React.FC<SwipeTabNavigatorProps> = ({ children }) => {
  // Simplified version without gesture handling to prevent errors
  return (
    <View style={{ flex: 1 }}>
      {children}
    </View>
  );
};

// Hook for programmatic tab navigation
export const useSwipeNavigation = () => {
  const _router = useRouter();
  const segments = useSegments();

  const getCurrentTabIndex = (): number => {
    const currentSegment = segments[segments.length - 1];

    switch (currentSegment) {
      case 'groups': return 1;
      case 'business': return 2;
      case 'calls': return 3;
      case 'updates': return 4; // Stories tab
      default: return 0; // Chats
    }
  };

  const navigateToTab = (index: number) => {
    if (index < 0 || index >= TABS.length) return;
    
    const targetTab = TABS[index];
    navigationService.navigate(targetTab.route as any);
  };

  return {
    currentTabIndex: getCurrentTabIndex(),
    navigateToTab,
    tabs: TABS,
  };
};
