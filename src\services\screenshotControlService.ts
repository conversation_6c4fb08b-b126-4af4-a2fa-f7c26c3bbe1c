import { Alert, Platform } from 'react-native';
import { realPrivacyService } from './realPrivacyService';
import { networkStateManager } from './networkStateManager';
import { offlineDatabaseService } from './offlineDatabase';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { db } from './firebaseSimple';

export interface ScreenshotAttempt {
  id: string;
  userId: string;
  attemptedBy: string;
  chatId: string;
  timestamp: Date;
  blocked: boolean;
  reason?: string;
}

class ScreenshotControlService {
  private isInitialized = false;
  private screenshotListeners: Map<string, () => void> = new Map();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Try to initialize database, but don't fail if it doesn't work
      await offlineDatabaseService.initialize();
      this.isInitialized = true;
      console.log('✅ ScreenshotControlService initialized with database');
    } catch (error) {
      console.error('Failed to initialize ScreenshotControlService:', error);
      // Continue without database support
      this.isInitialized = true;
      console.warn('⚠️ ScreenshotControlService will continue without database support');
    }
  }

  /**
   * Check if a user can take screenshots of another user's chat
   */
  async canTakeScreenshot(
    screenshotTakerId: string,
    chatOwnerId: string
  ): Promise<{ allowed: boolean; reason?: string }> {
    try {
      // Get chat owner's privacy settings
      const privacyResult = await realPrivacyService.getPrivacySettings(chatOwnerId);
      if (!privacyResult.success || !privacyResult.settings) {
        // If we can't get settings, allow by default (fail open)
        return { allowed: true };
      }

      const settings = privacyResult.settings;

      // Check if screenshot control is enabled
      if (!settings.screenshotControl) {
        return { allowed: true };
      }

      // Check screenshot control scope
      switch (settings.screenshotControlScope) {
        case 'everyone':
          return { allowed: true };
        
        case 'nobody':
          return { 
            allowed: false, 
            reason: 'This user has disabled screenshots for everyone' 
          };
        
        case 'contacts':
          // TODO: Implement contact checking
          // For now, allow contacts (simplified)
          return { allowed: true };
        
        case 'custom':
          const isAllowed = settings.screenshotControlCustomContacts?.includes(screenshotTakerId) || false;
          return { 
            allowed: isAllowed,
            reason: isAllowed ? undefined : 'You are not allowed to take screenshots of this chat'
          };
        
        default:
          return { allowed: false, reason: 'Unknown screenshot control setting' };
      }
    } catch (error) {
      console.error('Error checking screenshot permission:', error);
      // Fail open - allow screenshot if there's an error
      return { allowed: true };
    }
  }

  /**
   * Handle screenshot attempt
   */
  async handleScreenshotAttempt(
    screenshotTakerId: string,
    chatOwnerId: string,
    chatId: string
  ): Promise<{ blocked: boolean; message?: string }> {
    try {
      const permission = await this.canTakeScreenshot(screenshotTakerId, chatOwnerId);
      
      // Log the attempt
      await this.logScreenshotAttempt(
        screenshotTakerId,
        chatOwnerId,
        chatId,
        !permission.allowed,
        permission.reason
      );

      if (!permission.allowed) {
        // Show blocking message to user
        Alert.alert(
          'Screenshot Blocked',
          permission.reason || 'Screenshots are not allowed for this chat',
          [{ text: 'OK' }]
        );

        // Notify chat owner if they have screenshot notifications enabled
        await this.notifyScreenshotAttempt(screenshotTakerId, chatOwnerId, chatId);

        return { 
          blocked: true, 
          message: permission.reason 
        };
      }

      // Screenshot allowed - still notify if notifications are enabled
      await this.notifyScreenshotTaken(screenshotTakerId, chatOwnerId, chatId);

      return { blocked: false };
    } catch (error) {
      console.error('Error handling screenshot attempt:', error);
      return { blocked: false }; // Fail open
    }
  }

  /**
   * Log screenshot attempt to database
   */
  private async logScreenshotAttempt(
    attemptedBy: string,
    userId: string,
    chatId: string,
    blocked: boolean,
    reason?: string
  ): Promise<void> {
    try {
      const attemptId = `${attemptedBy}_${userId}_${Date.now()}`;
      const attempt: ScreenshotAttempt = {
        id: attemptId,
        userId,
        attemptedBy,
        chatId,
        timestamp: new Date(),
        blocked,
        reason
      };

      // Try to save locally (don't fail if database is unavailable)
      try {
        if (offlineDatabaseService.isReady()) {
          const localDb = offlineDatabaseService.getDatabase();
          if (localDb) {
            await localDb.runAsync(`
              INSERT OR REPLACE INTO screenshot_attempts (
                id, userId, attemptedBy, chatId, timestamp, blocked, reason
              ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
        attempt.id,
        attempt.userId,
        attempt.attemptedBy,
        attempt.chatId,
        attempt.timestamp.getTime(),
        attempt.blocked ? 1 : 0,
        attempt.reason || null
      ]);
          }
        }
      } catch (dbError) {
        console.warn('⚠️ Failed to save screenshot attempt to local database:', dbError);
      }

      // Save to Firebase if online
      if (networkStateManager.isOnline()) {
        try {
          await setDoc(doc(db, 'screenshot_attempts', attemptId), {
            ...attempt,
            timestamp: serverTimestamp()
          });
        } catch (error) {
          console.error('Failed to save screenshot attempt to Firebase:', error);
        }
      }
    } catch (error) {
      console.error('Error logging screenshot attempt:', error);
    }
  }

  /**
   * Notify chat owner of screenshot attempt (blocked)
   */
  private async notifyScreenshotAttempt(
    attemptedBy: string,
    chatOwnerId: string,
    chatId: string
  ): Promise<void> {
    try {
      // Get chat owner's privacy settings to check if notifications are enabled
      const privacyResult = await realPrivacyService.getPrivacySettings(chatOwnerId);
      if (!privacyResult.success || !privacyResult.settings?.screenshotNotification) {
        return;
      }

      // TODO: Implement push notification or in-app notification
      // For now, just log the event
      console.log(`Screenshot attempt blocked: ${attemptedBy} tried to screenshot ${chatOwnerId}'s chat ${chatId}`);
    } catch (error) {
      console.error('Error notifying screenshot attempt:', error);
    }
  }

  /**
   * Notify chat owner of successful screenshot
   */
  private async notifyScreenshotTaken(
    takenBy: string,
    chatOwnerId: string,
    chatId: string
  ): Promise<void> {
    try {
      // Get chat owner's privacy settings to check if notifications are enabled
      const privacyResult = await realPrivacyService.getPrivacySettings(chatOwnerId);
      if (!privacyResult.success || !privacyResult.settings?.screenshotNotification) {
        return;
      }

      // TODO: Implement push notification or in-app notification
      // For now, just log the event
      console.log(`Screenshot taken: ${takenBy} took a screenshot of ${chatOwnerId}'s chat ${chatId}`);
    } catch (error) {
      console.error('Error notifying screenshot taken:', error);
    }
  }

  /**
   * Get screenshot attempts for a user
   */
  async getScreenshotAttempts(userId: string): Promise<ScreenshotAttempt[]> {
    try {
      // Check if database is available
      if (!offlineDatabaseService.isReady()) {
        console.warn('⚠️ Offline database not ready for screenshot attempts');
        return [];
      }

      const localDb = offlineDatabaseService.getDatabase();
      if (!localDb) {
        console.warn('⚠️ Database is null for screenshot attempts');
        return [];
      }

      const attempts = await localDb.getAllAsync(`
        SELECT * FROM screenshot_attempts
        WHERE userId = ?
        ORDER BY timestamp DESC
        LIMIT 100
      `, [userId]) as Array<{
        id: string;
        userId: string;
        attemptedBy: string;
        chatId: string;
        timestamp: number;
        blocked: number;
        reason?: string;
      }>;

      return attempts.map(attempt => ({
        id: attempt.id,
        userId: attempt.userId,
        attemptedBy: attempt.attemptedBy,
        chatId: attempt.chatId,
        timestamp: new Date(attempt.timestamp),
        blocked: Boolean(attempt.blocked),
        reason: attempt.reason
      }));
    } catch (error) {
      console.error('Error getting screenshot attempts:', error);
      return [];
    }
  }

  /**
   * Enable screenshot protection for a chat screen
   * This would integrate with native screenshot detection
   */
  enableScreenshotProtection(
    chatId: string,
    screenshotTakerId: string,
    chatOwnerId: string
  ): void {
    // Platform-specific screenshot detection would go here
    // For now, this is a placeholder for future native implementation
    
    if (Platform.OS === 'ios') {
      // iOS screenshot detection implementation
      // Would use native modules to detect screenshot events
    } else if (Platform.OS === 'android') {
      // Android screenshot detection implementation
      // Would use native modules to detect screenshot events
    }

    console.log(`Screenshot protection enabled for chat ${chatId}`);
  }

  /**
   * Disable screenshot protection for a chat screen
   */
  disableScreenshotProtection(chatId: string): void {
    const listener = this.screenshotListeners.get(chatId);
    if (listener) {
      // Remove screenshot listener
      this.screenshotListeners.delete(chatId);
    }

    console.log(`Screenshot protection disabled for chat ${chatId}`);
  }

  cleanup(): void {
    // Clean up all screenshot listeners
    this.screenshotListeners.clear();
    this.isInitialized = false;
  }
}

export const screenshotControlService = new ScreenshotControlService();
