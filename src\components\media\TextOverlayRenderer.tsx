import React from 'react';
import {
  View,
  Text,
  StyleSheet,
} from 'react-native';
import { TextOverlay } from '../../types/Update';

interface TextOverlayRendererProps {
  textOverlays: TextOverlay[];
  mediaWidth: number;
  mediaHeight: number;
  style?: any;
}

export const TextOverlayRenderer: React.FC<TextOverlayRendererProps> = ({
  textOverlays,
  mediaWidth,
  mediaHeight,
  style,
}) => {
  if (!textOverlays || textOverlays.length === 0) {
    return null;
  }

  // Sort overlays by zIndex to ensure proper layering
  const sortedOverlays = [...textOverlays].sort((a, b) => (a.zIndex || 1) - (b.zIndex || 1));

  return (
    <View style={[styles.container, { width: mediaWidth, height: mediaHeight }, style]}>
      {sortedOverlays.map((overlay) => (
        <View
          key={overlay.id}
          style={[
            styles.textOverlay,
            {
              left: (overlay.position.x / 100) * mediaWidth,
              top: (overlay.position.y / 100) * mediaHeight,
              width: (overlay.size.width / 100) * mediaWidth,
              minHeight: (overlay.size.height / 100) * mediaHeight,
              transform: [
                { rotate: `${overlay.rotation || 0}deg` },
              ],
              zIndex: overlay.zIndex || 1,
            },
          ]}
        >
          <Text
            style={[
              styles.overlayText,
              {
                fontSize: overlay.style.fontSize,
                color: overlay.style.color,
                backgroundColor: overlay.style.backgroundColor,
                textAlign: overlay.style.textAlign,
                fontWeight: overlay.style.fontWeight,
                opacity: overlay.style.opacity || 1,
                fontFamily: overlay.style.fontFamily,
                borderColor: overlay.style.borderColor,
                borderWidth: overlay.style.borderWidth || 0,
                borderRadius: overlay.style.borderRadius || 0,
                shadowColor: overlay.style.shadowColor,
                shadowOffset: overlay.style.shadowOffset && 'x' in overlay.style.shadowOffset
                  ? { width: overlay.style.shadowOffset.x, height: overlay.style.shadowOffset.y }
                  : overlay.style.shadowOffset || { width: 0, height: 0 },
                shadowOpacity: overlay.style.shadowOpacity || 0,
                shadowRadius: overlay.style.shadowRadius || 0,
              },
            ]}
            numberOfLines={0}
          >
            {overlay.text}
          </Text>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    pointerEvents: 'none', // Allow touches to pass through to the media below
  },
  textOverlay: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  overlayText: {
    textAlign: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    flexWrap: 'wrap',
  },
});
