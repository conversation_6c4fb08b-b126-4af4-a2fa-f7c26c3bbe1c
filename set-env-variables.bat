@echo off
echo Setting up environment variables for Android development...

REM Set Android environment variables permanently
setx ANDROID_HOME "F:\Android\Sdk"
setx ANDROID_SDK_ROOT "F:\Android\Sdk"

REM Add Android tools to user PATH permanently
for /f "tokens=2*" %%A in ('reg query "HKCU\Environment" /v PATH 2^>nul') do set "current_path=%%B"
if not defined current_path set "current_path="

REM Check if Android paths are already in PATH
echo %current_path% | findstr /C:"F:\Android\Sdk\platform-tools" >nul
if %ERRORLEVEL% NEQ 0 (
    setx PATH "%current_path%;F:\Android\Sdk\platform-tools;F:\Android\Sdk\cmdline-tools\latest\bin;F:\Android\Sdk\emulator"
    echo ✓ Android tools added to PATH
) else (
    echo ✓ Android tools already in PATH
)

echo.
echo ✅ Android environment variables set!
echo.
echo Next steps:
echo 1. Install Java JDK 17 from: https://adoptium.net/temurin/releases/
echo 2. After installing Java, set JAVA_HOME:
echo    setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-17.x.x.x-hotspot"
echo    (Replace x.x.x.x with your actual version)
echo 3. Restart your command prompt/IDE
echo 4. Run: npm run android
echo.
pause
