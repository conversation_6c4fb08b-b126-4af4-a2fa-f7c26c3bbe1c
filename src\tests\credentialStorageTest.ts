// 🧪 CREDENTIAL STORAGE TEST
// Simple test to verify credential storage and retrieval functionality

import { credentialStorageService } from '../services/credentialStorageService';

/**
 * Test email credential storage and retrieval
 */
export const testEmailCredentials = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing email credential storage...');

    // Test data
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword123';

    // Clear any existing credentials
    await credentialStorageService.clearEmailCredentials();

    // Test saving credentials
    await credentialStorageService.saveEmailCredentials(testEmail, testPassword, true);
    console.log('✅ Email credentials saved');

    // Test retrieving credentials
    const retrievedCredentials = await credentialStorageService.getEmailCredentials();
    
    if (!retrievedCredentials) {
      console.error('❌ Failed to retrieve email credentials');
      return false;
    }

    if (retrievedCredentials.email !== testEmail || retrievedCredentials.password !== testPassword) {
      console.error('❌ Retrieved credentials do not match saved credentials');
      console.error('Expected:', { email: testEmail, password: testPassword });
      console.error('Retrieved:', retrievedCredentials);
      return false;
    }

    console.log('✅ Email credentials retrieved correctly');

    // Test remember preference
    const rememberPreference = await credentialStorageService.getRememberPreference();
    if (!rememberPreference) {
      console.error('❌ Remember preference not saved correctly');
      return false;
    }

    console.log('✅ Remember preference saved correctly');

    // Test clearing credentials
    await credentialStorageService.clearEmailCredentials();
    const clearedCredentials = await credentialStorageService.getEmailCredentials();
    
    if (clearedCredentials !== null) {
      console.error('❌ Credentials not cleared properly');
      return false;
    }

    console.log('✅ Email credentials cleared correctly');
    console.log('🎉 Email credential test passed!');
    return true;

  } catch (error) {
    console.error('❌ Email credential test failed:', error);
    return false;
  }
};

/**
 * Test phone credential storage and retrieval
 */
export const testPhoneCredentials = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing phone credential storage...');

    // Test data
    const testPhoneNumber = '+256701234567';

    // Clear any existing credentials
    await credentialStorageService.clearPhoneCredentials();

    // Test saving credentials
    await credentialStorageService.savePhoneCredentials(testPhoneNumber, true);
    console.log('✅ Phone credentials saved');

    // Test retrieving credentials
    const retrievedCredentials = await credentialStorageService.getPhoneCredentials();
    
    if (!retrievedCredentials) {
      console.error('❌ Failed to retrieve phone credentials');
      return false;
    }

    if (retrievedCredentials.phoneNumber !== testPhoneNumber) {
      console.error('❌ Retrieved phone number does not match saved phone number');
      console.error('Expected:', testPhoneNumber);
      console.error('Retrieved:', retrievedCredentials.phoneNumber);
      return false;
    }

    console.log('✅ Phone credentials retrieved correctly');

    // Test clearing credentials
    await credentialStorageService.clearPhoneCredentials();
    const clearedCredentials = await credentialStorageService.getPhoneCredentials();
    
    if (clearedCredentials !== null) {
      console.error('❌ Phone credentials not cleared properly');
      return false;
    }

    console.log('✅ Phone credentials cleared correctly');
    console.log('🎉 Phone credential test passed!');
    return true;

  } catch (error) {
    console.error('❌ Phone credential test failed:', error);
    return false;
  }
};

/**
 * Test credential expiration
 */
export const testCredentialExpiration = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing credential expiration...');

    // This test would require mocking the Date.now() function to simulate old credentials
    // For now, we'll just test that the expiration logic exists
    
    const testEmail = '<EMAIL>';
    const testPassword = 'testpassword';

    // Save credentials
    await credentialStorageService.saveEmailCredentials(testEmail, testPassword, true);
    
    // Retrieve immediately (should work)
    const freshCredentials = await credentialStorageService.getEmailCredentials();
    if (!freshCredentials) {
      console.error('❌ Fresh credentials should be retrievable');
      return false;
    }

    console.log('✅ Fresh credentials retrieved correctly');
    
    // Clean up
    await credentialStorageService.clearEmailCredentials();
    
    console.log('🎉 Credential expiration test passed!');
    return true;

  } catch (error) {
    console.error('❌ Credential expiration test failed:', error);
    return false;
  }
};

/**
 * Run all credential storage tests
 */
export const runAllCredentialTests = async (): Promise<void> => {
  console.log('🚀 Starting credential storage tests...');
  
  const emailTest = await testEmailCredentials();
  const phoneTest = await testPhoneCredentials();
  const expirationTest = await testCredentialExpiration();
  
  const allPassed = emailTest && phoneTest && expirationTest;
  
  if (allPassed) {
    console.log('🎉 All credential storage tests passed!');
    console.log('✅ Credential auto-fill functionality is working correctly');
  } else {
    console.error('❌ Some credential storage tests failed');
    console.error('❌ Credential auto-fill functionality may not work properly');
  }
  
  // Clean up any test data
  await credentialStorageService.clearAllCredentials();
  console.log('🧹 Test cleanup completed');
};

/**
 * Quick test function that can be called from the app
 */
export const quickCredentialTest = async (): Promise<void> => {
  try {
    console.log('⚡ Running quick credential test...');
    
    // Test email credentials
    await credentialStorageService.saveEmailCredentials('<EMAIL>', 'quickpass', true);
    const emailCreds = await credentialStorageService.getEmailCredentials();
    console.log('📧 Email test result:', emailCreds ? '✅ PASS' : '❌ FAIL');
    
    // Test phone credentials
    await credentialStorageService.savePhoneCredentials('+256123456789', true);
    const phoneCreds = await credentialStorageService.getPhoneCredentials();
    console.log('📱 Phone test result:', phoneCreds ? '✅ PASS' : '❌ FAIL');
    
    // Clean up
    await credentialStorageService.clearAllCredentials();
    console.log('⚡ Quick test completed');
    
  } catch (error) {
    console.error('❌ Quick credential test failed:', error);
  }
};
