// 🚀 APP INITIALIZATION SERVICE
// Handles app startup, session restoration, and initial setup

import { authPersistenceService } from './authPersistenceService';
import { auth } from './firebaseSimple';
import { onAuthStateChanged } from 'firebase/auth';
import { User } from '../types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

interface InitializationResult {
  isLoggedIn: boolean;
  user?: User;
  shouldShowOnboarding: boolean;
  error?: string;
}

export class AppInitializationService {
  private static instance: AppInitializationService;
  private isInitialized = false;
  private currentUser: User | null = null;

  static getInstance(): AppInitializationService {
    if (!AppInitializationService.instance) {
      AppInitializationService.instance = new AppInitializationService();
    }
    return AppInitializationService.instance;
  }

  /**
   * Initialize the app and restore user session
   */
  async initialize(): Promise<InitializationResult> {
    try {
      console.log('🚀 Initializing IraChat app...');

      // Check if this is the first app launch
      const shouldShowOnboarding = await this.checkFirstLaunch();

      // Initialize persistence service
      await authPersistenceService.initialize();

      // Check for logout scenarios
      const shouldLogout = await authPersistenceService.checkLogoutScenarios();
      if (shouldLogout) {
        return {
          isLoggedIn: false,
          shouldShowOnboarding,
        };
      }

      // Try to restore session
      const restoredUser = await authPersistenceService.restoreSession();
      
      if (restoredUser) {
        console.log('✅ Session restored successfully');
        this.currentUser = restoredUser;
        this.isInitialized = true;
        
        return {
          isLoggedIn: true,
          user: restoredUser,
          shouldShowOnboarding: false, // Don't show onboarding if user is logged in
        };
      }

      // No session to restore
      console.log('📭 No valid session found');
      this.isInitialized = true;
      
      return {
        isLoggedIn: false,
        shouldShowOnboarding,
      };
    } catch (error) {
      console.error('❌ App initialization failed:', error);
      this.isInitialized = true;
      
      return {
        isLoggedIn: false,
        shouldShowOnboarding: await this.checkFirstLaunch(),
        error: error instanceof Error ? error.message : 'Initialization failed',
      };
    }
  }

  /**
   * Handle user login (save session)
   */
  async handleUserLogin(user: User): Promise<void> {
    try {
      console.log('👤 Handling user login...');
      
      if (auth.currentUser) {
        await authPersistenceService.saveSession(user, auth.currentUser);
        this.currentUser = user;
        console.log('✅ User session saved');
      } else {
        console.warn('⚠️ No Firebase user found during login');
      }
    } catch (error) {
      console.error('❌ Failed to handle user login:', error);
      throw error;
    }
  }

  /**
   * Handle user logout
   */
  async handleUserLogout(): Promise<void> {
    try {
      console.log('👋 Handling user logout...');

      // Only clear session if this is an intentional logout, not a temporary error
      if (this.isIntentionalLogout) {
        await authPersistenceService.clearSession();

        // Sign out from Firebase
        if (auth.currentUser) {
          await auth.signOut();
        }

        // Clear any temporary data
        await this.clearTemporaryData();

        this.currentUser = null;
        this.isIntentionalLogout = false; // Reset flag
        console.log('✅ User logged out successfully');
      } else {
        console.log('⚠️ Skipping session clear - not an intentional logout');
      }
    } catch (error) {
      console.error('❌ Failed to handle user logout:', error);
      throw error;
    }
  }

  private isIntentionalLogout = false;

  /**
   * Mark next logout as intentional (user-initiated)
   */
  markIntentionalLogout(): void {
    this.isIntentionalLogout = true;
  }

  /**
   * Handle manual logout (user initiated)
   */
  async handleManualLogout(): Promise<void> {
    try {
      console.log('🚪 Handling manual logout...');
      await this.handleUserLogout();
    } catch (error) {
      console.error('❌ Failed to handle manual logout:', error);
      throw error;
    }
  }

  /**
   * Handle app data/cache clear
   */
  async handleAppDataClear(): Promise<void> {
    try {
      console.log('🗑️ Handling app data clear...');
      
      // Clear all stored data
      await authPersistenceService.clearSession();
      await this.clearTemporaryData();
      await this.clearAllAppData();
      
      this.currentUser = null;
      console.log('✅ App data cleared successfully');
    } catch (error) {
      console.error('❌ Failed to clear app data:', error);
      throw error;
    }
  }

  /**
   * Handle network connectivity changes
   */
  async handleNetworkChange(isConnected: boolean): Promise<void> {
    try {
      if (!isConnected) {
        console.log('📡 Network disconnected');
        // Mark that user attempted login while offline
        await AsyncStorage.setItem('@irachat_login_attempt', Date.now().toString());
      } else {
        console.log('📡 Network connected');
        // Remove offline login attempt marker
        await AsyncStorage.removeItem('@irachat_login_attempt');
      }
    } catch (error) {
      console.error('❌ Failed to handle network change:', error);
    }
  }

  /**
   * Check if this is the first app launch
   */
  private async checkFirstLaunch(): Promise<boolean> {
    try {
      const hasLaunchedBefore = await AsyncStorage.getItem('irachat_has_launched');

      if (!hasLaunchedBefore) {
        await AsyncStorage.setItem('irachat_has_launched', 'true');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('❌ Failed to check first launch:', error);
      return false;
    }
  }

  /**
   * Clear temporary data
   */
  private async clearTemporaryData(): Promise<void> {
    try {
      const keysToRemove = [
        'irachat_temp_registration',
        'irachat_login_attempt',
        'irachat_verification_data',
        'irachat_temp_user_data',
      ];
      
      await AsyncStorage.multiRemove(keysToRemove);
      console.log('✅ Temporary data cleared');
    } catch (error) {
      console.error('❌ Failed to clear temporary data:', error);
    }
  }

  /**
   * Clear all app data (for complete reset)
   */
  private async clearAllAppData(): Promise<void> {
    try {
      // Get all keys and remove app-specific ones
      const allKeys = await AsyncStorage.getAllKeys();
      const iraChatKeys = allKeys.filter(key => key.startsWith('@irachat'));
      
      if (iraChatKeys.length > 0) {
        await AsyncStorage.multiRemove(iraChatKeys);
      }
      
      console.log('✅ All app data cleared');
    } catch (error) {
      console.error('❌ Failed to clear all app data:', error);
    }
  }

  /**
   * Setup network monitoring
   */
  setupNetworkMonitoring(): void {
    NetInfo.addEventListener(state => {
      this.handleNetworkChange(state.isConnected || false);
    });
  }

  /**
   * Setup Firebase auth state monitoring - DISABLED to prevent logout loops
   */
  setupAuthStateMonitoring(): void {
    // DISABLED: This was causing logout loops when Firebase auth state changed
    console.log('🔓 Firebase auth state monitoring DISABLED to prevent logout loops');

    // onAuthStateChanged(auth, async (firebaseUser) => {
    //   if (!firebaseUser && this.currentUser) {
    //     // User was signed out from Firebase - handle logout
    //     console.log('🔓 Firebase user signed out - handling logout');
    //     await this.handleUserLogout();
    //   }
    // });
  }

  /**
   * Get current user
   */
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  /**
   * Check if app is initialized
   */
  isAppInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Check if user is logged in
   */
  isUserLoggedIn(): boolean {
    return this.currentUser !== null && authPersistenceService.isLoggedIn();
  }

  /**
   * Force refresh session
   */
  async refreshSession(): Promise<User | null> {
    try {
      console.log('🔄 Force refreshing session...');
      const restoredUser = await authPersistenceService.restoreSession();
      
      if (restoredUser) {
        this.currentUser = restoredUser;
        console.log('✅ Session refreshed successfully');
      } else {
        this.currentUser = null;
        console.log('❌ Session refresh failed');
      }
      
      return restoredUser;
    } catch (error) {
      console.error('❌ Failed to refresh session:', error);
      this.currentUser = null;
      return null;
    }
  }

  /**
   * Handle app version update
   */
  async handleAppVersionUpdate(oldVersion: string, newVersion: string): Promise<void> {
    try {
      console.log(`📱 App updated from ${oldVersion} to ${newVersion}`);
      
      // Check if it's a significant version change
      const oldMajor = parseInt(oldVersion.split('.')[0]);
      const newMajor = parseInt(newVersion.split('.')[0]);
      
      if (newMajor > oldMajor) {
        console.log('🔄 Major version update - clearing session');
        await authPersistenceService.clearSession();
        this.currentUser = null;
      }
    } catch (error) {
      console.error('❌ Failed to handle app version update:', error);
    }
  }
}

export const appInitializationService = AppInitializationService.getInstance();
