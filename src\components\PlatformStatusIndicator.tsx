// Platform Status Indicator Component
import React, { useEffect, useState, useCallback } from "react";
import { View, Text, TouchableOpacity, Platform } from "react-native";
import {
  isAuthReady,
  getCurrentUserSafely,
  getPlatformInfo,
  auth,
  db,
} from "../services/firebaseSimple";
import { isUserAuthenticated } from "../services/authService";
import { networkStateManager } from "../services/networkStateManager";
import { iraChatOfflineEngine } from "../services/iraChatOfflineEngine";
import { statusService } from "../services/statusService";
import { getStoredAuthData } from "../services/authStorageSimple";

interface PlatformStatus {
  platform: string;
  firebase: boolean;
  firebaseConnection: boolean;
  auth: boolean;
  user: boolean;
  currentUser: string | null;
  persistence: string;
  network: boolean;
  networkType: string;
  networkQuality: string;
  offlineEngine: boolean;
  syncStatus: string;
  localDatabase: boolean;
  storageUsed: string;
  pendingSync: number;
  timestamp: string;
}

export const PlatformStatusIndicator: React.FC = () => {
  const [status, setStatus] = useState<PlatformStatus | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  const updateStatus = useCallback(async () => {
    try {
      // Get platform info
      const platformInfo = getPlatformInfo();

      // Check authentication
      const userAuth = await isUserAuthenticated();
      const currentUser = getCurrentUserSafely();
      const storedAuth = await getStoredAuthData();

      // Test Firebase connection
      let firebaseConnection = false;
      try {
        if (auth && db) {
          // Try to access Firebase services - test if they're properly initialized
          const user = auth.currentUser;
          // If we can access currentUser without error, Firebase is connected
          firebaseConnection = true;
          console.log("Firebase connection test passed, user:", user ? "authenticated" : "not authenticated");
        }
      } catch (fbError) {
        console.warn("Firebase connection test failed:", fbError);
        firebaseConnection = false;
      }

      // Get network status
      const networkState = networkStateManager.getState();

      // Get offline engine status
      let offlineEngineStatus = false;
      let syncStatusText = "Unknown";
      let pendingSyncCount = 0;
      let storageUsedText = "Unknown";

      try {
        const engineStats = await iraChatOfflineEngine.getStats();
        offlineEngineStatus = engineStats.isInitialized;
        syncStatusText = engineStats.isOnline ? "Online" : "Offline";
        pendingSyncCount = engineStats.pendingSyncItems || 0;

        // Format storage usage
        const storageBytes = engineStats.totalStorageUsed || 0;
        const storageMB = (storageBytes / (1024 * 1024)).toFixed(1);
        storageUsedText = `${storageMB} MB`;
      } catch (engineError) {
        console.warn("Offline engine status check failed:", engineError);
      }

      // Check local database status
      let localDatabaseStatus = false;
      try {
        // Test if we can access local storage
        await getStoredAuthData();
        localDatabaseStatus = true;
      } catch (dbError) {
        console.warn("Local database test failed:", dbError);
      }

      setStatus({
        platform: Platform.OS,
        firebase: !!auth && !!db,
        firebaseConnection,
        auth: isAuthReady(),
        user: userAuth,
        currentUser: currentUser?.uid || storedAuth?.user?.id || null,
        persistence: platformInfo.persistence,
        network: networkState.isConnected,
        networkType: networkState.connectionType || "unknown",
        networkQuality: networkState.connectionQuality,
        offlineEngine: offlineEngineStatus,
        syncStatus: syncStatusText,
        localDatabase: localDatabaseStatus,
        storageUsed: storageUsedText,
        pendingSync: pendingSyncCount,
        timestamp: new Date().toLocaleTimeString(),
      });
    } catch (error) {
      console.error("Error updating platform status:", error);
      setStatus({
        platform: Platform.OS,
        firebase: false,
        firebaseConnection: false,
        auth: false,
        user: false,
        currentUser: null,
        persistence: "Unknown",
        network: false,
        networkType: "unknown",
        networkQuality: "unknown",
        offlineEngine: false,
        syncStatus: "Error",
        localDatabase: false,
        storageUsed: "Unknown",
        pendingSync: 0,
        timestamp: new Date().toLocaleTimeString(),
      });
    }
  }, []);

  useEffect(() => {
    let mounted = true;
    let interval: NodeJS.Timeout;

    const initializeAndUpdate = async () => {
      try {
        // Initialize network state manager if not already initialized
        if (!networkStateManager.getState().isConnected && networkStateManager.getState().connectionType === 'unknown') {
          await networkStateManager.initialize();
        }

        // Initialize offline engine if not already initialized
        try {
          await iraChatOfflineEngine.initialize();
        } catch (engineError) {
          console.warn("Offline engine initialization failed:", engineError);
        }

        if (mounted) {
          await updateStatus();

          // Update status every 10 seconds
          interval = setInterval(() => {
            if (mounted) {
              updateStatus();
            }
          }, 10000);
        }
      } catch (error) {
        console.error("Failed to initialize status indicator:", error);
        if (mounted) {
          await updateStatus();
        }
      }
    };

    initializeAndUpdate();

    return () => {
      mounted = false;
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [updateStatus]);

  if (!isVisible || !status) {
    return (
      <TouchableOpacity
        onPress={() => setIsVisible(true)}
        className="absolute top-12 right-4 bg-blue-500 rounded-full w-8 h-8 justify-center items-center z-50"
        style={{ elevation: 5 }}
      >
        <Text className="text-white text-xs font-bold">ℹ️</Text>
      </TouchableOpacity>
    );
  }

  const getStatusColor = (isWorking: boolean) =>
    isWorking ? "text-blue-600" : "text-red-600";
  const getStatusIcon = (isWorking: boolean) => (isWorking ? "✅" : "❌");

  return (
    <View className="absolute top-12 right-4 bg-white rounded-lg p-3 shadow-lg z-50 min-w-64 max-w-80">
      <View className="flex-row justify-between items-center mb-2">
        <Text className="font-bold text-gray-800">Platform Status</Text>
        <TouchableOpacity onPress={() => setIsVisible(false)}>
          <Text className="text-gray-500 text-lg">×</Text>
        </TouchableOpacity>
      </View>

      <View className="space-y-1">
        {/* Platform Info */}
        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Platform:</Text>
          <Text className="text-sm font-medium">
            {status.platform.toUpperCase()}
          </Text>
        </View>

        {/* Network Status */}
        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Network:</Text>
          <Text
            className={`text-sm font-medium ${getStatusColor(status.network)}`}
          >
            {getStatusIcon(status.network)} {status.network ? "Online" : "Offline"}
          </Text>
        </View>

        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Connection:</Text>
          <Text className="text-xs font-medium text-gray-700">
            {status.networkType} ({status.networkQuality})
          </Text>
        </View>

        {/* Firebase Status */}
        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Firebase:</Text>
          <Text
            className={`text-sm font-medium ${getStatusColor(status.firebase)}`}
          >
            {getStatusIcon(status.firebase)} {status.firebase ? "Ready" : "Error"}
          </Text>
        </View>

        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">FB Connect:</Text>
          <Text
            className={`text-sm font-medium ${getStatusColor(status.firebaseConnection)}`}
          >
            {getStatusIcon(status.firebaseConnection)} {status.firebaseConnection ? "OK" : "Failed"}
          </Text>
        </View>

        {/* Authentication Status */}
        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Auth:</Text>
          <Text
            className={`text-sm font-medium ${getStatusColor(status.auth)}`}
          >
            {getStatusIcon(status.auth)} {status.auth ? "Ready" : "Error"}
          </Text>
        </View>

        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">User:</Text>
          <Text
            className={`text-sm font-medium ${getStatusColor(status.user)}`}
          >
            {getStatusIcon(status.user)} {status.user ? "Auth" : "Guest"}
          </Text>
        </View>

        {status.currentUser && (
          <View className="flex-row justify-between">
            <Text className="text-sm text-gray-600">User ID:</Text>
            <Text className="text-xs font-medium text-gray-700" numberOfLines={1}>
              {status.currentUser.substring(0, 8)}...
            </Text>
          </View>
        )}

        {/* Storage & Database */}
        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Storage:</Text>
          <Text className="text-sm font-medium text-blue-600">
            {status.persistence}
          </Text>
        </View>

        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Local DB:</Text>
          <Text
            className={`text-sm font-medium ${getStatusColor(status.localDatabase)}`}
          >
            {getStatusIcon(status.localDatabase)} {status.localDatabase ? "OK" : "Error"}
          </Text>
        </View>

        {/* Offline Engine Status */}
        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Offline Engine:</Text>
          <Text
            className={`text-sm font-medium ${getStatusColor(status.offlineEngine)}`}
          >
            {getStatusIcon(status.offlineEngine)} {status.offlineEngine ? "Ready" : "Error"}
          </Text>
        </View>

        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Sync Status:</Text>
          <Text className="text-sm font-medium text-blue-600">
            {status.syncStatus}
          </Text>
        </View>

        {status.pendingSync > 0 && (
          <View className="flex-row justify-between">
            <Text className="text-sm text-gray-600">Pending Sync:</Text>
            <Text className="text-sm font-medium text-orange-600">
              {status.pendingSync} items
            </Text>
          </View>
        )}

        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Storage Used:</Text>
          <Text className="text-sm font-medium text-gray-700">
            {status.storageUsed}
          </Text>
        </View>

        <View className="flex-row justify-between">
          <Text className="text-sm text-gray-600">Updated:</Text>
          <Text className="text-xs text-gray-500">{status.timestamp}</Text>
        </View>
      </View>

      <TouchableOpacity
        onPress={updateStatus}
        className="mt-2 bg-blue-500 rounded px-3 py-1"
      >
        <Text className="text-white text-xs text-center font-medium">
          Refresh Status
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default PlatformStatusIndicator;
