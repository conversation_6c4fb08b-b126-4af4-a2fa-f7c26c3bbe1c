import {
  getDownloadURL,
  ref,
  uploadBytes,
  uploadBytesResumable,
} from "firebase/storage";
import { storage } from "./firebaseSimple";
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  progress: number;
}

export interface MediaUploadResult {
  url: string;
  path: string;
  metadata: {
    size: number;
    contentType: string;
    timeCreated: string;
  };
}

class StorageService {
  private isInitialized = false;
  private offlineQueue: Map<string, any[]> = new Map();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();

      // Set up network state listener for offline sync
      networkStateManager.addListener('storageService', (networkState) => {
        if (networkState.isConnected) {
          this.syncOfflineUploads();
        }
      });

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async syncOfflineUploads(): Promise<void> {
    try {
      // Sync any queued uploads when back online
      for (const [userId, queuedUploads] of this.offlineQueue.entries()) {
        for (const uploadData of queuedUploads) {
          try {
            await this.retryOfflineUpload(uploadData);
          } catch (error) {
            // Keep in queue for retry
            continue;
          }
        }
        // Clear successfully synced uploads
        this.offlineQueue.delete(userId);
      }
    } catch (error) {
      // Sync failed - will retry on next connection
    }
  }

  private async retryOfflineUpload(uploadData: any): Promise<void> {
    // Retry the upload based on the stored data
    const { file, path, onProgress } = uploadData;
    await this.uploadMedia(file, path, onProgress);
  }

  private async storeOfflineUpload(userId: string, uploadData: any): Promise<string> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const offlineId = `offline_${Date.now()}_${userId}`;

      await database.runAsync(`
        INSERT OR REPLACE INTO upload_queue (
          id, userId, data, timestamp, synced
        ) VALUES (?, ?, ?, ?, ?)
      `, [offlineId, userId, JSON.stringify(uploadData), Date.now(), 0]);

      // Also add to memory queue for immediate retry
      if (!this.offlineQueue.has(userId)) {
        this.offlineQueue.set(userId, []);
      }
      this.offlineQueue.get(userId)!.push(uploadData);

      // Return offline URL placeholder
      return `offline://${offlineId}`;
    } catch (error) {
      throw new Error('Failed to store upload offline');
    }
  }
  /**
   * Upload media file to Firebase Storage with progress tracking
   */
  async uploadMedia(
    file: Blob | File,
    path: string,
    onProgress?: (_progress: UploadProgress) => void,
  ): Promise<MediaUploadResult> {
    try {
      if (!storage) {
        throw new Error("Firebase Storage not initialized");
      }

      if (!networkStateManager.isOnline()) {
        // Store offline for later upload
        const userId = path.split('/')[1] || 'unknown'; // Extract userId from path
        const offlineUrl = await this.storeOfflineUpload(userId, { file, path, onProgress });

        return {
          url: offlineUrl,
          path: path,
          metadata: {
            size: file.size || 0,
            contentType: file.type || "application/octet-stream",
            timeCreated: new Date().toISOString(),
          },
        };
      }

      const storageRef = ref(storage, path);

      if (onProgress) {
        // Use resumable upload for progress tracking
        const uploadTask = uploadBytesResumable(storageRef, file);

        return new Promise((resolve, reject) => {
          uploadTask.on(
            "state_changed",
            (snapshot) => {
              const progress = {
                bytesTransferred: snapshot.bytesTransferred,
                totalBytes: snapshot.totalBytes,
                progress:
                  (snapshot.bytesTransferred / snapshot.totalBytes) * 100,
              };
              onProgress(progress);
            },
            (error) => {
              reject(error);
            },
            async () => {
              try {
                const downloadURL = await getDownloadURL(
                  uploadTask.snapshot.ref,
                );
                const metadata = uploadTask.snapshot.metadata;

                resolve({
                  url: downloadURL,
                  path: uploadTask.snapshot.ref.fullPath,
                  metadata: {
                    size: metadata.size || 0,
                    contentType:
                      metadata.contentType || "application/octet-stream",
                    timeCreated:
                      metadata.timeCreated || new Date().toISOString(),
                  },
                });
              } catch (urlError) {
                reject(urlError);
              }
            },
          );
        });
      } else {
        // Simple upload without progress
        const snapshot = await uploadBytes(storageRef, file);
        const downloadURL = await getDownloadURL(snapshot.ref);

        return {
          url: downloadURL,
          path: snapshot.ref.fullPath,
          metadata: {
            size: snapshot.metadata.size || 0,
            contentType:
              snapshot.metadata.contentType || "application/octet-stream",
            timeCreated:
              snapshot.metadata.timeCreated || new Date().toISOString(),
          },
        };
      }
    } catch (error) {
      // If online upload fails, try to store offline
      try {
        const userId = path.split('/')[1] || 'unknown';
        const offlineUrl = await this.storeOfflineUpload(userId, { file, path, onProgress });

        return {
          url: offlineUrl,
          path: path,
          metadata: {
            size: file.size || 0,
            contentType: file.type || "application/octet-stream",
            timeCreated: new Date().toISOString(),
          },
        };
      } catch (offlineError) {
        throw error; // Throw original error if offline storage also fails
      }
    }
  }

  /**
   * Generate unique file path for media uploads
   */
  generateMediaPath(
    userId: string,
    type: "images" | "videos" | "audio" | "documents",
    fileName: string,
  ): string {
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const extension = fileName.split(".").pop() || "";

    return `media/${type}/${userId}/${timestamp}_${randomId}.${extension}`;
  }

  /**
   * Generate unique path for profile pictures
   */
  generateProfilePicturePath(userId: string, fileName: string): string {
    const timestamp = Date.now();
    const extension = fileName.split(".").pop() || "jpg";

    return `profiles/${userId}/avatar_${timestamp}.${extension}`;
  }

  /**
   * Generate unique path for group pictures
   */
  generateGroupPicturePath(groupId: string, fileName: string): string {
    const timestamp = Date.now();
    const extension = fileName.split(".").pop() || "jpg";

    return `groups/${groupId}/avatar_${timestamp}.${extension}`;
  }

  /**
   * Upload profile picture
   */
  async uploadProfilePicture(
    userId: string,
    file: Blob | File,
    onProgress?: (_progress: UploadProgress) => void,
  ): Promise<MediaUploadResult> {
    const fileName = file instanceof File ? file.name : "profile.jpg";
    const path = this.generateProfilePicturePath(userId, fileName);

    return this.uploadMedia(file, path, onProgress);
  }

  /**
   * Upload group picture
   */
  async uploadGroupPicture(
    groupId: string,
    file: Blob | File,
    onProgress?: (_progress: UploadProgress) => void,
  ): Promise<MediaUploadResult> {
    const fileName = file instanceof File ? file.name : "group.jpg";
    const path = this.generateGroupPicturePath(groupId, fileName);

    return this.uploadMedia(file, path, onProgress);
  }

  /**
   * Upload chat media (images, videos, documents)
   */
  async uploadChatMedia(
    userId: string,
    file: Blob | File,
    type: "images" | "videos" | "audio" | "documents",
    onProgress?: (_progress: UploadProgress) => void,
  ): Promise<MediaUploadResult> {
    const fileName =
      file instanceof File
        ? file.name
        : `media.${type === "images" ? "jpg" : type === "videos" ? "mp4" : type === "audio" ? "mp3" : "bin"}`;
    const path = this.generateMediaPath(userId, type, fileName);

    return this.uploadMedia(file, path, onProgress);
  }

  /**
   * Upload update media (for Updates/Stories feature)
   */
  async uploadUpdateMedia(
    userId: string,
    file: Blob | File,
    type: "images" | "videos",
    onProgress?: (_progress: UploadProgress) => void,
  ): Promise<MediaUploadResult> {
    const fileName =
      file instanceof File
        ? file.name
        : `update.${type === "images" ? "jpg" : "mp4"}`;
    const timestamp = Date.now();
    const randomId = Math.random().toString(36).substring(2, 15);
    const extension = fileName.split(".").pop() || "";

    const path = `updates/${userId}/${timestamp}_${randomId}.${extension}`;

    return this.uploadMedia(file, path, onProgress);
  }
}

export const storageService = new StorageService();
export default storageService;
