import {
  doc,
  getDoc,
  updateDoc,
  setDoc,
  serverTimestamp,
  collection,
  query,
  where,
  getDocs,
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';
import { safeTimestampToDate } from '../utils/firebaseSerializers';

export interface UserProfile {
  id: string;
  name: string;
  username?: string;
  phoneNumber?: string; // Made optional since we now support email-only users
  email?: string;
  avatar?: string;
  bio?: string;
  status?: string;
  authMethod?: 'phone' | 'email' | 'both';
  isOnline: boolean;
  lastSeen: Date;
  createdAt: Date;
  updatedAt: Date;
  // Privacy settings
  showLastSeen: boolean;
  showOnlineStatus: boolean;
  allowUnknownMessages: boolean;
  // Verification
  isVerified: boolean;
  verificationBadge?: string;
  // Social features
  followers?: string[];
  following?: string[];
}

export interface UpdateUserProfileData {
  name?: string;
  username?: string;
  avatar?: string;
  bio?: string;
  status?: string;
  showLastSeen?: boolean;
  showOnlineStatus?: boolean;
  allowUnknownMessages?: boolean;
}

class RealUserService {
  private isInitialized = false;
  private offlineQueue: Map<string, any[]> = new Map();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();

      // Set up network state listener for offline sync
      networkStateManager.addListener('realUserService', (networkState) => {
        if (networkState.isConnected) {
          this.syncOfflineData();
        }
      });

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async syncOfflineData(): Promise<void> {
    try {
      // Sync any queued user data when back online
      for (const [userId, queuedData] of this.offlineQueue.entries()) {
        for (const data of queuedData) {
          try {
            await this.syncUserDataToFirebase(userId, data);
          } catch (_error) {
            // Keep in queue for retry
            continue;
          }
        }
        // Clear successfully synced data
        this.offlineQueue.delete(userId);
      }
    } catch (_error) {
      // Sync failed - will retry on next connection
    }
  }

  private async syncUserDataToFirebase(userId: string, data: any): Promise<void> {
    const userRef = doc(db, 'users', userId);
    if (data.type === 'create') {
      await setDoc(userRef, data.payload);
    } else if (data.type === 'update') {
      await updateDoc(userRef, data.payload);
    }
  }

  private async storeOfflineUserData(userId: string, data: any): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO sync_queue (
          id, operation, tableName, recordId, data, priority, status, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        `user_${userId}_${Date.now()}`,
        data.type,
        'users',
        userId,
        JSON.stringify(data.payload),
        1,
        'pending',
        Date.now(),
        Date.now()
      ]);

      // Also add to memory queue for immediate retry
      if (!this.offlineQueue.has(userId)) {
        this.offlineQueue.set(userId, []);
      }
      this.offlineQueue.get(userId)!.push(data);
    } catch (_error) {
      // Offline storage failed
    }
  }

  private async cacheUserOffline(user: UserProfile): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO users (
          localId, id, name, phoneNumber, email, avatar, bio,
          isOnline, lastSeen, createdAt, updatedAt, syncStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        user.id, // localId same as id for cached users
        user.id,
        user.name,
        user.phoneNumber || '',
        user.email || '',
        user.avatar || '',
        user.bio || '',
        user.isOnline ? 1 : 0,
        user.lastSeen.getTime(),
        user.createdAt.getTime(),
        user.updatedAt.getTime(),
        'synced'
      ]);
    } catch (_error) {
      // Cache failed - continue without caching
    }
  }

  private async getOfflineUser(userId: string): Promise<UserProfile | null> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const result = await database.getFirstAsync(`
        SELECT * FROM users WHERE id = ?
      `, [userId]) as any;

      if (!result) return null;

      return {
        id: result.id,
        name: result.name || '',
        username: undefined, // Not stored in offline schema
        phoneNumber: result.phoneNumber || undefined,
        email: result.email || undefined,
        avatar: result.avatar || undefined,
        bio: result.bio || undefined,
        status: undefined, // Not stored in offline schema
        isOnline: result.isOnline === 1,
        lastSeen: new Date(result.lastSeen),
        createdAt: new Date(result.createdAt),
        updatedAt: new Date(result.updatedAt),
        showLastSeen: true, // Default values for missing fields
        showOnlineStatus: true,
        allowUnknownMessages: true,
        isVerified: false,
        verificationBadge: undefined,
      };
    } catch (_error) {
      return null;
    }
  }
  /**
   * Get user profile by ID
   */
  async getUserProfile(userId: string): Promise<{ success: boolean; user?: UserProfile; error?: string }> {
    try {
      // Try offline first if not connected
      if (!networkStateManager.isOnline()) {
        const offlineUser = await this.getOfflineUser(userId);
        if (offlineUser) {
          return { success: true, user: offlineUser };
        }
        return { success: false, error: 'User not found' };
      }

      const userRef = doc(db, 'users', userId);
      const userDoc = await getDoc(userRef);

      if (!userDoc.exists()) {
        // Try offline as fallback
        const offlineUser = await this.getOfflineUser(userId);
        if (offlineUser) {
          return { success: true, user: offlineUser };
        }
        return { success: false, error: 'User not found' };
      }

      const userData = userDoc.data();
      const user: UserProfile = {
        id: userDoc.id,
        ...userData,
        lastSeen: userData.lastSeen?.toDate() || new Date(),
        createdAt: userData.createdAt?.toDate() || new Date(),
        updatedAt: userData.updatedAt?.toDate() || new Date(),
      } as UserProfile;

      // Cache user offline for future use
      await this.cacheUserOffline(user);

      return { success: true, user };
    } catch (error) {
      // Fallback to offline data
      const offlineUser = await this.getOfflineUser(userId);
      if (offlineUser) {
        return { success: true, user: offlineUser };
      }
      return { success: false, error: 'Failed to get user profile' };
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(
    userId: string,
    updates: UpdateUserProfileData
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🔄 updateUserProfile called with:', { userId, updates });

      // If username is being updated, check if it's available (only when online)
      if (updates.username && networkStateManager.isOnline()) {
        console.log('🔍 Checking username availability:', updates.username);
        const isAvailable = await this.isUsernameAvailable(updates.username, userId);
        console.log('✅ Username availability result:', isAvailable);

        if (!isAvailable) {
          console.log('❌ Username not available:', updates.username);
          return { success: false, error: 'Username is already taken' };
        }
      } else if (updates.username && !networkStateManager.isOnline()) {
        console.log('📱 Offline mode: Skipping username validation for now');
      }

      const updateData = {
        ...updates,
        updatedAt: serverTimestamp(),
      };

      if (networkStateManager.isOnline()) {
        const userRef = doc(db, 'users', userId);

        // Check if user exists
        const userDoc = await getDoc(userRef);
        if (!userDoc.exists()) {
          return { success: false, error: 'User not found' };
        }

        // Update user document
        await updateDoc(userRef, updateData);
      } else {
        // Store offline for later sync
        await this.storeOfflineUserData(userId, {
          type: 'update',
          payload: updateData,
        });
      }

      return { success: true };
    } catch (error) {
      // If online update fails, store offline
      try {
        await this.storeOfflineUserData(userId, {
          type: 'update',
          payload: {
            ...updates,
            updatedAt: new Date(),
          },
        });
      } catch (_offlineError) {
        // Both online and offline failed
      }

      return { success: false, error: 'Failed to update profile' };
    }
  }

  /**
   * Check if username is available
   */
  async isUsernameAvailable(username: string, excludeUserId?: string): Promise<boolean> {
    try {
      console.log('🔍 Checking username availability:', { username, excludeUserId });

      if (!networkStateManager.isOnline()) {
        console.log('📱 Offline mode: Assuming username is available');
        // In offline mode, assume username is available
        // This will be validated when back online
        return true;
      }

      const usersRef = collection(db, 'users');
      const q = query(usersRef, where('username', '==', username));
      const querySnapshot = await getDocs(q);

      console.log('📊 Username query results:', {
        empty: querySnapshot.empty,
        size: querySnapshot.size,
        docs: querySnapshot.docs.map(doc => ({ id: doc.id, data: doc.data() }))
      });

      // If no users found with this username, it's available
      if (querySnapshot.empty) {
        console.log('✅ Username is available (no matches found)');
        return true;
      }

      // If excluding a user ID (for updates), check if the only match is the excluded user
      if (excludeUserId) {
        const matches = querySnapshot.docs.filter(doc => doc.id !== excludeUserId);
        console.log('🔍 After excluding user ID:', {
          excludeUserId,
          remainingMatches: matches.length,
          matchIds: matches.map(doc => doc.id)
        });
        return matches.length === 0;
      }

      console.log('❌ Username is not available');
      return false;
    } catch (error) {
      console.error('❌ Error checking username availability:', error);
      return false;
    }
  }

  /**
   * Update user online status
   */
  async updateOnlineStatus(userId: string, isOnline: boolean): Promise<{ success: boolean; error?: string }> {
    const statusData = {
      isOnline,
      lastSeen: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    try {
      if (networkStateManager.isOnline()) {
        const userRef = doc(db, 'users', userId);
        await updateDoc(userRef, statusData);
      } else {
        // Store offline for later sync
        await this.storeOfflineUserData(userId, {
          type: 'update',
          payload: {
            isOnline,
            lastSeen: new Date(),
            updatedAt: new Date(),
          },
        });
      }

      return { success: true };
    } catch (error) {
      // If online update fails, store offline
      try {
        await this.storeOfflineUserData(userId, {
          type: 'update',
          payload: {
            isOnline,
            lastSeen: new Date(),
            updatedAt: new Date(),
          },
        });
      } catch (_offlineError) {
        // Both online and offline failed
      }

      return { success: false, error: 'Failed to update online status' };
    }
  }

  /**
   * Search users by name or username
   */
  async searchUsers(query: string, limit: number = 20): Promise<{ success: boolean; users?: UserProfile[]; error?: string }> {
    try {
      const nameQuery = query.toLowerCase();
      let matchingUsers: UserProfile[] = [];

      if (networkStateManager.isOnline()) {
        const usersRef = collection(db, 'users');
        const usersSnapshot = await getDocs(usersRef);

        usersSnapshot.docs.forEach(doc => {
          const userData = doc.data();
          const user = {
            id: doc.id,
            ...userData,
            lastSeen: userData.lastSeen?.toDate() || new Date(),
            createdAt: userData.createdAt?.toDate() || new Date(),
            updatedAt: userData.updatedAt?.toDate() || new Date(),
          } as UserProfile;

          // Check if name or username matches
          const nameMatch = user.name?.toLowerCase().includes(nameQuery);
          const usernameMatch = user.username?.toLowerCase().includes(nameQuery);

          if (nameMatch || usernameMatch) {
            matchingUsers.push(user);
          }
        });
      } else {
        // Search offline cached users
        matchingUsers = await this.searchOfflineUsers(nameQuery, limit);
      }

      // Sort by relevance (exact matches first, then partial matches)
      matchingUsers.sort((a, b) => {
        const aExactName = a.name?.toLowerCase() === nameQuery;
        const bExactName = b.name?.toLowerCase() === nameQuery;
        const aExactUsername = a.username?.toLowerCase() === nameQuery;
        const bExactUsername = b.username?.toLowerCase() === nameQuery;

        if (aExactName || aExactUsername) return -1;
        if (bExactName || bExactUsername) return 1;
        return 0;
      });

      return {
        success: true,
        users: matchingUsers.slice(0, limit)
      };
    } catch (error) {
      // Fallback to offline search
      try {
        const offlineUsers = await this.searchOfflineUsers(query.toLowerCase(), limit);
        return { success: true, users: offlineUsers };
      } catch (_offlineError) {
        return { success: false, error: 'Failed to search users' };
      }
    }
  }

  private async searchOfflineUsers(nameQuery: string, limit: number): Promise<UserProfile[]> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const result = await database.getAllAsync(`
        SELECT * FROM users
        WHERE LOWER(name) LIKE ?
        LIMIT ?
      `, [`%${nameQuery}%`, limit]);

      return result.map((row: any) => ({
        id: row.id,
        name: row.name || '',
        username: undefined, // Not stored in offline schema
        phoneNumber: row.phoneNumber || undefined,
        email: row.email || undefined,
        avatar: row.avatar || undefined,
        bio: row.bio || undefined,
        status: undefined, // Not stored in offline schema
        isOnline: row.isOnline === 1,
        lastSeen: new Date(row.lastSeen),
        createdAt: new Date(row.createdAt),
        updatedAt: new Date(row.updatedAt),
        showLastSeen: true, // Default values for missing fields
        showOnlineStatus: true,
        allowUnknownMessages: true,
        isVerified: false,
        verificationBadge: undefined,
      }));
    } catch (_error) {
      return [];
    }
  }

  /**
   * Create or update user profile
   */
  async createOrUpdateUser(
    userId: string,
    userData: Partial<UserProfile>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (networkStateManager.isOnline()) {
        const userRef = doc(db, 'users', userId);
        const userDoc = await getDoc(userRef);

        if (userDoc.exists()) {
          // Update existing user
          await updateDoc(userRef, {
            ...userData,
            updatedAt: serverTimestamp(),
          });
        } else {
          // Create new user
          await setDoc(userRef, {
            id: userId,
            isOnline: true,
            showLastSeen: true,
            showOnlineStatus: true,
            allowUnknownMessages: true,
            isVerified: false,
            ...userData,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            lastSeen: serverTimestamp(),
          });
        }
      } else {
        // Store offline for later sync
        const offlineUser = await this.getOfflineUser(userId);

        if (offlineUser) {
          // Update existing offline user
          await this.storeOfflineUserData(userId, {
            type: 'update',
            payload: {
              ...userData,
              updatedAt: new Date(),
            },
          });
        } else {
          // Create new offline user
          await this.storeOfflineUserData(userId, {
            type: 'create',
            payload: {
              id: userId,
              isOnline: true,
              showLastSeen: true,
              showOnlineStatus: true,
              allowUnknownMessages: true,
              isVerified: false,
              ...userData,
              createdAt: new Date(),
              updatedAt: new Date(),
              lastSeen: new Date(),
            },
          });
        }
      }

      return { success: true };
    } catch (error) {
      // If online operation fails, store offline
      try {
        await this.storeOfflineUserData(userId, {
          type: 'create',
          payload: {
            id: userId,
            isOnline: true,
            showLastSeen: true,
            showOnlineStatus: true,
            allowUnknownMessages: true,
            isVerified: false,
            ...userData,
            createdAt: new Date(),
            updatedAt: new Date(),
            lastSeen: new Date(),
          },
        });
      } catch (_offlineError) {
        // Both online and offline failed
      }

      return { success: false, error: 'Failed to create/update user' };
    }
  }

  /**
   * Get multiple users by IDs
   */
  async getUsersByIds(userIds: string[]): Promise<{ success: boolean; users?: UserProfile[]; error?: string }> {
    try {
      const users: UserProfile[] = [];

      if (networkStateManager.isOnline()) {
        // Fetch users in batches to avoid Firestore limitations
        const batchSize = 10;
        for (let i = 0; i < userIds.length; i += batchSize) {
          const batch = userIds.slice(i, i + batchSize);

          const userPromises = batch.map(async (userId) => {
            const userRef = doc(db, 'users', userId);
            const userDoc = await getDoc(userRef);

            if (userDoc.exists()) {
              const userData = userDoc.data();
              const user = {
                id: userDoc.id,
                ...userData,
                lastSeen: safeTimestampToDate(userData.lastSeen),
                createdAt: safeTimestampToDate(userData.createdAt),
                updatedAt: safeTimestampToDate(userData.updatedAt),
              } as UserProfile;

              // Cache user offline
              await this.cacheUserOffline(user);

              return user;
            }
            return null;
          });

          const batchUsers = await Promise.all(userPromises);
          users.push(...batchUsers.filter(user => user !== null) as UserProfile[]);
        }
      } else {
        // Get users from offline cache
        for (const userId of userIds) {
          const offlineUser = await this.getOfflineUser(userId);
          if (offlineUser) {
            users.push(offlineUser);
          }
        }
      }

      return { success: true, users };
    } catch (error) {
      // Fallback to offline data
      try {
        const offlineUsers: UserProfile[] = [];
        for (const userId of userIds) {
          const offlineUser = await this.getOfflineUser(userId);
          if (offlineUser) {
            offlineUsers.push(offlineUser);
          }
        }
        return { success: true, users: offlineUsers };
      } catch (_offlineError) {
        return { success: false, error: 'Failed to get users' };
      }
    }
  }

  /**
   * Get user by ID (simplified version for profile page)
   */
  async getUserById(userId: string): Promise<UserProfile | null> {
    try {
      const result = await this.getUserProfile(userId);
      return result.success ? result.user || null : null;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      return null;
    }
  }

  /**
   * Follow a user
   */
  async followUser(currentUserId: string, targetUserId: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (networkStateManager.isOnline()) {
        // Update current user's following list
        const currentUserRef = doc(db, 'users', currentUserId);
        const currentUserDoc = await getDoc(currentUserRef);

        if (currentUserDoc.exists()) {
          const currentUserData = currentUserDoc.data();
          const following = currentUserData.following || [];

          if (!following.includes(targetUserId)) {
            await updateDoc(currentUserRef, {
              following: [...following, targetUserId],
              updatedAt: serverTimestamp(),
            });
          }
        }

        // Update target user's followers list
        const targetUserRef = doc(db, 'users', targetUserId);
        const targetUserDoc = await getDoc(targetUserRef);

        if (targetUserDoc.exists()) {
          const targetUserData = targetUserDoc.data();
          const followers = targetUserData.followers || [];

          if (!followers.includes(currentUserId)) {
            await updateDoc(targetUserRef, {
              followers: [...followers, currentUserId],
              updatedAt: serverTimestamp(),
            });
          }
        }
      } else {
        // Store offline for later sync
        await this.storeOfflineUserData(currentUserId, {
          type: 'follow',
          payload: { targetUserId, action: 'follow' },
        });
      }

      return { success: true };
    } catch (error) {
      console.error('Error following user:', error);
      return { success: false, error: 'Failed to follow user' };
    }
  }

  /**
   * Unfollow a user
   */
  async unfollowUser(currentUserId: string, targetUserId: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (networkStateManager.isOnline()) {
        // Update current user's following list
        const currentUserRef = doc(db, 'users', currentUserId);
        const currentUserDoc = await getDoc(currentUserRef);

        if (currentUserDoc.exists()) {
          const currentUserData = currentUserDoc.data();
          const following = currentUserData.following || [];

          await updateDoc(currentUserRef, {
            following: following.filter((id: string) => id !== targetUserId),
            updatedAt: serverTimestamp(),
          });
        }

        // Update target user's followers list
        const targetUserRef = doc(db, 'users', targetUserId);
        const targetUserDoc = await getDoc(targetUserRef);

        if (targetUserDoc.exists()) {
          const targetUserData = targetUserDoc.data();
          const followers = targetUserData.followers || [];

          await updateDoc(targetUserRef, {
            followers: followers.filter((id: string) => id !== currentUserId),
            updatedAt: serverTimestamp(),
          });
        }
      } else {
        // Store offline for later sync
        await this.storeOfflineUserData(currentUserId, {
          type: 'follow',
          payload: { targetUserId, action: 'unfollow' },
        });
      }

      return { success: true };
    } catch (error) {
      console.error('Error unfollowing user:', error);
      return { success: false, error: 'Failed to unfollow user' };
    }
  }
}

export const realUserService = new RealUserService();
