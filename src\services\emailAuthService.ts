// 📧 EMAIL AUTHENTICATION SERVICE
// Modern email authentication without Dynamic Links (following Firebase 2025 guidelines)

import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendEmailVerification,
  sendPasswordResetEmail,
  updatePassword,
  updateEmail,
  EmailAuthProvider,
  reauthenticateWithCredential,
  User as FirebaseUser,
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from './firebaseSimple';
import { User } from '../types';

// Use the AuthResult interface from authService for consistency
interface AuthResult {
  success: boolean;
  error?: string;
  user?: User;
  requiresVerification?: boolean;
  verificationId?: string;
}

export class EmailAuthService {
  private static instance: EmailAuthService;

  static getInstance(): EmailAuthService {
    if (!EmailAuthService.instance) {
      EmailAuthService.instance = new EmailAuthService();
    }
    return EmailAuthService.instance;
  }

  /**
   * Register with email and password (no Dynamic Links needed)
   */
  async registerWithEmail(email: string, password: string, userData: Partial<User>): Promise<AuthResult> {
    try {
      console.log('📧 Starting email registration for:', email);

      // Create Firebase user
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Send email verification (modern approach without Dynamic Links)
      const emailSent = await this.sendVerificationEmail(firebaseUser);

      if (!emailSent) {
        console.error('❌ Failed to send verification email, cleaning up user account');
        // Clean up the created user account if email sending fails
        try {
          await firebaseUser.delete();
          console.log('✅ Cleaned up user account after email send failure');
        } catch (deleteError) {
          console.error('❌ Failed to clean up user account:', deleteError);
        }

        return {
          success: false,
          error: 'Failed to send verification email. Please try again or contact support if the problem persists.',
        };
      }

      // Create user document in Firestore
      const user: User = {
        id: firebaseUser.uid,
        email: email,
        // Don't include phoneNumber field for email-only registration
        username: userData.username || email.split('@')[0],
        displayName: userData.displayName || userData.name || email.split('@')[0],
        name: userData.name || email.split('@')[0],
        avatar: userData.avatar || '',
        photoURL: userData.photoURL || '',
        bio: userData.bio || '',
        status: 'online',
        authMethod: 'email',
        emailVerified: false,
        // Add required fields from BaseUser
        followersCount: 0,
        followingCount: 0,
        likesCount: 0,
        isVerified: false,
        isOnline: true,
        // Convert Date to number for Redux serialization
        createdAt: Date.now(),
        lastSeen: Date.now(),
        updatedAt: Date.now(),
      } as User;

      await setDoc(doc(db, 'users', firebaseUser.uid), user);

      // Sign out the user immediately since they need to verify their email first
      await auth.signOut();

      console.log('✅ Email registration successful, user signed out pending verification');
      return {
        success: true,
        user,
        requiresVerification: true,
      };
    } catch (error: any) {
      console.error('❌ Email registration failed:', error);

      // Handle the case where email is already in use
      if (error.code === 'auth/email-already-in-use') {
        console.log('🔄 Email already in use, checking if user is unverified');
        // Try to handle existing unverified email
        return await this.handleExistingUnverifiedEmail(email, password);
      }

      return {
        success: false,
        error: this.getErrorMessage(error),
      };
    }
  }

  /**
   * Sign in with email and password
   */
  async signInWithEmail(email: string, password: string): Promise<AuthResult> {
    try {
      console.log('📧 Starting email sign in for:', email);

      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // 🚨 CRITICAL: Check email verification status
      if (!firebaseUser.emailVerified) {
        console.error('❌ Email not verified for user:', email);

        // Sign out the user since they're not verified
        await auth.signOut();

        return {
          success: false,
          error: 'Please verify your email before signing in. Check your inbox for the verification link.',
          requiresVerification: true,
        };
      }

      // Get user data from Firestore
      const userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
      if (!userDoc.exists()) {
        throw new Error('User data not found');
      }

      const user = userDoc.data() as User;

      // Convert any Firestore Timestamps to numbers for Redux serialization
      const convertTimestamps = (obj: any): any => {
        if (!obj) return obj;

        const converted = { ...obj };
        const timestampFields = ['lastSeen', 'createdAt', 'updatedAt', 'lastLoginTime'];

        timestampFields.forEach(field => {
          if (converted[field]) {
            // Handle Firestore Timestamps
            if (converted[field] && typeof converted[field] === 'object' && 'seconds' in converted[field] && 'nanoseconds' in converted[field]) {
              converted[field] = converted[field].seconds * 1000 + converted[field].nanoseconds / 1000000;
            }
            // Handle Firestore Timestamp objects with toDate method
            else if (converted[field] && typeof converted[field] === 'object' && typeof converted[field].toDate === 'function') {
              converted[field] = converted[field].toDate().getTime();
            }
            // Handle Date objects
            else if (converted[field] instanceof Date) {
              converted[field] = converted[field].getTime();
            }
            // Ensure it's a number
            else if (typeof converted[field] !== 'number') {
              converted[field] = Date.now(); // Fallback to current time
            }
          }
        });

        return converted;
      };

      const serializedUser = convertTimestamps(user);

      // Update last seen and online status with serializable timestamps
      await updateDoc(doc(db, 'users', firebaseUser.uid), {
        lastSeen: Date.now(), // Use number instead of Date object
        isOnline: true,
        emailVerified: true, // Update verification status in Firestore
      });

      console.log('✅ Email sign in successful for verified user');
      return {
        success: true,
        user: {
          ...serializedUser,
          isOnline: true,
          lastSeen: Date.now(),
          emailVerified: true,
        } as User,
      };
    } catch (error: any) {
      console.error('❌ Email sign in failed:', error);
      return {
        success: false,
        error: this.getErrorMessage(error),
      };
    }
  }

  /**
   * Send email verification (modern approach without Dynamic Links)
   */
  async sendVerificationEmail(user?: FirebaseUser): Promise<boolean> {
    try {
      const currentUser = user || auth.currentUser;
      if (!currentUser) {
        throw new Error('No user signed in');
      }

      // Send email verification without custom URL to avoid domain allowlist issues
      await sendEmailVerification(currentUser);

      console.log('✅ Verification email sent to:', currentUser.email);
      console.log('📧 Please check your email inbox (including spam folder) for the verification link');

      return true;
    } catch (error: any) {
      const currentUser = user || auth.currentUser;
      console.error('❌ Failed to send verification email:', error);
      console.error('❌ Error details:', {
        code: error.code,
        message: error.message,
        email: currentUser?.email
      });
      return false;
    }
  }

  /**
   * Resend verification email for unverified users
   */
  async resendVerificationEmail(email: string, password: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log('📧 Attempting to resend verification email for:', email);

      // Sign in the user temporarily to send verification
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      if (firebaseUser.emailVerified) {
        await auth.signOut();
        return {
          success: false,
          message: 'Email is already verified. You can sign in normally.',
        };
      }

      // Send verification email
      await this.sendVerificationEmail(firebaseUser);

      // Sign out the user since they're not verified
      await auth.signOut();

      return {
        success: true,
        message: 'Verification email sent! Please check your inbox and click the verification link.',
      };
    } catch (error: any) {
      console.error('❌ Failed to resend verification email:', error);
      return {
        success: false,
        message: this.getErrorMessage(error),
      };
    }
  }

  /**
   * Send password reset email (modern approach)
   */
  async sendPasswordResetEmail(email: string): Promise<{ success: boolean; message: string }> {
    try {
      // Validate email format first
      if (!this.validateEmail(email)) {
        return {
          success: false,
          message: 'Please enter a valid email address.',
        };
      }

      // For React Native, we don't need to specify a custom URL
      // Firebase will use the default password reset flow
      await sendPasswordResetEmail(auth, email);

      console.log('✅ Password reset email sent to:', email);
      return {
        success: true,
        message: 'Password reset email sent! Please check your inbox (including spam folder) for the reset link.',
      };
    } catch (error: any) {
      console.error('❌ Failed to send password reset email:', error);
      console.error('❌ Error details:', {
        code: error.code,
        message: error.message,
        email: email
      });

      return {
        success: false,
        message: this.getPasswordResetErrorMessage(error),
      };
    }
  }

  /**
   * Get user-friendly error message for password reset
   */
  private getPasswordResetErrorMessage(error: any): string {
    switch (error.code) {
      case 'auth/user-not-found':
        return 'If an account with this email exists, you will receive a password reset link shortly.';
      case 'auth/invalid-email':
        return 'Please enter a valid email address.';
      case 'auth/too-many-requests':
        return 'Too many password reset requests. Please wait a few minutes before trying again.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your internet connection and try again.';
      default:
        return 'Failed to send password reset email. Please try again later.';
    }
  }

  /**
   * Update user password
   */
  async updatePassword(currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const user = auth.currentUser;
      if (!user || !user.email) {
        throw new Error('No user signed in');
      }

      // Re-authenticate user before password change
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);

      // Update password
      await updatePassword(user, newPassword);

      console.log('✅ Password updated successfully');
      return true;
    } catch (error: any) {
      console.error('❌ Failed to update password:', error);
      return false;
    }
  }

  /**
   * Update user email
   */
  async updateEmail(newEmail: string, password: string): Promise<boolean> {
    try {
      const user = auth.currentUser;
      if (!user || !user.email) {
        throw new Error('No user signed in');
      }

      // Re-authenticate user before email change
      const credential = EmailAuthProvider.credential(user.email, password);
      await reauthenticateWithCredential(user, credential);

      // Update email
      await updateEmail(user, newEmail);

      // Update Firestore document
      await updateDoc(doc(db, 'users', user.uid), {
        email: newEmail,
        emailVerified: false, // Reset verification status
      });

      // Send verification to new email
      await this.sendVerificationEmail(user);

      console.log('✅ Email updated successfully');
      return true;
    } catch (error: any) {
      console.error('❌ Failed to update email:', error);
      return false;
    }
  }

  /**
   * Check if email is verified
   */
  async checkEmailVerification(): Promise<boolean> {
    try {
      const user = auth.currentUser;
      if (!user) return false;

      // Reload user to get latest verification status
      await user.reload();

      if (user.emailVerified) {
        // Update Firestore document
        await updateDoc(doc(db, 'users', user.uid), {
          emailVerified: true,
        });
        return true;
      }

      return false;
    } catch (error: any) {
      console.error('❌ Failed to check email verification:', error);
      return false;
    }
  }

  /**
   * Handle registration for existing unverified email
   */
  async handleExistingUnverifiedEmail(email: string, password: string): Promise<AuthResult> {
    try {
      console.log('🔄 Attempting to sign in to check verification status for:', email);

      // Try to sign in to check if user exists and verification status
      const signInResult = await this.signInWithEmail(email, password);

      if (!signInResult.success && signInResult.requiresVerification) {
        // User exists but is unverified, resend verification email
        console.log('📧 User exists but unverified, attempting to resend verification email');

        try {
          // Sign in temporarily to resend verification
          const { signInWithEmailAndPassword } = await import('firebase/auth');
          const userCredential = await signInWithEmailAndPassword(auth, email, password);
          const user = userCredential.user;

          // Resend verification email
          const emailSent = await this.sendVerificationEmail(user);

          // Sign out immediately
          await auth.signOut();

          if (emailSent) {
            return {
              success: true,
              requiresVerification: true,
              error: 'Verification email resent! Please check your inbox (including spam folder) and click the verification link.',
            };
          } else {
            return {
              success: false,
              error: 'Failed to resend verification email. Please try again later.',
            };
          }
        } catch (resendError: any) {
          console.error('❌ Failed to resend verification email:', resendError);
          return {
            success: false,
            error: 'Unable to resend verification email. Please try again later.',
          };
        }
      }

      // If sign in was successful, user is verified
      if (signInResult.success) {
        return signInResult;
      }

      // Other error occurred
      return signInResult;

    } catch (error: any) {
      console.error('❌ Failed to handle existing unverified email:', error);
      return {
        success: false,
        error: 'Unable to process request. Please try again later.',
      };
    }
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate password strength
   */
  validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get user-friendly error message
   */
  private getErrorMessage(error: any): string {
    switch (error.code) {
      case 'auth/email-already-in-use':
        return 'This email is already registered. If you haven\'t verified your email yet, please check your inbox (including spam folder) for the verification link. You can also try signing in to resend the verification email.';
      case 'auth/invalid-email':
        return 'Please enter a valid email address.';
      case 'auth/weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'auth/user-not-found':
        return 'No account found with this email address.';
      case 'auth/wrong-password':
        return 'Incorrect password. Please try again.';
      case 'auth/too-many-requests':
        return 'Too many failed attempts. Please try again later.';
      case 'auth/network-request-failed':
        return 'Network error. Please check your connection and try again.';
      default:
        return error.message || 'An unexpected error occurred. Please try again.';
    }
  }
}

export const emailAuthService = EmailAuthService.getInstance();
