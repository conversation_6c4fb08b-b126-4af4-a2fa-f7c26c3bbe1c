# PowerShell script to verify everything is ready for standalone APK build
Write-Host "Verifying Build Readiness for Standalone APK" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

$allGood = $true

# Check 1: Node.js and npm
Write-Host "`n1. Checking Node.js and npm..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js or npm not found" -ForegroundColor Red
    $allGood = $false
}

# Check 2: EAS CLI
Write-Host "`n2. Checking EAS CLI..." -ForegroundColor Yellow
try {
    $easVersion = eas --version 2>$null
    Write-Host "✅ EAS CLI: $easVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ EAS CLI not found. Install with: npm install -g eas-cli" -ForegroundColor Red
    $allGood = $false
}

# Check 3: EAS Login
Write-Host "`n3. Checking EAS login..." -ForegroundColor Yellow
try {
    $whoami = eas whoami 2>$null
    if ($whoami) {
        Write-Host "✅ Logged in as: $whoami" -ForegroundColor Green
    } else {
        Write-Host "❌ Not logged in. Run: eas login" -ForegroundColor Red
        $allGood = $false
    }
} catch {
    Write-Host "❌ Not logged in. Run: eas login" -ForegroundColor Red
    $allGood = $false
}

# Check 4: Configuration files
Write-Host "`n4. Checking configuration files..." -ForegroundColor Yellow

if (Test-Path "app.config.js") {
    Write-Host "✅ app.config.js found" -ForegroundColor Green
} else {
    Write-Host "❌ app.config.js missing" -ForegroundColor Red
    $allGood = $false
}

if (Test-Path "eas.json") {
    Write-Host "✅ eas.json found" -ForegroundColor Green
    
    # Check if standalone profile exists
    $easContent = Get-Content "eas.json" -Raw | ConvertFrom-Json
    if ($easContent.build.standalone) {
        Write-Host "✅ standalone build profile configured" -ForegroundColor Green
    } else {
        Write-Host "❌ standalone build profile missing in eas.json" -ForegroundColor Red
        $allGood = $false
    }
} else {
    Write-Host "❌ eas.json missing" -ForegroundColor Red
    $allGood = $false
}

if (Test-Path "package.json") {
    Write-Host "✅ package.json found" -ForegroundColor Green
} else {
    Write-Host "❌ package.json missing" -ForegroundColor Red
    $allGood = $false
}

# Check 5: Assets
Write-Host "`n5. Checking app assets..." -ForegroundColor Yellow
if (Test-Path "assets/images/LOGO.png") {
    Write-Host "✅ App icon found" -ForegroundColor Green
} else {
    Write-Host "⚠️  App icon not found at assets/images/LOGO.png" -ForegroundColor Yellow
}

# Check 6: Dependencies
Write-Host "`n6. Checking dependencies..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "✅ node_modules found" -ForegroundColor Green
} else {
    Write-Host "❌ node_modules missing. Run: npm install" -ForegroundColor Red
    $allGood = $false
}

# Check 7: Android configuration
Write-Host "`n7. Checking Android configuration..." -ForegroundColor Yellow
if (Test-Path "android") {
    Write-Host "✅ android folder found" -ForegroundColor Green
} else {
    Write-Host "❌ android folder missing" -ForegroundColor Red
    $allGood = $false
}

# Check 8: Cache setup
Write-Host "`n8. Checking D: drive cache setup..." -ForegroundColor Yellow
$cacheVars = @("GRADLE_USER_HOME", "TEMP", "NPM_CONFIG_CACHE")
$cacheOk = $true

foreach ($var in $cacheVars) {
    $value = [Environment]::GetEnvironmentVariable($var, [EnvironmentVariableTarget]::User)
    if ($value -and $value.StartsWith("D:\")) {
        Write-Host "✅ $var = $value" -ForegroundColor Green
    } else {
        Write-Host "⚠️  $var not set to D: drive" -ForegroundColor Yellow
        $cacheOk = $false
    }
}

if (-not $cacheOk) {
    Write-Host "   Run setup-d-drive-cache.ps1 to fix cache configuration" -ForegroundColor Gray
}

# Final result
Write-Host "`n=============================================" -ForegroundColor Green
if ($allGood) {
    Write-Host "✅ ALL CHECKS PASSED!" -ForegroundColor Green
    Write-Host "You're ready to build the standalone APK!" -ForegroundColor White
    Write-Host "`nRun: .\build-standalone-apk.ps1" -ForegroundColor Cyan
} else {
    Write-Host "❌ SOME CHECKS FAILED!" -ForegroundColor Red
    Write-Host "Please fix the issues above before building." -ForegroundColor Yellow
}
Write-Host "=============================================" -ForegroundColor Green
