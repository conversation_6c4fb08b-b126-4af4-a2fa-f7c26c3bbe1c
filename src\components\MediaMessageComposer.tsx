// 📸 MEDIA MESSAGE COMPOSER - Complete media handling with captions
// Handles photos, videos, audio, documents, voice messages, location, contacts

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  Image,
  TextInput,
  Alert,
  Dimensions,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { Audio, Video, ResizeMode } from 'expo-av';
import { realTimeMessagingService } from '../services/realTimeMessagingService';
import NetInfo from '@react-native-community/netinfo';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface MediaMessageComposerProps {
  isVisible: boolean;
  onClose: () => void;
  chatId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  onMessageSent: () => void;
}

interface MediaItem {
  uri: string;
  type: 'image' | 'video' | 'audio' | 'file' | 'contact';
  name?: string;
  size?: number;
  duration?: number;
  width?: number;
  height?: number;
  mimeType?: string;
  contact?: {
    name: string;
    phoneNumber: string;
    avatar?: string;
  };
}

export const MediaMessageComposer: React.FC<MediaMessageComposerProps> = ({
  isVisible,
  onClose,
  chatId,
  senderId,
  senderName,
  senderAvatar,
  onMessageSent,
}) => {
  const [selectedMedia, setSelectedMedia] = useState<MediaItem | null>(null);
  const [caption, setCaption] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [showMediaOptions, setShowMediaOptions] = useState(true);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [isOnline, setIsOnline] = useState(true);
  const [showVideoSettings, setShowVideoSettings] = useState(false);
  const [videoConfig, setVideoConfig] = useState({
    quality: 1,
    maxDuration: 300,
    resizeMode: ResizeMode.CONTAIN
  });

  const recordingRef = useRef<Audio.Recording | null>(null);
  const recordingTimer = useRef<NodeJS.Timeout | null>(null);

  // Monitor network connectivity
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected ?? false);
    });

    return unsubscribe;
  }, []);

  // Handle camera photo
  const handleCameraPhoto = async () => {
    try {
      console.log('📷 Starting camera photo...');

      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is required to take photos.');
        return;
      }

      console.log('✅ Camera permission granted');

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false, // Disable editing to prevent crashes
        quality: 0.8, // Reduce quality to prevent memory issues
      });

      console.log('📷 Camera result:', { canceled: result.canceled, assetsLength: result.assets?.length });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        console.log('📷 Camera asset:', asset);

        if (!asset.uri) {
          console.error('❌ Invalid camera asset: missing URI');
          Alert.alert('Error', 'Camera capture failed. Please try again.');
          return;
        }

        setSelectedMedia({
          uri: asset.uri,
          type: 'image',
          width: asset.width || 0,
          height: asset.height || 0,
          size: asset.fileSize || 0,
          mimeType: asset.mimeType || 'image/jpeg',
        });
        setShowMediaOptions(false);
        console.log('✅ Camera photo selected successfully');
      }
    } catch (error) {
      console.error('❌ Error taking photo:', error);
      Alert.alert('Camera Error', 'Failed to take photo. Please check camera permissions and try again.');
    }
  };

  // Handle camera video
  const handleCameraVideo = async () => {
    try {
      console.log('🎥 Starting camera video...');

      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is required to record videos.');
        return;
      }

      console.log('✅ Camera permission granted for video');

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['videos'],
        allowsEditing: false, // Disable editing to prevent crashes
        quality: Math.min(videoConfig.quality, 0.5), // Lower quality for videos to prevent crashes
        videoMaxDuration: Math.min(videoConfig.maxDuration, 60), // Limit duration to prevent large files
      });

      console.log('🎥 Video result:', { canceled: result.canceled, assetsLength: result.assets?.length });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        console.log('🎥 Video asset:', asset);

        if (!asset.uri) {
          console.error('❌ Invalid video asset: missing URI');
          Alert.alert('Error', 'Video recording failed. Please try again.');
          return;
        }

        setSelectedMedia({
          uri: asset.uri,
          type: 'video',
          width: asset.width || 0,
          height: asset.height || 0,
          duration: asset.duration || undefined,
          size: asset.fileSize || 0,
          mimeType: asset.mimeType || 'video/mp4',
        });
        setShowMediaOptions(false);
        console.log('✅ Camera video selected successfully');
      }
    } catch (error) {
      console.error('❌ Error recording video:', error);
      Alert.alert('Video Error', 'Failed to record video. Please check camera permissions and storage space.');
    }
  };

  // Handle gallery selection
  const handleGallery = async () => {
    try {
      console.log('🎯 Starting gallery selection...');

      // Request permissions with better error handling
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Photo library permission is required to select media.');
        return;
      }

      console.log('✅ Media library permission granted');

      // Launch image library with safer options
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: false, // Disable editing to prevent crashes
        quality: Math.min(videoConfig.quality, 0.8), // Cap quality to prevent memory issues
        allowsMultipleSelection: false,
        selectionLimit: 1,
      });

      console.log('📸 Gallery result:', { canceled: result.canceled, assetsLength: result.assets?.length });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        console.log('📸 Selected media asset:', {
          uri: asset.uri,
          type: asset.type,
          width: asset.width,
          height: asset.height,
          fileSize: asset.fileSize,
          mimeType: asset.mimeType
        });

        // Validate asset data before setting state
        if (!asset.uri) {
          console.error('❌ Invalid asset: missing URI');
          Alert.alert('Error', 'Selected media is invalid. Please try again.');
          return;
        }

        // Set selected media with safe defaults
        const mediaItem: MediaItem = {
          uri: asset.uri,
          type: asset.type === 'video' ? 'video' : 'image',
          width: asset.width || 0,
          height: asset.height || 0,
          duration: asset.duration || undefined,
          size: asset.fileSize || 0,
          mimeType: asset.mimeType || (asset.type === 'video' ? 'video/mp4' : 'image/jpeg'),
        };

        console.log('🎯 Setting media item:', mediaItem);
        setSelectedMedia(mediaItem);
        setShowMediaOptions(false);
        console.log('✅ Media selected successfully');
      } else {
        console.log('📸 Gallery selection canceled or no assets');
      }
    } catch (error) {
      console.error('❌ Error selecting from gallery:', error);
      Alert.alert(
        'Gallery Error',
        'Failed to select media from gallery. Please check your device storage and try again.',
        [{ text: 'OK', style: 'default' }]
      );
    }
  };

  // Handle document selection
  const handleDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedMedia({
          uri: asset.uri,
          type: 'file',
          name: asset.name,
          size: asset.size,
          mimeType: asset.mimeType,
        });
        setShowMediaOptions(false);
      }
    } catch (error) {
      console.error('❌ Error selecting document:', error);
      Alert.alert('Error', 'Failed to select document');
    }
  };

  // Handle voice recording
  const handleVoiceRecord = async () => {
    try {
      if (isRecording) {
        // Stop recording
        if (recordingRef.current) {
          await recordingRef.current.stopAndUnloadAsync();
          const uri = recordingRef.current.getURI();
          
          if (uri) {
            setSelectedMedia({
              uri,
              type: 'audio',
              duration: recordingDuration,
              mimeType: 'audio/m4a',
            });
            setShowMediaOptions(false);
          }
          
          recordingRef.current = null;
        }
        
        if (recordingTimer.current) {
          clearInterval(recordingTimer.current);
          recordingTimer.current = null;
        }
        
        setIsRecording(false);
        setRecordingDuration(0);
      } else {
        // Start recording
        const { status } = await Audio.requestPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Microphone permission is required to record voice messages.');
          return;
        }

        await Audio.setAudioModeAsync({
          allowsRecordingIOS: true,
          playsInSilentModeIOS: true,
        });

        const recording = new Audio.Recording();
        await recording.prepareToRecordAsync({
          android: {
            extension: '.m4a',
            outputFormat: 2, // MPEG_4
            audioEncoder: 3, // AAC
            sampleRate: 44100,
            numberOfChannels: 2,
            bitRate: 128000,
          },
          ios: {
            extension: '.m4a',
            outputFormat: 'kAudioFormatMPEG4AAC',
            audioQuality: 0x60, // High quality
            sampleRate: 44100,
            numberOfChannels: 2,
            bitRate: 128000,
            linearPCMBitDepth: 16,
            linearPCMIsBigEndian: false,
            linearPCMIsFloat: false,
          },
          web: {
            mimeType: 'audio/webm;codecs=opus',
            bitsPerSecond: 128000,
          },
        });
        await recording.startAsync();
        
        recordingRef.current = recording;
        setIsRecording(true);
        
        // Start timer
        recordingTimer.current = setInterval(() => {
          setRecordingDuration(prev => prev + 1);
        }, 1000) as unknown as NodeJS.Timeout;
      }
    } catch (error) {
      console.error('❌ Error with voice recording:', error);
      Alert.alert('Error', 'Failed to record voice message');
    }
  };



  // Handle contact sharing
  const handleContact = async () => {
    try {
      // Import expo-contacts dynamically
      const Contacts = await import('expo-contacts');

      // Request permission to access contacts
      const { status } = await Contacts.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please allow access to contacts to share them');
        return;
      }

      // Launch contact picker
      const result = await Contacts.presentContactPickerAsync();
      if ((result as any)?.cancelled) {
        return;
      }

      // Get selected contact data
      const contact = (result as any)?.contact;
      if (!contact) {
        Alert.alert('Error', 'No contact selected');
        return;
      }

      // Format contact for sharing
      const phoneNumber = contact.phoneNumbers?.[0]?.number || 'No phone number';
      const contactName = contact.name || 'Unknown Contact';

      setSelectedMedia({
        uri: '', // No URI for contact
        type: 'contact',
        contact: {
          name: contactName,
          phoneNumber: phoneNumber,
          avatar: undefined,
        },
      });
      setShowMediaOptions(false);

      console.log('✅ Contact selected for sharing:', contactName);
    } catch (error) {
      console.error('❌ Error selecting contact:', error);
      Alert.alert('Error', 'Failed to access contacts. Make sure you have granted permission.');
    }
  };

  // Send media message
  const handleSend = async () => {
    if (!selectedMedia) return;

    try {
      setIsSending(true);

      let result: { success: boolean; error?: string } = { success: false };

      if (isOnline) {
        // Use real-time messaging service when online
        try {
          if (selectedMedia.type === 'contact') {
            // Send contact message via real-time service
            result = await realTimeMessagingService.sendMessage(
              chatId,
              senderId,
              senderName,
              caption || `Contact: ${selectedMedia.contact?.name}`,
              'text',
              undefined, // no mediaUrl for contact
              senderAvatar
            );
          } else {
            // Send media message via real-time service
            result = await realTimeMessagingService.sendMediaMessage(
              chatId,
              senderId,
              senderName,
              senderAvatar,
              selectedMedia.uri,
              selectedMedia.type as 'image' | 'video' | 'audio' | 'file',
              caption || `${selectedMedia.type} message from ${senderName}`
            );
          }
        } catch (onlineError) {
          console.warn('Real-time service failed, falling back to offline:', onlineError);
          result = { success: false, error: 'Real-time service unavailable' };
        }
      }

      // Fallback to offline engine if online failed or if offline
      if (!result.success) {
        const { iraChatOfflineEngine } = await import('../services/iraChatOfflineEngine');
        await iraChatOfflineEngine.initialize();

        if (selectedMedia.type === 'contact') {
          // Send contact message offline
          await iraChatOfflineEngine.sendMessage(
            chatId,
            caption || `Contact: ${selectedMedia.contact?.name} (shared by ${senderName})`,
            senderId,
            'text'
          );
          result = { success: true };
        } else {
          // Send media message offline
          await iraChatOfflineEngine.sendMessage(
            chatId,
            caption || `${selectedMedia.type} message from ${senderName}`,
            senderId,
            selectedMedia.type as 'image' | 'video' | 'audio' | 'file',
            {
              mediaUri: selectedMedia.uri,
              type: selectedMedia.type,
              caption,
              senderName,
              senderAvatar
            }
          );
          result = { success: true };
        }
      }

      if (result.success) {
        onMessageSent();
        handleClose();
      } else {
        Alert.alert('Error', result.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('❌ Error sending media message:', error);
      Alert.alert('Error', 'Failed to send message. Please try again.');
    } finally {
      setIsSending(false);
    }
  };

  // Handle close
  const handleClose = () => {
    setSelectedMedia(null);
    setCaption('');
    setShowMediaOptions(true);
    setIsSending(false);
    
    // Stop recording if active
    if (isRecording && recordingRef.current) {
      recordingRef.current.stopAndUnloadAsync();
      recordingRef.current = null;
      setIsRecording(false);
      setRecordingDuration(0);
      
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
    }
    
    onClose();
  };

  // Format recording duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color="#374151" />
          </TouchableOpacity>

          <View style={styles.headerCenter}>
            <Text style={styles.title}>Send Media</Text>
            <View style={styles.senderInfo}>
              {senderAvatar && (
                <Image source={{ uri: senderAvatar }} style={styles.senderAvatar} />
              )}
              <Text style={styles.senderName}>Sending as {senderName}</Text>
              <View style={styles.connectionStatus}>
                <View style={[styles.statusDot, { backgroundColor: isOnline ? '#10B981' : '#EF4444' }]} />
                <Text style={styles.statusText}>{isOnline ? 'Online' : 'Offline'}</Text>
              </View>
            </View>
          </View>

          {selectedMedia && !isSending && (
            <TouchableOpacity onPress={handleSend} style={styles.sendButton}>
              <Ionicons name="send" size={24} color="#667eea" />
            </TouchableOpacity>
          )}
        </View>

        {showMediaOptions ? (
          // Media options
          <ScrollView style={styles.content} contentContainerStyle={styles.optionsContainer}>
            <Text style={styles.sectionTitle}>Choose Media Type</Text>
            
            <View style={styles.optionsGrid}>
              <TouchableOpacity style={styles.optionButton} onPress={handleCameraPhoto}>
                <Ionicons name="camera" size={32} color="#667eea" />
                <Text style={styles.optionText}>Camera</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionButton} onPress={handleCameraVideo}>
                <Ionicons name="videocam" size={32} color="#667eea" />
                <Text style={styles.optionText}>Video</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionButton} onPress={handleGallery}>
                <Ionicons name="images" size={32} color="#667eea" />
                <Text style={styles.optionText}>Gallery</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionButton} onPress={handleDocument}>
                <Ionicons name="document" size={32} color="#667eea" />
                <Text style={styles.optionText}>Document</Text>
              </TouchableOpacity>



              <TouchableOpacity style={styles.optionButton} onPress={handleContact}>
                <Ionicons name="person" size={32} color="#667eea" />
                <Text style={styles.optionText}>Contact</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.optionButton}
                onPress={() => setShowVideoSettings(true)}
              >
                <Ionicons name="settings" size={32} color="#667eea" />
                <Text style={styles.optionText}>Video Settings</Text>
              </TouchableOpacity>
            </View>

            {/* Voice Recording */}
            <View style={styles.voiceSection}>
              <Text style={styles.sectionTitle}>Voice Message</Text>
              <TouchableOpacity
                style={[styles.voiceButton, isRecording && styles.voiceButtonRecording]}
                onPress={handleVoiceRecord}
              >
                <Ionicons
                  name={isRecording ? "stop" : "mic"}
                  size={32}
                  color={isRecording ? "#EF4444" : "#667eea"}
                />
                <Text style={[styles.voiceText, isRecording && styles.voiceTextRecording]}>
                  {isRecording ? `Recording ${formatDuration(recordingDuration)}` : 'Hold to Record'}
                </Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        ) : (
          // Media preview and caption
          <KeyboardAvoidingView
            style={styles.content}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 100 : 0}
          >
            <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
              {selectedMedia && (
                <View style={styles.previewContainer}>
                {/* Media Preview */}
                {selectedMedia.type === 'image' && (
                  <Image source={{ uri: selectedMedia.uri }} style={styles.imagePreview} />
                )}
                
                {selectedMedia.type === 'video' && (
                  <Video
                    source={{ uri: selectedMedia.uri }}
                    style={styles.videoPreview}
                    useNativeControls
                    resizeMode={videoConfig.resizeMode}
                  />
                )}
                
                {selectedMedia.type === 'audio' && (
                  <View style={styles.audioPreview}>
                    <Ionicons name="musical-notes" size={48} color="#667eea" />
                    <Text style={styles.audioText}>Voice Message</Text>
                    {selectedMedia.duration && (
                      <Text style={styles.audioDuration}>
                        {formatDuration(selectedMedia.duration)}
                      </Text>
                    )}
                  </View>
                )}
                
                {selectedMedia.type === 'file' && (
                  <View style={styles.filePreview}>
                    <Ionicons name="document" size={48} color="#667eea" />
                    <Text style={styles.fileName}>{selectedMedia.name}</Text>
                    {selectedMedia.size && (
                      <Text style={styles.fileSize}>
                        {(selectedMedia.size / 1024 / 1024).toFixed(2)} MB
                      </Text>
                    )}
                  </View>
                )}
                

                
                {selectedMedia.type === 'contact' && (
                  <View style={styles.contactPreview}>
                    <Ionicons name="person-circle" size={48} color="#667eea" />
                    <Text style={styles.contactName}>{selectedMedia.contact?.name}</Text>
                    <Text style={styles.contactPhone}>{selectedMedia.contact?.phoneNumber}</Text>
                  </View>
                )}

                {/* Caption Input */}
                <View style={styles.captionContainer}>
                  <TextInput
                    style={styles.captionInput}
                    placeholder="Add a caption..."
                    placeholderTextColor="#9CA3AF"
                    value={caption}
                    onChangeText={setCaption}
                    multiline
                    maxLength={1000}
                  />
                </View>

                {/* Send Button */}
                <TouchableOpacity
                  style={[styles.sendMediaButton, isSending && styles.sendingButton]}
                  onPress={handleSend}
                  disabled={isSending}
                >
                  {isSending ? (
                    <ActivityIndicator size="small" color="#FFFFFF" />
                  ) : (
                    <Ionicons name="send" size={24} color="#FFFFFF" />
                  )}
                  <Text style={styles.sendButtonText}>
                    {isSending ? 'Sending...' : 'Send'}
                  </Text>
                </TouchableOpacity>
              </View>
            )}
            </ScrollView>
          </KeyboardAvoidingView>
        )}
      </View>

      {/* Video Settings Modal */}
      <Modal
        visible={showVideoSettings}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowVideoSettings(false)}
      >
        <View style={styles.settingsContainer}>
          <View style={styles.settingsHeader}>
            <TouchableOpacity onPress={() => setShowVideoSettings(false)}>
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
            <Text style={styles.settingsTitle}>Video Settings</Text>
            <View style={{ width: 24 }} />
          </View>

          <ScrollView style={styles.settingsContent}>
            <View style={styles.settingSection}>
              <Text style={styles.settingLabel}>Video Quality</Text>
              <View style={styles.qualityOptions}>
                {[
                  { label: 'Low (0.3)', value: 0.3 },
                  { label: 'Medium (0.5)', value: 0.5 },
                  { label: 'High (0.8)', value: 0.8 },
                  { label: 'Best (1.0)', value: 1.0 },
                ].map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.qualityOption,
                      videoConfig.quality === option.value && styles.selectedOption
                    ]}
                    onPress={() => setVideoConfig(prev => ({ ...prev, quality: option.value }))}
                  >
                    <Text style={[
                      styles.optionLabel,
                      videoConfig.quality === option.value && styles.selectedOptionText
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.settingSection}>
              <Text style={styles.settingLabel}>Max Duration (seconds)</Text>
              <View style={styles.durationOptions}>
                {[60, 120, 300, 600].map((duration) => (
                  <TouchableOpacity
                    key={duration}
                    style={[
                      styles.durationOption,
                      videoConfig.maxDuration === duration && styles.selectedOption
                    ]}
                    onPress={() => setVideoConfig(prev => ({ ...prev, maxDuration: duration }))}
                  >
                    <Text style={[
                      styles.optionLabel,
                      videoConfig.maxDuration === duration && styles.selectedOptionText
                    ]}>
                      {duration}s
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View style={styles.settingSection}>
              <Text style={styles.settingLabel}>Resize Mode</Text>
              <View style={styles.resizeModeOptions}>
                {[
                  { label: 'Contain', value: ResizeMode.CONTAIN },
                  { label: 'Cover', value: ResizeMode.COVER },
                  { label: 'Stretch', value: ResizeMode.STRETCH },
                ].map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.resizeModeOption,
                      videoConfig.resizeMode === option.value && styles.selectedOption
                    ]}
                    onPress={() => setVideoConfig(prev => ({ ...prev, resizeMode: option.value }))}
                  >
                    <Text style={[
                      styles.optionLabel,
                      videoConfig.resizeMode === option.value && styles.selectedOptionText
                    ]}>
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ScrollView>
        </View>
      </Modal>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 60,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  sendButton: {
    padding: 8,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  senderInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 8,
  },
  senderAvatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
  },
  senderName: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  connectionStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  statusText: {
    fontSize: 10,
    color: '#6B7280',
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  optionsContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 32,
  },
  optionButton: {
    width: (SCREEN_WIDTH - 48) / 3,
    aspectRatio: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  optionText: {
    fontSize: 14,
    color: '#374151',
    marginTop: 8,
    fontWeight: '500',
  },
  voiceSection: {
    marginTop: 16,
  },
  voiceButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  voiceButtonRecording: {
    backgroundColor: '#FEF2F2',
    borderColor: '#EF4444',
  },
  voiceText: {
    fontSize: 16,
    color: '#374151',
    marginTop: 8,
    fontWeight: '500',
  },
  voiceTextRecording: {
    color: '#EF4444',
  },
  previewContainer: {
    flex: 1,
    padding: 16,
  },
  imagePreview: {
    width: '100%',
    height: 300,
    borderRadius: 12,
    resizeMode: 'cover',
    marginBottom: 16,
  },
  videoPreview: {
    width: '100%',
    height: 300,
    borderRadius: 12,
    marginBottom: 16,
  },
  audioPreview: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  audioText: {
    fontSize: 16,
    color: '#374151',
    marginTop: 8,
    fontWeight: '500',
  },
  audioDuration: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  filePreview: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  fileName: {
    fontSize: 16,
    color: '#374151',
    marginTop: 8,
    fontWeight: '500',
    textAlign: 'center',
  },
  fileSize: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },

  contactPreview: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  contactName: {
    fontSize: 16,
    color: '#374151',
    marginTop: 8,
    fontWeight: '500',
  },
  contactPhone: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
  },
  captionContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  captionInput: {
    padding: 16,
    fontSize: 16,
    color: '#374151',
    minHeight: 80,
    textAlignVertical: 'top',
  },
  sendMediaButton: {
    flexDirection: 'row',
    backgroundColor: '#667eea',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendingButton: {
    backgroundColor: '#9CA3AF',
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  // Video Settings Modal Styles
  settingsContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  settingsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 60,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  settingsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  settingsContent: {
    flex: 1,
    padding: 16,
  },
  settingSection: {
    marginBottom: 24,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  qualityOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  qualityOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  durationOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  durationOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  resizeModeOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  resizeModeOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  selectedOption: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  optionLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  selectedOptionText: {
    color: '#FFFFFF',
  },
});

export default MediaMessageComposer;
