import { Ionicons } from "@expo/vector-icons";
import { useSegments } from "expo-router";
import React, { useEffect, useState } from "react";
import { navigationService } from "../services/navigationService";
import {
    Dimensions,
    Platform,
    Text,
    TouchableOpacity,
    View,
} from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import Animated, { runOnJS, useSharedValue, withTiming } from "react-native-reanimated";

const { width: screenWidth } = Dimensions.get("window");

interface TabConfig {
  name: string;
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  route: string;
}

const tabs: TabConfig[] = [
  { name: "chats", title: "Chats", icon: "chatbubbles", route: "/(tabs)" },
  { name: "groups", title: "Groups", icon: "people", route: "/(tabs)/groups" },
  { name: "business", title: "Business", icon: "storefront", route: "/(tabs)/business" },
  { name: "calls", title: "Calls", icon: "call", route: "/(tabs)/calls" },
  { name: "stories",title: "Stories",icon: "radio-button-on",route: "/(tabs)/updates",
  },
];

interface AnimatedTabNavigatorProps {
  children: React.ReactNode;
  currentTab?: string;
}

export default function AnimatedTabNavigator({
  children,
  currentTab: _currentTab,
}: AnimatedTabNavigatorProps) {

  const segments = useSegments();

  // Animation values
  const translateX = useSharedValue(0);
  const tabIndicatorPosition = useSharedValue(0);
  const fadeAnim = useSharedValue(1);

  // State
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Determine current tab from route
  useEffect(() => {
    const currentSegment = segments[segments.length - 1];
    let tabIndex = 0;

    if (currentSegment === "groups") tabIndex = 1;
    else if (currentSegment === "business") tabIndex = 2;
    else if (currentSegment === "calls") tabIndex = 3;
    else if (currentSegment === "updates") tabIndex = 4; // Stories tab


    setActiveTabIndex(tabIndex);

    // No animation - set position directly
    tabIndicatorPosition.value = tabIndex * (screenWidth / tabs.length);
  }, [segments, tabIndicatorPosition]);

  // Swipe gesture handler
  const panGesture = Gesture.Pan()
    .activeOffsetX([-10, 10])
    .failOffsetY([-50, 50])
    .onUpdate((event) => {
      translateX.value = event.translationX;
    })
    .onEnd((event) => {
      const { translationX: translation, velocityX } = event;
      const threshold = screenWidth * 0.25;

      let newTabIndex = activeTabIndex;

      // Determine swipe direction and distance
      if (translation > threshold || velocityX > 500) {
        // Swipe right - go to previous tab
        newTabIndex = Math.max(0, activeTabIndex - 1);
      } else if (translation < -threshold || velocityX < -500) {
        // Swipe left - go to next tab
        newTabIndex = Math.min(tabs.length - 1, activeTabIndex + 1);
      }

      // Reset translation immediately
      translateX.value = 0;

      // Navigate to new tab if changed
      if (newTabIndex !== activeTabIndex) {
        runOnJS(navigateToTab)(newTabIndex);
      }
    });

  const navigateToTab = (tabIndex: number) => {
    if (isTransitioning || tabIndex === activeTabIndex) return;

    setIsTransitioning(true);

    // Fade out animation
    fadeAnim.value = withTiming(0.3, { duration: 150 }, () => {
      // Navigate to new tab using navigation service
      runOnJS(navigationService.navigate)(tabs[tabIndex].route as any);

      // Fade in animation
      fadeAnim.value = withTiming(1, { duration: 200 }, () => {
        runOnJS(setIsTransitioning)(false);
      });
    });
  };

  const renderTabBar = () => (
    <View
      style={{
        flexDirection: "row",
        backgroundColor: "#FFFFFF",
        borderTopWidth: 1,
        borderTopColor: "#E5E7EB",
        paddingBottom: Platform.OS === "ios" ? 20 : 5,
        height: Platform.OS === "ios" ? 85 : 65,
        position: "relative",
      }}
    >
      {/* Animated tab indicator */}
      <Animated.View
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: screenWidth / tabs.length,
          height: 3,
          backgroundColor: "#667eea",
          transform: [{ translateX: tabIndicatorPosition }],
        }}
      />

      {tabs.map((tab, index) => (
        <TouchableOpacity
          key={tab.name}
          style={{
            flex: 1,
            alignItems: "center",
            justifyContent: "center",
            paddingTop: 8,
          }}
          onPress={() => navigateToTab(index)}
          activeOpacity={0.7}
        >
          <Animated.View
            style={{
              alignItems: "center",
              transform: [
                {
                  scale: activeTabIndex === index ? 1.1 : 1,
                },
              ],
            }}
          >
            <Ionicons
              name={tab.icon}
              size={24}
              color={activeTabIndex === index ? "#667eea" : "#8E8E93"}
            />
            <Text
              style={{
                fontSize: 11,
                fontWeight: "600",
                color: activeTabIndex === index ? "#667eea" : "#8E8E93",
                marginTop: 4,
              }}
            >
              {tab.title}
            </Text>
          </Animated.View>
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={{ flex: 1 }}>
      <GestureDetector gesture={panGesture}>
        <Animated.View
          style={{
            flex: 1,
            opacity: fadeAnim,
            transform: [{ translateX }],
          }}
        >
          {children}
        </Animated.View>
      </GestureDetector>

      {renderTabBar()}
    </View>
  );
}
