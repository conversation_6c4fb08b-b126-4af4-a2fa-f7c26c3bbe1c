import React from 'react';
import { View, TextInput, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface ChatListSearchBarProps {
  searchQuery: string;
  onSearchChange: (text: string) => void;
  onClearSearch: () => void;
}

export const ChatListSearchBar: React.FC<ChatListSearchBarProps> = ({
  searchQuery,
  onSearchChange,
  onClearSearch,
}) => {
  return (
    <View style={{ 
      paddingHorizontal: 16, 
      paddingVertical: 12, 
      backgroundColor: 'white', 
      borderBottomWidth: 1, 
      borderBottomColor: '#f3f4f6' 
    }}>
      <View style={{ 
        flexDirection: 'row', 
        alignItems: 'center', 
        backgroundColor: '#f9fafb', 
        borderRadius: 25, 
        paddingHorizontal: 16, 
        paddingVertical: 12 
      }}>
        <Ionicons name="search" size={20} color="#6b7280" />
        <TextInput
          value={searchQuery}
          onChangeText={onSearchChange}
          placeholder="Search chats..."
          style={{
            flex: 1,
            marginLeft: 12,
            fontSize: 16,
            color: '#1f2937'
          }}
          selectionColor="#87CEEB"
          cursorColor="#87CEEB"
          placeholderTextColor="#9ca3af"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={onClearSearch}>
            <Ionicons name="close-circle" size={20} color="#6b7280" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};
