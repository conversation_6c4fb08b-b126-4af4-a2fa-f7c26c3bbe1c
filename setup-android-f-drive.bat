@echo off
echo ========================================
echo Android SDK Environment Setup (F: Drive)
echo ========================================
echo.
echo This script will:
echo - Search for Android SDK on F: drive
echo - Set ANDROID_HOME environment variable
echo - Set ANDROID_SDK_ROOT environment variable  
echo - Update PATH with Android tools
echo.
echo Make sure your Android SDK is installed on F: drive before running this.
echo.
pause

echo Running PowerShell script...
PowerShell -ExecutionPolicy Bypass -File "%~dp0set-android-env-f-drive.ps1"

echo.
echo ========================================
echo Setup completed!
echo ========================================
echo.
echo IMPORTANT: Close this window and open a new terminal
echo to use the updated environment variables.
echo.
pause
