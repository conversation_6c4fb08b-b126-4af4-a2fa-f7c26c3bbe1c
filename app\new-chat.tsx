import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { addDoc, collection, serverTimestamp } from "firebase/firestore";
import { useEffect, useState } from "react";
import {
    ActivityIndicator,
    Alert,
    FlatList,
    KeyboardAvoidingView,
    Platform,
    RefreshControl,
    Text,
    TextInput,
    TouchableOpacity,
    View
} from "react-native";
import ContactItem from "../src/components/ContactItem";
import { Contact, getIraChatContacts } from "../src/services/contactsService";
import { db } from "../src/services/firebaseSimple";

export default function NewChatScreen() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [creating, setCreating] = useState(false);
  const router = useRouter();

  // Load contacts on component mount
  useEffect(() => {
    loadContacts();
  }, []);

  // Filter contacts based on search query with proper null checks
  useEffect(() => {
    // Ensure contacts is a valid array before filtering
    if (!Array.isArray(contacts)) {
      setFilteredContacts([]);
      return;
    }

    if (searchQuery.trim() === "") {
      setFilteredContacts(contacts);
    } else {
      console.log(`🔍 Search input: "${searchQuery}"`);
      console.log(`📊 Total contacts available: ${contacts?.length || 0}`);
      console.log(`📊 Contacts array type: ${typeof contacts}, isArray: ${Array.isArray(contacts)}`);

      // Additional safety check
      if (!contacts || contacts.length === 0) {
        console.warn('⚠️ Contacts array is empty or invalid during search');
        setFilteredContacts([]);
        return;
      }

      const searchTerm = searchQuery.toLowerCase().trim();
      console.log(`🔍 Searching for: "${searchTerm}"`);

      const filtered = contacts.filter((contact, index) => {
        // Add null/undefined checks for all contact properties
        if (!contact) {
          console.warn(`Contact at index ${index} is null/undefined`);
          return false;
        }

        try {
          // Debug the first few contacts to see their structure
          if (index < 3) {
            console.log(`🔍 Debug contact ${index}:`, {
              name: contact.name,
              username: contact.username,
              phoneNumber: contact.phoneNumber,
              hasName: !!contact.name,
              hasPhone: !!contact.phoneNumber
            });
          }

          // Safe string operations with proper null checks
          const nameMatch = contact.name && typeof contact.name === 'string' ?
            contact.name.toLowerCase().includes(searchTerm) : false;
          const usernameMatch = contact.username && typeof contact.username === 'string' ?
            contact.username.toLowerCase().includes(searchTerm) : false;
          const phoneMatch = contact.phoneNumber && typeof contact.phoneNumber === 'string' ?
            contact.phoneNumber.includes(searchQuery) : false;
          const emailMatch = contact.email && typeof contact.email === 'string' ?
            contact.email.toLowerCase().includes(searchTerm) : false;

          const isMatch = nameMatch || usernameMatch || phoneMatch || emailMatch;

          return isMatch;
        } catch (error) {
          console.error(`❌ Error filtering contact at index ${index}:`, contact, error);
          return false;
        }
      });

      console.log(`✅ Search results: ${filtered.length} contacts found`);

      // Debug first few matches after filtering is complete
      if (filtered.length > 0) {
        console.log(`✅ First few matches:`, filtered.slice(0, 3).map(c => c.name));
      }

      setFilteredContacts(filtered);
    }
  }, [searchQuery, contacts]);

  // Auto-expand search when there's a query
  useEffect(() => {
    if (searchQuery && !isSearchExpanded) {
      setIsSearchExpanded(true);
    }
  }, [searchQuery, isSearchExpanded]);

  // Menu functionality removed

  const loadContacts = async () => {
    try {
      setLoading(true);
      console.log("🚀 Loading contacts...");

      const iraChatContacts = await getIraChatContacts();

      // Ensure we have a valid array
      const validContacts = Array.isArray(iraChatContacts) ? iraChatContacts : [];
      console.log(`✅ Loaded ${validContacts.length} contacts`);

      setContacts(validContacts);
      setFilteredContacts(validContacts);
    } catch (error) {
      console.error("❌ Error loading contacts:", error);
      Alert.alert("Error", "Failed to load contacts. Please try again.");
      // Set empty arrays on error to prevent undefined issues
      setContacts([]);
      setFilteredContacts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadContacts();
    setRefreshing(false);
  };

  const handleContactPress = async (contact: Contact) => {
    if (contact.isIraChatUser) {
      // Contact is on IraChat - create chat and navigate to chat room
      await createChatWithContact(contact);
    } else {
      // Contact is not on IraChat - invite them via link
      await inviteContactToIraChat(contact);
    }
  };

  const createChatWithContact = async (contact: Contact) => {
    setCreating(true);
    try {
      // Create a new chat with the selected contact
      const chatDoc = await addDoc(collection(db, "chats"), {
        name: contact.name,
        isGroup: false,
        participants: [contact.phoneNumber || contact.email || contact.id], // Add contact identifier
        lastMessage: "",
        lastMessageAt: serverTimestamp(),
        timestamp: serverTimestamp(),
        contactInfo: {
          id: contact.id,
          name: contact.name,
          phoneNumber: contact.phoneNumber,
          avatar: contact.avatar || "", // Ensure avatar is never undefined
          username: contact.username || "",
          status: contact.status || "I Love IraChat",
          bio: contact.bio || "I Love IraChat",
        },
      });

      console.log("✅ Chat created successfully:", chatDoc.id);

      // Navigate to the new chat with contact info
      const params = new URLSearchParams({
        name: contact.name,
        contactId: contact.id,
        phoneNumber: contact.phoneNumber || "",
        avatar: contact.avatar || "",
      });
      router.replace(`/chat/${chatDoc.id}?${params.toString()}`);
    } catch (error) {
      console.error("❌ Error creating chat:", error);
      Alert.alert("Error", "Failed to start conversation. Please try again.");
    } finally {
      setCreating(false);
    }
  };

  const inviteContactToIraChat = async (contact: Contact) => {
    try {
      // Create invite link for IraChat
      const inviteLink = `https://irachat.app/invite?ref=${encodeURIComponent(contact.phoneNumber || contact.email || contact.id)}`;
      const inviteMessage = `Hey ${contact.name}! 👋\n\nI'd love to chat with you on IraChat - a secure and fun messaging app!\n\nJoin me here: ${inviteLink}\n\nSee you there! 🚀`;

      // Show options to invite the contact
      Alert.alert(
        "Invite to IraChat",
        `${contact.name} is not on IraChat yet. Would you like to invite them?`,
        [
          {
            text: "Cancel",
            style: "cancel"
          },
          {
            text: "Send Invite",
            onPress: async () => {
              try {
                // Use React Native Share API
                const { Share } = await import('react-native');

                if (Platform.OS === 'ios') {
                  // iOS sharing with URL
                  await Share.share({
                    message: inviteMessage,
                    url: inviteLink,
                  });
                } else {
                  // Android sharing
                  await Share.share({
                    message: inviteMessage,
                  });
                }
                console.log("✅ Invite sent successfully");
              } catch (error) {
                console.error("❌ Error sending invite:", error);
                Alert.alert("Error", "Failed to send invite. Please try again.");
              }
            }
          }
        ]
      );
    } catch (error) {
      console.error("❌ Error creating invite:", error);
      Alert.alert("Error", "Failed to create invite. Please try again.");
    }
  };

  const renderContactItem = ({ item }: { item: Contact }) => {
    // Add safety check for item
    if (!item || !item.id) {
      console.warn('Invalid contact item:', item);
      return null;
    }

    return <ContactItem contact={item} onPress={handleContactPress} />;
  };

  const renderEmptyState = () => (
    <View style={{
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 30,
      paddingVertical: 60,
    }}>
      {/* Remove the white container - integrate directly */}
        <View style={{
          width: 80,
          height: 80,
          borderRadius: 40,
          backgroundColor: 'rgba(102, 126, 234, 0.1)',
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: 24,
          borderWidth: 3,
          borderColor: 'rgba(102, 126, 234, 0.2)',
        }}>
          <Ionicons
            name={searchQuery ? "search" : "people-outline"}
            size={40}
            color="#667eea"
          />
        </View>

        <Text style={{
          fontSize: 20,
          fontWeight: '600',
          color: '#FFFFFF',
          marginBottom: 12,
          textAlign: 'center',
        }}>
          {searchQuery ? "No contacts found" : "No IraChat contacts yet"}
        </Text>

        <Text style={{
          fontSize: 16,
          color: 'rgba(255, 255, 255, 0.7)',
          textAlign: 'center',
          lineHeight: 24,
          marginBottom: 24,
        }}>
          {searchQuery
            ? "Try searching with a different name or phone number"
            : "Your friends who use IraChat will appear here when they join"}
        </Text>

        {/* Invite Friends button moved to header menu */}
    </View>
  );



  const insets = { top: 50, bottom: 0, left: 0, right: 0 }; // Fallback safe area

  return (
    <View style={{
      flex: 1,
      backgroundColor: '#000000',
      // Ensure no white background shows during keyboard transitions
      ...(Platform.OS === 'android' && {
        windowSoftInputMode: 'adjustResize',
      })
    }}>
      <KeyboardAvoidingView
        style={{
          flex: 1,
          backgroundColor: '#000000',
          // Prevent any white flash during keyboard animation
          overflow: 'hidden',
        }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={0}
      >
      {/* Header with Safe Area and Gradient - Reduced Height */}
      <View
        style={{
          backgroundColor: '#667eea',
          paddingTop: insets.top + 5,
          paddingBottom: 4, // Further reduced from 8 to 4
          paddingHorizontal: 20,
          shadowColor: '#667eea',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
          elevation: 8,
        }}
      >
        {/* Header Row with Title, Search, and Menu - Properly Centered */}
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: 48, // Increased from 44 to 48 for better centering
          paddingVertical: 4, // Add padding for proper centering
        }}>
          {/* Left Section - Back Button and Title */}
          <View style={{ flexDirection: 'row', alignItems: 'center', flex: 1 }}>
            <TouchableOpacity
              onPress={() => router.back()}
              style={{
                marginRight: 16,
                padding: 6, // Reduced padding for better centering
                alignItems: 'center',
                justifyContent: 'center',
                height: 36, // Fixed height for consistent centering
                width: 36, // Fixed width for consistent centering
              }}
              accessible={true}
              accessibilityRole="button"
              accessibilityLabel="Go back"
            >
              <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
            </TouchableOpacity>

            {/* Title - Hide when search is expanded */}
            {!isSearchExpanded && (
              <Text style={{
                fontSize: 20,
                fontWeight: 'bold',
                color: '#FFFFFF',
                flex: 1,
                textAlignVertical: 'center',
                lineHeight: 24, // Add line height for better vertical centering
              }}>
                Select Contact
              </Text>
            )}

            {/* Expanded Search Bar - Increased height for better visibility */}
            {isSearchExpanded && (
              <View style={{
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.15)',
                borderRadius: 20,
                paddingHorizontal: 10,
                paddingVertical: 4, // Increased from 2 to 4
                borderWidth: 0.5,
                borderColor: 'rgba(255, 255, 255, 0.3)',
                marginRight: 8,
                height: 40, // Increased from 36 to 40 for better input visibility
              }}>
                <Ionicons name="search" size={16} color="rgba(255, 255, 255, 0.8)" />
                <TextInput
                  style={{
                    flex: 1,
                    marginLeft: 10,
                    marginRight: 6,
                    fontSize: 15,
                    color: '#FFFFFF',
                    fontWeight: '500',
                    height: 32, // Proper height for the increased container
                    paddingVertical: 0,
                    textAlignVertical: 'center',
                  }}
                  placeholder="Search contacts..."
                  placeholderTextColor="rgba(255, 255, 255, 0.6)"
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoFocus={true}
                />
                <TouchableOpacity
                  onPress={() => {
                    setSearchQuery('');
                    setIsSearchExpanded(false);
                  }}
                  style={{
                    padding: 4,
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Ionicons name="close" size={16} color="rgba(255, 255, 255, 0.8)" />
                </TouchableOpacity>
              </View>
            )}
          </View>

          {/* Right Section - Search Icon and Menu */}
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8 }}>
            {!isSearchExpanded && (
              <TouchableOpacity
                onPress={() => setIsSearchExpanded(true)}
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.2)',
                  borderRadius: 18,
                  padding: 8, // Reduced padding for better proportions
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: 36, // Fixed height for consistent centering
                  width: 36, // Fixed width for consistent centering
                }}
              >
                <Ionicons name="search" size={20} color="#FFFFFF" />
              </TouchableOpacity>
            )}

            {/* Menu button removed */}
          </View>
        </View>

        {/* Dropdown menu completely removed */}
      </View>

      {/* Menu overlay removed as requested */}

      {/* Contacts List - Add safety check for data readiness */}
      {loading || !Array.isArray(filteredContacts) ? (
        <View style={{
          flex: 1,
          alignItems: 'center',
          justifyContent: 'center',
          paddingHorizontal: 20,
          backgroundColor: '#000000',
        }}>
          <View style={{
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderRadius: 20,
            padding: 30,
            alignItems: 'center',
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 0.2)',
          }}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={{
              fontSize: 16,
              color: '#FFFFFF',
              marginTop: 16,
              textAlign: 'center',
              fontWeight: '500',
            }}>
              Finding your contacts...
            </Text>
          </View>
        </View>
      ) : (
        <FlatList
          data={Array.isArray(filteredContacts) ? filteredContacts : []}
          renderItem={renderContactItem}
          keyExtractor={(item, index) => {
            // Provide fallback key if item.id is missing
            return item?.id || `contact-${index}`;
          }}
          ListEmptyComponent={renderEmptyState}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              colors={["#667eea"]}
              tintColor="#667eea"
            />
          }
          showsVerticalScrollIndicator={false}
          // Performance optimizations for fast scrolling
          removeClippedSubviews={true}
          maxToRenderPerBatch={10}
          updateCellsBatchingPeriod={50}
          initialNumToRender={10}
          windowSize={5}
          // Error handling for rendering issues
          onScrollToIndexFailed={(info) => {
            console.warn('Scroll to index failed:', info);
          }}
          style={{
            backgroundColor: '#000000',
            flex: 1, // Ensure it takes full space
          }}
          contentContainerStyle={{
            paddingHorizontal: 20,
            paddingTop: 20,
            paddingBottom: 20, // Add some padding to prevent content cutoff
            flexGrow: 1, // Ensure content fills available space
            backgroundColor: '#000000', // Explicit background for content
          }}
        />
      )}

      {/* Create Group Chat moved to header menu */}

      {/* Loading overlay for chat creation */}
      {creating && (
        <View style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
        }}>
          <View style={{
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            borderRadius: 20,
            padding: 30,
            alignItems: 'center',
            borderWidth: 1,
            borderColor: 'rgba(255, 255, 255, 0.2)',
            minWidth: 200,
          }}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={{
              fontSize: 16,
              color: '#FFFFFF',
              marginTop: 16,
              fontWeight: '600',
              textAlign: 'center',
            }}>
              Starting conversation...
            </Text>
            <Text style={{
              fontSize: 14,
              color: 'rgba(255, 255, 255, 0.7)',
              marginTop: 4,
              textAlign: 'center',
            }}>
              Please wait a moment
            </Text>
          </View>
        </View>
      )}
      </KeyboardAvoidingView>
    </View>
  );
}
