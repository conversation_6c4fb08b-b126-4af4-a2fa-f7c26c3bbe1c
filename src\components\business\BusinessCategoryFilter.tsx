import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ProductCategory } from '../../types/Business';
import { useTheme } from '../../contexts/ThemeContext';

const CATEGORY_CONFIG = {
  all: { icon: 'grid-outline', label: 'All' },

  // Electronics & Technology
  electronics: { icon: 'phone-portrait-outline', label: 'Electronics' },
  computers: { icon: 'laptop-outline', label: 'Computers' },
  smartphones: { icon: 'phone-portrait-outline', label: 'Smartphones' },
  tablets: { icon: 'tablet-portrait-outline', label: 'Tablets' },
  gaming: { icon: 'game-controller-outline', label: 'Gaming' },
  audio_video: { icon: 'headset-outline', label: 'Audio & Video' },
  cameras: { icon: 'camera-outline', label: 'Cameras' },
  smart_home: { icon: 'home-outline', label: 'Smart Home' },
  wearables: { icon: 'watch-outline', label: 'Wearables' },
  accessories_tech: { icon: 'hardware-chip-outline', label: 'Tech Accessories' },

  // Fashion & Apparel
  fashion: { icon: 'shirt-outline', label: 'Fashion' },
  mens_clothing: { icon: 'man-outline', label: 'Men\'s Clothing' },
  womens_clothing: { icon: 'woman-outline', label: 'Women\'s Clothing' },
  kids_clothing: { icon: 'people-outline', label: 'Kids Clothing' },
  shoes: { icon: 'walk-outline', label: 'Shoes' },
  bags_luggage: { icon: 'bag-outline', label: 'Bags & Luggage' },
  jewelry: { icon: 'star-outline', label: 'Jewelry' },
  watches: { icon: 'time-outline', label: 'Watches' },
  sunglasses: { icon: 'sunny-outline', label: 'Sunglasses' },
  accessories_fashion: { icon: 'star-outline', label: 'Fashion Accessories' },

  // Home & Living
  home_garden: { icon: 'home-outline', label: 'Home & Garden' },
  furniture: { icon: 'bed-outline', label: 'Furniture' },
  home_decor: { icon: 'color-palette-outline', label: 'Home Decor' },
  kitchen_dining: { icon: 'restaurant-outline', label: 'Kitchen & Dining' },
  bedding_bath: { icon: 'water-outline', label: 'Bedding & Bath' },
  lighting: { icon: 'bulb-outline', label: 'Lighting' },
  storage_organization: { icon: 'archive-outline', label: 'Storage' },
  garden_outdoor: { icon: 'leaf-outline', label: 'Garden & Outdoor' },
  tools_hardware: { icon: 'hammer-outline', label: 'Tools & Hardware' },
  appliances: { icon: 'tv-outline', label: 'Appliances' },
  cleaning_supplies: { icon: 'water-outline', label: 'Cleaning Supplies' },

  // Health & Beauty
  beauty_health: { icon: 'medical-outline', label: 'Beauty & Health' },
  skincare: { icon: 'heart-outline', label: 'Skincare' },
  makeup: { icon: 'color-palette-outline', label: 'Makeup' },
  hair_care: { icon: 'cut-outline', label: 'Hair Care' },
  fragrances: { icon: 'rose-outline', label: 'Fragrances' },
  health_supplements: { icon: 'fitness-outline', label: 'Health Supplements' },
  medical_equipment: { icon: 'medical-outline', label: 'Medical Equipment' },
  fitness_equipment: { icon: 'barbell-outline', label: 'Fitness Equipment' },
  wellness: { icon: 'leaf-outline', label: 'Wellness' },

  // Food & Beverages
  food_drinks: { icon: 'restaurant-outline', label: 'Food & Drinks' },
  fresh_food: { icon: 'nutrition-outline', label: 'Fresh Food' },
  packaged_food: { icon: 'fast-food-outline', label: 'Packaged Food' },
  beverages: { icon: 'wine-outline', label: 'Beverages' },
  alcohol: { icon: 'wine-outline', label: 'Alcohol' },
  organic_food: { icon: 'leaf-outline', label: 'Organic Food' },
  specialty_food: { icon: 'star-outline', label: 'Specialty Food' },
  baby_food: { icon: 'heart-outline', label: 'Baby Food' },

  // Automotive & Transportation
  automotive: { icon: 'car-outline', label: 'Automotive' },
  cars: { icon: 'car-outline', label: 'Cars' },
  motorcycles: { icon: 'bicycle-outline', label: 'Motorcycles' },
  bicycles: { icon: 'bicycle-outline', label: 'Bicycles' },
  car_parts: { icon: 'settings-outline', label: 'Car Parts' },
  car_accessories: { icon: 'car-sport-outline', label: 'Car Accessories' },
  tires: { icon: 'ellipse-outline', label: 'Tires' },
  automotive_services: { icon: 'build-outline', label: 'Auto Services' },

  // Sports & Recreation
  sports_outdoors: { icon: 'football-outline', label: 'Sports & Outdoors' },
  fitness: { icon: 'fitness-outline', label: 'Fitness' },
  outdoor_gear: { icon: 'trail-sign-outline', label: 'Outdoor Gear' },
  sports_equipment: { icon: 'basketball-outline', label: 'Sports Equipment' },
  water_sports: { icon: 'boat-outline', label: 'Water Sports' },
  winter_sports: { icon: 'snow-outline', label: 'Winter Sports' },
  team_sports: { icon: 'people-outline', label: 'Team Sports' },
  individual_sports: { icon: 'person-outline', label: 'Individual Sports' },

  // Entertainment & Media
  books_media: { icon: 'book-outline', label: 'Books & Media' },
  books: { icon: 'library-outline', label: 'Books' },
  movies_tv: { icon: 'film-outline', label: 'Movies & TV' },
  music: { icon: 'musical-notes-outline', label: 'Music' },
  video_games: { icon: 'game-controller-outline', label: 'Video Games' },
  magazines: { icon: 'newspaper-outline', label: 'Magazines' },
  digital_media: { icon: 'cloud-outline', label: 'Digital Media' },
  collectibles: { icon: 'trophy-outline', label: 'Collectibles' },

  // Kids & Baby
  toys_games: { icon: 'game-controller-outline', label: 'Toys & Games' },
  baby_products: { icon: 'happy-outline', label: 'Baby Products' },
  kids_toys: { icon: 'gift-outline', label: 'Kids Toys' },
  educational_toys: { icon: 'school-outline', label: 'Educational Toys' },
  baby_gear: { icon: 'car-outline', label: 'Baby Gear' },
  maternity: { icon: 'heart-outline', label: 'Maternity' },
  kids_furniture: { icon: 'bed-outline', label: 'Kids Furniture' },

  // Professional Services
  services: { icon: 'construct-outline', label: 'Services' },
  business_services: { icon: 'briefcase-outline', label: 'Business Services' },
  professional_services: { icon: 'business-outline', label: 'Professional Services' },
  personal_services: { icon: 'person-outline', label: 'Personal Services' },
  home_services: { icon: 'home-outline', label: 'Home Services' },
  repair_maintenance: { icon: 'build-outline', label: 'Repair & Maintenance' },
  cleaning_services: { icon: 'brush-outline', label: 'Cleaning Services' },
  security_services: { icon: 'shield-outline', label: 'Security Services' },

  // Education & Learning
  education: { icon: 'school-outline', label: 'Education' },
  books_educational: { icon: 'library-outline', label: 'Educational Books' },
  courses: { icon: 'document-text-outline', label: 'Courses' },
  tutoring: { icon: 'person-outline', label: 'Tutoring' },
  educational_materials: { icon: 'clipboard-outline', label: 'Educational Materials' },
  school_supplies: { icon: 'pencil-outline', label: 'School Supplies' },
  training_programs: { icon: 'trophy-outline', label: 'Training Programs' },

  // Real Estate & Property
  real_estate: { icon: 'business-outline', label: 'Real Estate' },
  residential: { icon: 'home-outline', label: 'Residential' },
  commercial: { icon: 'storefront-outline', label: 'Commercial' },
  land: { icon: 'map-outline', label: 'Land' },
  rental_properties: { icon: 'key-outline', label: 'Rental Properties' },
  property_services: { icon: 'hammer-outline', label: 'Property Services' },
  construction_materials: { icon: 'cube-outline', label: 'Construction Materials' },

  // Agriculture & Farming
  agriculture: { icon: 'leaf-outline', label: 'Agriculture' },
  crops: { icon: 'leaf-outline', label: 'Crops' },
  livestock: { icon: 'leaf-outline', label: 'Livestock' },
  farming_equipment: { icon: 'build-outline', label: 'Farming Equipment' },
  seeds_plants: { icon: 'leaf-outline', label: 'Seeds & Plants' },
  fertilizers: { icon: 'flask-outline', label: 'Fertilizers' },
  agricultural_services: { icon: 'construct-outline', label: 'Agricultural Services' },

  // Business & Industrial
  business_industrial: { icon: 'business-outline', label: 'Business & Industrial' },
  office_supplies: { icon: 'clipboard-outline', label: 'Office Supplies' },
  industrial_equipment: { icon: 'settings-outline', label: 'Industrial Equipment' },
  raw_materials: { icon: 'cube-outline', label: 'Raw Materials' },
  packaging: { icon: 'archive-outline', label: 'Packaging' },
  safety_equipment: { icon: 'shield-outline', label: 'Safety Equipment' },

  // Arts & Crafts
  arts_crafts: { icon: 'color-palette-outline', label: 'Arts & Crafts' },
  art_supplies: { icon: 'brush-outline', label: 'Art Supplies' },
  craft_materials: { icon: 'cut-outline', label: 'Craft Materials' },
  handmade: { icon: 'hand-left-outline', label: 'Handmade' },
  vintage: { icon: 'time-outline', label: 'Vintage' },
  antiques: { icon: 'hourglass-outline', label: 'Antiques' },
  musical_instruments: { icon: 'musical-notes-outline', label: 'Musical Instruments' },

  // Travel & Tourism
  travel_tourism: { icon: 'airplane-outline', label: 'Travel & Tourism' },
  luggage: { icon: 'bag-outline', label: 'Luggage' },
  travel_accessories: { icon: 'compass-outline', label: 'Travel Accessories' },
  maps_guides: { icon: 'map-outline', label: 'Maps & Guides' },
  travel_services: { icon: 'globe-outline', label: 'Travel Services' },
  accommodation: { icon: 'bed-outline', label: 'Accommodation' },

  // Pets & Animals
  pets: { icon: 'heart-outline', label: 'Pets' },
  pet_supplies: { icon: 'bag-outline', label: 'Pet Supplies' },
  pet_food: { icon: 'nutrition-outline', label: 'Pet Food' },
  pet_accessories: { icon: 'heart-outline', label: 'Pet Accessories' },
  pet_services: { icon: 'medical-outline', label: 'Pet Services' },
  livestock_supplies: { icon: 'leaf-outline', label: 'Livestock Supplies' },

  // Other
  other: { icon: 'ellipsis-horizontal-outline', label: 'Other' },
};

interface BusinessCategoryFilterProps {
  selectedCategory: ProductCategory | 'all';
  onCategoryChange: (category: ProductCategory | 'all') => void;
  postCounts?: Record<string, number>;
}

export const BusinessCategoryFilter: React.FC<BusinessCategoryFilterProps> = ({
  selectedCategory,
  onCategoryChange,
  postCounts = {},
}) => {
  const { colors: COLORS } = useTheme();
  const categories = Object.keys(CATEGORY_CONFIG) as (ProductCategory | 'all')[];

  const styles = StyleSheet.create({
    container: {
      backgroundColor: COLORS.background,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
    },
    scrollContent: {
      paddingHorizontal: 16,
      paddingVertical: 12,
      gap: 8,
    },
    categoryButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      marginRight: 12,
      borderRadius: 24,
      backgroundColor: COLORS.background,
      borderWidth: 1,
      borderColor: COLORS.border,
      minHeight: 48,
    },
    categoryButtonActive: {
      backgroundColor: COLORS.primary,
      borderColor: COLORS.primary,
    },
    categoryText: {
      marginLeft: 8,
      fontSize: 14,
      fontWeight: '500',
      color: COLORS.textSecondary,
    },
    categoryTextActive: {
      color: 'white',
    },
    countBadge: {
      marginLeft: 8,
      backgroundColor: COLORS.border,
      paddingHorizontal: 6,
      paddingVertical: 2,
      borderRadius: 10,
      minWidth: 20,
      alignItems: 'center',
    },
    countBadgeActive: {
      backgroundColor: 'rgba(255, 255, 255, 0.3)',
    },
    countText: {
      fontSize: 10,
      fontWeight: '600',
      color: COLORS.textSecondary,
    },
    countTextActive: {
      color: 'white',
    },
  });

  const renderCategoryButton = (category: ProductCategory | 'all') => {
    const config = CATEGORY_CONFIG[category];
    const isSelected = selectedCategory === category;
    const count = postCounts[category] || 0;

    return (
      <TouchableOpacity
        key={category}
        style={[
          styles.categoryButton,
          isSelected && styles.categoryButtonActive,
        ]}
        onPress={() => onCategoryChange(category)}
      >
        <Ionicons
          name={config.icon as any}
          size={18}
          color={isSelected ? 'white' : COLORS.textSecondary}
        />
        <Text
          style={[
            styles.categoryText,
            isSelected && styles.categoryTextActive,
          ]}
        >
          {config.label}
        </Text>
        {count > 0 && (
          <View style={[
            styles.countBadge,
            isSelected && styles.countBadgeActive,
          ]}>
            <Text style={[
              styles.countText,
              isSelected && styles.countTextActive,
            ]}>
              {count > 99 ? '99+' : count}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {categories.map(renderCategoryButton)}
      </ScrollView>
    </View>
  );
};
