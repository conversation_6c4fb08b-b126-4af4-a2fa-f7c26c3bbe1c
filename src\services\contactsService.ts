// Real Contacts Integration Service with expo-contacts
import * as Contacts from "expo-contacts";
import { collection, getDocs, query, where } from "firebase/firestore";
import { Platform } from "react-native";
import { firestore } from "./firebaseSimple";
import { errorHandlingService } from "./errorHandlingService";

export interface Contact {
  id: string;
  name: string;
  username?: string; // Unique username for search
  phoneNumber?: string; // Made optional since we now support email-only users
  email?: string; // Added email support
  avatar?: string;
  isIraChatUser: boolean;
  status?: string;
  lastSeen?: Date;
  bio?: string;
  userId?: string;
  authMethod?: 'phone' | 'email' | 'both'; // Track authentication method
  isOnline?: boolean;
}

// OPTIMIZED CONTACTS SERVICE - Fast loading with caching
class ContactsService {
  private contacts: Contact[] = [];
  private hasPermission = false;
  private contactsCache: Contact[] | null = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private isLoading = false;

  /**
   * Request contacts permission
   */
  async requestPermission(): Promise<boolean> {
    try {
      if (Platform.OS === "web") {
        console.log("🌐 Web platform - contacts not available");
        return false;
      }

      const { status } = await Contacts.requestPermissionsAsync();
      this.hasPermission = status === "granted";
      return this.hasPermission;
    } catch (error) {
      console.error("❌ Error requesting contacts permission:", error);
      return false;
    }
  }

  /**
   * Normalize phone number for consistent comparison
   */
  private normalizePhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters except +
    let normalized = phoneNumber.replace(/[^\d+]/g, "");

    // If it starts with +, keep it
    if (normalized.startsWith("+")) {
      return normalized;
    }

    // If it's a 10-digit number, assume it's US and add +1
    if (normalized.length === 10) {
      return `+1${normalized}`;
    }

    // If it's 11 digits starting with 1, add +
    if (normalized.length === 11 && normalized.startsWith("1")) {
      return `+${normalized}`;
    }

    return normalized;
  }

  /**
   * Get all phone contacts from device
   */
  async getPhoneContacts(): Promise<Contact[]> {
    try {
      if (Platform.OS === "web") {
        console.log("🌐 Web platform - contacts not available");
        return [];
      }

      const hasPermission = await this.requestPermission();
      if (!hasPermission) {
        throw new Error("Contacts permission denied");
      }

      console.log("📱 Fetching phone contacts...");

      const { data } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.ID,
          Contacts.Fields.Name,
          Contacts.Fields.FirstName,
          Contacts.Fields.LastName,
          Contacts.Fields.MiddleName,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
          Contacts.Fields.Image,
          Contacts.Fields.ImageAvailable,
          Contacts.Fields.Addresses,
          Contacts.Fields.Company,
          Contacts.Fields.JobTitle,
          Contacts.Fields.Department,
          Contacts.Fields.Note,
          Contacts.Fields.InstantMessageAddresses,
          Contacts.Fields.Dates,
          Contacts.Fields.Relationships,
          Contacts.Fields.SocialProfiles,
          Contacts.Fields.RawImage,
        ],
        sort: Contacts.SortTypes.FirstName,
      });

      const contacts = data
        .filter((contact) => {
          // Add null/undefined checks to prevent "Cannot read property 'length' of undefined" errors
          if (!contact) return false;

          try {
            // Accept contacts with name AND (phone numbers OR emails)
            const hasName = contact.name || contact.firstName || contact.lastName;
            const hasPhone = Array.isArray(contact.phoneNumbers) && contact.phoneNumbers.length > 0;
            const hasEmail = Array.isArray(contact.emails) && contact.emails.length > 0;
            return hasName && (hasPhone || hasEmail);
          } catch (error) {
            console.warn('Error filtering contact:', contact, error);
            return false;
          }
        })
        .map((contact) => {
          try {
            // Build comprehensive name from all available name fields
            const firstName = contact?.firstName || '';
            const lastName = contact?.lastName || '';
            const middleName = contact?.middleName || '';
            const fullName = contact?.name ||
              [firstName, middleName, lastName].filter(Boolean).join(' ').trim() ||
              'Unknown Contact';

            // Get primary phone number with safe array access
            const phoneNumbers = Array.isArray(contact?.phoneNumbers) ? contact.phoneNumbers : [];
            const primaryPhone = phoneNumbers.length > 0 ?
              this.normalizePhoneNumber(phoneNumbers[0]?.number || "") : '';

            // Get primary email with safe array access
            const emails = Array.isArray(contact?.emails) ? contact.emails : [];
            const primaryEmail = emails.length > 0 ? emails[0]?.email || '' : '';

            return {
              id: contact?.id || `contact_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
              name: fullName,
              firstName,
              lastName,
              phoneNumber: primaryPhone,
              email: primaryEmail, // Add email field for Contact interface
              primaryEmail,
              company: contact?.company || '',
              jobTitle: contact?.jobTitle || '',
              isIraChatUser: false, // Will be updated when checking registration
              status: "Available",
              lastSeen: new Date(),
              hasImage: contact?.imageAvailable || false,
              avatar: contact?.imageAvailable ? contact?.image?.uri : undefined,
              authMethod: (primaryPhone && primaryEmail) ? 'both' as const :
                         primaryPhone ? 'phone' as const : 'email' as const,
            };
          } catch (error) {
            console.error('Error processing contact:', contact, error);
            // Return a minimal valid contact object
            return {
              id: `error_contact_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
              name: 'Unknown Contact',
              firstName: '',
              lastName: '',
              phoneNumber: '',
              primaryEmail: '',
              company: '',
              jobTitle: '',
              isIraChatUser: false,
              status: "Available",
              lastSeen: new Date(),
              hasImage: false,
              avatar: undefined,
            };
          }
        })
        .filter((contact) => {
          // Filter out contacts without valid phone numbers or emails
          const hasValidPhone = contact.phoneNumber && contact.phoneNumber.length > 5;
          const hasValidEmail = contact.primaryEmail && contact.primaryEmail.includes('@');
          return hasValidPhone || hasValidEmail;
        });

      this.contacts = contacts;
      console.log(`✅ Found ${contacts.length} contacts from ALL sources`);
      console.log(`📱 Contacts with phone numbers: ${contacts.filter(c => c.phoneNumber).length}`);
      console.log(`📧 Contacts with emails: ${contacts.filter(c => c.primaryEmail).length}`);
      console.log(`🏢 Contacts with company info: ${contacts.filter(c => c.company).length}`);
      console.log(`📸 Contacts with profile images: ${contacts.filter(c => c.hasImage).length}`);

      return contacts;
    } catch (error: any) {
      errorHandlingService.handleError(error, 'Fetch Contacts');
      return [];
    }
  }

  /**
   * Check which contacts are registered on IraChat
   */
  async getIraChatContacts(): Promise<Contact[]> {
    try {
      if (!firestore) {
        throw new Error("Firestore not initialized");
      }

      const phoneContacts = await this.getPhoneContacts();
      if (!Array.isArray(phoneContacts) || phoneContacts.length === 0) {
        console.log("📱 No phone contacts available");
        return [];
      }

      // Filter out contacts without valid phone numbers OR emails and add safety checks
      const validContacts = phoneContacts.filter(contact => {
        if (!contact) return false;

        const hasValidPhone = contact.phoneNumber && contact.phoneNumber.length > 0;
        const hasValidEmail = contact.email && contact.email.includes('@');

        return hasValidPhone || hasValidEmail;
      });

      if (validContacts.length === 0) {
        console.log("📱 No valid contacts found (no phone numbers or emails)");
        return [];
      }

      const allPhoneNumbers = validContacts.map(
        (contact) => contact.phoneNumber,
      ).filter(Boolean); // Remove any undefined/null phone numbers

      const allEmails = validContacts.map(
        (contact) => contact.email,
      ).filter(Boolean); // Remove any undefined/null emails

      console.log(`🔍 Checking ${allPhoneNumbers.length} phone numbers and ${allEmails.length} emails against IraChat users...`);

      // Query users collection for registered phone numbers (batch in groups of 10)
      const registeredUsers = new Map();

      for (let i = 0; i < allPhoneNumbers.length; i += 10) {
        const batch = allPhoneNumbers.slice(i, i + 10);
        const usersQuery = query(
          collection(firestore, "users"),
          where("phoneNumber", "in", batch),
        );

        const snapshot = await getDocs(usersQuery);
        snapshot.forEach((doc) => {
          const userData = doc.data();
          registeredUsers.set(userData.phoneNumber, {
            userId: doc.id,
            ...userData,
          });
        });
      }

      // Check emails
      for (let i = 0; i < allEmails.length; i += 10) {
        const batch = allEmails.slice(i, i + 10);
        const usersQuery = query(
          collection(firestore, "users"),
          where("email", "in", batch),
        );

        const snapshot = await getDocs(usersQuery);
        snapshot.forEach((doc) => {
          const userData = doc.data();
          registeredUsers.set(userData.email, {
            userId: doc.id,
            ...userData,
          });
        });
      }

      console.log(`✅ Found ${registeredUsers.size} registered IraChat users`);

      // Map valid contacts to IraChat contacts with error handling
      const iraChatContacts = validContacts.map((contact) => {
        try {
          if (!contact || (!contact.phoneNumber && !contact.email)) {
            console.warn('Invalid contact during mapping:', contact);
            return contact;
          }

          const phoneUserData = contact.phoneNumber ? registeredUsers.get(contact.phoneNumber) : null;
          const emailUserData = contact.email ? registeredUsers.get(contact.email) : null;
          const userData = phoneUserData || emailUserData;

          if (userData) {
            return {
              ...contact,
              isIraChatUser: true,
              userId: userData.userId,
              avatar: userData.avatar,
              lastSeen: userData.lastSeen?.toDate(),
              isOnline: userData.isOnline || false,
              status: userData.status,
              bio: userData.bio,
              username: userData.username,
            };
          }

          return contact;
        } catch (error) {
          console.error('Error mapping contact:', contact, error);
          return contact; // Return original contact if mapping fails
        }
      }).filter(Boolean); // Remove any null/undefined results

      const registeredCount = iraChatContacts.filter(
        (c) => c.isIraChatUser,
      ).length;
      console.log(`✅ Found ${registeredCount} contacts registered on IraChat`);

      return iraChatContacts.sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      console.error("❌ Error checking IraChat contacts:", error);
      return [];
    }
  }

  /**
   * Get only registered IraChat contacts
   */
  async getRegisteredContacts(): Promise<Contact[]> {
    const contacts = await this.getIraChatContacts();
    return contacts.filter((contact) => contact.isIraChatUser);
  }

  /**
   * Search contacts by name or phone number
   */
  async searchContacts(query: string): Promise<Contact[]> {
    const contacts = await this.getIraChatContacts();
    const searchTerm = query.toLowerCase();

    return contacts.filter(
      (contact) =>
        contact.name.toLowerCase().includes(searchTerm) ||
        (contact.phoneNumber && contact.phoneNumber.includes(query)) ||
        (contact.email && contact.email.toLowerCase().includes(searchTerm)) ||
        contact.username?.toLowerCase().includes(searchTerm),
    );
  }

  /**
   * Get contact by phone number
   */
  async getContactByPhone(phoneNumber: string): Promise<Contact | null> {
    const normalizedPhone = this.normalizePhoneNumber(phoneNumber);
    const contacts = await this.getIraChatContacts();

    return (
      contacts.find((contact) => contact.phoneNumber === normalizedPhone) ||
      null
    );
  }

  /**
   * Get contact by email address
   */
  async getContactByEmail(email: string): Promise<Contact | null> {
    const normalizedEmail = email.toLowerCase().trim();
    const contacts = await this.getIraChatContacts();

    return (
      contacts.find((contact) => contact.email === normalizedEmail) ||
      null
    );
  }

  /**
   * Get contact by identifier (phone or email)
   */
  async getContactByIdentifier(identifier: string): Promise<Contact | null> {
    // Try to find by phone first, then by email
    const byPhone = await this.getContactByPhone(identifier);
    if (byPhone) return byPhone;

    // Check if identifier looks like an email
    if (identifier.includes('@')) {
      return await this.getContactByEmail(identifier);
    }

    return null;
  }

  /**
   * Get contact by user ID
   */
  async getContactByUserId(userId: string): Promise<Contact | null> {
    const contacts = await this.getIraChatContacts();
    return contacts.find((contact) => contact.userId === userId) || null;
  }

  /**
   * Refresh contacts data
   */
  async refreshContacts(): Promise<Contact[]> {
    console.log("🔄 Refreshing contacts...");
    return this.getIraChatContacts();
  }
}

// Format last seen time
export const formatLastSeen = (lastSeen: Date | string | undefined): string => {
  try {
    if (!lastSeen) {
      return "Unknown";
    }

    let date: Date;
    if (lastSeen instanceof Date) {
      date = lastSeen;
    } else if (typeof lastSeen === "string") {
      date = new Date(lastSeen);
    } else {
      return "Unknown";
    }

    // Validate the date
    if (isNaN(date.getTime())) {
      return "Unknown";
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) {
      return "Online";
    } else if (diffMins < 60) {
      return `${diffMins} min${diffMins > 1 ? "s" : ""} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours > 1 ? "s" : ""} ago`;
    } else {
      return `${diffDays} day${diffDays > 1 ? "s" : ""} ago`;
    }
  } catch (error) {
    console.error("Error formatting last seen:", error);
    return "Unknown";
  }
};

// Create service instance
const contactsServiceInstance = new ContactsService();

// Export service and utility functions
export const contactsService = {
  ...contactsServiceInstance,
  formatLastSeen,
  // Legacy compatibility methods
  getIraChatContacts: () => contactsServiceInstance.getIraChatContacts(),
  getRealDeviceContacts: () => contactsServiceInstance.getPhoneContacts(),
  getContacts: () => contactsServiceInstance.getIraChatContacts(),
  getContactById: (id: string) =>
    contactsServiceInstance.getContactByUserId(id),
  searchContacts: (query: string) =>
    contactsServiceInstance.searchContacts(query),
  getContactByPhone: (phone: string) =>
    contactsServiceInstance.getContactByPhone(phone),
};

// Named exports for backward compatibility
export const getIraChatContacts = () =>
  contactsServiceInstance.getIraChatContacts();
export const getRealDeviceContacts = () =>
  contactsServiceInstance.getPhoneContacts();
export const searchContacts = (query: string) =>
  contactsServiceInstance.searchContacts(query);
export const getContactByPhone = (phone: string) =>
  contactsServiceInstance.getContactByPhone(phone);

export default contactsService;
