import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  TextInput,
  Image,
  Alert,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { realUpdatesService } from '../services/realUpdatesService';
import { offlineCommentsService } from '../services/offlineCommentsService';
import { updateInteractionsService } from '../services/updateInteractionsService';
import { formatTimeAgo } from '../utils/dateUtils';

const { width: __SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface Comment {
  id: string;
  updateId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  timestamp: Date;
  likes: string[];
  replies: Reply[];
  parentCommentId?: string;
  isOffline?: boolean;
}

interface Reply {
  id: string;
  commentId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  timestamp: Date;
  likes: string[];
  replyToUserId?: string;
  replyToUserName?: string;
}

interface UpdatesCommentsPageProps {
  visible: boolean;
  onClose: () => void;
  updateId: string;
  onUserPress: (_userId: string) => void;
}

export const UpdatesCommentsPage: React.FC<UpdatesCommentsPageProps> = ({
  visible,
  onClose,
  updateId,
  onUserPress,
}) => {
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [replyingTo, setReplyingTo] = useState<{ commentId: string; userName: string } | null>(null);
  const [showReplies, setShowReplies] = useState<{ [commentId: string]: boolean }>({});
  const [editingComment, setEditingComment] = useState<{ commentId: string; originalText: string } | null>(null);
  const [hiddenComments, setHiddenComments] = useState<Set<string>>(new Set());
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedComments, setSelectedComments] = useState<Set<string>>(new Set());
  
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    if (visible) {
      loadComments();
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible, slideAnim]);

  const loadComments = useCallback(async () => {
    if (!updateId) return;

    setIsLoading(true);
    try {
      // Load online comments
      const result = await realUpdatesService.getUpdateComments(updateId);
      let allComments: Comment[] = [];

      if (result.success && result.comments) {
        allComments = result.comments;
      }

      // Load offline comments and merge them
      const offlineComments = offlineCommentsService.getOfflineComments(updateId);
      console.log(`📊 Loading offline comments for ${updateId}: ${offlineComments.length} found`);
      const offlineCommentsFormatted: Comment[] = offlineComments.map(offlineComment => ({
        id: offlineComment.id,
        updateId: offlineComment.updateId,
        userId: offlineComment.userId,
        userName: offlineComment.userName,
        userAvatar: offlineComment.userAvatar,
        content: offlineComment.content,
        timestamp: new Date(offlineComment.timestamp),
        likes: [],
        replies: [],
        parentCommentId: offlineComment.parentCommentId,
        isOffline: true,
      }));

      // Merge and sort by timestamp
      allComments = [...allComments, ...offlineCommentsFormatted].sort(
        (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
      );

      setComments(allComments);
    } catch (error) {
      console.error('❌ Error loading comments:', error);
    } finally {
      setIsLoading(false);
    }
  }, [updateId]);

  useEffect(() => {
    if (visible && updateId) {
      loadComments();
    }
  }, [updateId, visible, loadComments]);

  const handlePostComment = async () => {
    if (!commentText.trim() || !currentUser?.id) return;

    setIsPosting(true);
    try {
      if (editingComment) {
        // Handle editing
        const comment = comments.find(c => c.id === editingComment.commentId);
        if (comment?.isOffline) {
          // Edit offline comment
          await offlineCommentsService.editOfflineComment(updateId, editingComment.commentId, commentText.trim());
          await loadComments();
          console.log('✅ Offline comment edited:', editingComment.commentId);
        } else {
          // Edit online comment
          const result = await realUpdatesService.editComment(editingComment.commentId, commentText.trim());
          if (result.success) {
            await loadComments();
            console.log('✅ Online comment edited:', editingComment.commentId);
          } else {
            Alert.alert('Failed to edit comment');
          }
        }
        setEditingComment(null);
      } else {
        // Handle new comment with complete Firebase sync
        const userName = currentUser.name || currentUser.displayName || 'Unknown User';
        const commentId = await updateInteractionsService.commentOnUpdate(
          updateId,
          currentUser.id,
          userName,
          commentText.trim(),
          currentUser.avatar
        );

        if (commentId) {
          setReplyingTo(null);
          await loadComments();

          // Scroll to bottom to show new comment
          setTimeout(() => {
            flatListRef.current?.scrollToEnd({ animated: true });
          }, 100);

          console.log('✅ Comment posted and synced to Firebase:', commentId);
        } else {
          Alert.alert('Error', 'Failed to post comment');
        }
      }

      setCommentText('');
    } catch (error) {
      console.error('❌ Error posting comment:', error);
      Alert.alert('Error', 'Failed to post comment');
    } finally {
      setIsPosting(false);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    if (!currentUser?.id) return;

    try {
      // Find the comment to check if it's offline
      const comment = comments.find(c => c.id === commentId);
      if (comment?.isOffline) {
        Alert.alert('Check your internet connection.');
        return;
      }

      // Optimistically update the UI
      const updatedComments = comments.map(c => {
        if (c.id === commentId) {
          const isLiked = c.likes.includes(currentUser.id);
          const newLikes = isLiked
            ? c.likes.filter(id => id !== currentUser.id)
            : [...c.likes, currentUser.id];
          return { ...c, likes: newLikes };
        }
        return c;
      });
      setComments(updatedComments);

      // Then sync with server
      const result = await realUpdatesService.likeComment(commentId, currentUser.id);
      if (!result.success) {
        // Revert on failure
        await loadComments();
        Alert.alert('Error', result.error || 'Failed to like comment');
      }
    } catch (error) {
      console.error('❌ Error liking comment:', error);
      // Revert on error
      await loadComments();
      Alert.alert('Error', 'Failed to like comment');
    }
  };

  const handleLongPressComment = (commentId: string) => {
    if (selectionMode) {
      // Toggle selection
      setSelectedComments(prev => {
        const newSet = new Set(prev);
        if (newSet.has(commentId)) {
          newSet.delete(commentId);
        } else {
          newSet.add(commentId);
        }
        return newSet;
      });
    } else {
      // Enter selection mode
      setSelectionMode(true);
      setSelectedComments(new Set([commentId]));
    }
  };

  const handleTapComment = (commentId: string) => {
    if (selectionMode) {
      // Toggle selection in selection mode
      setSelectedComments(prev => {
        const newSet = new Set(prev);
        if (newSet.has(commentId)) {
          newSet.delete(commentId);
        } else {
          newSet.add(commentId);
        }
        return newSet;
      });
    }
  };

  const exitSelectionMode = () => {
    setSelectionMode(false);
    setSelectedComments(new Set());
  };

  const handleBulkDelete = async () => {
    if (!currentUser?.id || selectedComments.size === 0) return;

    try {
      const commentsToDelete = Array.from(selectedComments);
      let deletedCount = 0;
      let hiddenCount = 0;
      const newHiddenComments = new Set(hiddenComments);

      for (const commentId of commentsToDelete) {
        const comment = comments.find(c => c.id === commentId);
        if (!comment) continue;

        if (comment.userId === currentUser.id) {
          // Delete own comment
          if (comment.isOffline) {
            console.log('🗑️ Deleting offline comment:', commentId);
            await offlineCommentsService.deleteOfflineComment(updateId, commentId);
            deletedCount++;
          } else {
            console.log('🗑️ Deleting synced comment:', commentId);
            const result = await realUpdatesService.deleteComment(commentId);
            if (result.success) {
              console.log('✅ Synced comment deleted successfully:', commentId);
              deletedCount++;
            } else {
              console.error('❌ Failed to delete synced comment:', commentId, result.error);
            }
          }
        } else {
          // Hide other's comment
          newHiddenComments.add(commentId);
          hiddenCount++;
        }
      }

      // Update hidden comments state first
      if (hiddenCount > 0) {
        setHiddenComments(newHiddenComments);
      }

      // Reload comments and exit selection mode
      if (deletedCount > 0) {
        await loadComments();
      }
      exitSelectionMode();

      console.log(`✅ Bulk operation complete: ${deletedCount} deleted, ${hiddenCount} hidden`);
    } catch (error) {
      console.error('❌ Error in bulk delete:', error);
      Alert.alert('Error', 'Failed to delete some comments');
    }
  };

  const handleEditComment = (commentId: string, currentText: string) => {
    setEditingComment({ commentId, originalText: currentText });
    setCommentText(currentText);
    setReplyingTo(null); // Clear any reply state
  };

  const handleReply = (commentId: string, userName: string) => {
    // Find the comment to check if it's offline
    const comment = comments.find(c => c.id === commentId);
    if (comment?.isOffline) {
      Alert.alert('Please','Check your internet connection.');
      return;
    }

    setReplyingTo({ commentId, userName });
    setCommentText(`@${userName} `);
    setEditingComment(null); // Clear any edit state
  };

  const toggleReplies = (commentId: string) => {
    setShowReplies(prev => ({
      ...prev,
      [commentId]: !prev[commentId],
    }));
  };

  const renderReply = ({ item: reply }: { item: Reply }) => (
    <TouchableOpacity
      style={[
        styles.replyContainer,
        selectedComments.has(reply.commentId) && styles.selectedCommentItem
      ]}
      onLongPress={() => handleLongPressComment(reply.commentId)}
      onPress={() => handleTapComment(reply.commentId)}
      activeOpacity={0.7}
    >
      {selectionMode && (
        <View style={styles.selectionIndicator}>
          <Ionicons
            name={selectedComments.has(reply.commentId) ? "checkmark-circle" : "ellipse-outline"}
            size={20}
            color={selectedComments.has(reply.commentId) ? "#007AFF" : "#AAAAAA"}
          />
        </View>
      )}
      <TouchableOpacity onPress={() => onUserPress(reply.userId)}>
        <Image
          source={{
            uri: currentUser?.id === reply.userId && currentUser?.avatar
              ? currentUser.avatar
              : reply.userAvatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(reply.userName) + '&background=007AFF&color=fff&size=32'
          }}
          style={styles.replyAvatar}
          defaultSource={{ uri: 'https://ui-avatars.com/api/?name=User&background=007AFF&color=fff&size=32' }}
        />
      </TouchableOpacity>
      
      <View style={styles.replyContent}>
        <View style={styles.replyBubble}>
          <TouchableOpacity onPress={() => onUserPress(reply.userId)}>
            <Text style={styles.replyUserName}>{reply.userName}</Text>
          </TouchableOpacity>
          {reply.replyToUserName && (
            <Text style={styles.replyToText}>
              replying to <Text style={styles.replyToUser}>@{reply.replyToUserName}</Text>
            </Text>
          )}
          <Text style={styles.replyText}>{reply.content}</Text>
        </View>
        
        <View style={styles.replyActions}>
          <Text style={styles.replyTime}>{formatTimeAgo(reply.timestamp)}</Text>
          <TouchableOpacity
            style={styles.replyActionButton}
            onPress={() => handleLikeComment(reply.id)}
          >
            <Ionicons
              name={reply.likes.includes(currentUser?.id || '') ? "heart" : "heart-outline"}
              size={14}
              color={reply.likes.includes(currentUser?.id || '') ? "#FF4444" : "#AAAAAA"}
            />
            {reply.likes.length > 0 && (
              <Text style={styles.replyActionText}>{reply.likes.length}</Text>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.replyActionButton}
            onPress={() => handleReply(reply.commentId, reply.userName)}
          >
            <Ionicons name="chatbubble-outline" size={14} color="#AAAAAA" />
            <Text style={styles.replyActionText}>Reply</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderComment = ({ item: comment }: { item: Comment }) => {
    // Don't render hidden comments
    if (hiddenComments.has(comment.id)) {
      return null;
    }

    return (
    <TouchableOpacity
      style={[
        styles.commentItem,
        selectedComments.has(comment.id) && styles.selectedCommentItem
      ]}
      onLongPress={() => handleLongPressComment(comment.id)}
      onPress={() => handleTapComment(comment.id)}
      activeOpacity={0.7}
    >
      {selectionMode && (
        <View style={styles.selectionIndicator}>
          <Ionicons
            name={selectedComments.has(comment.id) ? "checkmark-circle" : "ellipse-outline"}
            size={24}
            color={selectedComments.has(comment.id) ? "#007AFF" : "#AAAAAA"}
          />
        </View>
      )}
      <TouchableOpacity onPress={() => onUserPress(comment.userId)}>
        <Image
          source={{
            uri: currentUser?.id === comment.userId && currentUser?.avatar
              ? currentUser.avatar
              : comment.userAvatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(comment.userName) + '&background=007AFF&color=fff&size=40'
          }}
          style={styles.commentAvatar}
          defaultSource={{ uri: 'https://ui-avatars.com/api/?name=User&background=007AFF&color=fff&size=40' }}
        />
      </TouchableOpacity>
      
      <View style={styles.commentContent}>
        <View style={styles.commentBubble}>
          <TouchableOpacity onPress={() => onUserPress(comment.userId)}>
            <Text style={styles.commentUserName}>{comment.userName}</Text>
          </TouchableOpacity>
          <Text style={styles.commentText}>{comment.content}</Text>
        </View>
        
        <View style={styles.commentActions}>
          <Text style={styles.commentTime}>{formatTimeAgo(comment.timestamp)}</Text>
          {comment.isOffline && (
            <View style={styles.offlineIndicator}>
              <Ionicons name="cloud-offline-outline" size={14} color="#007AFF" />
              <Text style={styles.offlineText}>Sending...</Text>
            </View>
          )}
          <TouchableOpacity
            style={styles.commentActionButton}
            onPress={() => handleLikeComment(comment.id)}
            disabled={comment.isOffline} // Disable likes for offline comments
          >
            <Ionicons
              name={comment.likes.includes(currentUser?.id || '') ? "heart" : "heart-outline"}
              size={16}
              color={comment.isOffline ? "#999" : (comment.likes.includes(currentUser?.id || '') ? "#FF4444" : "#AAAAAA")}
            />
            {comment.likes.length > 0 && (
              <Text style={styles.commentActionText}>{comment.likes.length}</Text>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.commentActionButton}
            onPress={() => handleReply(comment.id, comment.userName)}
          >
            <Ionicons name="chatbubble-outline" size={16} color="#AAAAAA" />
            <Text style={styles.commentActionText}>Reply</Text>
          </TouchableOpacity>
          {comment.userId === currentUser?.id && (
            <TouchableOpacity
              style={styles.commentActionButton}
              onPress={() => handleEditComment(comment.id, comment.content)}
            >
              <Ionicons name="create-outline" size={16} color="#007AFF" />
              <Text style={[styles.commentActionText, { color: '#007AFF' }]}>Edit</Text>
            </TouchableOpacity>
          )}
          {comment.replies.length > 0 && (
            <TouchableOpacity
              style={styles.commentActionButton}
              onPress={() => toggleReplies(comment.id)}
            >
              <Ionicons
                name={showReplies[comment.id] ? "chevron-up" : "chevron-down"}
                size={16}
                color="#AAAAAA"
              />
              <Text style={styles.commentActionText}>
                {comment.replies.length} {comment.replies.length === 1 ? 'reply' : 'replies'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
        
        {/* Replies */}
        {showReplies[comment.id] && comment.replies.length > 0 && (
          <View style={styles.repliesContainer}>
            {comment.replies.map((reply) => (
              <View key={reply.id}>
                {renderReply({ item: reply })}
              </View>
            ))}
          </View>
        )}
      </View>
    </TouchableOpacity>
    );
  };

  if (!visible) return null;

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <KeyboardAvoidingView 
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={selectionMode ? exitSelectionMode : onClose}
            style={styles.closeButton}
          >
            <Ionicons name={selectionMode ? "arrow-back" : "close"} size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>
            {selectionMode ? `${selectedComments.size} selected` : `Comments (${comments.length})`}
          </Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Selection Mode Toolbar */}
        {selectionMode && (
          <View style={styles.selectionToolbar}>
            <TouchableOpacity onPress={exitSelectionMode} style={styles.toolbarButton}>
              <Ionicons name="close" size={20} color="#FFFFFF" />
              <Text style={styles.toolbarButtonText}>Cancel</Text>
            </TouchableOpacity>

            <Text style={styles.selectionCount}>
              {selectedComments.size} selected
            </Text>

            <TouchableOpacity
              onPress={handleBulkDelete}
              style={[styles.toolbarButton, styles.deleteButton]}
              disabled={selectedComments.size === 0}
            >
              <Ionicons name="trash-outline" size={20} color="#FFFFFF" />
              <Text style={styles.toolbarButtonText}>Delete</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Comments List */}
        <FlatList
          ref={flatListRef}
          data={comments}
          renderItem={renderComment}
          keyExtractor={(item) => item.id}
          style={styles.commentsList}
          contentContainerStyle={styles.commentsContent}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            isLoading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#87CEEB" />
                <Text style={styles.loadingText}>Loading comments...</Text>
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <Ionicons name="chatbubbles-outline" size={48} color="#ccc" />
                <Text style={styles.emptyText}>No comments yet</Text>
                <Text style={styles.emptySubtext}>Be the first to comment!</Text>
              </View>
            )
          }
        />

        {/* Comment Input */}
        <View style={styles.inputContainer}>
          {replyingTo && (
            <View style={styles.replyingToContainer}>
              <Text style={styles.replyingToText}>
                Replying to @{replyingTo.userName}
              </Text>
              <TouchableOpacity onPress={() => {
                setReplyingTo(null);
                setCommentText('');
              }}>
                <Ionicons name="close" size={16} color="#666" />
              </TouchableOpacity>
            </View>
          )}

          {editingComment && (
            <View style={styles.replyingToContainer}>
              <Text style={styles.replyingToText}>
                Editing comment
              </Text>
              <TouchableOpacity onPress={() => {
                setEditingComment(null);
                setCommentText('');
              }}>
                <Ionicons name="close" size={16} color="#666" />
              </TouchableOpacity>
            </View>
          )}
          
          <View style={styles.inputRow}>
            <Image
              source={{ uri: currentUser?.avatar || 'https://via.placeholder.com/32' }}
              style={styles.inputAvatar}
            />
            <TextInput
              style={styles.textInput}
              placeholder={
                editingComment
                  ? "Edit your comment..."
                  : replyingTo
                    ? `Reply to @${replyingTo.userName}...`
                    : "Add a comment..."
              }
              placeholderTextColor="#999"
              value={commentText}
              onChangeText={setCommentText}
              multiline
              maxLength={500}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                { opacity: commentText.trim() && !isPosting ? 1 : 0.5 }
              ]}
              onPress={handlePostComment}
              disabled={!commentText.trim() || isPosting}
            >
              {isPosting ? (
                <ActivityIndicator size="small" color="#87CEEB" />
              ) : (
                <Ionicons
                  name={editingComment ? "checkmark" : "send"}
                  size={20}
                  color="#87CEEB"
                />
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#2A2A2A',
    zIndex: 1000,
  },
  keyboardAvoid: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
    backgroundColor: '#2A2A2A',
    borderBottomWidth: 1,
    borderBottomColor: '#444',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  headerSpacer: {
    width: 40,
  },
  commentsList: {
    flex: 1,
    backgroundColor: '#2A2A2A',
  },
  commentsContent: {
    paddingVertical: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: '#666666',
  },
  emptySubtext: {
    marginTop: 4,
    fontSize: 14,
    color: '#999999',
  },
  commentItem: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: '#333',
  },
  selectedCommentItem: {
    backgroundColor: '#1A1A2E',
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  selectionIndicator: {
    marginRight: 12,
    justifyContent: 'center',
  },
  selectionToolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#1A1A2E',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#444',
  },
  toolbarButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#333',
  },
  toolbarButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    marginLeft: 6,
    fontWeight: '500',
  },
  selectionCount: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  deleteButton: {
    backgroundColor: '#FF4444',
  },
  commentAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  commentContent: {
    flex: 1,
  },
  commentBubble: {
    backgroundColor: '#3A3A3A',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  commentUserName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 2,
  },
  commentText: {
    fontSize: 15,
    color: '#FFFFFF',
    lineHeight: 20,
    fontWeight: '400',
  },
  commentActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    marginLeft: 12,
    flexWrap: 'wrap',
    paddingRight: 16,
    maxWidth: '100%',
  },
  commentTime: {
    fontSize: 12,
    color: '#AAAAAA',
    marginRight: 16,
    fontWeight: '400',
  },
  commentActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
    marginBottom: 4,
    paddingVertical: 2,
  },
  commentActionText: {
    fontSize: 12,
    color: '#AAAAAA',
    marginLeft: 4,
    fontWeight: '400',
  },
  repliesContainer: {
    marginTop: 8,
    marginLeft: 12,
  },
  replyContainer: {
    flexDirection: 'row',
    paddingVertical: 6,
    alignItems: 'flex-start',
  },
  replyAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  replyContent: {
    flex: 1,
  },
  replyBubble: {
    backgroundColor: '#3A3A3A',
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  replyUserName: {
    fontSize: 12,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 1,
  },
  replyToText: {
    fontSize: 11,
    color: '#666666',
    marginBottom: 2,
  },
  replyToUser: {
    color: '#87CEEB',
    fontWeight: '500',
  },
  replyText: {
    fontSize: 12,
    color: '#FFFFFF',
    lineHeight: 16,
  },
  replyActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    marginLeft: 10,
  },
  replyTime: {
    fontSize: 11,
    color: '#AAAAAA',
    marginRight: 12,
  },
  replyActionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  replyActionText: {
    fontSize: 11,
    color: '#AAAAAA',
    marginLeft: 3,
  },
  inputContainer: {
    backgroundColor: '#2A2A2A',
    borderTopWidth: 1,
    borderTopColor: '#444',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingBottom: 34,
  },
  replyingToContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    marginBottom: 8,
  },
  replyingToText: {
    fontSize: 12,
    color: '#87CEEB',
    fontWeight: '500',
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  inputAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 12,
  },
  textInput: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 14,
    color: '#000000',
    maxHeight: 100,
  },
  sendButton: {
    marginLeft: 8,
    padding: 8,
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
  },
  offlineText: {
    fontSize: 12,
    color: '#007AFF',
    marginLeft: 4,
    fontStyle: 'italic',
  },
});
