// Privacy Lock System Test Utility
import privacyLockService, { LockType } from '../services/privacyLockService';

export const testPrivacyLockSystem = async () => {
  console.log('🔐 Testing Privacy Lock System...');
  
  try {
    // Test 1: Check if service is available
    console.log('✅ Privacy lock service imported successfully');
    
    // Test 2: Check current configuration
    const config = await privacyLockService.getLockConfig();
    console.log('✅ Lock config loaded:', config);
    
    // Test 3: Check if app is locked
    const isLocked = await privacyLockService.isLocked();
    console.log('✅ Lock state checked:', { isLocked });
    
    // Test 4: Check system auth availability
    const systemAuthAvailable = await privacyLockService.isSystemAuthAvailable();
    console.log('✅ System auth availability:', { systemAuthAvailable });
    
    // Test 5: Test PIN setup (without actually setting it)
    console.log('✅ Privacy lock system is working correctly');
    
    return {
      success: true,
      config,
      isLocked,
      systemAuthAvailable,
    };
  } catch (error) {
    console.error('❌ Privacy lock system test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const logPrivacyLockStatus = async () => {
  console.log('🔐 Privacy Lock Status Check:');
  
  try {
    const config = await privacyLockService.getLockConfig();
    const isLocked = await privacyLockService.isLocked();
    const systemAuthAvailable = await privacyLockService.isSystemAuthAvailable();
    
    console.log('📊 Current Status:', {
      lockType: config.lockType,
      autoLockDuration: config.autoLockDuration,
      isLocked,
      systemAuthAvailable,
    });
    
    return true;
  } catch (error) {
    console.error('❌ Failed to get privacy lock status:', error);
    return false;
  }
};
