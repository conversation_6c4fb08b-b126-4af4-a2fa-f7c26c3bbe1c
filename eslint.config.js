// https://docs.expo.dev/guides/using-eslint/
const { defineConfig } = require("eslint/config");
const expoConfig = require("eslint-config-expo/flat");

module.exports = defineConfig([
  {
    ignores: [
      "dist/**",
      "node_modules/**",
      "functions/**",
      "scripts/**",
      "test-*.js",
      "verify-*.js",
      "utils/**/*.ts",
      "*.config.js",
      "!eslint.config.js",
    ],
  },
  expoConfig,
  {
    languageOptions: {
      globals: {
        __dirname: "readonly",
        __filename: "readonly",
        process: "readonly",
        Buffer: "readonly",
        global: "readonly",
      },
    },
    rules: {
      // Production-ready rules - suppress non-critical warnings
      'no-unused-vars': ['warn', {
        'argsIgnorePattern': '^_',
        'varsIgnorePattern': '^_',
        'ignoreRestSiblings': true
      }],
      'react-hooks/exhaustive-deps': 'warn',
      'react/no-unescaped-entities': 'warn',
      'import/no-duplicates': 'warn',
      'import/first': 'warn',
      'import/export': 'warn',
      'import/no-named-as-default': 'warn',
      'no-unreachable': 'warn',
      'no-undef': 'warn',
      // Allow console.log in production for debugging
      'no-console': 'off',
    },
  },
]);
