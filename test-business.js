// Simple test script to verify business functionality
const { businessService } = require('./src/services/businessService');

async function testBusinessFunctionality() {
  console.log('🧪 Testing Business Functionality...');
  
  try {
    // Test 1: Create a business profile
    console.log('\n📝 Test 1: Creating business profile...');
    const profileData = {
      userId: 'test_user_123',
      businessName: 'Test Business',
      businessType: 'retail',
      description: 'A test business for verification',
      contactInfo: {
        email: '<EMAIL>',
        primaryPhone: '+256700000000',
        secondaryPhone: '',
        website: 'https://testbusiness.com',
      },
      location: {
        address: 'Test Address',
        city: 'Kampala',
        district: 'Central',
        region: 'Central',
        country: 'Uganda',
        coordinates: { latitude: 0.3476, longitude: 32.5825 }
      },
      isVerified: false,
      verificationDocuments: [],
      totalPosts: 0,
      totalViews: 0,
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0,
      totalDownloads: 0,
      allowDirectMessages: true,
      allowPhoneCalls: true,
      businessHours: [
        { dayOfWeek: 1, isOpen: true, openTime: '08:00', closeTime: '18:00' },
        { dayOfWeek: 2, isOpen: true, openTime: '08:00', closeTime: '18:00' },
        { dayOfWeek: 3, isOpen: true, openTime: '08:00', closeTime: '18:00' },
        { dayOfWeek: 4, isOpen: true, openTime: '08:00', closeTime: '18:00' },
        { dayOfWeek: 5, isOpen: true, openTime: '08:00', closeTime: '18:00' },
        { dayOfWeek: 6, isOpen: true, openTime: '08:00', closeTime: '16:00' },
        { dayOfWeek: 0, isOpen: false },
      ],
    };

    const profileResult = await businessService.createBusinessProfile(profileData);
    console.log('Profile creation result:', profileResult);

    if (profileResult.success && profileResult.data) {
      console.log('✅ Business profile created successfully!');
      
      // Test 2: Get the business profile
      console.log('\n📖 Test 2: Getting business profile...');
      const fetchedProfile = await businessService.getUserBusinessProfile('test_user_123');
      console.log('Fetched profile:', fetchedProfile ? 'Found' : 'Not found');
      
      if (fetchedProfile) {
        console.log('✅ Business profile fetched successfully!');
        
        // Test 3: Create a business post
        console.log('\n📦 Test 3: Creating business post...');
        const postData = {
          businessId: fetchedProfile.id,
          businessName: fetchedProfile.businessName,
          businessLogo: fetchedProfile.logo,
          businessType: fetchedProfile.businessType,
          isVerified: fetchedProfile.isVerified,
          title: 'Test Product',
          description: 'A test product for verification',
          media: [],
          tags: ['test', 'product'],
          category: 'electronics',
          price: 100000,
          currency: 'UGX',
          isNegotiable: true,
          availability: 'in_stock',
          location: fetchedProfile.location,
        };

        const postResult = await businessService.posts.createPost(postData);
        console.log('Post creation result:', postResult);

        if (postResult.success && postResult.data) {
          console.log('✅ Business post created successfully!');
          
          // Test 4: Get marketplace posts
          console.log('\n📡 Test 4: Getting marketplace posts...');
          const posts = await businessService.getMarketplacePosts({});
          console.log('Marketplace posts:', posts.length, 'found');
          
          if (posts.length > 0) {
            console.log('✅ Marketplace posts fetched successfully!');
            console.log('First post:', posts[0].title);
          } else {
            console.log('⚠️ No marketplace posts found');
          }
        } else {
          console.log('❌ Business post creation failed:', postResult.error);
        }
      } else {
        console.log('❌ Business profile fetch failed');
      }
    } else {
      console.log('❌ Business profile creation failed:', profileResult.error);
    }
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the test
testBusinessFunctionality().then(() => {
  console.log('\n🏁 Test completed');
}).catch(error => {
  console.error('❌ Test script error:', error);
});
