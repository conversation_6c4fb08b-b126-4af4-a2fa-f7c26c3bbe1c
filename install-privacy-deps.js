#!/usr/bin/env node

const { execSync } = require('child_process');

console.log('Installing privacy lock dependencies...');

try {
  // Install required packages
  execSync('npm install crypto-js @types/crypto-js expo-local-authentication', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  console.log('✅ Privacy lock dependencies installed successfully!');
  console.log('📱 Please restart your Expo development server to see the changes.');
  console.log('🔒 The logout button has been replaced with Privacy Settings and Lock App buttons.');
  
} catch (error) {
  console.error('❌ Error installing dependencies:', error.message);
  console.log('Please run manually: npm install crypto-js @types/crypto-js expo-local-authentication');
}
