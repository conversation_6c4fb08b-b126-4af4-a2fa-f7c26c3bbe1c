import { useState, useCallback, useRef, useEffect } from 'react';
import { UnifiedChatItem } from '../components/ModernChatItem';
import { chatService } from '../services/chatService';

export interface ChatLoaderState {
  chats: UnifiedChatItem[];
  isLoadingChats: boolean;
  hasLoadedOnce: boolean;
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
  error: string | null;
}

export interface ChatLoaderActions {
  loadUserChats: () => Promise<void>;
  setError: (_error: string | null) => void;
  setConnectionStatus: (_status: 'connected' | 'connecting' | 'disconnected') => void;
}

export const useChatLoader = (currentUserId?: string): ChatLoaderState & ChatLoaderActions => {
  const [chats, setChats] = useState<UnifiedChatItem[]>([]);
  const [isLoadingChats, setIsLoadingChats] = useState(true);
  const [hasLoadedOnce, setHasLoadedOnce] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connecting');
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  const loadUserChats = useCallback(async () => {
    if (!currentUserId) {
      setChats([]);
      setIsLoadingChats(false);
      setHasLoadedOnce(true);
      setConnectionStatus('disconnected');
      return;
    }

    try {
      setConnectionStatus('connecting');
      setError(null);

      const unsubscribe = chatService.getUserChats(currentUserId, (chatsData: any[]) => {
        const unifiedChats: UnifiedChatItem[] = chatsData.map((chat: any) => ({
          id: chat.id,
          name: chat.name || 'Chat',
          avatar: chat.avatar || '',
          lastMessage: chat.lastMessage || 'No messages yet',
          lastMessageTime: chat.lastMessageAt || new Date(),
          lastMessageAt: chat.lastMessageAt,
          unreadCount: chat.unreadCount?.[currentUserId] || 0,
          isGroup: chat.isGroup || false,
          participants: chat.participants || [],
          participantNames: chat.participantNames || {},
          participantAvatars: chat.participantAvatars || {},
          isOnline: false,
          isTyping: false,
          isPinned: false,
          isMuted: false,
          isArchived: false,
          messageCount: 0,
          mediaCount: 0,
          lastMessageSender: chat.lastMessageBy,
          lastMessageType: 'text',
          lastMessageBy: chat.lastMessageBy,
          createdAt: chat.createdAt || new Date(),
          updatedAt: chat.updatedAt || new Date(),
          chatSettings: {
            notifications: true,
            readReceipts: true,
            typingIndicators: true,
          },
        }));

        setChats(unifiedChats);
        setConnectionStatus('connected');
        setIsLoadingChats(false);
        setHasLoadedOnce(true);
      });

      unsubscribeRef.current = unsubscribe;
    } catch (error) {
      setConnectionStatus('disconnected');
      setIsLoadingChats(false);
      setHasLoadedOnce(true);
      setError(error instanceof Error ? error.message : 'Failed to load chats');
    }
  }, [currentUserId]);

  // Initialize and cleanup
  useEffect(() => {
    loadUserChats();
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, [loadUserChats]);

  return {
    chats,
    isLoadingChats,
    hasLoadedOnce,
    connectionStatus,
    error,
    loadUserChats,
    setError,
    setConnectionStatus,
  };
};
