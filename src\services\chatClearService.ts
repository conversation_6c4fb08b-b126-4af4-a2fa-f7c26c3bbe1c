/**
 * Chat Clear Service for IraChat
 * Provides comprehensive chat clearing functionality
 * Handles both Firebase and offline database clearing
 */

import { Alert } from 'react-native';
import { chatsListService } from './chatsListService';
import { offlineDatabaseService } from './offlineDatabase';
import { realChatService } from './realChatService';

export interface ChatClearOptions {
  clearMessages: boolean;
  clearMedia: boolean;
  clearAll: boolean;
  keepStarred?: boolean;
}

export class ChatClearService {
  /**
   * Clear all messages from a chat
   */
  async clearChatMessages(
    chatId: string, 
    userId: string,
    options: ChatClearOptions = { clearMessages: true, clearMedia: true, clearAll: false }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🧹 Starting chat clear process for:', chatId);

      // Show confirmation dialog
      const confirmed = await this.showClearConfirmation(options);
      if (!confirmed) {
        return { success: false, error: 'User cancelled' };
      }

      let results: { success: boolean; error?: string }[] = [];

      // Clear from real chat service (handles both online and offline)
      if (options.clearAll) {
        const result = await realChatService.clearChatCompletely(chatId);
        results.push(result);
      } else if (options.clearMessages && options.clearMedia) {
        const result = await realChatService.clearChatMessages(chatId);
        results.push(result);
      } else if (options.clearMedia) {
        const result = await realChatService.clearChatMedia(chatId);
        results.push(result);
      }

      // Clear from offline services as backup
      if (options.clearMessages || options.clearAll) {
        try {
          // Clear messages from local database
          const db = offlineDatabaseService.getDatabase();
          await db.runAsync('UPDATE messages SET isDeleted = 1 WHERE chatId = ?', [chatId]);
        } catch (error) {
          console.warn('⚠️ Offline message clearing failed:', error);
        }
      }

      // Update chat list
      try {
        if (options.clearAll) {
          await chatsListService.deleteChat(userId, chatId);
        } else {
          // Update last message to indicate cleared
          await chatsListService.updateLastMessage(userId, chatId, {
            content: 'Chat cleared',
            senderId: userId,
            senderName: 'You',
            timestamp: new Date(),
            type: 'text',
            isRead: true,
          });
        }
      } catch (error) {
        console.warn('⚠️ Chat list update failed:', error);
      }

      // Check if any operation failed
      const hasFailures = results.some(result => !result.success);
      if (hasFailures) {
        const errors = results.filter(r => !r.success).map(r => r.error).join(', ');
        return { success: false, error: `Some operations failed: ${errors}` };
      }

      console.log('✅ Chat cleared successfully');
      return { success: true };

    } catch (error) {
      console.error('❌ Error clearing chat:', error);
      return { success: false, error: 'Failed to clear chat' };
    }
  }

  /**
   * Clear multiple chats
   */
  async clearMultipleChats(
    chatIds: string[],
    userId: string,
    options: ChatClearOptions
  ): Promise<{ success: boolean; results?: { [chatId: string]: boolean }; error?: string }> {
    try {
      const results: { [chatId: string]: boolean } = {};

      for (const chatId of chatIds) {
        const result = await this.clearChatMessages(chatId, userId, options);
        results[chatId] = result.success;
      }

      const successCount = Object.values(results).filter(success => success).length;
      const totalCount = chatIds.length;

      if (successCount === totalCount) {
        return { success: true, results };
      } else {
        return { 
          success: false, 
          results, 
          error: `${successCount}/${totalCount} chats cleared successfully` 
        };
      }
    } catch (error) {
      return { success: false, error: 'Failed to clear multiple chats' };
    }
  }

  /**
   * Clear chat with specific date range
   */
  async clearChatByDateRange(
    chatId: string,
    userId: string,
    startDate: Date,
    endDate: Date,
    options: ChatClearOptions
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // This would require implementing date-range specific clearing
      // For now, we'll use the general clear function
      console.log('🗓️ Clearing chat by date range:', { chatId, startDate, endDate });
      
      // TODO: Implement date-range specific clearing
      return await this.clearChatMessages(chatId, userId, options);
    } catch (error) {
      return { success: false, error: 'Failed to clear chat by date range' };
    }
  }

  /**
   * Show confirmation dialog for clearing chat
   */
  private async showClearConfirmation(options: ChatClearOptions): Promise<boolean> {
    return new Promise((resolve) => {
      let title = 'Clear Chat';
      let message = '';

      if (options.clearAll) {
        title = 'Delete Chat';
        message = 'This will permanently delete this chat and all its messages. This action cannot be undone.';
      } else if (options.clearMessages && options.clearMedia) {
        message = 'This will clear all messages and media from this chat. This action cannot be undone.';
      } else if (options.clearMedia) {
        message = 'This will clear all media files from this chat. Text messages will remain.';
      } else if (options.clearMessages) {
        message = 'This will clear all text messages from this chat. Media files will remain.';
      }

      Alert.alert(
        title,
        message,
        [
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => resolve(false),
          },
          {
            text: options.clearAll ? 'Delete' : 'Clear',
            style: 'destructive',
            onPress: () => resolve(true),
          },
        ]
      );
    });
  }

  /**
   * Get clear options for UI
   */
  getClearOptions(): { label: string; value: ChatClearOptions; icon: string }[] {
    return [
      {
        label: 'Clear All Messages',
        value: { clearMessages: true, clearMedia: true, clearAll: false },
        icon: 'trash-outline',
      },
      {
        label: 'Clear Text Messages Only',
        value: { clearMessages: true, clearMedia: false, clearAll: false },
        icon: 'chatbox-outline',
      },
      {
        label: 'Clear Media Only',
        value: { clearMessages: false, clearMedia: true, clearAll: false },
        icon: 'images-outline',
      },
      {
        label: 'Delete Entire Chat',
        value: { clearMessages: false, clearMedia: false, clearAll: true },
        icon: 'trash',
      },
    ];
  }

  /**
   * Check if user has permission to clear chat
   */
  async canClearChat(chatId: string, userId: string): Promise<boolean> {
    try {
      // For individual chats, user can always clear their own view
      // For group chats, check if user is admin (if needed)
      console.log(`Checking clear permission for chat ${chatId} and user ${userId}`);
      return true; // For now, allow all users to clear their chat view
    } catch (error) {
      return false;
    }
  }
}

export const chatClearService = new ChatClearService();
