/**
 * Voice Recorder Service for IraChat
 * Provides voice recording functionality for story narration
 */

import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';

export interface VoiceRecording {
  id: string;
  uri: string;
  duration: number;
  size: number;
  createdAt: Date;
  waveformData?: number[];
}

export interface RecordingStatus {
  isRecording: boolean;
  duration: number;
  metering?: number;
}

class VoiceRecorderService {
  private recording: Audio.Recording | null = null;
  private sound: Audio.Sound | null = null;
  private isRecording = false;
  private isPlaying = false;
  private recordingDuration = 0;
  private meteringData: number[] = [];

  /**
   * Initialize audio session
   */
  async initialize(): Promise<boolean> {
    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      });
      console.log('✅ Voice recorder service initialized');
      return true;
    } catch (error) {
      console.error('❌ Error initializing voice recorder:', error);
      Alert.alert('Error', 'Failed to initialize voice recorder');
      return false;
    }
  }

  /**
   * Start recording voice
   */
  async startRecording(): Promise<boolean> {
    try {
      if (this.isRecording) {
        console.warn('⚠️ Recording already in progress');
        return false;
      }

      // Request permissions
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Microphone permission is required to record voice notes.');
        return false;
      }

      // Create recording
      this.recording = new Audio.Recording();
      await this.recording.prepareToRecordAsync({
        android: {
          extension: '.m4a',
          outputFormat: Audio.AndroidOutputFormat.MPEG_4,
          audioEncoder: Audio.AndroidAudioEncoder.AAC,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
        },
        ios: {
          extension: '.m4a',
          outputFormat: Audio.IOSOutputFormat.MPEG4AAC,
          audioQuality: Audio.IOSAudioQuality.HIGH,
          sampleRate: 44100,
          numberOfChannels: 2,
          bitRate: 128000,
          linearPCMBitDepth: 16,
          linearPCMIsBigEndian: false,
          linearPCMIsFloat: false,
        },
        web: {
          mimeType: 'audio/webm',
          bitsPerSecond: 128000,
        },
      });

      await this.recording.startAsync();
      this.isRecording = true;
      this.recordingDuration = 0;
      this.meteringData = [];

      console.log('🎤 Voice recording started');
      return true;
    } catch (error) {
      console.error('❌ Error starting recording:', error);
      Alert.alert('Error', 'Failed to start recording');
      return false;
    }
  }

  /**
   * Stop recording voice
   */
  async stopRecording(): Promise<VoiceRecording | null> {
    try {
      if (!this.recording || !this.isRecording) {
        console.warn('⚠️ No recording in progress');
        return null;
      }

      await this.recording.stopAndUnloadAsync();
      const uri = this.recording.getURI();
      this.isRecording = false;

      if (!uri) {
        console.error('❌ No recording URI available');
        return null;
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(uri);
      const duration = this.recordingDuration;

      const voiceRecording: VoiceRecording = {
        id: `voice_${Date.now()}`,
        uri,
        duration,
        size: fileInfo.size || 0,
        createdAt: new Date(),
        waveformData: this.meteringData.length > 0 ? this.meteringData : undefined,
      };

      // Clean up
      this.recording = null;
      this.recordingDuration = 0;
      this.meteringData = [];

      console.log('🎤 Voice recording completed:', voiceRecording);
      return voiceRecording;
    } catch (error) {
      console.error('❌ Error stopping recording:', error);
      Alert.alert('Error', 'Failed to stop recording');
      return null;
    }
  }

  /**
   * Cancel current recording
   */
  async cancelRecording(): Promise<void> {
    try {
      if (this.recording && this.isRecording) {
        await this.recording.stopAndUnloadAsync();
        const uri = this.recording.getURI();
        
        // Delete the recording file
        if (uri) {
          await FileSystem.deleteAsync(uri, { idempotent: true });
        }
      }

      this.recording = null;
      this.isRecording = false;
      this.recordingDuration = 0;
      this.meteringData = [];

      console.log('🗑️ Voice recording cancelled');
    } catch (error) {
      console.error('❌ Error cancelling recording:', error);
    }
  }

  /**
   * Play voice recording
   */
  async playRecording(uri: string): Promise<boolean> {
    try {
      if (this.isPlaying) {
        await this.stopPlayback();
      }

      const { sound } = await Audio.Sound.createAsync(
        { uri },
        { shouldPlay: true, volume: 1.0 }
      );

      this.sound = sound;
      this.isPlaying = true;

      // Set up playback status listener
      sound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded && status.didJustFinish) {
          this.isPlaying = false;
          this.sound = null;
        }
      });

      console.log('🔊 Playing voice recording');
      return true;
    } catch (error) {
      console.error('❌ Error playing recording:', error);
      Alert.alert('Error', 'Failed to play recording');
      return false;
    }
  }

  /**
   * Stop playback
   */
  async stopPlayback(): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.unloadAsync();
        this.sound = null;
        this.isPlaying = false;
        console.log('🔇 Voice playback stopped');
      }
    } catch (error) {
      console.error('❌ Error stopping playback:', error);
    }
  }

  /**
   * Get current recording status
   */
  async getRecordingStatus(): Promise<RecordingStatus> {
    if (!this.recording || !this.isRecording) {
      return {
        isRecording: false,
        duration: 0,
      };
    }

    try {
      const status = await this.recording.getStatusAsync();
      if (status.isRecording) {
        this.recordingDuration = status.durationMillis || 0;
        
        // Add metering data for waveform (simulated)
        const metering = status.metering || Math.random() * 100;
        this.meteringData.push(metering);
        
        return {
          isRecording: true,
          duration: this.recordingDuration,
          metering,
        };
      }
    } catch (error) {
      console.error('❌ Error getting recording status:', error);
    }

    return {
      isRecording: false,
      duration: this.recordingDuration,
    };
  }

  /**
   * Format duration for display
   */
  formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Check if currently recording
   */
  getIsRecording(): boolean {
    return this.isRecording;
  }

  /**
   * Check if currently playing
   */
  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    await this.cancelRecording();
    await this.stopPlayback();
  }
}

export const voiceRecorderService = new VoiceRecorderService();

// Initialize the service
voiceRecorderService.initialize();
