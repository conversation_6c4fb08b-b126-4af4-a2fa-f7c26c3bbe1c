/**
 * Local Media Service for IraChat
 * Provides real camera, gallery, and local media functionality
 */

import * as ImagePicker from 'expo-image-picker';
import * as MediaLibrary from 'expo-media-library';
import { Alert, Platform } from 'react-native';

export interface MediaResult {
  uri: string;
  type: 'image' | 'video';
  width?: number;
  height?: number;
  duration?: number;
  fileSize?: number;
  fileName?: string;
}

export interface MediaOptions {
  mediaTypes?: 'images' | 'videos' | ['images', 'videos'];
  allowsEditing?: boolean;
  aspect?: [number, number];
  quality?: number;
  allowsMultipleSelection?: boolean;
  videoMaxDuration?: number;
}

class LocalMediaService {
  /**
   * Request camera permissions
   */
  async requestCameraPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please grant camera permission to take photos and videos.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => ImagePicker.requestCameraPermissionsAsync() }
          ]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('❌ Error requesting camera permissions:', error);
      return false;
    }
  }

  /**
   * Request media library permissions
   */
  async requestMediaLibraryPermissions(): Promise<boolean> {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Media Library Permission Required',
          'Please grant media library permission to access your photos and videos.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Settings', onPress: () => ImagePicker.requestMediaLibraryPermissionsAsync() }
          ]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('❌ Error requesting media library permissions:', error);
      return false;
    }
  }

  /**
   * Take a photo using the camera
   */
  async takePhoto(options: MediaOptions = {}): Promise<MediaResult | null> {
    try {
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) return null;

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: options.mediaTypes || 'images',
        allowsEditing: options.allowsEditing ?? true,
        aspect: options.aspect || [4, 3],
        quality: options.quality ?? 1,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      const asset = result.assets[0];
      return {
        uri: asset.uri,
        type: asset.type === 'video' ? 'video' : 'image',
        width: asset.width,
        height: asset.height,
        duration: asset.duration ?? undefined,
        fileSize: asset.fileSize,
        fileName: asset.fileName ?? undefined,
      };
    } catch (error) {
      console.error('❌ Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
      return null;
    }
  }

  /**
   * Record a video using the camera
   */
  async recordVideo(options: MediaOptions = {}): Promise<MediaResult | null> {
    try {
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) return null;

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: 'videos',
        allowsEditing: options.allowsEditing ?? true,
        quality: options.quality ?? 1,
        videoMaxDuration: options.videoMaxDuration ?? 60, // 60 seconds max
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return null;
      }

      const asset = result.assets[0];
      return {
        uri: asset.uri,
        type: 'video',
        width: asset.width,
        height: asset.height,
        duration: asset.duration ?? undefined,
        fileSize: asset.fileSize,
        fileName: asset.fileName ?? undefined,
      };
    } catch (error) {
      console.error('❌ Error recording video:', error);
      Alert.alert('Error', 'Failed to record video');
      return null;
    }
  }

  /**
   * Pick media from gallery
   */
  async pickFromGallery(options: MediaOptions = {}): Promise<MediaResult[]> {
    try {
      const hasPermission = await this.requestMediaLibraryPermissions();
      if (!hasPermission) return [];

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: options.mediaTypes || ['images', 'videos'],
        allowsEditing: options.allowsEditing ?? false,
        aspect: options.aspect,
        quality: options.quality ?? 1,
        allowsMultipleSelection: options.allowsMultipleSelection ?? false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return [];
      }

      return result.assets.map(asset => ({
        uri: asset.uri,
        type: asset.type === 'video' ? 'video' : 'image',
        width: asset.width,
        height: asset.height,
        duration: asset.duration ?? undefined,
        fileSize: asset.fileSize,
        fileName: asset.fileName ?? undefined,
      }));
    } catch (error) {
      console.error('❌ Error picking from gallery:', error);
      Alert.alert('Error', 'Failed to pick media from gallery');
      return [];
    }
  }

  /**
   * Show media picker options
   */
  async showMediaPicker(
    onPhoto?: (result: MediaResult) => void,
    onVideo?: (result: MediaResult) => void,
    onGallery?: (results: MediaResult[]) => void
  ): Promise<void> {
    Alert.alert(
      'Select Media',
      'Choose how you want to add media',
      [
        {
          text: 'Take Photo',
          onPress: async () => {
            const result = await this.takePhoto();
            if (result && onPhoto) {
              onPhoto(result);
            }
          }
        },
        {
          text: 'Record Video',
          onPress: async () => {
            const result = await this.recordVideo();
            if (result && onVideo) {
              onVideo(result);
            }
          }
        },
        {
          text: 'Choose from Gallery',
          onPress: async () => {
            const results = await this.pickFromGallery({ allowsMultipleSelection: true });
            if (results.length > 0 && onGallery) {
              onGallery(results);
            }
          }
        },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  }

  /**
   * Save media to device gallery
   */
  async saveToGallery(uri: string): Promise<boolean> {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant permission to save media to your gallery');
        return false;
      }

      await MediaLibrary.saveToLibraryAsync(uri);
      Alert.alert('Success', 'Media saved to gallery');
      return true;
    } catch (error) {
      console.error('❌ Error saving to gallery:', error);
      Alert.alert('Error', 'Failed to save media to gallery');
      return false;
    }
  }

  /**
   * Get media info
   */
  getMediaInfo(result: MediaResult): string {
    const sizeInMB = result.fileSize ? (result.fileSize / (1024 * 1024)).toFixed(2) : 'Unknown';
    const dimensions = result.width && result.height ? `${result.width}x${result.height}` : 'Unknown';
    const duration = result.duration ? `${Math.round(result.duration)}s` : '';
    
    return `${result.type.toUpperCase()} • ${dimensions} • ${sizeInMB}MB ${duration}`.trim();
  }
}

export const localMediaService = new LocalMediaService();
export default localMediaService;
