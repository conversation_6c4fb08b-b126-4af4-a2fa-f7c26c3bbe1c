// 🎥 FULL-FEATURED VIDEO PLAYER WITH CONTROLS
// Complete WhatsApp-style video viewing experience

import React, { useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
  StyleSheet,
  Text,
  ActivityIndicator,
} from 'react-native';
import { Video, ResizeMode, AVPlaybackStatus } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import { audioManager } from '../services/audioManager';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface VideoPlayerProps {
  visible: boolean;
  videoUri: string;
  onClose: () => void;
  caption?: string;
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  visible,
  videoUri,
  onClose,
  caption,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [duration, setDuration] = useState(0);
  const [position, setPosition] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const videoRef = useRef<Video>(null);
  const controlsTimeout = useRef<NodeJS.Timeout | null>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Auto-hide controls after 3 seconds
  const resetControlsTimeout = () => {
    if (controlsTimeout.current) {
      clearTimeout(controlsTimeout.current);
    }
    
    setShowControls(true);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();

    controlsTimeout.current = setTimeout(() => {
      if (isPlaying) {
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }).start(() => setShowControls(false));
      }
    }, 3000);
  };

  // Handle video status updates
  const onPlaybackStatusUpdate = (status: AVPlaybackStatus) => {
    if (status.isLoaded) {
      setIsLoading(false);
      setIsPlaying(status.isPlaying);
      setPosition(status.positionMillis || 0);
      setDuration(status.durationMillis || 0);
    }
  };

  // Toggle play/pause with audio manager integration
  const togglePlayPause = async () => {
    if (videoRef.current) {
      if (isPlaying) {
        await videoRef.current.pauseAsync();
      } else {
        // Stop any playing audio before starting video
        await audioManager.stopAll();
        audioManager.registerVideo(videoRef.current);
        await videoRef.current.playAsync();
      }
    }
    resetControlsTimeout();
  };

  // Seek to position
  const seekTo = async (positionMillis: number) => {
    if (videoRef.current) {
      await videoRef.current.setPositionAsync(positionMillis);
    }
  };

  // Format time
  const formatTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle screen tap
  const handleScreenTap = () => {
    if (showControls) {
      // Hide controls immediately
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start(() => setShowControls(false));
    } else {
      // Show controls
      resetControlsTimeout();
    }
  };

  // Reset when modal opens and stop audio
  useEffect(() => {
    if (visible) {
      // Stop any playing audio when video opens
      audioManager.stopAll();
      setIsLoading(true);
      setIsPlaying(false);
      setPosition(0);
      setDuration(0);
      resetControlsTimeout();
    }
  }, [visible]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (controlsTimeout.current) {
        clearTimeout(controlsTimeout.current);
      }
    };
  }, []);

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <StatusBar backgroundColor="black" barStyle="light-content" />
      <View style={styles.container}>
        {/* Video */}
        <TouchableOpacity
          activeOpacity={1}
          onPress={handleScreenTap}
          style={styles.videoContainer}
        >
          <Video
            ref={videoRef}
            source={{ uri: videoUri }}
            style={styles.video}
            resizeMode={ResizeMode.CONTAIN}
            shouldPlay={false}
            isLooping={true}
            onPlaybackStatusUpdate={onPlaybackStatusUpdate}
          />

          {/* Loading Indicator */}
          {isLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="white" />
              <Text style={styles.loadingText}>Loading video...</Text>
            </View>
          )}

          {/* Play Button Overlay (when paused) */}
          {!isPlaying && !isLoading && (
            <TouchableOpacity
              style={styles.playButtonOverlay}
              onPress={togglePlayPause}
            >
              <View style={styles.playButton}>
                <Ionicons name="play" size={60} color="white" />
              </View>
            </TouchableOpacity>
          )}
        </TouchableOpacity>

        {/* Controls */}
        {showControls && (
          <Animated.View style={[styles.controlsContainer, { opacity: fadeAnim }]}>
            {/* Caption */}
            {caption && (
              <View style={styles.captionContainer}>
                <Text style={styles.captionText}>{caption}</Text>
              </View>
            )}

            {/* Bottom Controls */}
            <View style={styles.bottomControls}>
              {/* Progress Bar */}
              <View style={styles.progressContainer}>
                <Text style={styles.timeText}>{formatTime(position)}</Text>
                <View style={styles.progressBar}>
                  <View style={styles.progressTrack} />
                  <View
                    style={[
                      styles.progressFill,
                      {
                        width: duration > 0 ? `${(position / duration) * 100}%` : '0%',
                      },
                    ]}
                  />
                  <TouchableOpacity
                    style={[
                      styles.progressThumb,
                      {
                        left: duration > 0 ? `${(position / duration) * 100}%` : '0%',
                      },
                    ]}
                    onPress={() => {
                      // Handle seek - you can implement slider functionality here
                    }}
                  />
                </View>
                <Text style={styles.timeText}>{formatTime(duration)}</Text>
              </View>

              {/* Play/Pause Button */}
              <TouchableOpacity
                style={styles.controlButton}
                onPress={togglePlayPause}
              >
                <Ionicons
                  name={isPlaying ? 'pause' : 'play'}
                  size={32}
                  color="white"
                />
              </TouchableOpacity>
            </View>
          </Animated.View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: screenWidth,
    height: screenHeight,
  },
  loadingContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    marginTop: 16,
    fontSize: 16,
  },
  playButtonOverlay: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  captionContainer: {
    position: 'absolute',
    top: 50,
    left: 16,
    right: 16,
    alignItems: 'center',
  },
  captionText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  bottomControls: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 32,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  timeText: {
    color: 'white',
    fontSize: 14,
    minWidth: 50,
    textAlign: 'center',
  },
  progressBar: {
    flex: 1,
    height: 4,
    marginHorizontal: 12,
    position: 'relative',
  },
  progressTrack: {
    position: 'absolute',
    width: '100%',
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressFill: {
    position: 'absolute',
    height: 4,
    backgroundColor: 'white',
    borderRadius: 2,
  },
  progressThumb: {
    position: 'absolute',
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'white',
    marginTop: -6,
    marginLeft: -8,
  },
  controlButton: {
    alignSelf: 'center',
    padding: 8,
  },
});

export default VideoPlayer;
