import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
  StyleSheet,
  ActivityIndicator,
  Switch,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import { IRACHAT_COLORS, TYPOGRAPHY, BORDER_RADIUS } from '../../styles/iraChatDesignSystem';
import { ResponsiveTypography, ResponsiveSpacing } from '../../utils/responsiveUtils';
import { EnhancedChatSearch, SearchFilters } from '../ChatList/EnhancedChatSearch';
import { localChatManagementService, LocalSearchResult, LocalChatData } from '../../services/localChatManagementService';

interface ChatManagementAction {
  id: string;
  title: string;
  icon: string;
  color: string;
  destructive?: boolean;
  requiresSelection?: boolean;
}

export const ComprehensiveChatManager: React.FC = () => {
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  
  // State
  const [chats, setChats] = useState<LocalChatData[]>([]);
  const [filteredChats, setFilteredChats] = useState<LocalChatData[]>([]);
  const [selectedChats, setSelectedChats] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<LocalSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showActions, setShowActions] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  
  // Search filters
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    searchType: 'all',
    chatType: 'all',
    timeRange: 'all',
    messageType: 'all',
    sortBy: 'relevance',
    includeArchived: false,
    includeMuted: true,
    includeHidden: false,
    includeDeleted: false,
  });

  // Filter options
  const [showArchived, setShowArchived] = useState(false);
  const [showMuted, setShowMuted] = useState(true);
  const [showPinned, setShowPinned] = useState(true);
  const [showLocked, setShowLocked] = useState(false);
  const [showHidden, setShowHidden] = useState(false);

  // Load chats from local storage
  const loadChats = useCallback(async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    try {
      await localChatManagementService.initialize();

      const localChats = await localChatManagementService.getFilteredChats({
        includeArchived: showArchived,
        includeMuted: showMuted,
        includeHidden: showHidden,
        includeDeleted: false, // Don't show deleted by default
        includeLocked: showLocked,
        sortBy: 'date',
      });

      setChats(localChats);
      applyFilters(localChats);
    } catch (error) {
      console.error('Error loading chats:', error);
      Alert.alert('Error', 'Failed to load chats');
    } finally {
      setLoading(false);
    }
  }, [currentUser?.id, showArchived, showMuted, showHidden, showLocked]);

  useEffect(() => {
    loadChats();
  }, [loadChats]);

  // Apply filters to chats
  const applyFilters = useCallback((chatList: LocalChatData[]) => {
    let filtered = chatList.filter(chat => {
      if (!showArchived && chat.isArchived) return false;
      if (!showMuted && chat.isMuted) return false;
      if (!showPinned && chat.isPinned) return false;
      if (!showLocked && chat.isLocked) return false;
      if (!showHidden && chat.isHidden) return false;
      return true;
    });

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(chat =>
        (chat.customName || chat.name).toLowerCase().includes(query) ||
        chat.lastMessage.toLowerCase().includes(query)
      );
    }

    setFilteredChats(filtered);
  }, [searchQuery, showArchived, showMuted, showPinned, showLocked, showHidden]);

  useEffect(() => {
    applyFilters(chats);
  }, [chats, applyFilters]);

  // Search functionality - now uses local search
  const handleSearch = useCallback(async (query: string) => {
    setSearchQuery(query);

    if (query.trim()) {
      setIsSearching(true);
      try {
        const results = await localChatManagementService.searchLocal(query, searchFilters);
        setSearchResults(results);
      } catch (error) {
        console.error('Local search error:', error);
      } finally {
        setIsSearching(false);
      }
    } else {
      setSearchResults([]);
    }
  }, [searchFilters]);

  const handleSearchResults = useCallback((results: LocalSearchResult[]) => {
    setSearchResults(results);
  }, []);

  // Selection management
  const toggleChatSelection = useCallback((chatId: string) => {
    const newSelected = new Set(selectedChats);
    if (newSelected.has(chatId)) {
      newSelected.delete(chatId);
    } else {
      newSelected.add(chatId);
    }
    setSelectedChats(newSelected);
    setIsSelectionMode(newSelected.size > 0);
  }, [selectedChats]);

  const selectAllChats = useCallback(() => {
    const allIds = new Set(filteredChats.map(chat => chat.id));
    setSelectedChats(allIds);
    setIsSelectionMode(true);
  }, [filteredChats]);

  const clearSelection = useCallback(() => {
    setSelectedChats(new Set());
    setIsSelectionMode(false);
  }, []);

  // Chat actions
  const chatActions: ChatManagementAction[] = [
    {
      id: 'archive',
      title: 'Archive',
      icon: 'archive',
      color: IRACHAT_COLORS.warning,
      requiresSelection: true,
    },
    {
      id: 'unarchive',
      title: 'Unarchive',
      icon: 'unarchive',
      color: IRACHAT_COLORS.success,
      requiresSelection: true,
    },
    {
      id: 'mute',
      title: 'Mute',
      icon: 'notifications-off',
      color: IRACHAT_COLORS.textMuted,
      requiresSelection: true,
    },
    {
      id: 'unmute',
      title: 'Unmute',
      icon: 'notifications',
      color: IRACHAT_COLORS.primary,
      requiresSelection: true,
    },
    {
      id: 'pin',
      title: 'Pin',
      icon: 'pin',
      color: IRACHAT_COLORS.accent,
      requiresSelection: true,
    },
    {
      id: 'unpin',
      title: 'Unpin',
      icon: 'pin-outline',
      color: IRACHAT_COLORS.textSecondary,
      requiresSelection: true,
    },
    {
      id: 'lock',
      title: 'Lock',
      icon: 'lock-closed',
      color: IRACHAT_COLORS.error,
      requiresSelection: true,
    },
    {
      id: 'unlock',
      title: 'Unlock',
      icon: 'lock-open',
      color: IRACHAT_COLORS.success,
      requiresSelection: true,
    },
    {
      id: 'hide',
      title: 'Hide',
      icon: 'eye-off',
      color: IRACHAT_COLORS.textMuted,
      requiresSelection: true,
    },
    {
      id: 'unhide',
      title: 'Unhide',
      icon: 'eye',
      color: IRACHAT_COLORS.primary,
      requiresSelection: true,
    },
    {
      id: 'clear',
      title: 'Clear History',
      icon: 'trash-outline',
      color: IRACHAT_COLORS.warning,
      requiresSelection: true,
    },
    {
      id: 'delete',
      title: 'Delete',
      icon: 'trash',
      color: IRACHAT_COLORS.error,
      destructive: true,
      requiresSelection: true,
    },
  ];

  const handleChatAction = useCallback(async (actionId: string) => {
    const selectedChatIds = Array.from(selectedChats);
    if (selectedChatIds.length === 0) {
      Alert.alert('No Selection', 'Please select chats to perform this action');
      return;
    }

    try {
      switch (actionId) {
        case 'archive':
          await localChatManagementService.archiveChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) archived locally`);
          break;

        case 'unarchive':
          await localChatManagementService.unarchiveChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) unarchived locally`);
          break;

        case 'mute':
          await localChatManagementService.muteChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) muted locally`);
          break;

        case 'unmute':
          await localChatManagementService.unmuteChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) unmuted locally`);
          break;

        case 'pin':
          await localChatManagementService.pinChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) pinned locally`);
          break;

        case 'unpin':
          await localChatManagementService.unpinChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) unpinned locally`);
          break;

        case 'hide':
          await localChatManagementService.hideChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) hidden locally`);
          break;

        case 'unhide':
          await localChatManagementService.unhideChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) unhidden locally`);
          break;

        case 'lock':
          Alert.prompt(
            'Lock Chats',
            'Enter a PIN to lock these chats:',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Lock',
                onPress: async (pin) => {
                  if (pin && pin.length >= 4) {
                    await localChatManagementService.lockChats(selectedChatIds, pin);
                    Alert.alert('Success', `${selectedChatIds.length} chat(s) locked locally`);
                    clearSelection();
                    loadChats();
                  } else {
                    Alert.alert('Error', 'PIN must be at least 4 characters');
                  }
                }
              }
            ],
            'secure-text'
          );
          return;

        case 'unlock':
          await localChatManagementService.unlockChats(selectedChatIds);
          Alert.alert('Success', `${selectedChatIds.length} chat(s) unlocked locally`);
          break;

        case 'clear':
          Alert.alert(
            'Clear Chat History',
            'What would you like to clear?',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Messages Only',
                onPress: async () => {
                  await localChatManagementService.clearChatHistory(selectedChatIds, 'messages');
                  Alert.alert('Success', 'Messages cleared locally');
                  clearSelection();
                  loadChats();
                }
              },
              {
                text: 'Media Only',
                onPress: async () => {
                  await localChatManagementService.clearChatHistory(selectedChatIds, 'media');
                  Alert.alert('Success', 'Media cleared locally');
                  clearSelection();
                  loadChats();
                }
              },
              {
                text: 'Everything',
                style: 'destructive',
                onPress: async () => {
                  await localChatManagementService.clearChatHistory(selectedChatIds, 'all');
                  Alert.alert('Success', 'Chat history cleared locally');
                  clearSelection();
                  loadChats();
                }
              }
            ]
          );
          return;

        case 'delete':
          Alert.alert(
            'Delete Chats',
            `Are you sure you want to delete ${selectedChatIds.length} chat(s)? This will hide them locally but they can be restored.`,
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Delete',
                style: 'destructive',
                onPress: async () => {
                  await localChatManagementService.deleteChats(selectedChatIds);
                  Alert.alert('Success', `${selectedChatIds.length} chat(s) deleted locally`);
                  clearSelection();
                  loadChats();
                }
              }
            ]
          );
          return;

        default:
          Alert.alert('Action Complete', `${actionId} performed locally`);
      }

      clearSelection();
      loadChats();
    } catch (error) {
      console.error(`Error performing ${actionId}:`, error);
      Alert.alert('Error', `Failed to ${actionId} chats locally`);
    }
  }, [selectedChats, clearSelection, loadChats]);

  const renderChatItem = ({ item }: { item: LocalChatData }) => (
    <TouchableOpacity
      style={[styles.chatItem, selectedChats.has(item.id) && styles.chatItemSelected]}
      onPress={() => toggleChatSelection(item.id)}
      onLongPress={() => toggleChatSelection(item.id)}
    >
      <View style={styles.chatItemContent}>
        <View style={styles.chatAvatar}>
          {item.avatar ? (
            <Text>📷</Text> // Placeholder for avatar
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarText}>
                {item.name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.chatInfo}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatName} numberOfLines={1}>
              {item.name}
            </Text>
            <View style={styles.chatBadges}>
              {item.isPinned && <Ionicons name="pin" size={12} color={IRACHAT_COLORS.accent} />}
              {item.isMuted && <Ionicons name="notifications-off" size={12} color={IRACHAT_COLORS.textMuted} />}
              {item.isLocked && <Ionicons name="lock-closed" size={12} color={IRACHAT_COLORS.error} />}
              {item.isArchived && <Ionicons name="archive" size={12} color={IRACHAT_COLORS.warning} />}
            </View>
          </View>
          
          <Text style={styles.lastMessage} numberOfLines={1}>
            {item.lastMessage}
          </Text>
          
          <View style={styles.chatStats}>
            <Text style={styles.statText}>
              {item.messageCount} messages • {item.mediaCount} media
            </Text>
            <Text style={styles.timestamp}>
              {item.timestamp.toLocaleDateString()}
            </Text>
          </View>
        </View>

        {selectedChats.has(item.id) && (
          <View style={styles.selectionIndicator}>
            <Ionicons name="checkmark-circle" size={24} color={IRACHAT_COLORS.primary} />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderActionButton = (action: ChatManagementAction) => (
    <TouchableOpacity
      key={action.id}
      style={[styles.actionButton, { backgroundColor: action.color }]}
      onPress={() => handleChatAction(action.id)}
      disabled={action.requiresSelection && selectedChats.size === 0}
    >
      <Ionicons name={action.icon as any} size={20} color={IRACHAT_COLORS.textOnPrimary} />
      <Text style={styles.actionButtonText}>{action.title}</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
        <Text style={styles.loadingText}>Loading chats...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Enhanced Search */}
      <EnhancedChatSearch
        searchQuery={searchQuery}
        onSearchChange={handleSearch}
        onClearSearch={() => handleSearch('')}
        filters={searchFilters}
        onFiltersChange={setSearchFilters}
        onSearchResults={handleSearchResults}
        placeholder="Search chats, messages, contacts..."
        showAdvancedOptions={true}
        enableOfflineSearch={true}
      />

      {/* Search Results */}
      {searchResults.length > 0 && (
        <View style={styles.searchResultsContainer}>
          <Text style={styles.searchResultsTitle}>
            Search Results ({searchResults.length})
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            {searchResults.map((result, index) => (
              <TouchableOpacity
                key={`${result.type}-${result.id}-${index}`}
                style={styles.searchResultItem}
                onPress={() => {
                  if (result.type === 'chat' && result.chatId) {
                    toggleChatSelection(result.chatId);
                  }
                }}
              >
                <Text style={styles.searchResultType}>{result.type}</Text>
                <Text style={styles.searchResultTitle} numberOfLines={1}>
                  {result.title}
                </Text>
                {result.subtitle && (
                  <Text style={styles.searchResultSubtitle} numberOfLines={1}>
                    {result.subtitle}
                  </Text>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      {/* Filter Controls */}
      <View style={styles.filterControls}>
        <Text style={styles.filterTitle}>Show:</Text>
        <View style={styles.filterRow}>
          <View style={styles.filterItem}>
            <Text style={styles.filterLabel}>Archived</Text>
            <Switch
              value={showArchived}
              onValueChange={setShowArchived}
              trackColor={{ false: IRACHAT_COLORS.borderLight, true: IRACHAT_COLORS.primary }}
            />
          </View>
          <View style={styles.filterItem}>
            <Text style={styles.filterLabel}>Muted</Text>
            <Switch
              value={showMuted}
              onValueChange={setShowMuted}
              trackColor={{ false: IRACHAT_COLORS.borderLight, true: IRACHAT_COLORS.primary }}
            />
          </View>
          <View style={styles.filterItem}>
            <Text style={styles.filterLabel}>Locked</Text>
            <Switch
              value={showLocked}
              onValueChange={setShowLocked}
              trackColor={{ false: IRACHAT_COLORS.borderLight, true: IRACHAT_COLORS.primary }}
            />
          </View>
        </View>
      </View>

      {/* Selection Controls */}
      {isSelectionMode && (
        <View style={styles.selectionControls}>
          <Text style={styles.selectionText}>
            {selectedChats.size} selected
          </Text>
          <View style={styles.selectionButtons}>
            <TouchableOpacity onPress={selectAllChats} style={styles.selectionButton}>
              <Text style={styles.selectionButtonText}>Select All</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={clearSelection} style={styles.selectionButton}>
              <Text style={styles.selectionButtonText}>Clear</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Chat List */}
      <FlatList
        data={filteredChats}
        renderItem={renderChatItem}
        keyExtractor={(item) => item.id}
        style={styles.chatList}
        contentContainerStyle={styles.chatListContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Ionicons name="chatbubbles-outline" size={64} color={IRACHAT_COLORS.textMuted} />
            <Text style={styles.emptyText}>No chats found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? 'Try adjusting your search' : 'Start a new conversation'}
            </Text>
          </View>
        )}
      />

      {/* Action Buttons */}
      {selectedChats.size > 0 && (
        <View style={styles.actionBar}>
          <TouchableOpacity
            style={styles.moreActionsButton}
            onPress={() => setShowActions(true)}
          >
            <Ionicons name="ellipsis-horizontal" size={24} color={IRACHAT_COLORS.textOnPrimary} />
            <Text style={styles.moreActionsText}>Actions</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Actions Modal */}
      <Modal
        visible={showActions}
        transparent
        animationType="slide"
        onRequestClose={() => setShowActions(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.actionsModal}>
            <View style={styles.actionsHeader}>
              <Text style={styles.actionsTitle}>Chat Actions</Text>
              <TouchableOpacity onPress={() => setShowActions(false)}>
                <Ionicons name="close" size={24} color={IRACHAT_COLORS.text} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.actionsGrid}>
              {chatActions.map(renderActionButton)}
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: ResponsiveSpacing.md,
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  filterControls: {
    padding: ResponsiveSpacing.md,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  filterTitle: {
    fontSize: ResponsiveTypography.fontSize.sm,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    marginBottom: ResponsiveSpacing.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  filterRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  filterItem: {
    alignItems: 'center',
  },
  filterLabel: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textSecondary,
    marginBottom: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  selectionControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: ResponsiveSpacing.md,
    backgroundColor: IRACHAT_COLORS.primaryLight,
  },
  selectionText: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  selectionButtons: {
    flexDirection: 'row',
  },
  selectionButton: {
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    marginLeft: ResponsiveSpacing.sm,
  },
  selectionButtonText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.primary,
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  chatList: {
    flex: 1,
  },
  chatListContent: {
    paddingBottom: ResponsiveSpacing.xl,
  },
  chatItem: {
    backgroundColor: IRACHAT_COLORS.surface,
    marginHorizontal: ResponsiveSpacing.md,
    marginVertical: ResponsiveSpacing.xs,
    borderRadius: BORDER_RADIUS.lg,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.borderLight,
  },
  chatItemSelected: {
    borderColor: IRACHAT_COLORS.primary,
    backgroundColor: IRACHAT_COLORS.primaryLight,
  },
  chatItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: ResponsiveSpacing.md,
  },
  chatAvatar: {
    marginRight: ResponsiveSpacing.md,
  },
  avatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: IRACHAT_COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    fontSize: ResponsiveTypography.fontSize.lg,
    fontWeight: 'bold',
    color: IRACHAT_COLORS.textOnPrimary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  chatInfo: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.xs,
  },
  chatName: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    flex: 1,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  chatBadges: {
    flexDirection: 'row',
    gap: ResponsiveSpacing.xs,
  },
  lastMessage: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    marginBottom: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  chatStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  timestamp: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  selectionIndicator: {
    marginLeft: ResponsiveSpacing.sm,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.xl * 2,
  },
  emptyText: {
    fontSize: ResponsiveTypography.fontSize.lg,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    marginTop: ResponsiveSpacing.md,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  emptySubtext: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    marginTop: ResponsiveSpacing.sm,
    textAlign: 'center',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  actionBar: {
    backgroundColor: IRACHAT_COLORS.primary,
    padding: ResponsiveSpacing.md,
  },
  moreActionsButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  moreActionsText: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '600',
    color: IRACHAT_COLORS.textOnPrimary,
    marginLeft: ResponsiveSpacing.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.overlay,
    justifyContent: 'flex-end',
  },
  actionsModal: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderTopLeftRadius: BORDER_RADIUS.xl,
    borderTopRightRadius: BORDER_RADIUS.xl,
    maxHeight: '70%',
  },
  actionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: ResponsiveSpacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  actionsTitle: {
    fontSize: ResponsiveTypography.fontSize.xl,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: ResponsiveSpacing.lg,
    gap: ResponsiveSpacing.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    borderRadius: BORDER_RADIUS.lg,
    minWidth: '45%',
  },
  actionButtonText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    fontWeight: '600',
    color: IRACHAT_COLORS.textOnPrimary,
    marginLeft: ResponsiveSpacing.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  searchResultsContainer: {
    backgroundColor: IRACHAT_COLORS.surface,
    padding: ResponsiveSpacing.md,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  searchResultsTitle: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    marginBottom: ResponsiveSpacing.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  searchResultItem: {
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    padding: ResponsiveSpacing.sm,
    borderRadius: BORDER_RADIUS.md,
    marginRight: ResponsiveSpacing.sm,
    minWidth: 120,
    maxWidth: 200,
  },
  searchResultType: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.primary,
    fontWeight: '600',
    textTransform: 'uppercase',
    marginBottom: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  searchResultTitle: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.text,
    fontWeight: '500',
    marginBottom: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  searchResultSubtitle: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
});
