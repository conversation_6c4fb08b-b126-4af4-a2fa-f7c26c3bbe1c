import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  PanResponder,
  Animated,
} from 'react-native';
import { VideoView, useVideoPlayer } from 'expo-video';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth } = Dimensions.get('window');

interface EnhancedVideoTrimmerProps {
  visible: boolean;
  videoUri: string;
  onClose: () => void;
  onTrimComplete: (trimmedUri: string, startTime: number, endTime: number) => void;
  maxDuration?: number;
}

export const EnhancedVideoTrimmer: React.FC<EnhancedVideoTrimmerProps> = ({
  visible,
  videoUri,
  onClose,
  onTrimComplete,
  maxDuration = 180, // 3 minutes max
}) => {
  const player = useVideoPlayer(videoUri, (player) => {
    player.loop = false;
    player.muted = false;
  });
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [startTime, setStartTime] = useState(0);
  const [endTime, setEndTime] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);

  const trimmerWidth = screenWidth - 40;
  const handleWidth = 20;

  // Animated values for smooth handle movement
  const startHandleAnim = useRef(new Animated.Value(0)).current;
  const endHandleAnim = useRef(new Animated.Value(trimmerWidth - handleWidth)).current;

  useEffect(() => {
    if (duration > 0 && endTime === 0) {
      const maxEnd = Math.min(duration, maxDuration);
      setEndTime(maxEnd);

      // Animate end handle to correct position
      const endPosition = (maxEnd / duration) * trimmerWidth - handleWidth;
      Animated.timing(endHandleAnim, {
        toValue: endPosition,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  }, [duration, maxDuration, endTime, trimmerWidth, handleWidth, endHandleAnim]);

  useEffect(() => {
    if (!player) return;

    const subscription = player.addListener('statusChange', (status) => {
      if (status.status === 'readyToPlay' && !isLoaded) {
        setIsLoaded(true);
        setDuration(player.duration || 0);
      }

      setCurrentTime(player.currentTime || 0);
      setIsPlaying(player.playing || false);

      // Auto-pause when reaching end time
      if ((player.currentTime || 0) >= endTime && player.playing) {
        player.pause();
      }
    });

    return () => {
      subscription?.remove();
    };
  }, [player, endTime, isLoaded]);

  const togglePlayPause = async () => {
    if (!player) return;

    if (isPlaying) {
      player.pause();
    } else {
      // If at end, restart from start time
      if (currentTime >= endTime) {
        player.currentTime = startTime;
      }
      player.play();
    }
  };

  const seekToPosition = async (time: number) => {
    if (player) {
      player.currentTime = time;
    }
  };

  // Create pan responder for start handle
  const startHandlePanResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      startHandleAnim.stopAnimation();
    },
    onPanResponderMove: (_, gestureState) => {
      const newPosition = Math.max(0, Math.min(
        gestureState.moveX - 20, // Adjust for container padding
        (endTime / duration) * trimmerWidth - handleWidth - 20 // Leave space before end handle
      ));
      
      const newStartTime = (newPosition / trimmerWidth) * duration;
      setStartTime(newStartTime);
      startHandleAnim.setValue(newPosition);
      seekToPosition(newStartTime);
    },
    onPanResponderRelease: () => {
      // Optional: Add spring animation for smooth release
    },
  });

  // Create pan responder for end handle
  const endHandlePanResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      endHandleAnim.stopAnimation();
    },
    onPanResponderMove: (_, gestureState) => {
      const newPosition = Math.min(trimmerWidth - handleWidth, Math.max(
        gestureState.moveX - 20, // Adjust for container padding
        (startTime / duration) * trimmerWidth + 20 // Leave space after start handle
      ));
      
      const newEndTime = Math.min(duration, ((newPosition + handleWidth) / trimmerWidth) * duration);
      setEndTime(newEndTime);
      endHandleAnim.setValue(newPosition);
    },
    onPanResponderRelease: () => {
      // Optional: Add spring animation for smooth release
    },
  });

  const handleTrimComplete = () => {
    // In a real implementation, this would return the actual trimmed video URI
    onTrimComplete(videoUri, startTime, endTime);
    onClose();
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const resetTrim = () => {
    setStartTime(0);
    const maxEnd = Math.min(duration, maxDuration);
    setEndTime(maxEnd);
    
    // Animate handles to reset positions
    Animated.parallel([
      Animated.timing(startHandleAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(endHandleAnim, {
        toValue: (maxEnd / duration) * trimmerWidth - handleWidth,
        duration: 300,
        useNativeDriver: false,
      }),
    ]).start();
    
    seekToPosition(0);
  };

  const startPosition = (startTime / duration) * trimmerWidth;
  const endPosition = (endTime / duration) * trimmerWidth;
  const currentPosition = (currentTime / duration) * trimmerWidth;
  const selectedWidth = endPosition - startPosition;

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.headerButton}>
          <Ionicons name="close" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Trim Video</Text>
        <TouchableOpacity onPress={resetTrim} style={styles.headerButton}>
          <Ionicons name="refresh" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.videoContainer}>
        <VideoView
          style={styles.video}
          player={player}
          allowsFullscreen={false}
          allowsPictureInPicture={false}
          contentFit="contain"
          nativeControls={false}
        />
        
        <TouchableOpacity style={styles.playButton} onPress={togglePlayPause}>
          <Ionicons 
            name={isPlaying ? "pause" : "play"} 
            size={40} 
            color="white" 
          />
        </TouchableOpacity>
      </View>

      <View style={styles.controls}>
        <View style={styles.timeInfo}>
          <Text style={styles.timeText}>
            Start: {formatTime(startTime)}
          </Text>
          <Text style={styles.durationText}>
            Duration: {formatTime(endTime - startTime)}
          </Text>
          <Text style={styles.timeText}>
            End: {formatTime(endTime)}
          </Text>
        </View>
        
        <View style={styles.trimmerContainer}>
          <View style={styles.trimmerTrack}>
            {/* Unselected areas */}
            <View style={[styles.unselectedArea, { width: startPosition }]} />
            <View style={[
              styles.unselectedArea, 
              { 
                left: endPosition,
                width: trimmerWidth - endPosition 
              }
            ]} />
            
            {/* Selected range */}
            <View 
              style={[
                styles.selectedRange,
                {
                  left: startPosition,
                  width: selectedWidth,
                }
              ]} 
            />
            
            {/* Current time indicator */}
            <View 
              style={[
                styles.currentTimeIndicator,
                { left: Math.max(startPosition, Math.min(endPosition, currentPosition)) }
              ]} 
            />
            
            {/* Start handle */}
            <Animated.View 
              style={[
                styles.trimHandle,
                styles.startHandle,
                { left: startHandleAnim }
              ]}
              {...startHandlePanResponder.panHandlers}
            >
              <View style={styles.handleGrip} />
              <Text style={styles.handleLabel}>START</Text>
            </Animated.View>
            
            {/* End handle */}
            <Animated.View 
              style={[
                styles.trimHandle,
                styles.endHandle,
                { left: endHandleAnim }
              ]}
              {...endHandlePanResponder.panHandlers}
            >
              <View style={styles.handleGrip} />
              <Text style={styles.handleLabel}>END</Text>
            </Animated.View>
          </View>
        </View>

        <Text style={styles.instruction}>
          Drag the START and END handles to trim your video
        </Text>

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.doneButton} onPress={handleTrimComplete}>
            <Text style={styles.doneButtonText}>Trim Video</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  videoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  playButton: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controls: {
    backgroundColor: '#1F2937',
    padding: 20,
    paddingBottom: 40,
  },
  timeInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  timeText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  durationText: {
    color: '#87CEEB',
    fontSize: 16,
    fontWeight: '600',
  },
  trimmerContainer: {
    marginBottom: 20,
  },
  trimmerTrack: {
    height: 60,
    backgroundColor: '#374151',
    borderRadius: 8,
    position: 'relative',
    overflow: 'hidden',
  },
  unselectedArea: {
    position: 'absolute',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    top: 0,
  },
  selectedRange: {
    position: 'absolute',
    height: '100%',
    backgroundColor: '#87CEEB',
    opacity: 0.3,
  },
  currentTimeIndicator: {
    position: 'absolute',
    width: 3,
    height: '100%',
    backgroundColor: '#EF4444',
    zIndex: 10,
  },
  trimHandle: {
    position: 'absolute',
    width: 20,
    height: 60,
    backgroundColor: '#87CEEB',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 20,
  },
  startHandle: {
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
  endHandle: {
    borderTopLeftRadius: 8,
    borderBottomLeftRadius: 8,
  },
  handleGrip: {
    width: 4,
    height: 20,
    backgroundColor: 'white',
    borderRadius: 2,
    marginBottom: 4,
  },
  handleLabel: {
    color: 'white',
    fontSize: 8,
    fontWeight: '600',
    transform: [{ rotate: '90deg' }],
  },
  instruction: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 16,
    backgroundColor: '#374151',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  doneButton: {
    flex: 1,
    paddingVertical: 16,
    backgroundColor: '#87CEEB',
    borderRadius: 8,
    alignItems: 'center',
  },
  doneButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
