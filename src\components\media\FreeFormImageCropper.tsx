import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
  Alert,
  PanResponder,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImageManipulator from 'expo-image-manipulator';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface FreeFormImageCropperProps {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
  onCropComplete: (croppedUri: string) => void;
}

export const FreeFormImageCropper: React.FC<FreeFormImageCropperProps> = ({
  visible,
  imageUri,
  onClose,
  onCropComplete,
}) => {
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [isProcessing, setIsProcessing] = useState(false);
  const [cropArea, setCropArea] = useState({
    x: 50,
    y: 100,
    width: SCREEN_WIDTH - 100,
    height: 300,
  });

  // Animation values for crop area
  const cropAreaAnim = useRef({
    x: new Animated.Value(50),
    y: new Animated.Value(100),
    width: new Animated.Value(SCREEN_WIDTH - 100),
    height: new Animated.Value(300),
  }).current;

  // Handle size for resize handles
  const handleSize = 20;

  // Pan responders for different handles
  const createPanResponder = (type: 'move' | 'resize-tl' | 'resize-tr' | 'resize-bl' | 'resize-br' | 'resize-t' | 'resize-b' | 'resize-l' | 'resize-r') => {
    return PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: () => {
        // Stop any ongoing animations
        Object.values(cropAreaAnim).forEach(anim => anim.stopAnimation());
      },
      onPanResponderMove: (_, gestureState) => {
        const { dx, dy } = gestureState;
        
        switch (type) {
          case 'move':
            // Move entire crop area
            const newX = Math.max(0, Math.min(SCREEN_WIDTH - cropArea.width, cropArea.x + dx));
            const newY = Math.max(0, Math.min(imageSize.height - cropArea.height, cropArea.y + dy));
            setCropArea(prev => ({ ...prev, x: newX, y: newY }));
            cropAreaAnim.x.setValue(newX);
            cropAreaAnim.y.setValue(newY);
            break;
            
          case 'resize-br':
            // Bottom-right corner resize
            const newWidth = Math.max(100, Math.min(SCREEN_WIDTH - cropArea.x, cropArea.width + dx));
            const newHeight = Math.max(100, Math.min(imageSize.height - cropArea.y, cropArea.height + dy));
            setCropArea(prev => ({ ...prev, width: newWidth, height: newHeight }));
            cropAreaAnim.width.setValue(newWidth);
            cropAreaAnim.height.setValue(newHeight);
            break;
            
          case 'resize-tl':
            // Top-left corner resize
            const tlNewX = Math.max(0, Math.min(cropArea.x + cropArea.width - 100, cropArea.x + dx));
            const tlNewY = Math.max(0, Math.min(cropArea.y + cropArea.height - 100, cropArea.y + dy));
            const tlNewWidth = cropArea.width - (tlNewX - cropArea.x);
            const tlNewHeight = cropArea.height - (tlNewY - cropArea.y);
            setCropArea({ x: tlNewX, y: tlNewY, width: tlNewWidth, height: tlNewHeight });
            cropAreaAnim.x.setValue(tlNewX);
            cropAreaAnim.y.setValue(tlNewY);
            cropAreaAnim.width.setValue(tlNewWidth);
            cropAreaAnim.height.setValue(tlNewHeight);
            break;
            
          case 'resize-tr':
            // Top-right corner resize
            const trNewY = Math.max(0, Math.min(cropArea.y + cropArea.height - 100, cropArea.y + dy));
            const trNewWidth = Math.max(100, Math.min(SCREEN_WIDTH - cropArea.x, cropArea.width + dx));
            const trNewHeight = cropArea.height - (trNewY - cropArea.y);
            setCropArea(prev => ({ ...prev, y: trNewY, width: trNewWidth, height: trNewHeight }));
            cropAreaAnim.y.setValue(trNewY);
            cropAreaAnim.width.setValue(trNewWidth);
            cropAreaAnim.height.setValue(trNewHeight);
            break;
            
          case 'resize-bl':
            // Bottom-left corner resize
            const blNewX = Math.max(0, Math.min(cropArea.x + cropArea.width - 100, cropArea.x + dx));
            const blNewWidth = cropArea.width - (blNewX - cropArea.x);
            const blNewHeight = Math.max(100, Math.min(imageSize.height - cropArea.y, cropArea.height + dy));
            setCropArea(prev => ({ ...prev, x: blNewX, width: blNewWidth, height: blNewHeight }));
            cropAreaAnim.x.setValue(blNewX);
            cropAreaAnim.width.setValue(blNewWidth);
            cropAreaAnim.height.setValue(blNewHeight);
            break;
            
          case 'resize-t':
            // Top edge resize
            const tNewY = Math.max(0, Math.min(cropArea.y + cropArea.height - 100, cropArea.y + dy));
            const tNewHeight = cropArea.height - (tNewY - cropArea.y);
            setCropArea(prev => ({ ...prev, y: tNewY, height: tNewHeight }));
            cropAreaAnim.y.setValue(tNewY);
            cropAreaAnim.height.setValue(tNewHeight);
            break;
            
          case 'resize-b':
            // Bottom edge resize
            const bNewHeight = Math.max(100, Math.min(imageSize.height - cropArea.y, cropArea.height + dy));
            setCropArea(prev => ({ ...prev, height: bNewHeight }));
            cropAreaAnim.height.setValue(bNewHeight);
            break;
            
          case 'resize-l':
            // Left edge resize
            const lNewX = Math.max(0, Math.min(cropArea.x + cropArea.width - 100, cropArea.x + dx));
            const lNewWidth = cropArea.width - (lNewX - cropArea.x);
            setCropArea(prev => ({ ...prev, x: lNewX, width: lNewWidth }));
            cropAreaAnim.x.setValue(lNewX);
            cropAreaAnim.width.setValue(lNewWidth);
            break;
            
          case 'resize-r':
            // Right edge resize
            const rNewWidth = Math.max(100, Math.min(SCREEN_WIDTH - cropArea.x, cropArea.width + dx));
            setCropArea(prev => ({ ...prev, width: rNewWidth }));
            cropAreaAnim.width.setValue(rNewWidth);
            break;
        }
      },
      onPanResponderRelease: () => {
        // Optional: Add spring animation for smooth release
      },
    });
  };

  const movePanResponder = createPanResponder('move');
  const resizeBRPanResponder = createPanResponder('resize-br');
  const resizeTLPanResponder = createPanResponder('resize-tl');
  const resizeTRPanResponder = createPanResponder('resize-tr');
  const resizeBLPanResponder = createPanResponder('resize-bl');
  const resizeTPanResponder = createPanResponder('resize-t');
  const resizeBPanResponder = createPanResponder('resize-b');
  const resizeLPanResponder = createPanResponder('resize-l');
  const resizeRPanResponder = createPanResponder('resize-r');

  const handleImageLoad = () => {
    Image.getSize(imageUri, (width, height) => {
      const aspectRatio = width / height;
      const displayWidth = SCREEN_WIDTH;
      const displayHeight = displayWidth / aspectRatio;
      
      setImageSize({ width: displayWidth, height: displayHeight });
      
      // Reset crop area to center of image
      const initialCropWidth = Math.min(displayWidth * 0.8, 300);
      const initialCropHeight = Math.min(displayHeight * 0.6, 300);
      const initialX = (displayWidth - initialCropWidth) / 2;
      const initialY = (displayHeight - initialCropHeight) / 2;
      
      setCropArea({
        x: initialX,
        y: initialY,
        width: initialCropWidth,
        height: initialCropHeight,
      });
      
      cropAreaAnim.x.setValue(initialX);
      cropAreaAnim.y.setValue(initialY);
      cropAreaAnim.width.setValue(initialCropWidth);
      cropAreaAnim.height.setValue(initialCropHeight);
    });
  };

  const handleCrop = async () => {
    if (!imageSize.width || !imageSize.height) return;
    
    setIsProcessing(true);
    
    try {
      // Get original image dimensions
      Image.getSize(imageUri, async (originalWidth, originalHeight) => {
        // Calculate scale factors
        const scaleX = originalWidth / imageSize.width;
        const scaleY = originalHeight / imageSize.height;
        
        const cropParams = {
          originX: cropArea.x * scaleX,
          originY: cropArea.y * scaleY,
          width: cropArea.width * scaleX,
          height: cropArea.height * scaleY,
        };
        
        const manipResult = await ImageManipulator.manipulateAsync(
          imageUri,
          [{ crop: cropParams }],
          { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
        );
        
        onCropComplete(manipResult.uri);
        onClose();
      });
    } catch (error) {
      console.error('Crop failed:', error);
      Alert.alert('Error', 'Failed to crop image. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const resetCrop = () => {
    if (imageSize.width && imageSize.height) {
      const initialCropWidth = Math.min(imageSize.width * 0.8, 300);
      const initialCropHeight = Math.min(imageSize.height * 0.6, 300);
      const initialX = (imageSize.width - initialCropWidth) / 2;
      const initialY = (imageSize.height - initialCropHeight) / 2;
      
      setCropArea({
        x: initialX,
        y: initialY,
        width: initialCropWidth,
        height: initialCropHeight,
      });
      
      Animated.parallel([
        Animated.spring(cropAreaAnim.x, { toValue: initialX, useNativeDriver: false }),
        Animated.spring(cropAreaAnim.y, { toValue: initialY, useNativeDriver: false }),
        Animated.spring(cropAreaAnim.width, { toValue: initialCropWidth, useNativeDriver: false }),
        Animated.spring(cropAreaAnim.height, { toValue: initialCropHeight, useNativeDriver: false }),
      ]).start();
    }
  };

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.headerButton}>
          <Ionicons name="close" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Crop Photo</Text>
        <TouchableOpacity onPress={resetCrop} style={styles.headerButton}>
          <Ionicons name="refresh" size={24} color="white" />
        </TouchableOpacity>
      </View>

      <View style={styles.imageContainer}>
        <Image
          source={{ uri: imageUri }}
          style={[styles.image, { width: imageSize.width, height: imageSize.height }]}
          resizeMode="contain"
          onLoad={handleImageLoad}
        />
        
        {/* Crop overlay */}
        {imageSize.width > 0 && (
          <>
            {/* Dark overlay */}
            <View style={[styles.overlay, { width: imageSize.width, height: imageSize.height }]} />
            
            {/* Crop area */}
            <Animated.View
              style={[
                styles.cropArea,
                {
                  left: cropAreaAnim.x,
                  top: cropAreaAnim.y,
                  width: cropAreaAnim.width,
                  height: cropAreaAnim.height,
                }
              ]}
              {...movePanResponder.panHandlers}
            >
              {/* Corner handles */}
              <View style={[styles.handle, styles.handleTL]} {...resizeTLPanResponder.panHandlers} />
              <View style={[styles.handle, styles.handleTR]} {...resizeTRPanResponder.panHandlers} />
              <View style={[styles.handle, styles.handleBL]} {...resizeBLPanResponder.panHandlers} />
              <View style={[styles.handle, styles.handleBR]} {...resizeBRPanResponder.panHandlers} />
              
              {/* Edge handles */}
              <View style={[styles.edgeHandle, styles.handleT]} {...resizeTPanResponder.panHandlers} />
              <View style={[styles.edgeHandle, styles.handleB]} {...resizeBPanResponder.panHandlers} />
              <View style={[styles.edgeHandle, styles.handleL]} {...resizeLPanResponder.panHandlers} />
              <View style={[styles.edgeHandle, styles.handleR]} {...resizeRPanResponder.panHandlers} />
            </Animated.View>
          </>
        )}
      </View>

      <View style={styles.controls}>
        <Text style={styles.instruction}>
          Drag to move • Drag corners/edges to resize
        </Text>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.cropButton, isProcessing && styles.cropButtonDisabled]} 
            onPress={handleCrop}
            disabled={isProcessing}
          >
            <Text style={styles.cropButtonText}>
              {isProcessing ? 'Cropping...' : 'Crop'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  image: {
    maxWidth: SCREEN_WIDTH,
    maxHeight: SCREEN_HEIGHT * 0.7,
  },
  overlay: {
    position: 'absolute',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  cropArea: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: '#87CEEB',
    backgroundColor: 'transparent',
  },
  handle: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: '#87CEEB',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'white',
  },
  handleTL: {
    top: -10,
    left: -10,
  },
  handleTR: {
    top: -10,
    right: -10,
  },
  handleBL: {
    bottom: -10,
    left: -10,
  },
  handleBR: {
    bottom: -10,
    right: -10,
  },
  edgeHandle: {
    position: 'absolute',
    backgroundColor: '#87CEEB',
    borderRadius: 2,
  },
  handleT: {
    top: -4,
    left: '50%',
    marginLeft: -15,
    width: 30,
    height: 8,
  },
  handleB: {
    bottom: -4,
    left: '50%',
    marginLeft: -15,
    width: 30,
    height: 8,
  },
  handleL: {
    left: -4,
    top: '50%',
    marginTop: -15,
    width: 8,
    height: 30,
  },
  handleR: {
    right: -4,
    top: '50%',
    marginTop: -15,
    width: 8,
    height: 30,
  },
  controls: {
    backgroundColor: '#1F2937',
    padding: 20,
    paddingBottom: 40,
  },
  instruction: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
    opacity: 0.8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 16,
    backgroundColor: '#374151',
    borderRadius: 8,
    alignItems: 'center',
  },
  cancelButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  cropButton: {
    flex: 1,
    paddingVertical: 16,
    backgroundColor: '#87CEEB',
    borderRadius: 8,
    alignItems: 'center',
  },
  cropButtonDisabled: {
    opacity: 0.5,
  },
  cropButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
