import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import { ActivityIndicator, Text, View, BackHandler } from "react-native";
import { useAuthPersistence } from "../src/hooks/useAuthPersistence";
import { BreathingLogo } from "../src/components/BreathingLogo";

export default function Index() {
  const [isLoading, setIsLoading] = useState(true);
  const [redirectTo, setRedirectTo] = useState<string | null>(null);

  const { isInitializing, isAuthenticated } = useAuthPersistence();

  useEffect(() => {
    const determineRoute = async () => {
      console.log('📱 Index: Determining route - isInitializing:', isInitializing, 'isAuthenticated:', isAuthenticated);

      // Wait for auth initialization to complete
      if (isInitializing) {
        console.log('📱 Index: Still initializing, waiting...');
        return;
      }

      try {
        if (isAuthenticated) {
          console.log('📱 Index: User authenticated, redirecting to tabs');
          console.log('🔐 Session persistence working correctly!');
          setRedirectTo("/(tabs)");
        } else {
          console.log('📱 Index: User not authenticated, redirecting to welcome');
          console.log('🔐 No valid session found - user needs to sign in');
          // Let the consolidated AuthNavigator handle first-time vs returning user logic
          setRedirectTo("/(auth)/welcome");
        }
      } catch (error) {
        console.error('📱 Index: Error determining route:', error);
        setRedirectTo("/(auth)/welcome");
      } finally {
        setIsLoading(false);
      }
    };

    determineRoute();
  }, [isInitializing, isAuthenticated]);

  // Simplified timeout - only for extreme cases
  useEffect(() => {
    const fallbackTimeout = setTimeout(() => {
      if (isLoading && !redirectTo) {
        console.log('📱 Index: Fallback timeout reached, redirecting to welcome');
        setRedirectTo("/(auth)/welcome");
        setIsLoading(false);
      }
    }, 5000); // 5 second fallback timeout

    return () => clearTimeout(fallbackTimeout);
  }, [isLoading, redirectTo]);

  // Handle back button to prevent infinite loading loops
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      console.log('📱 Index: Back button pressed during loading');

      if (isLoading) {
        // If we're loading, force redirect to welcome to break any loops
        console.log('📱 Index: Breaking loading loop, redirecting to welcome');
        setRedirectTo("/(auth)/welcome");
        setIsLoading(false);
        return true; // Prevent default back action
      }

      return false; // Allow default back action
    });

    return () => backHandler.remove();
  }, [isLoading]);

  // Show loading screen while determining route
  if (isLoading || isInitializing) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "#000000",
        }}
      >
        <BreathingLogo size={140} color="#FFFFFF" textColor="#000000" />
        <ActivityIndicator
          size="large"
          color="#FFFFFF"
          style={{ marginTop: 32 }}
        />
        <Text
          style={{
            color: "#FFFFFF",
            fontSize: 18,
            fontWeight: "600",
            marginTop: 16,
            opacity: 0.9,
          }}
        >
          Loading IraChat...
        </Text>

      </View>
    );
  }

  // Redirect to determined route
  if (redirectTo) {
    console.log('📱 Index: Redirecting to:', redirectTo);
    return <Redirect href={redirectTo as any} />;
  }

  // Fallback - Force redirect to welcome if nothing else works
  console.log('📱 Index: No redirect determined, using fallback to welcome');
  return <Redirect href="/(auth)/welcome" />;
}
