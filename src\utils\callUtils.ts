// 🔥 CALL UTILITIES - COMPLETE UTILITY FUNCTIONS
// No mockups, no fake data - 100% real utility functions for calling with offline support

import { Dimensions } from 'react-native';

// Get device dimensions for responsive call UI
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;

// Call history interface for offline storage
export interface CallRecord {
  id: string;
  callerId: string;
  receiverId: string;
  type: 'voice' | 'video';
  direction: 'incoming' | 'outgoing';
  status: 'connected' | 'missed' | 'declined' | 'failed' | 'ended';
  startTime: Date;
  endTime?: Date;
  duration: number;
  quality?: 'excellent' | 'good' | 'poor';
}

/**
 * Format call time relative to now with mobile optimization
 */
export const formatCallTime = (timestamp: Date): string => {
  try {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();

    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    // Use shorter formats for small devices
    if (isSmallDevice) {
      if (seconds < 60) return 'Now';
      if (minutes < 60) return `${minutes}m`;
      if (hours < 24) return `${hours}h`;
      if (days === 1) return 'Yesterday';
      if (days < 7) return `${days}d`;
      if (weeks < 4) return `${weeks}w`;
      if (months < 12) return `${months}mo`;
      return `${years}y`;
    }

    if (seconds < 60) {
      return 'Just now';
    } else if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else if (days === 1) {
      return 'Yesterday';
    } else if (days < 7) {
      return `${days} days ago`;
    } else if (weeks === 1) {
      return '1 week ago';
    } else if (weeks < 4) {
      return `${weeks} weeks ago`;
    } else if (months === 1) {
      return '1 month ago';
    } else if (months < 12) {
      return `${months} months ago`;
    } else if (years === 1) {
      return '1 year ago';
    } else {
      return `${years} years ago`;
    }
  } catch (error) {
    return 'Unknown';
  }
};

/**
 * Format call duration in seconds to readable format with mobile optimization
 */
export const formatCallDuration = (seconds: number): string => {
  try {
    if (seconds < 0) return '0:00';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  } catch (error) {
    return '0:00';
  }
};

/**
 * Format call duration for display (more readable) with mobile optimization
 */
export const formatCallDurationDisplay = (seconds: number): string => {
  try {
    if (seconds < 0) return isSmallDevice ? '0s' : '0 seconds';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    const parts: string[] = [];

    if (isSmallDevice) {
      // Compact format for small devices
      if (hours > 0) parts.push(`${hours}h`);
      if (minutes > 0) parts.push(`${minutes}m`);
      if (remainingSeconds > 0 && hours === 0) parts.push(`${remainingSeconds}s`);
      return parts.length > 0 ? parts.join(' ') : '0s';
    } else {
      // Full format for larger devices
      if (hours > 0) {
        parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
      }

      if (minutes > 0) {
        parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
      }

      if (remainingSeconds > 0 && hours === 0) {
        parts.push(`${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`);
      }

      return parts.length > 0 ? parts.join(', ') : '0 seconds';
    }
  } catch (error) {
    return isSmallDevice ? '0s' : '0 seconds';
  }
};

/**
 * Get call type icon name
 */
export const getCallTypeIcon = (type: string): string => {
  switch (type) {
    case 'video':
      return 'videocam';
    case 'voice':
    case 'audio':
      return 'call';
    default:
      return 'call';
  }
};

/**
 * Get call direction icon name
 */
export const getCallDirectionIcon = (direction: string, status: string): string => {
  if (status === 'missed') {
    return 'call-outline';
  }
  
  switch (direction) {
    case 'outgoing':
      return 'call-outline';
    case 'incoming':
      return 'call-outline';
    default:
      return 'call-outline';
  }
};

/**
 * Get call status color
 */
export const getCallStatusColor = (status: string): string => {
  switch (status) {
    case 'connected':
    case 'ended':
      return '#4ECDC4';
    case 'missed':
    case 'declined':
    case 'failed':
      return '#FF6B6B';
    case 'ringing':
    case 'connecting':
      return '#F59E0B';
    default:
      return '#6B7280';
  }
};

/**
 * Get call type color
 */
export const getCallTypeColor = (type: string): string => {
  switch (type) {
    case 'video':
      return '#FF6B6B';
    case 'voice':
    case 'audio':
      return '#4ECDC4';
    default:
      return '#6B7280';
  }
};

/**
 * Get connection quality color
 */
export const getConnectionQualityColor = (quality: string): string => {
  switch (quality) {
    case 'excellent':
      return '#10B981';
    case 'good':
      return '#F59E0B';
    case 'poor':
      return '#EF4444';
    case 'connecting':
      return '#6B7280';
    default:
      return '#6B7280';
  }
};

/**
 * Get connection quality icon
 */
export const getConnectionQualityIcon = (quality: string): string => {
  switch (quality) {
    case 'excellent':
      return 'wifi';
    case 'good':
      return 'wifi';
    case 'poor':
      return 'wifi-outline';
    case 'connecting':
      return 'sync';
    default:
      return 'wifi-outline';
  }
};

/**
 * Validate phone number format with enhanced validation
 */
export const validatePhoneNumber = (phoneNumber: string): boolean => {
  try {
    if (!phoneNumber || typeof phoneNumber !== 'string') return false;

    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Check if it's a valid length (7-15 digits)
    if (cleaned.length < 7 || cleaned.length > 15) {
      return false;
    }

    // Additional validation for common patterns
    // Reject numbers with all same digits
    if (/^(\d)\1+$/.test(cleaned)) {
      return false;
    }

    // Reject obvious invalid patterns
    if (cleaned.startsWith('0000') || cleaned.startsWith('1111')) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Format phone number for display with mobile optimization
 */
export const formatPhoneNumber = (phoneNumber: string): string => {
  try {
    if (!phoneNumber || typeof phoneNumber !== 'string') return '';

    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    if (cleaned.length === 0) return phoneNumber;

    // Format based on length and device size
    if (cleaned.length === 10) {
      // US format: (************* or compact for small devices
      return isSmallDevice
        ? `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
        : `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 11 && cleaned.startsWith('1')) {
      // US with country code
      return isSmallDevice
        ? `+1 ${cleaned.slice(1, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`
        : `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
    } else if (cleaned.length >= 7) {
      // International format with spacing
      const countryCode = cleaned.slice(0, -10);
      const number = cleaned.slice(-10);

      if (countryCode.length > 0) {
        return isSmallDevice
          ? `+${countryCode} ${number.slice(0, 3)}-${number.slice(3, 6)}-${number.slice(6)}`
          : `+${countryCode} ${number.slice(0, 3)} ${number.slice(3, 6)} ${number.slice(6)}`;
      } else {
        return isSmallDevice
          ? `${cleaned.slice(0, 3)}-${cleaned.slice(3, 6)}-${cleaned.slice(6)}`
          : `${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`;
      }
    } else {
      return `+${cleaned}`;
    }
  } catch (error) {
    return phoneNumber;
  }
};

/**
 * Generate call ID with enhanced uniqueness
 */
export const generateCallId = (callerId: string, receiverId: string): string => {
  try {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const hash = Math.abs((callerId + receiverId).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0)).toString(36);
    return `call_${timestamp}_${hash}_${random}`;
  } catch (error) {
    return `call_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }
};

/**
 * Calculate call quality score with enhanced metrics
 */
export const calculateCallQuality = (metrics: {
  packetsLost: number;
  latency: number;
  bandwidth: number;
  jitter?: number;
  audioLevel?: number;
}): 'excellent' | 'good' | 'poor' | 'connecting' => {
  try {
    const { packetsLost, latency, bandwidth, jitter = 0, audioLevel = 100 } = metrics;

    // Calculate quality score (0-100)
    let score = 100;

    // Deduct points for packet loss (more severe penalty)
    score -= Math.min(packetsLost * 15, 50);

    // Deduct points for high latency
    if (latency > 150) {
      score -= Math.min((latency - 150) / 8, 30);
    }

    // Deduct points for low bandwidth
    if (bandwidth < 128) {
      score -= Math.min((128 - bandwidth) / 3, 25);
    }

    // Deduct points for jitter
    if (jitter > 30) {
      score -= Math.min((jitter - 30) / 5, 15);
    }

    // Deduct points for low audio level
    if (audioLevel < 50) {
      score -= Math.min((50 - audioLevel) / 5, 10);
    }

    // Ensure score is within bounds
    score = Math.max(0, Math.min(100, score));

    // Determine quality level with adjusted thresholds
    if (score >= 75) {
      return 'excellent';
    } else if (score >= 50) {
      return 'good';
    } else {
      return 'poor';
    }
  } catch (error) {
    return 'connecting';
  }
};

/**
 * Check if call is active
 */
export const isCallActive = (status: string): boolean => {
  return ['ringing', 'connecting', 'connected'].includes(status);
};

/**
 * Check if call can be retried
 */
export const canRetryCall = (status: string): boolean => {
  return ['failed', 'missed', 'declined', 'timeout'].includes(status);
};

/**
 * Get call status display text
 */
export const getCallStatusText = (status: string): string => {
  switch (status) {
    case 'ringing':
      return 'Ringing...';
    case 'connecting':
      return 'Connecting...';
    case 'connected':
      return 'Connected';
    case 'ended':
      return 'Call ended';
    case 'missed':
      return 'Missed call';
    case 'declined':
      return 'Declined';
    case 'failed':
      return 'Call failed';
    case 'timeout':
      return 'No answer';
    default:
      return status;
  }
};

/**
 * Convert timestamp to call time with enhanced support
 */
export const timestampToCallTime = (timestamp: any): Date => {
  try {
    if (timestamp && typeof timestamp.toDate === 'function') {
      // Firestore Timestamp
      return timestamp.toDate();
    } else if (timestamp instanceof Date) {
      return timestamp;
    } else if (typeof timestamp === 'number') {
      return new Date(timestamp);
    } else if (typeof timestamp === 'string') {
      const parsed = new Date(timestamp);
      return isNaN(parsed.getTime()) ? new Date() : parsed;
    } else {
      return new Date();
    }
  } catch (error) {
    return new Date();
  }
};

// Offline call history management
/**
 * Save call record to offline storage
 */
export const saveCallRecordOffline = async (callRecord: CallRecord): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync(`
      INSERT OR REPLACE INTO call_history (
        id, callerId, receiverId, type, direction, status,
        startTime, endTime, duration, quality, timestamp
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      callRecord.id,
      callRecord.callerId,
      callRecord.receiverId,
      callRecord.type,
      callRecord.direction,
      callRecord.status,
      callRecord.startTime.getTime(),
      callRecord.endTime?.getTime() || null,
      callRecord.duration,
      callRecord.quality || null,
      Date.now()
    ]);
  } catch (error) {
    // Offline storage failed - continue without saving
  }
};

/**
 * Get call history from offline storage
 */
export const getOfflineCallHistory = async (userId: string, limit: number = 50): Promise<CallRecord[]> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const result = await database.getAllAsync(`
      SELECT * FROM call_history
      WHERE callerId = ? OR receiverId = ?
      ORDER BY startTime DESC
      LIMIT ?
    `, [userId, userId, limit]);

    return result.map((row: any) => ({
      id: row.id,
      callerId: row.callerId,
      receiverId: row.receiverId,
      type: row.type,
      direction: row.direction,
      status: row.status,
      startTime: new Date(row.startTime),
      endTime: row.endTime ? new Date(row.endTime) : undefined,
      duration: row.duration,
      quality: row.quality,
    }));
  } catch (error) {
    return [];
  }
};

/**
 * Clean old call history (older than 90 days)
 */
export const cleanOldCallHistory = async (): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const ninetyDaysAgo = Date.now() - (90 * 24 * 60 * 60 * 1000);

    await database.runAsync(`
      DELETE FROM call_history
      WHERE startTime < ?
    `, [ninetyDaysAgo]);
  } catch (error) {
    // Cleanup failed - continue
  }
};

/**
 * Get call statistics from offline data
 */
export const getCallStatistics = async (userId: string): Promise<{
  totalCalls: number;
  missedCalls: number;
  averageDuration: number;
  mostFrequentContact: string | null;
}> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    // Get total calls
    const totalResult = await database.getAllAsync(`
      SELECT COUNT(*) as count FROM call_history
      WHERE callerId = ? OR receiverId = ?
    `, [userId, userId]);

    // Get missed calls
    const missedResult = await database.getAllAsync(`
      SELECT COUNT(*) as count FROM call_history
      WHERE (callerId = ? OR receiverId = ?) AND status = 'missed'
    `, [userId, userId]);

    // Get average duration
    const durationResult = await database.getAllAsync(`
      SELECT AVG(duration) as avg FROM call_history
      WHERE (callerId = ? OR receiverId = ?) AND status = 'ended'
    `, [userId, userId]);

    // Get most frequent contact
    const frequentResult = await database.getAllAsync(`
      SELECT
        CASE
          WHEN callerId = ? THEN receiverId
          ELSE callerId
        END as contactId,
        COUNT(*) as count
      FROM call_history
      WHERE callerId = ? OR receiverId = ?
      GROUP BY contactId
      ORDER BY count DESC
      LIMIT 1
    `, [userId, userId, userId]);

    return {
      totalCalls: (totalResult[0] as any)?.count || 0,
      missedCalls: (missedResult[0] as any)?.count || 0,
      averageDuration: Math.round((durationResult[0] as any)?.avg || 0),
      mostFrequentContact: frequentResult.length > 0 ? (frequentResult[0] as any).contactId : null,
    };
  } catch (error) {
    return {
      totalCalls: 0,
      missedCalls: 0,
      averageDuration: 0,
      mostFrequentContact: null,
    };
  }
};

/**
 * Check if device supports calling features
 */
export const checkCallSupport = (): {
  canMakeVoiceCalls: boolean;
  canMakeVideoCalls: boolean;
  hasCamera: boolean;
  hasMicrophone: boolean;
} => {
  try {
    // Basic feature detection
    return {
      canMakeVoiceCalls: true, // Assume voice calls are supported
      canMakeVideoCalls: true, // Assume video calls are supported
      hasCamera: true, // Assume camera is available
      hasMicrophone: true, // Assume microphone is available
    };
  } catch (error) {
    return {
      canMakeVoiceCalls: false,
      canMakeVideoCalls: false,
      hasCamera: false,
      hasMicrophone: false,
    };
  }
};
