import { Ionicons } from "@expo/vector-icons";
import { Audio } from "expo-av";
import * as DocumentPicker from "expo-document-picker";
import * as ImagePicker from "expo-image-picker";
// Location import removed to avoid Google Maps API costs
import * as Contacts from "expo-contacts";
import * as Clipboard from 'expo-clipboard';
import { useRouter } from "expo-router";
import { navigationService, ROUTES } from "../services/navigationService";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from "../styles/iraChatDesignSystem";
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../utils/responsiveUtils";
import {
    addDoc,
    arrayUnion,
    collection,
    deleteDoc,
    doc,
    getDocs,
    onSnapshot,
    orderBy,
    query,
    serverTimestamp,
    updateDoc,
    where
} from "firebase/firestore";
import React, { useCallback, useEffect, useRef, useState, useReducer } from "react";
import {
    ActivityIndicator,
    Alert,
    Animated,
    Dimensions,
    FlatList,
    Image,
    KeyboardAvoidingView,
    Modal,
    Platform,
    Pressable,
    StatusBar,
    Text,
    TextInput,
    TouchableOpacity,
    Vibration,
    View
} from "react-native";
import { useCallManager } from "../hooks/useCallManager";
import { auth, db, storage } from "../services/firebaseSimple";
import MessageStatusIndicator from "./MessageStatusIndicator";
import TypingIndicator from "./TypingIndicator";
import MessageSearch from "./MessageSearch";
import { realTimeMessagingService } from "../services/realTimeMessagingService";
import { errorHandlingService } from "../services/errorHandlingService";
import { messageSyncService } from "../services/messageSyncService";

// Enhanced Message Interface
interface Message {
  id: string;
  text?: string;
  senderId: string;
  timestamp: any;
  status?: "sent" | "delivered" | "read" | "seen_not_replied" | "seen_replied";
  type: "text" | "image" | "video" | "audio" | "document" | "voice" | "call" | "location" | "contact";
  mediaUrl?: string;
  mediaThumbnail?: string;
  duration?: number; // for audio/video
  fileName?: string; // for documents
  fileSize?: number;
  // Call properties
  callType?: "voice" | "video";
  callStatus?: "outgoing" | "incoming" | "ended" | "cancelled" | "missed";
  callDuration?: number; // in seconds
  // Pinning functionality
  isPinned?: boolean;
  pinnedAt?: any;
  pinnedBy?: string;
  replyTo?: {
    messageId: string;
    text: string;
    senderName: string;
    type: string;
  };
  reactions?: {
    [userId: string]: string; // emoji
  };
  isEdited?: boolean;
  editedAt?: any;
  isForwarded?: boolean;
  forwardedFrom?: string;
  deletedFor?: string[]; // Array of user IDs who deleted this message


  // Contact data
  contact?: {
    name: string;
    phoneNumbers: string[];
    emails: string[];
  };
}

// Message state management with reducer for atomic updates
interface MessageState {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
}

type MessageAction =
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'MERGE_FIREBASE_MESSAGES'; payload: Message[] }
  | { type: 'UPDATE_MESSAGE'; payload: { id: string; updates: Partial<Message> } }
  | { type: 'DELETE_MESSAGE'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

const messageReducer = (state: MessageState, action: MessageAction): MessageState => {
  switch (action.type) {
    case 'SET_MESSAGES':
      return { ...state, messages: action.payload, isLoading: false, error: null };
    case 'ADD_MESSAGE':
      // Prevent duplicates by checking if message already exists
      const existingMessage = state.messages.find(msg => msg.id === action.payload.id);
      if (existingMessage) {
        return state; // Don't add duplicate
      }
      return {
        ...state,
        messages: [...state.messages, action.payload],
        error: null
      };
    case 'MERGE_FIREBASE_MESSAGES':
      // Merge Firebase messages with existing messages, avoiding duplicates
      const existingIds = new Set(state.messages.map(msg => msg.id));
      const newMessages = action.payload.filter(msg => !existingIds.has(msg.id));

      if (newMessages.length === 0) {
        return state; // No new messages to add
      }
      const allMessages = [...state.messages, ...newMessages];
      // Sort by timestamp
      allMessages.sort((a, b) => {
        const aTime = a.timestamp?.toDate?.() || new Date(a.timestamp);
        const bTime = b.timestamp?.toDate?.() || new Date(b.timestamp);
        return aTime.getTime() - bTime.getTime();
      });
      return {
        ...state,
        messages: allMessages,
        error: null
      };
    case 'UPDATE_MESSAGE':
      return {
        ...state,
        messages: state.messages.map(msg =>
          msg.id === action.payload.id
            ? { ...msg, ...action.payload.updates }
            : msg
        ),
        error: null
      };
    case 'DELETE_MESSAGE':
      return {
        ...state,
        messages: state.messages.filter(msg => msg.id !== action.payload),
        error: null
      };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    default:
      return state;
  }
};

// Firebase Storage Upload Service
const uploadToFirebaseStorage = async (
  uri: string,
  chatId: string,
  userId: string,
  type: 'voice' | 'image' | 'video' | 'document',
  fileName: string,
  metadata: any = {}
): Promise<string> => {
  if (!storage) {
    throw new Error('Firebase Storage not initialized');
  }

  // Convert URI to blob
  const response = await fetch(uri);
  const blob = await response.blob();

  // Import Firebase Storage functions
  const { ref, uploadBytes, getDownloadURL } = await import('firebase/storage');

  // Create storage reference
  const storagePath = `chats/${chatId}/${type}/${fileName}`;
  const storageRef = ref(storage, storagePath);

  // Set content type based on media type
  const contentType = type === 'voice' ? 'audio/m4a' :
                     type === 'image' ? 'image/jpeg' :
                     type === 'video' ? 'video/mp4' :
                     'application/octet-stream';

  // Upload file to Firebase Storage
  const uploadResult = await uploadBytes(storageRef, blob, {
    contentType,
    customMetadata: {
      'chatId': chatId,
      'senderId': userId,
      'mediaType': type,
      'uploadedAt': new Date().toISOString(),
      ...metadata
    }
  });

  // Get and return download URL
  return await getDownloadURL(uploadResult.ref);
};

// Retry failed uploads when back online
const retryFailedUploads = async (
  chatId: string,
  currentUserId: string,
  useFirebase: boolean,
  offlineMode: boolean,
  dispatchMessage: (action: any) => void
) => {
  if (!useFirebase || !storage || offlineMode) return;

  try {
    // Get messages with local URIs that need uploading
    const localMessages = await messageSyncService.loadMessagesFromLocal(chatId);
    const pendingUploads = localMessages.filter(msg =>
      msg.senderId === currentUserId &&
      msg.mediaUrl &&
      msg.mediaUrl.startsWith('file://') && // Local URI
      msg.type && ['voice', 'image', 'video', 'document'].includes(msg.type)
    );

    console.log(`🔄 Retrying ${pendingUploads.length} failed uploads...`);

    for (const message of pendingUploads) {
      try {
        const uploadedUrl = await uploadToFirebaseStorage(
          message.mediaUrl!,
          chatId,
          currentUserId,
          message.type as any,
          message.fileName || `${message.type}_${message.id}`,
          {
            'originalFileName': message.fileName,
            'fileSize': message.fileSize?.toString(),
            'duration': message.duration?.toString()
          }
        );

        // Update message with uploaded URL
        dispatchMessage({
          type: 'UPDATE_MESSAGE',
          payload: {
            id: message.id,
            updates: {
              mediaUrl: uploadedUrl,
              status: 'delivered'
            }
          }
        });

        console.log(`✅ Retry upload successful for ${message.type}: ${uploadedUrl}`);
      } catch (retryError) {
        console.warn(`⚠️ Retry upload failed for message ${message.id}:`, retryError);
      }
    }
  } catch (error) {
    console.error('❌ Error retrying failed uploads:', error);
  }
};

// Enhanced security validation
const validateMessageContent = (content: string): { isValid: boolean; error?: string } => {
  // Length validation
  if (content.length > 5000) {
    return { isValid: false, error: 'Message is too long (max 5000 characters)' };
  }

  // Enhanced XSS protection
  const suspiciousPatterns = [
    /<script[\s\S]*?>[\s\S]*?<\/script>/gi,
    /<iframe[\s\S]*?>[\s\S]*?<\/iframe>/gi,
    /javascript:/gi,
    /vbscript:/gi,
    /data:text\/html/gi,
    /data:application\/javascript/gi,
    /on\w+\s*=/gi, // Event handlers like onclick, onload, etc.
    /<object[\s\S]*?>[\s\S]*?<\/object>/gi,
    /<embed[\s\S]*?>/gi,
    /<link[\s\S]*?>/gi,
    /<meta[\s\S]*?>/gi,
    /expression\s*\(/gi,
    /url\s*\(\s*javascript:/gi,
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(content)) {
      return { isValid: false, error: 'Message contains potentially harmful content' };
    }
  }

  // Emoji flood protection
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
  const emojiMatches = content.match(emojiRegex);
  if (emojiMatches && emojiMatches.length > 50) {
    return { isValid: false, error: 'Too many emojis in message' };
  }

  // Spam detection - repeated characters
  const repeatedCharPattern = /(.)\1{20,}/g;
  if (repeatedCharPattern.test(content)) {
    return { isValid: false, error: 'Message contains excessive repeated characters' };
  }

  return { isValid: true };
};

// Reaction Interface (for future use)
// interface Reaction {
//   emoji: string;
//   users: string[];
// }

interface ChatRoomProps {
  chatId: string;
  partnerName: string;
  partnerAvatar: string;
  isOnline: boolean;
  partnerId: string;
}

export const ChatRoom: React.FC<ChatRoomProps> = ({
  chatId,
  partnerName,
  partnerAvatar,
  isOnline,
  partnerId,
}) => {
  // Core State with reducer for atomic updates
  const [messageState, dispatchMessage] = useReducer(messageReducer, {
    messages: [],
    isLoading: false,
    error: null
  });
  const { messages } = messageState;

  // Debug: Log messages state changes
  useEffect(() => {
    console.log('📊 Messages state updated. Count:', messages.length);
  }, [messages.length]);

  // Track if messages have been loaded to prevent reloading
  const [hasLoadedMessages, setHasLoadedMessages] = useState(false);

  // Reset loaded flag when chatId changes
  useEffect(() => {
    setHasLoadedMessages(false);
  }, [chatId]);

  const [newMessage, setNewMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<{ userId: string; userName: string }[]>([]);

  // Media & Voice State
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);
  const [sound, setSound] = useState<Audio.Sound | null>(null);

  // Blocking State
  const [isBlocked, setIsBlocked] = useState(false);
  const [isBlockedByPartner, setIsBlockedByPartner] = useState(false);

  // IraChat User Status
  const [isIraChatUser, setIsIraChatUser] = useState(false);
  const [lastSeen, setLastSeen] = useState<Date | null>(null);

  // Offline/Online Status
  const [isOfflineMode, setIsOfflineMode] = useState(false);

  // Upload Progress Tracking
  const [uploadingMessages, setUploadingMessages] = useState<Set<string>>(new Set());

  // Pinned Messages State
  const [pinnedMessages, setPinnedMessages] = useState<Message[]>([]);
  const [showPinnedMessages, setShowPinnedMessages] = useState(false);

  // UI State
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showAttachmentMenu, setShowAttachmentMenu] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [showMessageActions, setShowMessageActions] = useState(false);
  const [showMessageSearch, setShowMessageSearch] = useState(false);
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  const [editingMessage, setEditingMessage] = useState<Message | null>(null);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Enhanced Search State
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Message[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);
  const [isSearching, setIsSearching] = useState(false);
  const [searchHighlightedMessageId, setSearchHighlightedMessageId] = useState<string | null>(null);
  const [showQuickSearch, setShowQuickSearch] = useState(false);

  // Forward Message State
  const [showForwardModal, setShowForwardModal] = useState(false);
  const [messageToForward, setMessageToForward] = useState<Message | null>(null);
  const [availableChats, setAvailableChats] = useState<{id: string, name: string, avatar: string, lastSeen?: Date}[]>([]);

  // Animation Values
  const recordingAnimation = useRef(new Animated.Value(1)).current;
  const typingAnimation = useRef(new Animated.Value(0)).current;
  const replyAnimation = useRef(new Animated.Value(0)).current;

  // Refs
  const flatListRef = useRef<FlatList>(null);
  const textInputRef = useRef<TextInput>(null);
  const recordingTimer = useRef<ReturnType<typeof setInterval> | null>(null);

  const router = useRouter();
  const currentUser = auth?.currentUser;
  const { width: screenWidth } = Dimensions.get("window");
  const statusBarHeight = Platform.OS === 'android' ? StatusBar.currentHeight || 24 : 0;

  // Initialize call manager
  const { startCall } = useCallManager(currentUser?.uid || '');

  // Offline-first architecture: Local storage is primary, Firebase is for sync only
  const USE_FIREBASE = true; // Firebase used for real-time sync of new messages only

  // Load Messages function - Now loads from preloaded cache first, then local SQLite storage
  const loadMessages = useCallback(async () => {
    if (!currentUser || !chatId) return;

    dispatchMessage({ type: 'SET_LOADING', payload: true });

    try {
      // First, try to get preloaded messages for instant display
      const { messagePreloadService } = await import('../services/messagePreloadService');
      const preloadedMessages = messagePreloadService.getPreloadedMessages(chatId);

      if (preloadedMessages.length > 0) {
        // Convert preloaded messages to local Message format
        const convertedMessages = preloadedMessages.map(msg => ({
          id: msg.id,
          text: msg.content,
          senderId: msg.senderId,
          senderName: msg.senderName,
          timestamp: msg.timestamp,
          type: msg.type,
          status: msg.status,
          chatId: msg.chatId,
        })) as Message[];

        dispatchMessage({ type: 'SET_MESSAGES', payload: convertedMessages });
        setHasLoadedMessages(true);
        console.log(`⚡ Loaded ${convertedMessages.length} preloaded messages instantly`);
        return; // Exit early since we have preloaded messages
      }

      // Fallback: Load messages from local SQLite storage
      const localMessages = await messageSyncService.loadMessagesFromLocal(chatId);
      // Convert to local Message format
      const convertedMessages = localMessages.map(msg => ({
        ...msg,
        status: msg.status || "sent",
        type: msg.type || "text",
      })) as Message[];
      dispatchMessage({ type: 'SET_MESSAGES', payload: convertedMessages });
      setHasLoadedMessages(true);
      console.log(`✅ Loaded ${convertedMessages.length} messages from local storage`);
    } catch (error) {
      console.error("❌ Error loading messages:", error);
      dispatchMessage({
        type: 'SET_ERROR',
        payload: 'Failed to load messages. Some messages may not be visible.'
      });
    }
  }, [chatId, currentUser]);

  // Load Messages with Real-time Updates - Offline-first approach
  useEffect(() => {
    // Only load messages once to prevent overwriting new messages
    if (!hasLoadedMessages) {
      loadMessages();
    }

    if (USE_FIREBASE && currentUser && chatId) {
      const timeoutId = setTimeout(() => {
        const q = query(
          collection(db, `chats/${chatId}/messages`),
          orderBy("timestamp", "asc"),
        );

        const unsubscribe = onSnapshot(
          q,
          (snapshot) => {
            // Firebase is for real-time sync of new messages only
            const firebaseMessages = snapshot.docs.map((doc) => ({
              id: doc.id,
              ...doc.data(),
            })) as Message[];

            // Use MERGE_FIREBASE_MESSAGES to efficiently handle all Firebase messages at once
            // This prevents infinite loops and handles duplicates automatically

            dispatchMessage({ type: 'MERGE_FIREBASE_MESSAGES', payload: firebaseMessages });

            // Save new messages to local storage for offline access
            firebaseMessages.forEach(fbMsg => {
              const messageType = ['voice', 'call', 'location', 'contact'].includes(fbMsg.type) ? 'text' : fbMsg.type;
              const localMessageData = {
                text: fbMsg.text,
                mediaUrl: fbMsg.mediaUrl,
                fileName: fbMsg.fileName,
                fileSize: fbMsg.fileSize,
                duration: fbMsg.duration,
                replyTo: fbMsg.replyTo,
                isForwarded: fbMsg.isForwarded,
                forwardedFrom: fbMsg.forwardedFrom
              };
              messageSyncService.saveMessageLocally(
                chatId,
                fbMsg.text || `${fbMsg.type} message`,
                fbMsg.senderId,
                messageType as any,
                localMessageData
              ).catch(console.warn);
            });

            // Mark messages as read when chat is opened
            if (firebaseMessages.length > 0 && currentUser?.uid) {
              realTimeMessagingService.markChatAsRead(chatId, currentUser.uid);
              markMessagesAsRead(firebaseMessages);
            }

            // Optimize scroll timing - only scroll if messages exist
            if (firebaseMessages.length > 0) {
              setTimeout(() => {
                flatListRef.current?.scrollToEnd({ animated: false });
              }, 50);
            }

            // If we were offline and now have Firebase connection, retry failed uploads
            if (isOfflineMode) {
              setIsOfflineMode(false);
              console.log('🔄 Back online - retrying failed uploads...');
              retryFailedUploads(chatId, currentUser.uid, USE_FIREBASE, false, dispatchMessage);
            }
          },
          (error) => {
            console.warn("⚠️ Firebase sync unavailable - switching to offline mode:", error);
            // Set offline mode flag - app continues to work with local storage
            setIsOfflineMode(true);
            // Clear any previous errors since offline mode is expected and functional
            dispatchMessage({
              type: 'SET_ERROR',
              payload: null
            });
            // Show brief offline notification (non-blocking)
            console.log("📱 App running in offline mode - all messages stored locally");
          }
        );

        // Subscribe to typing indicators
        const typingUnsubscribe = realTimeMessagingService.subscribeToTypingIndicators(
          chatId,
          currentUser?.uid || '',
          (users) => {
            setTypingUsers(users);
          }
        );

        return () => {
          unsubscribe();
          typingUnsubscribe();
        };
      }, 50);

      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };
    }
  }, [chatId, currentUser, hasLoadedMessages, loadMessages]);

  // Real Firebase message loading is now active - no more mock data

  // Check blocking status and IraChat user status on component mount
  useEffect(() => {
    checkBlockingStatus();
    loadPinnedMessages();
    checkIraChatUserStatus();

    // Initialize message sync service
    messageSyncService.initialize().catch(error => {
      console.error('❌ Failed to initialize message sync service:', error);
    });
  }, [partnerId, currentUser]);

  // Check blocking status
  const checkBlockingStatus = async () => {
    if (!currentUser || !partnerId) return;

    try {
      const { isUserBlocked, isBlockedByUser } = await import("../services/blockingService");

      const blocked = await isUserBlocked(currentUser.uid, partnerId);
      const blockedBy = await isBlockedByUser(currentUser.uid, partnerId);

      setIsBlocked(blocked);
      setIsBlockedByPartner(blockedBy);
    } catch (error) {
      console.error("❌ Error checking blocking status:", error);
    }
  };

  // Check if partner is an IraChat user and get their status
  const checkIraChatUserStatus = async () => {
    if (!partnerId) return;

    try {
      // Real Firebase implementation - check users collection
      const userDoc = await getDocs(
        query(collection(db, "users"), where("uid", "==", partnerId))
      );

      if (!userDoc.empty) {
        const userData = userDoc.docs[0].data();
        setIsIraChatUser(true);

        // Get last seen if user is offline
        if (userData.lastSeen) {
          setLastSeen(userData.lastSeen.toDate());
        }
      } else {
        setIsIraChatUser(false);
        setLastSeen(null);
      }
    } catch (error) {
      console.error("❌ Error checking IraChat user status:", error);
      // Default to not an IraChat user if error
      setIsIraChatUser(false);
      setLastSeen(null);
    }
  };

  // Format last seen time
  const formatLastSeen = (lastSeenDate: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - lastSeenDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return "just now";
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    } else if (diffInMinutes < 1440) { // Less than 24 hours
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInMinutes < 10080) { // Less than 7 days
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      // More than 7 days, show date
      return lastSeenDate.toLocaleDateString();
    }
  };

  // Load pinned messages
  const loadPinnedMessages = async () => {
    if (!chatId) return;

    try {
      const q = query(
        collection(db, `chats/${chatId}/messages`),
        where("isPinned", "==", true),
        orderBy("pinnedAt", "desc")
      );

      const snapshot = await getDocs(q);
      const pinned = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Message[];

      setPinnedMessages(pinned);
    } catch (error) {
      console.error("❌ Error loading pinned messages:", error);
    }
  };

  // Typing Indicator Effect - Lazy loaded with proper cleanup
  useEffect(() => {
    let animationTimeout: ReturnType<typeof setTimeout>;
    let typingAnimationLoop: Animated.CompositeAnimation | null = null;

    // Defer animation setup to not block initial render
    animationTimeout = setTimeout(() => {
      const isPartnerTyping = typingUsers.length > 0;
      if (isPartnerTyping) {
        typingAnimationLoop = Animated.loop(
          Animated.sequence([
            Animated.timing(typingAnimation, {
              toValue: 1,
              duration: ANIMATIONS.slow,
              useNativeDriver: true,
            }),
            Animated.timing(typingAnimation, {
              toValue: 0,
              duration: ANIMATIONS.slow,
              useNativeDriver: true,
            }),
          ]),
        );
        typingAnimationLoop.start();
      } else {
        typingAnimation.setValue(0);
      }
    }, 100);

    return () => {
      clearTimeout(animationTimeout);
      if (typingAnimationLoop) {
        typingAnimationLoop.stop();
      }
    };
  }, [typingUsers, typingAnimation]);

  // Reply Animation Effect - Lazy loaded with proper cleanup
  useEffect(() => {
    let animationTimeout: ReturnType<typeof setTimeout>;
    let replyAnimationInstance: Animated.CompositeAnimation | null = null;

    // Defer animation setup to not block initial render
    animationTimeout = setTimeout(() => {
      if (replyingTo) {
        replyAnimationInstance = Animated.spring(replyAnimation, {
          toValue: 1,
          useNativeDriver: true,
        });
        replyAnimationInstance.start();
      } else {
        replyAnimationInstance = Animated.spring(replyAnimation, {
          toValue: 0,
          useNativeDriver: true,
        });
        replyAnimationInstance.start();
      }
    }, 100);

    return () => {
      clearTimeout(animationTimeout);
      if (replyAnimationInstance) {
        replyAnimationInstance.stop();
      }
    };
  }, [replyingTo, replyAnimation]);

  // Recording Animation Effect - Only when needed with proper cleanup
  useEffect(() => {
    let recordingAnimationLoop: Animated.CompositeAnimation | null = null;
    const MAX_RECORDING_DURATION = 300; // 5 minutes max

    if (isRecording) {
      // Start animation immediately when recording starts
      recordingAnimationLoop = Animated.loop(
        Animated.sequence([
          Animated.timing(recordingAnimation, {
            toValue: 1.2,
            duration: ANIMATIONS.slower,
            useNativeDriver: true,
          }),
          Animated.timing(recordingAnimation, {
            toValue: 1,
            duration: ANIMATIONS.slower,
            useNativeDriver: true,
          }),
        ]),
      );
      recordingAnimationLoop.start();

      // Start recording timer with duration limit
      recordingTimer.current = setInterval(() => {
        setRecordingDuration((prev) => {
          const newDuration = prev + 1;
          // Auto-stop recording if it exceeds max duration
          if (newDuration >= MAX_RECORDING_DURATION) {
            // Use setTimeout to avoid calling stopRecording during state update
            setTimeout(() => {
              stopRecording();
              Alert.alert("Recording Limit", "Maximum recording duration reached (5 minutes)");
            }, 0);
          }
          return newDuration;
        });
      }, 1000);
    } else {
      recordingAnimation.setValue(1);
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
      setRecordingDuration(0);
    }

    return () => {
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
        recordingTimer.current = null;
      }
      if (recordingAnimationLoop) {
        recordingAnimationLoop.stop();
      }
    };
  }, [isRecording, recordingAnimation]);

  // Mark Messages as Read
  const markMessagesAsRead = useCallback(
    async (messagesToMark: Message[]) => {
      if (!currentUser) return;

      const unreadMessages = messagesToMark.filter(
        (msg) => msg.senderId !== currentUser.uid && msg.status !== "read",
      );

      for (const message of unreadMessages) {
        try {
          await updateDoc(doc(db, `chats/${chatId}/messages/${message.id}`), {
            status: "read",
          });
        } catch (error) {
          console.error("Error marking message as read:", error);
        }
      }
    },
    [currentUser, chatId],
  );

  // Send Text Message
  const sendMessage = async () => {
    // Enhanced input validation and security
    const trimmedMessage = newMessage.trim();
    if (!trimmedMessage || !currentUser) return;

    // Check if user is blocked from sending messages
    if (isBlockedByPartner) {
      Alert.alert("Action Restricted", "You cannot send messages to this chat.");
      return;
    }

    // Enhanced security validation
    const validation = validateMessageContent(trimmedMessage);
    if (!validation.isValid) {
      Alert.alert('Error', validation.error || 'Invalid message content');
      return;
    }



    try {
      // Generate unique local ID for immediate UI update
      const localId = `local_${Date.now()}_${currentUser.uid.slice(-6)}_${Math.random().toString(36).substring(2, 11)}`;

      // Create message for immediate UI update
      const newMsg: Message = {
        id: localId,
        text: newMessage,
        senderId: currentUser.uid,
        timestamp: new Date(),
        status: "sent",
        type: "text",
        replyTo: replyingTo ? {
          messageId: replyingTo.id,
          text: replyingTo.text || "Media",
          senderName: replyingTo.senderId === currentUser.uid ? "You" : partnerName,
          type: replyingTo.type,
        } : undefined,
      };

      // Update messages immediately for better UX

      dispatchMessage({ type: 'ADD_MESSAGE', payload: newMsg });
      setNewMessage("");
      setIsTyping(false);
      setReplyingTo(null);

      // Send to Firebase immediately for real-time chat
      const sendToFirebase = async () => {
        if (USE_FIREBASE && !isOfflineMode) {
          try {
            const { addDoc, collection, serverTimestamp } = await import('firebase/firestore');
            const { db } = await import('../services/firebaseSimple');

            const messageData = {
              text: newMessage,
              senderId: currentUser.uid,
              timestamp: serverTimestamp(),
              status: 'sent',
              type: 'text',
              replyTo: newMsg.replyTo || null,
            };

            // Send to Firebase immediately
            const docRef = await addDoc(collection(db, `chats/${chatId}/messages`), messageData);
            console.log('✅ Message sent to Firebase immediately:', docRef.id);

            // Update the local message status
            dispatchMessage({
              type: 'UPDATE_MESSAGE',
              payload: {
                id: localId,
                updates: {
                  status: 'delivered'
                }
              }
            });

          } catch (firebaseError) {
            console.error('❌ Failed to send to Firebase:', firebaseError);
            setIsOfflineMode(true);
          }
        }
      };

      // Send to Firebase immediately (don't wait)
      sendToFirebase();

      // Also save to local storage for offline access (background)
      messageSyncService.saveMessageLocally(
        chatId,
        newMessage,
        currentUser.uid,
        'text',
        {
          id: localId,
          replyTo: newMsg.replyTo,
          status: 'sent'
        }
      ).catch(error => {
        console.error('❌ Failed to save message locally:', error);
      });

      // Update last message in chat list using the new service
      try {
        const { lastMessageSyncService } = await import('../services/lastMessageSyncService');
        await lastMessageSyncService.updateChatLastMessage(
          chatId,
          newMessage,
          currentUser.uid,
          currentUser.displayName || 'You',
          'text'
        );
      } catch (error) {
        console.error('❌ Failed to update last message:', error);
      }

      // Scroll to bottom immediately after message is added
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 10);
    } catch (error: any) {
      errorHandlingService.handleError(error, 'Send Message');
      Alert.alert("Error", "Failed to send message");
    }
  };





  // Block/Unblock User
  const handleBlockUser = async () => {
    if (!currentUser || !partnerId) return;

    try {
      if (isBlocked) {
        // Unblock user
        const { unblockUser } = await import("../services/blockingService");
        await unblockUser(currentUser.uid, partnerId);
        setIsBlocked(false);
        Alert.alert("Success", `${partnerName} has been unblocked`);
      } else {
        // Block user
        Alert.alert(
          "Block User",
          `Are you sure you want to block ${partnerName}? You won't receive messages from them.`,
          [
            { text: "Cancel", style: "cancel" },
            {
              text: "Block",
              style: "destructive",
              onPress: async () => {
                const { blockUser } = await import("../services/blockingService");
                await blockUser(currentUser.uid, partnerId, partnerName, partnerAvatar);
                setIsBlocked(true);
                Alert.alert("Success", `${partnerName} has been blocked`);
              },
            },
          ]
        );
      }
    } catch (error) {
      console.error("❌ Error blocking/unblocking user:", error);
      Alert.alert("Error", "Failed to update blocking status");
    }
  };

  // Forward Message - Local-first with offline support
  const forwardMessage = async (message: Message, targetChatId: string, targetChatName: string) => {
    if (!currentUser) return;

    try {
      // Create forwarded message with local storage first (WhatsApp-style)
      const forwardedMessage: Partial<Message> = {
        text: message.text,
        senderId: currentUser.uid,
        timestamp: new Date(), // Use local timestamp for immediate display
        status: "sent",
        type: message.type,
        mediaUrl: message.mediaUrl,
        fileName: message.fileName,
        duration: message.duration,
        isForwarded: true,
        forwardedFrom: partnerName,
      };

      // Save to local storage immediately for offline functionality (WhatsApp-style)
      let localId: string;

      if (message.type === 'voice' || message.type === 'image' || message.type === 'video' || message.type === 'audio' || message.type === 'document') {
        // Use media message storage for media types
        localId = await messageSyncService.saveMediaMessageLocally(
          targetChatId,
          currentUser.uid,
          message.type as 'image' | 'video' | 'audio' | 'document' | 'voice',
          message.mediaUrl || '',
          {
            fileName: message.fileName,
            fileSize: message.fileSize,
            duration: message.duration,
            text: message.text, // caption for forwarded media
          }
        );
      } else {
        // Use regular message storage for text and other types
        // Map unsupported types to 'text' for local storage
        const storageType = ['call', 'location', 'contact'].includes(message.type) ? 'text' : message.type;
        localId = await messageSyncService.saveMessageLocally(
          targetChatId,
          message.text || `Forwarded ${message.type}`,
          currentUser.uid,
          storageType as any,
          {
            isForwarded: true,
            forwardedFrom: partnerName,
          }
        );
      }

      // Update local UI immediately (like WhatsApp)
      console.log(`✅ Message forwarded to ${targetChatName}: ${localId}`);

      // Background sync to Firebase (when online) - non-blocking
      if (USE_FIREBASE && !isOfflineMode) {
        try {
          await addDoc(
            collection(db, `chats/${targetChatId}/messages`),
            {
              ...forwardedMessage,
              timestamp: serverTimestamp(), // Use server timestamp for Firebase
            }
          );
          console.log(`🔄 Message synced to Firebase for chat: ${targetChatId}`);
        } catch (syncError) {
          console.warn("⚠️ Firebase sync failed, message remains in local storage:", syncError);
          setIsOfflineMode(true);
          // Message remains in local storage and will be synced when online
        }
      } else if (isOfflineMode) {
        console.log("📱 Offline mode: Message saved locally, will sync when online");
      }

      Alert.alert("Success", `Message forwarded to ${targetChatName}`);
    } catch (error) {
      console.error("❌ Error forwarding message:", error);
      Alert.alert("Error", "Failed to forward message");
    }
  };

  // Location sharing functionality removed to avoid Google Maps API costs

  // Contact Sharing Functions
  const shareContact = async () => {
    try {
      const { status } = await Contacts.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant contacts permission to share contacts');
        return;
      }

      const { data } = await Contacts.getContactsAsync({
        fields: [Contacts.Fields.Name, Contacts.Fields.PhoneNumbers, Contacts.Fields.Emails],
        sort: Contacts.SortTypes.FirstName,
      });

      if (data.length > 0) {
        const contactOptions = data.slice(0, 5).map(contact => ({
          text: contact.name || 'Unknown',
          onPress: async () => {
            try {
              const messageData: Partial<Message> = {
                senderId: currentUser!.uid,
                timestamp: serverTimestamp(),
                type: "contact",
                contact: {
                  name: contact.name || 'Unknown',
                  phoneNumbers: contact.phoneNumbers?.map(p => p.number).filter((num): num is string => Boolean(num)) || [],
                  emails: contact.emails?.map(e => e.email).filter((email): email is string => Boolean(email)) || [],
                },
              };

              if (replyingTo) {
                messageData.replyTo = {
                  messageId: replyingTo.id,
                  text: replyingTo.text || "Media",
                  senderName: replyingTo.senderId,
                  type: replyingTo.type,
                };
              }

              await addDoc(collection(db, `chats/${chatId}/messages`), messageData);
              setReplyingTo(null);
              Alert.alert('Success', 'Contact shared successfully!');
            } catch (error) {
              console.error('Error sharing contact:', error);
              Alert.alert('Error', 'Failed to share contact');
            }
          }
        }));

        Alert.alert(
          'Share Contact',
          'Select a contact to share:',
          [
            { text: 'Cancel', style: 'cancel' },
            ...contactOptions,
          ]
        );
      } else {
        Alert.alert('No Contacts');
      }
    } catch (error) {
      console.error('Error accessing contacts:', error);
      Alert.alert('Error', 'Failed to access contacts');
    }
  };

  // Voice Recording Functions
  const startRecording = async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please grant microphone permission to record voice messages",
        );
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY,
      );

      setRecording(newRecording);
      setIsRecording(true);
      Vibration.vibrate(50); // Haptic feedback
    } catch (error: any) {
      errorHandlingService.handleError(error, 'Start Recording');
      Alert.alert("Recording Error", "Failed to start recording. Please try again.");
      setIsRecording(false);
      setRecording(null);
    }
  };

  const stopRecording = async () => {
    if (!recording) return;

    try {
      setIsRecording(false);
      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();
      setRecording(null);

      if (uri) {
        // Validate recording duration (minimum 1 second)
        if (recordingDuration < 1) {
          Alert.alert("Recording Too Short", "Please record for at least 1 second");
          return;
        }

        // Check if user is blocked from sending voice messages
        if (isBlockedByPartner) {
          Alert.alert("Action Restricted", "You cannot send voice messages to this chat.");
          return;
        }

        // Send voice message
        await sendVoiceMessage(uri);
      }

      Vibration.vibrate(50); // Haptic feedback
    } catch (error) {
      console.error("Failed to stop recording:", error);
      setIsRecording(false);
      setRecording(null);
      setRecordingDuration(0);
      errorHandlingService.handleError(error as Error, 'Stop Recording');
      Alert.alert("Recording Error", "Failed to stop recording. Please try again.");
    }
  };

  const sendVoiceMessage = async (audioUri: string) => {
    if (!currentUser) return;

    try {
      // Local-first approach (WhatsApp-style) - save locally immediately
      const fileName = `voice_${Date.now()}_${currentUser.uid}.m4a`;
      console.log(`📁 Generated voice file name: ${fileName}`);

      // Save voice message locally first for immediate UI update
      const localId = await messageSyncService.saveMediaMessageLocally(
        chatId,
        currentUser.uid,
        'voice',
        audioUri, // Use local URI initially
        {
          fileName,
          duration: recordingDuration,
          replyTo: replyingTo ? {
            messageId: replyingTo.id,
            text: replyingTo.text || "Media",
            senderName: replyingTo.senderId === currentUser.uid ? "You" : partnerName,
            type: replyingTo.type,
          } : undefined,
        }
      );

      // Create message for immediate UI update
      const newMsg: Message = {
        id: localId,
        senderId: currentUser.uid,
        timestamp: new Date(),
        status: "sent",
        type: "voice",
        mediaUrl: audioUri,
        duration: recordingDuration,
        fileName,
        replyTo: replyingTo ? {
          messageId: replyingTo.id,
          text: replyingTo.text || "Media",
          senderName: replyingTo.senderId === currentUser.uid ? "You" : partnerName,
          type: replyingTo.type,
        } : undefined,
      };

      // Update messages immediately for better UX (like WhatsApp)
      dispatchMessage({ type: 'ADD_MESSAGE', payload: newMsg });
      setReplyingTo(null);

      console.log('✅ Voice message saved locally and queued for sync:', localId);

      // Background upload to Firebase Storage (when online)
      if (USE_FIREBASE && storage && !isOfflineMode) {
        try {
          // Track upload progress
          setUploadingMessages(prev => new Set(prev).add(localId));

          console.log('🔄 Uploading voice message to Firebase Storage...');
          const uploadedAudioUrl = await uploadToFirebaseStorage(
            audioUri,
            chatId,
            currentUser.uid,
            'voice',
            fileName,
            {
              'duration': recordingDuration.toString(),
              'originalFileName': fileName
            }
          );

          console.log('✅ Voice message uploaded to Firebase Storage:', uploadedAudioUrl);

          // Update message with Firebase Storage URL
          dispatchMessage({
            type: 'UPDATE_MESSAGE',
            payload: {
              id: localId,
              updates: {
                mediaUrl: uploadedAudioUrl,
                status: 'delivered' // Mark as delivered once uploaded
              }
            }
          });

          // Remove from uploading set
          setUploadingMessages(prev => {
            const newSet = new Set(prev);
            newSet.delete(localId);
            return newSet;
          });

          // Also update in Firebase Firestore with the storage URL
          if (db) {
            const { addDoc, collection, serverTimestamp } = await import('firebase/firestore');
            await addDoc(collection(db, `chats/${chatId}/messages`), {
              ...newMsg,
              mediaUrl: uploadedAudioUrl,
              timestamp: serverTimestamp(),
              status: 'delivered'
            });
            console.log('✅ Voice message metadata saved to Firestore');
          }

        } catch (uploadError) {
          console.warn("⚠️ Voice upload failed, keeping local copy:", uploadError);
          setIsOfflineMode(true);
          // Remove from uploading set
          setUploadingMessages(prev => {
            const newSet = new Set(prev);
            newSet.delete(localId);
            return newSet;
          });
          // Voice message remains in local storage with local URI for offline playback
          // Will be uploaded when back online
        }
      } else if (isOfflineMode) {
        console.log("📱 Offline mode: Voice message saved locally, will upload when online");
      }

      // Scroll to bottom
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error("❌ Error sending voice message:", error);
      Alert.alert("Error", "Failed to send voice message");
    }
  };

  // Play Voice Message
  const playVoiceMessage = async (uri: string, messageId: string) => {
    try {
      if (playingAudio === messageId) {
        // Stop current audio
        if (sound) {
          await sound.stopAsync();
          await sound.unloadAsync();
          setSound(null);
          setPlayingAudio(null);
        }
        return;
      }

      // Stop any currently playing audio
      if (sound) {
        await sound.stopAsync();
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri },
        { shouldPlay: true },
      );

      setSound(newSound);
      setPlayingAudio(messageId);

      newSound.setOnPlaybackStatusUpdate((status: any) => {
        if (status.didJustFinish) {
          setPlayingAudio(null);
          setSound(null);
        }
      });
    } catch (error) {
      console.error("Error playing voice message:", error);
      Alert.alert("Error", "Failed to play voice message");
    }
  };

  // Media Functions
  const pickImage = async () => {
    try {
      console.log('📸 [DEBUG] Starting image picker...');
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      console.log('📸 [DEBUG] Media library permission status:', status);
      if (status !== "granted") {
        console.warn('⚠️ [DEBUG] Media library permission not granted');
        Alert.alert(
          "Permission Required",
          "Please grant photo library permission",
        );
        return;
      }

      console.log('📸 [DEBUG] Launching image library...');
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ["images"],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      console.log('📸 [DEBUG] Image picker result:', {
        canceled: result.canceled,
        hasAssets: (result.assets?.length || 0) > 0,
        firstAsset: result.assets?.[0] ? {
          uri: result.assets[0].uri,
          width: result.assets[0].width,
          height: result.assets[0].height,
          fileSize: result.assets[0].fileSize
        } : null
      });

      if (!result.canceled && result.assets[0]) {
        console.log('📸 [DEBUG] Calling sendMediaWithCaption for image...');
        await sendMediaWithCaption(result.assets[0].uri, "image");
      } else {
        console.log('📸 [DEBUG] Image picker was canceled or no assets');
      }

      setShowAttachmentMenu(false);
    } catch (error) {
      console.error("❌ [DEBUG] Error picking image:", error);
      Alert.alert("Error", "Failed to pick image");
    }
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Alert.alert("Permission Required", "Please grant camera permission");
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        await sendMediaWithCaption(result.assets[0].uri, "image");
      }

      setShowAttachmentMenu(false);
    } catch (error) {
      console.error("Error taking photo:", error);
      Alert.alert("Error", "Failed to take photo");
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: "*/*",
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        await sendMediaWithCaption(asset.uri, "document", asset.name, asset.size);
      }

      setShowAttachmentMenu(false);
    } catch (error) {
      console.error("Error picking document:", error);
      Alert.alert("Error", "Failed to pick document");
    }
  };

  const sendMediaMessage = async (
    mediaUri: string,
    type: "image" | "video" | "document",
    fileName?: string,
    fileSize?: number,
    caption?: string,
  ) => {
    console.log('🎬 [DEBUG] sendMediaMessage called with:', {
      mediaUri,
      type,
      fileName,
      fileSize,
      caption,
      currentUser: !!currentUser,
      chatId
    });

    if (!currentUser) {
      console.error('❌ [DEBUG] No current user found');
      return;
    }

    // Check if user is blocked from sending media
    if (isBlockedByPartner) {
      console.warn('⚠️ [DEBUG] User is blocked from sending media');
      Alert.alert("Action Restricted", "You cannot send media to this chat.");
      return;
    }

    // Validate file size (50MB limit)
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    if (fileSize && fileSize > MAX_FILE_SIZE) {
      Alert.alert("File Too Large", "File size must be less than 50MB");
      return;
    }

    // Validate caption if provided
    if (caption) {
      const validation = validateMessageContent(caption);
      if (!validation.isValid) {
        Alert.alert('Error', validation.error || 'Invalid caption content');
        return;
      }
    }

    try {
      // Save media message locally first
      const localId = await messageSyncService.saveMediaMessageLocally(
        chatId,
        currentUser.uid,
        type,
        mediaUri,
        {
          fileName,
          fileSize,
          text: caption,
          replyTo: replyingTo ? {
            messageId: replyingTo.id,
            text: replyingTo.text || "Media",
            senderName: replyingTo.senderId === currentUser.uid ? "You" : partnerName,
            type: replyingTo.type,
          } : undefined,
        }
      );

      // Create message for immediate UI update
      const newMsg: Message = {
        id: localId,
        text: caption,
        senderId: currentUser.uid,
        timestamp: new Date(),
        status: "sent",
        type,
        mediaUrl: mediaUri,
        fileName,
        fileSize,
        replyTo: replyingTo ? {
          messageId: replyingTo.id,
          text: replyingTo.text || "Media",
          senderName: replyingTo.senderId === currentUser.uid ? "You" : partnerName,
          type: replyingTo.type,
        } : undefined,
      };

      // Update messages immediately for better UX
      dispatchMessage({ type: 'ADD_MESSAGE', payload: newMsg });
      setReplyingTo(null);

      console.log('✅ Media message saved locally and queued for sync:', localId);

      // Background upload to Firebase Storage (when online)
      if (USE_FIREBASE && storage && !isOfflineMode) {
        try {
          // Track upload progress
          setUploadingMessages(prev => new Set(prev).add(localId));

          console.log(`🔄 Uploading ${type} to Firebase Storage...`);
          const uploadedMediaUrl = await uploadToFirebaseStorage(
            mediaUri,
            chatId,
            currentUser.uid,
            type,
            fileName || `${type}_${Date.now()}_${currentUser.uid}`,
            {
              'originalFileName': fileName || 'unknown',
              'fileSize': fileSize?.toString() || '0',
              'caption': caption || ''
            }
          );

          console.log(`✅ ${type} uploaded to Firebase Storage:`, uploadedMediaUrl);

          // Update message with Firebase Storage URL
          dispatchMessage({
            type: 'UPDATE_MESSAGE',
            payload: {
              id: localId,
              updates: {
                mediaUrl: uploadedMediaUrl,
                status: 'delivered' // Mark as delivered once uploaded
              }
            }
          });

          // Remove from uploading set
          setUploadingMessages(prev => {
            const newSet = new Set(prev);
            newSet.delete(localId);
            return newSet;
          });

          // Also update in Firebase Firestore with the storage URL
          if (db) {
            const { addDoc, collection, serverTimestamp } = await import('firebase/firestore');
            await addDoc(collection(db, `chats/${chatId}/messages`), {
              ...newMsg,
              mediaUrl: uploadedMediaUrl,
              timestamp: serverTimestamp(),
              status: 'delivered'
            });
            console.log(`✅ ${type} message metadata saved to Firestore`);
          }

        } catch (uploadError) {
          console.warn(`⚠️ ${type} upload failed, keeping local copy:`, uploadError);
          setIsOfflineMode(true);
          // Remove from uploading set
          setUploadingMessages(prev => {
            const newSet = new Set(prev);
            newSet.delete(localId);
            return newSet;
          });
          // Media remains in local storage with local URI for offline viewing
          // Will be uploaded when back online
        }
      } else if (isOfflineMode) {
        console.log(`📱 Offline mode: ${type} saved locally, will upload when online`);
      }

    } catch (error) {
      console.error("Error sending media message:", error);
      Alert.alert("Error", "Failed to send media");
    }
  };

  // Send media with caption prompt
  const sendMediaWithCaption = async (
    mediaUri: string,
    type: "image" | "video" | "document",
    fileName?: string,
    fileSize?: number,
  ) => {
    console.log('📝 [DEBUG] sendMediaWithCaption called with:', {
      mediaUri,
      type,
      fileName,
      fileSize,
      isBlockedByPartner
    });

    // Check if user is blocked from sending media
    if (isBlockedByPartner) {
      console.warn('⚠️ [DEBUG] User is blocked from sending media');
      Alert.alert("Action Restricted", "You cannot send media to this chat.");
      return;
    }

    // Show caption input for images and videos
    if (type === "image" || type === "video") {
      console.log('📝 [DEBUG] Showing caption prompt for', type);
      Alert.prompt(
        "Add Caption",
        `Add a caption to your ${type} (optional):`,
        [
          {
            text: "Skip",
            style: "cancel",
            onPress: () => {
              console.log('📝 [DEBUG] User skipped caption, sending without caption');
              sendMediaMessage(mediaUri, type, fileName, fileSize);
            }
          },
          {
            text: "Send",
            onPress: (caption) => {
              console.log('📝 [DEBUG] User provided caption:', caption);
              sendMediaMessage(mediaUri, type, fileName, fileSize, caption || undefined);
            }
          },
        ],
        "plain-text",
        "",
        "default"
      );
    } else {
      // Send documents without caption prompt
      console.log('📝 [DEBUG] Sending document without caption prompt');
      await sendMediaMessage(mediaUri, type, fileName, fileSize);
    }
  };

  // Reaction Functions
  const addReaction = async (messageId: string, emoji: string) => {
    if (!currentUser) return;

    try {
      const messageRef = doc(db, `chats/${chatId}/messages/${messageId}`);
      const message = messages.find((m) => m.id === messageId);

      if (message) {
        const currentReactions = message.reactions || {};
        const updatedReactions = { ...currentReactions };

        // Toggle reaction
        if (updatedReactions[currentUser.uid] === emoji) {
          delete updatedReactions[currentUser.uid];
        } else {
          updatedReactions[currentUser.uid] = emoji;
        }

        await updateDoc(messageRef, {
          reactions: updatedReactions,
        });
      }

      setShowMessageActions(false);
    } catch (error) {
      console.error("Error adding reaction:", error);
      Alert.alert("Error", "Failed to add reaction");
    }
  };

  // Message Actions
  const handleMessageLongPress = (message: Message) => {
    if (message.senderId === currentUser?.uid || message.type === "text") {
      setSelectedMessage(message);
      setShowMessageActions(true);
      Vibration.vibrate(50); // Haptic feedback
    }
  };

  const handleReply = (message: Message) => {
    setReplyingTo(message);
    setShowMessageActions(false);
    textInputRef.current?.focus();
  };

  const handleEdit = (message: Message) => {
    if (message.senderId === currentUser?.uid && message.type === "text") {
      setEditingMessage(message);
      setNewMessage(message.text || "");
      setShowMessageActions(false);
      textInputRef.current?.focus();
    }
  };

  const handleDelete = (message: Message) => {
    Alert.alert(
      "Delete Message",
      "Choose delete option:",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete for Me",
          style: "default",
          onPress: () => handleDeleteMessage(message.id, 'self'),
        },
        {
          text: "Delete for Everyone",
          style: "destructive",
          onPress: () => handleDeleteMessage(message.id, 'everyone'),
        },
      ],
    );
  };

  const handleShare = async (message: Message) => {
    try {
      // Check if user is blocked from sharing
      if (isBlockedByPartner) {
        Alert.alert("Action Restricted", "You cannot share messages from this chat.");
        setShowMessageActions(false);
        return;
      }

      const shareContent = message.text || "Shared media from IraChat";

      // Show share options
      Alert.alert(
        "Share Message",
        "Choose how to share this message:",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Copy to Clipboard",
            onPress: async () => {
              try {
                const shareContent = message.text || "Shared media from IraChat";
                await Clipboard.setStringAsync(shareContent);
                Alert.alert("Success", "Message copied to clipboard!");
              } catch (error) {
                console.error("Error copying to clipboard:", error);
                Alert.alert("Error", "Failed to copy message");
              }
            },
          },
          {
            text: "Share Externally",
            onPress: () => {
              // For now, just show the share content
              Alert.alert("Share Content", shareContent);
            },
          },
        ]
      );
    } catch (error) {
      console.error("❌ Error sharing message:", error);
      Alert.alert("Error", "Failed to share message");
    }
    setShowMessageActions(false);
  };

  const handlePin = async (message: Message) => {
    if (!currentUser) return;

    try {
      const messageRef = doc(db, `chats/${chatId}/messages/${message.id}`);
      const isPinned = message.isPinned || false;

      // Toggle pin status
      await updateDoc(messageRef, {
        isPinned: !isPinned,
        pinnedAt: !isPinned ? serverTimestamp() : null,
        pinnedBy: !isPinned ? currentUser.uid : null,
      });

      // Update local state
      dispatchMessage({
        type: 'UPDATE_MESSAGE',
        payload: {
          id: message.id,
          updates: { isPinned: !isPinned }
        }
      });

      // Update pinned messages list
      if (!isPinned) {
        setPinnedMessages(prev => [...prev, { ...message, isPinned: true }]);
        Alert.alert("Success", "Message pinned successfully");
      } else {
        setPinnedMessages(prev => prev.filter(msg => msg.id !== message.id));
        Alert.alert("Success", "Message unpinned successfully");
      }

      setShowMessageActions(false);
    } catch (error) {
      console.error("❌ Error pinning message:", error);
      Alert.alert("Error", "Failed to pin message");
    }
  };

  // Load available chats for forwarding
  const loadAvailableChats = async () => {
    if (!currentUser) return;

    try {
      // Get user's chats from Firebase
      const chatsQuery = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', currentUser.uid)
      );

      const chatsSnapshot = await getDocs(chatsQuery);
      const chats = chatsSnapshot.docs.map(doc => {
        const data = doc.data();
        const otherParticipant = data.participants.find((p: string) => p !== currentUser.uid);

        return {
          id: doc.id,
          name: data.chatName || data.participantNames?.[otherParticipant] || 'Unknown',
          avatar: data.participantAvatars?.[otherParticipant] || '',
          lastSeen: data.lastActivity?.toDate(),
        };
      }).filter(chat => chat.id !== chatId); // Exclude current chat

      setAvailableChats(chats);
    } catch (error) {
      console.error('❌ Error loading chats:', error);
      // Fallback to empty array
      setAvailableChats([]);
    }
  };

  const handleForward = (message: Message) => {
    // Set message to forward, load chats, and show modal
    setMessageToForward(message);
    loadAvailableChats();
    setShowForwardModal(true);
    setShowMessageActions(false);
  };

  const handleDownload = async (message: Message) => {
    if (!message.mediaUrl) {
      Alert.alert("Error", "No media to download");
      return;
    }

    try {
      // Check if user is blocked from downloading
      if (isBlockedByPartner) {
        Alert.alert("Action Restricted", "You cannot download media from this chat.");
        setShowMessageActions(false);
        return;
      }

      Alert.alert(
        "Download Media",
        "Choose download option:",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Save to Gallery",
            onPress: async () => {
              try {
                // Request media library permissions
                const MediaLibrary = await import("expo-media-library");
                const { status } = await MediaLibrary.requestPermissionsAsync();

                if (status !== 'granted') {
                  Alert.alert("Permission Required", "Please grant media library permissions to save media.");
                  return;
                }

                // Download and save media
                const { downloadAsync, documentDirectory } = await import("expo-file-system");
                const fileExtension = message.type === 'video' ? 'mp4' : 'jpg';
                const fileName = `IraChat_${message.id}_${Date.now()}.${fileExtension}`;
                const fileUri = `${documentDirectory}${fileName}`;

                await downloadAsync(message.mediaUrl!, fileUri);
                await MediaLibrary.saveToLibraryAsync(fileUri);

                Alert.alert("Success", "Media saved to your gallery!");
              } catch (error) {
                console.error("❌ Error downloading media:", error);
                Alert.alert("Error", "Failed to download media. Please try again.");
              }
            },
          },
          {
            text: "Share",
            onPress: () => {
              // Share the media URL
              Alert.alert("Share Media", `Media URL: ${message.mediaUrl}`);
            },
          },
        ]
      );
    } catch (error) {
      console.error("❌ Error in download handler:", error);
      Alert.alert("Error", "Failed to process download request");
    }
    setShowMessageActions(false);
  };

  const handleCopy = async (message: Message) => {
    if (message.text) {
      try {
        await Clipboard.setStringAsync(message.text);
        Alert.alert("Copied", "Message copied to clipboard");
      } catch (error) {
        console.error("Error copying message:", error);
        Alert.alert("Error", "Failed to copy message");
      }
    }
    setShowMessageActions(false);
  };

  // Enhanced emoji reactions with categories
  const commonEmojis = ["❤️", "😂", "😮", "😢", "😡", "👍", "👎", "🔥"];
  const extendedEmojis = {
    emotions: ["😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂", "🙂", "🙃", "😉", "😊", "😇", "🥰", "😍", "🤩", "😘", "😗", "😚", "😙", "😋", "😛", "😜", "🤪", "😝", "🤑", "🤗", "🤭", "🤫", "🤔", "🤐", "🤨", "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "🤥", "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕", "🤢", "🤮", "🤧", "🥵", "🥶", "🥴", "😵", "🤯", "🤠", "🥳", "😎", "🤓", "🧐"],
    gestures: ["👍", "👎", "👌", "🤌", "🤏", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉", "👆", "🖕", "👇", "☝️", "👋", "🤚", "🖐️", "✋", "🖖", "👏", "🙌", "🤲", "🤝", "🙏", "✍️", "💪", "🦾", "🦿", "🦵", "🦶"],
    hearts: ["❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖", "💘", "💝", "💟"],
    activities: ["⚽", "🏀", "🏈", "⚾", "🥎", "🎾", "🏐", "🏉", "🥏", "🎱", "🪀", "🏓", "🏸", "🏒", "🏑", "🥍", "🏏", "🪃", "🥅", "⛳", "🪁", "🏹", "🎣", "🤿", "🥊", "🥋", "🎽", "🛹", "🛷", "⛸️", "🥌", "🎿", "⛷️", "🏂", "🪂", "🏋️", "🤼", "🤸", "⛹️", "🤺", "🏌️", "🏇", "🧘", "🏄", "🏊", "🤽", "🚣", "🧗", "🚵", "🚴", "🏆", "🥇", "🥈", "🥉", "🏅", "🎖️", "🏵️", "🎗️"]
  };

  // Send typing indicator
  const sendTypingIndicator = useCallback(async (isTypingState: boolean) => {
    if (!currentUser || !chatId) return;

    try {
      await realTimeMessagingService.sendTypingIndicator(
        chatId,
        currentUser.uid,
        isTypingState
      );
    } catch (error) {
      console.error("❌ Error sending typing indicator:", error);
    }
  }, [currentUser, chatId]);

  // Handle typing state changes
  const handleTypingChange = useCallback((text: string) => {
    setNewMessage(text);

    // Update typing state based on text input
    const newTypingState = text.length > 0;
    if (newTypingState !== isTyping) {
      setIsTyping(newTypingState);
      sendTypingIndicator(newTypingState);
    }
  }, [isTyping, sendTypingIndicator]);

  // Debounced typing indicator
  const typingTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleTextChange = useCallback((text: string) => {
    // Input validation and sanitization
    if (typeof text !== 'string') {
      setNewMessage('');
      return;
    }

    // Limit message length for security and UX
    const maxLength = 5000;
    const sanitizedText = text.length > maxLength ? text.substring(0, maxLength) : text;

    // Use our enhanced typing change handler
    handleTypingChange(sanitizedText);

    // Clear previous typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Send typing indicator to other users (debounced)
    if (currentUser && chatId) {
      const isTypingNow = sanitizedText.length > 0;

      realTimeMessagingService.setTypingStatus(
        chatId,
        currentUser.uid,
        currentUser.displayName || currentUser.email || 'User',
        isTypingNow
      );

      // Clear typing indicator after 2 seconds of no typing
      if (isTypingNow) {
        typingTimeoutRef.current = setTimeout(() => {
          setIsTyping(false);
          if (currentUser && chatId) {
            realTimeMessagingService.setTypingStatus(
              chatId,
              currentUser.uid,
              currentUser.displayName || currentUser.email || 'User',
              false
            );
          }
        }, 2000);
      }
    }
  }, [currentUser, chatId, handleTypingChange]);

  // Cleanup all timeouts and resources on unmount
  useEffect(() => {
    return () => {
      // Cleanup typing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Cleanup search timeout
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      // Cleanup recording timer
      if (recordingTimer.current) {
        clearInterval(recordingTimer.current);
      }

      // Stop any playing audio
      if (sound) {
        sound.unloadAsync().catch(console.warn);
      }

      // Stop any ongoing recording
      if (recording) {
        recording.stopAndUnloadAsync().catch(console.warn);
      }
    };
  }, [sound, recording]);

  // Enhanced Search Functions with debouncing and error handling
  const searchTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const performMessageSearch = useCallback((query: string) => {
    // Update the search query state
    setSearchQuery(query);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (!query.trim()) {
      setSearchResults([]);
      setCurrentSearchIndex(0);
      setSearchHighlightedMessageId(null);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);

    // Debounced search with error handling
    searchTimeoutRef.current = setTimeout(() => {
      try {
        const normalizedQuery = query.toLowerCase().trim();
        const results = messages.filter(message => {
          // Safe search with null checks
          const textMatch = message.text?.toLowerCase().includes(normalizedQuery) || false;
          const fileNameMatch = message.fileName?.toLowerCase().includes(normalizedQuery) || false;
          return textMatch || fileNameMatch;
        });

        setSearchResults(results);
        setCurrentSearchIndex(0);

        if (results.length > 0) {
          setSearchHighlightedMessageId(results[0].id);
          // Safe scroll with error handling
          const messageIndex = messages.findIndex(m => m.id === results[0].id);
          if (messageIndex !== -1 && flatListRef.current) {
            try {
              flatListRef.current.scrollToIndex({
                index: messageIndex,
                animated: true,
                viewPosition: 0.5
              });
            } catch (scrollError) {
              console.warn("Search scroll error:", scrollError);
              // Fallback to scrollToEnd if scrollToIndex fails
              flatListRef.current.scrollToEnd({ animated: true });
            }
          }
        } else {
          setSearchHighlightedMessageId(null);
        }
      } catch (searchError) {
        console.error("Search error:", searchError);
        dispatchMessage({ type: 'SET_ERROR', payload: 'Search failed. Please try again.' });
      } finally {
        setIsSearching(false);
      }
    }, 300); // Debounce delay
  }, [messages]);

  // Cleanup search timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const navigateSearchResults = useCallback((direction: 'next' | 'prev') => {
    if (searchResults.length === 0) return;

    let newIndex = currentSearchIndex;
    if (direction === 'next') {
      newIndex = (currentSearchIndex + 1) % searchResults.length;
    } else {
      newIndex = currentSearchIndex === 0 ? searchResults.length - 1 : currentSearchIndex - 1;
    }

    setCurrentSearchIndex(newIndex);
    const targetMessage = searchResults[newIndex];

    if (!targetMessage) {
      console.warn("Target message not found in search results");
      return;
    }

    setSearchHighlightedMessageId(targetMessage.id);

    // Safe scroll to the message with error handling
    const messageIndex = messages.findIndex(m => m.id === targetMessage.id);
    if (messageIndex !== -1 && flatListRef.current) {
      try {
        flatListRef.current.scrollToIndex({
          index: messageIndex,
          animated: true,
          viewPosition: 0.5
        });
      } catch (scrollError) {
        console.warn("Search navigation scroll error:", scrollError);
        // Fallback: scroll to end if specific index fails
        try {
          flatListRef.current.scrollToEnd({ animated: true });
        } catch (fallbackError) {
          console.error("Fallback scroll also failed:", fallbackError);
        }
      }
    }
  }, [searchResults, currentSearchIndex, messages]);

  const closeSearch = () => {
    setShowMessageSearch(false);
    setSearchQuery("");
    setSearchResults([]);
    setCurrentSearchIndex(0);
    setSearchHighlightedMessageId(null);
  };

  // Enhanced Message Management Functions
  const handleEditMessage = async (messageId: string, newText: string) => {
    try {
      // Validate the edited message
      const validation = validateMessageContent(newText);
      if (!validation.isValid) {
        Alert.alert('Error', validation.error || 'Invalid message content');
        return;
      }

      // Update local state immediately for better UX
      dispatchMessage({
        type: 'UPDATE_MESSAGE',
        payload: {
          id: messageId,
          updates: { text: newText, isEdited: true, editedAt: new Date() }
        }
      });

      // Update in Firebase
      if (USE_FIREBASE) {
        const messageRef = doc(db, `chats/${chatId}/messages`, messageId);
        await updateDoc(messageRef, {
          text: newText,
          isEdited: true,
          editedAt: serverTimestamp()
        });
      }

      setEditingMessage(null);
      Alert.alert("Success", "Message updated successfully");
    } catch (error) {
      console.error("Error editing message:", error);
      Alert.alert("Error", "Failed to edit message");
      // Revert local changes on error
      loadMessages();
    }
  };

  const handleDeleteMessage = async (messageId: string, deleteType: 'self' | 'everyone') => {
    try {
      if (deleteType === 'everyone') {
        // Delete for everyone - remove from Firebase
        dispatchMessage({ type: 'DELETE_MESSAGE', payload: messageId });

        if (USE_FIREBASE) {
          const messageRef = doc(db, `chats/${chatId}/messages`, messageId);
          await deleteDoc(messageRef);
        }
      } else {
        // Delete for self - mark as deleted for current user
        dispatchMessage({
          type: 'UPDATE_MESSAGE',
          payload: {
            id: messageId,
            updates: {
              deletedFor: [...(messages.find(m => m.id === messageId)?.deletedFor || []), currentUser?.uid || '']
            }
          }
        });

        if (USE_FIREBASE) {
          const messageRef = doc(db, `chats/${chatId}/messages`, messageId);
          await updateDoc(messageRef, {
            deletedFor: arrayUnion(currentUser?.uid)
          });
        }
      }

      Alert.alert("Success", "Message deleted successfully");
    } catch (error) {
      console.error("Error deleting message:", error);
      Alert.alert("Error", "Failed to delete message");
    }
  };

  // Format duration for voice messages
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  // Get reaction summary
  const getReactionSummary = (reactions: { [userId: string]: string } = {}) => {
    const reactionCounts: { [emoji: string]: number } = {};
    Object.values(reactions).forEach((emoji) => {
      reactionCounts[emoji] = (reactionCounts[emoji] || 0) + 1;
    });

    return Object.entries(reactionCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([emoji, count]) => `${emoji}${count > 1 ? count : ""}`)
      .join(" ");
  };

  // Helper function to check if we need a date separator AFTER this message
  const shouldShowDateSeparator = (currentMessage: Message, nextMessage?: Message): boolean => {
    if (!nextMessage) return true; // Always show for last message

    const currentDate = currentMessage.timestamp?.toDate?.() || new Date(currentMessage.timestamp);
    const nextDate = nextMessage.timestamp?.toDate?.() || new Date(nextMessage.timestamp);

    // Show separator if next message is on a different day (end of current day)
    return currentDate.toDateString() !== nextDate.toDateString();
  };

  // Format date for separator (shows the date that just ended)
  const formatDateSeparator = (timestamp: any): string => {
    const date = timestamp?.toDate?.() || new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else if (date >= sevenDaysAgo) {
      // For messages within last 7 days, show day of week
      return date.toLocaleDateString('en-US', { weekday: 'long' });
    } else {
      // For messages older than 7 days, show full date
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
  };

  // Render Date Separator
  const renderDateSeparator = (timestamp: any) => (
    <View style={{
      alignItems: 'center',
      marginVertical: SPACING.md,
    }}>
      <View style={{
        backgroundColor: '#374151',
        paddingHorizontal: SPACING.sm,
        paddingVertical: SPACING.xs,
        borderRadius: BORDER_RADIUS.lg,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
        elevation: 2,
      }}>
        <Text style={{
          color: '#FFFFFF',
          fontSize: 12,
          fontWeight: '600',
          textAlign: 'center',
        }}>
          {formatDateSeparator(timestamp)}
        </Text>
      </View>

      {/* Horizontal dark border */}
      <View style={{
        position: 'absolute',
        top: '50%',
        left: 0,
        right: 0,
        height: 1,
        backgroundColor: '#374151',
        opacity: 0.3,
        zIndex: -1,
      }} />
    </View>
  );

  // Render Message Component
  const renderMessage = ({
    item: message,
    index,
  }: {
    item: Message;
    index: number;
  }) => {

    const isMyMessage = message.senderId === currentUser?.uid;
    const showAvatar =
      !isMyMessage &&
      (index === 0 || messages[index - 1]?.senderId !== message.senderId);
    const reactions = message.reactions || {};
    const hasReactions = Object.keys(reactions).length > 0;
    const showDateSeparator = shouldShowDateSeparator(message, messages[index + 1]);

    return (
      <View>
        <View style={{ marginVertical: SPACING.xs, paddingHorizontal: SPACING.md }}>
        {/* Reply Preview */}
        {message.replyTo && (
          <View
            style={{
              marginLeft: isMyMessage ? 50 : 0,
              marginRight: isMyMessage ? 0 : 50,
              marginBottom: SPACING.xs,
            }}
          >
            <View
              style={{
                backgroundColor: "#2A2A2A",
                borderLeftWidth: 3,
                borderLeftColor: "#4A90E2",
                paddingLeft: SPACING.sm,
                paddingVertical: SPACING.xs,
                borderRadius: BORDER_RADIUS.sm,
              }}
            >
              <Text
                style={{ fontSize: 12, color: "#4A90E2", fontWeight: "600" }}
              >
                {message.replyTo.senderName}
              </Text>
              <Text style={{ fontSize: 12, color: "#B0B0B0" }} numberOfLines={1}>
                {message.replyTo.text}
              </Text>
            </View>
          </View>
        )}

        <View
          style={{
            flexDirection: "row",
            justifyContent: isMyMessage ? "flex-end" : "flex-start",
            alignItems: "flex-end",
          }}
        >
          {/* Partner Avatar */}
          {showAvatar && !isMyMessage && (
            <Image
              source={{ uri: partnerAvatar }}
              style={{
                width: 32,
                height: 32,
                borderRadius: BORDER_RADIUS.md,
                marginRight: SPACING.sm,
              }}
            />
          )}

          {/* Message Bubble */}
          <Pressable
            onLongPress={() => handleMessageLongPress(message)}
            style={{
              maxWidth: screenWidth * 0.75,
              marginLeft: !isMyMessage && !showAvatar ? 40 : 0,
            }}
          >
            <View
              style={{
                backgroundColor: searchHighlightedMessageId === message.id
                  ? (isMyMessage ? "#ffd700" : "#fff3cd") // Gold highlight for search results
                  : selectedMessage?.id === message.id
                  ? (isMyMessage ? "#5a67d8" : "#e2e8f0") // Darker when selected
                  : (isMyMessage ? "#667eea" : "#f0f0f0"), // Normal colors
                borderRadius: BORDER_RADIUS.md,
                padding: SPACING.sm,
                borderBottomRightRadius: isMyMessage ? BORDER_RADIUS.sm : BORDER_RADIUS.md,
                borderBottomLeftRadius: isMyMessage ? BORDER_RADIUS.md : BORDER_RADIUS.sm,
                borderWidth: searchHighlightedMessageId === message.id ? 3 : selectedMessage?.id === message.id ? 2 : 0,
                borderColor: searchHighlightedMessageId === message.id ? "#ff6b35" : selectedMessage?.id === message.id ? "#4299e1" : "transparent",
              }}
            >
              {/* Forwarded Label */}
              {message.isForwarded && (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    marginBottom: SPACING.xs,
                  }}
                >
                  <Ionicons
                    name="arrow-forward"
                    size={12}
                    color={isMyMessage ? "#fff" : "#666"}
                  />
                  <Text
                    style={{
                      fontSize: 11,
                      color: isMyMessage ? "#fff" : "#666",
                      marginLeft: SPACING.xs,
                      fontStyle: "italic",
                    }}
                  >
                    Forwarded from {message.forwardedFrom}
                  </Text>
                </View>
              )}

              {/* Message Content */}
              {message.type === "text" && (
                <Text
                  style={{
                    color: isMyMessage ? "#fff" : "#000",
                    fontSize: 16,
                    lineHeight: 20,
                  }}
                >
                  {message.text}
                </Text>
              )}

              {/* Call Message */}
              {message.type === "call" && (
                <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                  <Ionicons
                    name={message.callType === 'video' ? 'videocam' : 'call'}
                    size={16}
                    color={isMyMessage ? "#FFFFFF" : "#667eea"}
                    style={{ marginRight: 8 }}
                  />
                  <Text
                    style={{
                      color: isMyMessage ? "#FFFFFF" : "#667eea",
                      fontSize: 14,
                      fontStyle: 'italic',
                    }}
                  >
                    {message.text}
                  </Text>
                  {message.callDuration && message.callDuration > 0 && (
                    <Text
                      style={{
                        color: isMyMessage ? "rgba(255,255,255,0.7)" : "rgba(102,126,234,0.7)",
                        fontSize: 12,
                        marginLeft: 8,
                      }}
                    >
                      ({Math.floor(message.callDuration / 60)}:{(message.callDuration % 60).toString().padStart(2, '0')})
                    </Text>
                  )}
                </View>
              )}

              {message.type === "image" && (
                <View>
                  <TouchableOpacity
                    onPress={() => {
                      setSelectedImage(message.mediaUrl || "");
                      setShowImageViewer(true);
                    }}
                  >
                    <Image
                      source={{ uri: message.mediaUrl }}
                      style={{
                        width: 200,
                        height: 200,
                        borderRadius: 8,
                      }}
                      resizeMode="cover"
                    />
                  </TouchableOpacity>
                  {message.text && (
                    <Text
                      style={{
                        marginTop: 8,
                        fontSize: 14,
                        color: isMyMessage ? "#FFFFFF" : "#000000",
                        backgroundColor: isMyMessage ? "rgba(255,255,255,0.1)" : "rgba(0,0,0,0.05)",
                        padding: 8,
                        borderRadius: 8,
                      }}
                    >
                      {message.text}
                    </Text>
                  )}
                </View>
              )}

              {message.type === "voice" && (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    minWidth: 150,
                  }}
                >
                  <TouchableOpacity
                    onPress={() =>
                      playVoiceMessage(message.mediaUrl || "", message.id)
                    }
                    style={{
                      width: 32,
                      height: 32,
                      borderRadius: 16,
                      backgroundColor: isMyMessage ? "#404040" : "#667eea", // Changed from white to dark grey
                      justifyContent: "center",
                      alignItems: "center",
                      marginRight: 8,
                    }}
                  >
                    <Ionicons
                      name={playingAudio === message.id ? "pause" : "play"}
                      size={16}
                      color={isMyMessage ? "#667eea" : "#fff"}
                    />
                  </TouchableOpacity>
                  <View style={{ flex: 1 }}>
                    <View
                      style={{
                        height: 2,
                        backgroundColor: isMyMessage
                          ? "rgba(255,255,255,0.3)"
                          : "rgba(102,126,234,0.3)",
                        borderRadius: 1,
                      }}
                    >
                      <View
                        style={{
                          height: 2,
                          backgroundColor: isMyMessage ? "#B0B0B0" : "#667eea", // Changed from white to light grey
                          borderRadius: 1,
                          width: "30%", // This would be dynamic based on playback progress
                        }}
                      />
                    </View>
                  </View>
                  <Text
                    style={{
                      color: isMyMessage ? "#fff" : "#666",
                      fontSize: 12,
                      marginLeft: 8,
                    }}
                  >
                    {formatDuration(message.duration || 0)}
                  </Text>
                </View>
              )}

              {message.type === "document" && (
                <View style={{ flexDirection: "row", alignItems: "center" }}>
                  <View
                    style={{
                      width: 40,
                      height: 40,
                      borderRadius: 20,
                      backgroundColor: isMyMessage ? "#404040" : "#667eea", // Changed from white to dark grey
                      justifyContent: "center",
                      alignItems: "center",
                      marginRight: 12,
                    }}
                  >
                    <Ionicons
                      name="document"
                      size={20}
                      color={isMyMessage ? "#667eea" : "#fff"}
                    />
                  </View>
                  <View style={{ flex: 1 }}>
                    <Text
                      style={{
                        color: isMyMessage ? "#fff" : "#000",
                        fontSize: 14,
                        fontWeight: "600",
                      }}
                      numberOfLines={1}
                    >
                      {message.fileName || "Document"}
                    </Text>
                    <Text
                      style={{
                        color: isMyMessage ? "rgba(255,255,255,0.8)" : "#666",
                        fontSize: 12,
                      }}
                    >
                      {message.fileSize
                        ? `${(message.fileSize / 1024).toFixed(1)} KB`
                        : "Unknown size"}
                    </Text>
                  </View>
                </View>
              )}

              {/* Location message rendering removed to avoid Google Maps API costs */}

              {message.type === "contact" && message.contact && (
                <View style={styles.contactMessageContainer}>
                  <View
                    style={[
                      styles.contactCard,
                      {
                        backgroundColor: isMyMessage
                          ? IRACHAT_COLORS.sentMessage
                          : IRACHAT_COLORS.receivedMessage,
                      }
                    ]}
                  >
                    <View style={styles.contactAvatar}>
                      <Ionicons
                        name="person"
                        size={ResponsiveScale.iconSize(20)}
                        color={IRACHAT_COLORS.textOnPrimary}
                      />
                    </View>
                    <View style={styles.contactInfo}>
                      <Text
                        style={[
                          styles.contactName,
                          {
                            color: isMyMessage ? IRACHAT_COLORS.textOnPrimary : IRACHAT_COLORS.text,
                          }
                        ]}
                      >
                        {message.contact.name}
                      </Text>
                      {message.contact.phoneNumbers.length > 0 && (
                        <Text
                          style={[
                            styles.contactPhone,
                            {
                              color: isMyMessage
                                ? `${IRACHAT_COLORS.textOnPrimary}CC`
                                : IRACHAT_COLORS.textSecondary,
                            }
                          ]}
                        >
                          📞 {message.contact.phoneNumbers[0]}
                        </Text>
                      )}
                      {message.contact.emails.length > 0 && (
                        <Text
                          style={{
                            color: isMyMessage ? "rgba(255,255,255,0.8)" : "#666",
                            fontSize: 12,
                          }}
                        >
                          ✉️ {message.contact.emails[0]}
                        </Text>
                      )}
                    </View>
                    <TouchableOpacity
                      onPress={() => {
                        Alert.alert(
                          "Add Contact",
                          `Add ${message.contact!.name} to your contacts?`,
                          [
                            { text: "Cancel", style: "cancel" },
                            { text: "Add", onPress: () => Alert.alert("Success", "Contact added!") }
                          ]
                        );
                      }}
                      style={{
                        backgroundColor: "#87CEEB",
                        paddingHorizontal: 12,
                        paddingVertical: 6,
                        borderRadius: 6,
                      }}
                    >
                      <Text style={{ color: "white", fontSize: 12, fontWeight: "600" }}>
                        Add
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}

              {/* Message Info */}
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginTop: 4,
                }}
              >
                <Text
                  style={{
                    fontSize: 11,
                    color: isMyMessage ? "rgba(255,255,255,0.8)" : "#999",
                  }}
                >
                  {message.timestamp
                    ?.toDate()
                    .toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  {message.isEdited && " • edited"}
                </Text>

                {isMyMessage && (
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    {uploadingMessages.has(message.id) && (
                      <View style={{
                        width: 12,
                        height: 12,
                        borderRadius: 6,
                        borderWidth: 1,
                        borderColor: '#667eea',
                        borderTopColor: 'transparent',
                        marginRight: 4,
                        // Add rotation animation here if needed
                      }} />
                    )}
                    <MessageStatusIndicator
                      status={message.status as any}
                      isMyMessage={isMyMessage}
                      size={14}
                    />
                  </View>
                )}
              </View>
            </View>

            {/* Reactions */}
            {hasReactions && (
              <View
                style={{
                  backgroundColor: "#404040", // Changed from white to dark grey
                  borderRadius: 12,
                  paddingHorizontal: 8,
                  paddingVertical: 2,
                  marginTop: 4,
                  alignSelf: isMyMessage ? "flex-end" : "flex-start",
                  borderWidth: 1,
                  borderColor: "#555", // Darker border
                }}
              >
                <Text style={{ fontSize: 12 }}>
                  {getReactionSummary(reactions)}
                </Text>
              </View>
            )}
          </Pressable>
        </View>

        {/* Date Separator - Shows at END of day's messages */}
        {showDateSeparator && renderDateSeparator(message.timestamp)}
      </View>
      </View>
    );
  };

  const IraChatWallpaper = require("./IraChatWallpaper").default;

  return (
    <View style={{ flex: 1, backgroundColor: "#87CEEB" }}>
      <StatusBar barStyle="dark-content" backgroundColor="#87CEEB" translucent={false} />
      {/* Status bar spacer */}
      <View style={{ height: statusBarHeight, backgroundColor: "#87CEEB" }} />

      <IraChatWallpaper opacity={0.1} overlayColor="#2D2D2D" overlayOpacity={0.3}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1, backgroundColor: "transparent" }}
        >
        {/* Header - Completely separated from status bar */}
        <View
          style={{
            flexDirection: "row",
            alignItems: "center",
            paddingHorizontal: ResponsiveSpacing.md,
            paddingVertical: ResponsiveSpacing.md,
            backgroundColor: "#2D2D2D", // Changed from white to dark grey
            borderBottomWidth: 1,
            borderBottomColor: "#404040", // Darker border for dark theme
            shadowColor: "#000",
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
            minHeight: ComponentSizes.headerHeight,
            paddingTop: DeviceInfo.isSmallPhone ? ResponsiveSpacing.sm : ResponsiveSpacing.md,
          }}
        >
        {/* Back Button */}
        <TouchableOpacity
          style={{ padding: 8, marginRight: ResponsiveSpacing.sm }}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#667eea" />
        </TouchableOpacity>

        {(() => {
          const Avatar = require("./Avatar").Avatar;
          return (
            <TouchableOpacity
              onPress={() => {
                navigationService.navigate(ROUTES.PROFILE.VIEW(partnerId), {
                  userName: partnerName,
                  userAvatar: partnerAvatar,
                  userPhone: "",
                  isIraChatUser: isIraChatUser.toString(),
                  isOnline: isOnline.toString(),
                  lastSeen: lastSeen?.toISOString() || "",
                });
              }}
              activeOpacity={0.7}
            >
              <Avatar
                name={partnerName}
                imageUrl={partnerAvatar}
                size={40}
                showOnlineStatus={isIraChatUser} // Only show online status for IraChat users
                isOnline={isOnline && isIraChatUser} // Only show as online if they're IraChat user and actually online
              />
            </TouchableOpacity>
          );
        })()}
        <View style={{ marginLeft: 12, flex: 1 }}>
          <Text style={{ fontSize: 18, fontWeight: "600", color: "#000" }}>
            {partnerName}
          </Text>
          <Text style={{ fontSize: 14, color: "#666" }}>
            {typingUsers.length > 0
              ? "typing..."
              : isIraChatUser
                ? (isOnline
                    ? "Online"
                    : lastSeen
                      ? `Last seen ${formatLastSeen(lastSeen)}`
                      : "I use IraChat" // Only shows for registered IraChat users
                  )
                : "Offline" // Non-IraChat users show as Offline
            }
          </Text>
          {isOfflineMode && (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <Text style={{ fontSize: 12, color: "#F59E0B", fontWeight: "500" }}>
                📱 Offline Mode - Messages saved locally
              </Text>
              <TouchableOpacity
                onPress={() => {
                  console.log('🔄 Manual retry of failed uploads...');
                  retryFailedUploads(chatId, currentUser?.uid || '', USE_FIREBASE, isOfflineMode, dispatchMessage);
                }}
                style={{
                  marginLeft: 8,
                  paddingHorizontal: 6,
                  paddingVertical: 2,
                  backgroundColor: '#F59E0B',
                  borderRadius: 4
                }}
              >
                <Text style={{ fontSize: 10, color: 'white', fontWeight: 'bold' }}>
                  RETRY
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Header Actions */}
        <TouchableOpacity
          style={{ padding: 8, marginRight: 8 }}
          onPress={async () => {
            try {
              await startCall(
                partnerId,
                partnerName,
                partnerAvatar,
                'video',
                chatId,
                currentUser
              );
            } catch (error: any) {
              errorHandlingService.handleError(error, 'Video Call Start');
              Alert.alert("Error", "Failed to start video call");
            }
          }}
        >
          <Ionicons name="videocam" size={24} color="#667eea" />
        </TouchableOpacity>
        <TouchableOpacity
          style={{ padding: 8 }}
          onPress={() => {
            // Toggle quick search
            if (showQuickSearch) {
              // Close quick search and clear results
              setShowQuickSearch(false);
              setSearchQuery("");
              setSearchResults([]);
              setSearchHighlightedMessageId(null);
              setIsSearching(false);
            } else {
              // Open quick search
              setShowQuickSearch(true);
            }
          }}
          onLongPress={() => {
            // Long press opens the full search modal
            setShowMessageSearch(true);
          }}
        >
          <Ionicons
            name={showQuickSearch ? "close" : "search"}
            size={24}
            color={showQuickSearch ? "#ff4444" : "#667eea"}
          />
        </TouchableOpacity>
        <TouchableOpacity
          style={{ padding: 8 }}
          onPress={() => setShowPinnedMessages(!showPinnedMessages)}
        >
          <Ionicons
            name="bookmark"
            size={24}
            color={pinnedMessages.length > 0 ? "#667eea" : "#ccc"}
          />
          {pinnedMessages.length > 0 && (
            <View style={{
              position: 'absolute',
              top: 4,
              right: 4,
              backgroundColor: '#ff4444',
              borderRadius: 8,
              minWidth: 16,
              height: 16,
              justifyContent: 'center',
              alignItems: 'center'
            }}>
              <Text style={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>
                {pinnedMessages.length}
              </Text>
            </View>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={{ padding: 8, marginRight: 8 }}
          onPress={async () => {
            try {
              await startCall(
                partnerId,
                partnerName,
                partnerAvatar,
                'voice',
                chatId,
                currentUser
              );
            } catch (error: any) {
              errorHandlingService.handleError(error, 'Voice Call Start');
              Alert.alert("Error", "Failed to start voice call");
            }
          }}
        >
          <Ionicons name="call" size={24} color="#667eea" />
        </TouchableOpacity>
        <TouchableOpacity
          style={{ padding: 8 }}
          onPress={handleBlockUser}
        >
          <Ionicons
            name={isBlocked ? "person-add" : "person-remove"}
            size={24}
            color={isBlocked ? "#10B981" : "#EF4444"}
          />
        </TouchableOpacity>
      </View>

      {/* Quick Search Input */}
      {showQuickSearch && (
        <View style={{
          backgroundColor: "#2D2D2D", // Changed from light grey to dark grey
          paddingHorizontal: ResponsiveSpacing.md,
          paddingVertical: ResponsiveSpacing.sm,
          borderBottomWidth: 1,
          borderBottomColor: "#404040", // Darker border
          flexDirection: "row",
          alignItems: "center",
        }}>
          <TextInput
            style={{
              flex: 1,
              backgroundColor: "#404040", // Changed from white to dark grey
              color: "#FFFFFF", // White text for visibility
              borderRadius: 20,
              paddingHorizontal: 15,
              paddingVertical: 8,
              fontSize: 16,
              borderWidth: 1,
              borderColor: "#555", // Darker border
            }}
            placeholder="Search messages..."
            value={searchQuery}
            onChangeText={performMessageSearch}
            autoFocus={true}
          />
          {isSearching && (
            <View style={{ marginLeft: 10 }}>
              <Text style={{ color: "#667eea", fontSize: 12 }}>Searching...</Text>
            </View>
          )}
          {searchResults.length > 0 && (
            <View style={{ marginLeft: 10, flexDirection: "row", alignItems: "center" }}>
              <TouchableOpacity
                onPress={() => navigateSearchResults('prev')}
                style={{ padding: 5, marginRight: 5 }}
              >
                <Ionicons name="chevron-up" size={20} color="#667eea" />
              </TouchableOpacity>
              <Text style={{ color: "#667eea", fontSize: 12, marginHorizontal: 5 }}>
                {currentSearchIndex + 1} of {searchResults.length}
              </Text>
              <TouchableOpacity
                onPress={() => navigateSearchResults('next')}
                style={{ padding: 5, marginLeft: 5 }}
              >
                <Ionicons name="chevron-down" size={20} color="#667eea" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}

      {/* Reply Bar */}
      {replyingTo && (
        <Animated.View
          style={{
            transform: [
              {
                translateY: replyAnimation.interpolate({
                  inputRange: [0, 1],
                  outputRange: [50, 0],
                }),
              },
            ],
            opacity: replyAnimation,
            backgroundColor: "#2A2A2A",
            padding: 12,
            borderTopWidth: 1,
            borderTopColor: "#404040",
          }}
        >
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <View style={{ flex: 1 }}>
              <Text
                style={{ fontSize: 12, color: "#4A90E2", fontWeight: "600" }}
              >
                Replying to{" "}
                {replyingTo.senderId === currentUser?.uid
                  ? "yourself"
                  : partnerName}
              </Text>
              <Text style={{ fontSize: 14, color: "#B0B0B0" }} numberOfLines={1}>
                {replyingTo.text || "Media message"}
              </Text>
            </View>
            <TouchableOpacity
              onPress={() => setReplyingTo(null)}
              style={{ padding: 4 }}
            >
              <Ionicons name="close" size={20} color="#B0B0B0" />
            </TouchableOpacity>
          </View>
        </Animated.View>
      )}

      {/* Error Display */}
      {messageState.error && (
        <View style={{
          backgroundColor: '#FEE2E2',
          borderColor: '#FECACA',
          borderWidth: 1,
          margin: 16,
          padding: 12,
          borderRadius: 8,
          flexDirection: 'row',
          alignItems: 'center'
        }}>
          <Ionicons name="warning" size={20} color="#DC2626" style={{ marginRight: 8 }} />
          <Text style={{ color: '#DC2626', flex: 1, fontSize: 14 }}>
            {messageState.error}
          </Text>
          <TouchableOpacity
            onPress={() => dispatchMessage({ type: 'SET_ERROR', payload: null })}
            style={{ padding: 4 }}
          >
            <Ionicons name="close" size={16} color="#DC2626" />
          </TouchableOpacity>
        </View>
      )}

      {/* Messages List */}
      {messageState.isLoading ? (
        <View style={{
          flex: 1,
          justifyContent: 'flex-end',
          alignItems: 'center',
          paddingBottom: 20,
          backgroundColor: 'transparent' // Remove white overlay
        }}>
          <View style={{
            backgroundColor: 'rgba(0,0,0,0.1)',
            paddingHorizontal: 16,
            paddingVertical: 8,
            borderRadius: 20,
            flexDirection: 'row',
            alignItems: 'center',
          }}>
            <ActivityIndicator size="small" color="#667eea" style={{ marginRight: 8 }} />
            <Text style={{ fontSize: 14, color: '#666', fontWeight: '500' }}>
              Loading messages...
            </Text>
          </View>
        </View>
      ) : (
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item) => item.id}
          renderItem={renderMessage}
          style={{ flex: 1 }}
          contentContainerStyle={{ paddingVertical: 8 }}
          showsVerticalScrollIndicator={false}
          onContentSizeChange={() => {
            setTimeout(() => {
              flatListRef.current?.scrollToEnd({ animated: false });
            }, 100);
          }}
          onScrollToIndexFailed={(info) => {
            // Handle scroll failures gracefully
            console.warn("Scroll to index failed:", info);
            setTimeout(() => {
              flatListRef.current?.scrollToEnd({ animated: true });
            }, 100);
          }}
        />
      )}

      {/* Typing Indicator */}
      <TypingIndicator
        typingUsers={typingUsers}
        isVisible={typingUsers.length > 0}
      />

      {/* Input Bar */}
      <View
        style={{
          backgroundColor: "#2D2D2D", // Changed from white to dark grey
          borderTopWidth: 1,
          borderTopColor: "#404040", // Darker border for dark theme
          paddingHorizontal: 16,
          paddingVertical: 12,
          paddingBottom: Platform.OS === "ios" ? 34 : 12,
        }}
      >
        <View style={{ flexDirection: "row", alignItems: "flex-end" }}>
          {/* Attachment Button */}
          <TouchableOpacity
            onPress={() => setShowAttachmentMenu(true)}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: "#f0f0f0",
              justifyContent: "center",
              alignItems: "center",
              marginRight: 8,
            }}
          >
            <Ionicons name="add" size={24} color="#667eea" />
          </TouchableOpacity>

          {/* Text Input Container */}
          <View
            style={{
              flex: 1,
              backgroundColor: "#f8f9fa",
              borderRadius: 20,
              borderWidth: 1,
              borderColor: "#e0e0e0",
              marginRight: 8,
              minHeight: 40,
              maxHeight: 120,
              justifyContent: "center",
            }}
          >
            <TextInput
              ref={textInputRef}
              placeholder={
                editingMessage ? "Edit message..." : "Type a message..."
              }
              placeholderTextColor="#999"
              value={newMessage}
              onChangeText={handleTextChange}
              multiline
              style={{
                paddingHorizontal: 16,
                paddingVertical: 10,
                fontSize: 16,
                color: "#000",
                textAlignVertical: "center",
              }}
            />
          </View>

          {/* Emoji Button */}
          <TouchableOpacity
            onPress={() => setShowEmojiPicker(true)}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: "#f0f0f0",
              justifyContent: "center",
              alignItems: "center",
              marginRight: 8,
            }}
          >
            <Ionicons name="happy" size={24} color="#667eea" />
          </TouchableOpacity>

          {/* Send/Voice Button */}
          {newMessage.trim() || editingMessage ? (
            <TouchableOpacity
              onPress={
                editingMessage
                  ? () => {
                      if (editingMessage && newMessage.trim()) {
                        handleEditMessage(editingMessage.id, newMessage);
                      }
                    }
                  : sendMessage
              }
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: "#667eea",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Ionicons
                name={editingMessage ? "checkmark" : "send"}
                size={20}
                color="white"
              />
            </TouchableOpacity>
          ) : (
            <Animated.View
              style={{ transform: [{ scale: recordingAnimation }] }}
            >
              <TouchableOpacity
                onPressIn={startRecording}
                onPressOut={stopRecording}
                style={{
                  width: 40,
                  height: 40,
                  borderRadius: 20,
                  backgroundColor: isRecording ? "#ff4444" : "#667eea",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Ionicons
                  name={isRecording ? "stop" : "mic"}
                  size={20}
                  color="white"
                />
              </TouchableOpacity>
            </Animated.View>
          )}
        </View>

        {/* Recording Indicator */}
        {isRecording && (
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "center",
              marginTop: 8,
              padding: 8,
              backgroundColor: "#4A2D2D", // Changed from light pink to dark red-grey
              borderRadius: 8,
            }}
          >
            <View
              style={{
                width: 8,
                height: 8,
                borderRadius: 4,
                backgroundColor: "#ff4444",
                marginRight: 8,
              }}
            />
            <Text style={{ color: "#ff4444", fontSize: 14, fontWeight: "600" }}>
              Recording... {formatDuration(recordingDuration)}
            </Text>
          </View>
        )}
      </View>

      {/* Attachment Menu Modal */}
      <Modal
        visible={showAttachmentMenu}
        transparent
        animationType="slide"
        onRequestClose={() => setShowAttachmentMenu(false)}
      >
        <Pressable
          style={{
            flex: 1,
            backgroundColor: "rgba(0,0,0,0.5)",
            justifyContent: "flex-end",
          }}
          onPress={() => setShowAttachmentMenu(false)}
        >
          <View
            style={{
              backgroundColor: "#2D2D2D", // Changed from white to dark grey
              borderTopLeftRadius: 20,
              borderTopRightRadius: 20,
              padding: 20,
            }}
          >
            <Text
              style={{
                fontSize: 18,
                fontWeight: "600",
                textAlign: "center",
                marginBottom: 20,
                color: "#000",
              }}
            >
              Send Media
            </Text>

            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-around",
                marginBottom: 20,
              }}
            >
              <TouchableOpacity
                onPress={takePhoto}
                style={{
                  alignItems: "center",
                  padding: 16,
                }}
              >
                <View
                  style={{
                    width: 60,
                    height: 60,
                    borderRadius: 30,
                    backgroundColor: "#667eea",
                    justifyContent: "center",
                    alignItems: "center",
                    marginBottom: 8,
                  }}
                >
                  <Ionicons name="camera" size={30} color="white" />
                </View>
                <Text style={{ fontSize: 12, color: "#666" }}>Camera</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={pickImage}
                style={{
                  alignItems: "center",
                  padding: 16,
                }}
              >
                <View
                  style={{
                    width: 60,
                    height: 60,
                    borderRadius: 30,
                    backgroundColor: "#10B981",
                    justifyContent: "center",
                    alignItems: "center",
                    marginBottom: 8,
                  }}
                >
                  <Ionicons name="images" size={30} color="white" />
                </View>
                <Text style={{ fontSize: 12, color: "#666" }}>Gallery</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={pickDocument}
                style={{
                  alignItems: "center",
                  padding: 16,
                }}
              >
                <View
                  style={{
                    width: 60,
                    height: 60,
                    borderRadius: 30,
                    backgroundColor: "#F59E0B",
                    justifyContent: "center",
                    alignItems: "center",
                    marginBottom: 8,
                  }}
                >
                  <Ionicons name="document" size={30} color="white" />
                </View>
                <Text style={{ fontSize: 12, color: "#666" }}>Document</Text>
              </TouchableOpacity>
            </View>

            {/* Second Row */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-around",
                paddingVertical: 20,
              }}
            >
              {/* Location sharing removed to avoid Google Maps API costs */}

              <TouchableOpacity
                onPress={() => {
                  setShowAttachmentMenu(false);
                  shareContact();
                }}
                style={{
                  alignItems: "center",
                  padding: 16,
                }}
              >
                <View
                  style={{
                    width: 60,
                    height: 60,
                    borderRadius: 30,
                    backgroundColor: "#87CEEB",
                    justifyContent: "center",
                    alignItems: "center",
                    marginBottom: 8,
                  }}
                >
                  <Ionicons name="person" size={30} color="white" />
                </View>
                <Text style={{ fontSize: 12, color: "#666" }}>Contact</Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  setShowAttachmentMenu(false);
                  Alert.alert('Coming Soon', 'Poll feature will be available in the next update');
                }}
                style={{
                  alignItems: "center",
                  padding: 16,
                }}
              >
                <View
                  style={{
                    width: 60,
                    height: 60,
                    borderRadius: 30,
                    backgroundColor: "#87CEEB",
                    justifyContent: "center",
                    alignItems: "center",
                    marginBottom: 8,
                  }}
                >
                  <Ionicons name="bar-chart" size={30} color="white" />
                </View>
                <Text style={{ fontSize: 12, color: "#666" }}>Poll</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Pressable>
      </Modal>

      {/* Message Actions Modal */}
      <Modal
        visible={showMessageActions}
        transparent
        animationType="fade"
        onRequestClose={() => setShowMessageActions(false)}
      >
        <Pressable
          style={{
            flex: 1,
            backgroundColor: "rgba(0,0,0,0.5)",
            justifyContent: "center",
            alignItems: "center",
          }}
          onPress={() => setShowMessageActions(false)}
        >
          <View
            style={{
              backgroundColor: "#2D2D2D", // Changed from white to dark grey
              borderRadius: 12,
              padding: 20,
              margin: 20,
              minWidth: 250,
            }}
          >
            {/* Reactions */}
            <View
              style={{
                flexDirection: "row",
                justifyContent: "space-around",
                marginBottom: 20,
                paddingVertical: 10,
              }}
            >
              {commonEmojis.map((emoji) => (
                <TouchableOpacity
                  key={emoji}
                  onPress={() =>
                    selectedMessage && addReaction(selectedMessage.id, emoji)
                  }
                  style={{
                    padding: 8,
                    borderRadius: 20,
                    backgroundColor: "#f8f9fa",
                  }}
                >
                  <Text style={{ fontSize: 20 }}>{emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>

            {/* Action Buttons */}
            <TouchableOpacity
              onPress={() => selectedMessage && handleReply(selectedMessage)}
              style={{
                flexDirection: "row",
                alignItems: "center",
                paddingVertical: 12,
                paddingHorizontal: 16,
              }}
            >
              <Ionicons name="arrow-undo" size={20} color="#667eea" />
              <Text style={{ marginLeft: 12, fontSize: 16, color: "#000" }}>
                Reply
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => selectedMessage && handlePin(selectedMessage)}
              style={{
                flexDirection: "row",
                alignItems: "center",
                paddingVertical: 12,
                paddingHorizontal: 16,
              }}
            >
              <Ionicons
                name={selectedMessage?.isPinned ? "bookmark" : "bookmark-outline"}
                size={20}
                color={selectedMessage?.isPinned ? "#F59E0B" : "#667eea"}
              />
              <Text style={{ marginLeft: 12, fontSize: 16, color: "#000" }}>
                {selectedMessage?.isPinned ? "Unpin" : "Pin"}
              </Text>
            </TouchableOpacity>

            {selectedMessage?.senderId === currentUser?.uid &&
              selectedMessage?.type === "text" && (
                <TouchableOpacity
                  onPress={() => selectedMessage && handleEdit(selectedMessage)}
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                  }}
                >
                  <Ionicons name="create" size={20} color="#667eea" />
                  <Text style={{ marginLeft: 12, fontSize: 16, color: "#000" }}>
                    Edit
                  </Text>
                </TouchableOpacity>
              )}

            <TouchableOpacity
              onPress={() => selectedMessage && handleShare(selectedMessage)}
              style={{
                flexDirection: "row",
                alignItems: "center",
                paddingVertical: 12,
                paddingHorizontal: 16,
              }}
            >
              <Ionicons name="share" size={20} color="#667eea" />
              <Text style={{ marginLeft: 12, fontSize: 16, color: "#000" }}>
                Share
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => selectedMessage && handleForward(selectedMessage)}
              style={{
                flexDirection: "row",
                alignItems: "center",
                paddingVertical: 12,
                paddingHorizontal: 16,
              }}
            >
              <Ionicons name="arrow-forward" size={20} color="#667eea" />
              <Text style={{ marginLeft: 12, fontSize: 16, color: "#000" }}>
                Forward
              </Text>
            </TouchableOpacity>

            {selectedMessage?.type === "text" && (
              <TouchableOpacity
                onPress={() => selectedMessage && handleCopy(selectedMessage)}
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                }}
              >
                <Ionicons name="copy" size={20} color="#667eea" />
                <Text style={{ marginLeft: 12, fontSize: 16, color: "#000" }}>
                  Copy
                </Text>
              </TouchableOpacity>
            )}

            {selectedMessage?.mediaUrl && (
              <TouchableOpacity
                onPress={() => selectedMessage && handleDownload(selectedMessage)}
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                }}
              >
                <Ionicons name="download" size={20} color="#667eea" />
                <Text style={{ marginLeft: 12, fontSize: 16, color: "#000" }}>
                  Download
                </Text>
              </TouchableOpacity>
            )}

            {selectedMessage?.senderId === currentUser?.uid && (
              <TouchableOpacity
                onPress={() => selectedMessage && handleDelete(selectedMessage)}
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                }}
              >
                <Ionicons name="trash" size={20} color="#ff4444" />
                <Text
                  style={{ marginLeft: 12, fontSize: 16, color: "#ff4444" }}
                >
                  Delete
                </Text>
              </TouchableOpacity>
            )}
          </View>
        </Pressable>
      </Modal>

      {/* Image Viewer Modal */}
      <Modal
        visible={showImageViewer}
        transparent
        animationType="fade"
        onRequestClose={() => setShowImageViewer(false)}
      >
        <Pressable
          style={{
            flex: 1,
            backgroundColor: "rgba(0,0,0,0.9)",
            justifyContent: "center",
            alignItems: "center",
          }}
          onPress={() => setShowImageViewer(false)}
        >
          <TouchableOpacity
            style={{
              position: "absolute",
              top: 50,
              right: 20,
              zIndex: 1,
              padding: 10,
            }}
            onPress={() => setShowImageViewer(false)}
          >
            <Ionicons name="close" size={30} color="white" />
          </TouchableOpacity>

          {selectedImage && (
            <Image
              source={{ uri: selectedImage }}
              style={{
                width: screenWidth,
                height: screenWidth,
              }}
              resizeMode="contain"
            />
          )}
        </Pressable>
      </Modal>

      {/* Message Search Modal */}
      <MessageSearch
        visible={showMessageSearch}
        onClose={closeSearch}

        onMessageSelect={(messageId) => {
          // Set the highlighted message for visual feedback
          setSearchHighlightedMessageId(messageId);

          // Scroll to selected message
          const messageIndex = messages.findIndex(msg => msg.id === messageId);
          if (messageIndex !== -1 && flatListRef.current) {
            flatListRef.current.scrollToIndex({
              index: messageIndex,
              animated: true,
              viewPosition: 0.5, // Center the message
            });
          }

          // Close search modal but keep highlighting
          setShowMessageSearch(false);

          // Clear highlight after 3 seconds
          setTimeout(() => {
            setSearchHighlightedMessageId(null);
          }, 3000);
        }}
      />

      {/* Emoji Picker Modal */}
      <Modal
        visible={showEmojiPicker}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowEmojiPicker(false)}
      >
        <View style={{ flex: 1, backgroundColor: '#2D2D2D' }}> {/* Changed from white to dark grey */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: ResponsiveSpacing.md,
            borderBottomWidth: 1,
            borderBottomColor: '#e0e0e0'
          }}>
            <Text style={{ fontSize: ResponsiveTypography.fontSize.lg, fontWeight: 'bold' }}>
              Choose Emoji
            </Text>
            <TouchableOpacity onPress={() => setShowEmojiPicker(false)}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>
          <View style={{ flex: 1, padding: ResponsiveSpacing.md }}>
            <View style={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'space-around'
            }}>
              {[...commonEmojis, ...extendedEmojis.emotions.slice(0, 8)].map((emoji) => (
                <TouchableOpacity
                  key={emoji}
                  style={{
                    padding: ResponsiveSpacing.md,
                    margin: ResponsiveSpacing.xs,
                    borderRadius: ResponsiveScale.borderRadius(8),
                    backgroundColor: '#f5f5f5'
                  }}
                  onPress={() => {
                    setNewMessage(prev => prev + emoji);
                    setShowEmojiPicker(false);
                  }}
                >
                  <Text style={{ fontSize: DeviceInfo.isSmallPhone ? 24 : 32 }}>{emoji}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </Modal>

      {/* Pinned Messages Modal */}
      {showPinnedMessages && (
        <Modal
          visible={showPinnedMessages}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowPinnedMessages(false)}
        >
          <View style={{ flex: 1, backgroundColor: '#2D2D2D' }}> {/* Changed from white to dark grey */}
            <View style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: ResponsiveSpacing.md,
              borderBottomWidth: 1,
              borderBottomColor: '#e0e0e0'
            }}>
              <Text style={{ fontSize: ResponsiveTypography.fontSize.lg, fontWeight: 'bold' }}>
                Pinned Messages ({pinnedMessages.length})
              </Text>
              <TouchableOpacity onPress={() => setShowPinnedMessages(false)}>
                <Ionicons name="close" size={24} color="#666" />
              </TouchableOpacity>
            </View>
            <FlatList
              data={pinnedMessages}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={{
                    padding: ResponsiveSpacing.md,
                    borderBottomWidth: 1,
                    borderBottomColor: '#f0f0f0'
                  }}
                  onPress={() => {
                    // Scroll to pinned message
                    const messageIndex = messages.findIndex(msg => msg.id === item.id);
                    if (messageIndex !== -1 && flatListRef.current) {
                      flatListRef.current.scrollToIndex({
                        index: messageIndex,
                        animated: true,
                        viewPosition: 0.5,
                      });
                    }
                    setShowPinnedMessages(false);
                  }}
                >
                  <Text style={{ fontSize: ResponsiveTypography.fontSize.sm }}>
                    {item.text || 'Media message'}
                  </Text>
                  <Text style={{
                    fontSize: ResponsiveTypography.fontSize.xs,
                    color: '#666',
                    marginTop: ResponsiveSpacing.xs
                  }}>
                    {item.timestamp?.toDate?.()?.toLocaleDateString() || 'Unknown date'}
                  </Text>
                </TouchableOpacity>
              )}
              ListEmptyComponent={
                <View style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                  padding: ResponsiveSpacing.xl
                }}>
                  <Ionicons name="bookmark-outline" size={48} color="#ccc" />
                  <Text style={{
                    fontSize: ResponsiveTypography.fontSize.md,
                    color: '#666',
                    marginTop: ResponsiveSpacing.md,
                    textAlign: 'center'
                  }}>
                    No pinned messages yet
                  </Text>
                </View>
              }
            />
          </View>
        </Modal>
      )}

      {/* Forward Message Modal */}
      <Modal
        visible={showForwardModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowForwardModal(false)}
      >
        <View style={{ flex: 1, backgroundColor: '#2D2D2D' }}> {/* Changed from white to dark grey */}
          <View style={{
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: ResponsiveSpacing.md,
            borderBottomWidth: 1,
            borderBottomColor: '#e0e0e0'
          }}>
            <Text style={{ fontSize: ResponsiveTypography.fontSize.lg, fontWeight: 'bold' }}>
              Forward Message
            </Text>
            <TouchableOpacity onPress={() => setShowForwardModal(false)}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={{ flex: 1, padding: ResponsiveSpacing.md }}>
            {/* Message Preview */}
            {messageToForward && (
              <View style={{
                backgroundColor: '#f5f5f5',
                padding: ResponsiveSpacing.md,
                borderRadius: ResponsiveScale.borderRadius(8),
                marginBottom: ResponsiveSpacing.md
              }}>
                <Text style={{ fontSize: ResponsiveTypography.fontSize.sm, color: '#666' }}>
                  Forwarding:
                </Text>
                <Text style={{ fontSize: ResponsiveTypography.fontSize.md, marginTop: ResponsiveSpacing.xs }}>
                  {messageToForward.text || `${messageToForward.type} message`}
                </Text>
              </View>
            )}

            {/* Available Chats List */}
            <Text style={{ fontSize: ResponsiveTypography.fontSize.md, fontWeight: 'bold', marginBottom: ResponsiveSpacing.md }}>
              Select Chat:
            </Text>

            {availableChats.length > 0 ? (
              <FlatList
                data={availableChats}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      padding: ResponsiveSpacing.md,
                      backgroundColor: '#f8f9fa',
                      borderRadius: ResponsiveScale.borderRadius(8),
                      marginBottom: ResponsiveSpacing.sm
                    }}
                    onPress={async () => {
                      if (messageToForward) {
                        try {
                          await forwardMessage(messageToForward, item.id, item.name);
                          setShowForwardModal(false);
                          setMessageToForward(null);
                          Alert.alert("Success", `Message forwarded to ${item.name}`);
                        } catch (error) {
                          console.error("Error forwarding message:", error);
                          Alert.alert("Error", "Failed to forward message");
                        }
                      }
                    }}
                  >
                    <View style={{
                      width: 40,
                      height: 40,
                      borderRadius: 20,
                      backgroundColor: '#667eea',
                      justifyContent: 'center',
                      alignItems: 'center',
                      marginRight: ResponsiveSpacing.md
                    }}>
                      {item.avatar ? (
                        <Image source={{ uri: item.avatar }} style={{ width: 40, height: 40, borderRadius: 20 }} />
                      ) : (
                        <Text style={{ color: 'white', fontWeight: 'bold' }}>
                          {item.name.charAt(0).toUpperCase()}
                        </Text>
                      )}
                    </View>
                    <View style={{ flex: 1 }}>
                      <Text style={{ fontSize: ResponsiveTypography.fontSize.md, fontWeight: '500' }}>
                        {item.name}
                      </Text>
                      {item.lastSeen && (
                        <Text style={{ fontSize: ResponsiveTypography.fontSize.sm, color: '#666' }}>
                          Last seen: {item.lastSeen.toLocaleDateString()}
                        </Text>
                      )}
                    </View>
                    <Ionicons name="chevron-forward" size={20} color="#666" />
                  </TouchableOpacity>
                )}
                style={{ maxHeight: 300 }}
              />
            ) : (
              <View style={{
                padding: ResponsiveSpacing.xl,
                alignItems: 'center'
              }}>
                <Ionicons name="chatbubbles-outline" size={48} color="#ccc" />
                <Text style={{
                  fontSize: ResponsiveTypography.fontSize.md,
                  color: '#666',
                  marginTop: ResponsiveSpacing.md,
                  textAlign: 'center'
                }}>
                  No other chats available
                </Text>
              </View>
            )}

            {/* Copy Option */}
            <View style={{ marginTop: ResponsiveSpacing.md, paddingTop: ResponsiveSpacing.md, borderTopWidth: 1, borderTopColor: '#e0e0e0' }}>
              <TouchableOpacity
                style={{
                  flexDirection: 'row',
                  alignItems: 'center',
                  padding: ResponsiveSpacing.md,
                  backgroundColor: '#f8f9fa',
                  borderRadius: ResponsiveScale.borderRadius(8),
                }}
                onPress={async () => {
                  if (messageToForward) {
                    try {
                      const textToCopy = messageToForward.text || `${messageToForward.type} media from IraChat`;
                      await Clipboard.setStringAsync(textToCopy);
                      Alert.alert("Copied", "Message content copied to clipboard");
                      setShowForwardModal(false);
                      setMessageToForward(null);
                    } catch (error) {
                      console.error("Error copying text:", error);
                      Alert.alert("Error", "Failed to copy text");
                    }
                  }
                }}
              >
                <Ionicons name="copy" size={20} color="#667eea" />
                <Text style={{ marginLeft: ResponsiveSpacing.sm, fontSize: ResponsiveTypography.fontSize.md }}>
                  Copy message content
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

        </KeyboardAvoidingView>
      </IraChatWallpaper>
    </View>
  );
};

// Beautiful Responsive Styles for ChatRoom
const styles = {
  contactMessageContainer: {
    marginVertical: ResponsiveSpacing.sm,
  },
  contactCard: {
    borderRadius: ResponsiveScale.borderRadius(12),
    padding: ResponsiveSpacing.md,
    flexDirection: 'row' as const,
    alignItems: 'center' as const,
    ...SHADOWS.sm,
  },
  contactAvatar: {
    width: ComponentSizes.avatarSize.medium,
    height: ComponentSizes.avatarSize.medium,
    borderRadius: ComponentSizes.avatarSize.medium / 2,
    backgroundColor: IRACHAT_COLORS.primary,
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
    marginRight: ResponsiveSpacing.md,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: ResponsiveTypography.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  contactPhone: {
    fontSize: ResponsiveTypography.fontSize.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
};

export default ChatRoom;
