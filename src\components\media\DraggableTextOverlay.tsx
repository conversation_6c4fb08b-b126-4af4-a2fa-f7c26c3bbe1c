import React, { useRef, useState } from 'react';
import {
  View,
  Text,
  PanResponder,
  Animated,
  Dimensions,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface DraggableTextOverlayProps {
  text: string;
  textColor: string;
  backgroundColor: string;
  backgroundStyle: 'none' | 'solid' | 'semi-transparent' | 'outline' | 'shadow';
  x: number; // 0-1 relative position
  y: number; // 0-1 relative position
  containerWidth: number;
  containerHeight: number;
  onPositionChange: (x: number, y: number) => void;
  onTextEdit?: () => void; // Callback for text editing
  onSizeChange?: (size: number) => void; // Callback for size change
  fontSize?: number; // Font size
}

export const DraggableTextOverlay: React.FC<DraggableTextOverlayProps> = ({
  text,
  textColor,
  backgroundColor,
  backgroundStyle,
  x,
  y,
  containerWidth,
  containerHeight,
  onPositionChange,
  onTextEdit,
  onSizeChange,
  fontSize = 16,
}) => {
  const [currentFontSize, setCurrentFontSize] = useState(fontSize);
  const [isDragging, setIsDragging] = useState(false);

  // Initialize position relative to container - center by default
  const initialX = x * containerWidth - 50;
  const initialY = y * containerHeight - 25;

  const pan = useRef(new Animated.ValueXY({ x: initialX, y: initialY })).current;
  const currentPosition = useRef({ x: initialX, y: initialY });

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: () => true,
    onPanResponderGrant: () => {
      setIsDragging(true);
      pan.setOffset({
        x: currentPosition.current.x,
        y: currentPosition.current.y,
      });
    },
    onPanResponderMove: Animated.event(
      [null, { dx: pan.x, dy: pan.y }],
      { useNativeDriver: false }
    ),
    onPanResponderRelease: (_, gestureState) => {
      setIsDragging(false);
      pan.flattenOffset();

      const finalX = currentPosition.current.x + gestureState.dx;
      const finalY = currentPosition.current.y + gestureState.dy;

      // Update tracked position
      currentPosition.current = { x: finalX, y: finalY };

      // Calculate new relative position
      const newX = Math.max(0, Math.min(1, finalX / containerWidth));
      const newY = Math.max(0, Math.min(1, finalY / containerHeight));

      // Snap to bounds
      const boundedX = Math.max(0, Math.min(containerWidth - 100, finalX));
      const boundedY = Math.max(0, Math.min(containerHeight - 50, finalY));

      // Update both animated value and tracked position
      pan.setValue({ x: boundedX, y: boundedY });
      currentPosition.current = { x: boundedX, y: boundedY };

      onPositionChange(newX, newY);
    },
  });

  // Handle tap to edit
  const handleTap = () => {
    if (onTextEdit) {
      onTextEdit();
    }
  };

  const getTextStyle = () => {
    const baseStyle = {
      color: textColor,
      fontSize: currentFontSize,
      fontWeight: '600' as const,
      textAlign: 'center' as const,
    };

    switch (backgroundStyle) {
      case 'outline':
        return {
          ...baseStyle,
          textShadowColor: backgroundColor,
          textShadowOffset: { width: -1, height: -1 },
          textShadowRadius: 0,
        };
      case 'shadow':
        return {
          ...baseStyle,
          textShadowColor: 'rgba(0, 0, 0, 0.8)',
          textShadowOffset: { width: 2, height: 2 },
          textShadowRadius: 4,
        };
      default:
        return baseStyle;
    }
  };

  const getContainerStyle = () => {
    const baseStyle = {
      paddingHorizontal: 12,
      paddingVertical: 6,
      borderRadius: 8,
      minWidth: 80,
      maxWidth: containerWidth * 0.8,
    };

    switch (backgroundStyle) {
      case 'solid':
        return {
          ...baseStyle,
          backgroundColor: backgroundColor,
        };
      case 'semi-transparent':
        return {
          ...baseStyle,
          backgroundColor: backgroundColor.includes('rgba') 
            ? backgroundColor 
            : backgroundColor.replace('rgb', 'rgba').replace(')', ', 0.7)'),
        };
      case 'outline':
        return {
          ...baseStyle,
          borderWidth: 2,
          borderColor: backgroundColor,
          backgroundColor: 'transparent',
        };
      case 'shadow':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.8,
          shadowRadius: 4,
          elevation: 5,
        };
      default:
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
        };
    }
  };

  if (!text || text.trim().length === 0) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: pan.getTranslateTransform(),
        },
        isDragging && styles.draggingContainer,
      ]}
      {...panResponder.panHandlers}
    >
      <TouchableOpacity
        onPress={handleTap}
        activeOpacity={0.8}
      >
        <View style={getContainerStyle()}>
          <Text style={getTextStyle()}>{text}</Text>
        </View>
      </TouchableOpacity>
      {isDragging && (
        <View style={styles.dragIndicator}>
          <View style={styles.dragHandle} />
        </View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    zIndex: 10,
  },
  draggingContainer: {
    borderWidth: 2,
    borderColor: '#87CEEB',
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: 4,
  },
  dragIndicator: {
    position: 'absolute',
    top: -12,
    right: -12,
    width: 24,
    height: 24,
    backgroundColor: '#87CEEB',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dragHandle: {
    width: 8,
    height: 8,
    backgroundColor: 'white',
    borderRadius: 4,
  },
});
