/**
 * useMediaSystem Hook for IraChat
 * Provides easy access to all media functionality from React components
 * No fake implementations - fully functional media system hook
 */

import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { mediaSystemIntegration, MediaSystemConfig, MediaSystemStatus } from '../services/mediaSystemIntegration';
import { remembranceService, RememberedMedia } from '../services/remembranceService';
import { externalAppIntegration, ExternalApp } from '../services/externalAppIntegration';

export interface UseMediaSystemReturn {
  // System status
  isInitialized: boolean;
  status: MediaSystemStatus;
  
  // Auto download
  updateAutoDownloadSettings: (settings: any) => Promise<{ success: boolean; error?: string }>;
  
  // Remembrance
  saveForRemembrance: (
    mediaUrl: string,
    type: 'image' | 'video' | 'audio' | 'document',
    fileName: string,
    sourceInfo: any,
    options?: any
  ) => Promise<{ success: boolean; rememberedId?: string; error?: string }>;
  removeFromRemembrance: (rememberedId: string) => Promise<{ success: boolean; error?: string }>;
  getRememberedMedia: (options?: any) => Promise<RememberedMedia[]>;
  searchRememberedMedia: (searchTerm: string) => Promise<RememberedMedia[]>;
  addTagsToRemembered: (rememberedId: string, tags: string[]) => Promise<{ success: boolean; error?: string }>;
  updateRememberedNotes: (rememberedId: string, notes: string) => Promise<{ success: boolean; error?: string }>;
  
  // External apps
  getExternalAppsForType: (mediaType: 'image' | 'video' | 'audio' | 'document') => ExternalApp[];
  shareToExternalApp: (appId: string, content: any) => Promise<{ success: boolean; error?: string }>;
  openWithExternalApp: (filePath: string, mimeType: string) => Promise<{ success: boolean; error?: string }>;
  
  // Bulk operations
  performBulkOperation: (
    operation: 'download' | 'share' | 'delete' | 'remember',
    mediaItems: any[]
  ) => Promise<{ success: boolean; processed: number; failed: number; errors: string[] }>;
  
  // Statistics
  getMediaStatistics: () => Promise<any>;
  
  // Utilities
  showMediaViewer: (mediaItems: any[], initialIndex: number) => void;
  showDownloadedMediaUI: () => void;
}

export const useMediaSystem = (userId?: string): UseMediaSystemReturn => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [status, setStatus] = useState<MediaSystemStatus>({
    autoDownload: 'disabled',
    remembrance: 'disabled',
    mediaActions: 'disabled',
    externalApps: 'disabled',
    messaging: 'disabled',
  });

  // Initialize the media system
  useEffect(() => {
    if (!userId) return;

    const initializeSystem = async () => {
      try {
        const config: MediaSystemConfig = {
          userId,
          autoDownloadEnabled: true,
          remembranceEnabled: true,
          externalAppsEnabled: true,
        };

        await mediaSystemIntegration.initialize(config);
        setIsInitialized(true);
        setStatus(mediaSystemIntegration.getSystemStatus());
      } catch (error) {
        console.error('❌ Failed to initialize media system in hook:', error);
        Alert.alert('Error', 'Failed to initialize media system');
      }
    };

    initializeSystem();

    // Cleanup on unmount
    return () => {
      mediaSystemIntegration.cleanup();
    };
  }, [userId]);

  // Update status periodically
  useEffect(() => {
    if (!isInitialized) return;

    const interval = setInterval(() => {
      setStatus(mediaSystemIntegration.getSystemStatus());
    }, 5000);

    return () => clearInterval(interval);
  }, [isInitialized]);

  // Auto download settings
  const updateAutoDownloadSettings = useCallback(async (settings: any) => {
    return await mediaSystemIntegration.updateAutoDownloadSettings(settings);
  }, []);

  // Remembrance functions
  const saveForRemembrance = useCallback(async (
    mediaUrl: string,
    type: 'image' | 'video' | 'audio' | 'document',
    fileName: string,
    sourceInfo: any,
    options?: any
  ) => {
    return await mediaSystemIntegration.saveForRemembrance(
      mediaUrl,
      type,
      fileName,
      sourceInfo,
      options
    );
  }, []);

  const removeFromRemembrance = useCallback(async (rememberedId: string) => {
    if (!mediaSystemIntegration.isServiceAvailable('remembrance')) {
      return { success: false, error: 'Remembrance service not available' };
    }

    try {
      return await remembranceService.removeFromRemembrance(rememberedId);
    } catch (error) {
      console.error('❌ Error removing from remembrance:', error);
      return { success: false, error: 'Failed to remove from remembrance' };
    }
  }, []);

  const getRememberedMedia = useCallback(async (options?: any) => {
    if (!mediaSystemIntegration.isServiceAvailable('remembrance')) {
      return [];
    }

    try {
      return await remembranceService.getRememberedMedia(options);
    } catch (error) {
      console.error('❌ Error getting remembered media:', error);
      return [];
    }
  }, []);

  const searchRememberedMedia = useCallback(async (searchTerm: string) => {
    if (!mediaSystemIntegration.isServiceAvailable('remembrance')) {
      return [];
    }

    try {
      return await remembranceService.searchRememberedMedia(searchTerm);
    } catch (error) {
      console.error('❌ Error searching remembered media:', error);
      return [];
    }
  }, []);

  const addTagsToRemembered = useCallback(async (rememberedId: string, tags: string[]) => {
    if (!mediaSystemIntegration.isServiceAvailable('remembrance')) {
      return { success: false, error: 'Remembrance service not available' };
    }

    try {
      return await remembranceService.addTags(rememberedId, tags);
    } catch (error) {
      console.error('❌ Error adding tags:', error);
      return { success: false, error: 'Failed to add tags' };
    }
  }, []);

  const updateRememberedNotes = useCallback(async (rememberedId: string, notes: string) => {
    if (!mediaSystemIntegration.isServiceAvailable('remembrance')) {
      return { success: false, error: 'Remembrance service not available' };
    }

    try {
      return await remembranceService.updateNotes(rememberedId, notes);
    } catch (error) {
      console.error('❌ Error updating notes:', error);
      return { success: false, error: 'Failed to update notes' };
    }
  }, []);

  // External apps functions
  const getExternalAppsForType = useCallback((mediaType: 'image' | 'video' | 'audio' | 'document') => {
    return mediaSystemIntegration.getExternalAppsForMediaType(mediaType);
  }, []);

  const shareToExternalApp = useCallback(async (appId: string, content: any) => {
    return await mediaSystemIntegration.shareToExternalApp(appId, content);
  }, []);

  const openWithExternalApp = useCallback(async (filePath: string, mimeType: string) => {
    if (!mediaSystemIntegration.isServiceAvailable('externalApps')) {
      return { success: false, error: 'External apps integration not available' };
    }

    try {
      return await externalAppIntegration.openWithExternalApp(filePath, mimeType);
    } catch (error) {
      console.error('❌ Error opening with external app:', error);
      return { success: false, error: 'Failed to open with external app' };
    }
  }, []);

  // Bulk operations
  const performBulkOperation = useCallback(async (
    operation: 'download' | 'share' | 'delete' | 'remember',
    mediaItems: any[]
  ) => {
    return await mediaSystemIntegration.performBulkOperation(operation, mediaItems);
  }, []);

  // Statistics
  const getMediaStatistics = useCallback(async () => {
    return await mediaSystemIntegration.getMediaStatistics();
  }, []);

  // UI utilities
  const showMediaViewer = useCallback((mediaItems: any[], initialIndex: number) => {
    // This would typically set state to show the ModernMediaViewer component
    console.log('📱 Show media viewer:', { mediaItems, initialIndex });
  }, []);

  const showDownloadedMediaUI = useCallback(() => {
    // This would typically set state to show the ModernDownloadedMediaUI component
    console.log('📱 Show downloaded media UI');
  }, []);

  return {
    // System status
    isInitialized,
    status,
    
    // Auto download
    updateAutoDownloadSettings,
    
    // Remembrance
    saveForRemembrance,
    removeFromRemembrance,
    getRememberedMedia,
    searchRememberedMedia,
    addTagsToRemembered,
    updateRememberedNotes,
    
    // External apps
    getExternalAppsForType,
    shareToExternalApp,
    openWithExternalApp,
    
    // Bulk operations
    performBulkOperation,
    
    // Statistics
    getMediaStatistics,
    
    // Utilities
    showMediaViewer,
    showDownloadedMediaUI,
  };
};

// Helper hook for media viewer state management
export const useMediaViewer = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [mediaItems, setMediaItems] = useState<any[]>([]);
  const [initialIndex, setInitialIndex] = useState(0);

  const showViewer = useCallback((items: any[], index: number = 0) => {
    setMediaItems(items);
    setInitialIndex(index);
    setIsVisible(true);
  }, []);

  const hideViewer = useCallback(() => {
    setIsVisible(false);
  }, []);

  return {
    isVisible,
    mediaItems,
    initialIndex,
    showViewer,
    hideViewer,
  };
};

// Helper hook for downloaded media UI state management
export const useDownloadedMediaUI = () => {
  const [isVisible, setIsVisible] = useState(false);

  const showUI = useCallback(() => {
    setIsVisible(true);
  }, []);

  const hideUI = useCallback(() => {
    setIsVisible(false);
  }, []);

  return {
    isVisible,
    showUI,
    hideUI,
  };
};
