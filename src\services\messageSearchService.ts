/**
 * Message Search Service for IraChat
 * Provides comprehensive message search functionality
 * Supports searching by text, date, file type, media type, and more
 */

import { offlineDatabaseService } from './offlineDatabase';
import { realTimeMessagingService } from './realTimeMessagingService';
import { networkStateManager } from './networkStateManager';

export interface SearchFilters {
  query?: string;
  startDate?: Date;
  endDate?: Date;
  messageType?: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice' | 'location' | 'contact' | 'all';
  senderId?: string;
  hasMedia?: boolean;
  isStarred?: boolean;
  isForwarded?: boolean;
}

export interface SearchResult {
  id: string;
  content: string;
  type: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  mediaUrl?: string;
  fileName?: string;
  caption?: string;
  isStarred?: boolean;
  isForwarded?: boolean;
  chatId: string;
  matchedText?: string;
  contextBefore?: string;
  contextAfter?: string;
}

export interface SearchOptions {
  limit?: number;
  offset?: number;
  sortBy?: 'timestamp' | 'relevance';
  sortOrder?: 'asc' | 'desc';
  includeContext?: boolean;
  contextLength?: number;
}

class MessageSearchService {
  private static instance: MessageSearchService;

  static getInstance(): MessageSearchService {
    if (!MessageSearchService.instance) {
      MessageSearchService.instance = new MessageSearchService();
    }
    return MessageSearchService.instance;
  }

  /**
   * Search messages in a specific chat
   */
  async searchInChat(
    chatId: string,
    filters: SearchFilters,
    options: SearchOptions = {}
  ): Promise<{ results: SearchResult[]; totalCount: number }> {
    try {
      const {
        limit = 50,
        offset = 0,
        sortBy = 'timestamp',
        sortOrder = 'desc',
        includeContext = true,
        contextLength = 50
      } = options;

      // Build SQL query based on filters (handle missing columns gracefully)
      let query = `
        SELECT
          id, content, type, senderId, senderName, timestamp,
          mediaUrl, fileName,
          CASE WHEN EXISTS(SELECT * FROM pragma_table_info('messages') WHERE name='caption')
               THEN caption ELSE NULL END as caption,
          CASE WHEN EXISTS(SELECT * FROM pragma_table_info('messages') WHERE name='isStarred')
               THEN isStarred ELSE 0 END as isStarred,
          CASE WHEN EXISTS(SELECT * FROM pragma_table_info('messages') WHERE name='isForwarded')
               THEN isForwarded ELSE 0 END as isForwarded,
          chatId
        FROM messages
        WHERE chatId = ? AND (isDeleted IS NULL OR isDeleted = 0)
      `;
      
      const params: any[] = [chatId];

      // Add text search (handle missing caption column)
      if (filters.query && filters.query.trim()) {
        query += ` AND (content LIKE ? OR fileName LIKE ?)`;
        const searchTerm = `%${filters.query.trim()}%`;
        params.push(searchTerm, searchTerm);

        // Add caption search only if column exists
        try {
          const db = offlineDatabaseService.getDatabase();
          const columnCheck = await db.getFirstAsync(`SELECT * FROM pragma_table_info('messages') WHERE name='caption'`);
          if (columnCheck) {
            query = query.replace('(content LIKE ? OR fileName LIKE ?)', '(content LIKE ? OR caption LIKE ? OR fileName LIKE ?)');
            params.splice(-1, 0, searchTerm); // Insert caption search term
          }
        } catch (error) {
          // Column doesn't exist, continue without caption search
        }
      }

      // Add date range filter
      if (filters.startDate) {
        query += ` AND timestamp >= ?`;
        params.push(filters.startDate.getTime());
      }
      if (filters.endDate) {
        query += ` AND timestamp <= ?`;
        params.push(filters.endDate.getTime());
      }

      // Add message type filter
      if (filters.messageType && filters.messageType !== 'all') {
        query += ` AND type = ?`;
        params.push(filters.messageType);
      }

      // Add sender filter
      if (filters.senderId) {
        query += ` AND senderId = ?`;
        params.push(filters.senderId);
      }

      // Add media filter
      if (filters.hasMedia !== undefined) {
        if (filters.hasMedia) {
          query += ` AND type IN ('image', 'video', 'audio', 'document', 'voice')`;
        } else {
          query += ` AND type NOT IN ('image', 'video', 'audio', 'document', 'voice')`;
        }
      }

      // Add starred filter
      if (filters.isStarred !== undefined) {
        query += ` AND isStarred = ?`;
        params.push(filters.isStarred ? 1 : 0);
      }

      // Add forwarded filter
      if (filters.isForwarded !== undefined) {
        query += ` AND isForwarded = ?`;
        params.push(filters.isForwarded ? 1 : 0);
      }

      // Add sorting
      const sortColumn = sortBy === 'timestamp' ? 'timestamp' : 'timestamp'; // For now, only timestamp sorting
      query += ` ORDER BY ${sortColumn} ${sortOrder.toUpperCase()}`;

      // Add pagination
      query += ` LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      // Execute search
      const db = offlineDatabaseService.getDatabase();
      if (!db) {
        throw new Error('Database not initialized');
      }

      console.log('🔍 Executing search query:', query);
      console.log('🔍 Search params:', params);

      const results = await db.getAllAsync(query, params);

      // Get total count for pagination
      let countQuery = query.replace(/SELECT.*?FROM/, 'SELECT COUNT(*) as count FROM');
      countQuery = countQuery.replace(/ORDER BY.*?LIMIT.*?OFFSET.*?$/, '');
      const countParams = params.slice(0, -2); // Remove limit and offset params
      const countResult = await db.getFirstAsync<{ count: number }>(countQuery, countParams);
      const totalCount = countResult?.count || 0;

      // Process results
      const searchResults: SearchResult[] = await Promise.all(
        results.map(async (row: any) => {
          const result: SearchResult = {
            id: row.id,
            content: row.content || '',
            type: row.type,
            senderId: row.senderId,
            senderName: row.senderName || 'Unknown',
            timestamp: new Date(row.timestamp),
            mediaUrl: row.mediaUrl,
            fileName: row.fileName,
            caption: row.caption,
            isStarred: row.isStarred === 1,
            isForwarded: row.isForwarded === 1,
            chatId: row.chatId,
          };

          // Add context and highlight matched text
          if (includeContext && filters.query) {
            const context = await this.getMessageContext(row.id, chatId, contextLength);
            result.contextBefore = context.before;
            result.contextAfter = context.after;
            result.matchedText = this.highlightMatchedText(
              row.content || row.caption || row.fileName || '',
              filters.query
            );
          }

          return result;
        })
      );

      return { results: searchResults, totalCount };
    } catch (error) {
      console.error('Error searching messages:', error);
      return { results: [], totalCount: 0 };
    }
  }

  /**
   * Search messages across all chats
   */
  async searchGlobal(
    filters: SearchFilters,
    options: SearchOptions = {}
  ): Promise<{ results: SearchResult[]; totalCount: number }> {
    try {
      // Similar to searchInChat but without chatId filter
      const {
        limit = 50,
        offset = 0,
        sortBy = 'timestamp',
        sortOrder = 'desc',
      } = options;

      let query = `
        SELECT 
          m.id, m.content, m.type, m.senderId, m.senderName, m.timestamp, 
          m.mediaUrl, m.fileName, m.caption, m.isStarred, m.isForwarded,
          m.chatId, c.name as chatName
        FROM messages m
        LEFT JOIN chats c ON m.chatId = c.id
        WHERE m.isDeleted = 0
      `;
      
      const params: any[] = [];

      // Add filters (similar to searchInChat)
      if (filters.query && filters.query.trim()) {
        query += ` AND (m.content LIKE ? OR m.caption LIKE ? OR m.fileName LIKE ?)`;
        const searchTerm = `%${filters.query.trim()}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      // Add other filters...
      if (filters.startDate) {
        query += ` AND m.timestamp >= ?`;
        params.push(filters.startDate.getTime());
      }
      if (filters.endDate) {
        query += ` AND m.timestamp <= ?`;
        params.push(filters.endDate.getTime());
      }
      if (filters.messageType && filters.messageType !== 'all') {
        query += ` AND m.type = ?`;
        params.push(filters.messageType);
      }

      query += ` ORDER BY m.timestamp ${sortOrder.toUpperCase()}`;
      query += ` LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const db = offlineDatabaseService.getDatabase();
      const results = await db.getAllAsync(query, params);

      // Get total count
      let countQuery = query.replace(/SELECT.*?FROM/, 'SELECT COUNT(*) as count FROM');
      countQuery = countQuery.replace(/ORDER BY.*?LIMIT.*?OFFSET.*?$/, '');
      const countParams = params.slice(0, -2);
      const countResult = await db.getFirstAsync<{ count: number }>(countQuery, countParams);
      const totalCount = countResult?.count || 0;

      const searchResults: SearchResult[] = results.map((row: any) => ({
        id: row.id,
        content: row.content || '',
        type: row.type,
        senderId: row.senderId,
        senderName: row.senderName || 'Unknown',
        timestamp: new Date(row.timestamp),
        mediaUrl: row.mediaUrl,
        fileName: row.fileName,
        caption: row.caption,
        isStarred: row.isStarred === 1,
        isForwarded: row.isForwarded === 1,
        chatId: row.chatId,
      }));

      return { results: searchResults, totalCount };
    } catch (error) {
      console.error('Error in global search:', error);
      return { results: [], totalCount: 0 };
    }
  }

  /**
   * Get message context (messages before and after)
   */
  private async getMessageContext(
    messageId: string,
    chatId: string,
    contextLength: number
  ): Promise<{ before: string; after: string }> {
    try {
      const db = offlineDatabaseService.getDatabase();
      
      // Get message timestamp
      const message = await db.getFirstAsync<{ timestamp: number }>(
        'SELECT timestamp FROM messages WHERE id = ?',
        [messageId]
      );
      
      if (!message) return { before: '', after: '' };

      // Get context before
      const beforeResults = await db.getAllAsync(
        `SELECT content FROM messages 
         WHERE chatId = ? AND timestamp < ? AND isDeleted = 0 
         ORDER BY timestamp DESC LIMIT 2`,
        [chatId, message.timestamp]
      );

      // Get context after
      const afterResults = await db.getAllAsync(
        `SELECT content FROM messages 
         WHERE chatId = ? AND timestamp > ? AND isDeleted = 0 
         ORDER BY timestamp ASC LIMIT 2`,
        [chatId, message.timestamp]
      );

      const before = beforeResults.map((r: any) => r.content).reverse().join(' ');
      const after = afterResults.map((r: any) => r.content).join(' ');

      return {
        before: before.length > contextLength ? '...' + before.slice(-contextLength) : before,
        after: after.length > contextLength ? after.slice(0, contextLength) + '...' : after,
      };
    } catch (error) {
      console.error('Error getting message context:', error);
      return { before: '', after: '' };
    }
  }

  /**
   * Highlight matched text in search results
   */
  private highlightMatchedText(text: string, query: string): string {
    if (!text || !query) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }

  /**
   * Get search suggestions based on recent searches
   */
  async getSearchSuggestions(chatId?: string): Promise<string[]> {
    try {
      // This could be enhanced to store and retrieve recent search queries
      // For now, return some common search terms
      return [
        'image', 'video', 'document', 'audio',
        'today', 'yesterday', 'last week',
        'shared', 'forwarded', 'starred'
      ];
    } catch (error) {
      console.error('Error getting search suggestions:', error);
      return [];
    }
  }
}

export const messageSearchService = MessageSearchService.getInstance();

/**
 * Helper function to format date for search
 */
export const formatDateForSearch = (date: Date): string => {
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

/**
 * Helper function to get date range presets
 */
export const getDateRangePresets = () => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

  return [
    { label: 'Today', startDate: today, endDate: now },
    { label: 'Yesterday', startDate: yesterday, endDate: today },
    { label: 'Last 7 days', startDate: lastWeek, endDate: now },
    { label: 'Last 30 days', startDate: lastMonth, endDate: now },
  ];
};
