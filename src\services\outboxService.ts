/**
 * Outbox Service for IraChat
 * Manages pending messages that need to be sent when network is available
 * Similar to <PERSON>sApp's message queue system
 */

import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';
import { realTimeMessagingService } from './realTimeMessagingService';

export interface OutboxMessage {
  id: string;
  messageId: string;
  chatId: string;
  senderId: string;
  senderName?: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice';
  mediaUrl?: string;
  mediaType?: string;
  fileName?: string;
  fileSize?: number;
  thumbnailUrl?: string;
  replyToMessageId?: string;
  priority: number; // 0 = normal, 1 = high, 2 = urgent
  status: 'pending' | 'sending' | 'sent' | 'failed';
  retryCount: number;
  maxRetries: number;
  lastAttempt?: number;
  nextAttempt?: number;
  errorMessage?: string;
  createdAt: number;
  updatedAt: number;
}

class OutboxService {
  private processingQueue = false;
  private backgroundInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize outbox service
   */
  async initialize(): Promise<void> {
    console.log('📤 Initializing Outbox Service...');
    
    // Start background processing
    this.startBackgroundProcessing();
    
    // Listen for network changes
    networkStateManager.addListener('outbox', this.handleNetworkChange.bind(this));
    
    console.log('✅ Outbox Service initialized');
  }

  /**
   * Add message to outbox
   */
  async addToOutbox(message: Omit<OutboxMessage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = Date.now();
    const outboxId = `outbox_${now}_${Math.random().toString(36).substring(2, 9)}`;
    
    const outboxMessage: OutboxMessage = {
      ...message,
      id: outboxId,
      createdAt: now,
      updatedAt: now,
    };

    try {
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync(`
        INSERT INTO outbox (
          id, messageId, chatId, senderId, senderName, content, type,
          mediaUrl, mediaType, fileName, fileSize, thumbnailUrl, replyToMessageId,
          priority, status, retryCount, maxRetries, lastAttempt, nextAttempt,
          errorMessage, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        outboxMessage.id,
        outboxMessage.messageId,
        outboxMessage.chatId,
        outboxMessage.senderId,
        outboxMessage.senderName || null,
        outboxMessage.content,
        outboxMessage.type,
        outboxMessage.mediaUrl || null,
        outboxMessage.mediaType || null,
        outboxMessage.fileName || null,
        outboxMessage.fileSize || null,
        outboxMessage.thumbnailUrl || null,
        outboxMessage.replyToMessageId || null,
        outboxMessage.priority,
        outboxMessage.status,
        outboxMessage.retryCount,
        outboxMessage.maxRetries,
        outboxMessage.lastAttempt || null,
        outboxMessage.nextAttempt || null,
        outboxMessage.errorMessage || null,
        outboxMessage.createdAt,
        outboxMessage.updatedAt,
      ]);

      console.log(`📤 Added message to outbox: ${outboxId}`);
      
      // Try to process immediately if online
      if (networkStateManager.isOnline()) {
        this.processOutbox();
      }

      return outboxId;
    } catch (error) {
      console.error('❌ Failed to add message to outbox:', error);
      throw error;
    }
  }

  /**
   * Get pending messages from outbox
   */
  async getPendingMessages(): Promise<OutboxMessage[]> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const results = await db.getAllAsync(`
        SELECT * FROM outbox 
        WHERE status IN ('pending', 'failed') 
        AND (nextAttempt IS NULL OR nextAttempt <= ?)
        ORDER BY priority DESC, createdAt ASC
      `, [Date.now()]);

      return results.map(this.mapRowToOutboxMessage);
    } catch (error) {
      console.error('❌ Failed to get pending messages:', error);
      return [];
    }
  }

  /**
   * Process outbox queue
   */
  async processOutbox(): Promise<void> {
    if (this.processingQueue || !networkStateManager.isOnline()) {
      return;
    }

    this.processingQueue = true;
    console.log('📤 Processing outbox...');

    try {
      const pendingMessages = await this.getPendingMessages();
      console.log(`📤 Found ${pendingMessages.length} pending messages`);

      for (const message of pendingMessages) {
        await this.processSingleMessage(message);
      }
    } catch (error) {
      console.error('❌ Error processing outbox:', error);
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * Process a single message
   */
  private async processSingleMessage(message: OutboxMessage): Promise<void> {
    try {
      // Update status to sending
      await this.updateMessageStatus(message.id, 'sending');

      // Send the message
      const messageType = message.type === 'document' ? 'file' : message.type === 'voice' ? 'audio' : message.type;
      const result = await realTimeMessagingService.sendMessage(
        message.chatId,
        message.senderId,
        message.senderName || 'User',
        message.content,
        messageType as 'text' | 'image' | 'video' | 'audio' | 'file',
        message.mediaUrl
      );

      if (result.success) {
        // Mark as sent and remove from outbox
        await this.removeFromOutbox(message.id);
        console.log(`✅ Message sent successfully: ${message.id}`);
      } else {
        // Mark as failed and schedule retry
        await this.handleMessageFailure(message, result.error || 'Unknown error');
      }
    } catch (error) {
      console.error(`❌ Failed to send message ${message.id}:`, error);
      await this.handleMessageFailure(message, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Handle message sending failure
   */
  private async handleMessageFailure(message: OutboxMessage, errorMessage: string): Promise<void> {
    const newRetryCount = message.retryCount + 1;
    
    if (newRetryCount >= message.maxRetries) {
      // Max retries reached, mark as permanently failed
      await this.updateMessageStatus(message.id, 'failed', errorMessage);
      console.log(`❌ Message permanently failed after ${newRetryCount} attempts: ${message.id}`);
    } else {
      // Schedule retry with exponential backoff
      const backoffDelay = Math.min(1000 * Math.pow(2, newRetryCount), 300000); // Max 5 minutes
      const nextAttempt = Date.now() + backoffDelay;
      
      await this.scheduleRetry(message.id, newRetryCount, nextAttempt, errorMessage);
      console.log(`🔄 Scheduled retry for message ${message.id} in ${backoffDelay}ms`);
    }
  }

  /**
   * Update message status
   */
  private async updateMessageStatus(
    id: string, 
    status: OutboxMessage['status'], 
    errorMessage?: string
  ): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync(`
        UPDATE outbox 
        SET status = ?, errorMessage = ?, updatedAt = ?, lastAttempt = ?
        WHERE id = ?
      `, [status, errorMessage || null, Date.now(), Date.now(), id]);
    } catch (error) {
      console.error('❌ Failed to update message status:', error);
    }
  }

  /**
   * Schedule message retry
   */
  private async scheduleRetry(
    id: string, 
    retryCount: number, 
    nextAttempt: number, 
    errorMessage: string
  ): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync(`
        UPDATE outbox 
        SET status = 'pending', retryCount = ?, nextAttempt = ?, 
            errorMessage = ?, updatedAt = ?, lastAttempt = ?
        WHERE id = ?
      `, [retryCount, nextAttempt, errorMessage, Date.now(), Date.now(), id]);
    } catch (error) {
      console.error('❌ Failed to schedule retry:', error);
    }
  }

  /**
   * Remove message from outbox
   */
  private async removeFromOutbox(id: string): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync('DELETE FROM outbox WHERE id = ?', [id]);
    } catch (error) {
      console.error('❌ Failed to remove message from outbox:', error);
    }
  }

  /**
   * Handle network state changes
   */
  private handleNetworkChange(state: any): void {
    if (state.isOnline) {
      console.log('🌐 Network restored, processing outbox...');
      this.processOutbox();
    }
  }

  /**
   * Start background processing
   */
  private startBackgroundProcessing(): void {
    // Process outbox every 30 seconds
    this.backgroundInterval = setInterval(() => {
      if (networkStateManager.isOnline()) {
        this.processOutbox();
      }
    }, 30000);
  }

  /**
   * Stop background processing
   */
  stopBackgroundProcessing(): void {
    if (this.backgroundInterval) {
      clearInterval(this.backgroundInterval);
      this.backgroundInterval = null;
    }
  }

  /**
   * Get outbox statistics
   */
  async getStats(): Promise<{
    pending: number;
    failed: number;
    total: number;
  }> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const results = await db.getAllAsync(`
        SELECT status, COUNT(*) as count 
        FROM outbox 
        GROUP BY status
      `);

      const stats = { pending: 0, failed: 0, total: 0 };
      
      for (const row of results) {
        const count = (row as any).count;
        stats.total += count;
        
        if ((row as any).status === 'pending') {
          stats.pending = count;
        } else if ((row as any).status === 'failed') {
          stats.failed = count;
        }
      }

      return stats;
    } catch (error) {
      console.error('❌ Failed to get outbox stats:', error);
      return { pending: 0, failed: 0, total: 0 };
    }
  }

  /**
   * Map database row to OutboxMessage
   */
  private mapRowToOutboxMessage(row: any): OutboxMessage {
    return {
      id: row.id,
      messageId: row.messageId,
      chatId: row.chatId,
      senderId: row.senderId,
      senderName: row.senderName,
      content: row.content,
      type: row.type,
      mediaUrl: row.mediaUrl,
      mediaType: row.mediaType,
      fileName: row.fileName,
      fileSize: row.fileSize,
      thumbnailUrl: row.thumbnailUrl,
      replyToMessageId: row.replyToMessageId,
      priority: row.priority,
      status: row.status,
      retryCount: row.retryCount,
      maxRetries: row.maxRetries,
      lastAttempt: row.lastAttempt,
      nextAttempt: row.nextAttempt,
      errorMessage: row.errorMessage,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    };
  }
}

export const outboxService = new OutboxService();
