# Complete F: Drive Development Environment Setup
# This script sets up all necessary tools for React Native/Expo development

param(
    [switch]$Force,
    [switch]$SkipInstall
)

Write-Host "🚀 Complete F: Drive Development Environment Setup" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Require Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script requires Administrator privileges to modify system environment variables." -ForegroundColor Red
    Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Function to safely add to PATH
function Add-ToSystemPath {
    param(
        [string]$PathToAdd,
        [string]$Description
    )
    
    if (Test-Path $PathToAdd) {
        $currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::Machine)
        if ($currentPath -notlike "*$PathToAdd*") {
            $newPath = "$currentPath;$PathToAdd"
            [Environment]::SetEnvironmentVariable("PATH", $newPath, [EnvironmentVariableTarget]::Machine)
            Write-Host "✅ Added to System PATH: $Description" -ForegroundColor Green
            Write-Host "   Path: $PathToAdd" -ForegroundColor Gray
            return $true
        } else {
            Write-Host "✓ Already in System PATH: $Description" -ForegroundColor Gray
            return $false
        }
    } else {
        Write-Host "⚠️  Path not found: $Description ($PathToAdd)" -ForegroundColor Yellow
        return $false
    }
}

# 1. JAVA SETUP
Write-Host "`n☕ Setting up Java JDK 17..." -ForegroundColor Cyan
$javaPath = "F:\IraChat\java17\jdk-17.0.12+7"
if (Test-Path $javaPath) {
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath, [EnvironmentVariableTarget]::Machine)
    Write-Host "✅ Set JAVA_HOME = $javaPath" -ForegroundColor Green
    Add-ToSystemPath "$javaPath\bin" "Java JDK 17"
} else {
    Write-Host "❌ Java 17 not found at: $javaPath" -ForegroundColor Red
    Write-Host "   Please ensure Java 17 is extracted to this location." -ForegroundColor Yellow
}

# 2. ANDROID SDK SETUP
Write-Host "`n🤖 Setting up Android SDK..." -ForegroundColor Cyan
$possibleSdkPaths = @(
    "F:\Android\Sdk",
    "F:\android\sdk",
    "F:\AndroidSDK",
    "F:\android-sdk",
    "F:\SDK\Android",
    "F:\Tools\Android\Sdk",
    "F:\Development\Android\Sdk",
    "F:\android-studio\sdk"
)

$sdkPath = $null
foreach ($path in $possibleSdkPaths) {
    if (Test-Path $path) {
        $sdkPath = $path
        Write-Host "✅ Found Android SDK at: $sdkPath" -ForegroundColor Green
        break
    }
}

if ($sdkPath) {
    [Environment]::SetEnvironmentVariable("ANDROID_HOME", $sdkPath, [EnvironmentVariableTarget]::Machine)
    [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $sdkPath, [EnvironmentVariableTarget]::Machine)
    Write-Host "✅ Set ANDROID_HOME = $sdkPath" -ForegroundColor Green
    
    # Add Android tools to PATH
    Add-ToSystemPath "$sdkPath\platform-tools" "Android Platform Tools (ADB)"
    Add-ToSystemPath "$sdkPath\cmdline-tools\latest\bin" "Android Command Line Tools"
    Add-ToSystemPath "$sdkPath\tools" "Android Tools"
    Add-ToSystemPath "$sdkPath\tools\bin" "Android Tools Bin"
    Add-ToSystemPath "$sdkPath\emulator" "Android Emulator"
    
    # Find and add build-tools
    $buildToolsDir = "$sdkPath\build-tools"
    if (Test-Path $buildToolsDir) {
        $latestBuildTools = Get-ChildItem $buildToolsDir | Sort-Object Name -Descending | Select-Object -First 1
        if ($latestBuildTools) {
            Add-ToSystemPath $latestBuildTools.FullName "Android Build Tools"
        }
    }
} else {
    Write-Host "❌ Android SDK not found. Please install Android Studio or SDK." -ForegroundColor Red
}

# 3. NODE.JS SETUP
Write-Host "`n📦 Setting up Node.js..." -ForegroundColor Cyan
$possibleNodePaths = @(
    "F:\nodejs",
    "F:\Node",
    "F:\Tools\nodejs",
    "F:\Development\nodejs",
    "F:\Program Files\nodejs",
    "F:\node"
)

$nodePath = $null
foreach ($path in $possibleNodePaths) {
    if (Test-Path "$path\node.exe") {
        $nodePath = $path
        Write-Host "✅ Found Node.js at: $nodePath" -ForegroundColor Green
        Add-ToSystemPath $nodePath "Node.js"
        break
    }
}

if (-not $nodePath) {
    Write-Host "⚠️  Node.js not found on F: drive" -ForegroundColor Yellow
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Host "✅ Node.js is available in system: $nodeVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Node.js not found anywhere. Please install Node.js." -ForegroundColor Red
    }
}

# 4. GIT SETUP
Write-Host "`n📝 Setting up Git..." -ForegroundColor Cyan
$possibleGitPaths = @(
    "F:\Git\bin",
    "F:\Tools\Git\bin",
    "F:\Development\Git\bin",
    "F:\PortableGit\bin"
)

$gitPath = $null
foreach ($path in $possibleGitPaths) {
    if (Test-Path "$path\git.exe") {
        $gitPath = $path
        Write-Host "✅ Found Git at: $gitPath" -ForegroundColor Green
        Add-ToSystemPath $gitPath "Git"
        break
    }
}

if (-not $gitPath) {
    try {
        $gitVersion = git --version 2>$null
        if ($gitVersion) {
            Write-Host "✅ Git is available in system: $gitVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️  Git not found. Consider installing Git." -ForegroundColor Yellow
    }
}

# 5. PYTHON SETUP (for build scripts)
Write-Host "`n🐍 Setting up Python..." -ForegroundColor Cyan
$possiblePythonPaths = @(
    "F:\Python",
    "F:\Python39",
    "F:\Python310",
    "F:\Python311",
    "F:\Python312",
    "F:\Tools\Python",
    "F:\Development\Python"
)

$pythonPath = $null
foreach ($path in $possiblePythonPaths) {
    if (Test-Path "$path\python.exe") {
        $pythonPath = $path
        Write-Host "✅ Found Python at: $pythonPath" -ForegroundColor Green
        Add-ToSystemPath $pythonPath "Python"
        Add-ToSystemPath "$pythonPath\Scripts" "Python Scripts"
        break
    }
}

if (-not $pythonPath) {
    try {
        $pythonVersion = python --version 2>$null
        if ($pythonVersion) {
            Write-Host "✅ Python is available in system: $pythonVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "⚠️  Python not found. Some build scripts may not work." -ForegroundColor Yellow
    }
}

Write-Host "`n🎉 Environment setup completed!" -ForegroundColor Green
Write-Host "====================================================" -ForegroundColor Green

# Display summary
Write-Host "`n📋 Environment Variables Set:" -ForegroundColor Yellow
if ($javaPath -and (Test-Path $javaPath)) {
    Write-Host "   JAVA_HOME = $javaPath" -ForegroundColor Cyan
}
if ($sdkPath) {
    Write-Host "   ANDROID_HOME = $sdkPath" -ForegroundColor Cyan
    Write-Host "   ANDROID_SDK_ROOT = $sdkPath" -ForegroundColor Cyan
}

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Close ALL terminal windows and restart them" -ForegroundColor White
Write-Host "2. Open a new PowerShell/Command Prompt" -ForegroundColor White
Write-Host "3. Navigate to your project: cd F:\IraChat" -ForegroundColor White
Write-Host "4. Run: npm install (if not done already)" -ForegroundColor White
Write-Host "5. Run: npx expo start" -ForegroundColor White
Write-Host "6. Press 'a' to open Android emulator" -ForegroundColor White

Write-Host "`n🔧 Verification Commands:" -ForegroundColor Yellow
Write-Host "   java -version" -ForegroundColor White
Write-Host "   node --version" -ForegroundColor White
Write-Host "   npm --version" -ForegroundColor White
Write-Host "   adb version" -ForegroundColor White
Write-Host "   echo `$env:ANDROID_HOME" -ForegroundColor White
Write-Host "   echo `$env:JAVA_HOME" -ForegroundColor White

Write-Host "`n⚠️  CRITICAL: You MUST restart all terminals for changes to take effect!" -ForegroundColor Red
Write-Host ""

Read-Host "Press Enter to exit"
