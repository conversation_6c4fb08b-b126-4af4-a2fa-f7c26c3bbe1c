# PowerShell script to download and install the built APK
Write-Host "Download and Install IraChat APK" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Step 1: Check build status
Write-Host "`nStep 1: Checking build status..." -ForegroundColor Yellow
try {
    Write-Host "Fetching build list..." -ForegroundColor Cyan
    eas build:list --platform android --limit 5
} catch {
    Write-Host "❌ Failed to fetch build list. Make sure you're logged in:" -ForegroundColor Red
    Write-Host "  eas login" -ForegroundColor White
    exit 1
}

Write-Host "`nLook for a build with status 'FINISHED' and profile 'standalone'" -ForegroundColor Cyan
$continue = Read-Host "Is your build finished? (y/n)"

if ($continue -ne 'y' -and $continue -ne 'Y') {
    Write-Host "`nBuild not ready yet. Wait for it to complete, then run this script again." -ForegroundColor Yellow
    Write-Host "You can check status with: eas build:list" -ForegroundColo<PERSON> White
    exit
}

# Step 2: Download the APK
Write-Host "`nStep 2: Downloading APK..." -ForegroundColor Yellow
Write-Host "==========================" -ForegroundColor Yellow

try {
    Write-Host "Downloading latest standalone APK..." -ForegroundColor Cyan
    eas build:download --platform android --profile standalone
    Write-Host "✅ APK downloaded successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Download failed. Try manually:" -ForegroundColor Red
    Write-Host "  eas build:download --platform android --profile standalone" -ForegroundColor White
    exit 1
}

# Step 3: Find the downloaded APK
Write-Host "`nStep 3: Locating APK file..." -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

$apkFiles = Get-ChildItem -Path "." -Filter "*.apk" | Sort-Object LastWriteTime -Descending
if ($apkFiles.Count -gt 0) {
    $latestApk = $apkFiles[0]
    Write-Host "✅ Found APK: $($latestApk.Name)" -ForegroundColor Green
    Write-Host "   Size: $([math]::Round($latestApk.Length/1MB, 2)) MB" -ForegroundColor Gray
    Write-Host "   Location: $($latestApk.FullName)" -ForegroundColor Gray
} else {
    Write-Host "❌ No APK file found in current directory" -ForegroundColor Red
    Write-Host "The APK might have been downloaded to a different location." -ForegroundColor Yellow
    exit 1
}

# Step 4: Installation options
Write-Host "`nStep 4: Installation Options" -ForegroundColor Yellow
Write-Host "=============================" -ForegroundColor Yellow

Write-Host "Choose how to install the APK on your phone:" -ForegroundColor Cyan
Write-Host "1. Install via ADB (phone connected via USB)" -ForegroundColor White
Write-Host "2. Transfer file manually" -ForegroundColor White
Write-Host "3. Just show me the file location" -ForegroundColor White

$choice = Read-Host "`nEnter your choice (1-3)"

switch ($choice) {
    "1" {
        Write-Host "`nOption 1: Installing via ADB..." -ForegroundColor Cyan
        
        # Check if ADB is available
        try {
            adb version | Out-Null
            Write-Host "✅ ADB found" -ForegroundColor Green
        } catch {
            Write-Host "❌ ADB not found. Please:" -ForegroundColor Red
            Write-Host "1. Run set-env.bat to set up Android tools" -ForegroundColor White
            Write-Host "2. Or install Android SDK Platform Tools" -ForegroundColor White
            break
        }
        
        # Check for connected devices
        Write-Host "`nChecking for connected devices..." -ForegroundColor Cyan
        $devices = adb devices
        Write-Host $devices
        
        if ($devices -like "*device*") {
            Write-Host "`n✅ Device detected!" -ForegroundColor Green
            Write-Host "Installing APK..." -ForegroundColor Cyan
            
            try {
                adb install $latestApk.FullName
                Write-Host "`n✅ APK installed successfully!" -ForegroundColor Green
                Write-Host "You can now open IraChat from your phone's app drawer!" -ForegroundColor White
            } catch {
                Write-Host "`n❌ Installation failed. Make sure:" -ForegroundColor Red
                Write-Host "• USB debugging is enabled on your phone" -ForegroundColor White
                Write-Host "• You've allowed installation from unknown sources" -ForegroundColor White
                Write-Host "• The phone is unlocked" -ForegroundColor White
            }
        } else {
            Write-Host "`n❌ No device detected. Please:" -ForegroundColor Red
            Write-Host "• Connect your phone via USB" -ForegroundColor White
            Write-Host "• Enable USB debugging in Developer Options" -ForegroundColor White
            Write-Host "• Allow USB debugging when prompted" -ForegroundColor White
        }
    }
    
    "2" {
        Write-Host "`nOption 2: Manual Transfer Instructions" -ForegroundColor Cyan
        Write-Host "======================================" -ForegroundColor Cyan
        
        Write-Host "`nAPK Location: $($latestApk.FullName)" -ForegroundColor Yellow
        
        Write-Host "`nTransfer methods:" -ForegroundColor White
        Write-Host "📧 Email: Attach the APK file and email it to yourself" -ForegroundColor Gray
        Write-Host "☁️  Cloud: Upload to Google Drive, Dropbox, or OneDrive" -ForegroundColor Gray
        Write-Host "💾 USB: Copy to phone via USB cable and file manager" -ForegroundColor Gray
        Write-Host "📱 Messaging: Send via WhatsApp, Telegram, etc." -ForegroundColor Gray
        
        Write-Host "`nInstallation on phone:" -ForegroundColor White
        Write-Host "1. Go to Settings > Security (or Privacy)" -ForegroundColor Gray
        Write-Host "2. Enable 'Install from unknown sources' or 'Allow from this source'" -ForegroundColor Gray
        Write-Host "3. Tap the APK file to install" -ForegroundColor Gray
        Write-Host "4. Grant permissions when prompted" -ForegroundColor Gray
        Write-Host "5. Open IraChat from your app drawer!" -ForegroundColor Gray
    }
    
    "3" {
        Write-Host "`nAPK File Information:" -ForegroundColor Cyan
        Write-Host "=====================" -ForegroundColor Cyan
        Write-Host "File: $($latestApk.Name)" -ForegroundColor White
        Write-Host "Location: $($latestApk.FullName)" -ForegroundColor White
        Write-Host "Size: $([math]::Round($latestApk.Length/1MB, 2)) MB" -ForegroundColor White
        Write-Host "Created: $($latestApk.LastWriteTime)" -ForegroundColor White
        
        # Open file location
        Write-Host "`nOpening file location..." -ForegroundColor Cyan
        Start-Process "explorer.exe" -ArgumentList "/select,`"$($latestApk.FullName)`""
    }
    
    default {
        Write-Host "`nInvalid choice. APK is ready at: $($latestApk.FullName)" -ForegroundColor Yellow
    }
}

Write-Host "`n🎉 Your standalone IraChat APK is ready!" -ForegroundColor Green
Write-Host "Once installed, the app will work completely independently!" -ForegroundColor White
