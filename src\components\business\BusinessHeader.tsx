import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BusinessProfile } from '../../types/Business';
import { useTheme } from '../../contexts/ThemeContext';

interface BusinessHeaderProps {
  userBusinessProfile?: BusinessProfile;
  userBusinessProfiles?: BusinessProfile[];
  paddingTop: number;
  onShowRegistration: () => void;
  onBusinessProfilePress?: (business: BusinessProfile) => void; // Navigate to business profile page
  onBusinessSelect?: () => void;
  onSearchPress: () => void;
  onFilterPress: () => void;
  hasActiveFilters?: boolean;
}

export const BusinessHeader: React.FC<BusinessHeaderProps> = ({
  userBusinessProfile,
  userBusinessProfiles = [],
  paddingTop,
  onShowRegistration,
  onBusinessProfilePress,
  onBusinessSelect,
  onSearchPress,
  onFilterPress,
  hasActiveFilters = false,
}) => {
  const { colors: COLORS, toggleTheme, isDark } = useTheme();
  const [showMenu, setShowMenu] = useState(false);

  const styles = StyleSheet.create({
    headerContainer: {
      backgroundColor: COLORS.background,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
      paddingHorizontal: 16,
    },
    titleRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: 8,
    },
    headerLeft: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    searchButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: COLORS.surface,
      marginRight: 12,
    },
    titleContainer: {
      alignItems: 'flex-start',
    },
    headerCenter: {
      flex: 1,
      alignItems: 'center',
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      color: COLORS.text,
    },
    subtitle: {
      fontSize: 12,
      color: COLORS.textSecondary,
      marginTop: 2,
    },
    activeBusiness: {
      fontSize: 14,
      color: COLORS.primary,
      marginTop: 2,
      fontWeight: '600',
      paddingRight: 16,
    },
    actionButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: COLORS.surface,
      position: 'relative',
    },
    activeFilterButton: {
      backgroundColor: COLORS.primaryLight,
    },
    filterBadge: {
      position: 'absolute',
      top: 6,
      right: 6,
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: COLORS.primary,
    },
    addButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      backgroundColor: COLORS.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    registerButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: COLORS.primary,
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 18,
      gap: 4,
    },
    registerButtonText: {
      color: '#fff',
      fontSize: 12,
      fontWeight: '600',
    },

    businessSelectorButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: COLORS.surface,
      paddingHorizontal: 8,
      paddingVertical: 6,
      borderRadius: 12,
      gap: 4,
    },
    businessSelectorText: {
      color: COLORS.text,
      fontSize: 10,
      fontWeight: '500',
    },
    addBusinessButton: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: COLORS.surface,
      paddingHorizontal: 8,
      paddingVertical: 6,
      borderRadius: 12,
      gap: 4,
      borderWidth: 1,
      borderColor: COLORS.primary,
    },
    addBusinessText: {
      color: COLORS.primary,
      fontSize: 10,
      fontWeight: '600',
    },
    // Menu styles
    menuButton: {
      padding: 8,
      borderRadius: 20,
    },
    menuOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'flex-start',
      alignItems: 'flex-end',
      paddingTop: paddingTop + 60,
      paddingRight: 16,
    },
    menuContainer: {
      backgroundColor: COLORS.background,
      borderRadius: 12,
      paddingVertical: 8,
      minWidth: 280,
      maxWidth: 320,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 5,
      borderWidth: 1,
      borderColor: COLORS.border,
    },
    menuItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 14,
      gap: 12,
    },
    activeMenuItem: {
      backgroundColor: COLORS.surface,
    },
    menuItemText: {
      fontSize: 16,
      color: COLORS.text,
      flex: 1,
    },
    activeMenuItemText: {
      color: COLORS.primary,
      fontWeight: '600',
    },
    menuFilterBadge: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: COLORS.primary,
    },
    // Business-specific menu styles
    menuSeparator: {
      height: 1,
      backgroundColor: COLORS.border,
      marginVertical: 8,
      marginHorizontal: 16,
    },
    menuSectionHeader: {
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    menuSectionTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: COLORS.textSecondary,
      textTransform: 'uppercase',
    },
    businessSection: {
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    businessHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
      marginBottom: 8,
      paddingHorizontal: 16,
      paddingVertical: 8,
      backgroundColor: COLORS.surface,
      borderRadius: 8,
      marginHorizontal: 16,
    },
    businessName: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.text,
      flex: 1,
    },
    businessActions: {
      flexDirection: 'row',
      gap: 8,
      paddingLeft: 24,
    },
    businessActionsContainer: {
      paddingHorizontal: 16,
      marginBottom: 8,
    },
    businessActionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 8,
      paddingHorizontal: 16,
      paddingVertical: 10,
      backgroundColor: COLORS.primary,
      borderRadius: 20,
      width: '100%',
    },
    businessActionText: {
      fontSize: 14,
      color: '#FFFFFF',
      fontWeight: '600',
    },
  });

  return (
    <View style={[styles.headerContainer, { paddingTop: paddingTop + 8 }]}>
      {/* Single Row - Title with Search and Menu */}
      <View style={styles.titleRow}>
        <Text style={styles.title}>IraMarket & Services</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={styles.searchButton}
            onPress={onSearchPress}
          >
            <Ionicons name="search" size={20} color={COLORS.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.menuButton}
            onPress={() => setShowMenu(true)}
          >
            <Ionicons name="menu" size={24} color={COLORS.text} />
            {hasActiveFilters && <View style={styles.filterBadge} />}
          </TouchableOpacity>
        </View>

      </View>

      {/* Menu Modal */}
      <Modal
        visible={showMenu}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowMenu(false)}
      >
        <TouchableOpacity
          style={styles.menuOverlay}
          activeOpacity={1}
          onPress={() => setShowMenu(false)}
        >
          <View style={styles.menuContainer}>
            {/* Filter Option */}
            <TouchableOpacity
              style={[styles.menuItem, hasActiveFilters && styles.activeMenuItem]}
              onPress={() => {
                setShowMenu(false);
                onFilterPress();
              }}
            >
              <Ionicons
                name="options"
                size={20}
                color={hasActiveFilters ? COLORS.primary : COLORS.text}
              />
              <Text style={[styles.menuItemText, hasActiveFilters && styles.activeMenuItemText]}>
                Filters
              </Text>
              {hasActiveFilters && <View style={styles.menuFilterBadge} />}
            </TouchableOpacity>

            {/* Theme Toggle Option */}
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                setShowMenu(false);
                toggleTheme();
              }}
            >
              <Ionicons
                name={isDark ? "sunny-outline" : "moon-outline"}
                size={20}
                color={COLORS.text}
              />
              <Text style={styles.menuItemText}>
                {isDark ? 'Light Mode' : 'Dark Mode'}
              </Text>
            </TouchableOpacity>

            {/* Separator */}
            <View style={styles.menuSeparator} />

            {/* Business Section Header */}
            <View style={styles.menuSectionHeader}>
              <Text style={styles.menuSectionTitle}>My Businesses</Text>
            </View>

            {/* List all user businesses with individual actions */}
            {userBusinessProfiles.length > 0 ? (
              userBusinessProfiles.map((business) => (
                <View key={business.id} style={styles.businessSection}>
                  {/* Business Name - Clickable to navigate to profile */}
                  <TouchableOpacity
                    style={styles.businessHeader}
                    onPress={() => {
                      setShowMenu(false);
                      // Navigate to business profile page
                      if (onBusinessProfilePress) {
                        onBusinessProfilePress(business);
                      }
                    }}
                  >
                    <Text style={styles.businessName} numberOfLines={2}>
                      {business.businessName}
                    </Text>
                    {business.isVerified && (
                      <Ionicons name="checkmark-circle" size={14} color="#3B82F6" />
                    )}
                    <Ionicons name="chevron-forward" size={14} color={COLORS.textSecondary} />
                  </TouchableOpacity>

                  {/* Business Actions - Single Row */}

                </View>
              ))
            ) : (
              /* No businesses - show registration option */
              <TouchableOpacity
                style={styles.menuItem}
                onPress={() => {
                  setShowMenu(false);
                  if (onShowRegistration) {
                    onShowRegistration();
                  }
                }}
              >
                <Text style={[styles.menuItemText, { color: COLORS.primary, fontWeight: '600' }]}>
                  Register Your First Business
                </Text>
              </TouchableOpacity>
            )}

            {/* Add Another Business Option */}
            {userBusinessProfiles.length > 0 && (
              <>
                <View style={styles.menuSeparator} />
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setShowMenu(false);
                    if (onShowRegistration) {
                      onShowRegistration();
                    }
                  }}
                >
                  <Text style={[styles.menuItemText, { color: COLORS.primary, fontWeight: '600' }]}>
                    Add Another Business
                  </Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};
