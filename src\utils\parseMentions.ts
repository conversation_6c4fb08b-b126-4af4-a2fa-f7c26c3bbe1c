// Enhanced Mention Parsing Utilities with Mobile Optimization and Offline Support
import { Dimensions } from 'react-native';

// Get device dimensions for responsive mention handling
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

export interface ParsedMention {
  userId: string;
  username: string;
  displayName?: string;
  startIndex: number;
  endIndex: number;
  isValid?: boolean;
  avatar?: string;
  isOnline?: boolean;
  type?: 'user' | 'group' | 'channel';
}

export interface MentionParseOptions {
  maxMentions?: number;
  allowGroupMentions?: boolean;
  allowChannelMentions?: boolean;
  validateUsers?: boolean;
  cacheResults?: boolean;
  mobileOptimized?: boolean;
}

export interface MentionParseResult {
  mentions: ParsedMention[];
  processedText: string;
  hasValidMentions: boolean;
  truncatedMentions?: ParsedMention[];
  warnings?: string[];
}

/**
 * Enhanced mention parsing with mobile optimization and validation
 */
export const parseMentions = (
  caption: string,
  options: MentionParseOptions = {}
): MentionParseResult => {
  const {
    maxMentions = isSmallDevice ? 5 : isTablet ? 10 : 20,
    allowGroupMentions = true,
    allowChannelMentions = true,
    validateUsers = false,
    cacheResults = true,
    mobileOptimized = isSmallDevice
  } = options;

  if (!caption || typeof caption !== 'string') {
    return {
      mentions: [],
      processedText: caption || '',
      hasValidMentions: false
    };
  }

  // Check cache first if enabled
  if (cacheResults) {
    const cached = getCachedMentionResults(caption);
    if (cached) {
      return {
        mentions: cached,
        processedText: caption,
        hasValidMentions: cached.some((m: ParsedMention) => m.isValid !== false)
      };
    }
  }

  const mentions: ParsedMention[] = [];
  const warnings: string[] = [];

  // Enhanced regex patterns for different mention types
  const userMentionRegex = /@([a-zA-Z0-9_]{1,20})/g;
  const groupMentionRegex = allowGroupMentions ? /@group:([a-zA-Z0-9_]{1,20})/g : null;
  const channelMentionRegex = allowChannelMentions ? /@channel:([a-zA-Z0-9_]{1,20})/g : null;

  // Parse user mentions
  let match;
  while ((match = userMentionRegex.exec(caption)) !== null) {
    if (mentions.length >= maxMentions) {
      warnings.push(mobileOptimized
        ? `Max ${maxMentions} mentions`
        : `Maximum ${maxMentions} mentions allowed`);
      break;
    }

    const username = match[1];

    // Basic validation
    if (username.length < 2) {
      continue; // Skip very short usernames
    }

    mentions.push({
      username,
      userId: "", // Will be populated when we have user data
      startIndex: match.index,
      endIndex: match.index + match[0].length,
      type: 'user',
      isValid: !validateUsers // Assume valid if not validating
    });
  }

  // Parse group mentions
  if (groupMentionRegex) {
    groupMentionRegex.lastIndex = 0; // Reset regex
    while ((match = groupMentionRegex.exec(caption)) !== null) {
      if (mentions.length >= maxMentions) {
        break;
      }

      mentions.push({
        username: match[1],
        userId: "",
        startIndex: match.index,
        endIndex: match.index + match[0].length,
        type: 'group',
        isValid: !validateUsers
      });
    }
  }

  // Parse channel mentions
  if (channelMentionRegex) {
    channelMentionRegex.lastIndex = 0; // Reset regex
    while ((match = channelMentionRegex.exec(caption)) !== null) {
      if (mentions.length >= maxMentions) {
        break;
      }

      mentions.push({
        username: match[1],
        userId: "",
        startIndex: match.index,
        endIndex: match.index + match[0].length,
        type: 'channel',
        isValid: !validateUsers
      });
    }
  }

  // Sort mentions by start index
  mentions.sort((a, b) => a.startIndex - b.startIndex);

  // Check for overlapping mentions and remove duplicates
  const cleanedMentions = removeDuplicateAndOverlappingMentions(mentions);

  // Cache results if enabled
  if (cacheResults && cleanedMentions.length > 0) {
    cacheMentionResults(caption, cleanedMentions);
  }

  return {
    mentions: cleanedMentions,
    processedText: caption,
    hasValidMentions: cleanedMentions.some((m: ParsedMention) => m.isValid !== false),
    warnings: warnings.length > 0 ? warnings : undefined
  };
};

/**
 * Remove duplicate and overlapping mentions
 */
const removeDuplicateAndOverlappingMentions = (mentions: ParsedMention[]): ParsedMention[] => {
  const cleaned: ParsedMention[] = [];
  const seen = new Set<string>();

  for (const mention of mentions) {
    const key = `${mention.type}:${mention.username}:${mention.startIndex}`;

    if (seen.has(key)) {
      continue; // Skip duplicate
    }

    // Check for overlaps with existing mentions
    const hasOverlap = cleaned.some(existing =>
      (mention.startIndex >= existing.startIndex && mention.startIndex < existing.endIndex) ||
      (mention.endIndex > existing.startIndex && mention.endIndex <= existing.endIndex) ||
      (mention.startIndex <= existing.startIndex && mention.endIndex >= existing.endIndex)
    );

    if (!hasOverlap) {
      cleaned.push(mention);
      seen.add(key);
    }
  }

  return cleaned;
};

/**
 * Mention cache for better performance
 */
const mentionCache = new Map<string, ParsedMention[]>();
const MAX_CACHE_SIZE = isSmallDevice ? 50 : 100;

/**
 * Cache mention parsing results
 */
const cacheMentionResults = (text: string, mentions: ParsedMention[]): void => {
  // Clear old entries if cache is full
  if (mentionCache.size >= MAX_CACHE_SIZE) {
    const firstKey = mentionCache.keys().next().value;
    if (firstKey) {
      mentionCache.delete(firstKey);
    }
  }

  mentionCache.set(text, mentions);
};

/**
 * Get cached mention results
 */
const getCachedMentionResults = (text: string): ParsedMention[] | null => {
  return mentionCache.get(text) || null;
};

/**
 * Clear mention cache
 */
export const clearMentionCache = (): void => {
  mentionCache.clear();
};

/**
 * Enhanced mention link replacement with mobile optimization
 */
export const replaceMentionsWithLinks = (
  caption: string,
  mentions: ParsedMention[],
  onMentionPress: (_userId: string, _mentionType?: string) => void,
  options: {
    mobileOptimized?: boolean;
    showAvatars?: boolean;
    truncateUsernames?: boolean;
    maxUsernameLength?: number;
  } = {}
): {
  text: string;
  mentions: ParsedMention[];
  onMentionPress: (_userId: string, _mentionType?: string) => void;
  hasLinks: boolean;
} => {
  const {
    mobileOptimized = isSmallDevice,
    showAvatars = !isSmallDevice,
    truncateUsernames = isSmallDevice,
    maxUsernameLength = isSmallDevice ? 10 : 15
  } = options;

  let processedCaption = caption;
  const processedMentions: ParsedMention[] = [];

  // Sort mentions by start index in reverse order to avoid index shifting
  const sortedMentions = [...mentions].sort(
    (a, b) => b.startIndex - a.startIndex,
  );

  for (const mention of sortedMentions) {
    let displayUsername = mention.username;

    // Truncate username for mobile if needed
    if (truncateUsernames && displayUsername.length > maxUsernameLength) {
      displayUsername = displayUsername.substring(0, maxUsernameLength - 1) + '…';
    }

    // Create mention text based on type
    let mentionText: string;
    let linkProtocol: string;

    switch (mention.type) {
      case 'group':
        mentionText = `@group:${displayUsername}`;
        linkProtocol = 'group';
        break;
      case 'channel':
        mentionText = `@channel:${displayUsername}`;
        linkProtocol = 'channel';
        break;
      default:
        mentionText = `@${displayUsername}`;
        linkProtocol = 'user';
    }

    // Create a clickable link format
    const linkText = mobileOptimized
      ? `[${mentionText}](${linkProtocol}:${mention.userId})`
      : `[${mentionText}](${linkProtocol}:${mention.userId}:${mention.username})`;

    processedCaption =
      processedCaption.slice(0, mention.startIndex) +
      linkText +
      processedCaption.slice(mention.endIndex);

    // Store the processed mention with updated indices
    const linkLength = linkText.length;
    const originalLength = mention.endIndex - mention.startIndex;
    const indexDifference = linkLength - originalLength;

    processedMentions.push({
      ...mention,
      endIndex: mention.endIndex + indexDifference,
      displayName: displayUsername
    });
  }

  return {
    text: processedCaption,
    mentions: processedMentions,
    onMentionPress,
    hasLinks: processedMentions.length > 0
  };
};

/**
 * Validate mention against user database
 */
export const validateMention = async (
  mention: ParsedMention,
  userLookupFunction?: (_username: string) => Promise<{ userId: string; displayName?: string; avatar?: string; isOnline?: boolean } | null>
): Promise<ParsedMention> => {
  if (!userLookupFunction) {
    return mention;
  }

  try {
    const userInfo = await userLookupFunction(mention.username);

    return {
      ...mention,
      userId: userInfo?.userId || '',
      displayName: userInfo?.displayName,
      avatar: userInfo?.avatar,
      isOnline: userInfo?.isOnline,
      isValid: !!userInfo
    };
  } catch (error) {
    return {
      ...mention,
      isValid: false
    };
  }
};

/**
 * Batch validate multiple mentions
 */
export const validateMentions = async (
  mentions: ParsedMention[],
  userLookupFunction?: (_username: string) => Promise<{ userId: string; displayName?: string; avatar?: string; isOnline?: boolean } | null>
): Promise<ParsedMention[]> => {
  if (!userLookupFunction || mentions.length === 0) {
    return mentions;
  }

  const validationPromises = mentions.map(mention => validateMention(mention, userLookupFunction));

  try {
    return await Promise.all(validationPromises);
  } catch (error) {
    // Return original mentions if validation fails
    return mentions;
  }
};

/**
 * Extract usernames from mentions for autocomplete
 */
export const extractUsernamesForAutocomplete = (
  text: string,
  cursorPosition: number,
  options: { maxSuggestions?: number; minChars?: number } = {}
): {
  query: string;
  startIndex: number;
  endIndex: number;
  isActive: boolean;
  maxSuggestions: number;
} => {
  const { maxSuggestions = isSmallDevice ? 5 : 10, minChars = 1 } = options;

  // Find the current mention being typed
  const beforeCursor = text.substring(0, cursorPosition);
  const afterCursor = text.substring(cursorPosition);

  // Look for @ symbol before cursor
  const mentionMatch = beforeCursor.match(/@([a-zA-Z0-9_]*)$/);

  if (!mentionMatch) {
    return {
      query: '',
      startIndex: -1,
      endIndex: -1,
      isActive: false,
      maxSuggestions
    };
  }

  const query = mentionMatch[1];
  const startIndex = beforeCursor.length - mentionMatch[0].length;

  // Find end of mention (space or end of string)
  const endMatch = afterCursor.match(/^[a-zA-Z0-9_]*/);
  const endIndex = cursorPosition + (endMatch ? endMatch[0].length : 0);

  return {
    query: query + (endMatch ? endMatch[0] : ''),
    startIndex,
    endIndex,
    isActive: query.length >= minChars,
    maxSuggestions // Include for autocomplete components
  };
};

/**
 * Format mentions for display with avatars and status
 */
export const formatMentionsForDisplay = (
  mentions: ParsedMention[],
  options: {
    showAvatars?: boolean;
    showOnlineStatus?: boolean;
    maxDisplay?: number;
    mobileOptimized?: boolean;
  } = {}
): {
  mention: ParsedMention;
  displayText: string;
  hasAvatar: boolean;
  statusIndicator?: 'online' | 'offline' | 'away';
}[] => {
  const {
    showAvatars = !isSmallDevice,
    showOnlineStatus = true,
    maxDisplay = isSmallDevice ? 3 : 5,
    mobileOptimized = isSmallDevice
  } = options;

  return mentions
    .slice(0, maxDisplay)
    .map(mention => {
      const displayText = mobileOptimized && mention.displayName
        ? mention.displayName
        : mention.username;

      return {
        mention,
        displayText,
        hasAvatar: showAvatars && !!mention.avatar,
        statusIndicator: showOnlineStatus
          ? (mention.isOnline ? 'online' : 'offline')
          : undefined
      };
    });
};

/**
 * Get mention statistics
 */
export const getMentionStatistics = (mentions: ParsedMention[]): {
  total: number;
  byType: Record<string, number>;
  valid: number;
  invalid: number;
  withAvatars: number;
} => {
  const stats = {
    total: mentions.length,
    byType: {} as Record<string, number>,
    valid: 0,
    invalid: 0,
    withAvatars: 0
  };

  mentions.forEach(mention => {
    const type = mention.type || 'user';
    stats.byType[type] = (stats.byType[type] || 0) + 1;

    if (mention.isValid !== false) {
      stats.valid++;
    } else {
      stats.invalid++;
    }

    if (mention.avatar) {
      stats.withAvatars++;
    }
  });

  return stats;
};

// Export default object with all functions
export default {
  parseMentions,
  replaceMentionsWithLinks,
  validateMention,
  validateMentions,
  extractUsernamesForAutocomplete,
  formatMentionsForDisplay,
  getMentionStatistics,
  clearMentionCache,
};
