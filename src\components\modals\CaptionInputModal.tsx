import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  TextInput,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
};

interface CaptionInputModalProps {
  visible: boolean;
  mediaUri: string;
  mediaType: 'image' | 'video';
  caption: string;
  onCaptionChange: (caption: string) => void;
  onPost: () => void;
  onClose: () => void;
  isUploading: boolean;
  uploadProgress: number;
}

export const CaptionInputModal: React.FC<CaptionInputModalProps> = ({
  visible,
  mediaUri,
  mediaType,
  caption,
  onCaptionChange,
  onPost,
  onClose,
  isUploading,
  uploadProgress,
}) => {
  // Handle Android back button
  useEffect(() => {
    if (!visible) return;

    const handleBackPress = () => {
      if (!isUploading) {
        onClose();
      }
      return true; // Prevent default back action
    };

    // Add back button listener for Android
    const backHandler = require('react-native').BackHandler;
    const subscription = backHandler?.addEventListener('hardwareBackPress', handleBackPress);

    return () => {
      subscription?.remove();
    };
  }, [visible, isUploading, onClose]);

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity 
            onPress={onClose} 
            style={styles.closeButton}
            disabled={isUploading}
          >
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Add Caption</Text>
          <TouchableOpacity 
            onPress={onPost} 
            style={[
              styles.postButton,
              { backgroundColor: isUploading ? COLORS.textMuted : COLORS.primary }
            ]}
            disabled={isUploading}
          >
            <Text style={styles.postButtonText}>
              {isUploading ? 'Posting...' : 'Post'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.content}>
          {/* Media Preview */}
          {mediaUri && (
            <View style={styles.mediaPreviewContainer}>
              {mediaType === 'video' ? (
                <Video
                  source={{ uri: mediaUri }}
                  style={styles.mediaPreview}
                  resizeMode={ResizeMode.COVER}
                  shouldPlay={false}
                  useNativeControls={false}
                />
              ) : (
                <Image
                  source={{ uri: mediaUri }}
                  style={styles.mediaPreview}
                  resizeMode="cover"
                />
              )}
              {mediaType === 'video' && (
                <View style={styles.videoOverlay}>
                  <Ionicons name="play-circle" size={40} color="rgba(255,255,255,0.8)" />
                </View>
              )}
            </View>
          )}

          {/* Caption Input */}
          <View style={styles.captionContainer}>
            <TextInput
              style={styles.captionInput}
              placeholder="Write a caption..."
              placeholderTextColor={COLORS.textMuted}
              value={caption}
              onChangeText={onCaptionChange}
              multiline
              maxLength={500}
              editable={!isUploading}
            />
            <Text style={styles.characterCount}>
              {caption.length}/500
            </Text>
          </View>

          {/* Upload Progress */}
          {isUploading && (
            <View style={styles.uploadContainer}>
              <Text style={styles.uploadText}>
                Uploading... {Math.round(uploadProgress)}%
              </Text>
              <View style={styles.progressBarContainer}>
                <View 
                  style={[
                    styles.progressBar,
                    { width: `${uploadProgress}%` }
                  ]} 
                />
              </View>
            </View>
          )}

          {/* Additional Options */}
          <View style={styles.optionsContainer}>
            <TouchableOpacity 
              style={styles.optionItem}
              disabled={isUploading}
            >
              <Ionicons name="location-outline" size={20} color={COLORS.primary} />
              <Text style={styles.optionText}>Add Location</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.optionItem}
              disabled={isUploading}
            >
              <Ionicons name="people-outline" size={20} color={COLORS.primary} />
              <Text style={styles.optionText}>Tag People</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={styles.optionItem}
              disabled={isUploading}
            >
              <Ionicons name="musical-notes-outline" size={20} color={COLORS.primary} />
              <Text style={styles.optionText}>Add Music</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  postButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  postButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  mediaPreviewContainer: {
    alignItems: 'center',
    marginBottom: 20,
    position: 'relative',
  },
  mediaPreview: {
    width: 200,
    height: 150,
    borderRadius: 8,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captionContainer: {
    marginBottom: 20,
  },
  captionInput: {
    backgroundColor: COLORS.surfaceLight,
    color: COLORS.text,
    padding: 15,
    borderRadius: 8,
    minHeight: 100,
    textAlignVertical: 'top',
    fontSize: 16,
    lineHeight: 22,
  },
  characterCount: {
    color: COLORS.textMuted,
    fontSize: 12,
    textAlign: 'right',
    marginTop: 8,
  },
  uploadContainer: {
    marginBottom: 20,
  },
  uploadText: {
    color: COLORS.textSecondary,
    marginBottom: 8,
    textAlign: 'center',
  },
  progressBarContainer: {
    backgroundColor: COLORS.surfaceLight,
    height: 4,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    backgroundColor: COLORS.primary,
    height: '100%',
  },
  optionsContainer: {
    marginTop: 20,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: COLORS.surface,
    borderRadius: 8,
    marginBottom: 8,
  },
  optionText: {
    color: COLORS.text,
    fontSize: 16,
    marginLeft: 12,
    fontWeight: '500',
  },
});
