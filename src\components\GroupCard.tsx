/**
 * Beautiful Group Card Component for IraChat
 * Responsive design with animations and sky blue branding
 */

import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, SHADOWS, ANIMATIONS } from '../styles/iraChatDesignSystem';
import { ResponsiveScale, ComponentSizes, ResponsiveSpacing, ResponsiveTypography } from '../utils/responsiveUtils';
import { Avatar } from './Avatar';

interface GroupCardProps {
  groupId: string;
  groupName: string;
  groupDescription?: string;
  groupImage?: string;
  memberCount: number;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
  isPrivate?: boolean;
  onPress: (groupId: string) => void;
  onLongPress?: (groupId: string) => void;
  animated?: boolean;
  index?: number;
  variant?: 'default' | 'compact' | 'detailed';
}

export const GroupCard: React.FC<GroupCardProps> = ({
  groupId,
  groupName,
  groupDescription,
  groupImage,
  memberCount,
  lastMessage,
  lastMessageTime,
  unreadCount = 0,
  isPrivate = false,
  onPress,
  onLongPress,
  animated = true,
  index = 0,
  variant = 'default',
}) => {
  // Beautiful animation refs
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const slideAnimation = useRef(new Animated.Value(30)).current;
  const scaleAnimation = useRef(new Animated.Value(1)).current;

  // Entrance animation with stagger
  useEffect(() => {
    if (animated) {
      Animated.sequence([
        Animated.delay(index * 100),
        Animated.parallel([
          Animated.timing(fadeAnimation, {
            toValue: 1,
            duration: ANIMATIONS.normal,
            useNativeDriver: true,
          }),
          Animated.spring(slideAnimation, {
            toValue: 0,
            tension: 50,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
      ]).start();
    } else {
      fadeAnimation.setValue(1);
      slideAnimation.setValue(0);
    }
  }, [animated, index]);

  // Press animations
  const handlePressIn = () => {
    if (animated) {
      Animated.spring(scaleAnimation, {
        toValue: 0.95,
        useNativeDriver: true,
      }).start();
    }
  };

  const handlePressOut = () => {
    if (animated) {
      Animated.spring(scaleAnimation, {
        toValue: 1,
        useNativeDriver: true,
      }).start();
    }
  };

  // Wrapper functions to handle TouchableOpacity events
  const handlePress = () => {
    onPress(groupId);
  };

  const handleLongPress = () => {
    onLongPress?.(groupId);
  };

  const renderGroupAvatar = () => {
    if (groupImage) {
      return (
        <Avatar
          imageUrl={groupImage}
          name={groupName}
          size="large"
          animated={animated}
          variant="gradient"
        />
      );
    }

    // Fallback group avatar with gradient
    return (
      <LinearGradient
        colors={IRACHAT_COLORS.skyGradient as any}
        style={styles.groupAvatarFallback}
      >
        <Ionicons
          name="people"
          size={ResponsiveScale.iconSize(32)}
          color={IRACHAT_COLORS.textOnPrimary}
        />
      </LinearGradient>
    );
  };

  const renderMemberCount = () => (
    <View style={styles.memberCountContainer}>
      <Ionicons
        name="people"
        size={ResponsiveScale.iconSize(12)}
        color={IRACHAT_COLORS.textSecondary}
      />
      <Text style={styles.memberCountText}>
        {memberCount} {memberCount === 1 ? 'member' : 'members'}
      </Text>
    </View>
  );

  const renderPrivacyIndicator = () => {
    if (!isPrivate) return null;

    return (
      <View style={styles.privacyIndicator}>
        <Ionicons
          name="lock-closed"
          size={ResponsiveScale.iconSize(12)}
          color={IRACHAT_COLORS.warning}
        />
      </View>
    );
  };

  const renderUnreadBadge = () => {
    if (unreadCount === 0) return null;

    return (
      <View style={styles.unreadBadge}>
        <Text style={styles.unreadText}>
          {unreadCount > 99 ? '99+' : unreadCount}
        </Text>
      </View>
    );
  };

  const renderLastMessage = () => {
    if (!lastMessage || variant === 'compact') return null;

    return (
      <View style={styles.lastMessageContainer}>
        <Text style={styles.lastMessageText} numberOfLines={1}>
          {lastMessage}
        </Text>
        {lastMessageTime && (
          <Text style={styles.lastMessageTime}>
            {lastMessageTime}
          </Text>
        )}
      </View>
    );
  };

  const animatedStyle = {
    opacity: fadeAnimation,
    transform: [
      { translateY: slideAnimation },
      { scale: scaleAnimation }
    ],
  };

  if (variant === 'compact') {
    return (
      <Animated.View style={[styles.compactContainer, animatedStyle]}>
        <TouchableOpacity
          onPress={handlePress}
          onLongPress={handleLongPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          style={styles.compactCard}
          activeOpacity={1}
        >
          {renderGroupAvatar()}
          <View style={styles.compactContent}>
            <View style={styles.compactHeader}>
              <Text style={styles.groupNameCompact} numberOfLines={1}>
                {groupName}
              </Text>
              {renderPrivacyIndicator()}
              {renderUnreadBadge()}
            </View>
            {renderMemberCount()}
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  }

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <TouchableOpacity
        onPress={handlePress}
        onLongPress={handleLongPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={styles.card}
        activeOpacity={1}
      >
        <View style={styles.header}>
          {renderGroupAvatar()}
          <View style={styles.headerContent}>
            <View style={styles.titleRow}>
              <Text style={styles.groupName} numberOfLines={1}>
                {groupName}
              </Text>
              {renderPrivacyIndicator()}
              {renderUnreadBadge()}
            </View>
            
            {groupDescription && variant === 'detailed' && (
              <Text style={styles.groupDescription} numberOfLines={2}>
                {groupDescription}
              </Text>
            )}
            
            {renderMemberCount()}
          </View>
        </View>
        
        {renderLastMessage()}
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: ResponsiveSpacing.screenPadding,
    marginVertical: ResponsiveSpacing.sm,
  },
  card: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: ResponsiveScale.borderRadius(16),
    padding: ResponsiveSpacing.md,
    ...SHADOWS.md,
  },
  compactContainer: {
    marginHorizontal: ResponsiveSpacing.sm,
    marginVertical: ResponsiveSpacing.xs,
  },
  compactCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: ResponsiveScale.borderRadius(12),
    padding: ResponsiveSpacing.sm,
    ...SHADOWS.sm,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  headerContent: {
    flex: 1,
    marginLeft: ResponsiveSpacing.md,
  },
  compactContent: {
    flex: 1,
    marginLeft: ResponsiveSpacing.sm,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.xs,
  },
  compactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  groupName: {
    fontSize: ResponsiveTypography.fontSize.lg,
    fontWeight: '500' as const,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
    flex: 1,
  },
  groupNameCompact: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '500' as const,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
    flex: 1,
  },
  groupDescription: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    marginBottom: ResponsiveSpacing.xs,
    lineHeight: ResponsiveTypography.fontSize.sm * 1.4,
  },
  groupAvatarFallback: {
    width: ComponentSizes.avatarSize.large,
    height: ComponentSizes.avatarSize.large,
    borderRadius: ComponentSizes.avatarSize.large / 2,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.sm,
  },
  memberCountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  memberCountText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    marginLeft: ResponsiveSpacing.xs,
  },
  privacyIndicator: {
    marginLeft: ResponsiveSpacing.sm,
  },
  unreadBadge: {
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: ResponsiveScale.borderRadius(10),
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.xs,
    marginLeft: ResponsiveSpacing.sm,
    ...SHADOWS.sm,
  },
  unreadText: {
    fontSize: ResponsiveTypography.fontSize.xs,
    fontWeight: '600' as const,
    color: IRACHAT_COLORS.textOnPrimary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  lastMessageContainer: {
    marginTop: ResponsiveSpacing.md,
    paddingTop: ResponsiveSpacing.sm,
    borderTopWidth: 1,
    borderTopColor: IRACHAT_COLORS.borderLight,
  },
  lastMessageText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    marginBottom: ResponsiveSpacing.xs,
  },
  lastMessageTime: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
});

export default GroupCard;
