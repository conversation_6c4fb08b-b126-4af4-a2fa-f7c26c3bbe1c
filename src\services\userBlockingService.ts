// 🚫 USER BLOCKING SERVICE - Block and unblock users
// Prevents blocked users from sending messages and seeing status

import {
  doc,
  updateDoc,
  getDoc,
  arrayUnion,
  arrayRemove,
  collection,
  query,
  where,
  getDocs,
  serverTimestamp,
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface BlockedUser {
  userId: string;
  userName: string;
  userAvatar?: string;
  blockedAt: Date;
  reason?: string;
}

export interface UserBlockingData {
  blockedUsers: string[]; // Array of blocked user IDs
  blockedBy: string[]; // Array of user IDs who blocked this user
  lastUpdated: Date;
}

class UserBlockingService {
  private isInitialized = false;
  private offlineQueue: Map<string, any[]> = new Map();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();

      // Set up network state listener for offline sync
      networkStateManager.addListener('userBlockingService', (networkState) => {
        if (networkState.isConnected) {
          this.syncOfflineData();
        }
      });

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async syncOfflineData(): Promise<void> {
    try {
      // Sync any queued blocking actions when back online
      for (const [userId, queuedData] of this.offlineQueue.entries()) {
        for (const data of queuedData) {
          try {
            await this.syncBlockingActionToFirebase(userId, data);
          } catch (error) {
            // Keep in queue for retry
            continue;
          }
        }
        // Clear successfully synced data
        this.offlineQueue.delete(userId);
      }
    } catch (error) {
      // Sync failed - will retry on next connection
    }
  }

  private async syncBlockingActionToFirebase(_userId: string, data: any): Promise<void> {
    if (data.type === 'block') {
      await this.blockUser(data.currentUserId, data.targetUserId, data.targetUserName, data.targetUserAvatar, data.reason);
    } else if (data.type === 'unblock') {
      await this.unblockUser(data.currentUserId, data.targetUserId);
    }
  }

  private async storeOfflineBlockingAction(userId: string, data: any): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO blocking_queue (
          userId, data, timestamp, synced
        ) VALUES (?, ?, ?, ?)
      `, [userId, JSON.stringify(data), Date.now(), 0]);

      // Also add to memory queue for immediate retry
      if (!this.offlineQueue.has(userId)) {
        this.offlineQueue.set(userId, []);
      }
      this.offlineQueue.get(userId)!.push(data);
    } catch (error) {
      // Offline storage failed
    }
  }

  private async cacheBlockedUserOffline(currentUserId: string, blockedUser: BlockedUser): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO cached_blocked_users (
          currentUserId, blockedUserId, userName, userAvatar, blockedAt, reason
        ) VALUES (?, ?, ?, ?, ?, ?)
      `, [
        currentUserId,
        blockedUser.userId,
        blockedUser.userName,
        blockedUser.userAvatar || '',
        blockedUser.blockedAt.getTime(),
        blockedUser.reason || ''
      ]);
    } catch (error) {
      // Cache failed - continue without caching
    }
  }

  private async getOfflineBlockedUsers(currentUserId: string): Promise<BlockedUser[]> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const result = await database.getAllAsync(`
        SELECT * FROM cached_blocked_users WHERE currentUserId = ?
      `, [currentUserId]);

      return result.map((row: any) => ({
        userId: row.blockedUserId,
        userName: row.userName,
        userAvatar: row.userAvatar || undefined,
        blockedAt: new Date(row.blockedAt),
        reason: row.reason || undefined,
      }));
    } catch (error) {
      return [];
    }
  }
  /**
   * Block a user
   */
  async blockUser(
    currentUserId: string,
    targetUserId: string,
    targetUserName: string,
    targetUserAvatar?: string,
    reason?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (currentUserId === targetUserId) {
        return { success: false, error: 'Cannot block yourself' };
      }

      const blockData = {
        type: 'block',
        currentUserId,
        targetUserId,
        targetUserName,
        targetUserAvatar,
        reason,
      };

      if (networkStateManager.isOnline()) {
        // Update current user's blocked list
        const currentUserRef = doc(db, 'users', currentUserId);
        await updateDoc(currentUserRef, {
          blockedUsers: arrayUnion(targetUserId),
          lastUpdated: serverTimestamp(),
        });

        // Update target user's blockedBy list
        const targetUserRef = doc(db, 'users', targetUserId);
        await updateDoc(targetUserRef, {
          blockedBy: arrayUnion(currentUserId),
          lastUpdated: serverTimestamp(),
        });

        // Create block record for detailed tracking
        const blockRef = doc(db, 'blocks', `${currentUserId}_${targetUserId}`);
        await updateDoc(blockRef, {
          blockerId: currentUserId,
          blockedUserId: targetUserId,
          blockedUserName: targetUserName,
          blockedUserAvatar: targetUserAvatar,
          blockedAt: serverTimestamp(),
          reason: reason || '',
          isActive: true,
        });
      } else {
        // Store offline for later sync
        await this.storeOfflineBlockingAction(currentUserId, blockData);
      }

      // Cache blocked user offline
      await this.cacheBlockedUserOffline(currentUserId, {
        userId: targetUserId,
        userName: targetUserName,
        userAvatar: targetUserAvatar,
        blockedAt: new Date(),
        reason,
      });

      return { success: true };
    } catch (error) {
      // If online operation fails, store offline
      try {
        await this.storeOfflineBlockingAction(currentUserId, {
          type: 'block',
          currentUserId,
          targetUserId,
          targetUserName,
          targetUserAvatar,
          reason,
        });
      } catch (offlineError) {
        // Both online and offline failed
      }

      return { success: false, error: 'Failed to block user' };
    }
  }

  /**
   * Unblock a user
   */
  async unblockUser(
    currentUserId: string,
    targetUserId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const unblockData = {
        type: 'unblock',
        currentUserId,
        targetUserId,
      };

      if (networkStateManager.isOnline()) {
        // Update current user's blocked list
        const currentUserRef = doc(db, 'users', currentUserId);
        await updateDoc(currentUserRef, {
          blockedUsers: arrayRemove(targetUserId),
          lastUpdated: serverTimestamp(),
        });

        // Update target user's blockedBy list
        const targetUserRef = doc(db, 'users', targetUserId);
        await updateDoc(targetUserRef, {
          blockedBy: arrayRemove(currentUserId),
          lastUpdated: serverTimestamp(),
        });

        // Deactivate block record
        const blockRef = doc(db, 'blocks', `${currentUserId}_${targetUserId}`);
        await updateDoc(blockRef, {
          isActive: false,
          unblockedAt: serverTimestamp(),
        });
      } else {
        // Store offline for later sync
        await this.storeOfflineBlockingAction(currentUserId, unblockData);
      }

      // Remove from offline cache
      try {
        const database = offlineDatabaseService.getDatabase();
        await database.runAsync(`
          DELETE FROM cached_blocked_users
          WHERE currentUserId = ? AND blockedUserId = ?
        `, [currentUserId, targetUserId]);
      } catch (cacheError) {
        // Cache removal failed - continue
      }

      return { success: true };
    } catch (error) {
      // If online operation fails, store offline
      try {
        await this.storeOfflineBlockingAction(currentUserId, {
          type: 'unblock',
          currentUserId,
          targetUserId,
        });
      } catch (offlineError) {
        // Both online and offline failed
      }

      return { success: false, error: 'Failed to unblock user' };
    }
  }

  /**
   * Check if a user is blocked
   */
  async isUserBlocked(currentUserId: string, targetUserId: string): Promise<boolean> {
    try {
      const userRef = doc(db, 'users', currentUserId);
      const userDoc = await getDoc(userRef);
      
      if (userDoc.exists()) {
        const userData = userDoc.data();
        const blockedUsers = userData.blockedUsers || [];
        return blockedUsers.includes(targetUserId);
      }
      
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if current user is blocked by target user
   */
  async isBlockedByUser(currentUserId: string, targetUserId: string): Promise<boolean> {
    try {
      const userRef = doc(db, 'users', currentUserId);
      const userDoc = await getDoc(userRef);

      if (userDoc.exists()) {
        const userData = userDoc.data();
        const blockedBy = userData.blockedBy || [];
        return blockedBy.includes(targetUserId);
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get list of blocked users
   */
  async getBlockedUsers(currentUserId: string): Promise<BlockedUser[]> {
    try {
      if (networkStateManager.isOnline()) {
        const blocksRef = collection(db, 'blocks');
        const q = query(
          blocksRef,
          where('blockerId', '==', currentUserId),
          where('isActive', '==', true)
        );

        const snapshot = await getDocs(q);
        const blockedUsers: BlockedUser[] = [];

        snapshot.docs.forEach(doc => {
          const data = doc.data();
          const blockedUser = {
            userId: data.blockedUserId,
            userName: data.blockedUserName,
            userAvatar: data.blockedUserAvatar,
            blockedAt: data.blockedAt?.toDate() || new Date(),
            reason: data.reason,
          };
          blockedUsers.push(blockedUser);
        });

        // Cache blocked users offline
        for (const blockedUser of blockedUsers) {
          await this.cacheBlockedUserOffline(currentUserId, blockedUser);
        }

        return blockedUsers;
      } else {
        // Return offline cached blocked users
        return await this.getOfflineBlockedUsers(currentUserId);
      }
    } catch (error) {
      // Fallback to offline data
      return await this.getOfflineBlockedUsers(currentUserId);
    }
  }

  /**
   * Check if users can communicate (neither has blocked the other)
   */
  async canCommunicate(user1Id: string, user2Id: string): Promise<boolean> {
    try {
      const [user1BlocksUser2, user2BlocksUser1] = await Promise.all([
        this.isUserBlocked(user1Id, user2Id),
        this.isUserBlocked(user2Id, user1Id),
      ]);

      return !user1BlocksUser2 && !user2BlocksUser1;
    } catch (error) {
      return false;
    }
  }

  /**
   * Filter messages based on blocking status
   */
  filterMessagesForBlockedUsers(
    messages: any[],
    _currentUserId: string,
    blockedUsers: string[]
  ): any[] {
    return messages.filter(message => {
      // Hide messages from blocked users
      if (blockedUsers.includes(message.senderId)) {
        return false;
      }
      
      // Hide messages where current user is blocked by sender
      // (This would be handled by the backend in a real app)
      return true;
    });
  }

  /**
   * Get blocking status between two users
   */
  async getBlockingStatus(
    currentUserId: string,
    targetUserId: string
  ): Promise<{
    currentUserBlocked: boolean;
    blockedByTarget: boolean;
    canCommunicate: boolean;
  }> {
    try {
      const [currentUserBlocked, blockedByTarget] = await Promise.all([
        this.isUserBlocked(currentUserId, targetUserId),
        this.isBlockedByUser(currentUserId, targetUserId),
      ]);

      return {
        currentUserBlocked,
        blockedByTarget,
        canCommunicate: !currentUserBlocked && !blockedByTarget,
      };
    } catch (error) {
      return {
        currentUserBlocked: false,
        blockedByTarget: false,
        canCommunicate: true,
      };
    }
  }

  /**
   * Report and block user (for abuse cases)
   */
  async reportAndBlockUser(
    currentUserId: string,
    targetUserId: string,
    targetUserName: string,
    reason: string,
    reportDetails?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Block the user first
      const blockResult = await this.blockUser(
        currentUserId,
        targetUserId,
        targetUserName,
        undefined,
        reason
      );

      if (!blockResult.success) {
        return blockResult;
      }

      // Create abuse report
      const reportRef = doc(db, 'reports', `${currentUserId}_${targetUserId}_${Date.now()}`);
      await updateDoc(reportRef, {
        reporterId: currentUserId,
        reportedUserId: targetUserId,
        reportedUserName: targetUserName,
        reason,
        details: reportDetails || '',
        reportedAt: serverTimestamp(),
        status: 'pending',
        type: 'user_abuse',
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to report and block user' };
    }
  }
}

// Export singleton instance
export const userBlockingService = new UserBlockingService();
export default userBlockingService;
