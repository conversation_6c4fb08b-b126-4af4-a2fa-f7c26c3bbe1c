import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  SafeAreaView,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BusinessProfile, BusinessPost, ProductCategory } from '../../types/Business';

const COLORS = {
  primary: '#1DA1F2',
  background: '#FFFFFF',
  text: '#14171A',
  textSecondary: '#657786',
  border: '#E1E8ED',
  surface: '#F7F9FA',
  success: '#17BF63',
  warning: '#FFAD1F',
  error: '#E0245E',
};

// Product status options with colors
const PRODUCT_STATUS_OPTIONS = [
  { id: 'available', label: 'Available', color: '#17BF63', icon: 'checkmark-circle' },
  { id: 'out_of_stock', label: 'Out of Stock', color: '#E0245E', icon: 'close-circle' },
  { id: 'new', label: 'New', color: '#1DA1F2', icon: 'sparkles' },
  { id: 'refurbished', label: 'Refurbished', color: '#FFAD1F', icon: 'construct' },
  { id: 'second_hand', label: 'Second Hand', color: '#8B5CF6', icon: 'repeat' },
  { id: 'limited', label: 'Limited Edition', color: '#F59E0B', icon: 'star' },
];

interface EnhancedAddProductModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (post: BusinessPost) => void;
  businessProfile: BusinessProfile;
}

export const EnhancedAddProductModal: React.FC<EnhancedAddProductModalProps> = ({
  visible,
  onClose,
  onSuccess,
  businessProfile,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [productForm, setProductForm] = useState({
    title: '',
    description: '',
    category: 'other' as ProductCategory,
    price: '',
    currency: 'UGX',
    isNegotiable: false,
    availability: 'available',
    status: 'available', // New field for product status
    tags: '',
    locations: '', // Multiple locations separated by commas
    contactPhone: businessProfile.contactInfo.primaryPhone || '',
    contactEmail: businessProfile.contactInfo.email || '',
    warranty: '',
    brand: '',
    model: '',
    condition: 'new',
  });

  const resetForm = () => {
    setProductForm({
      title: '',
      description: '',
      category: 'other' as ProductCategory,
      price: '',
      currency: 'UGX',
      isNegotiable: false,
      availability: 'available',
      status: 'available',
      tags: '',
      locations: '',
      contactPhone: businessProfile.contactInfo.primaryPhone || '',
      contactEmail: businessProfile.contactInfo.email || '',
      warranty: '',
      brand: '',
      model: '',
      condition: 'new',
    });
  };

  useEffect(() => {
    if (!visible) {
      resetForm();
    }
  }, [visible]);

  const handleSubmit = async () => {
    if (!productForm.title || !productForm.description) {
      Alert.alert('Missing Information', 'Please fill in the title and description.');
      return;
    }

    setIsLoading(true);
    try {
      // Process locations - split by comma and trim
      const processedLocations = productForm.locations
        .split(',')
        .map(loc => loc.trim())
        .filter(loc => loc.length > 0);

      const newPost: BusinessPost = {
        id: Date.now().toString(),
        businessId: businessProfile.id,
        businessName: businessProfile.businessName,
        businessType: businessProfile.businessType,
        isVerified: businessProfile.isVerified,
        title: productForm.title,
        description: productForm.description,
        media: [], // Will be handled by parent component
        tags: productForm.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        category: productForm.category,
        price: productForm.price ? parseFloat(productForm.price) : undefined,
        currency: productForm.currency,
        isNegotiable: productForm.isNegotiable,
        availability: productForm.availability as 'available' | 'out_of_stock' | 'discontinued',
        status: productForm.status as 'available' | 'new' | 'out_of_stock' | 'refurbished' | 'second_hand' | 'limited',
        location: {
          address: processedLocations[0] || businessProfile.location.address,
          city: businessProfile.location.city,
          district: businessProfile.location.district,
          region: businessProfile.location.region,
          country: businessProfile.location.country,
          postalCode: businessProfile.location.postalCode,
          coordinates: businessProfile.location.coordinates,
          landmark: businessProfile.location.landmark,
          directions: businessProfile.location.directions,
        },
        additionalLocations: processedLocations.slice(1), // Additional locations as separate field
        contact: {
          phone: productForm.contactPhone,
          email: productForm.contactEmail,
        },
        productDetails: {
          warranty: productForm.warranty,
          brand: productForm.brand,
          model: productForm.model,
          condition: productForm.condition,
        },
        views: 0,
        likes: [],
        comments: [],
        shares: 0,
        downloads: 0,
        isActive: true,
        isPinned: false,
        isPromoted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      onSuccess(newPost);
      Alert.alert('Success', 'Product added successfully!');
    } catch (error) {
      console.error('❌ Error adding product:', error);
      Alert.alert('Error', 'Failed to add product. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStatusSelector = () => (
    <View style={styles.statusContainer}>
      <Text style={styles.sectionTitle}>Product Status</Text>
      <View style={styles.statusGrid}>
        {PRODUCT_STATUS_OPTIONS.map((status) => (
          <TouchableOpacity
            key={status.id}
            style={[
              styles.statusOption,
              { borderColor: status.color },
              productForm.status === status.id && { backgroundColor: status.color + '20' }
            ]}
            onPress={() => setProductForm(prev => ({ ...prev, status: status.id }))}
          >
            <Ionicons 
              name={status.icon as any} 
              size={20} 
              color={productForm.status === status.id ? status.color : COLORS.textSecondary} 
            />
            <Text style={[
              styles.statusText,
              { color: productForm.status === status.id ? status.color : COLORS.textSecondary }
            ]}>
              {status.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderLocationInput = () => (
    <View style={styles.locationContainer}>
      <Text style={styles.sectionTitle}>Available Locations</Text>
      <Text style={styles.locationHint}>
        Specific Locations
      </Text>
      <TextInput
        style={[styles.input, styles.locationInput]}
        placeholder="Enter locations where product is available..."
        placeholderTextColor={COLORS.textSecondary}
        value={productForm.locations}
        onChangeText={(text) => setProductForm(prev => ({ ...prev, locations: text }))}
        multiline
        textAlignVertical="top"
      />
      {productForm.locations.length > 0 && (
        <View style={styles.locationPreview}>
          {productForm.locations.split(',').map((location, index) => {
            const trimmedLocation = location.trim();
            if (!trimmedLocation) return null;
            
            const colors = ['#1DA1F2', '#17BF63', '#FFAD1F', '#E0245E', '#8B5CF6', '#F59E0B'];
            const color = colors[index % colors.length];
            
            return (
              <View key={index} style={[styles.locationTag, { backgroundColor: color + '20', borderColor: color }]}>
                <Text style={[styles.locationTagText, { color }]}>
                  {trimmedLocation}
                </Text>
              </View>
            );
          })}
        </View>
      )}
    </View>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <SafeAreaView style={{ flex: 1, backgroundColor: COLORS.background }}>
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose}>
              <Text style={styles.cancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.title}>Add Product</Text>
            <TouchableOpacity
              style={[!productForm.title || !productForm.description || isLoading ? { opacity: 0.6 } : {}]}
              onPress={handleSubmit}
              disabled={!productForm.title || !productForm.description || isLoading}
            >
              <Text style={styles.submitText}>Post</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            {/* Basic Product Information */}
            <Text style={styles.sectionTitle}>Product Information</Text>
            
            <TextInput
              style={styles.input}
              placeholder="Product Title *"
              placeholderTextColor={COLORS.textSecondary}
              value={productForm.title}
              onChangeText={(text) => setProductForm(prev => ({ ...prev, title: text }))}
            />

            <TextInput
              style={[styles.input, { height: 100 }]}
              placeholder="Product Description *"
              placeholderTextColor={COLORS.textSecondary}
              value={productForm.description}
              onChangeText={(text) => setProductForm(prev => ({ ...prev, description: text }))}
              multiline
              textAlignVertical="top"
            />

            {/* Product Category */}
            <Text style={styles.sectionTitle}>Product Category</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ marginBottom: 16 }}
              contentContainerStyle={{ paddingHorizontal: 4 }}
            >
              {[
                { id: 'electronics', label: 'Electronics', icon: 'phone-portrait-outline' },
                { id: 'computers', label: 'Computers', icon: 'laptop-outline' },
                { id: 'smartphones', label: 'Smartphones', icon: 'phone-portrait-outline' },
                { id: 'fashion', label: 'Fashion', icon: 'shirt-outline' },
                { id: 'mens_clothing', label: 'Men\'s Clothing', icon: 'man-outline' },
                { id: 'womens_clothing', label: 'Women\'s Clothing', icon: 'woman-outline' },
                { id: 'shoes', label: 'Shoes', icon: 'footsteps-outline' },
                { id: 'home_garden', label: 'Home & Garden', icon: 'home-outline' },
                { id: 'furniture', label: 'Furniture', icon: 'bed-outline' },
                { id: 'automotive', label: 'Automotive', icon: 'car-outline' },
                { id: 'cars', label: 'Cars', icon: 'car-outline' },
                { id: 'beauty_health', label: 'Beauty & Health', icon: 'medical-outline' },
                { id: 'skincare', label: 'Skincare', icon: 'heart-outline' },
                { id: 'sports_outdoors', label: 'Sports & Outdoors', icon: 'football-outline' },
                { id: 'fitness', label: 'Fitness', icon: 'fitness-outline' },
                { id: 'books_media', label: 'Books & Media', icon: 'book-outline' },
                { id: 'toys_games', label: 'Toys & Games', icon: 'game-controller-outline' },
                { id: 'baby_products', label: 'Baby Products', icon: 'heart-outline' },
                { id: 'food_drinks', label: 'Food & Drinks', icon: 'restaurant-outline' },
                { id: 'fresh_food', label: 'Fresh Food', icon: 'nutrition-outline' },
                { id: 'services', label: 'Services', icon: 'construct-outline' },
                { id: 'education', label: 'Education', icon: 'school-outline' },
                { id: 'real_estate', label: 'Real Estate', icon: 'business-outline' },
                { id: 'agriculture', label: 'Agriculture', icon: 'leaf-outline' },
                { id: 'pets', label: 'Pets', icon: 'paw-outline' },
                { id: 'other', label: 'Other', icon: 'ellipsis-horizontal-outline' },
              ].map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryOption,
                    productForm.category === category.id && styles.categorySelected
                  ]}
                  onPress={() => setProductForm(prev => ({ ...prev, category: category.id as ProductCategory }))}
                >
                  <Ionicons
                    name={category.icon as any}
                    size={18}
                    color={productForm.category === category.id ? COLORS.primary : COLORS.textSecondary}
                  />
                  <Text style={[
                    styles.categoryLabel,
                    productForm.category === category.id && styles.categoryLabelSelected
                  ]}>
                    {category.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Product Status */}
            {renderStatusSelector()}

            {/* Location Input */}
            {renderLocationInput()}

            {/* Price Information */}
            <Text style={styles.sectionTitle}>Pricing</Text>
            
            <View style={styles.priceRow}>
              <TextInput
                style={[styles.input, { flex: 1 }]}
                placeholder="Price (UGX)"
                placeholderTextColor={COLORS.textSecondary}
                value={productForm.price}
                onChangeText={(text) => setProductForm(prev => ({ ...prev, price: text }))}
                keyboardType="numeric"
              />
              <TouchableOpacity
                style={styles.negotiableButton}
                onPress={() => setProductForm(prev => ({ ...prev, isNegotiable: !prev.isNegotiable }))}
              >
                <Ionicons
                  name={productForm.isNegotiable ? "checkbox" : "square-outline"}
                  size={20}
                  color={COLORS.primary}
                />
                <Text style={styles.negotiableText}>Negotiable</Text>
              </TouchableOpacity>
            </View>

            {/* Additional Details */}
            <Text style={styles.sectionTitle}>Additional Details</Text>
            
            <TextInput
              style={styles.input}
              placeholder="Brand (optional)"
              placeholderTextColor={COLORS.textSecondary}
              value={productForm.brand}
              onChangeText={(text) => setProductForm(prev => ({ ...prev, brand: text }))}
            />

            <TextInput
              style={styles.input}
              placeholder="Model (optional)"
              placeholderTextColor={COLORS.textSecondary}
              value={productForm.model}
              onChangeText={(text) => setProductForm(prev => ({ ...prev, model: text }))}
            />

            <TextInput
              style={styles.input}
              placeholder="Warranty information (optional)"
              placeholderTextColor={COLORS.textSecondary}
              value={productForm.warranty}
              onChangeText={(text) => setProductForm(prev => ({ ...prev, warranty: text }))}
            />

            {/* Contact Information */}
            <Text style={styles.sectionTitle}>Contact Information</Text>
            
            <TextInput
              style={styles.input}
              placeholder="Contact Phone"
              placeholderTextColor={COLORS.textSecondary}
              value={productForm.contactPhone}
              onChangeText={(text) => setProductForm(prev => ({ ...prev, contactPhone: text }))}
              keyboardType="phone-pad"
            />

            <TextInput
              style={styles.input}
              placeholder="Contact Email"
              placeholderTextColor={COLORS.textSecondary}
              value={productForm.contactEmail}
              onChangeText={(text) => setProductForm(prev => ({ ...prev, contactEmail: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TextInput
              style={styles.input}
              placeholder="Tags (comma separated)"
              placeholderTextColor={COLORS.textSecondary}
              value={productForm.tags}
              onChangeText={(text) => setProductForm(prev => ({ ...prev, tags: text }))}
            />
          </ScrollView>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  cancelText: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  submitText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.primary,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
    marginTop: 16,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.text,
    backgroundColor: COLORS.background,
    marginBottom: 12,
  },
  statusContainer: {
    marginBottom: 16,
  },
  statusGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  statusOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginBottom: 8,
  },
  statusText: {
    marginLeft: 6,
    fontSize: 14,
    fontWeight: '500',
  },
  locationContainer: {
    marginBottom: 16,
  },
  locationHint: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 8,
    fontStyle: 'italic',
  },
  locationInput: {
    minHeight: 80,
  },
  locationPreview: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  locationTag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  locationTagText: {
    fontSize: 12,
    fontWeight: '500',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  negotiableButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  negotiableText: {
    fontSize: 14,
    color: COLORS.text,
  },
  categoryOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: COLORS.border,
    backgroundColor: COLORS.background,
    minWidth: 100,
  },
  categorySelected: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.primary + '10',
  },
  categoryLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginLeft: 6,
    fontWeight: '500',
  },
  categoryLabelSelected: {
    color: COLORS.primary,
    fontWeight: '600',
  },
});
