// 📱 REAL UPDATES SERVICE - Complete social media functionality
// Real story posting, media upload, interactions, and feed management

import {
  doc,
  setDoc,
  getDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  updateDoc as updateFirestoreDoc,
  deleteDoc,
  getDocs,
  addDoc,
  arrayUnion,
  arrayRemove,
  increment,
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { db, storage } from './firebaseSimple';
import { Update } from '../types/Update';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';


export type UpdateType = 'photo' | 'video' | 'text';
export type UpdatePrivacy = 'public' | 'friends' | 'private';

export interface RealUpdate {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  caption?: string;
  type: UpdateType;
  mediaUrl?: string;
  mediaWidth?: number;
  mediaHeight?: number;
  videoDuration?: number;
  privacy: UpdatePrivacy;
  timestamp: Date;
  expiresAt?: Date; // For stories that expire
  // Repost data
  isRepost?: boolean;
  originalMediaId?: string;
  originalAuthor?: string;
  originalCaption?: string;
  // Interaction data
  likes: string[]; // Array of user IDs who liked
  comments: string[]; // Array of comment IDs
  shares: string[]; // Array of user IDs who shared
  views: string[]; // Array of user IDs who viewed
  downloads: string[]; // Array of user IDs who downloaded
  // Metadata
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  tags?: string[]; // Hashtags
  mentions?: string[]; // @mentions
  musicTrack?: {
    title: string;
    artist: string;
    url: string;
  };
  // Audio caption for photo/text posts
  audioCaption?: {
    id: string;
    url: string;
    duration: number; // in seconds
    type: 'voice' | 'music'; // voice recording or background music
    volume?: number; // 0-1, default 1
  };
  // Text overlays for media posts
  textOverlays?: {
    id: string;
    text: string;
    position: {
      x: number; // percentage from left (0-100)
      y: number; // percentage from top (0-100)
    };
    size: {
      width: number; // percentage of media width (0-100)
      height: number; // percentage of media height (0-100)
    };
    style: {
      fontSize: number;
      fontFamily?: string;
      color: string;
      backgroundColor?: string;
      borderColor?: string;
      borderWidth?: number;
      borderRadius?: number;
      textAlign: 'left' | 'center' | 'right';
      fontWeight?: 'normal' | 'bold';
      fontStyle?: 'normal' | 'italic';
      opacity?: number; // 0-1, default 1
    };
    rotation?: number; // degrees, default 0
    zIndex?: number; // layer order, default 1
  }[];
}

export interface UpdateComment {
  id: string;
  updateId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  timestamp: Date;
  likes: string[];
  replies?: UpdateComment[];
}

export interface UpdateInteraction {
  updateId: string;
  userId: string;
  type: 'like' | 'comment' | 'share' | 'view' | 'download';
  timestamp: Date;
}

class RealUpdatesService {
  private COLLECTIONS = {
    UPDATES: 'updates',
    COMMENTS: 'comments',
    LIKES: 'likes',
    VIEWS: 'views',
    DOWNLOADS: 'downloads',
  };
  private isInitialized = false;
  private offlineQueue: Map<string, any[]> = new Map();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();

      // Set up network state listener for offline sync
      networkStateManager.addListener('realUpdatesService', (networkState) => {
        if (networkState.isConnected) {
          this.syncOfflineData();
        }
      });

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async syncOfflineData(): Promise<void> {
    try {
      // Sync any queued updates when back online
      for (const [updateId, queuedData] of this.offlineQueue.entries()) {
        for (const data of queuedData) {
          try {
            await this.syncUpdateToFirebase(updateId, data);
          } catch (_error) {
            // Keep in queue for retry
            continue;
          }
        }
        // Clear successfully synced data
        this.offlineQueue.delete(updateId);
      }
    } catch (_error) {
      // Sync failed - will retry on next connection
    }
  }

  private async syncUpdateToFirebase(updateId: string, data: any): Promise<void> {
    if (data.type === 'create') {
      const updateRef = doc(db, 'updates', updateId);
      await setDoc(updateRef, data.payload);
    } else if (data.type === 'update') {
      const updateRef = doc(db, 'updates', updateId);
      await updateFirestoreDoc(updateRef, data.payload);
    }
  }

  private async storeOfflineUpdate(updateId: string, data: any): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO updates_queue (
          updateId, data, timestamp, synced
        ) VALUES (?, ?, ?, ?)
      `, [updateId, JSON.stringify(data), Date.now(), 0]);

      // Also add to memory queue for immediate retry
      if (!this.offlineQueue.has(updateId)) {
        this.offlineQueue.set(updateId, []);
      }
      this.offlineQueue.get(updateId)!.push(data);
    } catch (_error) {
      // Offline storage failed
    }
  }
  /**
   * Create a new update/story
   */
  async createUpdate(
    userId: string,
    userName: string,
    userAvatar: string | undefined,
    content: {
      type: UpdateType;
      caption?: string;
      mediaUri?: string;
      privacy?: UpdatePrivacy;
      isStory?: boolean;
      musicTrack?: any;
      location?: any;
      tags?: string[];
      mentions?: string[];
      audioCaption?: {
        id: string;
        url: string;
        duration: number;
        type: 'voice' | 'music';
        volume?: number;
      };
      textOverlays?: {
        id: string;
        text: string;
        position: { x: number; y: number };
        size: { width: number; height: number };
        style: {
          fontSize: number;
          color: string;
          backgroundColor?: string;
          textAlign: 'left' | 'center' | 'right';
          fontWeight?: 'normal' | 'bold';
          opacity?: number;
        };
        rotation?: number;
        zIndex?: number;
      }[];
    }
  ): Promise<{ success: boolean; updateId?: string; error?: string }> {
    const updateId = `update_${Date.now()}_${userId}`;
    let mediaUrl: string | undefined;

    console.log('🚀 Creating update:', {
      updateId,
      type: content.type,
      hasMediaUri: !!content.mediaUri,
      caption: content.caption
    });

    try {
      // Upload media if provided
      if (content.mediaUri && content.type !== 'text') {
        console.log('📤 Uploading media for type:', content.type);
        const uploadResult = await this.uploadMedia(content.mediaUri, updateId, content.type);
        if (!uploadResult.success) {
          console.error('❌ Media upload failed:', uploadResult.error);
          return { success: false, error: uploadResult.error };
        }
        mediaUrl = uploadResult.url;
        console.log('✅ Media uploaded successfully:', mediaUrl);
      }

      // Create update object
      const update: Omit<RealUpdate, 'id'> = {
        userId,
        userName,
        userAvatar,
        content: content.type === 'text' ? content.caption || '' : '', // For text updates, content is the main text
        caption: content.type === 'text' ? undefined : content.caption, // For media updates, caption is separate
        type: content.type,
        mediaUrl,
        privacy: content.privacy || 'public',
        timestamp: new Date(),
        expiresAt: content.isStory ? new Date(Date.now() + 24 * 60 * 60 * 1000) : undefined, // 24 hours for stories
        likes: [],
        comments: [],
        shares: [],
        views: [],
        downloads: [],
        location: content.location,
        tags: content.tags || [],
        mentions: content.mentions || [],
        musicTrack: content.musicTrack,
        audioCaption: content.audioCaption,
        textOverlays: content.textOverlays,
      };

      const updateData = {
        ...update,
        timestamp: serverTimestamp(),
        expiresAt: update.expiresAt ? serverTimestamp() : null,
      };

      if (networkStateManager.isOnline()) {
        // Save to Firebase
        console.log('💾 Saving update to Firebase:', { updateId, type: update.type });
        const updateRef = doc(db, 'updates', updateId);
        await setDoc(updateRef, updateData);
        console.log('✅ Update saved to Firebase successfully');
      } else {
        // Store offline for later sync
        console.log('📱 Storing update offline for later sync');
        await this.storeOfflineUpdate(updateId, {
          type: 'create',
          payload: updateData,
        });
      }

      return { success: true, updateId };
    } catch (error) {
      console.error('❌ Error creating update:', error);
      // If online save fails, store offline
      try {
        console.log('🔄 Attempting offline fallback...');
        const fallbackUpdateData = {
          userId,
          userName,
          userAvatar,
          content: content.type === 'text' ? content.caption || '' : '',
          caption: content.type === 'text' ? undefined : content.caption,
          type: content.type,
          mediaUrl,
          privacy: content.privacy || 'public',
          timestamp: new Date(),
          expiresAt: content.isStory ? new Date(Date.now() + 24 * 60 * 60 * 1000) : undefined,
          likes: [],
          comments: [],
          shares: [],
          views: [],
          downloads: [],
          location: content.location,
          tags: content.tags || [],
          mentions: content.mentions || [],
          musicTrack: content.musicTrack,
        };

        await this.storeOfflineUpdate(updateId, {
          type: 'create',
          payload: fallbackUpdateData,
        });

        console.log('✅ Update stored offline as fallback');
        return { success: true, updateId };
      } catch (_offlineError) {
        console.error('❌ Both online and offline save failed');
        return { success: false, error: 'Failed to create update' };
      }
    }
  }

  /**
   * Upload media to Firebase Storage
   */
  private async uploadMedia(
    mediaUri: string,
    updateId: string,
    type: UpdateType
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      console.log('🎥 Uploading media:', { mediaUri, updateId, type });

      // Convert URI to blob
      const response = await fetch(mediaUri);
      const blob = await response.blob();

      console.log('📁 Blob type:', blob.type);

      // Determine file extension based on blob type or fallback to type
      let fileExtension = 'mp4'; // default for video
      if (type === 'video') {
        // Check blob type for video format
        if (blob.type.includes('mp4')) fileExtension = 'mp4';
        else if (blob.type.includes('mov')) fileExtension = 'mov';
        else if (blob.type.includes('avi')) fileExtension = 'avi';
        else fileExtension = 'mp4'; // fallback
      } else if (type === 'photo') {
        // Check blob type for image format
        if (blob.type.includes('jpeg') || blob.type.includes('jpg')) fileExtension = 'jpg';
        else if (blob.type.includes('png')) fileExtension = 'png';
        else if (blob.type.includes('webp')) fileExtension = 'webp';
        else fileExtension = 'jpg'; // fallback
      }

      const fileName = `${updateId}.${fileExtension}`;
      const storageRef = ref(storage, `updates/${type}s/${fileName}`);

      console.log('☁️ Uploading to:', `updates/${type}s/${fileName}`);

      // Upload file
      await uploadBytes(storageRef, blob);

      // Get download URL
      const downloadURL = await getDownloadURL(storageRef);

      console.log('✅ Upload successful:', downloadURL);

      return { success: true, url: downloadURL };
    } catch (error) {
      console.error('❌ Upload failed:', error);
      return { success: false, error: 'Failed to upload media' };
    }
  }

  /**
   * Get updates for a specific user
   */
  async getUserUpdates(
    userId: string,
    limitCount: number = 50
  ): Promise<RealUpdate[]> {
    try {
      // Try offline first
      if (!networkStateManager.isOnline()) {
        return await this.getOfflineUserUpdates(userId, limitCount);
      }

      const updatesRef = collection(db, 'updates');
      const q = query(
        updatesRef,
        where('userId', '==', userId),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      const updates: RealUpdate[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        updates.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate(),
        } as RealUpdate);
      });

      // Cache updates offline
      await this.cacheUserUpdatesOffline(userId, updates);

      return updates;
    } catch (error) {
      console.error('Error getting user updates:', error);
      // Fallback to offline
      return await this.getOfflineUserUpdates(userId, limitCount);
    }
  }

  /**
   * Get updates feed
   */
  async getUpdatesFeed(
    userId: string,
    limitCount: number = 20
  ): Promise<RealUpdate[]> {
    try {
      // Try offline first
      if (!networkStateManager.isOnline()) {
        return await this.getOfflineUpdatesFeed(userId, limitCount);
      }

      const updatesRef = collection(db, 'updates');
      const q = query(
        updatesRef,
        where('privacy', '==', 'public'),
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      const updates: RealUpdate[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        updates.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate(),
        } as RealUpdate);
      });

      // Cache updates offline for future use
      await this.cacheUpdatesOffline(updates);

      return updates;
    } catch (_error) {
      // Fallback to offline data
      return await this.getOfflineUpdatesFeed(userId, limitCount);
    }
  }

  private async getOfflineUpdatesFeed(_userId: string, limitCount: number): Promise<RealUpdate[]> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const result = await database.getAllAsync(`
        SELECT * FROM cached_updates
        WHERE privacy = 'public'
        ORDER BY timestamp DESC
        LIMIT ?
      `, [limitCount]);

      return result.map((row: any) => ({
        id: row.id,
        userId: row.userId,
        userName: row.userName,
        userAvatar: row.userAvatar,
        content: row.content,
        caption: row.caption,
        type: row.type,
        mediaUrl: row.mediaUrl,
        privacy: row.privacy,
        timestamp: new Date(row.timestamp),
        expiresAt: row.expiresAt ? new Date(row.expiresAt) : undefined,
        likes: JSON.parse(row.likes || '[]'),
        comments: JSON.parse(row.comments || '[]'),
        shares: JSON.parse(row.shares || '[]'),
        views: JSON.parse(row.views || '[]'),
        downloads: JSON.parse(row.downloads || '[]'),
        location: row.location ? JSON.parse(row.location) : undefined,
        tags: JSON.parse(row.tags || '[]'),
        mentions: JSON.parse(row.mentions || '[]'),
        musicTrack: row.musicTrack ? JSON.parse(row.musicTrack) : undefined,
      }));
    } catch (_error) {
      return [];
    }
  }

  private async cacheUpdatesOffline(updates: RealUpdate[]): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();

      for (const update of updates) {
        await database.runAsync(`
          INSERT OR REPLACE INTO cached_updates (
            id, userId, userName, userAvatar, content, caption, type, mediaUrl,
            privacy, timestamp, expiresAt, likes, comments, shares, views, downloads,
            location, tags, mentions, musicTrack
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          update.id, update.userId, update.userName, update.userAvatar || '',
          update.content || '', update.caption || '', update.type, update.mediaUrl || '',
          update.privacy, update.timestamp.getTime(),
          update.expiresAt?.getTime() || null,
          JSON.stringify(update.likes), JSON.stringify(update.comments),
          JSON.stringify(update.shares), JSON.stringify(update.views),
          JSON.stringify(update.downloads),
          update.location ? JSON.stringify(update.location) : null,
          JSON.stringify(update.tags), JSON.stringify(update.mentions),
          update.musicTrack ? JSON.stringify(update.musicTrack) : null
        ]);
      }
    } catch (_error) {
      // Cache failed - continue without caching
    }
  }

  /**
   * Subscribe to updates feed
   */
  subscribeToUpdatesFeed(
    userId: string,
    callback: (_updates: RealUpdate[]) => void,
    limitCount: number = 20
  ): () => void {
    if (!networkStateManager.isOnline()) {
      // Return offline data immediately and set up periodic refresh
      this.getOfflineUpdatesFeed(userId, limitCount).then(callback);

      // Return empty unsubscribe function for offline mode
      return () => {};
    }

    const updatesRef = collection(db, 'updates');
    const q = query(
      updatesRef,
      where('privacy', '==', 'public'),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const updates: RealUpdate[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        const update = {
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate(),
        } as RealUpdate;

        // Filter out expired stories
        if (update.expiresAt && update.expiresAt < new Date()) {
          return;
        }

        updates.push(update);
      });

      // Cache updates offline
      this.cacheUpdatesOffline(updates);

      callback(updates);
    });

    return unsubscribe;
  }

  /**
   * Like/unlike an update
   */
  async toggleLike(
    updateId: string,
    userId: string
  ): Promise<{ success: boolean; isLiked: boolean; error?: string }> {
    try {
      let updateData: RealUpdate | null = null;
      let isCurrentlyLiked = false;

      if (networkStateManager.isOnline()) {
        const updateRef = doc(db, 'updates', updateId);
        const updateSnapshot = await getDoc(updateRef);

        if (!updateSnapshot.exists()) {
          return { success: false, isLiked: false, error: 'Update not found' };
        }

        updateData = updateSnapshot.data() as RealUpdate;
        isCurrentlyLiked = updateData.likes.includes(userId);

        if (isCurrentlyLiked) {
          // Unlike
          await updateFirestoreDoc(updateRef, {
            likes: arrayRemove(userId),
          });
        } else {
          // Like
          await updateFirestoreDoc(updateRef, {
            likes: arrayUnion(userId),
          });
        }

        // Log interaction
        await this.logInteraction(updateId, userId, 'like');
      } else {
        // Handle offline like/unlike
        const offlineData = await this.getOfflineUpdateData(updateId);
        if (!offlineData) {
          return { success: false, isLiked: false, error: 'Update not found' };
        }

        isCurrentlyLiked = offlineData.likes.includes(userId);

        // Store offline action for later sync
        await this.storeOfflineUpdate(updateId, {
          type: 'update',
          payload: isCurrentlyLiked
            ? { likes: arrayRemove(userId) }
            : { likes: arrayUnion(userId) }
        });

        // Update local cache
        await this.updateOfflineUpdateData(updateId, userId, isCurrentlyLiked ? 'unlike' : 'like');
      }

      return { success: true, isLiked: !isCurrentlyLiked };
    } catch (_error) {
      return { success: false, isLiked: false, error: 'Failed to toggle like' };
    }
  }

  private async getOfflineUpdateData(updateId: string): Promise<RealUpdate | null> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const result = await database.getFirstAsync(`
        SELECT * FROM cached_updates WHERE id = ?
      `, [updateId]) as any;

      if (!result) return null;

      return {
        id: result.id,
        userId: result.userId,
        userName: result.userName,
        userAvatar: result.userAvatar,
        content: result.content,
        caption: result.caption,
        type: result.type,
        mediaUrl: result.mediaUrl,
        privacy: result.privacy,
        timestamp: new Date(result.timestamp),
        expiresAt: result.expiresAt ? new Date(result.expiresAt) : undefined,
        likes: JSON.parse(result.likes || '[]'),
        comments: JSON.parse(result.comments || '[]'),
        shares: JSON.parse(result.shares || '[]'),
        views: JSON.parse(result.views || '[]'),
        downloads: JSON.parse(result.downloads || '[]'),
        location: result.location ? JSON.parse(result.location) : undefined,
        tags: JSON.parse(result.tags || '[]'),
        mentions: JSON.parse(result.mentions || '[]'),
        musicTrack: result.musicTrack ? JSON.parse(result.musicTrack) : undefined,
      } as RealUpdate;
    } catch (_error) {
      return null;
    }
  }

  private async updateOfflineUpdateData(updateId: string, userId: string, action: 'like' | 'unlike'): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const updateData = await this.getOfflineUpdateData(updateId);

      if (updateData) {
        if (action === 'like' && !updateData.likes.includes(userId)) {
          updateData.likes.push(userId);
        } else if (action === 'unlike') {
          updateData.likes = updateData.likes.filter(id => id !== userId);
        }

        await database.runAsync(`
          UPDATE cached_updates SET likes = ? WHERE id = ?
        `, [JSON.stringify(updateData.likes), updateId]);
      }
    } catch (_error) {
      // Update failed - continue without local update
    }
  }



  /**
   * Share an update
   */
  async shareUpdate(
    updateId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const updateRef = doc(db, 'updates', updateId);
      await updateFirestoreDoc(updateRef, {
        shares: arrayUnion(userId),
      });

      // Log interaction
      await this.logInteraction(updateId, userId, 'share');

      console.log('✅ Update shared:', updateId);
      return { success: true };
    } catch (error) {
      console.error('❌ Error sharing update:', error);
      return { success: false, error: 'Failed to share update' };
    }
  }

  /**
   * View an update (for analytics)
   */
  async viewUpdate(
    updateId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const updateRef = doc(db, 'updates', updateId);
      const updateSnapshot = await getDoc(updateRef);

      if (updateSnapshot.exists()) {
        const updateData = updateSnapshot.data() as RealUpdate;
        
        // Only add view if not already viewed
        if (!updateData.views.includes(userId)) {
          await updateFirestoreDoc(updateRef, {
            views: arrayUnion(userId),
          });

          // Log interaction
          await this.logInteraction(updateId, userId, 'view');
        }
      }

      return { success: true };
    } catch (error) {
      console.error('❌ Error viewing update:', error);
      return { success: false, error: 'Failed to view update' };
    }
  }

  /**
   * Get user stories
   */
  async getUserStories(userId: string): Promise<Update[]> {
    try {
      console.log('🔍 Getting user stories for:', userId);

      const storiesQuery = query(
        collection(db, this.COLLECTIONS.UPDATES),
        where('userId', '==', userId),
        where('isStory', '==', true),
        where('createdAt', '>', new Date(Date.now() - 24 * 60 * 60 * 1000)), // Last 24 hours
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(storiesQuery);
      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          user: {
            id: data.userId || '',
            username: data.userName || '',
            displayName: data.userName || '',
            name: data.userName || '',
            phoneNumber: '',
            avatar: data.userAvatar || '',
            followersCount: 0,
            followingCount: 0,
            likesCount: 0,
          },
          mediaUrl: data.mediaUrl || '',
          mediaType: data.type === 'video' ? 'video' : 'image',
          caption: data.caption || data.content || '',
          createdAt: data.timestamp?.toDate()?.getTime() || Date.now(),
          likeCount: data.likes?.length || 0,
          commentCount: data.comments?.length || 0,
          shareCount: data.shares?.length || 0,
          viewCount: data.views?.length || 0,
          isLiked: false,
          isFollowing: false,
          timestamp: data.timestamp?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as unknown as Update;
      });
    } catch (error) {
      console.error('❌ Error getting user stories:', error);
      return [];
    }
  }

  /**
   * Get friends stories
   */
  async getFriendsStories(userId: string): Promise<Update[]> {
    try {
      console.log('🔍 Getting friends stories for:', userId);

      // For now, get all recent stories - in a real app you'd filter by friends
      const storiesQuery = query(
        collection(db, this.COLLECTIONS.UPDATES),
        where('isStory', '==', true),
        where('createdAt', '>', new Date(Date.now() - 24 * 60 * 60 * 1000)), // Last 24 hours
        orderBy('createdAt', 'desc'),
        limit(50)
      );

      const snapshot = await getDocs(storiesQuery);
      return snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          user: {
            id: data.userId || '',
            username: data.userName || '',
            displayName: data.userName || '',
            name: data.userName || '',
            phoneNumber: '',
            avatar: data.userAvatar || '',
            followersCount: 0,
            followingCount: 0,
            likesCount: 0,
          },
          mediaUrl: data.mediaUrl || '',
          mediaType: data.type === 'video' ? 'video' : 'image',
          caption: data.caption || data.content || '',
          createdAt: data.timestamp?.toDate()?.getTime() || Date.now(),
          likeCount: data.likes?.length || 0,
          commentCount: data.comments?.length || 0,
          shareCount: data.shares?.length || 0,
          viewCount: data.views?.length || 0,
          isLiked: false,
          isFollowing: false,
          timestamp: data.timestamp?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as unknown as Update;
      });
    } catch (error) {
      console.error('❌ Error getting friends stories:', error);
      return [];
    }
  }

  /**
   * Delete an update
   */
  async deleteUpdate(
    updateId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Check if user owns the update
      const updateRef = doc(db, 'updates', updateId);
      const updateSnapshot = await getDoc(updateRef);

      if (!updateSnapshot.exists()) {
        return { success: false, error: 'Update not found' };
      }

      const updateData = updateSnapshot.data() as RealUpdate;
      if (updateData.userId !== userId) {
        return { success: false, error: 'Not authorized to delete this update' };
      }

      // Delete media from storage if exists
      if (updateData.mediaUrl) {
        try {
          const mediaRef = ref(storage, updateData.mediaUrl);
          await deleteObject(mediaRef);
        } catch (error) {
          console.warn('⚠️ Could not delete media file:', error);
        }
      }

      // Delete update document
      await updateFirestoreDoc(updateRef, {
        deleted: true,
        deletedAt: serverTimestamp(),
      });

      console.log('✅ Update deleted:', updateId);
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting update:', error);
      return { success: false, error: 'Failed to delete update' };
    }
  }

  /**
   * Get update likes with user details
   */
  async getUpdateLikes(updateId: string): Promise<{ success: boolean; interactions?: any[]; error?: string }> {
    try {
      const likesRef = collection(db, 'updates', updateId, 'likes');
      const likesSnapshot = await getDocs(likesRef);

      const interactions = await Promise.all(
        likesSnapshot.docs.map(async (likeDoc) => {
          const likeData = likeDoc.data();
          const userRef = doc(db, 'users', likeData.userId);
          const userDoc = await getDoc(userRef);
          const userData = userDoc.exists() ? userDoc.data() : {};

          return {
            userId: likeData.userId,
            userName: userData.name || 'Unknown User',
            userAvatar: userData.avatar,
            timestamp: likeData.timestamp?.toDate() || new Date(),
            interactionType: 'like',
          };
        })
      );

      return { success: true, interactions };
    } catch (error) {
      console.error('❌ Error getting update likes:', error);
      return { success: false, error: 'Failed to get likes' };
    }
  }

  /**
   * Get update views with user details
   */
  async getUpdateViews(updateId: string): Promise<{ success: boolean; interactions?: any[]; error?: string }> {
    try {
      const viewsRef = collection(db, 'updates', updateId, 'views');
      const viewsSnapshot = await getDocs(viewsRef);

      const interactions = await Promise.all(
        viewsSnapshot.docs.map(async (viewDoc) => {
          const viewData = viewDoc.data();
          const userRef = doc(db, 'users', viewData.userId);
          const userDoc = await getDoc(userRef);
          const userData = userDoc.exists() ? userDoc.data() : {};

          return {
            userId: viewData.userId,
            userName: userData.name || 'Unknown User',
            userAvatar: userData.avatar,
            timestamp: viewData.timestamp?.toDate() || new Date(),
            interactionType: 'view',
          };
        })
      );

      return { success: true, interactions };
    } catch (error) {
      console.error('❌ Error getting update views:', error);
      return { success: false, error: 'Failed to get views' };
    }
  }

  /**
   * Get update shares with user details
   */
  async getUpdateShares(updateId: string): Promise<{ success: boolean; interactions?: any[]; error?: string }> {
    try {
      const sharesRef = collection(db, 'updates', updateId, 'shares');
      const sharesSnapshot = await getDocs(sharesRef);

      const interactions = await Promise.all(
        sharesSnapshot.docs.map(async (shareDoc) => {
          const shareData = shareDoc.data();
          const userRef = doc(db, 'users', shareData.userId);
          const userDoc = await getDoc(userRef);
          const userData = userDoc.exists() ? userDoc.data() : {};

          return {
            userId: shareData.userId,
            userName: userData.name || 'Unknown User',
            userAvatar: userData.avatar,
            timestamp: shareData.timestamp?.toDate() || new Date(),
            interactionType: 'share',
          };
        })
      );

      return { success: true, interactions };
    } catch (error) {
      console.error('❌ Error getting update shares:', error);
      return { success: false, error: 'Failed to get shares' };
    }
  }

  /**
   * Get update downloads with user details
   */
  async getUpdateDownloads(updateId: string): Promise<{ success: boolean; interactions?: any[]; error?: string }> {
    try {
      const downloadsRef = collection(db, 'updates', updateId, 'downloads');
      const downloadsSnapshot = await getDocs(downloadsRef);

      const interactions = await Promise.all(
        downloadsSnapshot.docs.map(async (downloadDoc) => {
          const downloadData = downloadDoc.data();
          const userRef = doc(db, 'users', downloadData.userId);
          const userDoc = await getDoc(userRef);
          const userData = userDoc.exists() ? userDoc.data() : {};

          return {
            userId: downloadData.userId,
            userName: userData.name || 'Unknown User',
            userAvatar: userData.avatar,
            timestamp: downloadData.timestamp?.toDate() || new Date(),
            interactionType: 'download',
          };
        })
      );

      return { success: true, interactions };
    } catch (error) {
      console.error('❌ Error getting update downloads:', error);
      return { success: false, error: 'Failed to get downloads' };
    }
  }

  /**
   * Record download interaction with comprehensive Firebase tracking
   */
  async recordDownload(updateId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Record individual download interaction
      const downloadRef = doc(db, 'updates', updateId, 'downloads', userId);
      await setDoc(downloadRef, {
        userId,
        timestamp: serverTimestamp(),
        deviceInfo: {
          platform: 'mobile',
          userAgent: 'IraChat-Mobile',
        },
      });

      // Update main update document with download count
      const updateRef = doc(db, 'updates', updateId);
      await updateFirestoreDoc(updateRef, {
        downloads: arrayUnion(userId),
        downloadCount: increment(1),
        lastDownloadAt: serverTimestamp(),
      });

      // Log in general interactions for analytics
      await this.logInteraction(updateId, userId, 'download');

      // Record in user's download history for the downloaded-media screen
      const userDownloadRef = doc(db, 'users', userId, 'downloads', updateId);
      await setDoc(userDownloadRef, {
        updateId,
        downloadedAt: serverTimestamp(),
        mediaType: 'update',
      });

      console.log('✅ Download recorded with comprehensive Firebase tracking');
      return { success: true };
    } catch (error) {
      console.error('❌ Error recording download:', error);
      return { success: false, error: 'Failed to record download' };
    }
  }

  /**
   * Get update comments with replies
   */
  async getUpdateComments(updateId: string): Promise<{ success: boolean; comments?: any[]; error?: string }> {
    try {
      const commentsRef = collection(db, 'updates', updateId, 'comments');
      const commentsQuery = query(commentsRef, orderBy('timestamp', 'asc'));
      const commentsSnapshot = await getDocs(commentsQuery);

      const comments = await Promise.all(
        commentsSnapshot.docs.map(async (commentDoc) => {
          const commentData = commentDoc.data();

          // Get replies for this comment
          const repliesRef = collection(db, 'updates', updateId, 'comments', commentDoc.id, 'replies');
          const repliesQuery = query(repliesRef, orderBy('timestamp', 'asc'));
          const repliesSnapshot = await getDocs(repliesQuery);

          const replies = repliesSnapshot.docs.map(replyDoc => ({
            id: replyDoc.id,
            ...replyDoc.data(),
            timestamp: replyDoc.data().timestamp?.toDate() || new Date(),
          }));

          return {
            id: commentDoc.id,
            ...commentData,
            timestamp: commentData.timestamp?.toDate() || new Date(),
            replies,
          };
        })
      );

      return { success: true, comments };
    } catch (error) {
      console.error('❌ Error getting update comments:', error);
      return { success: false, error: 'Failed to get comments' };
    }
  }

  /**
   * Add comment to update
   */
  async addComment(
    updateId: string,
    userId: string,
    userName: string,
    userAvatar: string | undefined,
    content: string,
    parentCommentId?: string,
    isFromOfflineService = false // Add flag to prevent infinite loops
  ): Promise<{ success: boolean; commentId?: string; error?: string }> {
    try {
      // Check if this is an offline update (starts with 'offline_') and not already from offline service
      if (updateId.startsWith('offline_') && !isFromOfflineService) {
        // Import offline comments service dynamically to avoid circular imports
        const { offlineCommentsService } = await import('./offlineCommentsService');

        console.log('📱 Adding comment to offline update via offline service:', updateId);
        const result = await offlineCommentsService.addComment(
          updateId,
          userId,
          userName,
          userAvatar,
          content,
          parentCommentId
        );

        return {
          success: result.success,
          commentId: result.commentId,
          error: result.error
        };
      }

      if (parentCommentId) {
        // This is a reply
        const replyRef = collection(db, 'updates', updateId, 'comments', parentCommentId, 'replies');
        const replyDoc = await addDoc(replyRef, {
          commentId: parentCommentId,
          userId,
          userName,
          userAvatar,
          content,
          timestamp: serverTimestamp(),
          likes: [],
        });

        console.log('✅ Reply added');
        return { success: true, commentId: replyDoc.id };
      } else {
        // This is a main comment
        const commentRef = collection(db, 'updates', updateId, 'comments');
        const commentDoc = await addDoc(commentRef, {
          updateId,
          userId,
          userName,
          userAvatar,
          content,
          timestamp: serverTimestamp(),
          likes: [],
        });

        // Update comment count on the update (only if it exists in Firebase)
        try {
          const updateRef = doc(db, 'updates', updateId);
          await updateFirestoreDoc(updateRef, {
            comments: arrayUnion(commentDoc.id),
          });
        } catch (updateError) {
          console.warn('⚠️ Could not update comment count on update:', updateId, updateError);
          // Comment was added successfully, but couldn't update the count
          // This is not a critical error
        }

        console.log('✅ Comment added');
        return { success: true, commentId: commentDoc.id };
      }
    } catch (error) {
      console.error('❌ Error adding comment:', error);
      return { success: false, error: 'Failed to add comment' };
    }
  }

  /**
   * Like/unlike comment
   */
  async likeComment(commentId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // For simplicity, we'll assume this is a main comment
      // In a real implementation, you'd need to determine if it's a comment or reply
      const commentRef = doc(db, 'comments', commentId);
      const commentDoc = await getDoc(commentRef);

      if (!commentDoc.exists()) {
        return { success: false, error: 'Comment not found' };
      }

      const commentData = commentDoc.data();
      const isLiked = commentData.likes?.includes(userId);

      if (isLiked) {
        await updateFirestoreDoc(commentRef, {
          likes: arrayRemove(userId),
        });
      } else {
        await updateFirestoreDoc(commentRef, {
          likes: arrayUnion(userId),
        });
      }

      console.log('✅ Comment like toggled');
      return { success: true };
    } catch (error) {
      console.error('❌ Error liking comment:', error);
      return { success: false, error: 'Failed to like comment' };
    }
  }

  /**
   * Delete a comment
   */
  async deleteComment(commentId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const commentRef = doc(db, 'comments', commentId);
      await deleteDoc(commentRef);

      console.log('✅ Comment deleted from Firebase:', commentId);
      return { success: true };
    } catch (error) {
      console.error('❌ Error deleting comment:', error);
      return { success: false, error: 'Failed to delete comment' };
    }
  }

  /**
   * Edit a comment
   */
  async editComment(commentId: string, newContent: string): Promise<{ success: boolean; error?: string }> {
    try {
      const commentRef = doc(db, 'comments', commentId);
      await updateFirestoreDoc(commentRef, {
        content: newContent,
        updatedAt: Date.now(),
        isEdited: true
      });

      console.log('✅ Comment edited in Firebase:', commentId);
      return { success: true };
    } catch (error) {
      console.error('❌ Error editing comment:', error);
      return { success: false, error: 'Failed to edit comment' };
    }
  }

  /**
   * Log user interaction for analytics
   */
  private async logInteraction(
    updateId: string,
    userId: string,
    type: 'like' | 'comment' | 'share' | 'view' | 'download'
  ): Promise<void> {
    try {
      const interactionId = `${updateId}_${userId}_${type}_${Date.now()}`;
      const interactionRef = doc(db, 'interactions', interactionId);

      await setDoc(interactionRef, {
        updateId,
        userId,
        type,
        timestamp: serverTimestamp(),
      });
    } catch (error) {
      console.error('❌ Error logging interaction:', error);
    }
  }

  /**
   * Cache user updates offline
   */
  private async cacheUserUpdatesOffline(userId: string, updates: RealUpdate[]): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();

      // Clear existing cached updates for this user
      await database.runAsync('DELETE FROM cached_user_updates WHERE userId = ?', [userId]);

      // Insert new updates
      for (const update of updates) {
        await database.runAsync(`
          INSERT INTO cached_user_updates (
            id, userId, userName, userAvatar, content, caption, type, mediaUrl,
            privacy, timestamp, expiresAt, likes, comments, shares, views, downloads,
            location, tags, mentions, musicTrack, cachedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          update.id, update.userId, update.userName, update.userAvatar || '',
          update.content, update.caption || '', update.type, update.mediaUrl || '',
          update.privacy, update.timestamp.getTime(), update.expiresAt?.getTime() || null,
          JSON.stringify(update.likes), JSON.stringify(update.comments),
          JSON.stringify(update.shares), JSON.stringify(update.views),
          JSON.stringify(update.downloads), JSON.stringify(update.location || null),
          JSON.stringify(update.tags), JSON.stringify(update.mentions),
          JSON.stringify(update.musicTrack || null), Date.now()
        ]);
      }
    } catch (error) {
      console.error('Error caching user updates offline:', error);
    }
  }

  /**
   * Get offline user updates
   */
  private async getOfflineUserUpdates(userId: string, limitCount: number): Promise<RealUpdate[]> {
    try {
      const database = offlineDatabaseService.getDatabase();
      const result = await database.getAllAsync(`
        SELECT * FROM cached_user_updates
        WHERE userId = ?
        ORDER BY timestamp DESC
        LIMIT ?
      `, [userId, limitCount]);

      return result.map((row: any) => ({
        id: row.id,
        userId: row.userId,
        userName: row.userName,
        userAvatar: row.userAvatar,
        content: row.content,
        caption: row.caption,
        type: row.type,
        mediaUrl: row.mediaUrl,
        privacy: row.privacy,
        timestamp: new Date(row.timestamp),
        expiresAt: row.expiresAt ? new Date(row.expiresAt) : undefined,
        likes: JSON.parse(row.likes || '[]'),
        comments: JSON.parse(row.comments || '[]'),
        shares: JSON.parse(row.shares || '[]'),
        views: JSON.parse(row.views || '[]'),
        downloads: JSON.parse(row.downloads || '[]'),
        location: row.location ? JSON.parse(row.location) : undefined,
        tags: JSON.parse(row.tags || '[]'),
        mentions: JSON.parse(row.mentions || '[]'),
        musicTrack: row.musicTrack ? JSON.parse(row.musicTrack) : undefined,
      })) as RealUpdate[];
    } catch (error) {
      console.error('Error getting offline user updates:', error);
      return [];
    }
  }
}

// Export singleton instance
export const realUpdatesService = new RealUpdatesService();
export default realUpdatesService;
