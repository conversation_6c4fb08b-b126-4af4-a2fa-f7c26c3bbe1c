// 🚀 COMPREHENSIVE UPDATES SERVICE
// Complete social media updates system with advanced features
// Includes: Updates, Stories, Live Streaming, Analytics, Moderation, AI Features

import {
  doc,
  setDoc,
  getDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  serverTimestamp,
  updateDoc,
  getDocs,
  writeBatch,
  startAfter,
  Timestamp,
  runTransaction,
  increment,
} from 'firebase/firestore';
import {
  ref,
  getDownloadURL,
  uploadBytesResumable,
  uploadBytes,
  getMetadata,
} from 'firebase/storage';
import * as VideoThumbnails from 'expo-video-thumbnails';
import { db, storage, auth } from './firebaseSimple';
import { localUpdatesStorage } from './localUpdatesStorage';
import {
  Update,
  Story,
  CreateUpdateData,
  UpdateAnalytics,
  UpdateComment,
  UserInteraction,
  UpdateNotification,
  MediaInfo,
  FeedConfig,
  UpdateType,
  UpdatePrivacy
} from '../types/Update';

// Service Response Types
interface ServiceResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

interface UploadProgress {
  bytesTransferred: number;
  totalBytes: number;
  percentage: number;
}

class ComprehensiveUpdatesService {
  private readonly COLLECTIONS = {
    UPDATES: 'updates',
    STORIES: 'stories',
    COMMENTS: 'comments',
    ANALYTICS: 'analytics',
    HIGHLIGHTS: 'story_highlights',
    NOTIFICATIONS: 'notifications',
    TRENDING: 'trending',
    LIVE: 'live_updates',
    REPORTS: 'reports',
    USERS: 'users',
  };

  private readonly STORAGE_PATHS = {
    UPDATES: 'updates',
    STORIES: 'stories',
    THUMBNAILS: 'thumbnails',
    LIVE: 'live',
  };

  /**
   * Check if Firebase is available
   */
  private isFirebaseAvailable(): boolean {
    return !!db && !!storage;
  }

  // ==================== CORE UPDATE OPERATIONS ====================

  /**
   * Create a new update or story
   */
  async createUpdate(
    userId: string,
    userName: string,
    userAvatar: string | undefined,
    data: CreateUpdateData,
    onProgress?: (_progress: UploadProgress) => void
  ): Promise<ServiceResponse<{ updateId: string; mediaUrl: string }>> {
    try {
      console.log('🎬 Creating update:', data.type, data.isStory ? '(Story)' : '(Update)');

      // Generate unique ID
      const updateId = `${data.isStory ? 'story' : 'update'}_${Date.now()}_${userId}`;

      // Upload media if provided
      let mediaInfo: MediaInfo | null = null;
      if (data.mediaUri) {
        const uploadResult = await this.uploadMedia(
          data.mediaUri,
          data.type,
          updateId,
          data.isStory,
          onProgress
        );
        
        if (!uploadResult.success) {
          return { success: false, error: uploadResult.error };
        }
        
        mediaInfo = uploadResult.data!;
      }

      // Process hashtags and mentions
      const hashtags = this.extractHashtags(data.caption || '');
      const mentions = this.extractMentions(data.caption || '');

      // Create update object
      const update: Omit<Update, 'id'> = {
        userId,
        userName,
        userAvatar,
        type: data.type,
        caption: data.caption,
        media: mediaInfo ? [mediaInfo] : [],
        timestamp: new Date(),
        isStory: data.isStory,
        expiresAt: data.isStory ? new Date(Date.now() + 24 * 60 * 60 * 1000) : data.expiresAt,
        privacy: data.privacy,
        isVisible: true,
        isArchived: false,
        location: data.location,
        musicTrack: data.musicTrack,
        audioCaption: data.audioCaption,
        textOverlays: data.textOverlays,
        hashtags: [...hashtags, ...(data.hashtags || [])],
        mentions: [...mentions, ...(data.mentions || [])],
        groupTags: data.groupTags || [],
        likes: [],
        views: [],
        shares: [],
        downloads: [],
        reactions: [],
        comments: [],
        viewCount: 0,
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        downloadCount: 0,
        isLikedByCurrentUser: false,
        isViewedByCurrentUser: false,
        isSharedByCurrentUser: false,
        isDownloadedByCurrentUser: false,
        isReported: false,
        reportCount: 0,
        isFlagged: false,
        isPinned: false,
        isHighlight: false,
      };

      // 🏠 SAVE TO LOCAL STORAGE FIRST (Offline-first approach)
      console.log('💾 Saving update to local storage first...');
      const localId = await localUpdatesStorage.saveUpdate(update);
      console.log('✅ Update saved locally:', localId);

      // 🔄 SYNC TO FIREBASE IN BACKGROUND
      try {
        const collection_name = data.isStory ? this.COLLECTIONS.STORIES : this.COLLECTIONS.UPDATES;
        const updateRef = doc(db, collection_name, updateId);

        await setDoc(updateRef, {
          ...update,
          timestamp: serverTimestamp(),
          expiresAt: update.expiresAt ? Timestamp.fromDate(update.expiresAt) : null,
        });

        // Update local storage with Firebase ID and sync status
        await localUpdatesStorage.updateSyncStatus(localId, 'synced', updateId);

        // Create analytics entry
        await this.initializeAnalytics(updateId, data.isStory);

        // Send notifications for mentions
        if (mentions.length > 0) {
          await this.sendMentionNotifications(updateId, userId, userName, mentions);
        }

        console.log('✅ Update synced to Firebase:', updateId);
      } catch (firebaseError: any) {
        console.error('⚠️ Firebase sync failed, keeping local copy:', firebaseError);
        // Update sync status to failed, but keep local copy
        await localUpdatesStorage.updateSyncStatus(localId, 'failed');
        // Don't throw error - local copy is still available
      }

      console.log('✅ Update created successfully (local + Firebase)');
      return {
        success: true,
        data: {
          updateId: updateId,
          mediaUrl: mediaInfo?.url || ''
        }
      };

    } catch (error: any) {
      console.error('❌ Error creating update:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Upload media with progress tracking
   */
  private async uploadMedia(
    mediaUri: string,
    type: UpdateType,
    updateId: string,
    isStory: boolean,
    onProgress?: (_progress: UploadProgress) => void
  ): Promise<ServiceResponse<MediaInfo>> {
    try {
      // Fetch the media file
      const response = await fetch(mediaUri);
      const blob = await response.blob();

      // Generate file path
      const basePath = isStory ? this.STORAGE_PATHS.STORIES : this.STORAGE_PATHS.UPDATES;
      const fileExtension = type === 'video' ? 'mp4' : 'jpg';
      const fileName = `${updateId}.${fileExtension}`;
      const filePath = `${basePath}/${fileName}`;

      // Create storage reference
      const storageRef = ref(storage, filePath);

      // Upload with progress tracking
      const uploadTask = uploadBytesResumable(storageRef, blob);

      return new Promise((resolve) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            const progress = {
              bytesTransferred: snapshot.bytesTransferred,
              totalBytes: snapshot.totalBytes,
              percentage: (snapshot.bytesTransferred / snapshot.totalBytes) * 100,
            };
            onProgress?.(progress);
          },
          (error) => {
            console.error('❌ Upload error:', error);
            resolve({ success: false, error: error.message });
          },
          async () => {
            try {
              // Get download URL
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              
              // Get metadata
              const metadata = await getMetadata(uploadTask.snapshot.ref);
              
              // Create media info
              const mediaInfo: MediaInfo = {
                id: updateId,
                url: downloadURL,
                type,
                size: metadata.size,
                format: fileExtension,
                quality: 'high',
              };

              // Generate thumbnail for videos
              if (type === 'video') {
                const thumbnailResult = await this.generateVideoThumbnail(downloadURL, updateId);
                if (thumbnailResult.success) {
                  mediaInfo.thumbnailUrl = thumbnailResult.data;
                }
              }

              resolve({ success: true, data: mediaInfo });
            } catch (error: any) {
              resolve({ success: false, error: error.message });
            }
          }
        );
      });

    } catch (error: any) {
      console.error('❌ Media upload error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate video thumbnail
   */
  private async generateVideoThumbnail(
    videoUrl: string, 
    updateId: string
  ): Promise<ServiceResponse<string>> {
    try {
      // Real video thumbnail generation implementation
      const thumbnailPath = `${this.STORAGE_PATHS.THUMBNAILS}/${updateId}_thumb.jpg`;

      try {
        // Use expo-av for real thumbnail generation

        const { uri } = await VideoThumbnails.getThumbnailAsync(videoUrl, {
          time: 1000, // 1 second
          quality: 1,
        });

        // Upload thumbnail to Firebase Storage
        const response = await fetch(uri);
        const blob = await response.blob();
        const storageRef = ref(storage, thumbnailPath);
        await uploadBytes(storageRef, blob);
        const downloadURL = await getDownloadURL(storageRef);

        return { success: true, data: downloadURL };
      } catch (thumbnailError) {
        console.error('Error generating video thumbnail:', thumbnailError);
        // Fallback to a simple thumbnail URL
        return { success: true, data: `${videoUrl}_thumbnail` };
      }
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Extract hashtags from text
   */
  private extractHashtags(text: string): string[] {
    const hashtagRegex = /#[\w\u0590-\u05ff]+/g;
    const matches = text.match(hashtagRegex);
    return matches ? matches.map(tag => tag.slice(1).toLowerCase()) : [];
  }

  /**
   * Extract mentions from text
   */
  private extractMentions(text: string): string[] {
    const mentionRegex = /@[\w\u0590-\u05ff]+/g;
    const matches = text.match(mentionRegex);
    return matches ? matches.map(mention => mention.slice(1).toLowerCase()) : [];
  }

  /**
   * Initialize analytics for new update
   */
  private async initializeAnalytics(updateId: string, isStory: boolean): Promise<void> {
    try {
      const analytics: Omit<UpdateAnalytics, 'updateId'> = {
        totalViews: 0,
        uniqueViews: 0,
        likes: 0,
        comments: 0,
        shares: 0,
        downloads: 0,
        reactions: {},


        viewsByHour: {},
        viewsByCountry: {},
        viewsByAge: {},
        viewsByGender: {},
        engagementRate: 0,
        reachRate: 0,
        impressions: 0,
        clickThroughRate: 0,
      };

      const analyticsRef = doc(db, this.COLLECTIONS.ANALYTICS, updateId);
      await setDoc(analyticsRef, {
        updateId,
        ...analytics,
        isStory,
        createdAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('❌ Error initializing analytics:', error);
    }
  }

  /**
   * Send mention notifications
   */
  private async sendMentionNotifications(
    updateId: string,
    fromUserId: string,
    fromUserName: string,
    mentions: string[]
  ): Promise<void> {
    try {
      const batch = writeBatch(db);

      for (const mentionedUser of mentions) {
        const notificationId = `mention_${updateId}_${mentionedUser}_${Date.now()}`;
        const notification: Omit<UpdateNotification, 'id'> = {
          type: 'mention',
          updateId,
          fromUserId,
          fromUserName,
          toUserId: mentionedUser,
          message: `${fromUserName} mentioned you in an update`,
          timestamp: new Date(),
          isRead: false,
          actionUrl: `/update/${updateId}`,
        };

        const notificationRef = doc(db, this.COLLECTIONS.NOTIFICATIONS, notificationId);
        batch.set(notificationRef, {
          id: notificationId,
          ...notification,
          timestamp: serverTimestamp(),
        });
      }

      await batch.commit();
    } catch (error) {
      console.error('❌ Error sending mention notifications:', error);
    }
  }

  // ==================== UPDATE INTERACTIONS ====================

  /**
   * Like/Unlike an update
   */
  async toggleLike(
    updateId: string,
    userId: string,
    userName: string,
    userAvatar?: string
  ): Promise<ServiceResponse<{ isLiked: boolean; likeCount: number }>> {
    try {
      if (!this.isFirebaseAvailable()) {
        return {
          success: false,
          error: 'Firebase not available'
        };
      }

      const updateRef = doc(db, this.COLLECTIONS.UPDATES, updateId);

      return await runTransaction(db, async (transaction) => {
        const updateDoc = await transaction.get(updateRef);

        if (!updateDoc.exists()) {
          throw new Error('Update not found');
        }

        const updateData = updateDoc.data() as Update;
        const isCurrentlyLiked = updateData.likes.includes(userId);

        let newLikes: string[];
        let newLikeCount: number;

        if (isCurrentlyLiked) {
          // Unlike
          newLikes = updateData.likes.filter(id => id !== userId);
          newLikeCount = Math.max(0, updateData.likeCount - 1);
        } else {
          // Like
          newLikes = [...updateData.likes, userId];
          newLikeCount = updateData.likeCount + 1;

          // Create like notification
          await this.createNotification({
            type: 'like',
            updateId,
            fromUserId: userId,
            fromUserName: userName,
            fromUserAvatar: userAvatar,
            toUserId: updateData.userId,
            message: `${userName} liked your update`,
          });
        }

        // Update the document
        transaction.update(updateRef, {
          likes: newLikes,
          likeCount: newLikeCount,
        });

        // Update analytics
        const analyticsRef = doc(db, this.COLLECTIONS.ANALYTICS, updateId);
        transaction.update(analyticsRef, {
          likes: newLikeCount,
        });

        return {
          success: true,
          data: {
            isLiked: !isCurrentlyLiked,
            likeCount: newLikeCount,
          }
        };
      });

    } catch (error: any) {
      console.error('❌ Error toggling like:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add a comment to an update
   */
  async addComment(
    updateId: string,
    userId: string,
    userName: string,
    userAvatar: string | undefined,
    text: string,
    parentCommentId?: string
  ): Promise<ServiceResponse<{ commentId: string; comment: UpdateComment }>> {
    try {
      const commentId = `comment_${Date.now()}_${userId}`;

      // Extract mentions from comment
      const mentions = this.extractMentions(text);

      const comment: UpdateComment = {
        id: commentId,
        updateId,
        userId,
        userName,
        userAvatar,
        text,
        timestamp: new Date(),
        likes: [],
        replies: [],
        mentions,
        isEdited: false,
        isVisible: true,
        parentCommentId,
      };

      // Save comment
      const commentRef = doc(db, this.COLLECTIONS.COMMENTS, commentId);
      await setDoc(commentRef, {
        ...comment,
        timestamp: serverTimestamp(),
      });

      // Update update's comment count
      const updateRef = doc(db, this.COLLECTIONS.UPDATES, updateId);
      await updateDoc(updateRef, {
        commentCount: increment(1),
      });

      // Update analytics
      const analyticsRef = doc(db, this.COLLECTIONS.ANALYTICS, updateId);
      await updateDoc(analyticsRef, {
        comments: increment(1),
      });

      // Send notifications for mentions
      if (mentions.length > 0) {
        await this.sendMentionNotifications(updateId, userId, userName, mentions);
      }

      // Notify update owner
      const updateSnapshot = await getDoc(updateRef);
      if (updateSnapshot.exists()) {
        const updateData = updateSnapshot.data() as Update;
        if (updateData.userId !== userId) {
          await this.createNotification({
            type: 'comment',
            updateId,
            fromUserId: userId,
            fromUserName: userName,
            fromUserAvatar: userAvatar,
            toUserId: updateData.userId,
            message: `${userName} commented on your update`,
          });
        }
      }

      return { success: true, data: { commentId, comment } };

    } catch (error: any) {
      console.error('❌ Error adding comment:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Share an update
   */
  async shareUpdate(
    updateId: string,
    userId: string,
    userName: string,
    userAvatar?: string,
    shareType: 'repost' | 'external' | 'chat' = 'repost'
  ): Promise<ServiceResponse<{ shareCount: number }>> {
    try {
      const updateRef = doc(db, this.COLLECTIONS.UPDATES, updateId);

      return await runTransaction(db, async (transaction) => {
        const updateDoc = await transaction.get(updateRef);

        if (!updateDoc.exists()) {
          throw new Error('Update not found');
        }

        const updateData = updateDoc.data() as Update;
        const newShareCount = updateData.shareCount + 1;

        // Add user to shares array if not already there
        const newShares = updateData.shares.some(share => share.userId === userId)
          ? updateData.shares
          : [...updateData.shares, {
              userId,
              userName,
              userAvatar,
              timestamp: new Date(),
              type: 'share',
              metadata: { shareType },
            }];

        // Update the document
        transaction.update(updateRef, {
          shares: newShares,
          shareCount: newShareCount,
        });

        // Update analytics
        const analyticsRef = doc(db, this.COLLECTIONS.ANALYTICS, updateId);
        transaction.update(analyticsRef, {
          shares: newShareCount,
        });

        // Notify update owner
        if (updateData.userId !== userId) {
          await this.createNotification({
            type: 'share',
            updateId,
            fromUserId: userId,
            fromUserName: userName,
            fromUserAvatar: userAvatar,
            toUserId: updateData.userId,
            message: `${userName} shared your update`,
          });
        }

        return {
          success: true,
          data: { shareCount: newShareCount }
        };
      });

    } catch (error: any) {
      console.error('❌ Error sharing update:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a notification
   */
  private async createNotification(data: Omit<UpdateNotification, 'id' | 'timestamp' | 'isRead'>): Promise<void> {
    try {
      const notificationId = `${data.type}_${data.updateId}_${data.fromUserId}_${Date.now()}`;

      const notification: UpdateNotification = {
        id: notificationId,
        ...data,
        timestamp: new Date(),
        isRead: false,
        actionUrl: `/update/${data.updateId}`,
      };

      const notificationRef = doc(db, this.COLLECTIONS.NOTIFICATIONS, notificationId);
      await setDoc(notificationRef, {
        ...notification,
        timestamp: serverTimestamp(),
      });
    } catch (error) {
      console.error('❌ Error creating notification:', error);
    }
  }

  // ==================== FEED MANAGEMENT ====================

  /**
   * Get personalized updates feed (Local-first with Firebase sync)
   */
  async getUpdatesFeed(
    userId: string,
    config: FeedConfig = {
      algorithm: 'personalized',
      includeStories: false,
      includeFriends: true,
      includeFollowing: true,
      includePublic: true,
      contentTypes: ['image', 'video', 'text'],
      maxAge: 168, // 7 days
      minEngagement: 0,
    },
    lastUpdateId?: string,
    limitCount: number = 20
  ): Promise<ServiceResponse<{ updates: Update[]; hasMore: boolean; nextCursor?: string }>> {
    try {
      console.log('📱 Loading updates feed for user:', userId);

      // 🏠 LOAD FROM LOCAL STORAGE FIRST
      console.log('💾 Loading updates from local storage...');
      const localUpdates = await localUpdatesStorage.getUpdates(limitCount, 0, config.includeStories);
      console.log(`✅ Loaded ${localUpdates.length} updates from local storage`);

      // 🔄 SYNC WITH FIREBASE IN BACKGROUND
      let firebaseUpdates: Update[] = [];
      let hasMore = false;
      let nextCursor: string | undefined;

      if (this.isFirebaseAvailable()) {
        try {
          // Simplified query to avoid index requirement - filter privacy client-side
          let q = query(
            collection(db, this.COLLECTIONS.UPDATES),
            where('isVisible', '==', true),
            orderBy('timestamp', 'desc'),
            limit(limitCount * 2) // Get more to account for client-side filtering
          );

        // Add cursor for pagination
        if (lastUpdateId) {
          const lastDoc = await getDoc(doc(db, this.COLLECTIONS.UPDATES, lastUpdateId));
          if (lastDoc.exists()) {
            q = query(q, startAfter(lastDoc));
          }
        }

        const snapshot = await getDocs(q);

        for (const docSnap of snapshot.docs) {
          const data = docSnap.data();
          const update: Update = {
            id: docSnap.id,
            ...data,
            timestamp: data.timestamp?.toDate() || new Date(),
            expiresAt: data.expiresAt?.toDate(),
          } as Update;

          // Client-side privacy filtering (since we removed it from the query)
          const allowedPrivacyLevels = this.getPrivacyFilters(config);
          if (!allowedPrivacyLevels.includes(update.privacy)) {
            continue;
          }

          // Apply additional filters
          if (this.passesFilters(update, config)) {
            // Add user-specific interaction states
            update.isLikedByCurrentUser = update.likes.includes(userId);
            update.isViewedByCurrentUser = update.views.some(v => v.userId === userId);
            update.isSharedByCurrentUser = update.shares.some(s => s.userId === userId);
            update.isDownloadedByCurrentUser = update.downloads.some(d => d.userId === userId);

            firebaseUpdates.push(update);
          }
        }

        hasMore = snapshot.docs.length === limitCount;
        nextCursor = hasMore ? snapshot.docs[snapshot.docs.length - 1].id : undefined;

        // Save new Firebase updates to local storage
        for (const update of firebaseUpdates) {
          try {
            await localUpdatesStorage.saveUpdate(update);
          } catch (error) {
            console.error('⚠️ Failed to save Firebase update locally:', error);
          }
        }

        console.log(`✅ Synced ${firebaseUpdates.length} updates from Firebase`);
      } catch (firebaseError: any) {
        console.error('⚠️ Firebase sync failed, using local data only:', firebaseError);
        // Continue with local data only
      }
    } else {
      console.warn('⚠️ Firestore not available, using local data only');
    }

      // 📱 MERGE LOCAL AND FIREBASE DATA
      // Prioritize local updates (offline-first), then add Firebase updates not in local
      const mergedUpdates = this.mergeLocalAndFirebaseUpdates(localUpdates, firebaseUpdates);

      // Track view for analytics (only for Firebase updates)
      if (firebaseUpdates.length > 0) {
        try {
          await this.trackFeedView(userId, firebaseUpdates.map(u => u.id));
        } catch (error) {
          console.error('⚠️ Failed to track feed view:', error);
        }
      }

      return {
        success: true,
        data: {
          updates: mergedUpdates,
          hasMore,
          nextCursor,
        }
      };

    } catch (error: any) {
      console.error('❌ Error loading updates feed:', error);
      // Fallback to local data only
      try {
        const localUpdates = await localUpdatesStorage.getUpdates(limitCount, 0, config.includeStories);
        return {
          success: true,
          data: {
            updates: localUpdates,
            hasMore: false,
            nextCursor: undefined,
          }
        };
      } catch (localError) {
        console.error('❌ Failed to load local updates as fallback:', localError);
        return { success: false, error: error.message };
      }
    }
  }

  /**
   * Get stories feed
   */
  async getStoriesFeed(
    userId: string,
    includeOwn: boolean = true
  ): Promise<ServiceResponse<{ stories: Story[]; myStories: Story[] }>> {
    try {
      console.log('📖 Loading stories feed for user:', userId);

      // Get current time for expiration check
      const now = new Date();
      const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const allStories: Story[] = [];
      const myStories: Story[] = [];

      if (!this.isFirebaseAvailable()) {
        console.warn('⚠️ Firestore not available for stories');
        return {
          success: true,
          data: { stories: allStories, myStories }
        };
      }

      // Check if user is authenticated
      const currentUser = auth?.currentUser;
      if (!currentUser) {
        console.warn('⚠️ User not authenticated for stories feed');
        return {
          success: true,
          data: { stories: allStories, myStories }
        };
      }

      // Query for non-expired stories with error handling
      try {
        const q = query(
          collection(db, this.COLLECTIONS.STORIES),
          where('isVisible', '==', true),
          where('timestamp', '>', Timestamp.fromDate(twentyFourHoursAgo)),
          orderBy('timestamp', 'desc')
        );

        const snapshot = await getDocs(q);

      for (const docSnap of snapshot.docs) {
        const data = docSnap.data();
        const story: Story = {
          id: docSnap.id,
          ...data,
          isStory: true,
          timestamp: data.timestamp?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date(now.getTime() + 24 * 60 * 60 * 1000),
        } as Story;

        // Check if story is still valid (not expired)
        if (story.expiresAt > now) {
          if (story.userId === userId && includeOwn) {
            myStories.push(story);
          } else if (story.userId !== userId) {
            // Add user-specific interaction states
            story.isLikedByCurrentUser = story.likes.includes(userId);
            story.isViewedByCurrentUser = story.views.some(v => v.userId === userId);

            allStories.push(story);
          }
        }
      }

      } catch (firebaseError: any) {
        console.error('❌ Firebase query error for stories:', firebaseError);

        if (firebaseError.code === 'permission-denied') {
          console.warn('⚠️ Permission denied for stories - using empty results');
        } else if (firebaseError.code === 'unavailable') {
          console.warn('⚠️ Firebase unavailable for stories - using empty results');
        } else {
          console.warn('⚠️ Unknown Firebase error for stories:', firebaseError.message);
        }

        // Continue with empty stories instead of failing
      }

      // Group stories by user
      const storiesByUser = this.groupStoriesByUser(allStories);

      return {
        success: true,
        data: {
          stories: storiesByUser,
          myStories: myStories.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime()),
        }
      };

    } catch (error: any) {
      console.error('❌ Error loading stories feed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * View an update (track analytics)
   */
  async viewUpdate(
    updateId: string,
    userId: string,
    userName: string,
    userAvatar?: string,
    viewDuration?: number
  ): Promise<ServiceResponse<{ viewCount: number }>> {
    try {
      const updateRef = doc(db, this.COLLECTIONS.UPDATES, updateId);

      return await runTransaction(db, async (transaction) => {
        const updateDoc = await transaction.get(updateRef);

        if (!updateDoc.exists()) {
          throw new Error('Update not found');
        }

        const updateData = updateDoc.data() as Update;

        // Check if user already viewed this update
        const hasViewed = updateData.views.some(v => v.userId === userId);

        if (!hasViewed) {
          const viewInteraction: UserInteraction = {
            userId,
            userName,
            userAvatar,
            timestamp: new Date(),
            type: 'view',
            metadata: { viewDuration },
          };

          const newViews = [...updateData.views, viewInteraction];
          const newViewCount = updateData.viewCount + 1;

          // Update the document
          transaction.update(updateRef, {
            views: newViews,
            viewCount: newViewCount,
          });

          // Update analytics
          const analyticsRef = doc(db, this.COLLECTIONS.ANALYTICS, updateId);
          transaction.update(analyticsRef, {
            totalViews: increment(1),
            uniqueViews: increment(1),
          });

          return {
            success: true,
            data: { viewCount: newViewCount }
          };
        }

        return {
          success: true,
          data: { viewCount: updateData.viewCount }
        };
      });

    } catch (error: any) {
      console.error('❌ Error viewing update:', error);
      return { success: false, error: error.message };
    }
  }

  // ==================== HELPER METHODS ====================

  private getPrivacyFilters(config: FeedConfig): UpdatePrivacy[] {
    const filters: UpdatePrivacy[] = [];

    if (config.includePublic) filters.push('public');
    if (config.includeFriends) filters.push('friends');

    return filters.length > 0 ? filters : ['public'];
  }

  private passesFilters(update: Update, config: FeedConfig): boolean {
    // Check content type
    if (!config.contentTypes.includes(update.type)) return false;

    // Check age
    const ageInHours = (Date.now() - update.timestamp.getTime()) / (1000 * 60 * 60);
    if (ageInHours > config.maxAge) return false;

    // Check minimum engagement
    const engagementScore = update.likeCount + update.commentCount + update.shareCount;
    if (engagementScore < config.minEngagement) return false;

    return true;
  }

  private groupStoriesByUser(stories: Story[]): Story[] {
    const userStories = new Map<string, Story[]>();

    // Group stories by user
    stories.forEach(story => {
      if (!userStories.has(story.userId)) {
        userStories.set(story.userId, []);
      }
      userStories.get(story.userId)!.push(story);
    });

    // Return the first story from each user (most recent)
    const groupedStories: Story[] = [];
    userStories.forEach(userStoriesArray => {
      if (userStoriesArray.length > 0) {
        // Sort by timestamp and take the most recent
        userStoriesArray.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
        groupedStories.push(userStoriesArray[0]);
      }
    });

    return groupedStories.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  private async trackFeedView(userId: string, updateIds: string[]): Promise<void> {
    try {
      // Track that user viewed these updates in their feed
      // This can be used for recommendation algorithms
      const batch = writeBatch(db);

      updateIds.forEach(updateId => {
        const viewRef = doc(db, 'feed_views', `${userId}_${updateId}_${Date.now()}`);
        batch.set(viewRef, {
          userId,
          updateId,
          timestamp: serverTimestamp(),
          context: 'feed',
        });
      });

      await batch.commit();
    } catch (error) {
      console.error('❌ Error tracking feed view:', error);
    }
  }

  // ==================== MISSING METHODS ====================

  async getComments(updateId: string): Promise<{
    success: boolean;
    comments?: any[];
    error?: string;
  }> {
    try {
      console.log('🔥 Getting comments for update:', updateId);

      const commentsQuery = query(
        collection(db, this.COLLECTIONS.COMMENTS),
        where('updateId', '==', updateId),
        orderBy('timestamp', 'desc')
      );

      const snapshot = await getDocs(commentsQuery);
      const comments = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return {
        success: true,
        comments
      };
    } catch (error) {
      console.error('❌ Error getting comments:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get comments'
      };
    }
  }

  async getLikes(updateId: string): Promise<{
    success: boolean;
    likes?: any[];
    error?: string;
  }> {
    try {
      console.log('🔥 Getting likes for update:', updateId);

      const updateRef = doc(db, this.COLLECTIONS.UPDATES, updateId);
      const updateDoc = await getDoc(updateRef);

      if (!updateDoc.exists()) {
        return { success: false, error: 'Update not found' };
      }

      const updateData = updateDoc.data() as Update;
      const likeUserIds = updateData.likes || [];

      // Fetch user details for each like
      const likesWithUserDetails = await Promise.all(
        likeUserIds.map(async (userId: string) => {
          try {
            const userRef = doc(db, this.COLLECTIONS.USERS, userId);
            const userDoc = await getDoc(userRef);

            if (userDoc.exists()) {
              const userData = userDoc.data();
              return {
                userId,
                userName: userData.name || userData.displayName || userData.username || 'Unknown User',
                userAvatar: userData.avatar || userData.photoURL,
                timestamp: new Date(), // We don't store individual like timestamps, use current time
                type: 'like'
              };
            } else {
              // User not found, return basic info
              return {
                userId,
                userName: 'Unknown User',
                userAvatar: undefined,
                timestamp: new Date(),
                type: 'like'
              };
            }
          } catch (userError) {
            console.error('❌ Error fetching user details for like:', userId, userError);
            return {
              userId,
              userName: 'Unknown User',
              userAvatar: undefined,
              timestamp: new Date(),
              type: 'like'
            };
          }
        })
      );

      return {
        success: true,
        likes: likesWithUserDetails
      };
    } catch (error) {
      console.error('❌ Error getting likes:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get likes'
      };
    }
  }

  async getViews(updateId: string): Promise<{
    success: boolean;
    views?: any[];
    error?: string;
  }> {
    try {
      console.log('🔥 Getting views for update:', updateId);

      const updateRef = doc(db, this.COLLECTIONS.UPDATES, updateId);
      const updateDoc = await getDoc(updateRef);

      if (!updateDoc.exists()) {
        return { success: false, error: 'Update not found' };
      }

      const updateData = updateDoc.data() as Update;
      const views = updateData.views || [];

      return {
        success: true,
        views
      };
    } catch (error) {
      console.error('❌ Error getting views:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get views'
      };
    }
  }

  async getDownloads(updateId: string): Promise<{
    success: boolean;
    downloads?: any[];
    error?: string;
  }> {
    try {
      console.log('🔥 Getting downloads for update:', updateId);

      const updateRef = doc(db, this.COLLECTIONS.UPDATES, updateId);
      const updateDoc = await getDoc(updateRef);

      if (!updateDoc.exists()) {
        return { success: false, error: 'Update not found' };
      }

      const updateData = updateDoc.data() as Update;
      const downloads = updateData.downloads || [];

      return {
        success: true,
        downloads
      };
    } catch (error) {
      console.error('❌ Error getting downloads:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get downloads'
      };
    }
  }

  async getDownloadAnalytics(updateId: string): Promise<{
    success: boolean;
    analytics?: any;
    error?: string;
  }> {
    try {
      console.log('🔥 Getting download analytics for update:', updateId);

      const downloads = await this.getDownloads(updateId);
      if (!downloads.success || !downloads.downloads) {
        return { success: false, error: 'Failed to get downloads' };
      }

      const analytics = {
        totalDownloads: downloads.downloads.length,
        deviceBreakdown: [],
        timeBreakdown: []
      };

      return {
        success: true,
        analytics
      };
    } catch (error) {
      console.error('❌ Error getting download analytics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get download analytics'
      };
    }
  }

  async getAnalytics(updateId: string): Promise<{
    success: boolean;
    analytics?: any;
    error?: string;
  }> {
    try {
      console.log('🔥 Getting analytics for update:', updateId);

      const views = await this.getViews(updateId);
      if (!views.success || !views.views) {
        return { success: false, error: 'Failed to get views' };
      }

      const analytics = {
        totalViews: views.views.length,
        hourlyBreakdown: [],
        deviceBreakdown: []
      };

      return {
        success: true,
        analytics
      };
    } catch (error) {
      console.error('❌ Error getting analytics:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get analytics'
      };
    }
  }

  // ==================== LOCAL STORAGE HELPERS ====================

  /**
   * Merge local and Firebase updates, prioritizing local data
   */
  private mergeLocalAndFirebaseUpdates(localUpdates: Update[], firebaseUpdates: Update[]): Update[] {
    const merged: Update[] = [...localUpdates];
    const localIds = new Set(localUpdates.map(u => u.id));

    // Add Firebase updates that aren't already in local storage
    for (const firebaseUpdate of firebaseUpdates) {
      if (!localIds.has(firebaseUpdate.id)) {
        merged.push(firebaseUpdate);
      }
    }

    // Sort by timestamp (newest first)
    return merged.sort((a, b) => {
      const timeA = typeof a.timestamp === 'object' ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
      const timeB = typeof b.timestamp === 'object' ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
      return timeB - timeA;
    });
  }

  /**
   * Get update by ID
   */
  async getUpdateById(updateId: string): Promise<ServiceResponse<Update>> {
    try {
      console.log('📱 Getting update by ID:', updateId);

      const updateDoc = await getDoc(doc(db, this.COLLECTIONS.UPDATES, updateId));

      if (!updateDoc.exists()) {
        return { success: false, error: 'Update not found' };
      }

      const data = updateDoc.data();
      const update: Update = {
        id: updateDoc.id,
        ...data,
        timestamp: data.timestamp?.toDate() || new Date(),
        expiresAt: data.expiresAt?.toDate(),
      } as Update;

      return { success: true, data: update };
    } catch (error: any) {
      console.error('❌ Error getting update by ID:', error);
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
export const comprehensiveUpdatesService = new ComprehensiveUpdatesService();
export default comprehensiveUpdatesService;
