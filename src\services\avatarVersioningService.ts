/**
 * Avatar Versioning Service for IraChat
 * Manages avatar versions and background sync updates
 * Similar to <PERSON>s<PERSON><PERSON>'s profile picture caching system
 */

import { doc, getDoc } from 'firebase/firestore';
import { db } from './firebase';
import { enhancedMediaCacheService } from './enhancedMediaCacheService';
import { networkStateManager } from './networkStateManager';
import { offlineDatabaseService } from './offlineDatabase';

export interface AvatarVersion {
  userId: string;
  version: string;
  url: string;
  lastUpdated: number;
  size: number;
}

export interface AvatarSyncResult {
  userId: string;
  updated: boolean;
  newVersion?: string;
  error?: string;
}

class AvatarVersioningService {
  private syncInProgress = false;
  private backgroundSyncInterval: NodeJS.Timeout | null = null;
  private readonly SYNC_INTERVAL = 5 * 60 * 1000; // 5 minutes
  private readonly VERSION_CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Initialize avatar versioning service
   */
  async initialize(): Promise<void> {
    console.log('👤 Initializing Avatar Versioning Service...');
    
    // Start background sync
    this.startBackgroundSync();
    
    // Listen for network changes
    networkStateManager.addListener('avatarVersioning', this.handleNetworkChange.bind(this));
    
    console.log('✅ Avatar Versioning Service initialized');
  }

  /**
   * Get avatar with version check
   */
  async getAvatarWithVersionCheck(
    userId: string, 
    size: number = 150,
    forceCheck: boolean = false
  ): Promise<string | null> {
    try {
      // Get cached avatar first for instant loading
      const cachedAvatar = await enhancedMediaCacheService.getCachedAvatar(userId);
      
      // If we have a cached avatar and don't need to force check
      if (cachedAvatar && cachedAvatar.isValid && !forceCheck) {
        // Check if we need to verify version in background
        const shouldCheckVersion = this.shouldCheckVersion(cachedAvatar);
        
        if (shouldCheckVersion && networkStateManager.isOnline()) {
          // Check version in background without blocking UI
          this.checkAvatarVersionInBackground(userId, cachedAvatar.version);
        }
        
        return cachedAvatar.localPath || null;
      }

      // If no cached avatar or force check, get from server
      if (networkStateManager.isOnline()) {
        const serverAvatar = await this.getAvatarVersionFromServer(userId);
        
        if (serverAvatar) {
          // Check if we need to download new version
          const needsUpdate = !cachedAvatar || 
                             cachedAvatar.version !== serverAvatar.version ||
                             !cachedAvatar.isValid;
          
          if (needsUpdate) {
            const newCachedAvatar = await enhancedMediaCacheService.cacheAvatar(
              userId,
              serverAvatar.url,
              size,
              serverAvatar.version
            );
            
            return newCachedAvatar?.localPath || null;
          }
        }
      }

      // Return cached avatar path if available
      return cachedAvatar?.localPath || null;
    } catch (error) {
      console.error('❌ Failed to get avatar with version check:', error);
      return null;
    }
  }

  /**
   * Check avatar version from server
   */
  async getAvatarVersionFromServer(userId: string): Promise<AvatarVersion | null> {
    try {
      if (!networkStateManager.isOnline()) {
        return null;
      }

      const userDoc = await getDoc(doc(db, 'users', userId));
      
      if (!userDoc.exists()) {
        return null;
      }

      const userData = userDoc.data();
      
      if (!userData.avatar || !userData.avatarVersion) {
        return null;
      }

      return {
        userId,
        version: userData.avatarVersion,
        url: userData.avatar,
        lastUpdated: userData.avatarLastUpdated || Date.now(),
        size: 150, // Default size
      };
    } catch (error) {
      console.error('❌ Failed to get avatar version from server:', error);
      return null;
    }
  }

  /**
   * Sync avatars for multiple users
   */
  async syncAvatarsForUsers(userIds: string[]): Promise<AvatarSyncResult[]> {
    if (!networkStateManager.isOnline() || this.syncInProgress) {
      return [];
    }

    this.syncInProgress = true;
    console.log(`👤 Syncing avatars for ${userIds.length} users...`);

    const results: AvatarSyncResult[] = [];

    try {
      // Get server versions for all users
      const serverVersions = await this.getMultipleAvatarVersions(userIds);
      
      for (const userId of userIds) {
        try {
          const result = await this.syncSingleUserAvatar(userId, serverVersions.get(userId));
          results.push(result);
        } catch (error) {
          console.error(`❌ Failed to sync avatar for user ${userId}:`, error);
          results.push({
            userId,
            updated: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    } catch (error) {
      console.error('❌ Failed to sync avatars:', error);
    } finally {
      this.syncInProgress = false;
    }

    console.log(`✅ Avatar sync completed. Updated: ${results.filter(r => r.updated).length}`);
    return results;
  }

  /**
   * Sync single user avatar
   */
  private async syncSingleUserAvatar(
    userId: string, 
    serverVersion?: AvatarVersion
  ): Promise<AvatarSyncResult> {
    try {
      // Get cached avatar
      const cachedAvatar = await enhancedMediaCacheService.getCachedAvatar(userId);
      
      // If no server version, try to get it
      if (!serverVersion) {
        serverVersion = await this.getAvatarVersionFromServer(userId) || undefined;
      }

      // If no server version available, no update needed
      if (!serverVersion) {
        return { userId, updated: false };
      }

      // Check if update is needed
      const needsUpdate = !cachedAvatar || 
                         cachedAvatar.version !== serverVersion.version ||
                         !cachedAvatar.isValid;

      if (!needsUpdate) {
        // Update last checked time
        await this.updateLastChecked(userId);
        return { userId, updated: false };
      }

      // Download new avatar
      const newCachedAvatar = await enhancedMediaCacheService.cacheAvatar(
        userId,
        serverVersion.url,
        150,
        serverVersion.version
      );

      if (newCachedAvatar) {
        console.log(`✅ Updated avatar for user ${userId} to version ${serverVersion.version}`);
        return {
          userId,
          updated: true,
          newVersion: serverVersion.version,
        };
      } else {
        return {
          userId,
          updated: false,
          error: 'Failed to cache new avatar',
        };
      }
    } catch (error) {
      console.error(`❌ Failed to sync avatar for user ${userId}:`, error);
      return {
        userId,
        updated: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get multiple avatar versions efficiently
   */
  private async getMultipleAvatarVersions(userIds: string[]): Promise<Map<string, AvatarVersion>> {
    const versions = new Map<string, AvatarVersion>();

    try {
      // Batch get user documents
      const userDocs = await Promise.all(
        userIds.map(userId => getDoc(doc(db, 'users', userId)))
      );

      userDocs.forEach((userDoc, index) => {
        if (userDoc.exists()) {
          const userData = userDoc.data();
          const userId = userIds[index];
          
          if (userData.avatar && userData.avatarVersion) {
            versions.set(userId, {
              userId,
              version: userData.avatarVersion,
              url: userData.avatar,
              lastUpdated: userData.avatarLastUpdated || Date.now(),
              size: 150,
            });
          }
        }
      });
    } catch (error) {
      console.error('❌ Failed to get multiple avatar versions:', error);
    }

    return versions;
  }

  /**
   * Check if we should verify avatar version
   */
  private shouldCheckVersion(cachedAvatar: any): boolean {
    if (!cachedAvatar.lastChecked) {
      return true;
    }

    const timeSinceLastCheck = Date.now() - cachedAvatar.lastChecked;
    return timeSinceLastCheck > this.VERSION_CHECK_INTERVAL;
  }

  /**
   * Check avatar version in background
   */
  private async checkAvatarVersionInBackground(userId: string, currentVersion?: string): Promise<void> {
    try {
      const serverVersion = await this.getAvatarVersionFromServer(userId);
      
      if (serverVersion && serverVersion.version !== currentVersion) {
        console.log(`🔄 New avatar version available for user ${userId}`);
        
        // Download new version in background
        await enhancedMediaCacheService.cacheAvatar(
          userId,
          serverVersion.url,
          150,
          serverVersion.version
        );
      } else {
        // Update last checked time
        await this.updateLastChecked(userId);
      }
    } catch (error) {
      console.error('❌ Background avatar version check failed:', error);
    }
  }

  /**
   * Update last checked time
   */
  private async updateLastChecked(userId: string): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync(`
        UPDATE cached_avatars 
        SET lastChecked = ? 
        WHERE userId = ?
      `, [Date.now(), userId]);
    } catch (error) {
      console.error('❌ Failed to update last checked time:', error);
    }
  }

  /**
   * Start background sync
   */
  private startBackgroundSync(): void {
    // Clear any existing interval
    if (this.backgroundSyncInterval) {
      clearInterval(this.backgroundSyncInterval);
    }

    // Start periodic sync
    this.backgroundSyncInterval = setInterval(() => {
      if (networkStateManager.isOnline() && !this.syncInProgress) {
        this.performBackgroundSync();
      }
    }, this.SYNC_INTERVAL);
  }

  /**
   * Perform background sync for recently accessed avatars
   */
  private async performBackgroundSync(): Promise<void> {
    try {
      console.log('🔄 Performing background avatar sync...');
      
      // Get recently accessed avatars that need version check
      const db = offlineDatabaseService.getDatabase();
      const results = await db.getAllAsync(`
        SELECT userId FROM cached_avatars 
        WHERE isValid = 1 
        AND (lastChecked IS NULL OR lastChecked < ?)
        ORDER BY timestamp DESC
        LIMIT 20
      `, [Date.now() - this.VERSION_CHECK_INTERVAL]);

      const userIds = results.map((row: any) => row.userId);
      
      if (userIds.length > 0) {
        await this.syncAvatarsForUsers(userIds);
      }
    } catch (error) {
      console.error('❌ Background avatar sync failed:', error);
    }
  }

  /**
   * Handle network state changes
   */
  private handleNetworkChange(state: any): void {
    if (state.isOnline) {
      console.log('🌐 Network restored, checking avatar updates...');
      this.performBackgroundSync();
    }
  }

  /**
   * Stop background sync
   */
  stopBackgroundSync(): void {
    if (this.backgroundSyncInterval) {
      clearInterval(this.backgroundSyncInterval);
      this.backgroundSyncInterval = null;
    }
  }

  /**
   * Force refresh avatar for user
   */
  async forceRefreshAvatar(userId: string, size: number = 150): Promise<string | null> {
    return this.getAvatarWithVersionCheck(userId, size, true);
  }

  /**
   * Get avatar sync statistics
   */
  async getSyncStats(): Promise<{
    totalCached: number;
    needsCheck: number;
    invalid: number;
  }> {
    try {
      const db = offlineDatabaseService.getDatabase();
      
      const totalResult = await db.getAllAsync(`
        SELECT COUNT(*) as count FROM cached_avatars
      `);
      
      const needsCheckResult = await db.getAllAsync(`
        SELECT COUNT(*) as count FROM cached_avatars 
        WHERE isValid = 1 AND (lastChecked IS NULL OR lastChecked < ?)
      `, [Date.now() - this.VERSION_CHECK_INTERVAL]);
      
      const invalidResult = await db.getAllAsync(`
        SELECT COUNT(*) as count FROM cached_avatars WHERE isValid = 0
      `);

      return {
        totalCached: (totalResult[0] as any)?.count || 0,
        needsCheck: (needsCheckResult[0] as any)?.count || 0,
        invalid: (invalidResult[0] as any)?.count || 0,
      };
    } catch (error) {
      console.error('❌ Failed to get sync stats:', error);
      return { totalCached: 0, needsCheck: 0, invalid: 0 };
    }
  }
}

export const avatarVersioningService = new AvatarVersioningService();
