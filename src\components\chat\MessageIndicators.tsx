/**
 * Message Indicators Component for IraChat
 * Shows visual indicators for starred, forwarded, edited, copied messages
 */

import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

interface MessageIndicatorsProps {
  isStarred?: boolean;
  isForwarded?: boolean;
  isEdited?: boolean;
  isCopied?: boolean;
  editedAt?: Date;
  forwardedFrom?: string;
  copiedAt?: Date;
  showText?: boolean; // Whether to show text labels
}

export const MessageIndicators: React.FC<MessageIndicatorsProps> = ({
  isStarred = false,
  isForwarded = false,
  isEdited = false,
  isCopied = false,
  editedAt,
  forwardedFrom,
  copiedAt,
  showText = true,
}) => {
  const { colors } = useTheme();

  if (!isStarred && !isForwarded && !isEdited && !isCopied) {
    return null;
  }

  return (
    <View style={styles.container}>
      {/* Forwarded indicator */}
      {isForwarded && (
        <View style={styles.indicatorRow}>
          <Ionicons 
            name="arrow-forward" 
            size={12} 
            color={colors.textSecondary} 
            style={styles.icon}
          />
          {showText && (
            <Text style={[styles.indicatorText, { color: colors.textSecondary }]}>
              {forwardedFrom ? `Forwarded from ${forwardedFrom}` : 'Forwarded'}
            </Text>
          )}
        </View>
      )}

      {/* Bottom row indicators */}
      <View style={styles.bottomRow}>
        {/* Edited indicator */}
        {isEdited && (
          <View style={styles.bottomIndicator}>
            <Ionicons 
              name="create-outline" 
              size={10} 
              color={colors.textSecondary} 
              style={styles.smallIcon}
            />
            {showText && (
              <Text style={[styles.smallText, { color: colors.textSecondary }]}>
                edited{editedAt ? ` ${formatTime(editedAt)}` : ''}
              </Text>
            )}
          </View>
        )}

        {/* Copied indicator */}
        {isCopied && (
          <View style={styles.bottomIndicator}>
            <Ionicons 
              name="copy-outline" 
              size={10} 
              color={colors.textSecondary} 
              style={styles.smallIcon}
            />
            {showText && (
              <Text style={[styles.smallText, { color: colors.textSecondary }]}>
                copied{copiedAt ? ` ${formatTime(copiedAt)}` : ''}
              </Text>
            )}
          </View>
        )}

        {/* Starred indicator */}
        {isStarred && (
          <View style={styles.bottomIndicator}>
            <Ionicons 
              name="star" 
              size={10} 
              color="#FFD700" 
              style={styles.smallIcon}
            />
            {showText && (
              <Text style={[styles.smallText, { color: colors.textSecondary }]}>
                starred
              </Text>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

// Helper function to format time
const formatTime = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return 'now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  
  return date.toLocaleDateString();
};

const styles = StyleSheet.create({
  container: {
    marginTop: 2,
  },
  indicatorRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  bottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: 2,
  },
  bottomIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 8,
    marginTop: 1,
  },
  icon: {
    marginRight: 4,
  },
  smallIcon: {
    marginRight: 2,
  },
  indicatorText: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  smallText: {
    fontSize: 9,
    fontStyle: 'italic',
  },
});

export default MessageIndicators;
