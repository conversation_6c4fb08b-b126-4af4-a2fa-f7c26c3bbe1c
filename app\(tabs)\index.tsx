// 💬 REAL CHAT LIST - Shows actual conversations!
// Fixed: Single export default, proper dependencies, optimized performance

import React, { useEffect, useState, useCallback, useMemo, useRef } from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  Text,
  TouchableOpacity,
  View,
  Image,
  StyleSheet,
  Animated,
  Dimensions,
  Alert,
} from "react-native";
import { Gesture, GestureDetector, GestureHandlerRootView } from "react-native-gesture-handler";
import ReAnimated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  runOnJS,
} from "react-native-reanimated";
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import * as Contacts from 'expo-contacts';
import { useSelector } from "react-redux";
import ErrorBoundary from "../../src/components/ErrorBoundary";
import { MainHeader } from "../../src/components/MainHeader";
import { IraChatWallpaper } from "../../src/components/ui/IraChatWallpaper";
import { RootState } from "../../src/redux/store";
import { realTimeMessagingService, RealChat } from "../../src/services/realTimeMessagingService";
import { offlineDatabaseService } from "../../src/services/offlineDatabase";
import { offlineChatListService } from "../../src/services/offlineChatListService";
import { localChatManagementService } from "../../src/services/localChatManagementService";
import { navigationService } from "../../src/services/navigationService";
import { chatsListService, ChatListItem } from "../../src/services/chatsListService";
import { formatChatTime } from "../../src/utils/dateUtils";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from "../../src/styles/iraChatDesignSystem";

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Function to get local contact name with priority over Firestore name
const getLocalContactName = async (userId: string, firestoreName: string): Promise<string> => {
  try {
    // Check if we have contacts permission
    const { status } = await Contacts.getPermissionsAsync();
    if (status !== 'granted') {
      return firestoreName;
    }

    // Get device contacts
    const { data } = await Contacts.getContactsAsync({
      fields: [Contacts.Fields.Name, Contacts.Fields.PhoneNumbers, Contacts.Fields.Emails],
    });

    // Try to find a matching contact by phone number or email
    // Note: This is a simplified approach. In a real app, you'd want to store
    // the mapping between userIds and phone numbers/emails
    for (const contact of data) {
      // Check if contact phone numbers or emails match the userId
      // This is a simplified check - you'd need proper user ID to phone/email mapping
      if (contact.phoneNumbers?.some(phone =>
          phone.number?.replace(/\D/g, '') === userId.replace(/\D/g, '')
        ) ||
        contact.emails?.some(email =>
          email.email?.toLowerCase() === userId.toLowerCase()
        )) {
        return contact.name || firestoreName;
      }
    }

    return firestoreName;
  } catch (error) {
    console.warn('Failed to get local contact name:', error);
    return firestoreName;
  }
};

// Beautiful Animated Chat Item Component with React.memo
const ChatItem = React.memo(function ChatItem({
  chat,
  currentUserId,
  onPress,
  onLongPress,
  isSelected,
  isSelectionMode,
  index
}: {
  chat: RealChat;
  currentUserId: string;
  onPress: (chat: RealChat) => void;
  onLongPress?: (chat: RealChat) => void;
  isSelected?: boolean;
  isSelectionMode?: boolean;
  index: number;
}) {
  const scaleAnimation = useRef(new Animated.Value(1)).current;
  const slideAnimation = useRef(new Animated.Value(50)).current;
  const fadeAnimation = useRef(new Animated.Value(0)).current;
  const [localContactName, setLocalContactName] = useState<string>('');

  // Entrance animation
  useEffect(() => {
    Animated.sequence([
      Animated.delay(index * 100), // Stagger animation
      Animated.parallel([
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: ANIMATIONS.normal,
          useNativeDriver: true,
        }),
        Animated.spring(slideAnimation, {
          toValue: 0,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]),
    ]).start();
  }, [index]);

  // Load local contact name on mount
  useEffect(() => {
    const loadLocalName = async () => {
      const otherUserId = chat.participants.find(id => id !== currentUserId);
      if (otherUserId) {
        const firestoreName = chat.participantNames[otherUserId] || 'Contact';
        const localName = await getLocalContactName(otherUserId, firestoreName);
        if (localName !== firestoreName) {
          setLocalContactName(localName);
        }
      }
    };
    loadLocalName();
  }, [chat, currentUserId]);

  // Memoize expensive calculations
  const chatData = useMemo(() => {
    const otherUserId = chat.participants.find(id => id !== currentUserId);
    const firestoreName = otherUserId ? chat.participantNames[otherUserId] : 'Contact';
    const otherUserName = localContactName || firestoreName; // Prioritize local contact name
    const otherUserAvatar = otherUserId ? chat.participantAvatars[otherUserId] : '';
    const unreadCount = chat.unreadCount[currentUserId] || 0;
    const formattedTime = formatChatTime(chat.lastMessageTime);

    return {
      otherUserId,
      otherUserName,
      otherUserAvatar,
      unreadCount,
      formattedTime,
    };
  }, [chat, currentUserId, localContactName]);

  const handlePressIn = () => {
    Animated.spring(scaleAnimation, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    Animated.spring(scaleAnimation, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  return (
    <Animated.View
      style={[
        styles.chatItemContainer,
        {
          opacity: fadeAnimation,
          transform: [
            { translateX: slideAnimation },
            { scale: scaleAnimation }
          ],
        },
      ]}
    >
      <TouchableOpacity
        style={[
          styles.chatItem,
          isSelected && styles.selectedChatItem,
          isSelectionMode && styles.selectionModeChatItem
        ]}
        onPress={() => {
          if (isSelectionMode) {
            onLongPress?.(chat);
          } else {
            onPress(chat);
          }
        }}
        onLongPress={() => onLongPress?.(chat)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={1}
      >
        {isSelectionMode && (
          <View style={styles.selectionIndicator}>
            <Ionicons
              name={isSelected ? "checkmark-circle" : "ellipse-outline"}
              size={24}
              color={isSelected ? IRACHAT_COLORS.primary : IRACHAT_COLORS.textMuted}
            />
          </View>
        )}

        <View style={styles.avatarContainer}>
          <Image
            source={{
              uri: chatData.otherUserAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(chatData.otherUserName)}&background=87CEEB&color=fff`
            }}
            style={styles.chatAvatar}
          />
          {chatData.unreadCount > 0 && (
            <View style={styles.onlineIndicator} />
          )}
        </View>

        <View style={styles.chatContent}>
          <View style={styles.chatHeader}>
            <Text style={styles.chatName} numberOfLines={1}>
              {chatData.otherUserName}
            </Text>
            <Text style={styles.chatTime}>{chatData.formattedTime}</Text>
          </View>

          <View style={styles.chatFooter}>
            <Text style={styles.lastMessage} numberOfLines={1}>
              {(() => {
                // Handle different lastMessage structures
                let messageText = '';

                if (typeof chat.lastMessage === 'string') {
                  messageText = chat.lastMessage;
                } else if (chat.lastMessage?.content) {
                  messageText = chat.lastMessage.content;
                } else if ((chat.lastMessage as any)?.text) {
                  messageText = (chat.lastMessage as any).text;
                }

                // Filter out empty or system messages
                if (messageText &&
                    messageText.trim() !== '' &&
                    messageText !== 'Chat opened' &&
                    messageText !== 'Chat cleared' &&
                    messageText !== 'No messages yet') {
                  return messageText;
                }

                return 'No messages yet';
              })()}
            </Text>
            {chatData.unreadCount > 0 && (
              <Animated.View style={styles.unreadBadge}>
                <Text style={styles.unreadText}>
                  {chatData.unreadCount > 99 ? '99+' : chatData.unreadCount}
                </Text>
              </Animated.View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
});

// Main Chat List Screen - SINGLE EXPORT DEFAULT
export default function ChatListScreen() {
  const router = useRouter();
  const [chats, setChats] = useState<RealChat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Enhanced Chat Management State
  const [searchQuery, setSearchQuery] = useState("");
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [selectedChats, setSelectedChats] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [filterType, setFilterType] = useState<'all' | 'unread'>('all');
  const [pinnedChats, setPinnedChats] = useState<string[]>([]);
  const [archivedChats, setArchivedChats] = useState<string[]>([]);

  // Swipe navigation setup
  const { width: SCREEN_WIDTH } = Dimensions.get('window');
  const translateX = useSharedValue(0);
  const tabIndicatorPosition = useSharedValue(0);
  const filterTabs = ['all', 'unread'] as const;

  // Swipe navigation functions
  const switchToFilter = (filterName: typeof filterTabs[number]) => {
    const filterIndex = filterTabs.indexOf(filterName);
    setFilterType(filterName);

    // Animate tab indicator with smooth spring
    tabIndicatorPosition.value = withSpring(filterIndex, {
      damping: 18,
      stiffness: 180,
      mass: 0.9,
    });
  };

  // Create smooth pan gesture with better physics and FlatList compatibility
  const panGesture = Gesture.Pan()
    .minDistance(10) // Require minimum distance to start gesture
    .activeOffsetX([-10, 10]) // Only activate for horizontal swipes
    .failOffsetY([-20, 20]) // Fail if vertical movement is too large
    .onUpdate((event) => {
      // Only respond to primarily horizontal gestures
      if (Math.abs(event.velocityY) > Math.abs(event.velocityX) * 2) {
        return; // Let FlatList handle vertical scrolling
      }

      // Smooth, responsive translation with damping
      const currentIndex = filterTabs.indexOf(filterType);
      const maxTranslation = SCREEN_WIDTH * 0.6;

      // Apply resistance at boundaries
      let translation = event.translationX;

      if (currentIndex === 0 && translation > 0) {
        // At first tab, add resistance to right swipe
        translation = translation * 0.2;
      } else if (currentIndex === filterTabs.length - 1 && translation < 0) {
        // At last tab, add resistance to left swipe
        translation = translation * 0.2;
      }

      // Clamp translation to prevent excessive movement
      translateX.value = Math.max(-maxTranslation, Math.min(maxTranslation, translation));
    })
    .onEnd((event) => {
      const { translationX, velocityX } = event;
      const currentIndex = filterTabs.indexOf(filterType);

      // More sensitive thresholds for smoother experience
      const swipeThreshold = SCREEN_WIDTH * 0.12; // Even more sensitive
      const velocityThreshold = 250; // Lower velocity threshold

      // Determine if we should switch tabs
      let shouldSwitch = false;
      let targetFilter = filterType;

      if (Math.abs(translationX) > swipeThreshold || Math.abs(velocityX) > velocityThreshold) {
        if (translationX > 0 && currentIndex > 0) {
          // Swipe right - go to previous tab
          targetFilter = filterTabs[currentIndex - 1];
          shouldSwitch = true;
        } else if (translationX < 0 && currentIndex < filterTabs.length - 1) {
          // Swipe left - go to next tab
          targetFilter = filterTabs[currentIndex + 1];
          shouldSwitch = true;
        }
      }

      if (shouldSwitch) {
        // Switch tab with smooth animation
        runOnJS(switchToFilter)(targetFilter);
      }

      // Always animate back to center with smooth spring
      translateX.value = withSpring(0, {
        damping: 25,
        stiffness: 250,
        mass: 0.7,
      });
    });

  const animatedContentStyle = useAnimatedStyle(() => {
    const progress = Math.abs(translateX.value) / (SCREEN_WIDTH * 0.3);
    const clampedProgress = Math.min(progress, 1);

    return {
      transform: [
        {
          translateX: translateX.value * 0.3 // More responsive movement
        },
        {
          scale: 1 - clampedProgress * 0.02 // Subtle scale effect
        }
      ],
      opacity: 1 - clampedProgress * 0.1, // Subtle fade during swipe
    };
  });



  // Get current user from Redux
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  // Subscribe to user's chats - FIXED DEPENDENCIES
  useEffect(() => {
    if (!currentUser?.id) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);

    // Load offline chats first for instant display
    const loadOfflineChats = async () => {
      try {
        await offlineDatabaseService.initialize();
        if (offlineDatabaseService.isReady()) {
          const db = offlineDatabaseService.getDatabase();
          const offlineChats = await db.getAllAsync(`
            SELECT * FROM chats ORDER BY updatedAt DESC
          `);

          if (offlineChats.length > 0) {
            // Convert offline chats to RealChat format
            const convertedChats: RealChat[] = offlineChats.map((chat: any) => ({
              id: chat.id,
              participants: [currentUser.id, chat.partnerId || ''],
              participantNames: {
                [currentUser.id]: currentUser.displayName || currentUser.name || 'You',
                [chat.partnerId || '']: chat.partnerName || 'Unknown'
              },
              participantAvatars: {
                [currentUser.id]: currentUser.photoURL || currentUser.avatar || '',
                [chat.partnerId || '']: chat.partnerAvatar || ''
              },
              isGroup: false,
              groupName: undefined,
              groupAvatar: undefined,
              lastMessage: chat.lastMessage ? {
                id: 'last',
                chatId: chat.id,
                content: chat.lastMessage,
                type: 'text' as const,
                senderId: chat.lastMessageSender || '',
                senderName: '',
                senderAvatar: '',
                timestamp: new Date(chat.lastMessageTime || Date.now()),
                statusInfo: {
                  status: 'delivered',
                  timestamp: new Date(chat.lastMessageTime || Date.now()),
                },
                isRead: false,
                isDelivered: true,
              } : undefined,
              lastMessageTime: new Date(chat.lastMessageTime || Date.now()),
              unreadCount: { [currentUser.id]: chat.unreadCount || 0 },
              createdAt: new Date(chat.createdAt),
              updatedAt: new Date(chat.updatedAt),
            }));

            setChats(convertedChats);
            console.log('📱 Loaded offline chats:', convertedChats.length);
          }
        }
      } catch (error) {
        console.warn('⚠️ Failed to load offline chats:', error);
      }
    };

    loadOfflineChats();

    // Subscribe to new chats list service for real-time updates
    const chatsListUnsubscribe = chatsListService.subscribeToChats(
      currentUser.id,
      (chatListItems: ChatListItem[]) => {
        // Convert ChatListItem to RealChat format
        const convertedChats: RealChat[] = chatListItems.map(item => ({
          id: item.id,
          participants: [currentUser.id, item.partnerId],
          participantNames: {
            [currentUser.id]: currentUser.displayName || currentUser.name || 'You',
            [item.partnerId]: item.partnerName
          },
          participantAvatars: {
            [currentUser.id]: currentUser.photoURL || currentUser.avatar || '',
            [item.partnerId]: item.partnerAvatar || ''
          },
          isGroup: false,
          groupName: undefined,
          groupAvatar: undefined,
          lastMessage: {
            id: 'last',
            chatId: item.id,
            content: item.lastMessage.content,
            type: item.lastMessage.type,
            senderId: item.lastMessage.senderId,
            senderName: item.lastMessage.senderName,
            senderAvatar: '',
            timestamp: item.lastMessage.timestamp,
            statusInfo: {
              status: 'delivered',
              timestamp: item.lastMessage.timestamp,
            },
            isRead: item.lastMessage.isRead,
            isDelivered: true,
          },
          lastMessageTime: item.lastMessage.timestamp,
          unreadCount: { [currentUser.id]: item.unreadCount },
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          isArchived: item.isArchived,
          isPinned: item.isPinned,
          isMuted: item.isMuted,
        }));

        setChats(convertedChats);
        setIsLoading(false);
      }
    );

    // Also subscribe to Firebase chats for backward compatibility
    const unsubscribe = realTimeMessagingService.subscribeToUserChats(
      currentUser.id,
      async (firebaseChats: RealChat[]) => {
        // Merge Firebase chats with offline chats
        try {
          if (offlineDatabaseService.isReady()) {
            const db = offlineDatabaseService.getDatabase();
            const offlineChats = await db.getAllAsync(`
              SELECT * FROM chats ORDER BY updatedAt DESC
            `);

            const convertedOfflineChats: RealChat[] = offlineChats.map((chat: any) => ({
              id: chat.id,
              participants: [currentUser.id, chat.partnerId || ''],
              participantNames: {
                [currentUser.id]: currentUser.displayName || currentUser.name || 'You',
                [chat.partnerId || '']: chat.partnerName || 'Unknown'
              },
              participantAvatars: {
                [currentUser.id]: currentUser.photoURL || currentUser.avatar || '',
                [chat.partnerId || '']: chat.partnerAvatar || ''
              },
              isGroup: false,
              groupName: undefined,
              groupAvatar: undefined,
              lastMessage: chat.lastMessage ? {
                id: 'last',
                chatId: chat.id,
                content: chat.lastMessage,
                type: 'text' as const,
                senderId: chat.lastMessageSender || '',
                senderName: '',
                senderAvatar: '',
                timestamp: new Date(chat.lastMessageTime || Date.now()),
                statusInfo: {
                  status: 'delivered',
                  timestamp: new Date(chat.lastMessageTime || Date.now()),
                },
                isRead: false,
                isDelivered: true,
              } : undefined,
              lastMessageTime: new Date(chat.lastMessageTime || Date.now()),
              unreadCount: { [currentUser.id]: chat.unreadCount || 0 },
              createdAt: new Date(chat.createdAt),
              updatedAt: new Date(chat.updatedAt),
            }));

            // Merge and deduplicate chats
            const allChats = [...firebaseChats];
            convertedOfflineChats.forEach(offlineChat => {
              if (!firebaseChats.find(fc => fc.id === offlineChat.id)) {
                allChats.push(offlineChat);
              }
            });

            setChats(allChats);
            console.log('📨 Merged chats - Firebase:', firebaseChats.length, 'Offline:', convertedOfflineChats.length, 'Total:', allChats.length);
          } else {
            setChats(firebaseChats);
          }
        } catch (error) {
          console.warn('⚠️ Failed to merge offline chats:', error);
          setChats(firebaseChats);
        }

        setIsLoading(false);
      }
    );

    return () => {
      unsubscribe();
      chatsListUnsubscribe();
    };
  }, [currentUser?.id]); // FIXED: Proper dependency

  // Handle refresh - useCallback for performance
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    // Refresh happens automatically via real-time subscription
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  // Navigate to chat - useCallback for performance
  const openChat = useCallback(async (chat: RealChat) => {
    if (!currentUser) return;

    try {
      // Use router for direct navigation
      if (chat.isGroup) {
        // Include both groupId and groupName for proper navigation
        router.push({
          pathname: '/group-chat',
          params: {
            groupId: chat.id,
            groupName: chat.groupName || 'Unknown Group',
            groupAvatar: chat.groupAvatar || '',
          }
        });
      } else {
        router.push(`/chat/${chat.id}`);
      }
    } catch (error) {
      // Fallback to navigationService if router fails
      navigationService.openChat(chat.id, chat.isGroup, chat.groupName);
    }
  }, [currentUser, router]);

  // Start new chat - useCallback for performance
  const startNewChat = useCallback(() => {
    try {
      router.push('/contacts');
    } catch (error) {
      // Fallback to navigationService if router fails
      navigationService.openContacts();
    }
  }, [router]);

  // Toggle search functionality - now expands in header
  const toggleSearch = useCallback(() => {
    setIsSearchExpanded(prev => !prev);
    if (isSearchExpanded) {
      setSearchQuery('');
    }
  }, [isSearchExpanded]);

  // Enhanced Chat Management Functions
  const toggleChatSelection = useCallback((chatId: string) => {
    setSelectedChats(prev => {
      const isSelected = prev.includes(chatId);
      if (isSelected) {
        const newSelection = prev.filter(id => id !== chatId);
        if (newSelection.length === 0) {
          setIsSelectionMode(false);
        }
        return newSelection;
      } else {
        return [...prev, chatId];
      }
    });
  }, []);

  const enterSelectionMode = useCallback((chatId: string) => {
    setIsSelectionMode(true);
    setSelectedChats([chatId]);
  }, []);

  const exitSelectionMode = useCallback(() => {
    setIsSelectionMode(false);
    setSelectedChats([]);
  }, []);

  const pinSelectedChats = useCallback(async () => {
    try {
      setPinnedChats(prev => [...new Set([...prev, ...selectedChats])]);
      // Update in Firebase
      // await realTimeMessagingService.pinChats(currentUser?.id, selectedChats);
      exitSelectionMode();
      Alert.alert('Success', 'Chats pinned successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to pin chats');
    }
  }, [selectedChats, exitSelectionMode]);

  const archiveSelectedChats = useCallback(async () => {
    if (!currentUser?.id) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    try {
      console.log('📦 [DEBUG] Archiving chats:', selectedChats);

      // Update local state first for immediate UI feedback
      setArchivedChats(prev => [...new Set([...prev, ...selectedChats])]);

      // Archive chats in offline database (the actual data source)
      await offlineDatabaseService.initialize();
      if (offlineDatabaseService.isReady()) {
        const db = offlineDatabaseService.getDatabase();

        for (const chatId of selectedChats) {
          await db.runAsync(`
            UPDATE chats SET
              isArchived = 1,
              updatedAt = ?
            WHERE id = ?
          `, [Date.now(), chatId]);

          console.log('✅ [DEBUG] Archived chat in offline DB:', chatId);
        }
      }

      // Also archive using the local chat management service
      try {
        await localChatManagementService.archiveChats(selectedChats);
      } catch (localError) {
        console.warn('⚠️ Local chat management archive failed:', localError);
      }

      // Also update in Firebase if online
      for (const chatId of selectedChats) {
        try {
          await realTimeMessagingService.archiveChat(chatId, currentUser.id);
        } catch (firebaseError) {
          console.warn('⚠️ Firebase archive failed for chat:', chatId, firebaseError);
          // Continue with local archiving even if Firebase fails
        }
      }

      exitSelectionMode();
      Alert.alert('Success', `${selectedChats.length} chat(s) archived successfully`);

      console.log('✅ [DEBUG] Successfully archived chats:', selectedChats);
    } catch (error) {
      console.error('❌ [DEBUG] Failed to archive chats:', error);
      Alert.alert('Error', 'Failed to archive chats');
    }
  }, [selectedChats, exitSelectionMode, currentUser?.id]);

  const deleteSelectedChats = useCallback(async () => {
    Alert.alert(
      'Delete Chats',
      `Are you sure you want to delete ${selectedChats.length} chat(s)?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Delete from Firebase
              // await realTimeMessagingService.deleteChats(currentUser?.id, selectedChats);
              setChats(prev => prev.filter(chat => !selectedChats.includes(chat.id)));
              exitSelectionMode();
              Alert.alert('Success', 'Chats deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete chats');
            }
          }
        }
      ]
    );
  }, [selectedChats, exitSelectionMode]);

  const markSelectedAsRead = useCallback(async () => {
    try {
      // Mark as read in Firebase
      // await realTimeMessagingService.markChatsAsRead(currentUser?.id, selectedChats);
      setChats(prev =>
        prev.map(chat =>
          selectedChats.includes(chat.id)
            ? { ...chat, unreadCount: { ...chat.unreadCount, [currentUser?.id || '']: 0 } }
            : chat
        )
      );
      exitSelectionMode();
      Alert.alert('Success', 'Chats marked as read');
    } catch (error) {
      Alert.alert('Error', 'Failed to mark chats as read');
    }
  }, [selectedChats, currentUser?.id, exitSelectionMode]);

  // Enhanced Filtering and Search
  const filteredAndSortedChats = useMemo(() => {
    let filtered = chats;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(chat => {
        const otherUserId = chat.participants.find(id => id !== currentUser?.id);
        const chatName = chat.isGroup
          ? chat.groupName || 'Group Chat'
          : otherUserId
            ? chat.participantNames[otherUserId] || 'Unknown'
            : 'Unknown';

        const lastMessageText = typeof chat.lastMessage === 'string'
          ? chat.lastMessage
          : chat.lastMessage && typeof chat.lastMessage === 'object' && 'text' in chat.lastMessage
            ? String((chat.lastMessage as any).text || '')
            : '';

        return chatName.toLowerCase().includes(query) ||
               lastMessageText.toLowerCase().includes(query);
      });
    }

    // Apply type filter
    switch (filterType) {
      case 'unread':
        filtered = filtered.filter(chat =>
          (chat.unreadCount[currentUser?.id || ''] || 0) > 0
        );
        break;
      default:
        // 'all' - exclude archived chats from main view
        filtered = filtered.filter(chat => !archivedChats.includes(chat.id));
        break;
    }

    // Sort: pinned chats first, then by last message time
    return filtered.sort((a, b) => {
      const aIsPinned = pinnedChats.includes(a.id);
      const bIsPinned = pinnedChats.includes(b.id);

      if (aIsPinned && !bIsPinned) return -1;
      if (!aIsPinned && bIsPinned) return 1;

      // Both pinned or both not pinned - sort by time
      return new Date(b.lastMessageTime).getTime() - new Date(a.lastMessageTime).getTime();
    });
  }, [chats, searchQuery, filterType, currentUser?.id, archivedChats, pinnedChats]);



  // Handle chat long press for selection mode
  const handleChatLongPress = useCallback((chat: RealChat) => {
    if (isSelectionMode) {
      toggleChatSelection(chat.id);
    } else {
      enterSelectionMode(chat.id);
    }
  }, [isSelectionMode, toggleChatSelection, enterSelectionMode]);

  // Render chat item - useCallback for FlatList performance
  const renderChatItem = useCallback(({ item, index }: { item: RealChat; index: number }) => {
    if (!currentUser) return null;

    const isSelected = selectedChats.includes(item.id);

    return (
      <ChatItem
        chat={item}
        currentUserId={currentUser.id}
        onPress={openChat}
        onLongPress={handleChatLongPress}
        isSelected={isSelected}
        isSelectionMode={isSelectionMode}
        index={index}
      />
    );
  }, [currentUser, openChat, handleChatLongPress, selectedChats, isSelectionMode]);

  // Key extractor - useCallback for FlatList performance
  const keyExtractor = useCallback((item: RealChat) => item.id, []);

  // Render empty state
  const renderEmptyState = useCallback(() => (
    <View style={styles.emptyState}>
      <Ionicons name="chatbubbles-outline" size={80} color="#9CA3AF" />
      <Text style={styles.emptyTitle}>No conversations yet</Text>
      <Text style={styles.emptySubtitle}>
        Start a conversation with your contacts
      </Text>
      <TouchableOpacity style={styles.startChatButton} onPress={startNewChat}>
        <Ionicons name="add" size={24} color="#FFFFFF" />
        <Text style={styles.startChatText}>Start New Chat</Text>
      </TouchableOpacity>
    </View>
  ), [startNewChat]);

  return (
    <ErrorBoundary>
      <View style={styles.container}>
        <StatusBar style="light" />

        {/* Beautiful animated wallpaper */}
        <IraChatWallpaper variant="auto" animated={true} />

        <MainHeader
          isSearchExpanded={isSearchExpanded}
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          onSearchClose={() => {
            setIsSearchExpanded(false);
            setSearchQuery('');
          }}
        />

        {/* Search now handled in MainHeader */}

        {/* Filter Bar Container */}
        {!isSelectionMode && (
          <View style={styles.filterBarContainer}>
            {/* Modern Filter Tabs with Animated Indicator */}
            <View style={styles.modernFilterContainer}>
              <View style={styles.filterTabsWrapper}>
                <TouchableOpacity
                  onPress={() => switchToFilter('all')}
                  style={styles.modernFilterTab}
                >
                  <Text style={[
                    styles.modernFilterText,
                    filterType === 'all' && styles.modernActiveFilterText
                  ]}>
                    All
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => switchToFilter('unread')}
                  style={styles.modernFilterTab}
                >
                  <Text style={[
                    styles.modernFilterText,
                    filterType === 'unread' && styles.modernActiveFilterText
                  ]}>
                    Unread
                  </Text>
                </TouchableOpacity>
              </View>


            </View>

            {/* Search Button - Expands in header */}
            <TouchableOpacity onPress={toggleSearch} style={styles.searchToggleButton}>
              <Ionicons name="search" size={20} color={IRACHAT_COLORS.primary} />
            </TouchableOpacity>
          </View>
        )}

        {/* Selection Mode Toolbar */}
        {isSelectionMode && (
          <View style={styles.selectionToolbar}>
            <View style={styles.selectionInfo}>
              <Text style={styles.selectionCount}>
                {selectedChats.length} selected
              </Text>
            </View>
            <View style={styles.selectionActions}>
              <TouchableOpacity onPress={pinSelectedChats} style={styles.actionButton}>
                <Ionicons name="pin" size={20} color={IRACHAT_COLORS.primary} />
              </TouchableOpacity>
              <TouchableOpacity onPress={markSelectedAsRead} style={styles.actionButton}>
                <Ionicons name="checkmark-done" size={20} color={IRACHAT_COLORS.primary} />
              </TouchableOpacity>
              <TouchableOpacity onPress={archiveSelectedChats} style={styles.actionButton}>
                <Ionicons name="archive" size={20} color={IRACHAT_COLORS.primary} />
              </TouchableOpacity>
              <TouchableOpacity onPress={deleteSelectedChats} style={styles.actionButton}>
                <Ionicons name="trash" size={20} color={IRACHAT_COLORS.error} />
              </TouchableOpacity>
              <TouchableOpacity onPress={exitSelectionMode} style={styles.actionButton}>
                <Ionicons name="close" size={20} color={IRACHAT_COLORS.textMuted} />
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Swipeable Content */}
        <GestureHandlerRootView style={styles.gestureContainer}>
          <GestureDetector gesture={Gesture.Simultaneous(panGesture)}>
            <ReAnimated.View style={[styles.contentWrapper, animatedContentStyle]}>
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
                  <Text style={styles.loadingText}>Loading chats...</Text>
                </View>
              ) : (
                <FlatList
                  data={filteredAndSortedChats}
                  renderItem={renderChatItem}
                  keyExtractor={keyExtractor}
                  contentContainerStyle={filteredAndSortedChats.length === 0 ? styles.emptyContainer : styles.chatsList}
                  ListEmptyComponent={renderEmptyState}
                  refreshControl={
                    <RefreshControl
                      refreshing={refreshing}
                      onRefresh={handleRefresh}
                      colors={[IRACHAT_COLORS.primary]}
                      tintColor={IRACHAT_COLORS.primary}
                    />
                  }
                  showsVerticalScrollIndicator={false}
                  scrollEventThrottle={16}
                  // Performance optimizations
                  removeClippedSubviews={true}
                  maxToRenderPerBatch={10}
                  windowSize={10}
                  getItemLayout={(_, index) => ({
                    length: 80, // Updated height for new design
                    offset: 80 * index,
                    index,
                  })}
                />
              )}
            </ReAnimated.View>
          </GestureDetector>
        </GestureHandlerRootView>

        {/* Beautiful Floating Action Button */}
        <Animated.View style={styles.fabContainer}>
          <LinearGradient
            colors={IRACHAT_COLORS.primaryGradient as any}
            style={styles.fab}
          >
            <TouchableOpacity style={styles.fabButton} onPress={startNewChat}>
              <Ionicons name="add" size={28} color={IRACHAT_COLORS.textOnPrimary} />
            </TouchableOpacity>
          </LinearGradient>
        </Animated.View>
      </View>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
  },
  loadingText: {
    marginTop: SPACING.md,
    fontSize: TYPOGRAPHY.fontSize.base,
    color: '#F9FAFB', // White text for better visibility on dark background
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  emptyContainer: {
    flex: 1,
  },
  chatsList: {
    paddingVertical: SPACING.sm,
    paddingHorizontal: SPACING.sm,
    maxWidth: screenWidth, // Use screen width for responsive chat list
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
    backgroundColor: 'transparent',
    minHeight: screenHeight * 0.6, // Use screen height for responsive empty state
  },
  emptyTitle: {
    fontSize: TYPOGRAPHY.fontSize['2xl'],
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: '#F9FAFB', // White text for better visibility on dark background
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: '#9CA3AF', // Light gray for better visibility on dark background
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: TYPOGRAPHY.lineHeight.relaxed * TYPOGRAPHY.fontSize.base,
  },
  startChatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.primary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.full,
    ...SHADOWS.md,
  },
  startChatText: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    marginLeft: SPACING.sm,
  },
  chatItemContainer: {
    marginHorizontal: SPACING.sm,
    marginVertical: SPACING.xs,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.surface,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
    height: 80,
    ...SHADOWS.sm,
  },
  selectedChatItem: {
    backgroundColor: IRACHAT_COLORS.primaryLight,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.primary,
  },
  selectionModeChatItem: {
    marginLeft: SPACING.sm,
  },
  selectionIndicator: {
    marginRight: SPACING.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: SPACING.md,
  },
  chatAvatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.primaryLight,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: IRACHAT_COLORS.online,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.surface,
  },
  chatContent: {
    flex: 1,
  },
  chatHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  chatName: {
    fontSize: TYPOGRAPHY.fontSize.base,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
    color: IRACHAT_COLORS.text,
    flex: 1,
  },
  chatTime: {
    fontSize: TYPOGRAPHY.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },
  chatFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  lastMessage: {
    flex: 1,
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    marginRight: SPACING.sm,
  },
  unreadBadge: {
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: BORDER_RADIUS.full,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xs,
    ...SHADOWS.sm,
  },
  unreadText: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontSize: TYPOGRAPHY.fontSize.xs,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
  },
  fabContainer: {
    position: 'absolute',
    bottom: SPACING.xl,
    right: SPACING.lg,
  },
  fab: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.lg,
  },
  fabButton: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 32,
  },
  // Test User FAB Styles
  testUserFabContainer: {
    position: 'absolute',
    bottom: SPACING.xl + 80, // Position above the main FAB
    right: SPACING.lg,
  },
  testUserFab: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FF6B35', // Orange color to distinguish from main FAB
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.md,
    elevation: 4,
  },
  // Search Styles
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match conversations area background
    // Removed border to eliminate white line
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.md,
    marginRight: SPACING.sm,
  },
  searchIcon: {
    marginRight: SPACING.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.text,
    paddingVertical: SPACING.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  clearButton: {
    padding: SPACING.xs,
  },
  cancelButton: {
    padding: SPACING.sm,
    justifyContent: 'center',
    alignItems: 'center',
    width: 40, // Fixed width for icon
    height: 40, // Fixed height for icon
  },
  cancelText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.primary,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },
  // Selection Mode Styles
  selectionToolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: IRACHAT_COLORS.primaryLight,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  selectionInfo: {
    flex: 1,
  },
  selectionCount: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.primary,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
  },
  selectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: SPACING.sm,
    marginLeft: SPACING.xs,
  },
  // Filter Bar Styles
  filterBar: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  filterButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.full,
    marginRight: SPACING.sm,
    backgroundColor: 'transparent',
  },
  activeFilterButton: {
    backgroundColor: IRACHAT_COLORS.primary,
  },
  filterText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
  },
  activeFilterText: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
  },
  searchToggleButton: {
    padding: SPACING.sm,
    justifyContent: 'center',
    alignItems: 'center',
    width: 44,
    height: 36,
  },

  // Modern Filter Styles
  filterBarContainer: {
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match conversations area background
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.xs,
    // Removed border to eliminate white line
    flexDirection: 'row',
    alignItems: 'center',
    height: 44, // Compact height
  },
  modernFilterContainer: {
    flex: 1,
    position: 'relative',
    height: 36,
  },
  filterTabsWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 36,
  },
  modernFilterTab: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: SPACING.xs, // Reduced padding
    height: 36, // Fixed height
  },
  modernFilterText: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    fontWeight: TYPOGRAPHY.fontWeight.medium as any,
    color: '#9CA3AF', // Light gray for better visibility on dark background
    textAlign: 'center',
  },
  modernActiveFilterText: {
    color: '#667eea', // Bright blue for active state
    fontWeight: TYPOGRAPHY.fontWeight.semibold as any,
  },


  // Gesture and Animation Styles
  gestureContainer: {
    flex: 1,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match stories page header background
  },
  contentWrapper: {
    flex: 1,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match stories page header background
  },
});