// 🎵 GLOBAL AUDIO PLAYER COMPONENT
// Shows in chat header when audio is playing in background

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { globalAudioManager, AudioPlaybackState } from '../services/globalAudioManager';

// IraChat Colors
const COLORS = {
  primary: '#25D366',
  primaryDark: '#128C7E',
  background: '#111B21',
  surface: '#1F2937',
  text: '#E5E7EB',
  textSecondary: '#9CA3AF',
  textMuted: '#6B7280',
};

interface GlobalAudioPlayerProps {
  visible: boolean;
}

export const GlobalAudioPlayer: React.FC<GlobalAudioPlayerProps> = ({ visible }) => {
  const [audioState, setAudioState] = useState<AudioPlaybackState>(globalAudioManager.getState());
  const [slideAnim] = useState(new Animated.Value(visible ? 0 : -60));

  useEffect(() => {
    const listener = (state: AudioPlaybackState) => {
      setAudioState(state);
    };

    globalAudioManager.addListener(listener);
    return () => globalAudioManager.removeListener(listener);
  }, []);

  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: visible && audioState.currentTrack ? 0 : -60,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [visible, audioState.currentTrack, slideAnim]);

  const handlePlayPause = async () => {
    await globalAudioManager.togglePlayPause();
  };

  const handleStop = async () => {
    await globalAudioManager.stop();
  };

  if (!audioState.currentTrack) {
    return null;
  }

  const progressPercentage = audioState.duration > 0 
    ? (audioState.position / audioState.duration) * 100 
    : 0;

  return (
    <Animated.View 
      style={[
        styles.container,
        { transform: [{ translateY: slideAnim }] }
      ]}
    >
      <View style={styles.content}>
        {/* Track Info */}
        <View style={styles.trackInfo}>
          {audioState.currentTrack.thumbnailUrl ? (
            <Image 
              source={{ uri: audioState.currentTrack.thumbnailUrl }} 
              style={styles.thumbnail}
            />
          ) : (
            <View style={styles.thumbnailPlaceholder}>
              <Ionicons 
                name={
                  audioState.currentTrack.source === 'voice' ? 'mic' :
                  audioState.currentTrack.source === 'music' ? 'musical-notes' :
                  'volume-high'
                } 
                size={16} 
                color={COLORS.primary} 
              />
            </View>
          )}
          
          <View style={styles.textInfo}>
            <Text style={styles.title} numberOfLines={1}>
              {audioState.currentTrack.title}
            </Text>
            <Text style={styles.artist} numberOfLines={1}>
              {audioState.currentTrack.artist}
            </Text>
          </View>
        </View>

        {/* Controls */}
        <View style={styles.controls}>
          <TouchableOpacity
            style={styles.controlButton}
            onPress={handlePlayPause}
            disabled={audioState.isLoading}
          >
            <Ionicons 
              name={audioState.isLoading ? 'hourglass' : audioState.isPlaying ? 'pause' : 'play'} 
              size={20} 
              color={COLORS.text} 
            />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.controlButton}
            onPress={handleStop}
          >
            <Ionicons name="stop" size={18} color={COLORS.textSecondary} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground}>
          <View
            style={[
              styles.progressBar,
              { width: `${progressPercentage}%` }
            ]}
          />
        </View>

        <View style={styles.timeContainer}>
          <Text style={styles.timeText}>
            {globalAudioManager.formatTime(audioState.position)}
          </Text>
          <Text style={styles.progressPercentage}>
            {Math.round(progressPercentage)}%
          </Text>
          <Text style={styles.timeText}>
            {globalAudioManager.formatTime(audioState.duration)}
          </Text>
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.background,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  trackInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  thumbnail: {
    width: 32,
    height: 32,
    borderRadius: 4,
    marginRight: 12,
  },
  thumbnailPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 4,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textInfo: {
    flex: 1,
  },
  title: {
    color: COLORS.text,
    fontSize: 14,
    fontWeight: '600',
  },
  artist: {
    color: COLORS.textSecondary,
    fontSize: 12,
    marginTop: 2,
  },
  controls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  controlButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBackground: {
    height: 2,
    backgroundColor: COLORS.background,
    borderRadius: 1,
  },
  progressBar: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 1,
  },
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  timeText: {
    color: COLORS.textMuted,
    fontSize: 10,
  },
  progressPercentage: {
    color: COLORS.primary,
    fontSize: 10,
    fontWeight: '600',
  },
});
