/**
 * Enhanced Chat Component with Full Media Integration
 * Demonstrates how to use all the implemented media features
 * This is a complete example showing real functionality
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Alert,
  ActionSheetIOS,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useMediaSystem, useMediaViewer, useDownloadedMediaUI } from '../hooks/useMediaSystem';
import { ModernMediaViewer } from './ModernMediaViewer';
import { ModernDownloadedMediaUI } from './ModernDownloadedMediaUI';

interface MediaMessage {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document';
  uri: string;
  fileName: string;
  fileSize?: number;
  caption?: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
}

interface EnhancedChatWithMediaProps {
  chatId: string;
  chatName: string;
  userId: string;
  messages: MediaMessage[];
}

export const EnhancedChatWithMedia: React.FC<EnhancedChatWithMediaProps> = ({
  chatId,
  chatName,
  userId,
  messages,
}) => {
  const mediaSystem = useMediaSystem(userId);
  const mediaViewer = useMediaViewer();
  const downloadedMediaUI = useDownloadedMediaUI();
  const [selectedMessages, setSelectedMessages] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);

  // Filter media messages
  const mediaMessages = messages.filter(msg => 
    ['image', 'video', 'audio', 'document'].includes(msg.type)
  );

  const handleMediaPress = (message: MediaMessage, index: number) => {
    if (isSelectionMode) {
      toggleSelection(message.id);
      return;
    }

    // Convert to media viewer format
    const viewerItems = mediaMessages.map(msg => ({
      id: msg.id,
      uri: msg.uri,
      type: msg.type,
      caption: msg.caption,
      fileName: msg.fileName,
      fileSize: msg.fileSize,
    }));

    mediaViewer.showViewer(viewerItems, index);
  };

  const handleMediaLongPress = (message: MediaMessage) => {
    if (Platform.OS === 'ios') {
      showIOSActionSheet(message);
    } else {
      showAndroidContextMenu(message);
    }
  };

  const showIOSActionSheet = (message: MediaMessage) => {
    const options = [
      'Save for Remembrance',
      'Share',
      'Download',
      'View in Chat',
      'Forward',
      'Delete',
      'Cancel',
    ];

    ActionSheetIOS.showActionSheetWithOptions(
      {
        options,
        cancelButtonIndex: options.length - 1,
        destructiveButtonIndex: options.length - 2,
        title: `${message.fileName}`,
      },
      (buttonIndex) => {
        handleMediaAction(message, options[buttonIndex]);
      }
    );
  };

  const showAndroidContextMenu = (message: MediaMessage) => {
    // For Android, we'll show a custom modal or use Alert
    Alert.alert(
      message.fileName,
      'Choose an action',
      [
        { text: 'Save for Remembrance', onPress: () => handleMediaAction(message, 'Save for Remembrance') },
        { text: 'Share', onPress: () => handleMediaAction(message, 'Share') },
        { text: 'Download', onPress: () => handleMediaAction(message, 'Download') },
        { text: 'Forward', onPress: () => handleMediaAction(message, 'Forward') },
        { text: 'Delete', style: 'destructive', onPress: () => handleMediaAction(message, 'Delete') },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const handleMediaAction = async (message: MediaMessage, action: string) => {
    switch (action) {
      case 'Save for Remembrance':
        await saveForRemembrance(message);
        break;
      case 'Share':
        await shareMedia(message);
        break;
      case 'Download':
        await downloadMedia(message);
        break;
      case 'View in Chat':
        // Already in chat, could scroll to message
        break;
      case 'Forward':
        await forwardMedia(message);
        break;
      case 'Delete':
        await deleteMedia(message);
        break;
    }
  };

  const saveForRemembrance = async (message: MediaMessage) => {
    const result = await mediaSystem.saveForRemembrance(
      message.uri,
      message.type,
      message.fileName,
      {
        chatId,
        chatName,
        senderId: message.senderId,
        senderName: message.senderName,
        messageId: message.id,
      },
      {
        caption: message.caption,
        tags: ['chat', chatName.toLowerCase()],
      }
    );

    if (result.success) {
      Alert.alert('Saved!', 'Media saved to your remembrance collection');
    } else {
      Alert.alert('Error', result.error || 'Failed to save media');
    }
  };

  const shareMedia = async (message: MediaMessage) => {
    const externalApps = mediaSystem.getExternalAppsForType(message.type);
    
    if (externalApps.length === 0) {
      Alert.alert('No Apps', 'No compatible apps found for sharing this media type');
      return;
    }

    // Show app picker (simplified - in real app, show custom picker)
    const appNames = externalApps.map(app => app.name);
    appNames.push('Cancel');

    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options: appNames,
          cancelButtonIndex: appNames.length - 1,
          title: 'Share with...',
        },
        async (buttonIndex) => {
          if (buttonIndex < externalApps.length) {
            const selectedApp = externalApps[buttonIndex];
            const result = await mediaSystem.shareToExternalApp(selectedApp.id, {
              type: message.type,
              uri: message.uri,
              text: message.caption,
            });

            if (!result.success) {
              Alert.alert('Error', result.error || 'Failed to share');
            }
          }
        }
      );
    }
  };

  const downloadMedia = async (message: MediaMessage) => {
    const result = await mediaSystem.performBulkOperation('download', [message]);
    
    if (result.success) {
      Alert.alert('Downloaded!', 'Media saved to your device gallery');
    } else {
      Alert.alert('Error', 'Failed to download media');
    }
  };

  const forwardMedia = async (message: MediaMessage) => {
    // In a real app, this would show a chat picker
    Alert.alert('Forward', 'Chat picker would be shown here');
  };

  const deleteMedia = async (message: MediaMessage) => {
    Alert.alert(
      'Delete Media',
      'Are you sure you want to delete this media?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            // Delete logic would go here
            Alert.alert('Deleted', 'Media deleted successfully');
          },
        },
      ]
    );
  };

  const toggleSelection = (messageId: string) => {
    const newSelected = new Set(selectedMessages);
    if (newSelected.has(messageId)) {
      newSelected.delete(messageId);
    } else {
      newSelected.add(messageId);
    }
    setSelectedMessages(newSelected);

    if (newSelected.size === 0) {
      setIsSelectionMode(false);
    }
  };

  const handleBulkAction = async (action: 'download' | 'share' | 'delete' | 'remember') => {
    const selectedMediaMessages = mediaMessages.filter(msg => selectedMessages.has(msg.id));
    
    if (selectedMediaMessages.length === 0) {
      Alert.alert('No Selection', 'Please select media items first');
      return;
    }

    const result = await mediaSystem.performBulkOperation(action, selectedMediaMessages);
    
    const message = result.success 
      ? `Successfully processed ${result.processed} items`
      : `Processed ${result.processed} items, ${result.failed} failed`;
    
    Alert.alert(result.success ? 'Success' : 'Partial Success', message);
    
    setSelectedMessages(new Set());
    setIsSelectionMode(false);
  };

  const renderMediaMessage = (message: MediaMessage, index: number) => {
    const isSelected = selectedMessages.has(message.id);

    return (
      <TouchableOpacity
        key={message.id}
        style={{
          margin: 5,
          borderRadius: 8,
          overflow: 'hidden',
          opacity: isSelected ? 0.7 : 1,
          borderWidth: isSelected ? 3 : 0,
          borderColor: '#007AFF',
        }}
        onPress={() => handleMediaPress(message, index)}
        onLongPress={() => handleMediaLongPress(message)}
      >
        {message.type === 'image' ? (
          <Image
            source={{ uri: message.uri }}
            style={{ width: 280, height: 280 }}
            resizeMode="cover"
          />
        ) : (
          <View
            style={{
              width: 280,
              height: 280,
              backgroundColor: '#f0f0f0',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Ionicons 
              name={getMediaIcon(message.type)} 
              size={60} 
              color="#666" 
            />
            <Text style={{ marginTop: 10, fontSize: 12, color: '#666' }}>
              {message.type.toUpperCase()}
            </Text>
          </View>
        )}

        {message.caption && (
          <View style={{ padding: 8, backgroundColor: 'rgba(0,0,0,0.7)' }}>
            <Text style={{ color: 'white', fontSize: 12 }}>
              {message.caption}
            </Text>
          </View>
        )}

        {isSelectionMode && (
          <View
            style={{
              position: 'absolute',
              top: 8,
              right: 8,
              width: 24,
              height: 24,
              borderRadius: 12,
              backgroundColor: isSelected ? '#007AFF' : 'rgba(0,0,0,0.5)',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {isSelected && <Ionicons name="checkmark" size={16} color="white" />}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const getMediaIcon = (type: string) => {
    switch (type) {
      case 'video': return 'videocam';
      case 'audio': return 'musical-notes';
      case 'document': return 'document-text';
      default: return 'image';
    }
  };

  return (
    <View style={{ flex: 1 }}>
      {/* Header with media actions */}
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: 15,
          backgroundColor: '#f8f8f8',
          borderBottomWidth: 1,
          borderBottomColor: '#e0e0e0',
        }}
      >
        <Text style={{ fontSize: 18, fontWeight: '600' }}>
          {chatName}
        </Text>

        <View style={{ flexDirection: 'row', gap: 15 }}>
          <TouchableOpacity onPress={downloadedMediaUI.showUI}>
            <Ionicons name="download" size={24} color="#007AFF" />
          </TouchableOpacity>
          
          <TouchableOpacity onPress={() => setIsSelectionMode(!isSelectionMode)}>
            <Ionicons 
              name={isSelectionMode ? "close" : "checkmark-circle-outline"} 
              size={24} 
              color="#007AFF" 
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Selection toolbar */}
      {isSelectionMode && (
        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'space-around',
            alignItems: 'center',
            padding: 10,
            backgroundColor: '#f0f8ff',
            borderBottomWidth: 1,
            borderBottomColor: '#e0e0e0',
          }}
        >
          <Text style={{ fontSize: 16, fontWeight: '500' }}>
            {selectedMessages.size} selected
          </Text>

          <TouchableOpacity onPress={() => handleBulkAction('download')}>
            <Ionicons name="download-outline" size={24} color="#007AFF" />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => handleBulkAction('share')}>
            <Ionicons name="share-outline" size={24} color="#007AFF" />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => handleBulkAction('remember')}>
            <Ionicons name="heart-outline" size={24} color="#007AFF" />
          </TouchableOpacity>

          <TouchableOpacity onPress={() => handleBulkAction('delete')}>
            <Ionicons name="trash-outline" size={24} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      )}

      {/* Media grid */}
      <View style={{ flex: 1, padding: 10 }}>
        {mediaMessages.length === 0 ? (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
            <Ionicons name="images-outline" size={80} color="#ccc" />
            <Text style={{ fontSize: 18, color: '#666', marginTop: 20 }}>
              No media in this chat
            </Text>
          </View>
        ) : (
          <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
            {mediaMessages.map((message, index) => renderMediaMessage(message, index))}
          </View>
        )}
      </View>

      {/* System status indicator */}
      {mediaSystem.isInitialized && (
        <View
          style={{
            position: 'absolute',
            bottom: 20,
            right: 20,
            backgroundColor: 'rgba(0, 122, 255, 0.1)',
            borderRadius: 20,
            padding: 8,
          }}
        >
          <Ionicons name="checkmark-circle" size={16} color="#007AFF" />
        </View>
      )}

      {/* Media Viewer */}
      <ModernMediaViewer
        visible={mediaViewer.isVisible}
        mediaItems={mediaViewer.mediaItems}
        initialIndex={mediaViewer.initialIndex}
        onClose={mediaViewer.hideViewer}
        onSaveForRemembrance={async (item) => {
          const message = mediaMessages.find(msg => msg.id === item.id);
          if (message) {
            await saveForRemembrance(message);
          }
        }}
        onShare={async (item) => {
          const message = mediaMessages.find(msg => msg.id === item.id);
          if (message) {
            await shareMedia(message);
          }
        }}
        canDelete={true}
        showRememberanceOption={true}
      />

      {/* Downloaded Media UI */}
      <ModernDownloadedMediaUI
        visible={downloadedMediaUI.isVisible}
        onClose={downloadedMediaUI.hideUI}
      />
    </View>
  );
};
