// 🎵 AUDIO MESSAGE PLAYER WITH WAVEFORM
// Complete audio playback functionality for voice and audio messages

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { Audio, AVPlaybackStatus } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';
import { audioManager } from '../services/audioManager';

interface AudioMessagePlayerProps {
  audioUri: string;
  isOwnMessage: boolean;
  theme: any;
  title?: string;
  duration?: number;
}

export const AudioMessagePlayer: React.FC<AudioMessagePlayerProps> = ({
  audioUri,
  isOwnMessage,
  theme,
  duration,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [playbackDuration, setPlaybackDuration] = useState(duration || 0);
  const [playbackRate, setPlaybackRate] = useState(1.0);
  const [showControls, setShowControls] = useState(false);

  // Format time in MM:SS
  const formatTime = (milliseconds: number) => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle playback status updates
  const onPlaybackStatusUpdate = (status: any) => {
    if (status.isLoaded) {
      setPlaybackPosition(status.positionMillis || 0);
      setPlaybackDuration(status.durationMillis || duration || 0);

      if (status.didJustFinish) {
        setIsPlaying(false);
        setPlaybackPosition(0);
      }
    }
  };

  // Toggle play/pause using global audio manager
  const togglePlayback = async () => {
    try {
      setIsLoading(true);

      if (isPlaying) {
        await audioManager.pauseCurrentAudio();
        setIsPlaying(false);
      } else {
        const sound = await audioManager.playAudio(audioUri, onPlaybackStatusUpdate);
        if (sound) {
          setIsPlaying(true);
        }
      }
    } catch (error) {
      console.error('❌ Error toggling playback:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Seek to position
  const seekTo = async (positionMillis: number) => {
    await audioManager.seekAudio(positionMillis);
    setPlaybackPosition(positionMillis);
  };

  // Change playback speed
  const changePlaybackRate = async (rate: number) => {
    await audioManager.setPlaybackRate(rate);
    setPlaybackRate(rate);
  };

  // Handle seek bar press
  const handleSeekBarPress = (event: any) => {
    const { locationX } = event.nativeEvent;
    const seekBarWidth = 120; // Approximate width of seek bar
    const percentage = locationX / seekBarWidth;
    const newPosition = percentage * playbackDuration;
    seekTo(Math.max(0, Math.min(newPosition, playbackDuration)));
  };

  // Check if this audio is currently playing globally
  useEffect(() => {
    const currentSound = audioManager.getCurrentSound();
    if (!currentSound) {
      setIsPlaying(false);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Don't cleanup here as audioManager handles it globally
    };
  }, []);

  // Calculate progress percentage
  const progressPercentage = playbackDuration > 0
    ? (playbackPosition / playbackDuration) * 100
    : 0;

  return (
    <View style={styles.audioContainer}>
      {/* Main Audio Controls */}
      <TouchableOpacity
        style={styles.mainControls}
        onPress={() => setShowControls(!showControls)}
        activeOpacity={0.7}
      >
        {/* Play/Pause Button */}
        <TouchableOpacity
          style={[
            styles.audioPlayButton,
            { backgroundColor: isOwnMessage ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.1)' }
          ]}
          onPress={togglePlayback}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator size="small" color={theme.sendButtonActive} />
          ) : (
            <Ionicons
              name={isPlaying ? 'pause' : 'play'}
              size={16}
              color={theme.sendButtonActive}
            />
          )}
        </TouchableOpacity>

        {/* Seekable Waveform */}
        <TouchableOpacity
          style={styles.audioWaveform}
          onPress={handleSeekBarPress}
          activeOpacity={1}
        >
          {[1, 2, 3, 4, 5, 6, 7, 8].map((_, index) => (
            <View
              key={index}
              style={[
                styles.waveformBar,
                {
                  backgroundColor: index < (progressPercentage / 12.5)
                    ? theme.sendButtonActive
                    : isOwnMessage
                      ? 'rgba(255,255,255,0.3)'
                      : 'rgba(0,0,0,0.2)',
                  height: Math.random() * 15 + 8,
                },
              ]}
            />
          ))}
        </TouchableOpacity>

        {/* Duration */}
        <Text style={[styles.audioDuration, { color: theme.timeText }]}>
          {isPlaying ? formatTime(playbackPosition) : formatTime(playbackDuration)}
        </Text>
      </TouchableOpacity>

      {/* Advanced Controls (when expanded) */}
      {showControls && (
        <View style={styles.advancedControls}>
          {/* Playback Speed */}
          <View style={styles.speedControls}>
            {[0.5, 1.0, 1.5, 2.0].map((speed) => (
              <TouchableOpacity
                key={speed}
                style={[
                  styles.speedButton,
                  {
                    backgroundColor: playbackRate === speed
                      ? theme.sendButtonActive
                      : 'transparent'
                  }
                ]}
                onPress={() => changePlaybackRate(speed)}
              >
                <Text
                  style={[
                    styles.speedText,
                    {
                      color: playbackRate === speed
                        ? 'white'
                        : theme.timeText
                    }
                  ]}
                >
                  {speed}x
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  audioContainer: {
    paddingVertical: 4,
    paddingHorizontal: 4,
    minWidth: 180,
  },
  mainControls: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
  },
  audioPlayButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  audioWaveform: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 20,
    marginRight: 8,
  },
  waveformBar: {
    width: 3,
    borderRadius: 1.5,
    marginHorizontal: 1,
  },
  audioDuration: {
    fontSize: 12,
    fontWeight: '500',
    minWidth: 35,
    textAlign: 'right',
  },
  advancedControls: {
    paddingTop: 8,
    paddingHorizontal: 4,
  },
  speedControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  speedButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    minWidth: 32,
    alignItems: 'center',
  },
  speedText: {
    fontSize: 10,
    fontWeight: '600',
  },
});

export default AudioMessagePlayer;
