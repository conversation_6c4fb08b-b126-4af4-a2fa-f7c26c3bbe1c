import React from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import { usePersistentAuth } from '../hooks/usePersistentAuth';

interface AuthInitializerProps {
  children: React.ReactNode;
}

export const AuthInitializer: React.FC<AuthInitializerProps> = ({ children }) => {
  const [authState] = usePersistentAuth();

  // Only show loading screen during initial app launch, not for session restoration
  // Don't show if user is already logged in, even if loading/not initialized
  if ((authState.isLoading || !authState.isInitialized) && !authState.isLoggedIn && !authState.user) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#161414ff'
      }}>
        <ActivityIndicator size="large" color="white" />
        <Text style={{
          color: 'white',
          marginTop: 16,
          fontSize: 16,
          fontWeight: '500'
        }}>
          Welcome to IraChat
        </Text>
        <Text style={{
          color: 'rgba(255,255,255,0.8)',
          marginTop: 8,
          fontSize: 14
        }}>
          Setting up your experience...
        </Text>
      </View>
    );
  }

  // Show error screen if initialization failed
  if (authState.error) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
        backgroundColor: '#87CEEB'
      }}>
        <Text style={{
          color: 'white',
          textAlign: 'center',
          marginBottom: 20,
          fontSize: 18,
          fontWeight: '600'
        }}>
          Initialization Error
        </Text>
        <Text style={{
          color: 'rgba(255,255,255,0.9)',
          textAlign: 'center',
          marginBottom: 20,
          fontSize: 16
        }}>
          {authState.error}
        </Text>
        <Text style={{
          textAlign: 'center',
          color: 'rgba(255,255,255,0.7)',
          fontSize: 14
        }}>
          The app will continue with limited functionality.
        </Text>
      </View>
    );
  }

  // Authentication is ready - render children
  console.log('✅ Auth initialization complete - User logged in:', authState.isLoggedIn);
  return <>{children}</>;
};
