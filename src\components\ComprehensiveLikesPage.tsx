// ❤️ COMPREHENSIVE LIKES PAGE
// Beautiful likes page showing all users who liked with animations
// Perfect responsiveness and IraChat sky blue branding

import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Modal,
  StatusBar,
  Animated,
  FlatList,
  Alert,
  Image,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { UserInteraction } from '../types/Update';
import { comprehensiveUpdatesService } from '../services/comprehensiveUpdatesService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
  overlay: 'rgba(0, 0, 0, 0.7)',
  heartColor: '#FF1744',   // Bright Red for hearts
};

interface LikeUser extends UserInteraction {
  // IraChat specific properties - no following/followers system
}

interface ComprehensiveLikesPageProps {
  visible: boolean;
  updateId: string;
  updateTitle?: string;
  updateThumbnail?: string;
  currentUserId: string;
  onClose: () => void;
  onUserPress?: (userId: string) => void;
}

export const ComprehensiveLikesPage: React.FC<ComprehensiveLikesPageProps> = ({
  visible,
  updateId,
  currentUserId,
  onClose,
  onUserPress,
}) => {
  const insets = useSafeAreaInsets();

  // Responsive dimensions
  const isTablet = SCREEN_WIDTH > 768;
  const headerHeight = 60 + insets.top;

  // ==================== STATE MANAGEMENT ====================

  const [likes, setLikes] = useState<LikeUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [totalLikes, setTotalLikes] = useState(0);

  // Animation refs
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const heartAnimations = useRef<{ [key: string]: Animated.Value }>({}).current;

  // ==================== LIFECYCLE METHODS ====================

  useEffect(() => {
    if (visible) {
      loadLikes();
      showModal();
    } else {
      hideModal();
    }
  }, [visible, updateId]);

  // ==================== ANIMATION METHODS ====================

  const showModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideModal = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const animateHeart = useCallback((userId: string) => {
    if (!heartAnimations[userId]) {
      heartAnimations[userId] = new Animated.Value(1);
    }

    Animated.sequence([
      Animated.timing(heartAnimations[userId], {
        toValue: 1.3,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(heartAnimations[userId], {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();
  }, [heartAnimations]);

  // ==================== DATA METHODS ====================

  const loadLikes = useCallback(async () => {
    setIsLoading(true);
    try {
      // Load likes from service - real implementation
      const result = await comprehensiveUpdatesService.getLikes(updateId);

      if (result.success && result.likes) {
        // Transform the likes data to match UserInteraction interface
        const transformedLikes: LikeUser[] = result.likes.map((like: any) => ({
          userId: like.userId,
          userName: like.userName || 'Unknown User',
          userAvatar: like.userAvatar,
          timestamp: like.timestamp instanceof Date ? like.timestamp : new Date(like.timestamp),
          type: 'like' as const,
          metadata: {}
        }));

        setLikes(transformedLikes);
        setTotalLikes(transformedLikes.length);
      } else {
        setLikes([]);
        setTotalLikes(0);
      }
    } catch (error) {
      console.error('❌ Error loading likes:', error);
      Alert.alert('Error', 'Failed to load likes');
    } finally {
      setIsLoading(false);
    }
  }, [updateId]);

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadLikes();
    setIsRefreshing(false);
  }, [loadLikes]);

  const handleUserPress = (userId: string) => {
    onUserPress?.(userId);
  };

  const handleMessageUser = (userId: string) => {
    // Navigate to chat with this user - real implementation
    console.log('🔥 Starting chat with user:', userId);
    // This would typically navigate to the chat screen
    // navigation.navigate('Chat', { userId });
  };

  const getTimeAgo = (timestamp: Date) => {
    const now = Date.now();
    const diff = now - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}d`;
    if (hours > 0) return `${hours}h`;
    if (minutes > 0) return `${minutes}m`;
    return 'now';
  };

  // IraChat specific helper functions

  // ==================== RENDER METHODS ====================

  const renderHeader = () => (
    <View style={[styles.header, { height: headerHeight }]}>
      <LinearGradient
        colors={[COLORS.primary, COLORS.primaryDark]}
        style={styles.headerGradient}
      >
        <View style={[styles.headerContent, { paddingTop: insets.top }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="chevron-down" size={28} color={COLORS.text} />
          </TouchableOpacity>

          <View style={styles.headerInfo}>
            <View style={styles.headerTitleContainer}>
              <Ionicons name="heart" size={24} color={COLORS.heartColor} />
              <Text style={[styles.headerTitle, isTablet && { fontSize: 22 }]}>Likes</Text>
            </View>
            <Text style={styles.headerSubtitle}>
              {totalLikes} {totalLikes === 1 ? 'like' : 'likes'}
            </Text>
          </View>

          <TouchableOpacity style={styles.headerAction}>
            <Ionicons name="ellipsis-horizontal" size={24} color={COLORS.text} />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );

  const renderLikeItem = ({ item: like }: { item: LikeUser }) => (
    <Animated.View
      style={[
        styles.likeContainer,
        {
          opacity: fadeAnim,
          transform: [{
            translateY: fadeAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [30, 0],
            }),
          }],
        },
      ]}
    >
      <TouchableOpacity
        style={[styles.likeItem, isTablet && { paddingHorizontal: 32, paddingVertical: 20 }]}
        onPress={() => handleUserPress(like.userId)}
        activeOpacity={0.7}
      >
        <View style={styles.likeLeft}>
          <TouchableOpacity
            style={styles.avatarContainer}
            onPress={() => {
              handleUserPress(like.userId);
              animateHeart(like.userId);
            }}
            activeOpacity={0.8}
          >
            <Image
              source={{
                uri: like.userAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(like.userName)}&background=87CEEB&color=fff&size=40`
              }}
              style={[styles.avatarImage, isTablet && { width: 48, height: 48, borderRadius: 24 }]}
            />
            <Animated.View
              style={[
                styles.heartBadge,
                {
                  transform: [{
                    scale: heartAnimations[like.userId] || new Animated.Value(1)
                  }]
                }
              ]}
            >
              <Ionicons name="heart" size={10} color={COLORS.heartColor} />
            </Animated.View>
          </TouchableOpacity>

          <View style={styles.userInfo}>
            <Text style={styles.userName}>{like.userName}</Text>
            <View style={styles.userMeta}>
              <Text style={styles.timeAgo}>{getTimeAgo(like.timestamp)}</Text>
            </View>
          </View>
        </View>

        {/* IraChat: Simple message button instead of follow */}
        {like.userId !== currentUserId && (
          <TouchableOpacity
            style={styles.messageButton}
            onPress={() => handleMessageUser(like.userId)}
            activeOpacity={0.8}
          >
            <Ionicons name="chatbubble-outline" size={16} color={COLORS.primary} />
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    </Animated.View>
  );

  if (!visible) return null;

  return (
    <Modal visible={visible} animationType="none" statusBarTranslucent>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      
      <Animated.View style={[styles.container, { opacity: fadeAnim }]}>
        <Animated.View 
          style={[
            styles.modalContent,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {renderHeader()}

          {/* Likes List */}
          <FlatList
            data={likes}
            renderItem={renderLikeItem}
            keyExtractor={(item) => item.userId}
            style={styles.likesList}
            contentContainerStyle={styles.likesListContent}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                tintColor={COLORS.primary}
                colors={[COLORS.primary]}
              />
            }
            ListEmptyComponent={() => {
              if (isLoading) {
                return (
                  <View style={styles.emptyContainer}>
                    <Animated.View
                      style={{
                        transform: [{
                          rotate: fadeAnim.interpolate({
                            inputRange: [0, 1],
                            outputRange: ['0deg', '360deg'],
                          })
                        }]
                      }}
                    >
                      <Ionicons name="heart" size={64} color={COLORS.primary} />
                    </Animated.View>
                    <Text style={styles.emptyText}>Loading likes...</Text>
                    <Text style={styles.emptySubtext}>Please wait while we fetch the likes</Text>
                  </View>
                );
              }

              return (
                <View style={styles.emptyContainer}>
                  <Ionicons name="heart-outline" size={64} color={COLORS.textMuted} />
                  <Text style={styles.emptyText}>No likes yet</Text>
                  <Text style={styles.emptySubtext}>Be the first to like this update!</Text>
                </View>
              );
            }}
          />
        </Animated.View>
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.overlay,
  },
  modalContent: {
    flex: 1,
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: 50,
  },
  header: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  headerGradient: {
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
    marginLeft: 8,
  },
  headerSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    fontWeight: '500',
  },
  headerAction: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  likesList: {
    flex: 1,
  },
  likesListContent: {
    paddingVertical: 8,
  },
  likeContainer: {
    marginBottom: 4,
  },
  likeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: COLORS.surface,
    marginHorizontal: 16,
    marginVertical: 2,
    borderRadius: 16,
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  likeLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16,
  },
  avatarImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1.5,
    borderColor: COLORS.primary,
  },
  heartBadge: {
    position: 'absolute',
    bottom: -1,
    right: -1,
    backgroundColor: COLORS.heartColor,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: COLORS.surface,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 15,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeAgo: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  messageButton: {
    backgroundColor: COLORS.surface,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 16,
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 80,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    color: COLORS.textMuted,
    marginTop: 4,
    textAlign: 'center',
  },
});
