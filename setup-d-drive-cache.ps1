# PowerShell script to force ALL build cache to D: drive
Write-Host "Setting up D: drive cache directories..." -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Create all necessary cache directories on D: drive
$cacheDirectories = @(
    "D:\gradle-cache",
    "D:\android-build-cache",
    "D:\metro-cache",
    "D:\npm-cache",
    "D:\yarn-cache",
    "D:\temp",
    "D:\expo-cache",
    "D:\react-native-cache"
)

Write-Host "Creating cache directories on D: drive..." -ForegroundColor Yellow
foreach ($dir in $cacheDirectories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Created: $dir" -ForegroundColor Green
    } else {
        Write-Host "✓ Already exists: $dir" -ForegroundColor Gray
    }
}

# Set environment variables to force cache to D: drive
Write-Host "`nSetting environment variables..." -ForegroundColor Yellow

$envVars = @{
    "GRADLE_USER_HOME" = "D:\gradle-cache"
    "TMPDIR" = "D:\temp"
    "TEMP" = "D:\temp"
    "TMP" = "D:\temp"
    "NPM_CONFIG_CACHE" = "D:\npm-cache"
    "NPM_CONFIG_TMP" = "D:\temp"
    "YARN_CACHE_FOLDER" = "D:\yarn-cache"
    "EXPO_CACHE_DIR" = "D:\expo-cache"
    "REACT_NATIVE_CACHE_DIR" = "D:\react-native-cache"
    "ANDROID_USER_HOME" = "D:\android-cache"
}

foreach ($var in $envVars.GetEnumerator()) {
    try {
        [Environment]::SetEnvironmentVariable($var.Key, $var.Value, [EnvironmentVariableTarget]::User)
        Write-Host "✅ Set $($var.Key) = $($var.Value)" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to set $($var.Key): $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Clean existing cache on C: drive (optional)
Write-Host "`nCleaning existing cache on C: drive..." -ForegroundColor Yellow
$cDriveCachePaths = @(
    "$env:USERPROFILE\.gradle",
    "$env:LOCALAPPDATA\Temp\metro-*",
    "$env:LOCALAPPDATA\Temp\react-*",
    "$env:LOCALAPPDATA\Temp\expo-*",
    "$env:APPDATA\npm-cache"
)

foreach ($path in $cDriveCachePaths) {
    if (Test-Path $path) {
        try {
            Remove-Item $path -Recurse -Force -ErrorAction SilentlyContinue
            Write-Host "✅ Cleaned: $path" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Could not clean: $path (may be in use)" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n=========================================" -ForegroundColor Green
Write-Host "✅ D: drive cache setup complete!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Close all terminals and development tools" -ForegroundColor White
Write-Host "2. Restart your computer (recommended)" -ForegroundColor White
Write-Host "3. Run: set-env.bat" -ForegroundColor White
Write-Host "4. Run: npx expo start" -ForegroundColor White

Write-Host "`nAll build cache will now go to D: drive!" -ForegroundColor Green
