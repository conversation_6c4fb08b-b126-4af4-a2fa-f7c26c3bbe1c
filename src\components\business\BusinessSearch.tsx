import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { ProductCategory, BusinessType } from '../../types/Business';
import { useTheme } from '../../contexts/ThemeContext';

export interface SearchFilters {
  searchText: string;
  category?: ProductCategory;
  businessType?: BusinessType;
  priceRange: {
    min: number;
    max: number;
  };
  location?: string;
  availability?: 'available' | 'out_of_stock' | 'discontinued';
  isNegotiable?: boolean;
  sortBy: 'newest' | 'oldest' | 'price_low' | 'price_high' | 'popular';
}

interface BusinessSearchProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onSearch: (filters: SearchFilters) => void;
  totalResults?: number;
  hideFilterButton?: boolean;
  onClose?: () => void;
}

export const BusinessSearch: React.FC<BusinessSearchProps> = ({
  filters,
  onFiltersChange,
  onSearch,
  totalResults,
  hideFilterButton = false,
  onClose,
}) => {
  const { colors: COLORS } = useTheme();
  const [showFilters, setShowFilters] = useState(false);
  const [tempFilters, setTempFilters] = useState<SearchFilters>(filters);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const handleSearchTextChange = useCallback((text: string) => {
    const newFilters = { ...filters, searchText: text };
    onFiltersChange(newFilters);

    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Auto-search after 500ms delay
    searchTimeoutRef.current = setTimeout(() => {
      onSearch(newFilters);
    }, 500);
  }, [filters, onFiltersChange, onSearch]);

  const handleApplyFilters = () => {
    onFiltersChange(tempFilters);
    onSearch(tempFilters);
    setShowFilters(false);
  };

  const handleResetFilters = () => {
    const resetFilters: SearchFilters = {
      searchText: '',
      category: undefined,
      businessType: undefined,
      priceRange: { min: 0, max: 1000000 },
      location: undefined,
      availability: undefined,
      isNegotiable: undefined,
      sortBy: 'newest',
    };
    setTempFilters(resetFilters);
    onFiltersChange(resetFilters);
    onSearch(resetFilters);
    setShowFilters(false);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.category) count++;
    if (filters.businessType) count++;
    if (filters.priceRange.min > 0 || filters.priceRange.max < 1000000) count++;
    if (filters.location) count++;
    if (filters.availability) count++;
    if (filters.isNegotiable !== undefined) count++;
    if (filters.sortBy !== 'newest') count++;
    return count;
  };

  const categories: ProductCategory[] = [
    'electronics', 'computers', 'smartphones', 'fashion', 'mens_clothing', 'womens_clothing',
    'home_garden', 'furniture', 'automotive', 'cars', 'beauty_health', 'skincare',
    'sports_outdoors', 'fitness', 'books_media', 'books', 'toys_games', 'baby_products',
    'food_drinks', 'fresh_food', 'services', 'business_services', 'education', 'courses',
    'real_estate', 'residential', 'agriculture', 'crops', 'pets', 'pet_supplies', 'other'
  ];

  const businessTypes: BusinessType[] = [
    'retail', 'wholesale', 'restaurant', 'fast_food', 'cafe', 'hotel', 'school',
    'church', 'hospital', 'clinic', 'pharmacy', 'supermarket', 'convenience_store',
    'law_firm', 'accounting', 'real_estate_agency', 'construction',
    'manufacturing', 'software_company', 'agriculture', 'other'
  ];

  const styles = StyleSheet.create({
    container: {
      backgroundColor: COLORS.background,
    },
    searchBar: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
      gap: 12,
    },
    searchInputContainer: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: COLORS.surface,
      borderRadius: 25,
      paddingHorizontal: 16,
      paddingVertical: 12,
      gap: 8,
    },
    searchInput: {
      flex: 1,
      fontSize: 16,
      color: COLORS.text,
    },
    clearButton: {
      padding: 4,
    },
    closeButton: {
      width: 36,
      height: 36,
      borderRadius: 18,
      justifyContent: 'center',
      alignItems: 'center',
      backgroundColor: COLORS.surface,
      marginRight: 8,
    },
    filterButton: {
      width: 44,
      height: 44,
      borderRadius: 22,
      backgroundColor: COLORS.surface,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    },
    filterButtonActive: {
      backgroundColor: COLORS.primary + '20',
    },
    filterBadge: {
      position: 'absolute',
      top: -2,
      right: -2,
      backgroundColor: COLORS.primary,
      borderRadius: 10,
      minWidth: 20,
      height: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    filterBadgeText: {
      color: 'white',
      fontSize: 12,
      fontWeight: 'bold',
    },
    resultsContainer: {
      paddingHorizontal: 16,
      paddingBottom: 8,
    },
    resultsText: {
      fontSize: 14,
      color: COLORS.textSecondary,
    },
    quickFilters: {
      paddingHorizontal: 16,
    },
    quickFiltersContent: {
      gap: 8,
      paddingBottom: 12,
      paddingRight: 32, // Add extra padding to ensure last item is visible
    },
    quickFilter: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 20,
      backgroundColor: COLORS.surface,
      borderWidth: 1,
      borderColor: COLORS.border,
    },
    quickFilterActive: {
      backgroundColor: COLORS.primary,
      borderColor: COLORS.primary,
    },
    quickFilterText: {
      fontSize: 14,
      color: COLORS.text,
      fontWeight: '500',
    },
    quickFilterTextActive: {
      color: 'white',
    },
    modalContainer: {
      flex: 1,
      backgroundColor: COLORS.background,
    },
    modalHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: COLORS.border,
    },
    modalCancelText: {
      fontSize: 16,
      color: COLORS.textSecondary,
    },
    modalTitle: {
      fontSize: 18,
      fontWeight: 'bold',
      color: COLORS.text,
    },
    modalResetText: {
      fontSize: 16,
      color: COLORS.primary,
    },
    modalContent: {
      flex: 1,
      paddingHorizontal: 16,
    },
    filterSection: {
      marginVertical: 16,
    },
    filterSectionTitle: {
      fontSize: 16,
      fontWeight: '600',
      color: COLORS.text,
      marginBottom: 12,
    },
    filterOptions: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: 8,
    },
    filterOption: {
      paddingHorizontal: 12,
      paddingVertical: 8,
      borderRadius: 16,
      backgroundColor: COLORS.surface,
      borderWidth: 1,
      borderColor: COLORS.border,
    },
    filterOptionActive: {
      backgroundColor: COLORS.primary,
      borderColor: COLORS.primary,
    },
    filterOptionText: {
      fontSize: 14,
      color: COLORS.text,
    },
    filterOptionTextActive: {
      color: 'white',
    },
    priceInputs: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 12,
    },
    priceInput: {
      flex: 1,
      borderWidth: 1,
      borderColor: COLORS.border,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
      color: COLORS.text,
    },
    priceSeparator: {
      fontSize: 16,
      color: COLORS.textSecondary,
    },
    locationInput: {
      borderWidth: 1,
      borderColor: COLORS.border,
      borderRadius: 8,
      paddingHorizontal: 12,
      paddingVertical: 10,
      fontSize: 16,
      color: COLORS.text,
    },
    modalFooter: {
      paddingHorizontal: 16,
      paddingVertical: 16,
      borderTopWidth: 1,
      borderTopColor: COLORS.border,
    },
    applyButton: {
      backgroundColor: COLORS.primary,
      borderRadius: 8,
      paddingVertical: 16,
      alignItems: 'center',
    },
    applyButtonText: {
      color: 'white',
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchBar}>
        {onClose && (
          <TouchableOpacity
            onPress={onClose}
            style={styles.closeButton}
          >
            <Ionicons name="close" size={20} color={COLORS.textSecondary} />
          </TouchableOpacity>
        )}
        <View style={styles.searchInputContainer}>
          <Ionicons name="search" size={20} color={COLORS.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search products & services..."
            placeholderTextColor={COLORS.textSecondary}
            value={filters.searchText}
            onChangeText={handleSearchTextChange}
            returnKeyType="search"
            onSubmitEditing={() => onSearch(filters)}
            numberOfLines={1}
            multiline={false}
          />
          {filters.searchText.length > 0 && (
            <TouchableOpacity
              onPress={() => handleSearchTextChange('')}
              style={styles.clearButton}
            >
              <Ionicons name="close-circle" size={20} color={COLORS.textSecondary} />
            </TouchableOpacity>
          )}
        </View>

        {!hideFilterButton && (
          <TouchableOpacity
            style={[styles.filterButton, getActiveFiltersCount() > 0 && styles.filterButtonActive]}
            onPress={() => setShowFilters(true)}
          >
            <Ionicons
              name="options"
              size={20}
              color={getActiveFiltersCount() > 0 ? COLORS.primary : COLORS.textSecondary}
            />
            {getActiveFiltersCount() > 0 && (
              <View style={styles.filterBadge}>
                <Text style={styles.filterBadgeText}>{getActiveFiltersCount()}</Text>
              </View>
            )}
          </TouchableOpacity>
        )}
      </View>

      {/* Results Count */}
      {totalResults !== undefined && (
        <View style={styles.resultsContainer}>
          <Text style={styles.resultsText}>
            {totalResults} result{totalResults !== 1 ? 's' : ''} found
          </Text>
        </View>
      )}

      {/* Quick Filters */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.quickFilters}
        contentContainerStyle={styles.quickFiltersContent}
      >
        <TouchableOpacity
          style={[styles.quickFilter, filters.sortBy === 'newest' && styles.quickFilterActive]}
          onPress={() => {
            const newFilters = { ...filters, sortBy: 'newest' as const };
            onFiltersChange(newFilters);
            onSearch(newFilters);
          }}
        >
          <Text style={[styles.quickFilterText, filters.sortBy === 'newest' && styles.quickFilterTextActive]}>
            Newest
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickFilter, filters.sortBy === 'popular' && styles.quickFilterActive]}
          onPress={() => {
            const newFilters = { ...filters, sortBy: 'popular' as const };
            onFiltersChange(newFilters);
            onSearch(newFilters);
          }}
        >
          <Text style={[styles.quickFilterText, filters.sortBy === 'popular' && styles.quickFilterTextActive]}>
            Popular
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickFilter, filters.sortBy === 'price_low' && styles.quickFilterActive]}
          onPress={() => {
            const newFilters = { ...filters, sortBy: 'price_low' as const };
            onFiltersChange(newFilters);
            onSearch(newFilters);
          }}
        >
          <Text style={[styles.quickFilterText, filters.sortBy === 'price_low' && styles.quickFilterTextActive]}>
            Price: Low to High
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.quickFilter, filters.availability === 'available' && styles.quickFilterActive]}
          onPress={() => {
            const newFilters = { 
              ...filters, 
              availability: filters.availability === 'available' ? undefined : 'available' as const
            };
            onFiltersChange(newFilters);
            onSearch(newFilters);
          }}
        >
          <Text style={[styles.quickFilterText, filters.availability === 'available' && styles.quickFilterTextActive]}>
            Available
          </Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Advanced Filters Modal */}
      <Modal
        visible={showFilters}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowFilters(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Filters</Text>
            <TouchableOpacity onPress={handleResetFilters}>
              <Text style={styles.modalResetText}>Reset</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
            {/* Category Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Category</Text>
              <View style={styles.filterOptions}>
                {categories.map((category) => (
                  <TouchableOpacity
                    key={category}
                    style={[
                      styles.filterOption,
                      tempFilters.category === category && styles.filterOptionActive
                    ]}
                    onPress={() => setTempFilters(prev => ({
                      ...prev,
                      category: prev.category === category ? undefined : category
                    }))}
                  >
                    <Text style={[
                      styles.filterOptionText,
                      tempFilters.category === category && styles.filterOptionTextActive
                    ]}>
                      {category.charAt(0).toUpperCase() + category.slice(1).replace('_', ' ')}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Business Type Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Business Type</Text>
              <View style={styles.filterOptions}>
                {businessTypes.map((type) => (
                  <TouchableOpacity
                    key={type}
                    style={[
                      styles.filterOption,
                      tempFilters.businessType === type && styles.filterOptionActive
                    ]}
                    onPress={() => setTempFilters(prev => ({
                      ...prev,
                      businessType: prev.businessType === type ? undefined : type
                    }))}
                  >
                    <Text style={[
                      styles.filterOptionText,
                      tempFilters.businessType === type && styles.filterOptionTextActive
                    ]}>
                      {type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Price Range */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Price Range (UGX)</Text>
              <View style={styles.priceInputs}>
                <TextInput
                  style={styles.priceInput}
                  placeholder="Min"
                  value={tempFilters.priceRange.min.toString()}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 0;
                    setTempFilters(prev => ({
                      ...prev,
                      priceRange: { ...prev.priceRange, min: value }
                    }));
                  }}
                  keyboardType="numeric"
                />
                <Text style={styles.priceSeparator}>to</Text>
                <TextInput
                  style={styles.priceInput}
                  placeholder="Max"
                  value={tempFilters.priceRange.max.toString()}
                  onChangeText={(text) => {
                    const value = parseInt(text) || 1000000;
                    setTempFilters(prev => ({
                      ...prev,
                      priceRange: { ...prev.priceRange, max: value }
                    }));
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            {/* Location */}
            <View style={styles.filterSection}>
              <Text style={styles.filterSectionTitle}>Location</Text>
              <TextInput
                style={styles.locationInput}
                placeholder="Enter city or area"
                value={tempFilters.location || ''}
                onChangeText={(text) => setTempFilters(prev => ({
                  ...prev,
                  location: text || undefined
                }))}
              />
            </View>
          </ScrollView>

          <View style={styles.modalFooter}>
            <TouchableOpacity
              style={styles.applyButton}
              onPress={handleApplyFilters}
            >
              <Text style={styles.applyButtonText}>Apply Filters</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};
