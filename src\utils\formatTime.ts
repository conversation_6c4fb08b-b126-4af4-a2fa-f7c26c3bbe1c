import { Dimensions } from 'react-native';

// Get device dimensions for responsive time formatting
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

/**
 * Detect device's time format preference (12/24 hour)
 */
function getDeviceTimeFormat(): boolean {
  try {
    // Create a test date and format it with the device's locale
    const testDate = new Date(2023, 0, 1, 13, 0, 0); // 1:00 PM
    const timeString = testDate.toLocaleTimeString();

    // If the formatted time contains 'PM' or 'AM', device uses 12-hour format
    // If it shows '13:00' or similar, device uses 24-hour format
    const has12HourIndicator = /AM|PM|am|pm/i.test(timeString);
    const has24HourFormat = /^([01]?[0-9]|2[0-3]):[0-5][0-9]/.test(timeString.trim());

    // Return true for 24-hour format, false for 12-hour format
    return !has12HourIndicator || has24HourFormat;
  } catch (error) {
    console.warn('Failed to detect device time format, defaulting to 12-hour:', error);
    return false; // Default to 12-hour format
  }
}

// Cache the device time format preference
const deviceUses24Hour = getDeviceTimeFormat();

// Time formatting options interface
export interface TimeFormatOptions {
  use24Hour?: boolean;
  includeSeconds?: boolean;
  includeDate?: boolean;
  shortFormat?: boolean;
  relative?: boolean;
  locale?: string;
  timezone?: string;
}

// Enhanced time formatting with mobile optimization
export function formatTime(timestamp: any, options: TimeFormatOptions = {}): string {
  try {
    if (!timestamp) return "";

    // Parse timestamp to Date object
    let date: Date;
    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      date = timestamp.toDate();
    } else if (timestamp.seconds) {
      date = new Date(timestamp.seconds * 1000);
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'number') {
      date = new Date(timestamp);
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else {
      return "";
    }

    // Validate date
    if (isNaN(date.getTime())) {
      return "";
    }

    const {
      use24Hour = deviceUses24Hour, // Use device's time format preference
      includeSeconds = false,
      includeDate = false,
      shortFormat = isSmallDevice,
      relative = false,
      locale = 'en-US'
    } = options;

    // Return relative time if requested
    if (relative) {
      return formatRelativeTime(date, shortFormat);
    }

    // Build time format options
    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: !use24Hour,
    };

    if (includeSeconds) {
      timeOptions.second = "2-digit";
    }

    if (includeDate) {
      if (shortFormat) {
        timeOptions.month = "short";
        timeOptions.day = "numeric";
      } else {
        timeOptions.year = "numeric";
        timeOptions.month = "long";
        timeOptions.day = "numeric";
      }
    }

    return date.toLocaleTimeString(locale, timeOptions);
  } catch (error) {
    return "";
  }
}

/**
 * Format relative time (e.g., "2 minutes ago", "in 3 hours")
 */
export function formatRelativeTime(date: Date, shortFormat: boolean = isSmallDevice): string {
  try {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    const isFuture = diffMs < 0;
    const absSeconds = Math.abs(diffSeconds);
    const absMinutes = Math.abs(diffMinutes);
    const absHours = Math.abs(diffHours);
    const absDays = Math.abs(diffDays);
    const absWeeks = Math.abs(diffWeeks);
    const absMonths = Math.abs(diffMonths);
    const absYears = Math.abs(diffYears);

    if (shortFormat) {
      // Compact format for small devices
      if (absSeconds < 60) return "now";
      if (absMinutes < 60) return `${absMinutes}m${isFuture ? ' later' : ''}`;
      if (absHours < 24) return `${absHours}h${isFuture ? ' later' : ''}`;
      if (absDays < 7) return `${absDays}d${isFuture ? ' later' : ''}`;
      if (absWeeks < 4) return `${absWeeks}w${isFuture ? ' later' : ''}`;
      if (absMonths < 12) return `${absMonths}mo${isFuture ? ' later' : ''}`;
      return `${absYears}y${isFuture ? ' later' : ''}`;
    } else {
      // Full format for larger devices
      if (absSeconds < 60) return "just now";
      if (absMinutes < 60) {
        const unit = absMinutes === 1 ? "minute" : "minutes";
        return isFuture ? `in ${absMinutes} ${unit}` : `${absMinutes} ${unit} ago`;
      }
      if (absHours < 24) {
        const unit = absHours === 1 ? "hour" : "hours";
        return isFuture ? `in ${absHours} ${unit}` : `${absHours} ${unit} ago`;
      }
      if (absDays < 7) {
        const unit = absDays === 1 ? "day" : "days";
        return isFuture ? `in ${absDays} ${unit}` : `${absDays} ${unit} ago`;
      }
      if (absWeeks < 4) {
        const unit = absWeeks === 1 ? "week" : "weeks";
        return isFuture ? `in ${absWeeks} ${unit}` : `${absWeeks} ${unit} ago`;
      }
      if (absMonths < 12) {
        const unit = absMonths === 1 ? "month" : "months";
        return isFuture ? `in ${absMonths} ${unit}` : `${absMonths} ${unit} ago`;
      }
      const unit = absYears === 1 ? "year" : "years";
      return isFuture ? `in ${absYears} ${unit}` : `${absYears} ${unit} ago`;
    }
  } catch (error) {
    return "";
  }
}

/**
 * Format time for chat messages with smart formatting
 */
export function formatChatTime(timestamp: any): string {
  try {
    if (!timestamp) return "";

    const date = parseTimestamp(timestamp);
    if (!date) return "";

    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();

    if (isToday) {
      return formatTime(timestamp, {
        use24Hour: deviceUses24Hour,
        shortFormat: isSmallDevice
      });
    } else if (isYesterday) {
      return isSmallDevice ? "Yesterday" : "Yesterday";
    } else {
      const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
      if (diffDays < 7) {
        return date.toLocaleDateString([], { weekday: isSmallDevice ? "short" : "long" });
      } else {
        return date.toLocaleDateString([], {
          month: isSmallDevice ? "short" : "long",
          day: "numeric"
        });
      }
    }
  } catch (error) {
    return "";
  }
}

/**
 * Format duration in milliseconds to readable format
 */
export function formatDuration(durationMs: number, options: TimeFormatOptions = {}): string {
  try {
    if (typeof durationMs !== 'number' || isNaN(durationMs) || durationMs < 0) {
      return "0s";
    }

    const { shortFormat = isSmallDevice, includeSeconds = true } = options;

    const totalSeconds = Math.floor(durationMs / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    const parts: string[] = [];

    if (shortFormat) {
      if (hours > 0) parts.push(`${hours}h`);
      if (minutes > 0) parts.push(`${minutes}m`);
      if (includeSeconds && (seconds > 0 || parts.length === 0)) {
        parts.push(`${seconds}s`);
      }
    } else {
      if (hours > 0) {
        parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
      }
      if (minutes > 0) {
        parts.push(`${minutes} minute${minutes !== 1 ? 's' : ''}`);
      }
      if (includeSeconds && (seconds > 0 || parts.length === 0)) {
        parts.push(`${seconds} second${seconds !== 1 ? 's' : ''}`);
      }
    }

    // Limit parts for small devices
    if (isSmallDevice && parts.length > 2) {
      return parts.slice(0, 2).join(' ');
    }

    return parts.join(' ');
  } catch (error) {
    return "0s";
  }
}

/**
 * Format time range (e.g., "2:00 PM - 4:30 PM")
 */
export function formatTimeRange(
  startTime: any,
  endTime: any,
  options: TimeFormatOptions = {}
): string {
  try {
    const start = parseTimestamp(startTime);
    const end = parseTimestamp(endTime);

    if (!start || !end) return "";

    const { shortFormat = isSmallDevice, use24Hour = deviceUses24Hour } = options;
    const separator = shortFormat ? "-" : " - ";

    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: !use24Hour,
    };

    const startFormatted = start.toLocaleTimeString([], timeOptions);
    const endFormatted = end.toLocaleTimeString([], timeOptions);

    return `${startFormatted}${separator}${endFormatted}`;
  } catch (error) {
    return "";
  }
}

/**
 * Parse various timestamp formats to Date object
 */
function parseTimestamp(timestamp: any): Date | null {
  try {
    if (!timestamp) return null;

    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }

    if (timestamp.seconds) {
      return new Date(timestamp.seconds * 1000);
    }

    if (timestamp instanceof Date) {
      return isNaN(timestamp.getTime()) ? null : timestamp;
    }

    if (typeof timestamp === 'number') {
      return new Date(timestamp);
    }

    if (typeof timestamp === 'string') {
      const parsed = new Date(timestamp);
      return isNaN(parsed.getTime()) ? null : parsed;
    }

    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Get time zone abbreviation
 */
export function getTimeZoneAbbreviation(): string {
  try {
    const date = new Date();
    const timeZoneString = date.toString();
    const match = timeZoneString.match(/\(([^)]+)\)$/);

    if (match) {
      // Extract abbreviation from timezone name
      const timeZoneName = match[1];
      const words = timeZoneName.split(' ');
      if (words.length > 1) {
        return words.map(word => word[0]).join('').toUpperCase();
      }
      return timeZoneName.substring(0, 3).toUpperCase();
    }

    // Fallback to offset
    const offset = -date.getTimezoneOffset();
    const hours = Math.floor(offset / 60);
    const minutes = offset % 60;
    const sign = offset >= 0 ? '+' : '-';

    return `UTC${sign}${Math.abs(hours).toString().padStart(2, '0')}:${Math.abs(minutes).toString().padStart(2, '0')}`;
  } catch (error) {
    return "UTC";
  }
}

/**
 * Check if time is in business hours
 */
export function isBusinessHours(
  timestamp: any,
  startHour: number = 9,
  endHour: number = 17
): boolean {
  try {
    const date = parseTimestamp(timestamp);
    if (!date) return false;

    const hour = date.getHours();
    const day = date.getDay(); // 0 = Sunday, 6 = Saturday

    // Check if it's a weekday (Monday-Friday)
    const isWeekday = day >= 1 && day <= 5;

    // Check if it's within business hours
    const isBusinessTime = hour >= startHour && hour < endHour;

    return isWeekday && isBusinessTime;
  } catch (error) {
    return false;
  }
}

/**
 * Format time with smart precision based on context
 */
export function formatSmartTime(timestamp: any, context: 'chat' | 'call' | 'update' | 'general' = 'general'): string {
  try {
    const date = parseTimestamp(timestamp);
    if (!date) return "";

    switch (context) {
      case 'chat':
        return formatChatTime(timestamp);

      case 'call':
        return formatTime(timestamp, {
          use24Hour: false,
          shortFormat: isSmallDevice,
          relative: false
        });

      case 'update':
        return formatRelativeTime(date, isSmallDevice);

      default:
        return formatTime(timestamp, {
          use24Hour: deviceUses24Hour,
          shortFormat: isSmallDevice
        });
    }
  } catch (error) {
    return "";
  }
}

// Export default object with all functions
export default {
  formatTime,
  formatRelativeTime,
  formatChatTime,
  formatDuration,
  formatTimeRange,
  formatSmartTime,
  getTimeZoneAbbreviation,
  isBusinessHours,
};
