# PowerShell script to download and install Android SDK command line tools
Write-Host "Installing Android SDK Command Line Tools..." -ForegroundColor Green

# Create Android SDK directory
$androidSdkPath = "C:\Android\Sdk"
$cmdlineToolsPath = "$androidSdkPath\cmdline-tools\latest"

Write-Host "Creating Android SDK directory at $androidSdkPath..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path $androidSdkPath
New-Item -ItemType Directory -Force -Path "$androidSdkPath\cmdline-tools"

# Download Android command line tools
$downloadUrl = "https://dl.google.com/android/repository/commandlinetools-win-11076708_latest.zip"
$zipFile = "$env:TEMP\commandlinetools-win.zip"

Write-Host "Downloading Android command line tools..." -ForegroundColor Yellow
try {
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipFile -UseBasicParsing
    Write-Host "Download completed!" -ForegroundColor Green
} catch {
    Write-Host "Failed to download: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Extract the zip file
Write-Host "Extracting command line tools..." -ForegroundColor Yellow
try {
    Expand-Archive -Path $zipFile -DestinationPath "$androidSdkPath\cmdline-tools" -Force
    
    # Move cmdline-tools to latest folder
    if (Test-Path "$androidSdkPath\cmdline-tools\cmdline-tools") {
        Move-Item "$androidSdkPath\cmdline-tools\cmdline-tools" "$androidSdkPath\cmdline-tools\latest"
    }
    
    Write-Host "Extraction completed!" -ForegroundColor Green
} catch {
    Write-Host "Failed to extract: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Set environment variables
Write-Host "Setting environment variables..." -ForegroundColor Yellow
[Environment]::SetEnvironmentVariable("ANDROID_HOME", $androidSdkPath, [EnvironmentVariableTarget]::User)
[Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $androidSdkPath, [EnvironmentVariableTarget]::User)

# Update PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)
$pathsToAdd = @(
    "$androidSdkPath\cmdline-tools\latest\bin",
    "$androidSdkPath\platform-tools",
    "$androidSdkPath\emulator"
)

foreach ($path in $pathsToAdd) {
    if ($currentPath -notlike "*$path*") {
        $currentPath = "$currentPath;$path"
    }
}

[Environment]::SetEnvironmentVariable("PATH", $currentPath, [EnvironmentVariableTarget]::User)

Write-Host "Environment variables set!" -ForegroundColor Green

# Install essential SDK components
Write-Host "Installing essential SDK components..." -ForegroundColor Yellow
$sdkmanager = "$androidSdkPath\cmdline-tools\latest\bin\sdkmanager.bat"

if (Test-Path $sdkmanager) {
    Write-Host "Installing platform-tools..." -ForegroundColor Yellow
    & $sdkmanager "platform-tools"

    Write-Host "Installing Android API 34..." -ForegroundColor Yellow
    & $sdkmanager "platforms;android-34"

    Write-Host "Installing build-tools..." -ForegroundColor Yellow
    & $sdkmanager "build-tools;34.0.0"

    Write-Host "Installing emulator..." -ForegroundColor Yellow
    & $sdkmanager "emulator"

    Write-Host "Installing system images for emulator..." -ForegroundColor Yellow
    & $sdkmanager "system-images;android-34;google_apis;x86_64"

    Write-Host "✅ SDK components installed!" -ForegroundColor Green
} else {
    Write-Host "⚠️  SDK Manager not found. Please install manually through Android Studio." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "✅ Android SDK setup complete!" -ForegroundColor Green
Write-Host "ANDROID_HOME = $androidSdkPath" -ForegroundColor White
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart your terminal/PowerShell" -ForegroundColor White
Write-Host "2. Run: npx expo start" -ForegroundColor White
Write-Host "3. Press 'a' to open Android" -ForegroundColor White
Write-Host ""
Write-Host "To create an Android emulator:" -ForegroundColor Yellow
Write-Host "1. Download Android Studio from: https://developer.android.com/studio" -ForegroundColor Yellow
Write-Host "2. Open Android Studio > Tools > AVD Manager" -ForegroundColor Yellow
Write-Host "3. Create a new virtual device" -ForegroundColor Yellow
Write-Host "ANDROID_HOME = $androidSdkPath" -ForegroundColor Cyan
Write-Host "ANDROID_SDK_ROOT = $androidSdkPath" -ForegroundColor Cyan

# Clean up
Remove-Item $zipFile -Force

Write-Host "`nAndroid SDK command line tools installed successfully!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart your terminal" -ForegroundColor White
Write-Host "2. Run: sdkmanager --install 'platform-tools' 'platforms;android-34' 'build-tools;34.0.0'" -ForegroundColor White
Write-Host "3. Accept licenses: sdkmanager --licenses" -ForegroundColor White
