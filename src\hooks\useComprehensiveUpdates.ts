import { useState, useEffect, useCallback } from 'react';
import { Alert } from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../redux/store';
import { Update, Story } from '../types/Update';
import { BusinessPost, BusinessProfile } from '../types/Business';
import { comprehensiveUpdatesService } from '../services/comprehensiveUpdatesService';
import { businessService } from '../services/businessService';
import { updatesSyncService } from '../services/updatesSyncService';
import { businessOfflineSync } from '../services/businessOfflineSync';
import { localUpdatesStorage } from '../services/localUpdatesStorage';
// import { storiesService } from '../services/storiesService'; // TODO: Create this service

export type TabType = 'feed' | 'marketplace' | 'skills' | 'rewards';

interface UseComprehensiveUpdatesReturn {
  // State
  activeTab: TabType;
  updates: Update[];
  businessPosts: BusinessPost[];
  educationPosts: BusinessPost[];
  stories: Story[];
  myStories: Story[];
  userBusinessProfile: BusinessProfile | null;
  
  // Loading states
  isLoading: boolean;
  isRefreshing: boolean;
  isLoadingMore: boolean;
  
  // Modal states
  showUnifiedSearch: boolean;
  showPostDetails: boolean;
  showBusinessRegistration: boolean;
  showStoryViewer: boolean;
  
  // Selected data
  selectedPost: BusinessPost | null;
  selectedStoryIndex: number;
  
  // Actions
  setActiveTab: (tab: TabType) => void;
  handleRefresh: () => Promise<void>;
  handleLoadMore: () => Promise<void>;
  
  // Modal actions
  setShowUnifiedSearch: (show: boolean) => void;
  setShowPostDetails: (show: boolean) => void;
  setShowBusinessRegistration: (show: boolean) => void;
  setShowStoryViewer: (show: boolean) => void;
  
  // Content actions
  handlePostPress: (post: BusinessPost) => void;
  handleStoryPress: (story: Story, index: number) => void;
  handleChatPress: (businessId: string) => void;
  handleLikePress: (updateId: string) => Promise<void>;
  handleCommentPress: (updateId: string) => void;
  handleSharePress: (updateId: string) => void;
  
  // Business actions
  handleBusinessRegistrationSuccess: (profile: BusinessProfile) => void;
}

export const useComprehensiveUpdates = (initialTab: TabType = 'feed'): UseComprehensiveUpdatesReturn => {
  // Redux state
  const currentUser = useSelector((state: RootState) => state.auth.user);
  const isOnline = useSelector((state: RootState) => state.network.isOnline);

  // Tab state
  const [activeTab, setActiveTab] = useState<TabType>(initialTab);

  // Data state
  const [updates, setUpdates] = useState<Update[]>([]);
  const [businessPosts, setBusinessPosts] = useState<BusinessPost[]>([]);
  const [educationPosts, setEducationPosts] = useState<BusinessPost[]>([]);
  const [stories, setStories] = useState<Story[]>([]);
  const [myStories, setMyStories] = useState<Story[]>([]);
  const [userBusinessProfile, setUserBusinessProfile] = useState<BusinessProfile | null>(null);

  // Loading states
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Modal states
  const [showUnifiedSearch, setShowUnifiedSearch] = useState(false);
  const [showPostDetails, setShowPostDetails] = useState(false);
  const [showBusinessRegistration, setShowBusinessRegistration] = useState(false);
  const [showStoryViewer, setShowStoryViewer] = useState(false);

  // Selected data
  const [selectedPost, setSelectedPost] = useState<BusinessPost | null>(null);
  const [selectedStoryIndex, setSelectedStoryIndex] = useState(0);

  // Initialize data
  useEffect(() => {
    if (currentUser?.id) {
      initializeData();
      loadUserBusinessProfile();
    }
  }, [currentUser?.id, activeTab]);

  // Initialize all data with complete online/offline functionality
  const initializeData = async () => {
    setIsLoading(true);
    try {
      if (isOnline) {
        // Online: Load from Firebase and sync with local storage
        await Promise.all([
          loadFeedDataOnline(),
          loadBusinessDataOnline(),
          loadStoriesDataOnline(),
        ]);
      } else {
        // Offline: Load from local storage
        await Promise.all([
          loadFeedDataOffline(),
          loadBusinessDataOffline(),
          loadStoriesDataOffline(),
        ]);
      }
    } catch (error) {
      console.error('❌ Error initializing data:', error);
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load feed data online
  const loadFeedDataOnline = async () => {
    try {
      const result = await comprehensiveUpdatesService.getUpdatesFeed(currentUser?.id || '', {
        algorithm: 'personalized',
        includeStories: false,
        includeFriends: true,
        includeFollowing: true,
        includePublic: true,
        contentTypes: ['image', 'video', 'text'],
        maxAge: 168, // 7 days
        minEngagement: 0,
      }, undefined, 20);
      if (result.success && result.data) {
        setUpdates(result.data.updates);
      }
    } catch (error) {
      console.error('❌ Error loading feed data online:', error);
      // Fallback to offline data
      await loadFeedDataOffline();
    }
  };

  // Load feed data offline
  const loadFeedDataOffline = async () => {
    try {
      const cachedUpdates = await localUpdatesStorage.getUpdates(50, 0, false);
      setUpdates(cachedUpdates);
    } catch (error) {
      console.error('❌ Error loading feed data offline:', error);
      setUpdates([]);
    }
  };

  // Load business data online
  const loadBusinessDataOnline = async () => {
    try {
      const allPosts = await businessService.getMarketplacePosts({});
      setBusinessPosts(allPosts.filter((post: BusinessPost) => post.category !== 'education'));
      setEducationPosts(allPosts.filter((post: BusinessPost) => post.category === 'education'));
      // Posts are automatically cached by the service during getPosts call
    } catch (error) {
      console.error('❌ Error loading business data online:', error);
      // Fallback to offline data
      await loadBusinessDataOffline();
    }
  };

  // Load business data offline
  const loadBusinessDataOffline = async () => {
    try {
      // Use the businessOfflineSync service for offline data
      const result = await businessOfflineSync.getBusinessPosts({ limit: 50 });
      if (result.success && result.data) {
        const cachedPosts = result.data;
        setBusinessPosts(cachedPosts.filter((post: BusinessPost) => post.category !== 'education'));
        setEducationPosts(cachedPosts.filter((post: BusinessPost) => post.category === 'education'));
      } else {
        setBusinessPosts([]);
        setEducationPosts([]);
      }
    } catch (error) {
      console.error('❌ Error loading business data offline:', error);
      setBusinessPosts([]);
      setEducationPosts([]);
    }
  };

  // Load stories data online
  const loadStoriesDataOnline = async () => {
    if (!currentUser?.id) return;

    try {
      // TODO: Implement storiesService
      console.log('📱 Loading stories data online - service not implemented yet');
      setStories([]);
      setMyStories([]);
    } catch (error) {
      console.error('❌ Error loading stories data online:', error);
      // Fallback to offline data
      await loadStoriesDataOffline();
    }
  };

  // Load stories data offline
  const loadStoriesDataOffline = async () => {
    if (!currentUser?.id) return;

    try {
      // TODO: Implement storiesService
      console.log('📱 Loading stories data offline - service not implemented yet');
      setStories([]);
      setMyStories([]);
    } catch (error) {
      console.error('❌ Error loading stories data offline:', error);
      setStories([]);
      setMyStories([]);
    }
  };

  // Load user business profile
  const loadUserBusinessProfile = async () => {
    if (!currentUser?.id) return;

    try {
      // Use the users service directly for consistent API response handling
      const result = await businessService.users.getBusinessProfile(currentUser.id);
      if (result.success && result.data) {
        setUserBusinessProfile(result.data);
      }
    } catch (error) {
      console.error('❌ Error loading business profile:', error);
    }
  };

  // Handle refresh
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    
    if (isOnline) {
      // Online refresh: sync with server
      await initializeData();
      // Sync any pending offline changes
      await syncOfflineChanges();
    } else {
      // Offline refresh: reload from cache
      await initializeData();
    }
    
    setIsRefreshing(false);
  }, [activeTab, isOnline]);

  // Handle load more
  const handleLoadMore = useCallback(async () => {
    if (isLoadingMore || !isOnline) return;

    setIsLoadingMore(true);
    try {
      // Load more based on active tab
      if (activeTab === 'feed') {
        const result = await comprehensiveUpdatesService.getUpdatesFeed(
          currentUser?.id || '',
          {
            algorithm: 'personalized',
            includeStories: false,
            includeFriends: true,
            includeFollowing: true,
            includePublic: true,
            contentTypes: ['image', 'video', 'text'],
            maxAge: 168, // 7 days
            minEngagement: 0,
          },
          updates.length > 0 ? updates[updates.length - 1].id : undefined,
          20
        );
        if (result.success && result.data?.updates) {
          setUpdates(prev => [...prev, ...result.data!.updates]);
        }
      } else if (activeTab === 'marketplace') {
        const result = await businessService.getMarketplacePosts({});
        if (result.length > 0) {
          setBusinessPosts(prev => [...prev, ...result.filter((post: any) => post.category !== 'education')]);
        }
      } else if (activeTab === 'skills') {
        const result = await businessService.getMarketplacePosts({ category: 'education' });
        if (result.length > 0) {
          setEducationPosts(prev => [...prev, ...result]);
        }
      }
    } catch (error) {
      console.error('❌ Error loading more data:', error);
    } finally {
      setIsLoadingMore(false);
    }
  }, [activeTab, isOnline, isLoadingMore, updates.length, businessPosts.length, educationPosts.length]);

  // Sync offline changes when back online
  const syncOfflineChanges = async () => {
    try {
      // Sync pending updates
      await updatesSyncService.syncPendingUpdates();
      // Sync pending business operations (includes posts and likes)
      await businessOfflineSync.syncPendingOperations();
    } catch (error) {
      console.error('❌ Error syncing offline changes:', error);
    }
  };

  // Handle post press
  const handlePostPress = (post: BusinessPost) => {
    setSelectedPost(post);
    setShowPostDetails(true);
  };

  // Handle story press
  const handleStoryPress = (_story: Story, index: number) => {
    setSelectedStoryIndex(index);
    setShowStoryViewer(true);
  };

  // Handle chat press
  const handleChatPress = (businessId: string) => {
    Alert.alert('Chat', `Opening chat with business: ${businessId}`);
  };

  // Handle like press
  const handleLikePress = async (updateId: string) => {
    if (!currentUser?.id) {
      Alert.alert('Login Required', 'Please login to like posts');
      return;
    }

    try {
      await comprehensiveUpdatesService.toggleLike(
        updateId,
        currentUser.id,
        currentUser.name || currentUser.displayName || 'Unknown User',
        currentUser.avatar
      );
    } catch (error) {
      console.error('❌ Error liking post:', error);
      throw error;
    }
  };

  // Handle comment press
  const handleCommentPress = (updateId: string) => {
    Alert.alert('Comments', `Opening comments for update: ${updateId}`);
  };

  // Handle share press
  const handleSharePress = (updateId: string) => {
    Alert.alert('Share', `Sharing update: ${updateId}`);
  };

  // Handle business registration success
  const handleBusinessRegistrationSuccess = (profile: BusinessProfile) => {
    setUserBusinessProfile(profile);
    setShowBusinessRegistration(false);
    Alert.alert('Success!', 'Your business has been registered successfully!');
  };

  return {
    // State
    activeTab,
    updates,
    businessPosts,
    educationPosts,
    stories,
    myStories,
    userBusinessProfile,
    
    // Loading states
    isLoading,
    isRefreshing,
    isLoadingMore,
    
    // Modal states
    showUnifiedSearch,
    showPostDetails,
    showBusinessRegistration,
    showStoryViewer,
    
    // Selected data
    selectedPost,
    selectedStoryIndex,
    
    // Actions
    setActiveTab,
    handleRefresh,
    handleLoadMore,
    
    // Modal actions
    setShowUnifiedSearch,
    setShowPostDetails,
    setShowBusinessRegistration,
    setShowStoryViewer,
    
    // Content actions
    handlePostPress,
    handleStoryPress,
    handleChatPress,
    handleLikePress,
    handleCommentPress,
    handleSharePress,
    
    // Business actions
    handleBusinessRegistrationSuccess,
  };
};
