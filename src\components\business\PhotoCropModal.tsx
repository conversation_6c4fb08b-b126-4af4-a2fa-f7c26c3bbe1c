import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  SafeAreaView,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
} from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
} from 'react-native-reanimated';
import * as ImageManipulator from 'expo-image-manipulator';

const COLORS = {
  primary: '#1DA1F2',
  background: '#FFFFFF',
  text: '#14171A',
  textSecondary: '#657786',
  border: '#E1E8ED',
  surface: '#F7F9FA',
  success: '#17BF63',
  warning: '#FFAD1F',
  error: '#E0245E',
};

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface PhotoCropModalProps {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
  onSave: (croppedImageUri: string) => void;
}

export const PhotoCropModal: React.FC<PhotoCropModalProps> = ({
  visible,
  imageUri,
  onClose,
  onSave,
}) => {
  const [cropArea, setCropArea] = useState({
    x: 50,
    y: 100,
    width: screenWidth - 100,
    height: screenWidth - 100, // Square crop by default
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragHandle, setDragHandle] = useState<string | null>(null);

  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  const pinchGesture = Gesture.Pinch()
    .onUpdate((event) => {
      scale.value = Math.max(0.5, Math.min(event.scale, 3));
    })
    .onEnd(() => {
      if (scale.value < 1) {
        scale.value = withSpring(1);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
      }
    });

  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      if (scale.value > 1) {
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      }
    });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { scale: scale.value },
      ],
    };
  });

  const handleSave = async () => {
    try {
      // For now, we'll implement a simple crop based on the crop area
      // In a production app, you'd want more sophisticated cropping

      const croppedImage = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          {
            crop: {
              originX: cropArea.x,
              originY: cropArea.y,
              width: cropArea.width,
              height: cropArea.height,
            },
          },
        ],
        {
          compress: 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      onSave(croppedImage.uri);
    } catch (error) {
      console.error('Error cropping image:', error);
      Alert.alert('Error', 'Failed to crop image. Using original image.');
      onSave(imageUri);
    }
  };

  const resetCrop = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
    // Reset crop area to default
    setCropArea({
      x: 50,
      y: 100,
      width: screenWidth - 100,
      height: screenWidth - 100,
    });
  };

  // Handle corner dragging
  const handleCornerDrag = (corner: string, gestureX: number, gestureY: number) => {
    const newCropArea = { ...cropArea };

    switch (corner) {
      case 'topLeft':
        const newWidth = cropArea.width + (cropArea.x - gestureX);
        const newHeight = cropArea.height + (cropArea.y - gestureY);
        if (newWidth > 50 && newHeight > 50) {
          newCropArea.x = gestureX;
          newCropArea.y = gestureY;
          newCropArea.width = newWidth;
          newCropArea.height = newHeight;
        }
        break;
      case 'topRight':
        const newWidthTR = gestureX - cropArea.x;
        const newHeightTR = cropArea.height + (cropArea.y - gestureY);
        if (newWidthTR > 50 && newHeightTR > 50) {
          newCropArea.y = gestureY;
          newCropArea.width = newWidthTR;
          newCropArea.height = newHeightTR;
        }
        break;
      case 'bottomLeft':
        const newWidthBL = cropArea.width + (cropArea.x - gestureX);
        const newHeightBL = gestureY - cropArea.y;
        if (newWidthBL > 50 && newHeightBL > 50) {
          newCropArea.x = gestureX;
          newCropArea.width = newWidthBL;
          newCropArea.height = newHeightBL;
        }
        break;
      case 'bottomRight':
        const newWidthBR = gestureX - cropArea.x;
        const newHeightBR = gestureY - cropArea.y;
        if (newWidthBR > 50 && newHeightBR > 50) {
          newCropArea.width = newWidthBR;
          newCropArea.height = newHeightBR;
        }
        break;
    }

    // Ensure crop area stays within bounds
    if (newCropArea.x >= 0 && newCropArea.y >= 0 &&
        newCropArea.x + newCropArea.width <= screenWidth &&
        newCropArea.y + newCropArea.height <= screenHeight - 200) {
      setCropArea(newCropArea);
    }
  };

  const setCropRatio = (ratio: number) => {
    const maxWidth = screenWidth - 100;
    const maxHeight = screenHeight - 300;
    
    let newWidth, newHeight;
    
    if (ratio === 1) {
      // Square
      const size = Math.min(maxWidth, maxHeight);
      newWidth = size;
      newHeight = size;
    } else if (ratio === 16/9) {
      // Landscape
      newWidth = maxWidth;
      newHeight = maxWidth * (9/16);
    } else if (ratio === 9/16) {
      // Portrait
      newHeight = maxHeight;
      newWidth = maxHeight * (9/16);
    } else {
      // Free form
      newWidth = maxWidth;
      newHeight = maxHeight * 0.7;
    }
    
    setCropArea({
      x: (screenWidth - newWidth) / 2,
      y: 150,
      width: newWidth,
      height: newHeight,
    });
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="fullScreen">
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.headerButton}>
            <Ionicons name="close" size={24} color={COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Crop Photo</Text>
          <TouchableOpacity onPress={handleSave} style={styles.headerButton}>
            <Text style={styles.saveText}>Save</Text>
          </TouchableOpacity>
        </View>

        {/* Image Container */}
        <View style={styles.imageContainer}>
          <GestureDetector gesture={Gesture.Simultaneous(pinchGesture, panGesture)}>
            <Animated.View style={animatedStyle}>
              <Image
                source={{ uri: imageUri }}
                style={styles.image}
                resizeMode="contain"
              />
            </Animated.View>
          </GestureDetector>

          {/* Crop Overlay */}
          <View style={styles.overlay}>
            {/* Top overlay */}
            <View style={[styles.overlaySection, { height: cropArea.y }]} />
            
            {/* Middle section with side overlays */}
            <View style={styles.middleSection}>
              <View style={[styles.overlaySection, { width: cropArea.x }]} />
              
              {/* Crop area */}
              <View
                style={[
                  styles.cropArea,
                  {
                    width: cropArea.width,
                    height: cropArea.height,
                  },
                ]}
              >
                {/* Corner handles - draggable */}
                <GestureDetector gesture={Gesture.Pan()
                  .onUpdate((event) => {
                    handleCornerDrag('topLeft', event.absoluteX, event.absoluteY);
                  })}>
                  <View style={[styles.handle, styles.topLeft]} />
                </GestureDetector>

                <GestureDetector gesture={Gesture.Pan()
                  .onUpdate((event) => {
                    handleCornerDrag('topRight', event.absoluteX, event.absoluteY);
                  })}>
                  <View style={[styles.handle, styles.topRight]} />
                </GestureDetector>

                <GestureDetector gesture={Gesture.Pan()
                  .onUpdate((event) => {
                    handleCornerDrag('bottomLeft', event.absoluteX, event.absoluteY);
                  })}>
                  <View style={[styles.handle, styles.bottomLeft]} />
                </GestureDetector>

                <GestureDetector gesture={Gesture.Pan()
                  .onUpdate((event) => {
                    handleCornerDrag('bottomRight', event.absoluteX, event.absoluteY);
                  })}>
                  <View style={[styles.handle, styles.bottomRight]} />
                </GestureDetector>
              </View>
              
              <View style={[styles.overlaySection, { flex: 1 }]} />
            </View>
            
            {/* Bottom overlay */}
            <View style={[styles.overlaySection, { flex: 1 }]} />
          </View>
        </View>

        {/* Controls */}
        <View style={styles.controls}>
          <Text style={styles.controlsTitle}>Crop Ratio</Text>
          <View style={styles.ratioButtons}>
            <TouchableOpacity
              style={styles.ratioButton}
              onPress={() => setCropRatio(0)} // Free form
            >
              <Text style={styles.ratioButtonText}>Free</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.ratioButton}
              onPress={() => setCropRatio(1)} // Square
            >
              <Text style={styles.ratioButtonText}>1:1</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.ratioButton}
              onPress={() => setCropRatio(16/9)} // Landscape
            >
              <Text style={styles.ratioButtonText}>16:9</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.ratioButton}
              onPress={() => setCropRatio(9/16)} // Portrait
            >
              <Text style={styles.ratioButtonText}>9:16</Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity style={styles.resetButton} onPress={resetCrop}>
            <Ionicons name="refresh-outline" size={20} color={COLORS.primary} />
            <Text style={styles.resetButtonText}>Reset</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: COLORS.background,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  saveText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.primary,
  },
  imageContainer: {
    flex: 1,
    position: 'relative',
  },
  image: {
    width: screenWidth,
    height: screenHeight - 200,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlaySection: {
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  middleSection: {
    flexDirection: 'row',
  },
  cropArea: {
    borderWidth: 2,
    borderColor: 'white',
    position: 'relative',
  },
  handle: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: 'white',
    borderRadius: 10,
  },
  topLeft: {
    top: -10,
    left: -10,
  },
  topRight: {
    top: -10,
    right: -10,
  },
  bottomLeft: {
    bottom: -10,
    left: -10,
  },
  bottomRight: {
    bottom: -10,
    right: -10,
  },
  controls: {
    backgroundColor: COLORS.background,
    padding: 16,
  },
  controlsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  ratioButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  ratioButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: COLORS.border,
    backgroundColor: COLORS.surface,
  },
  ratioButtonText: {
    fontSize: 14,
    color: COLORS.text,
    fontWeight: '500',
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    gap: 8,
  },
  resetButtonText: {
    fontSize: 16,
    color: COLORS.primary,
    fontWeight: '500',
  },
});
