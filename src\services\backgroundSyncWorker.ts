/**
 * Background Sync Worker for IraC<PERSON>
 * Handles background synchronization when app is backgrounded
 * Similar to <PERSON><PERSON><PERSON><PERSON>'s background sync system
 */

import { AppState, AppStateStatus } from 'react-native';
import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import { networkStateManager } from './networkStateManager';
import { outboxService } from './outboxService';
import { messageSyncService } from './messageSyncService';
import { enhancedContactSyncService } from './enhancedContactSyncService';
import { avatarVersioningService } from './avatarVersioningService';
import { offlineDatabaseService } from './offlineDatabase';

const BACKGROUND_SYNC_TASK = 'IRACHAT_BACKGROUND_SYNC';
const BACKGROUND_FETCH_INTERVAL = 15000; // 15 seconds minimum

export interface BackgroundSyncConfig {
  enableMessageSync: boolean;
  enableContactSync: boolean;
  enableAvatarSync: boolean;
  enableMediaSync: boolean;
  syncInterval: number;
  maxSyncDuration: number;
}

export interface BackgroundSyncStats {
  lastSyncTime: number;
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  messagesSynced: number;
  contactsSynced: number;
  avatarsSynced: number;
  isRegistered: boolean;
  isRunning: boolean;
}

class BackgroundSyncWorker {
  private isInitialized = false;
  private appState: AppStateStatus = 'active';
  private config: BackgroundSyncConfig = {
    enableMessageSync: true,
    enableContactSync: true,
    enableAvatarSync: true,
    enableMediaSync: true,
    syncInterval: 30000, // 30 seconds
    maxSyncDuration: 25000, // 25 seconds (leave buffer for iOS)
  };
  
  private stats: BackgroundSyncStats = {
    lastSyncTime: 0,
    totalSyncs: 0,
    successfulSyncs: 0,
    failedSyncs: 0,
    messagesSynced: 0,
    contactsSynced: 0,
    avatarsSynced: 0,
    isRegistered: false,
    isRunning: false,
  };

  /**
   * Initialize background sync worker
   */
  async initialize(config?: Partial<BackgroundSyncConfig>): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ Background sync worker already initialized');
      return;
    }

    console.log('🔄 Initializing Background Sync Worker...');

    // Update config
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // Define the background task
    this.defineBackgroundTask();

    // Register background fetch
    await this.registerBackgroundFetch();

    // Listen for app state changes
    this.setupAppStateListener();

    // Listen for network changes
    networkStateManager.addListener('backgroundSync', this.handleNetworkChange.bind(this));

    this.isInitialized = true;
    console.log('✅ Background Sync Worker initialized');
  }

  /**
   * Define the background sync task
   */
  private defineBackgroundTask(): void {
    TaskManager.defineTask(BACKGROUND_SYNC_TASK, async () => {
      console.log('🔄 Background sync task started');
      this.stats.isRunning = true;
      this.stats.totalSyncs++;

      try {
        const syncStartTime = Date.now();
        
        // Perform sync operations with timeout
        const syncPromise = this.performBackgroundSync();
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Sync timeout')), this.config.maxSyncDuration)
        );

        await Promise.race([syncPromise, timeoutPromise]);

        this.stats.successfulSyncs++;
        this.stats.lastSyncTime = Date.now();
        
        console.log(`✅ Background sync completed in ${Date.now() - syncStartTime}ms`);
        
        return BackgroundFetch.BackgroundFetchResult.NewData;
      } catch (error) {
        console.error('❌ Background sync failed:', error);
        this.stats.failedSyncs++;
        
        return BackgroundFetch.BackgroundFetchResult.Failed;
      } finally {
        this.stats.isRunning = false;
      }
    });
  }

  /**
   * Register background fetch
   */
  private async registerBackgroundFetch(): Promise<void> {
    try {
      const status = await BackgroundFetch.getStatusAsync();
      
      if (status === BackgroundFetch.BackgroundFetchStatus.Available) {
        await BackgroundFetch.registerTaskAsync(BACKGROUND_SYNC_TASK, {
          minimumInterval: BACKGROUND_FETCH_INTERVAL,
          stopOnTerminate: false,
          startOnBoot: true,
        });
        
        this.stats.isRegistered = true;
        console.log('✅ Background fetch registered');
      } else {
        console.warn('⚠️ Background fetch not available:', status);
      }
    } catch (error) {
      console.error('❌ Failed to register background fetch:', error);
    }
  }

  /**
   * Setup app state listener
   */
  private setupAppStateListener(): void {
    AppState.addEventListener('change', this.handleAppStateChange.bind(this));
  }

  /**
   * Handle app state changes
   */
  private handleAppStateChange(nextAppState: AppStateStatus): void {
    console.log(`📱 App state changed: ${this.appState} -> ${nextAppState}`);
    
    if (this.appState === 'active' && nextAppState.match(/inactive|background/)) {
      // App is going to background
      this.onAppBackground();
    } else if (this.appState.match(/inactive|background/) && nextAppState === 'active') {
      // App is coming to foreground
      this.onAppForeground();
    }
    
    this.appState = nextAppState;
  }

  /**
   * Handle app going to background
   */
  private async onAppBackground(): Promise<void> {
    console.log('📱 App going to background, performing final sync...');
    
    try {
      // Perform a quick sync before backgrounding
      await this.performQuickSync();
    } catch (error) {
      console.error('❌ Background sync on app background failed:', error);
    }
  }

  /**
   * Handle app coming to foreground
   */
  private async onAppForeground(): Promise<void> {
    console.log('📱 App coming to foreground, performing catch-up sync...');
    
    try {
      // Perform a comprehensive sync when returning to foreground
      await this.performForegroundSync();
    } catch (error) {
      console.error('❌ Foreground sync failed:', error);
    }
  }

  /**
   * Perform background sync
   */
  private async performBackgroundSync(): Promise<void> {
    if (!networkStateManager.isOnline()) {
      console.log('📵 Offline, skipping background sync');
      return;
    }

    console.log('🔄 Performing background sync...');
    const syncTasks: Promise<any>[] = [];

    // Message sync (highest priority)
    if (this.config.enableMessageSync) {
      syncTasks.push(this.syncMessages());
    }

    // Contact sync (medium priority)
    if (this.config.enableContactSync) {
      syncTasks.push(this.syncContacts());
    }

    // Avatar sync (low priority)
    if (this.config.enableAvatarSync) {
      syncTasks.push(this.syncAvatars());
    }

    // Execute all sync tasks
    await Promise.allSettled(syncTasks);
  }

  /**
   * Perform quick sync (for app backgrounding)
   */
  private async performQuickSync(): Promise<void> {
    if (!networkStateManager.isOnline()) {
      return;
    }

    console.log('⚡ Performing quick sync...');
    
    // Only sync critical items quickly
    const quickTasks = [
      this.syncMessages(), // Most important
    ];

    await Promise.allSettled(quickTasks);
  }

  /**
   * Perform foreground sync (when app returns)
   */
  private async performForegroundSync(): Promise<void> {
    if (!networkStateManager.isOnline()) {
      return;
    }

    console.log('🔄 Performing foreground sync...');
    
    // Comprehensive sync when returning to app
    const foregroundTasks = [
      this.syncMessages(),
      this.syncContacts(),
      this.syncAvatars(),
    ];

    await Promise.allSettled(foregroundTasks);
  }

  /**
   * Sync messages
   */
  private async syncMessages(): Promise<void> {
    try {
      console.log('📤 Syncing messages...');
      
      // Process outbox
      await outboxService.processOutbox();
      
      // Sync pending messages
      await messageSyncService.syncPendingMessages();
      
      this.stats.messagesSynced++;
      console.log('✅ Messages synced');
    } catch (error) {
      console.error('❌ Message sync failed:', error);
      throw error;
    }
  }

  /**
   * Sync contacts
   */
  private async syncContacts(): Promise<void> {
    try {
      console.log('👥 Syncing contacts...');
      
      // Perform background contact sync
      await enhancedContactSyncService.syncDeviceContacts('current_user_id'); // Get from auth
      
      this.stats.contactsSynced++;
      console.log('✅ Contacts synced');
    } catch (error) {
      console.error('❌ Contact sync failed:', error);
      throw error;
    }
  }

  /**
   * Sync avatars
   */
  private async syncAvatars(): Promise<void> {
    try {
      console.log('👤 Syncing avatars...');
      
      // Get recent contacts for avatar sync
      const db = offlineDatabaseService.getDatabase();
      const recentContacts = await db.getAllAsync(`
        SELECT userId FROM contacts 
        WHERE userId IS NOT NULL 
        AND updatedAt > ?
        ORDER BY updatedAt DESC
        LIMIT 10
      `, [Date.now() - 24 * 60 * 60 * 1000]); // Last 24 hours

      const userIds = recentContacts.map((row: any) => row.userId);
      
      if (userIds.length > 0) {
        await avatarVersioningService.syncAvatarsForUsers(userIds);
      }
      
      this.stats.avatarsSynced++;
      console.log('✅ Avatars synced');
    } catch (error) {
      console.error('❌ Avatar sync failed:', error);
      throw error;
    }
  }

  /**
   * Handle network state changes
   */
  private handleNetworkChange(state: any): void {
    if (state.isOnline && this.appState === 'background') {
      console.log('🌐 Network restored in background, triggering sync...');
      // Trigger background sync when network is restored
      this.performBackgroundSync().catch(error => {
        console.error('❌ Network restoration sync failed:', error);
      });
    }
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<BackgroundSyncConfig>): void {
    this.config = { ...this.config, ...updates };
    console.log('⚙️ Background sync config updated:', updates);
  }

  /**
   * Get sync statistics
   */
  getStats(): BackgroundSyncStats {
    return { ...this.stats };
  }

  /**
   * Force sync now (for testing/manual trigger)
   */
  async forceSyncNow(): Promise<void> {
    if (this.stats.isRunning) {
      console.log('⏳ Sync already running');
      return;
    }

    console.log('🔄 Force sync triggered');
    await this.performBackgroundSync();
  }

  /**
   * Unregister background fetch
   */
  async unregister(): Promise<void> {
    try {
      await BackgroundFetch.unregisterTaskAsync(BACKGROUND_SYNC_TASK);
      this.stats.isRegistered = false;
      console.log('✅ Background fetch unregistered');
    } catch (error) {
      console.error('❌ Failed to unregister background fetch:', error);
    }
  }

  /**
   * Check if background sync is supported
   */
  async isSupported(): Promise<boolean> {
    const status = await BackgroundFetch.getStatusAsync();
    return status === BackgroundFetch.BackgroundFetchStatus.Available;
  }

  /**
   * Get background fetch status
   */
  async getBackgroundFetchStatus(): Promise<BackgroundFetch.BackgroundFetchStatus> {
    const status = await BackgroundFetch.getStatusAsync();
    return status || BackgroundFetch.BackgroundFetchStatus.Denied;
  }
}

export const backgroundSyncWorker = new BackgroundSyncWorker();
