import React, { useState } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Switch,
  Pressable,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface GroupSettings {
  whoCanSendMessages: 'everyone' | 'admins_only';
  whoCanAddMembers: 'everyone' | 'admins_only';
  whoCanEditGroupInfo: 'admins_only';
  disappearingMessages: boolean;
  disappearingMessagesDuration: number;
  allowMemberInvites: boolean;
  requireAdminApproval: boolean;
  muteNotifications: boolean;
  showReadReceipts: boolean;
}

interface GroupInfo {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  createdBy: string;
  createdAt: Date;
  memberCount: number;
  settings: GroupSettings;
  pinnedMessages: string[];
  rules?: string[];
  category?: string;
  isPublic: boolean;
  inviteLink?: string;
}

interface GroupSettingsModalProps {
  visible: boolean;
  onClose: () => void;
  groupInfo: GroupInfo | null;
  currentUserRole: 'owner' | 'admin' | 'member';
  onUpdateSettings?: (settings: Partial<GroupSettings>) => void;
}

export const GroupSettingsModal: React.FC<GroupSettingsModalProps> = ({
  visible,
  onClose,
  groupInfo,
  currentUserRole,
  onUpdateSettings,
}) => {
  const [localSettings, setLocalSettings] = useState<GroupSettings>(
    groupInfo?.settings || {
      whoCanSendMessages: 'everyone',
      whoCanAddMembers: 'admins_only',
      whoCanEditGroupInfo: 'admins_only',
      disappearingMessages: false,
      disappearingMessagesDuration: 24,
      allowMemberInvites: true,
      requireAdminApproval: false,
      muteNotifications: false,
      showReadReceipts: true,
    }
  );

  const canEdit = currentUserRole === 'owner' || currentUserRole === 'admin';

  const handleSettingChange = (key: keyof GroupSettings, value: any) => {
    if (!canEdit) {
      Alert.alert('Permission Denied', 'Only admins can change group settings');
      return;
    }

    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    onUpdateSettings?.(newSettings);
  };

  const SettingRow = ({
    title,
    description,
    value,
    onValueChange,
    type = 'switch',
    options,
  }: {
    title: string;
    description?: string;
    value: any;
    onValueChange: (value: any) => void;
    type?: 'switch' | 'select';
    options?: { label: string; value: any }[];
  }) => (
    <View
      style={{
        paddingVertical: 16,
        paddingHorizontal: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#E5E7EB',
      }}
    >
      <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
        <View style={{ flex: 1, marginRight: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: '500', color: '#333' }}>
            {title}
          </Text>
          {description && (
            <Text style={{ fontSize: 14, color: '#666', marginTop: 4 }}>
              {description}
            </Text>
          )}
        </View>

        {type === 'switch' ? (
          <Switch
            value={value}
            onValueChange={onValueChange}
            trackColor={{ false: '#E5E7EB', true: '#87CEEB' }}
            thumbColor={value ? '#FFFFFF' : '#FFFFFF'}
            disabled={!canEdit}
          />
        ) : (
          <TouchableOpacity
            onPress={() => {
              if (!canEdit) return;
              Alert.alert(
                title,
                'Select an option:',
                options?.map((option) => ({
                  text: option.label,
                  onPress: () => onValueChange(option.value),
                })) || []
              );
            }}
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              paddingVertical: 8,
              paddingHorizontal: 12,
              backgroundColor: '#F8F9FA',
              borderRadius: 8,
            }}
          >
            <Text style={{ fontSize: 14, color: '#333', marginRight: 8 }}>
              {options?.find((opt) => opt.value === value)?.label || value}
            </Text>
            <Ionicons name="chevron-down" size={16} color="#666" />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <Pressable
        style={{
          flex: 1,
          backgroundColor: 'rgba(0,0,0,0.5)',
          justifyContent: 'flex-end',
        }}
        onPress={onClose}
      >
        <View
          style={{
            backgroundColor: 'white',
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            maxHeight: '80%',
          }}
        >
          {/* Header */}
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: 20,
              borderBottomWidth: 1,
              borderBottomColor: '#E5E7EB',
            }}
          >
            <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#333' }}>
              Group Settings
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={{ flex: 1 }}>
            {/* Permissions Section */}
            <View style={{ marginTop: 20 }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#333',
                  paddingHorizontal: 20,
                  marginBottom: 16,
                }}
              >
                Permissions
              </Text>

              <SettingRow
                title="Who can send messages"
                description="Control who can send messages in this group"
                value={localSettings.whoCanSendMessages}
                onValueChange={(value) => handleSettingChange('whoCanSendMessages', value)}
                type="select"
                options={[
                  { label: 'All members', value: 'everyone' },
                  { label: 'Only admins', value: 'admins_only' },
                ]}
              />

              <SettingRow
                title="Who can add members"
                description="Control who can add new members to this group"
                value={localSettings.whoCanAddMembers}
                onValueChange={(value) => handleSettingChange('whoCanAddMembers', value)}
                type="select"
                options={[
                  { label: 'All members', value: 'everyone' },
                  { label: 'Only admins', value: 'admins_only' },
                ]}
              />

              <SettingRow
                title="Allow member invites"
                description="Allow members to invite others via invite link"
                value={localSettings.allowMemberInvites}
                onValueChange={(value) => handleSettingChange('allowMemberInvites', value)}
              />

              <SettingRow
                title="Require admin approval"
                description="New members need admin approval to join"
                value={localSettings.requireAdminApproval}
                onValueChange={(value) => handleSettingChange('requireAdminApproval', value)}
              />
            </View>

            {/* Privacy Section */}
            <View style={{ marginTop: 20 }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#333',
                  paddingHorizontal: 20,
                  marginBottom: 16,
                }}
              >
                Privacy
              </Text>

              <SettingRow
                title="Disappearing messages"
                description="Messages will automatically delete after set time"
                value={localSettings.disappearingMessages}
                onValueChange={(value) => handleSettingChange('disappearingMessages', value)}
              />

              <SettingRow
                title="Show read receipts"
                description="Show when messages have been read"
                value={localSettings.showReadReceipts}
                onValueChange={(value) => handleSettingChange('showReadReceipts', value)}
              />
            </View>

            {/* Notifications Section */}
            <View style={{ marginTop: 20, marginBottom: 40 }}>
              <Text
                style={{
                  fontSize: 18,
                  fontWeight: 'bold',
                  color: '#333',
                  paddingHorizontal: 20,
                  marginBottom: 16,
                }}
              >
                Notifications
              </Text>

              <SettingRow
                title="Mute notifications"
                description="Turn off notifications for this group"
                value={localSettings.muteNotifications}
                onValueChange={(value) => handleSettingChange('muteNotifications', value)}
              />
            </View>
          </ScrollView>

          {!canEdit && (
            <View
              style={{
                backgroundColor: '#FEF3C7',
                padding: 16,
                margin: 20,
                borderRadius: 12,
                flexDirection: 'row',
                alignItems: 'center',
              }}
            >
              <Ionicons name="information-circle" size={20} color="#F59E0B" />
              <Text style={{ marginLeft: 12, fontSize: 14, color: '#92400E', flex: 1 }}>
                Only group admins can modify these settings
              </Text>
            </View>
          )}
        </View>
      </Pressable>
    </Modal>
  );
};
