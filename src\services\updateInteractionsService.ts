// 🔥 UPDATE INTERACTIONS SERVICE - Complete Firebase sync for all story interactions
import {
  doc,
  setDoc,
  getDoc,
  serverTimestamp,
  updateDoc,
  deleteDoc,
  arrayUnion,
  arrayRemove,
  increment,
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { networkStateManager } from './networkStateManager';
import { offlineDatabaseService } from './offlineDatabase';

// Firebase Collections for Update Interactions
const COLLECTIONS = {
  UPDATES: 'updates',
  UPDATE_COMMENTS: 'updateComments',
  UPDATE_LIKES: 'updateLikes', 
  UPDATE_SHARES: 'updateShares',
  UPDATE_SAVES: 'updateSaves',
  UPDATE_VIEWS: 'updateViews',
  UPDATE_DOWNLOADS: 'updateDownloads',
  UPDATE_DELETES: 'updateDeletes',
  UPDATE_REPORTS: 'updateReports',
  UPDATE_UPLOADS: 'updateUploads'
};

interface InteractionData {
  updateId: string;
  userId: string;
  userName?: string;
  userAvatar?: string;
  timestamp: Date;
  metadata?: any;
}

class UpdateInteractionsService {
  
  /**
   * LIKE INTERACTION - Sync to updateLikes collection
   */
  async likeUpdate(updateId: string, userId: string, userName: string, userAvatar?: string): Promise<boolean> {
    try {
      // Skip Firebase operations for offline updates
      if (updateId.startsWith('offline_')) {
        console.log('📱 Skipping Firebase sync for offline update:', updateId);
        return true;
      }

      const likeId = `${updateId}_${userId}`;
      const likeData: InteractionData = {
        updateId,
        userId,
        userName,
        userAvatar,
        timestamp: new Date(),
      };

      if (networkStateManager.isOnline()) {
        // Check if document exists before updating
        const updateRef = doc(db, COLLECTIONS.UPDATES, updateId);
        const updateDocSnapshot = await getDoc(updateRef);

        if (!updateDocSnapshot.exists()) {
          console.warn('⚠️ Update document does not exist:', updateId);
          return false;
        }

        // Add to updateLikes collection
        const likeRef = doc(db, COLLECTIONS.UPDATE_LIKES, likeId);
        await setDoc(likeRef, {
          ...likeData,
          timestamp: serverTimestamp(),
        });

        // Update likes array in main updates collection
        await updateDoc(updateRef, {
          likes: arrayUnion(userId),
          likesCount: increment(1),
        });

        console.log('✅ Like synced to Firebase:', likeId);
      } else {
        // Store offline for later sync
        await this.storeOfflineInteraction('like', likeData);
      }

      return true;
    } catch (error) {
      console.error('❌ Error liking update:', error);
      return false;
    }
  }

  /**
   * UNLIKE INTERACTION - Remove from updateLikes collection
   */
  async unlikeUpdate(updateId: string, userId: string): Promise<boolean> {
    try {
      const likeId = `${updateId}_${userId}`;

      if (networkStateManager.isOnline()) {
        // Remove from updateLikes collection
        const likeRef = doc(db, COLLECTIONS.UPDATE_LIKES, likeId);
        await deleteDoc(likeRef);

        // Update likes array in main updates collection
        const updateRef = doc(db, COLLECTIONS.UPDATES, updateId);
        await updateDoc(updateRef, {
          likes: arrayRemove(userId),
          likesCount: increment(-1),
        });

        console.log('✅ Unlike synced to Firebase:', likeId);
      } else {
        // Store offline for later sync
        await this.storeOfflineInteraction('unlike', { updateId, userId, timestamp: new Date() });
      }

      return true;
    } catch (error) {
      console.error('❌ Error unliking update:', error);
      return false;
    }
  }

  /**
   * SHARE INTERACTION - Sync to updateShares collection
   */
  async shareUpdate(updateId: string, userId: string, userName: string, shareType: string = 'general'): Promise<boolean> {
    try {
      const shareId = `${updateId}_${userId}_${Date.now()}`;
      const shareData: InteractionData = {
        updateId,
        userId,
        userName,
        timestamp: new Date(),
        metadata: { shareType }
      };

      if (networkStateManager.isOnline()) {
        // Add to updateShares collection
        const shareRef = doc(db, COLLECTIONS.UPDATE_SHARES, shareId);
        await setDoc(shareRef, {
          ...shareData,
          timestamp: serverTimestamp(),
        });

        // Update shares array in main updates collection
        const updateRef = doc(db, COLLECTIONS.UPDATES, updateId);
        await updateDoc(updateRef, {
          shares: arrayUnion(userId),
          sharesCount: increment(1),
        });

        console.log('✅ Share synced to Firebase:', shareId);
      } else {
        await this.storeOfflineInteraction('share', shareData);
      }

      return true;
    } catch (error) {
      console.error('❌ Error sharing update:', error);
      return false;
    }
  }

  /**
   * SAVE INTERACTION - Sync to updateSaves collection
   */
  async saveUpdate(updateId: string, userId: string, userName: string): Promise<boolean> {
    try {
      const saveId = `${updateId}_${userId}`;
      const saveData: InteractionData = {
        updateId,
        userId,
        userName,
        timestamp: new Date(),
      };

      if (networkStateManager.isOnline()) {
        // Add to updateSaves collection
        const saveRef = doc(db, COLLECTIONS.UPDATE_SAVES, saveId);
        await setDoc(saveRef, {
          ...saveData,
          timestamp: serverTimestamp(),
        });

        // Update saves array in main updates collection
        const updateRef = doc(db, COLLECTIONS.UPDATES, updateId);
        await updateDoc(updateRef, {
          saves: arrayUnion(userId),
          savesCount: increment(1),
        });

        console.log('✅ Save synced to Firebase:', saveId);
      } else {
        await this.storeOfflineInteraction('save', saveData);
      }

      return true;
    } catch (error) {
      console.error('❌ Error saving update:', error);
      return false;
    }
  }

  /**
   * VIEW INTERACTION - Sync to updateViews collection
   */
  async viewUpdate(updateId: string, userId: string, viewDuration: number = 0): Promise<boolean> {
    try {
      // Skip Firebase operations for offline updates
      if (updateId.startsWith('offline_')) {
        console.log('📱 Skipping Firebase sync for offline update view:', updateId);
        return true;
      }

      const viewId = `${updateId}_${userId}_${Date.now()}`;
      const viewData: InteractionData = {
        updateId,
        userId,
        timestamp: new Date(),
        metadata: { viewDuration }
      };

      if (networkStateManager.isOnline()) {
        // Check if document exists before updating
        const updateRef = doc(db, COLLECTIONS.UPDATES, updateId);
        const updateDocSnapshot = await getDoc(updateRef);

        if (!updateDocSnapshot.exists()) {
          console.warn('⚠️ Update document does not exist for view:', updateId);
          return false;
        }

        // Add to updateViews collection
        const viewRef = doc(db, COLLECTIONS.UPDATE_VIEWS, viewId);
        await setDoc(viewRef, {
          ...viewData,
          timestamp: serverTimestamp(),
        });

        // Update views array in main updates collection (only if not already viewed)
        const currentViews = updateDocSnapshot.data()?.views || [];

        if (!currentViews.includes(userId)) {
          await updateDoc(updateRef, {
            views: arrayUnion(userId),
            viewsCount: increment(1),
          });
        }

        console.log('✅ View synced to Firebase:', viewId);
      } else {
        await this.storeOfflineInteraction('view', viewData);
      }

      return true;
    } catch (error) {
      console.error('❌ Error recording view:', error);
      return false;
    }
  }

  /**
   * DOWNLOAD INTERACTION - Sync to updateDownloads collection
   */
  async downloadUpdate(updateId: string, userId: string, userName: string): Promise<boolean> {
    try {
      const downloadId = `${updateId}_${userId}_${Date.now()}`;
      const downloadData: InteractionData = {
        updateId,
        userId,
        userName,
        timestamp: new Date(),
      };

      if (networkStateManager.isOnline()) {
        // Add to updateDownloads collection
        const downloadRef = doc(db, COLLECTIONS.UPDATE_DOWNLOADS, downloadId);
        await setDoc(downloadRef, {
          ...downloadData,
          timestamp: serverTimestamp(),
        });

        // Update downloads count in main updates collection
        const updateRef = doc(db, COLLECTIONS.UPDATES, updateId);
        await updateDoc(updateRef, {
          downloadsCount: increment(1),
        });

        console.log('✅ Download synced to Firebase:', downloadId);
      } else {
        await this.storeOfflineInteraction('download', downloadData);
      }

      return true;
    } catch (error) {
      console.error('❌ Error recording download:', error);
      return false;
    }
  }

  /**
   * COMMENT INTERACTION - Sync to updateComments collection
   */
  async commentOnUpdate(updateId: string, userId: string, userName: string, commentText: string, userAvatar?: string): Promise<string | null> {
    try {
      const commentId = `${updateId}_${userId}_${Date.now()}`;
      const commentData = {
        updateId,
        userId,
        userName,
        userAvatar,
        text: commentText,
        timestamp: new Date(),
        likes: [],
        likesCount: 0,
      };

      if (networkStateManager.isOnline()) {
        // Add to updateComments collection
        const commentRef = doc(db, COLLECTIONS.UPDATE_COMMENTS, commentId);
        await setDoc(commentRef, {
          ...commentData,
          timestamp: serverTimestamp(),
        });

        // Update comments count in main updates collection
        const updateRef = doc(db, COLLECTIONS.UPDATES, updateId);
        await updateDoc(updateRef, {
          commentsCount: increment(1),
        });

        console.log('✅ Comment synced to Firebase:', commentId);
        return commentId;
      } else {
        await this.storeOfflineInteraction('comment', commentData);
        return commentId;
      }
    } catch (error) {
      console.error('❌ Error adding comment:', error);
      return null;
    }
  }

  /**
   * Store offline interaction for later sync
   */
  private async storeOfflineInteraction(type: string, data: any): Promise<void> {
    try {
      const offlineDb = offlineDatabaseService.getDatabase();
      await offlineDb.runAsync(`
        INSERT OR REPLACE INTO offline_interactions (
          id, type, data, timestamp, synced
        ) VALUES (?, ?, ?, ?, ?)
      `, [
        `${type}_${Date.now()}_${Math.random()}`,
        type,
        JSON.stringify(data),
        new Date().toISOString(),
        0
      ]);
      console.log(`📱 Stored ${type} interaction offline`);
    } catch (error) {
      console.error(`❌ Error storing ${type} interaction offline:`, error);
    }
  }

  /**
   * Sync all offline interactions when online
   */
  async syncOfflineInteractions(): Promise<void> {
    if (!networkStateManager.isOnline()) return;

    try {
      const offlineDb = offlineDatabaseService.getDatabase();
      const interactions = await offlineDb.getAllAsync(`
        SELECT * FROM offline_interactions WHERE synced = 0
      `);

      for (const interaction of interactions) {
        const data = JSON.parse((interaction as any).data);

        switch ((interaction as any).type) {
          case 'like':
            await this.likeUpdate(data.updateId, data.userId, data.userName, data.userAvatar);
            break;
          case 'unlike':
            await this.unlikeUpdate(data.updateId, data.userId);
            break;
          case 'share':
            await this.shareUpdate(data.updateId, data.userId, data.userName, data.metadata?.shareType);
            break;
          case 'save':
            await this.saveUpdate(data.updateId, data.userId, data.userName);
            break;
          case 'view':
            await this.viewUpdate(data.updateId, data.userId, data.metadata?.viewDuration);
            break;
          case 'download':
            await this.downloadUpdate(data.updateId, data.userId, data.userName);
            break;
          case 'comment':
            await this.commentOnUpdate(data.updateId, data.userId, data.userName, data.text, data.userAvatar);
            break;
        }

        // Mark as synced
        await offlineDb.runAsync(`
          UPDATE offline_interactions SET synced = 1 WHERE id = ?
        `, [(interaction as any).id]);
      }

      console.log(`✅ Synced ${interactions.length} offline interactions`);
    } catch (error) {
      console.error('❌ Error syncing offline interactions:', error);
    }
  }
}

export const updateInteractionsService = new UpdateInteractionsService();
