/**
 * Music Picker Modal Component
 * WhatsApp-style music selection for stories
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  FlatList,
  TextInput,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { musicPickerService, MusicTrack, MusicCategory } from '../services/musicPickerService';
import Slider from '@react-native-community/slider';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MusicPickerModalProps {
  visible: boolean;
  onClose: () => void;
  onSelectTrack: (track: MusicTrack) => void;
}

export const MusicPickerModal: React.FC<MusicPickerModalProps> = ({
  visible,
  onClose,
  onSelectTrack,
}) => {
  const [categories, setCategories] = useState<MusicCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('trending');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<MusicTrack[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [playingTrack, setPlayingTrack] = useState<string | null>(null);
  const [volume, setVolume] = useState(0.5);

  useEffect(() => {
    if (visible) {
      loadMusicCategories();
    } else {
      // Clean up when modal closes
      musicPickerService.stopPreview();
      setPlayingTrack(null);
      setSearchQuery('');
      setSearchResults([]);
    }
  }, [visible]);

  const loadMusicCategories = () => {
    const musicCategories = musicPickerService.getMusicCategories();
    setCategories(musicCategories);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      setIsSearching(true);
      const results = musicPickerService.searchTracks(query);
      setSearchResults(results);
      setIsSearching(false);
    } else {
      setSearchResults([]);
    }
  };

  const handlePlayPreview = async (track: MusicTrack) => {
    if (playingTrack === track.id) {
      // Stop if already playing
      await musicPickerService.stopPreview();
      setPlayingTrack(null);
    } else {
      // Play new track
      await musicPickerService.previewTrack(track);
      setPlayingTrack(track.id);
    }
  };

  const handleVolumeChange = async (newVolume: number) => {
    setVolume(newVolume);
    await musicPickerService.setVolume(newVolume);
  };

  const handleSelectTrack = async (track: MusicTrack) => {
    await musicPickerService.stopPreview();
    setPlayingTrack(null);
    onSelectTrack(track);
    onClose();
  };

  const renderTrackItem = ({ item }: { item: MusicTrack }) => (
    <View style={styles.trackItem}>
      <TouchableOpacity
        style={styles.trackInfo}
        onPress={() => handleSelectTrack(item)}
      >
        <View style={styles.trackDetails}>
          <Text style={styles.trackTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={styles.trackArtist} numberOfLines={1}>
            {item.artist} • {musicPickerService.formatDuration(item.duration)}
          </Text>
          {item.genre && (
            <Text style={styles.trackGenre}>{item.genre}</Text>
          )}
        </View>
        {item.isPopular && (
          <View style={styles.popularBadge}>
            <Ionicons name="trending-up" size={12} color="#25D366" />
          </View>
        )}
      </TouchableOpacity>
      
      <TouchableOpacity
        style={styles.playButton}
        onPress={() => handlePlayPreview(item)}
      >
        <Ionicons
          name={playingTrack === item.id ? 'pause' : 'play'}
          size={20}
          color="#25D366"
        />
      </TouchableOpacity>
    </View>
  );

  const renderCategoryTab = (category: MusicCategory) => (
    <TouchableOpacity
      key={category.id}
      style={[
        styles.categoryTab,
        selectedCategory === category.id && styles.selectedCategoryTab
      ]}
      onPress={() => setSelectedCategory(category.id)}
    >
      <Text
        style={[
          styles.categoryTabText,
          selectedCategory === category.id && styles.selectedCategoryTabText
        ]}
      >
        {category.name}
      </Text>
    </TouchableOpacity>
  );

  const getCurrentTracks = (): MusicTrack[] => {
    if (searchQuery.trim()) {
      return searchResults;
    }
    
    const category = categories.find(cat => cat.id === selectedCategory);
    return category?.tracks || [];
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Ionicons name="close" size={24} color="#000000" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Add Music</Text>
          <View style={{ width: 24 }} />
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color="#666666" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search for music..."
            value={searchQuery}
            onChangeText={handleSearch}
            placeholderTextColor="#999999"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => handleSearch('')}>
              <Ionicons name="close-circle" size={20} color="#666666" />
            </TouchableOpacity>
          )}
        </View>

        {/* Volume Control */}
        {playingTrack && (
          <View style={styles.volumeContainer}>
            <Ionicons name="volume-low" size={16} color="#666666" />
            <Slider
              style={styles.volumeSlider}
              minimumValue={0}
              maximumValue={1}
              value={volume}
              onValueChange={handleVolumeChange}
              minimumTrackTintColor="#25D366"
              maximumTrackTintColor="#E0E0E0"
              thumbStyle={styles.volumeThumb}
            />
            <Ionicons name="volume-high" size={16} color="#666666" />
          </View>
        )}

        {/* Category Tabs */}
        {!searchQuery.trim() && (
          <View style={styles.categoryTabs}>
            {categories.map(renderCategoryTab)}
          </View>
        )}

        {/* Tracks List */}
        <FlatList
          data={getCurrentTracks()}
          renderItem={renderTrackItem}
          keyExtractor={(item) => item.id}
          style={styles.tracksList}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              {isSearching ? (
                <ActivityIndicator size="large" color="#25D366" />
              ) : (
                <>
                  <Ionicons name="musical-notes" size={48} color="#CCCCCC" />
                  <Text style={styles.emptyText}>
                    {searchQuery.trim() ? 'No music found' : 'No music available'}
                  </Text>
                </>
              )}
            </View>
          }
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  closeButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: '#000000',
  },
  volumeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F9F9F9',
  },
  volumeSlider: {
    flex: 1,
    marginHorizontal: 12,
    height: 20,
  },
  volumeThumb: {
    backgroundColor: '#25D366',
    width: 16,
    height: 16,
  },
  categoryTabs: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  categoryTab: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 16,
    backgroundColor: '#F0F0F0',
  },
  selectedCategoryTab: {
    backgroundColor: '#25D366',
  },
  categoryTabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  selectedCategoryTabText: {
    color: '#FFFFFF',
  },
  tracksList: {
    flex: 1,
  },
  trackItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F5F5F5',
  },
  trackInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  trackDetails: {
    flex: 1,
  },
  trackTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  trackArtist: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  trackGenre: {
    fontSize: 12,
    color: '#999999',
  },
  popularBadge: {
    marginLeft: 8,
  },
  playButton: {
    padding: 8,
    marginLeft: 12,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#999999',
    marginTop: 12,
  },
});
