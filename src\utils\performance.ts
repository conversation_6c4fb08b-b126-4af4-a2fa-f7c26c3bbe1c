/**
 * Enhanced Performance utilities for IraChat
 * Optimized for React Native performance with mobile-first approach
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Dimensions, Platform, InteractionManager } from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Device performance characteristics
const isLowEndDevice = SCREEN_WIDTH < 375 || Platform.OS === 'android';
const isTablet = SCREEN_WIDTH >= 768;

// Performance thresholds based on device
const PERFORMANCE_THRESHOLDS = {
  lowEnd: {
    maxListItems: 50,
    imageQuality: 1,
    animationDuration: 200,
    debounceDelay: 300,
  },
  midRange: {
    maxListItems: 100,
    imageQuality: 1,
    animationDuration: 250,
    debounceDelay: 200,
  },
  highEnd: {
    maxListItems: 200,
    imageQuality: 1,
    animationDuration: 300,
    debounceDelay: 150,
  },
};

// Enhanced screen dimensions with performance context
export const useScreenDimensions = () => {
  return useMemo(() => {
    const isSmall = SCREEN_WIDTH < 375;
    const isLarge = SCREEN_WIDTH > 414;
    const performanceProfile = isLowEndDevice ? 'lowEnd' : isTablet ? 'highEnd' : 'midRange';

    return {
      width: SCREEN_WIDTH,
      height: SCREEN_HEIGHT,
      isSmall,
      isLarge,
      isTablet,
      isLowEndDevice,
      platform: Platform.OS,
      performanceProfile,
      thresholds: PERFORMANCE_THRESHOLDS[performanceProfile],
    };
  }, []);
};

// Enhanced optimized list rendering with device-specific settings
export const useOptimizedList = (itemHeight: number = 80) => {
  const { thresholds, isLowEndDevice } = useScreenDimensions();

  const getItemLayout = useCallback((_data: any, index: number) => ({
    length: itemHeight,
    offset: itemHeight * index,
    index,
  }), [itemHeight]);

  const keyExtractor = useCallback((item: any, index: number) =>
    item.id?.toString() || item.key?.toString() || `item-${index}`, []);

  // Performance optimizations based on device
  const listProps = useMemo(() => ({
    removeClippedSubviews: isLowEndDevice,
    maxToRenderPerBatch: isLowEndDevice ? 5 : 10,
    updateCellsBatchingPeriod: isLowEndDevice ? 100 : 50,
    initialNumToRender: Math.min(thresholds.maxListItems / 10, 15),
    windowSize: isLowEndDevice ? 5 : 10,
    getItemLayout,
    keyExtractor,
  }), [isLowEndDevice, thresholds.maxListItems, getItemLayout, keyExtractor]);

  return listProps;
};

// Enhanced debounced search with device-specific delays
export const useDebounce = (value: string, customDelay?: number) => {
  const { thresholds } = useScreenDimensions();
  const delay = customDelay || thresholds.debounceDelay;
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Performance-aware async operations
export const useInteractionManager = () => {
  const runAfterInteractions = useCallback((callback: () => void) => {
    const task = InteractionManager.runAfterInteractions(callback);
    return () => task.cancel();
  }, []);

  const createInteractionHandle = useCallback(() => {
    return InteractionManager.createInteractionHandle();
  }, []);

  const clearInteractionHandle = useCallback((handle: number) => {
    InteractionManager.clearInteractionHandle(handle);
  }, []);

  return {
    runAfterInteractions,
    createInteractionHandle,
    clearInteractionHandle,
  };
};

// Optimized image loading
export const useImageCache = () => {
  const cache = useRef(new Map()).current;

  const getCachedImage = useCallback((uri: string) => {
    return cache.get(uri);
  }, [cache]);

  const setCachedImage = useCallback((uri: string, image: any) => {
    cache.set(uri, image);
  }, [cache]);

  return { getCachedImage, setCachedImage };
};

// Memory optimization
export const useMemoryOptimization = () => {
  const cleanup = useCallback(() => {
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  }, []);

  return { cleanup };
};

// Batch updates for better performance
export const useBatchUpdates = () => {
  const batchRef = useRef<any[]>([]);
  const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const addToBatch = useCallback((update: any) => {
    batchRef.current.push(update);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      // Process batch
      const batch = [...batchRef.current];
      batchRef.current = [];
      
      // Apply all updates at once
      batch.forEach(update => update());
    }, 16); // One frame
  }, []);

  return { addToBatch };
};

// Performance monitoring
export const usePerformanceMonitor = () => {
  const metricsRef = useRef({
    renderCount: 0,
    lastRenderTime: Date.now(),
    averageRenderTime: 0,
  });

  const trackRender = useCallback(() => {
    const now = Date.now();
    const renderTime = now - metricsRef.current.lastRenderTime;
    metricsRef.current.renderCount++;
    metricsRef.current.averageRenderTime =
      (metricsRef.current.averageRenderTime + renderTime) / 2;
    metricsRef.current.lastRenderTime = now;
  }, []);

  const getMetrics = useCallback(() => ({
    ...metricsRef.current,
  }), []);

  const resetMetrics = useCallback(() => {
    metricsRef.current = {
      renderCount: 0,
      lastRenderTime: Date.now(),
      averageRenderTime: 0,
    };
  }, []);

  return { trackRender, getMetrics, resetMetrics };
};

// Throttled function execution
export const useThrottle = (callback: (...args: any[]) => void, delay: number) => {
  const lastRun = useRef(Date.now());

  return useCallback((...args: any[]) => {
    if (Date.now() - lastRun.current >= delay) {
      callback(...args);
      lastRun.current = Date.now();
    }
  }, [callback, delay]);
};

// Optimized state updates
export const useOptimizedState = <T>(initialValue: T) => {
  const [state, setState] = useState(initialValue);
  const pendingUpdate = useRef<T | null>(null);
  const updateTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);

  const setOptimizedState = useCallback((newValue: T | ((prev: T) => T)) => {
    const value = typeof newValue === 'function'
      ? (newValue as (prev: T) => T)(pendingUpdate.current || state)
      : newValue;

    pendingUpdate.current = value;

    if (updateTimeout.current) {
      clearTimeout(updateTimeout.current);
    }

    updateTimeout.current = setTimeout(() => {
      setState(pendingUpdate.current!);
      pendingUpdate.current = null;
    }, 16); // One frame delay
  }, [state]);

  useEffect(() => {
    return () => {
      if (updateTimeout.current) {
        clearTimeout(updateTimeout.current);
      }
    };
  }, []);

  return [state, setOptimizedState] as const;
};

// Memory pressure detection
export const useMemoryPressure = () => {
  const [isUnderPressure, setIsUnderPressure] = useState(false);
  const { isLowEndDevice } = useScreenDimensions();

  useEffect(() => {
    if (isLowEndDevice) {
      // Simulate memory pressure detection for low-end devices
      const checkMemory = () => {
        // In a real implementation, you'd check actual memory usage
        const fakeMemoryUsage = Math.random();
        setIsUnderPressure(fakeMemoryUsage > 0.8);
      };

      const interval = setInterval(checkMemory, 5000);
      return () => clearInterval(interval);
    }
  }, [isLowEndDevice]);

  const forceGarbageCollection = useCallback(() => {
    if (global.gc) {
      global.gc();
    }
  }, []);

  return { isUnderPressure, forceGarbageCollection };
};

// Adaptive quality settings
export const useAdaptiveQuality = () => {
  const { thresholds, isLowEndDevice } = useScreenDimensions();
  const { isUnderPressure } = useMemoryPressure();

  const getImageQuality = useCallback(() => {
    if (isUnderPressure) return 0.5;
    return thresholds.imageQuality;
  }, [isUnderPressure, thresholds.imageQuality]);

  const getAnimationDuration = useCallback(() => {
    if (isUnderPressure) return thresholds.animationDuration * 0.5;
    return thresholds.animationDuration;
  }, [isUnderPressure, thresholds.animationDuration]);

  const shouldReduceAnimations = useCallback(() => {
    return isLowEndDevice || isUnderPressure;
  }, [isLowEndDevice, isUnderPressure]);

  return {
    getImageQuality,
    getAnimationDuration,
    shouldReduceAnimations,
  };
};

export default {
  useScreenDimensions,
  useOptimizedList,
  useDebounce,
  useImageCache,
  useMemoryOptimization,
  useBatchUpdates,
  useInteractionManager,
  usePerformanceMonitor,
  useThrottle,
  useOptimizedState,
  useMemoryPressure,
  useAdaptiveQuality,
};