/**
 * Offline Likes Service for IraChat
 * Handles like/dislike actions when offline and syncs with Firebase when online
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { networkStateManager } from './networkStateManager';
import { realUpdatesService } from './realUpdatesService';

export interface LikeAction {
  id: string;
  updateId: string;
  userId: string;
  action: 'like' | 'unlike';
  timestamp: number;
  retryCount: number;
  maxRetries: number;
}

export interface OfflineLikeState {
  updateId: string;
  isLiked: boolean;
  likeCount: number;
  timestamp: number;
}

class OfflineLikesService {
  private readonly QUEUE_KEY = 'offline_likes_queue';
  private readonly STATE_KEY = 'offline_likes_state';
  private queue: LikeAction[] = [];
  private offlineStates: Map<string, OfflineLikeState> = new Map();
  private isProcessing = false;

  constructor() {
    this.initializeService();
    this.setupNetworkListener();
  }

  /**
   * Force initialization (useful for debugging)
   */
  async forceInitialize(): Promise<void> {
    await this.initializeService();
  }

  private async initializeService(): Promise<void> {
    try {
      await this.loadQueue();
      await this.loadOfflineStates();
      console.log('✅ Offline likes service initialized');
    } catch (error) {
      console.error('❌ Error initializing offline likes service:', error);
    }
  }

  private setupNetworkListener(): void {
    networkStateManager.addListener('offline-likes', (state) => {
      if (state.isConnected && this.queue.length > 0) {
        console.log('🌐 Network connected, processing like queue');
        this.processQueue();
      }
    });
  }

  private async loadQueue(): Promise<void> {
    try {
      const queueData = await AsyncStorage.getItem(this.QUEUE_KEY);
      this.queue = queueData ? JSON.parse(queueData) : [];
    } catch (error) {
      console.error('❌ Error loading likes queue:', error);
      this.queue = [];
    }
  }

  private async saveQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.QUEUE_KEY, JSON.stringify(this.queue));
    } catch (error) {
      console.error('❌ Error saving likes queue:', error);
    }
  }

  private async loadOfflineStates(): Promise<void> {
    try {
      const statesData = await AsyncStorage.getItem(this.STATE_KEY);
      const states = statesData ? JSON.parse(statesData) : {};
      this.offlineStates = new Map(Object.entries(states));
      console.log(`📊 Loaded ${this.offlineStates.size} offline like states`);
    } catch (error) {
      console.error('❌ Error loading offline states:', error);
      this.offlineStates = new Map();
    }
  }

  private async saveOfflineStates(): Promise<void> {
    try {
      const states = Object.fromEntries(this.offlineStates);
      await AsyncStorage.setItem(this.STATE_KEY, JSON.stringify(states));
    } catch (error) {
      console.error('❌ Error saving offline states:', error);
    }
  }

  /**
   * Toggle like for an update (works offline)
   */
  async toggleLike(
    updateId: string,
    userId: string,
    currentLikeState: boolean,
    currentLikeCount: number
  ): Promise<{ isLiked: boolean; likeCount: number; isOffline: boolean }> {
    const action: 'like' | 'unlike' = currentLikeState ? 'unlike' : 'like';
    const newLikeState = !currentLikeState;
    const newLikeCount = newLikeState ? currentLikeCount + 1 : Math.max(0, currentLikeCount - 1);

    // Update offline state immediately for UI responsiveness
    const offlineState: OfflineLikeState = {
      updateId,
      isLiked: newLikeState,
      likeCount: newLikeCount,
      timestamp: Date.now(),
    };
    
    this.offlineStates.set(updateId, offlineState);
    await this.saveOfflineStates();

    if (networkStateManager.isOnline()) {
      // Try to sync immediately if online
      try {
        const result = await realUpdatesService.toggleLike(updateId, userId);
        if (result.success) {
          // Keep the offline state temporarily to ensure UI consistency
          // It will be cleaned up during the next sync cycle
          console.log('✅ Like synced immediately:', updateId, 'isLiked:', result.isLiked);

          return {
            isLiked: newLikeState, // Use our calculated state for consistency
            likeCount: newLikeCount,
            isOffline: false,
          };
        }
      } catch (error) {
        console.warn('⚠️ Failed to sync like immediately, queuing for later:', error);
      }
    }

    // Queue for later sync
    const queueAction: LikeAction = {
      id: `${updateId}_${userId}_${Date.now()}`,
      updateId,
      userId,
      action,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3,
    };

    this.queue.push(queueAction);
    await this.saveQueue();

    console.log(`📱 Like ${action} queued for offline sync:`, updateId);

    return {
      isLiked: newLikeState,
      likeCount: newLikeCount,
      isOffline: true,
    };
  }

  /**
   * Get the current like state for an update (including offline changes)
   */
  getLikeState(updateId: string): OfflineLikeState | null {
    return this.offlineStates.get(updateId) || null;
  }

  /**
   * Get the effective like state for an update, considering offline changes
   */
  getEffectiveLikeState(updateId: string, serverLikeState: boolean): boolean {
    const offlineState = this.offlineStates.get(updateId);
    if (offlineState) {
      console.log('📱 Using offline like state for', updateId, ':', offlineState.isLiked);
      return offlineState.isLiked;
    }
    return serverLikeState;
  }

  /**
   * Check if an update has pending offline like actions
   */
  hasPendingActions(updateId: string): boolean {
    return this.queue.some(action => action.updateId === updateId) || 
           this.offlineStates.has(updateId);
  }

  /**
   * Process the queue when network is available
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || !networkStateManager.isOnline() || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;
    console.log(`🔄 Processing ${this.queue.length} queued like actions`);

    const actionsToProcess = [...this.queue];
    
    for (const action of actionsToProcess) {
      try {
        const result = await realUpdatesService.toggleLike(action.updateId, action.userId);
        
        if (result.success) {
          // Remove from queue and offline state
          this.queue = this.queue.filter(q => q.id !== action.id);
          this.offlineStates.delete(action.updateId);
          console.log(`✅ Synced like action for update: ${action.updateId}`);
        } else {
          throw new Error('Toggle like failed');
        }
      } catch (error) {
        console.error(`❌ Error syncing like action for ${action.updateId}:`, error);
        
        // Increment retry count
        const actionIndex = this.queue.findIndex(q => q.id === action.id);
        if (actionIndex !== -1) {
          this.queue[actionIndex].retryCount++;
          
          // Remove if max retries exceeded
          if (this.queue[actionIndex].retryCount >= action.maxRetries) {
            console.warn(`⚠️ Max retries exceeded for like action: ${action.updateId}`);
            this.queue.splice(actionIndex, 1);
            this.offlineStates.delete(action.updateId);
          }
        }
      }
    }

    await this.saveQueue();
    await this.saveOfflineStates();
    this.isProcessing = false;

    console.log(`✅ Like queue processing complete. Remaining: ${this.queue.length}`);
  }

  /**
   * Get queue status for debugging
   */
  getQueueStatus(): { queueLength: number; offlineStatesCount: number; isProcessing: boolean } {
    return {
      queueLength: this.queue.length,
      offlineStatesCount: this.offlineStates.size,
      isProcessing: this.isProcessing,
    };
  }

  /**
   * Clear all offline data (for testing/reset)
   */
  async clearOfflineData(): Promise<void> {
    this.queue = [];
    this.offlineStates.clear();
    await this.saveQueue();
    await this.saveOfflineStates();
    console.log('🗑️ Offline likes data cleared');
  }
}

export const offlineLikesService = new OfflineLikesService();
