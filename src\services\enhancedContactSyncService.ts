/**
 * Enhanced Contact Sync Service for IraChat
 * Handles contact synchronization with encrypted local storage
 * Similar to <PERSON>s<PERSON><PERSON>'s contact management system
 */

import * as Contacts from 'expo-contacts';
import * as Crypto from 'expo-crypto';
import { collection, query, where, getDocs } from 'firebase/firestore';
import { db } from './firebase';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface LocalContact {
  localId: string;
  id?: string; // IraChat user ID if they're on the platform
  userId?: string;
  phoneNumber?: string;
  email?: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: number;
  isBlocked: boolean;
  isFavorite: boolean;
  isFromDevice: boolean;
  deviceContactId?: string;
  hashedPhone?: string; // For privacy
  hashedEmail?: string; // For privacy
  syncStatus: 'pending' | 'synced' | 'failed';
  createdAt: number;
  updatedAt: number;
}

export interface ContactSyncResult {
  totalDeviceContacts: number;
  totalIraChatUsers: number;
  newContacts: number;
  updatedContacts: number;
  errors: any[];
}

class EnhancedContactSyncService {
  private syncInProgress = false;
  private readonly SYNC_INTERVAL = 30 * 60 * 1000; // 30 minutes
  private backgroundSyncInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize contact sync service
   */
  async initialize(): Promise<void> {
    console.log('📱 Initializing Enhanced Contact Sync Service...');
    
    // Start background sync
    this.startBackgroundSync();
    
    // Listen for network changes
    networkStateManager.addListener('contactSync', this.handleNetworkChange.bind(this));
    
    console.log('✅ Enhanced Contact Sync Service initialized');
  }

  /**
   * Sync device contacts with local database
   */
  async syncDeviceContacts(currentUserId: string): Promise<ContactSyncResult> {
    if (this.syncInProgress) {
      console.log('⏳ Contact sync already in progress');
      return { totalDeviceContacts: 0, totalIraChatUsers: 0, newContacts: 0, updatedContacts: 0, errors: [] };
    }

    this.syncInProgress = true;
    console.log('📱 Starting device contact sync...');

    const result: ContactSyncResult = {
      totalDeviceContacts: 0,
      totalIraChatUsers: 0,
      newContacts: 0,
      updatedContacts: 0,
      errors: [],
    };

    try {
      // Request contacts permission
      const { status } = await Contacts.requestPermissionsAsync();
      if (status !== 'granted') {
        result.errors.push('Contacts permission not granted');
        return result;
      }

      // Get device contacts
      const { data: deviceContacts } = await Contacts.getContactsAsync({
        fields: [
          Contacts.Fields.ID,
          Contacts.Fields.Name,
          Contacts.Fields.FirstName,
          Contacts.Fields.LastName,
          Contacts.Fields.PhoneNumbers,
          Contacts.Fields.Emails,
          Contacts.Fields.Image,
        ],
      });

      result.totalDeviceContacts = deviceContacts.length;
      console.log(`📱 Found ${deviceContacts.length} device contacts`);

      // Process contacts in batches
      const batchSize = 50;
      for (let i = 0; i < deviceContacts.length; i += batchSize) {
        const batch = deviceContacts.slice(i, i + batchSize);
        const batchResult = await this.processBatch(batch, currentUserId);
        
        result.newContacts += batchResult.newContacts;
        result.updatedContacts += batchResult.updatedContacts;
        result.totalIraChatUsers += batchResult.iraChatUsers;
        result.errors.push(...batchResult.errors);
      }

      // Sync with server if online
      if (networkStateManager.isOnline()) {
        await this.syncWithServer(currentUserId);
      }

      console.log(`✅ Contact sync completed: ${result.newContacts} new, ${result.updatedContacts} updated`);
    } catch (error) {
      console.error('❌ Contact sync failed:', error);
      result.errors.push(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      this.syncInProgress = false;
    }

    return result;
  }

  /**
   * Process a batch of device contacts
   */
  private async processBatch(
    deviceContacts: Contacts.Contact[],
    currentUserId: string
  ): Promise<{ newContacts: number; updatedContacts: number; iraChatUsers: number; errors: string[] }> {
    const result = { newContacts: 0, updatedContacts: 0, iraChatUsers: 0, errors: [] };

    for (const deviceContact of deviceContacts) {
      try {
        const processedContact = await this.processDeviceContact(deviceContact, currentUserId);
        
        if (processedContact) {
          if (processedContact.isNew) {
            result.newContacts++;
          } else {
            result.updatedContacts++;
          }
          
          if (processedContact.isIraChatUser) {
            result.iraChatUsers++;
          }
        }
      } catch (error) {
        console.error(`❌ Failed to process contact ${deviceContact.name}:`, error);
        (result.errors as any[]).push(`Failed to process ${deviceContact.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return result;
  }

  /**
   * Process a single device contact
   */
  private async processDeviceContact(
    deviceContact: Contacts.Contact,
    currentUserId: string
  ): Promise<{ isNew: boolean; isIraChatUser: boolean } | null> {
    // Extract contact information
    const name = this.extractContactName(deviceContact);
    const phoneNumbers = this.extractPhoneNumbers(deviceContact);
    const emails = this.extractEmails(deviceContact);

    if (phoneNumbers.length === 0 && emails.length === 0) {
      return null; // Skip contacts without phone or email
    }

    // Use primary phone number or email
    const primaryPhone = phoneNumbers[0];
    const primaryEmail = emails[0];

    // Check if contact already exists
    const existingContact = await this.getExistingContact(primaryPhone, primaryEmail);

    const now = Date.now();
    const localId = existingContact?.localId || `contact_${now}_${Math.random().toString(36).substring(2, 9)}`;

    // Hash phone and email for privacy
    const hashedPhone = primaryPhone ? await this.hashValue(primaryPhone) : undefined;
    const hashedEmail = primaryEmail ? await this.hashValue(primaryEmail) : undefined;

    const contactData: LocalContact = {
      localId,
      phoneNumber: primaryPhone,
      email: primaryEmail,
      name,
      avatar: deviceContact.imageAvailable ? deviceContact.image?.uri : undefined,
      isOnline: false,
      isBlocked: false,
      isFavorite: false,
      isFromDevice: true,
      deviceContactId: deviceContact.id,
      hashedPhone,
      hashedEmail,
      syncStatus: 'pending',
      createdAt: existingContact?.createdAt || now,
      updatedAt: now,
    };

    // Save to local database
    await this.saveContactToDatabase(contactData);

    return {
      isNew: !existingContact,
      isIraChatUser: false, // Will be determined during server sync
    };
  }

  /**
   * Sync with server to find IraChat users
   */
  private async syncWithServer(currentUserId: string): Promise<void> {
    try {
      console.log('🔄 Syncing contacts with server...');

      // Get all pending contacts
      const pendingContacts = await this.getPendingContacts();
      
      if (pendingContacts.length === 0) {
        return;
      }

      // Create hashed lists for privacy
      const hashedPhones = pendingContacts
        .map(c => c.hashedPhone)
        .filter(Boolean) as string[];
      
      const hashedEmails = pendingContacts
        .map(c => c.hashedEmail)
        .filter(Boolean) as string[];

      // Find IraChat users by hashed phone numbers
      const iraChatUsers = await this.findIraChatUsers(hashedPhones, hashedEmails);

      // Update contacts with IraChat user information
      for (const contact of pendingContacts) {
        const iraChatUser = iraChatUsers.find(
          user => user.hashedPhone === contact.hashedPhone || 
                  user.hashedEmail === contact.hashedEmail
        );

        if (iraChatUser) {
          contact.id = iraChatUser.id;
          contact.userId = iraChatUser.id;
          contact.avatar = iraChatUser.avatar || contact.avatar;
          contact.isOnline = iraChatUser.isOnline || false;
          contact.lastSeen = iraChatUser.lastSeen;
        }

        contact.syncStatus = 'synced';
        await this.updateContactInDatabase(contact);
      }

      console.log(`✅ Server sync completed. Found ${iraChatUsers.length} IraChat users`);
    } catch (error) {
      console.error('❌ Server sync failed:', error);
    }
  }

  /**
   * Find IraChat users by hashed identifiers
   */
  private async findIraChatUsers(
    hashedPhones: string[],
    hashedEmails: string[]
  ): Promise<Array<{ id: string; hashedPhone?: string; hashedEmail?: string; avatar?: string; isOnline?: boolean; lastSeen?: number }>> {
    try {
      const users: any[] = [];

      // Query by hashed phone numbers
      if (hashedPhones.length > 0) {
        const phoneQuery = query(
          collection(db, 'users'),
          where('hashedPhone', 'in', hashedPhones.slice(0, 10)) // Firestore limit
        );
        const phoneSnapshot = await getDocs(phoneQuery);
        phoneSnapshot.forEach(doc => {
          users.push({ id: doc.id, ...doc.data() });
        });
      }

      // Query by hashed emails
      if (hashedEmails.length > 0) {
        const emailQuery = query(
          collection(db, 'users'),
          where('hashedEmail', 'in', hashedEmails.slice(0, 10)) // Firestore limit
        );
        const emailSnapshot = await getDocs(emailQuery);
        emailSnapshot.forEach(doc => {
          users.push({ id: doc.id, ...doc.data() });
        });
      }

      return users;
    } catch (error) {
      console.error('❌ Failed to find IraChat users:', error);
      return [];
    }
  }

  /**
   * Get existing contact by phone or email
   */
  private async getExistingContact(phoneNumber?: string, email?: string): Promise<LocalContact | null> {
    try {
      const db = offlineDatabaseService.getDatabase();
      
      let query = 'SELECT * FROM contacts WHERE ';
      const params: string[] = [];
      
      if (phoneNumber) {
        query += 'phoneNumber = ?';
        params.push(phoneNumber);
      }
      
      if (email) {
        if (phoneNumber) query += ' OR ';
        query += 'email = ?';
        params.push(email);
      }
      
      const results = await db.getAllAsync(query, params);
      
      if (results.length > 0) {
        return this.mapRowToLocalContact(results[0]);
      }
      
      return null;
    } catch (error) {
      console.error('❌ Failed to get existing contact:', error);
      return null;
    }
  }

  /**
   * Get pending contacts
   */
  private async getPendingContacts(): Promise<LocalContact[]> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const results = await db.getAllAsync(`
        SELECT * FROM contacts WHERE syncStatus = 'pending'
      `);
      
      return results.map(this.mapRowToLocalContact);
    } catch (error) {
      console.error('❌ Failed to get pending contacts:', error);
      return [];
    }
  }

  /**
   * Save contact to database
   */
  private async saveContactToDatabase(contact: LocalContact): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      INSERT OR REPLACE INTO contacts (
        localId, id, userId, phoneNumber, email, name, avatar, isOnline,
        lastSeen, isBlocked, isFavorite, syncStatus, createdAt, updatedAt,
        isDeleted
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0)
    `, [
      contact.localId, contact.id || null, contact.userId || null, contact.phoneNumber || null,
      contact.email || null, contact.name, contact.avatar || null, contact.isOnline ? 1 : 0,
      contact.lastSeen || null, contact.isBlocked ? 1 : 0, contact.isFavorite ? 1 : 0,
      contact.syncStatus, contact.createdAt, contact.updatedAt
    ]);
  }

  /**
   * Update contact in database
   */
  private async updateContactInDatabase(contact: LocalContact): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE contacts SET
        id = ?, userId = ?, name = ?, avatar = ?, isOnline = ?,
        lastSeen = ?, syncStatus = ?, updatedAt = ?
      WHERE localId = ?
    `, [
      contact.id || null, contact.userId || null, contact.name, contact.avatar || null,
      contact.isOnline ? 1 : 0, contact.lastSeen || null, contact.syncStatus,
      contact.updatedAt, contact.localId
    ]);
  }

  /**
   * Extract contact name
   */
  private extractContactName(contact: Contacts.Contact): string {
    if (contact.name) return contact.name;
    
    const firstName = contact.firstName || '';
    const lastName = contact.lastName || '';
    const fullName = `${firstName} ${lastName}`.trim();
    
    return fullName || 'Unknown Contact';
  }

  /**
   * Extract phone numbers
   */
  private extractPhoneNumbers(contact: Contacts.Contact): string[] {
    if (!contact.phoneNumbers) return [];
    
    return contact.phoneNumbers
      .map(phone => this.normalizePhoneNumber(phone.number || ''))
      .filter(Boolean);
  }

  /**
   * Extract emails
   */
  private extractEmails(contact: Contacts.Contact): string[] {
    if (!contact.emails) return [];
    
    return contact.emails
      .map(email => email.email?.toLowerCase().trim())
      .filter(Boolean) as string[];
  }

  /**
   * Normalize phone number
   */
  private normalizePhoneNumber(phoneNumber: string): string {
    // Remove all non-digit characters except +
    const cleaned = phoneNumber.replace(/[^\d+]/g, '');
    
    // Add + if it starts with a country code
    if (cleaned.length > 10 && !cleaned.startsWith('+')) {
      return `+${cleaned}`;
    }
    
    return cleaned;
  }

  /**
   * Hash value for privacy
   */
  private async hashValue(value: string): Promise<string> {
    return await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      value.toLowerCase().trim()
    );
  }

  /**
   * Start background sync
   */
  private startBackgroundSync(): void {
    if (this.backgroundSyncInterval) {
      clearInterval(this.backgroundSyncInterval);
    }

    this.backgroundSyncInterval = setInterval(() => {
      if (networkStateManager.isOnline() && !this.syncInProgress) {
        // Perform lightweight background sync
        this.performBackgroundSync();
      }
    }, this.SYNC_INTERVAL);
  }

  /**
   * Perform background sync
   */
  private async performBackgroundSync(): Promise<void> {
    try {
      console.log('🔄 Performing background contact sync...');
      
      // Only sync pending contacts, don't re-scan device
      const pendingContacts = await this.getPendingContacts();
      
      if (pendingContacts.length > 0) {
        // Sync with server to find IraChat users
        await this.syncWithServer('current_user_id'); // You'd get this from auth
      }
    } catch (error) {
      console.error('❌ Background contact sync failed:', error);
    }
  }

  /**
   * Handle network state changes
   */
  private handleNetworkChange(state: any): void {
    if (state.isOnline) {
      console.log('🌐 Network restored, syncing contacts...');
      this.performBackgroundSync();
    }
  }

  /**
   * Map database row to LocalContact
   */
  private mapRowToLocalContact(row: any): LocalContact {
    return {
      localId: row.localId,
      id: row.id,
      userId: row.userId,
      phoneNumber: row.phoneNumber,
      email: row.email,
      name: row.name,
      avatar: row.avatar,
      isOnline: row.isOnline === 1,
      lastSeen: row.lastSeen,
      isBlocked: row.isBlocked === 1,
      isFavorite: row.isFavorite === 1,
      isFromDevice: true,
      syncStatus: row.syncStatus,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    };
  }

  /**
   * Stop background sync
   */
  stopBackgroundSync(): void {
    if (this.backgroundSyncInterval) {
      clearInterval(this.backgroundSyncInterval);
      this.backgroundSyncInterval = null;
    }
  }
}

export const enhancedContactSyncService = new EnhancedContactSyncService();
