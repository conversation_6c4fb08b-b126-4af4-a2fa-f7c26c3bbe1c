// 🔒 Secure Environment Configuration for IraChat
// Validates and manages environment variables with security best practices

interface EnvironmentConfig {
  // Firebase Configuration
  firebase: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId?: string;
  };
  
  // iOS Firebase Configuration
  firebaseIOS?: {
    apiKey: string;
    appId: string;
  };
  
  // WebRTC Configuration
  webrtc: {
    stunServers: string[];
    turnServer: {
      url: string;
      username: string;
      password: string;
    };
    iceCandidatePoolSize: number;
  };
  
  // App Configuration
  app: {
    environment: 'development' | 'staging' | 'production';
    useMockData: boolean;
    locationSharingEnabled: boolean;
    projectId: string;
    owner: string;
  };
  
  // Contact Information
  support: {
    email: string;
    phone: string;
    website: string;
  };
}

// Validation functions
const validateRequired = (value: string | undefined, name: string): string => {
  if (!value || value.trim() === '' || value.includes('your-') || value.includes('-here')) {
    console.error(`❌ CRITICAL: Missing required environment variable: ${name}`);
    console.error(`Current value: "${value}"`);
    throw new Error(`❌ CRITICAL: Missing required environment variable: ${name}`);
  }
  return value.trim();
};

const validateOptional = (value: string | undefined, defaultValue: string): string => {
  return value && value.trim() !== '' ? value.trim() : defaultValue;
};

const validateBoolean = (value: string | undefined, defaultValue: boolean): boolean => {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
};

const validateNumber = (value: string | undefined, defaultValue: number): number => {
  if (!value) return defaultValue;
  const num = parseInt(value, 10);
  return isNaN(num) ? defaultValue : num;
};

// Security validation
const validateFirebaseApiKey = (apiKey: string): void => {
  if (!apiKey.startsWith('AIza')) {
    throw new Error('❌ SECURITY: Invalid Firebase API key format');
  }
  if (apiKey.length < 30) {
    throw new Error('❌ SECURITY: Firebase API key appears to be invalid');
  }
};

const validateProjectId = (projectId: string): void => {
  if (!/^[a-z0-9-]+$/.test(projectId)) {
    throw new Error('❌ SECURITY: Invalid Firebase project ID format');
  }
};

// Load and validate environment configuration
export const loadEnvironmentConfig = (): EnvironmentConfig => {
  // Loading and validating environment configuration...
  
  try {
    // Firebase Configuration
    const firebaseApiKey = validateRequired(process.env.EXPO_PUBLIC_FIREBASE_API_KEY, 'FIREBASE_API_KEY');
    const firebaseProjectId = validateRequired(process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID, 'FIREBASE_PROJECT_ID');
    
    // Security validation
    validateFirebaseApiKey(firebaseApiKey);
    validateProjectId(firebaseProjectId);
    
    const config: EnvironmentConfig = {
      firebase: {
        apiKey: firebaseApiKey,
        authDomain: validateRequired(process.env.EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN, 'FIREBASE_AUTH_DOMAIN'),
        projectId: firebaseProjectId,
        storageBucket: validateRequired(process.env.EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET, 'FIREBASE_STORAGE_BUCKET'),
        messagingSenderId: validateRequired(process.env.EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID, 'FIREBASE_MESSAGING_SENDER_ID'),
        appId: validateRequired(process.env.EXPO_PUBLIC_FIREBASE_APP_ID, 'FIREBASE_APP_ID'),
        measurementId: validateOptional(process.env.EXPO_PUBLIC_FIREBASE_MEASUREMENT_ID, ''),
      },
      
      // iOS Configuration (optional)
      firebaseIOS: process.env.EXPO_PUBLIC_FIREBASE_API_KEY_IOS ? {
        apiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY_IOS,
        appId: validateRequired(process.env.EXPO_PUBLIC_FIREBASE_APP_ID_IOS, 'FIREBASE_APP_ID_IOS'),
      } : undefined,
      
      // WebRTC Configuration
      webrtc: {
        stunServers: [
          validateOptional(process.env.EXPO_PUBLIC_STUN_SERVER_URL, 'stun:stun.l.google.com:19302'),
          validateOptional(process.env.EXPO_PUBLIC_STUN_SERVER_URL_2, 'stun:stun1.l.google.com:19302'),
        ],
        turnServer: {
          url: validateOptional(process.env.EXPO_PUBLIC_TURN_SERVER_URL, 'turn:openrelay.metered.ca:80'),
          username: validateOptional(process.env.EXPO_PUBLIC_TURN_SERVER_USERNAME, 'openrelayproject'),
          password: validateOptional(process.env.EXPO_PUBLIC_TURN_SERVER_PASSWORD, 'openrelayproject'),
        },
        iceCandidatePoolSize: validateNumber(process.env.EXPO_PUBLIC_WEBRTC_ICE_CANDIDATE_POOL_SIZE, 10),
      },
      
      // App Configuration
      app: {
        environment: (process.env.NODE_ENV as any) || 'development',
        useMockData: validateBoolean(process.env.EXPO_PUBLIC_USE_MOCK_DATA, false),
        locationSharingEnabled: validateBoolean(process.env.EXPO_PUBLIC_LOCATION_SHARING_ENABLED, false),
        projectId: validateOptional(process.env.EXPO_PUBLIC_PROJECT_ID, firebaseProjectId),
        owner: validateOptional(process.env.EXPO_PUBLIC_OWNER, 'irachat'),
      },

      // Support Information
      support: {
        email: validateOptional(process.env.EXPO_PUBLIC_SUPPORT_EMAIL, '<EMAIL>'),
        phone: validateOptional(process.env.EXPO_PUBLIC_SUPPORT_PHONE, '******-IRACHAT'),
        website: validateOptional(process.env.EXPO_PUBLIC_SUPPORT_WEBSITE, 'https://irachat.app'),
      },
    };
    
    // Environment configuration loaded successfully
    console.log('✅ Environment configuration loaded successfully');
    console.log(`Environment: ${config.app.environment}`);
    console.log(`Firebase Project: ${config.firebase.projectId}`);
    console.log(`Expo Project: ${config.app.owner}/${config.app.projectId}`);
    console.log(`Storage Bucket: ${config.firebase.storageBucket}`);

    return config;
    
  } catch (error) {
    // CRITICAL: Failed to load environment configuration
    throw error;
  }
};

// Export singleton instance
export const environmentConfig = loadEnvironmentConfig();
