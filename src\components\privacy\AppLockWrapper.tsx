import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { usePrivacyLock } from '../../contexts/PrivacyLockContext';
import LockScreen from './LockScreen';
import { LockType } from '../../services/privacyLockService';

interface AppLockWrapperProps {
  children: React.ReactNode;
}

const AppLockWrapper: React.FC<AppLockWrapperProps> = ({ children }) => {
  const { isLocked, lockConfig, isLoading, unlockApp } = usePrivacyLock();
  const [showLockScreen, setShowLockScreen] = useState(false);

  useEffect(() => {
    // Show lock screen if app is locked and lock type is not NONE
    const shouldShowLockScreen = isLocked && lockConfig.lockType !== LockType.NONE;
    console.log('🔐 AppLockWrapper: Lock state changed', {
      isLocked,
      lockType: lockConfig.lockType,
      shouldShowLockScreen
    });
    setShowLockScreen(shouldShowLockScreen);
  }, [isLocked, lockConfig.lockType]);

  const handleUnlock = async (credential?: string) => {
    try {
      const success = await unlockApp(credential);
      if (success) {
        setShowLockScreen(false);
      }
      return success;
    } catch (error) {
      console.error('Error unlocking app:', error);
      return false;
    }
  };

  const handleUnlockError = (error: string) => {
    console.error('Lock screen error:', error);
  };

  // Don't render anything while loading
  if (isLoading) {
    return <View style={styles.container} />;
  }

  // Show lock screen if app is locked
  if (showLockScreen) {
    return (
      <View style={styles.container}>
        <LockScreen
          onUnlock={() => handleUnlock()}
          onError={handleUnlockError}
        />
      </View>
    );
  }

  // Show normal app content
  return <>{children}</>;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default AppLockWrapper;
