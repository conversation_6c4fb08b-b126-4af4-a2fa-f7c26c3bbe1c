import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import { offlineDatabaseService } from '../../services/offlineDatabase';

export const DatabaseResetButton: React.FC = () => {
  const [isResetting, setIsResetting] = useState(false);

  const handleReset = () => {
    Alert.alert(
      'Reset Database',
      'This will delete all offline data and recreate the database with the latest schema. This action cannot be undone.\n\nUse this if you\'re experiencing database column errors.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset Database',
          style: 'destructive',
          onPress: async () => {
            setIsResetting(true);
            try {
              await offlineDatabaseService.resetDatabase();
              Alert.alert('Success', 'Database has been reset successfully. The app should now work properly.');
            } catch (error) {
              console.error('❌ Error resetting database:', error);
              Alert.alert('Error', 'Failed to reset database. Please restart the app.');
            } finally {
              setIsResetting(false);
            }
          }
        }
      ]
    );
  };

  const handleReinitialize = () => {
    Alert.alert(
      'Reinitialize Database',
      'This will apply any missing database migrations without deleting data.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reinitialize',
          onPress: async () => {
            setIsResetting(true);
            try {
              await offlineDatabaseService.forceReinitialize();
              Alert.alert('Success', 'Database has been reinitialized successfully.');
            } catch (error) {
              console.error('❌ Error reinitializing database:', error);
              Alert.alert('Error', 'Failed to reinitialize database.');
            } finally {
              setIsResetting(false);
            }
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>🛠️ Database Tools</Text>
      <Text style={styles.subtitle}>
        Use these tools if you're experiencing database errors
      </Text>
      
      <TouchableOpacity
        style={[styles.button, styles.reinitButton]}
        onPress={handleReinitialize}
        disabled={isResetting}
      >
        <Text style={styles.buttonText}>
          {isResetting ? 'Working...' : 'Reinitialize Database'}
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.button, styles.resetButton]}
        onPress={handleReset}
        disabled={isResetting}
      >
        <Text style={styles.buttonText}>
          {isResetting ? 'Working...' : 'Reset Database (Delete All)'}
        </Text>
      </TouchableOpacity>

      <Text style={styles.warning}>
        ⚠️ Reset will delete all offline messages and chats
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    margin: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
  },
  button: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    alignItems: 'center',
  },
  reinitButton: {
    backgroundColor: '#007bff',
  },
  resetButton: {
    backgroundColor: '#dc3545',
  },
  buttonText: {
    color: 'white',
    fontWeight: '600',
    fontSize: 16,
  },
  warning: {
    fontSize: 12,
    color: '#dc3545',
    textAlign: 'center',
    marginTop: 8,
  },
});
