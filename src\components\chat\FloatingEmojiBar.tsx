/**
 * Floating Emoji Reaction Bar for IraChat
 * Shows emoji reactions above selected message
 */

import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';
import { useMessageSelection } from '../../contexts/MessageSelectionContext';
import { useTheme } from '../../contexts/ThemeContext';

interface FloatingEmojiBarProps {
  messageId: string;
  onEmojiSelect: (emoji: string, messageId: string) => void;
  position: { x: number; y: number };
  visible: boolean;
}

const QUICK_EMOJIS = ['❤️', '😂', '😮', '😢', '😡', '👍'];

export const FloatingEmojiBar: React.FC<FloatingEmojiBarProps> = ({
  messageId,
  onEmojiSelect,
  position,
  visible,
}) => {
  const { colors } = useTheme();
  const { getSelectedCount, isMessageSelected } = useMessageSelection();
  
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const translateYAnim = useRef(new Animated.Value(10)).current;

  const selectedCount = getSelectedCount();
  const shouldShow = visible && 
                    selectedCount === 1 && 
                    isMessageSelected(messageId);

  useEffect(() => {
    if (shouldShow) {
      // Animate in
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: false, // Changed to false for consistency
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: false, // Changed to false for consistency
        }),
        Animated.timing(translateYAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false, // Changed to false for consistency
        }),
      ]).start();
    } else {
      // Animate out
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: false, // Changed to false for consistency
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 150,
          useNativeDriver: false, // Changed to false for consistency
        }),
        Animated.timing(translateYAnim, {
          toValue: 10,
          duration: 150,
          useNativeDriver: false, // Changed to false for consistency
        }),
      ]).start();
    }
  }, [shouldShow]);

  if (!shouldShow) {
    return null;
  }

  const handleEmojiPress = (emoji: string) => {
    onEmojiSelect(emoji, messageId);
    
    // Add a small bounce animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.1,
        duration: 100,
        useNativeDriver: false, // Changed to false for consistency
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: false, // Changed to false for consistency
      }),
    ]).start();
  };

  // Calculate position to center above the message, avoiding toolbar
  const screenWidth = Dimensions.get('window').width;
  const barWidth = QUICK_EMOJIS.length * 50 + 20; // Approximate width
  const leftPosition = Math.max(10, Math.min(position.x - barWidth / 2, screenWidth - barWidth - 10));

  // Position emoji bar above the message, avoiding toolbar
  const toolbarHeight = Platform.OS === 'ios' ? 160 : 140; // Account for status bar + header + toolbar
  const adjustedTopPosition = Math.max(toolbarHeight + 10, position.y - 80); // More space above message

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: colors.surface,
          borderColor: colors.border,
          left: leftPosition,
          top: adjustedTopPosition, // Position above the message, avoiding toolbar
          opacity: fadeAnim,
          transform: [
            { scale: scaleAnim },
            { translateY: translateYAnim },
          ],
        },
      ]}
    >
      {/* Arrow pointing down to message */}
      <View style={[styles.arrow, { borderTopColor: colors.surface }]} />
      
      {/* Emoji buttons */}
      <View style={styles.emojiContainer}>
        {QUICK_EMOJIS.map((emoji) => (
          <TouchableOpacity
            key={emoji}
            style={[
              styles.emojiButton,
              { backgroundColor: colors.background },
            ]}
            onPress={() => handleEmojiPress(emoji)}
            activeOpacity={0.7}
          >
            <Text style={styles.emojiText}>{emoji}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </Animated.View>
  );
};

// Hook to manage emoji bar state
export const useFloatingEmojiBar = () => {
  const [emojiBarState, setEmojiBarState] = useState<{
    visible: boolean;
    messageId: string | null;
    position: { x: number; y: number };
  }>({
    visible: false,
    messageId: null,
    position: { x: 0, y: 0 },
  });

  const showEmojiBar = (messageId: string, position: { x: number; y: number }) => {
    setEmojiBarState({
      visible: true,
      messageId,
      position,
    });
  };

  const hideEmojiBar = () => {
    setEmojiBarState(prev => ({
      ...prev,
      visible: false,
    }));
  };

  return {
    emojiBarState,
    showEmojiBar,
    hideEmojiBar,
  };
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    borderRadius: 25,
    borderWidth: 1,
    paddingVertical: 8,
    paddingHorizontal: 10,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 500, // Much lower than toolbar (1000)
  },
  arrow: {
    position: 'absolute',
    bottom: -8,
    left: '50%',
    marginLeft: -8,
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
  },
  emojiContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  emojiButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 2,
  },
  emojiText: {
    fontSize: 20,
  },
});

export default FloatingEmojiBar;
