/**
 * IraChat Business Service
 * Handles all business advertising functionality with real Firebase integration
 */

import {
  BusinessUser, BusinessProfile, BusinessPost, BusinessComment,
  SubscriptionPlan, SearchFilters, SearchResult, BusinessAnalytics,
  ApiResponse, PaginatedResponse, UserType, BusinessType, ProductCategory
} from '../types/Business';
import {
  collection,
  doc,
  addDoc,
  updateDoc,
  deleteDoc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  serverTimestamp,
  onSnapshot,
  writeBatch,
  increment,
  arrayUnion,
  arrayRemove,
  Timestamp
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';
import { paymentService } from './paymentService';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Real payment service interface
interface PaymentRequest {
  amount: number;
  currency: string;
  email: string;
  phoneNumber: string;
  fullName: string;
  description: string;
  reference: string;
  paymentMethod: 'mtn_momo' | 'airtel_money' | 'card';
  metadata: any;
}

// Firebase Collections
const COLLECTIONS = {
  BUSINESS_USERS: 'business_users',
  BUSINESS_PROFILES: 'business_profiles',
  BUSINESS_POSTS: 'businessPosts', // Fixed: Use camelCase to match other services
  BUSINESS_COMMENTS: 'business_comments',
  BUSINESS_ANALYTICS: 'business_analytics',
  BUSINESS_CHATS: 'business_chats',
  BUSINESS_ORDERS: 'business_orders',
} as const;

// ==================== BUSINESS USER SERVICE ====================

export class BusinessUserService {
  private usersCollection = collection(db, COLLECTIONS.BUSINESS_USERS);
  private profilesCollection = collection(db, COLLECTIONS.BUSINESS_PROFILES);
  private isOnline: boolean = true;

  constructor() {
    // Monitor network state
    networkStateManager.addListener('businessService', (networkState) => {
      this.isOnline = networkState.isConnected;
    });
  }

  // Register new business user
  async registerBusinessUser(userData: Omit<BusinessUser, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<BusinessUser>> {
    try {
      const now = new Date();
      const user: Omit<BusinessUser, 'id'> = {
        ...userData,
        createdAt: now,
        updatedAt: now,
      };

      if (this.isOnline) {
        try {
          const docRef = await addDoc(this.usersCollection, {
            ...user,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            subscriptionStartDate: user.subscriptionStartDate ? Timestamp.fromDate(user.subscriptionStartDate) : null,
            subscriptionEndDate: user.subscriptionEndDate ? Timestamp.fromDate(user.subscriptionEndDate) : null,
            lastPaymentDate: user.lastPaymentDate ? Timestamp.fromDate(user.lastPaymentDate) : null,
          });

          const createdUser = { ...user, id: docRef.id } as BusinessUser;

          // Cache offline
          await this.cacheUserOffline(createdUser);


          return { success: true, data: createdUser };
        } catch (error) {
          console.error('❌ Online registration failed, caching offline:', error);
          // Fall through to offline handling
        }
      }

      // Offline handling
      const offlineId = `offline_${Date.now()}_${userData.email}`;
      const createdUser = { ...user, id: offlineId } as BusinessUser;

      await this.cacheUserOffline(createdUser);


      return { success: true, data: createdUser };
    } catch (error) {
      console.error('❌ Error registering business user:', error);
      return { success: false, error: 'Failed to register business user' };
    }
  }

  // Subscribe user to plan
  async subscribeUser(
    userId: string,
    planType: UserType,
    paymentMethod: 'mtn_momo' | 'airtel_money' | 'card',
    phoneNumber?: string
  ): Promise<ApiResponse<BusinessUser>> {
    try {
      // Get subscription plan details
      const plan = this.getSubscriptionPlan(planType);
      if (!plan) {
        return { success: false, error: 'Invalid subscription plan' };
      }

      // Process payment if not free
      if (plan.price > 0) {
        const paymentRequest: PaymentRequest = {
          amount: plan.price,
          currency: plan.currency,
          email: `${userId}@irachat.app`,
          phoneNumber: phoneNumber || '+256700000000',
          fullName: 'Business User',
          description: `IraChat ${plan.name} Subscription`,
          reference: `SUB_${Date.now()}_${userId}`,
          paymentMethod: paymentMethod,
          metadata: { userId, planType, planId: plan.id },
        };

        const paymentResult = await paymentService.processPayment(paymentRequest);

        if (!paymentResult.success) {
          return { success: false, error: paymentResult.error };
        }
      }

      // Update user subscription
      const subscriptionEndDate = new Date();
      subscriptionEndDate.setDate(subscriptionEndDate.getDate() + (plan.duration || 30));

      const updateData = {
        userType: planType,
        subscriptionStatus: 'active' as const,
        subscriptionStartDate: serverTimestamp(),
        subscriptionEndDate: Timestamp.fromDate(subscriptionEndDate),
        lastPaymentDate: plan.price > 0 ? serverTimestamp() : null,
        updatedAt: serverTimestamp(),
      };

      if (this.isOnline) {
        try {
          await updateDoc(doc(this.usersCollection, userId), updateData);

          return { success: true, message: 'Subscription successful' };
        } catch (error) {
          console.error('❌ Online subscription update failed:', error);
          // Cache for later sync
          await this.cacheSubscriptionOffline(userId, updateData);
          return { success: true, message: 'Subscription cached for sync' };
        }
      } else {
        // Cache for later sync
        await this.cacheSubscriptionOffline(userId, updateData);

        return { success: true, message: 'Subscription cached for sync' };
      }
    } catch (error) {
      console.error('❌ Error subscribing user:', error);
      return { success: false, error: 'Subscription failed' };
    }
  }

  // Cache user data offline
  private async cacheUserOffline(user: BusinessUser): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_users (
          id, email, phoneNumber, fullName, userType, isVerified,
          subscriptionStatus, subscriptionStartDate, subscriptionEndDate,
          lastPaymentDate, createdAt, updatedAt, syncStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        user.id,
        user.email,
        user.phoneNumber,
        user.fullName,
        user.userType,
        user.isVerified ? 1 : 0,
        user.subscriptionStatus,
        user.subscriptionStartDate?.getTime() || null,
        user.subscriptionEndDate?.getTime() || null,
        user.lastPaymentDate?.getTime() || null,
        user.createdAt.getTime(),
        user.updatedAt.getTime(),
        this.isOnline ? 'synced' : 'pending'
      ]);
    } catch (error) {
      console.error('❌ Error caching user offline:', error);
    }
  }

  // Cache subscription update offline
  private async cacheSubscriptionOffline(userId: string, updateData: any): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_subscription_updates (
          userId, updateData, timestamp, syncStatus
        ) VALUES (?, ?, ?, ?)
      `, [
        userId,
        JSON.stringify(updateData),
        Date.now(),
        'pending'
      ]);
    } catch (error) {
      console.error('❌ Error caching subscription update offline:', error);
    }
  }

  // Get subscription plan by type
  private getSubscriptionPlan(planType: UserType): SubscriptionPlan | null {
    const { SUBSCRIPTION_PLANS } = require('../types/Business');
    return SUBSCRIPTION_PLANS.find((plan: SubscriptionPlan) => plan.type === planType) || null;
  }

  // Remove profile from cache
  private async removeProfileFromCache(profileId: string): Promise<void> {
    try {
      const cacheKey = `business_profile_${profileId}`;
      await AsyncStorage.removeItem(cacheKey);
      console.log('🗑️ Business profile removed from cache:', profileId);
    } catch (error) {
      console.error('❌ Error removing profile from cache:', error);
    }
  }

  // Update business profile statistics
  async updateBusinessStats(businessId: string, statsUpdate: {
    views?: number;
    likes?: number;
    shares?: number;
    comments?: number;
  }): Promise<ApiResponse<boolean>> {
    try {
      console.log('📊 BusinessUserService: Updating business stats:', businessId, statsUpdate);

      if (this.isOnline) {
        try {
          const profileRef = doc(this.profilesCollection, businessId);
          const updateData: any = {};

          if (statsUpdate.views !== undefined) {
            updateData.totalViews = increment(statsUpdate.views);
          }
          if (statsUpdate.likes !== undefined) {
            updateData.totalLikes = increment(statsUpdate.likes);
          }
          if (statsUpdate.shares !== undefined) {
            updateData.totalShares = increment(statsUpdate.shares);
          }
          if (statsUpdate.comments !== undefined) {
            updateData.totalComments = increment(statsUpdate.comments);
          }

          updateData.updatedAt = serverTimestamp();

          await updateDoc(profileRef, updateData);
          console.log('✅ Business stats updated successfully');
          return { success: true, data: true };
        } catch (error) {
          console.error('❌ Online business stats update failed:', error);
          return { success: false, error: 'Failed to update business stats online' };
        }
      }

      return { success: false, error: 'Stats update requires internet connection' };
    } catch (error) {
      console.error('❌ Error updating business stats:', error);
      return { success: false, error: 'Failed to update business stats' };
    }
  }

  // Delete business profile
  async deleteBusinessProfile(profileId: string): Promise<ApiResponse<boolean>> {
    try {
      console.log('🏢 BusinessUserService: Deleting business profile:', profileId);

      if (this.isOnline) {
        try {
          // First, get all posts for this business to delete them
          const postsQuery = query(
            collection(db, COLLECTIONS.BUSINESS_POSTS),
            where('businessId', '==', profileId)
          );
          const postsSnapshot = await getDocs(postsQuery);

          // Delete all business posts
          const deletePromises = postsSnapshot.docs.map(doc => deleteDoc(doc.ref));
          await Promise.all(deletePromises);

          console.log(`🗑️ Deleted ${postsSnapshot.docs.length} business posts`);

          // Delete the business profile
          const profileRef = doc(this.profilesCollection, profileId);
          await deleteDoc(profileRef);

          // Remove from offline cache
          await this.removeProfileFromCache(profileId);

          console.log('✅ Business profile and all related data deleted successfully');
          return { success: true, data: true };
        } catch (error) {
          console.error('❌ Online business deletion failed:', error);
          return { success: false, error: 'Failed to delete business profile online' };
        }
      }

      return { success: false, error: 'Deletion requires internet connection' };
    } catch (error) {
      console.error('❌ Error deleting business profile:', error);
      return { success: false, error: 'Failed to delete business profile' };
    }
  }

  // Update business profile
  async updateBusinessProfile(profileId: string, profileData: Partial<BusinessProfile>): Promise<ApiResponse<BusinessProfile>> {
    try {
      console.log('🏢 BusinessUserService: Updating business profile:', profileId);

      const updateData = {
        ...profileData,
        updatedAt: new Date(),
      };

      if (this.isOnline) {
        try {
          // Update in Firebase
          const profileRef = doc(this.profilesCollection, profileId);
          await updateDoc(profileRef, {
            ...updateData,
            updatedAt: serverTimestamp(),
          });

          // Get updated profile
          const updatedDoc = await getDoc(profileRef);
          if (updatedDoc.exists()) {
            const data = updatedDoc.data();
            const updatedProfile: BusinessProfile = {
              id: updatedDoc.id,
              userId: data.userId,
              businessName: data.businessName,
              businessType: data.businessType,
              description: data.description,
              logo: data.logo,
              profilePhoto: data.profilePhoto,
              coverImage: data.coverImage,
              coverPhoto: data.coverPhoto,
              contactInfo: data.contactInfo,
              location: data.location,
              establishedYear: data.establishedYear,
              website: data.website,
              socialMedia: data.socialMedia,
              isVerified: data.isVerified,
              verificationDate: data.verificationDate?.toDate(),
              verificationDocuments: data.verificationDocuments || [],
              totalPosts: data.totalPosts || 0,
              totalViews: data.totalViews || 0,
              totalLikes: data.totalLikes || 0,
              totalComments: data.totalComments || 0,
              totalShares: data.totalShares || 0,
              totalDownloads: data.totalDownloads || 0,
              allowDirectMessages: data.allowDirectMessages,
              allowPhoneCalls: data.allowPhoneCalls,
              businessHours: data.businessHours || [],
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date(),
            };

            // Cache offline
            await this.cacheProfileOffline(updatedProfile);

            return { success: true, data: updatedProfile };
          }
        } catch (error) {
          console.error('❌ Online profile update failed:', error);
          return { success: false, error: 'Failed to update business profile online' };
        }
      }

      return { success: false, error: 'Update requires internet connection' };
    } catch (error) {
      console.error('❌ Error updating business profile:', error);
      return { success: false, error: 'Failed to update business profile' };
    }
  }

  // Create business profile
  async createBusinessProfile(profileData: Omit<BusinessProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<BusinessProfile>> {
    try {
      const now = new Date();
      const profile: Omit<BusinessProfile, 'id'> = {
        ...profileData,
        totalPosts: 0,
        totalViews: 0,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
        totalDownloads: 0,
        isVerified: false, // Will be verified after review
        createdAt: now,
        updatedAt: now,
      };

      if (this.isOnline) {
        try {
          const docRef = await addDoc(this.profilesCollection, {
            ...profile,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });

          const createdProfile = { ...profile, id: docRef.id } as BusinessProfile;

          // Cache offline
          await this.cacheProfileOffline(createdProfile);


          return { success: true, data: createdProfile };
        } catch (error) {
          console.error('❌ Online profile creation failed, caching offline:', error);
          // Fall through to offline handling
        }
      }

      // Offline handling
      const offlineId = `offline_profile_${Date.now()}_${profileData.userId}`;
      const createdProfile = { ...profile, id: offlineId } as BusinessProfile;

      await this.cacheProfileOffline(createdProfile);


      return { success: true, data: createdProfile };
    } catch (error) {
      console.error('❌ Error creating business profile:', error);
      return { success: false, error: 'Failed to create business profile' };
    }
  }

  // Get all business profiles for a user
  async getUserBusinessProfiles(userId: string): Promise<ApiResponse<BusinessProfile[]>> {
    try {
      console.log('🏢 BusinessUserService: Getting all business profiles for user:', userId);

      if (this.isOnline) {
        try {
          // Query all business profiles for this user
          const profilesQuery = query(
            this.profilesCollection,
            where('userId', '==', userId)
          );

          const querySnapshot = await getDocs(profilesQuery);
          const profiles: BusinessProfile[] = [];

          querySnapshot.forEach((doc) => {
            const data = doc.data();
            const profile: BusinessProfile = {
              id: doc.id,
              userId: data.userId,
              businessName: data.businessName,
              businessType: data.businessType,
              description: data.description,
              logo: data.logo,
              profilePhoto: data.profilePhoto,
              coverImage: data.coverImage,
              coverPhoto: data.coverPhoto,
              contactInfo: data.contactInfo,
              location: data.location,
              establishedYear: data.establishedYear,
              website: data.website,
              socialMedia: data.socialMedia,
              isVerified: data.isVerified,
              verificationDate: data.verificationDate?.toDate(),
              verificationDocuments: data.verificationDocuments || [],
              totalPosts: data.totalPosts || 0,
              totalViews: data.totalViews || 0,
              totalLikes: data.totalLikes || 0,
              totalComments: data.totalComments || 0,
              totalShares: data.totalShares || 0,
              totalDownloads: data.totalDownloads || 0,
              allowDirectMessages: data.allowDirectMessages,
              allowPhoneCalls: data.allowPhoneCalls,
              businessHours: data.businessHours || [],
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date(),
            };
            profiles.push(profile);
          });

          console.log(`✅ Found ${profiles.length} business profiles for user`);
          return { success: true, data: profiles };
        } catch (error) {
          console.error('❌ Online profiles fetch failed:', error);
          return { success: false, error: 'Failed to fetch business profiles online' };
        }
      }

      return { success: false, error: 'Getting profiles requires internet connection' };
    } catch (error) {
      console.error('❌ Error getting user business profiles:', error);
      return { success: false, error: 'Failed to get user business profiles' };
    }
  }

  // Get business profile by user ID
  async getBusinessProfile(userId: string): Promise<ApiResponse<BusinessProfile>> {
    try {
      if (this.isOnline) {
        try {
          const q = query(this.profilesCollection, where('userId', '==', userId));
          const snapshot = await getDocs(q);

          if (!snapshot.empty) {
            const doc = snapshot.docs[0];
            const data = doc.data();
            const profile: BusinessProfile = {
              id: doc.id,
              userId: data.userId,
              businessName: data.businessName,
              businessType: data.businessType,
              description: data.description,
              logo: data.logo,
              coverImage: data.coverImage,
              contactInfo: data.contactInfo,
              location: data.location,
              establishedYear: data.establishedYear,
              website: data.website,
              socialMedia: data.socialMedia,
              isVerified: data.isVerified,
              verificationDate: data.verificationDate?.toDate(),
              verificationDocuments: data.verificationDocuments || [],
              totalPosts: data.totalPosts || 0,
              totalViews: data.totalViews || 0,
              totalLikes: data.totalLikes || 0,
              totalComments: data.totalComments || 0,
              totalShares: data.totalShares || 0,
              totalDownloads: data.totalDownloads || 0,
              allowDirectMessages: data.allowDirectMessages,
              allowPhoneCalls: data.allowPhoneCalls,
              businessHours: data.businessHours || [],
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date(),
            };

            // Cache offline
            await this.cacheProfileOffline(profile);

            return { success: true, data: profile };
          }
        } catch (error) {
          console.error('❌ Online profile fetch failed, checking offline:', error);
          // Fall through to offline handling
        }
      }

      // Try offline cache
      const cachedProfile = await this.getCachedProfileOffline(userId);
      if (cachedProfile) {
        return { success: true, data: cachedProfile };
      }

      return { success: false, error: 'Business profile not found' };
    } catch (error) {
      console.error('❌ Error getting business profile:', error);
      return { success: false, error: 'Failed to get business profile' };
    }
  }

  // Get business profile by business ID
  async getBusinessProfileById(businessId: string): Promise<ApiResponse<BusinessProfile>> {
    try {
      if (this.isOnline) {
        try {
          const docRef = doc(this.profilesCollection, businessId);
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            const data = docSnap.data();
            const profile: BusinessProfile = {
              id: docSnap.id,
              userId: data.userId,
              businessName: data.businessName,
              businessType: data.businessType,
              description: data.description,
              logo: data.logo,
              profilePhoto: data.profilePhoto,
              coverImage: data.coverImage,
              coverPhoto: data.coverPhoto,
              contactInfo: data.contactInfo,
              location: data.location,
              establishedYear: data.establishedYear,
              website: data.website,
              socialMedia: data.socialMedia,
              isVerified: data.isVerified,
              verificationDate: data.verificationDate?.toDate(),
              verificationDocuments: data.verificationDocuments || [],
              totalPosts: data.totalPosts || 0,
              totalViews: data.totalViews || 0,
              totalLikes: data.totalLikes || 0,
              totalComments: data.totalComments || 0,
              totalShares: data.totalShares || 0,
              totalDownloads: data.totalDownloads || 0,
              allowDirectMessages: data.allowDirectMessages,
              allowPhoneCalls: data.allowPhoneCalls,
              businessHours: data.businessHours || [],
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date(),
            };

            return { success: true, data: profile };
          }
        } catch (error) {
          console.error('❌ Online profile fetch by ID failed:', error);
        }
      }

      return { success: false, error: 'Business profile not found' };
    } catch (error) {
      console.error('❌ Error getting business profile by ID:', error);
      return { success: false, error: 'Failed to get business profile' };
    }
  }

  // Cache profile data offline
  private async cacheProfileOffline(profile: BusinessProfile): Promise<void> {
    try {
      console.log('💾 BusinessUserService: Caching profile offline:', profile.id, profile.businessName);
      const database = offlineDatabaseService.getDatabase();

      if (!database) {
        console.error('❌ BusinessUserService: Database not initialized for caching');
        return;
      }

      await database.runAsync(`
        INSERT OR REPLACE INTO business_profiles (
          id, userId, businessName, businessType, description, logo, coverImage,
          contactInfo, location, establishedYear, website, socialMedia,
          isVerified, verificationDate, verificationDocuments,
          totalPosts, totalViews, totalLikes, totalComments, totalShares, totalDownloads,
          allowDirectMessages, allowPhoneCalls, businessHours,
          createdAt, updatedAt, syncStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        profile.id,
        profile.userId,
        profile.businessName,
        profile.businessType,
        profile.description,
        profile.logo || null,
        profile.coverImage || null,
        JSON.stringify(profile.contactInfo),
        JSON.stringify(profile.location),
        profile.establishedYear || null,
        profile.website || null,
        JSON.stringify(profile.socialMedia || {}),
        profile.isVerified ? 1 : 0,
        profile.verificationDate?.getTime() || null,
        JSON.stringify(profile.verificationDocuments),
        profile.totalPosts,
        profile.totalViews,
        profile.totalLikes,
        profile.totalComments,
        profile.totalShares,
        profile.totalDownloads,
        profile.allowDirectMessages ? 1 : 0,
        profile.allowPhoneCalls ? 1 : 0,
        JSON.stringify(profile.businessHours),
        profile.createdAt.getTime(),
        profile.updatedAt.getTime(),
        this.isOnline ? 'synced' : 'pending'
      ]);
      console.log('✅ BusinessUserService: Profile cached successfully:', profile.id);
    } catch (error) {
      console.error('❌ BusinessUserService: Error caching profile offline:', error);
    }
  }

  // Get cached profile offline
  private async getCachedProfileOffline(userId: string): Promise<BusinessProfile | null> {
    try {
      console.log('📱 BusinessUserService: Getting cached profile for user:', userId);
      const database = offlineDatabaseService.getDatabase();

      if (!database) {
        console.error('❌ BusinessUserService: Database not initialized');
        return null;
      }

      const result = await database.getFirstAsync(`
        SELECT * FROM business_profiles WHERE userId = ?
      `, [userId]) as any;

      if (result) {
        console.log('📱 BusinessUserService: Found cached profile:', result.businessName);
        return {
          id: result.id,
          userId: result.userId,
          businessName: result.businessName,
          businessType: result.businessType,
          description: result.description,
          logo: result.logo,
          coverImage: result.coverImage,
          contactInfo: JSON.parse(result.contactInfo || '{}'),
          location: JSON.parse(result.location || '{}'),
          establishedYear: result.establishedYear,
          website: result.website,
          socialMedia: JSON.parse(result.socialMedia || '{}'),
          isVerified: Boolean(result.isVerified),
          verificationDate: result.verificationDate ? new Date(result.verificationDate) : undefined,
          verificationDocuments: JSON.parse(result.verificationDocuments || '[]'),
          totalPosts: result.totalPosts || 0,
          totalViews: result.totalViews || 0,
          totalLikes: result.totalLikes || 0,
          totalComments: result.totalComments || 0,
          totalShares: result.totalShares || 0,
          totalDownloads: result.totalDownloads || 0,
          allowDirectMessages: Boolean(result.allowDirectMessages),
          allowPhoneCalls: Boolean(result.allowPhoneCalls),
          businessHours: JSON.parse(result.businessHours || '[]'),
          createdAt: new Date(result.createdAt),
          updatedAt: new Date(result.updatedAt),
        };
      }

      console.log('📱 BusinessUserService: No cached profile found for user:', userId);
      return null;
    } catch (error) {
      console.error('❌ BusinessUserService: Error getting cached profile offline:', error);
      return null;
    }
  }

  // Verify business profile
  async verifyBusinessProfile(profileId: string): Promise<ApiResponse<boolean>> {
    try {
      const updateData = {
        isVerified: true,
        verificationDate: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      if (this.isOnline) {
        try {
          await updateDoc(doc(this.profilesCollection, profileId), updateData);

          return { success: true, data: true };
        } catch (error) {
          console.error('❌ Online verification failed, caching offline:', error);
          // Cache for later sync
          await this.cacheVerificationOffline(profileId, updateData);
          return { success: true, data: true };
        }
      } else {
        // Cache for later sync
        await this.cacheVerificationOffline(profileId, updateData);

        return { success: true, data: true };
      }
    } catch (error) {
      console.error('❌ Error verifying business profile:', error);
      return { success: false, error: 'Failed to verify business profile' };
    }
  }

  // Cache verification update offline
  private async cacheVerificationOffline(profileId: string, updateData: any): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_profile_updates (
          profileId, updateData, timestamp, syncStatus
        ) VALUES (?, ?, ?, ?)
      `, [
        profileId,
        JSON.stringify(updateData),
        Date.now(),
        'pending'
      ]);
    } catch (error) {
      console.error('❌ Error caching verification offline:', error);
    }
  }
}

// ==================== BUSINESS POSTS SERVICE ====================

export class BusinessPostsService {
  private postsCollection = collection(db, COLLECTIONS.BUSINESS_POSTS);
  private commentsCollection = collection(db, COLLECTIONS.BUSINESS_COMMENTS);
  private isOnline: boolean = true;

  constructor() {
    // Monitor network state
    networkStateManager.addListener('businessService', (networkState) => {
      this.isOnline = networkState.isConnected;
    });
  }

  // Create new business post
  async createPost(postData: Omit<BusinessPost, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<BusinessPost>> {
    try {
      console.log('📦 BusinessPostsService: Creating post with data:', {
        title: postData.title,
        businessName: postData.businessName,
        category: postData.category,
        isOnline: this.isOnline
      });

      const now = new Date();
      const post: Omit<BusinessPost, 'id'> = {
        ...postData,
        views: 0,
        likes: [],
        comments: [],
        shares: 0,
        downloads: 0,
        isActive: true,
        isPinned: false,
        isPromoted: false,
        createdAt: now,
        updatedAt: now,
      };

      if (this.isOnline) {
        try {
          console.log('📦 BusinessPostsService: Creating post online in Firebase...');
          const docRef = await addDoc(this.postsCollection, {
            ...post,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });

          const createdPost = { ...post, id: docRef.id } as BusinessPost;
          console.log('📦 BusinessPostsService: Post created online with ID:', docRef.id);

          // Cache offline
          await this.cachePostOffline(createdPost);

          return { success: true, data: createdPost };
        } catch (error) {
          console.error('❌ BusinessPostsService: Online post creation failed, caching offline:', error);
          // Fall through to offline handling
        }
      }

      // Offline handling
      console.log('📦 BusinessPostsService: Creating post offline...');
      const offlineId = `offline_post_${Date.now()}_${postData.businessId}`;
      const createdPost = { ...post, id: offlineId } as BusinessPost;

      await this.cachePostOffline(createdPost);
      console.log('📦 BusinessPostsService: Post cached offline with ID:', offlineId);

      return { success: true, data: createdPost };
    } catch (error) {
      console.error('❌ BusinessPostsService: Error creating business post:', error);
      return { success: false, error: 'Failed to create business post' };
    }
  }

  // Cache post data offline
  private async cachePostOffline(post: BusinessPost): Promise<void> {
    try {
      console.log('💾 BusinessPostsService: Caching post offline:', post.id, post.title);
      const database = offlineDatabaseService.getDatabase();

      if (!database) {
        console.error('❌ BusinessPostsService: Database not initialized for caching');
        return;
      }

      await database.runAsync(`
        INSERT OR REPLACE INTO business_posts (
          id, businessId, businessName, businessLogo, businessType, isVerified,
          title, description, media, tags, category, price, oldPrice, currency, isNegotiable,
          availability, location, views, likes, comments, shares, downloads,
          isActive, isPinned, isPromoted, promotionEndsAt, priceHistory,
          createdAt, updatedAt, syncStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        post.id,
        post.businessId,
        post.businessName,
        post.businessLogo || null,
        post.businessType,
        post.isVerified ? 1 : 0,
        post.title,
        post.description,
        JSON.stringify(post.media),
        JSON.stringify(post.tags),
        post.category,
        post.price || null,
        (post as any).oldPrice || null,
        post.currency,
        post.isNegotiable ? 1 : 0,
        post.availability,
        JSON.stringify(post.location),
        post.views,
        JSON.stringify(post.likes),
        JSON.stringify(post.comments),
        post.shares,
        post.downloads,
        post.isActive ? 1 : 0,
        post.isPinned ? 1 : 0,
        post.isPromoted ? 1 : 0,
        post.promotionEndsAt?.getTime() || null,
        JSON.stringify((post as any).priceHistory || []),
        post.createdAt.getTime(),
        post.updatedAt.getTime(),
        this.isOnline ? 'synced' : 'pending'
      ]);
      console.log('✅ BusinessPostsService: Post cached successfully:', post.id);
    } catch (error) {
      console.error('❌ BusinessPostsService: Error caching post offline:', error);
    }
  }

  // Get business posts with filters
  async getPosts(
    filters: SearchFilters = {},
    page: number = 1,
    pageLimit: number = 20
  ): Promise<PaginatedResponse<BusinessPost>> {
    try {
      console.log('📡 BusinessPostsService: Getting posts with filters:', filters, 'page:', page, 'limit:', pageLimit, 'isOnline:', this.isOnline);
      let posts: BusinessPost[] = [];

      let onlineSuccess = false;

      if (this.isOnline) {
        try {
          console.log('📡 BusinessPostsService: Fetching posts from Firebase...');
          // Simplified Firestore query to avoid index requirements
          // Only use isActive filter and createdAt ordering to avoid complex composite indexes
          let q = query(
            this.postsCollection,
            where('isActive', '==', true),
            orderBy('createdAt', 'desc'),
            limit(pageLimit)
          );

          const snapshot = await getDocs(q);
          console.log('📡 BusinessPostsService: Firebase returned', snapshot.docs.length, 'documents');

          posts = snapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              businessId: data.businessId,
              businessName: data.businessName,
              businessLogo: data.businessLogo,
              businessType: data.businessType,
              isVerified: data.isVerified,
              title: data.title,
              description: data.description,
              media: data.media || [],
              tags: data.tags || [],
              category: data.category,
              price: data.price,
              oldPrice: data.oldPrice,
              currency: data.currency,
              isNegotiable: data.isNegotiable,
              priceHistory: data.priceHistory || [],
              availability: data.availability,
              location: data.location,
              views: data.views || 0,
              likes: data.likes || [],
              comments: data.comments || [],
              shares: data.shares || 0,
              downloads: data.downloads || 0,
              isActive: data.isActive,
              isPinned: data.isPinned,
              isPromoted: data.isPromoted,
              promotionEndsAt: data.promotionEndsAt?.toDate(),
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date(),
            } as BusinessPost;
          });

          console.log('📡 BusinessPostsService: Processed', posts.length, 'posts from Firebase');
          onlineSuccess = true;

          // Cache posts offline only if we got data from Firebase
          for (const post of posts) {
            await this.cachePostOffline(post);
          }
        } catch (error) {
          console.error('❌ BusinessPostsService: Online posts fetch failed:', error);
          onlineSuccess = false;
        }
      }

      // Only use cached posts if online fetch completely failed (not if it returned empty results)
      if (!onlineSuccess && !this.isOnline) {
        console.log('📱 BusinessPostsService: Offline mode, fetching from cache...');
        posts = await this.getCachedPostsOffline(filters, page, pageLimit);
        console.log('📱 BusinessPostsService: Loaded', posts.length, 'posts from offline cache');
      } else if (onlineSuccess) {
        console.log('✅ BusinessPostsService: Using Firebase data (even if empty) - this ensures deleted posts stay deleted');
      }

      // Apply client-side filters (moved from Firestore to avoid index requirements)
      let filteredPosts = posts;

      // Apply filters that were previously done in Firestore
      if (filters.businessType) {
        filteredPosts = filteredPosts.filter(post => post.businessType === filters.businessType);
      }

      if (filters.category) {
        filteredPosts = filteredPosts.filter(post => post.category === filters.category);
      }

      if (filters.isVerified !== undefined) {
        filteredPosts = filteredPosts.filter(post => post.isVerified === filters.isVerified);
      }

      // Apply text search
      if (filters.query) {
        const query = filters.query.toLowerCase();
        filteredPosts = filteredPosts.filter(post =>
          post.title.toLowerCase().includes(query) ||
          post.description.toLowerCase().includes(query) ||
          post.tags.some(tag => tag.toLowerCase().includes(query))
        );
      }

      // Apply location filter
      if (filters.location?.city) {
        filteredPosts = filteredPosts.filter(post =>
          post.location.city.toLowerCase().includes(filters.location!.city!.toLowerCase())
        );
      }

      // Apply price range filter
      if (filters.priceRange) {
        filteredPosts = filteredPosts.filter(post => {
          if (!post.price) return false;
          const { min, max } = filters.priceRange!;
          return (!min || post.price >= min) && (!max || post.price <= max);
        });
      }

      // Apply client-side sorting if different from default (newest)
      if (filters.sortBy && filters.sortBy !== 'newest') {
        switch (filters.sortBy) {
          case 'oldest':
            filteredPosts.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
            break;
          case 'most_viewed':
            filteredPosts.sort((a, b) => b.views - a.views);
            break;
          case 'price_low':
            filteredPosts.sort((a, b) => (a.price || 0) - (b.price || 0));
            break;
          case 'price_high':
            filteredPosts.sort((a, b) => (b.price || 0) - (a.price || 0));
            break;
        }
      }

      return {
        success: true,
        data: filteredPosts,
        pagination: {
          page,
          limit: pageLimit,
          total: filteredPosts.length,
          totalPages: Math.ceil(filteredPosts.length / pageLimit),
          hasMore: filteredPosts.length === pageLimit,
        },
      };
    } catch (error) {
      console.error('❌ Error getting business posts:', error);
      return {
        success: false,
        error: 'Failed to get business posts',
        data: [],
        pagination: {
          page,
          limit: pageLimit,
          total: 0,
          totalPages: 0,
          hasMore: false,
        },
      };
    }
  }

  // Get cached posts offline
  private async getCachedPostsOffline(
    filters: SearchFilters = {},
    page: number = 1,
    pageLimit: number = 20
  ): Promise<BusinessPost[]> {
    try {
      console.log('📱 BusinessPostsService: Getting cached posts with filters:', filters);
      const database = offlineDatabaseService.getDatabase();

      if (!database) {
        console.error('❌ BusinessPostsService: Database not initialized');
        return [];
      }

      let query = `SELECT * FROM business_posts WHERE isActive = 1`;
      const params: any[] = [];

      // Add filters
      if (filters.businessType) {
        query += ` AND businessType = ?`;
        params.push(filters.businessType);
      }

      if (filters.category) {
        query += ` AND category = ?`;
        params.push(filters.category);
      }

      if (filters.isVerified !== undefined) {
        query += ` AND isVerified = ?`;
        params.push(filters.isVerified ? 1 : 0);
      }

      // Add ordering
      query += ` ORDER BY createdAt DESC`;

      // Add pagination
      query += ` LIMIT ? OFFSET ?`;
      params.push(pageLimit, (page - 1) * pageLimit);

      console.log('📱 BusinessPostsService: Executing query:', query, 'with params:', params);
      const results = await database.getAllAsync(query, params);
      console.log('📱 BusinessPostsService: Query returned', results.length, 'results');

      const posts = results.map((result: any) => ({
        id: result.id,
        businessId: result.businessId,
        businessName: result.businessName,
        businessLogo: result.businessLogo,
        businessType: result.businessType,
        isVerified: Boolean(result.isVerified),
        title: result.title,
        description: result.description,
        media: JSON.parse(result.media || '[]'),
        tags: JSON.parse(result.tags || '[]'),
        category: result.category,
        price: result.price,
        oldPrice: result.oldPrice,
        currency: result.currency,
        isNegotiable: Boolean(result.isNegotiable),
        priceHistory: JSON.parse(result.priceHistory || '[]'),
        availability: result.availability,
        location: JSON.parse(result.location || '{}'),
        views: result.views || 0,
        likes: JSON.parse(result.likes || '[]'),
        comments: JSON.parse(result.comments || '[]'),
        shares: result.shares || 0,
        downloads: result.downloads || 0,
        isActive: Boolean(result.isActive),
        isPinned: Boolean(result.isPinned),
        isPromoted: Boolean(result.isPromoted),
        promotionEndsAt: result.promotionEndsAt ? new Date(result.promotionEndsAt) : undefined,
        createdAt: new Date(result.createdAt),
        updatedAt: new Date(result.updatedAt),
      })) as BusinessPost[];

      console.log('📱 BusinessPostsService: Returning', posts.length, 'cached posts');
      return posts;
    } catch (error) {
      console.error('❌ BusinessPostsService: Error getting cached posts offline:', error);
      return [];
    }
  }

  // Like/Unlike post
  async toggleLike(postId: string, userId: string): Promise<ApiResponse<boolean>> {
    try {
      // Skip Firebase update for local posts
      if (postId.startsWith('temp_') || postId.startsWith('offline_')) {
        console.log('⚠️ BusinessPostsService: Skipping like toggle for local post:', postId);
        await this.cacheLikeOffline(postId, userId);
        return { success: true, data: true };
      }

      if (this.isOnline) {
        try {
          const postRef = doc(this.postsCollection, postId);
          const postDoc = await getDoc(postRef);

          if (postDoc.exists()) {
            const data = postDoc.data();
            const likes = data.likes || [];
            const isLiked = likes.includes(userId);

            if (isLiked) {
              await updateDoc(postRef, {
                likes: arrayRemove(userId),
                updatedAt: serverTimestamp(),
              });
            } else {
              await updateDoc(postRef, {
                likes: arrayUnion(userId),
                updatedAt: serverTimestamp(),
              });
            }

            return { success: true, data: !isLiked };
          } else {
            console.error('❌ BusinessPostsService: Cannot toggle like - document does not exist:', postId);
            await this.cacheLikeOffline(postId, userId);
            return { success: true, data: true };
          }
        } catch (error) {
          console.error('❌ Online like toggle failed:', error);

          // If it's a "No document to update" error, cache offline
          if (error instanceof Error && error.message.includes('No document to update')) {
            console.log('⚠️ BusinessPostsService: Document not found, caching like toggle offline');
            await this.cacheLikeOffline(postId, userId);
            return { success: true, data: true };
          }
        }
      }

      // Cache for later sync
      await this.cacheLikeOffline(postId, userId);
      return { success: true, data: true };
    } catch (error) {
      console.error('❌ Error toggling like:', error);
      return { success: false, error: 'Failed to toggle like' };
    }
  }

  // Increment view count
  async incrementViews(postId: string): Promise<ApiResponse<boolean>> {
    try {
      // Skip Firebase update for local posts
      if (postId.startsWith('temp_') || postId.startsWith('offline_')) {
        console.log('⚠️ BusinessPostsService: Skipping view increment for local post:', postId);
        await this.cacheViewOffline(postId);
        return { success: true, data: true };
      }

      if (this.isOnline) {
        try {
          const postRef = doc(this.postsCollection, postId);

          // Check if document exists before updating
          const { getDoc } = await import('firebase/firestore');
          const docSnapshot = await getDoc(postRef);

          if (!docSnapshot.exists()) {
            console.error('❌ BusinessPostsService: Cannot increment views - document does not exist:', postId);
            await this.cacheViewOffline(postId);
            return { success: true, data: true };
          }

          await updateDoc(postRef, {
            views: increment(1),
            updatedAt: serverTimestamp(),
          });

          return { success: true, data: true };
        } catch (error) {
          console.error('❌ Online view increment failed:', error);

          // If it's a "No document to update" error, cache offline
          if (error instanceof Error && error.message.includes('No document to update')) {
            console.log('⚠️ BusinessPostsService: Document not found, caching view increment offline');
            await this.cacheViewOffline(postId);
            return { success: true, data: true };
          }
        }
      }

      // Cache for later sync
      await this.cacheViewOffline(postId);
      return { success: true, data: true };
    } catch (error) {
      console.error('❌ Error incrementing views:', error);
      return { success: false, error: 'Failed to increment views' };
    }
  }

  // Increment share count
  async incrementShares(postId: string): Promise<ApiResponse<boolean>> {
    try {
      // Skip Firebase update for local posts
      if (postId.startsWith('temp_') || postId.startsWith('offline_')) {
        console.log('⚠️ BusinessPostsService: Skipping share increment for local post:', postId);
        await this.cacheShareOffline(postId);
        return { success: true, data: true };
      }

      if (this.isOnline) {
        try {
          const postRef = doc(this.postsCollection, postId);

          // Check if document exists before updating
          const { getDoc } = await import('firebase/firestore');
          const docSnapshot = await getDoc(postRef);

          if (!docSnapshot.exists()) {
            console.error('❌ BusinessPostsService: Cannot increment shares - document does not exist:', postId);
            await this.cacheShareOffline(postId);
            return { success: true, data: true };
          }

          await updateDoc(postRef, {
            shares: increment(1),
            updatedAt: serverTimestamp(),
          });

          return { success: true, data: true };
        } catch (error) {
          console.error('❌ Online share increment failed:', error);

          // If it's a "No document to update" error, cache offline
          if (error instanceof Error && error.message.includes('No document to update')) {
            console.log('⚠️ BusinessPostsService: Document not found, caching share increment offline');
            await this.cacheShareOffline(postId);
            return { success: true, data: true };
          }
        }
      }

      // Cache for later sync
      await this.cacheShareOffline(postId);
      return { success: true, data: true };
    } catch (error) {
      console.error('❌ Error incrementing shares:', error);
      return { success: false, error: 'Failed to increment shares' };
    }
  }

  // Increment download count
  async incrementDownloads(postId: string): Promise<ApiResponse<boolean>> {
    try {
      // Skip Firebase update for local posts
      if (postId.startsWith('temp_') || postId.startsWith('offline_')) {
        console.log('⚠️ BusinessPostsService: Skipping download increment for local post:', postId);
        await this.cacheDownloadOffline(postId);
        return { success: true, data: true };
      }

      if (this.isOnline) {
        try {
          const postRef = doc(this.postsCollection, postId);

          // Check if document exists before updating
          const { getDoc } = await import('firebase/firestore');
          const docSnapshot = await getDoc(postRef);

          if (!docSnapshot.exists()) {
            console.error('❌ BusinessPostsService: Cannot increment downloads - document does not exist:', postId);
            await this.cacheDownloadOffline(postId);
            return { success: true, data: true };
          }

          await updateDoc(postRef, {
            downloads: increment(1),
            updatedAt: serverTimestamp(),
          });

          return { success: true, data: true };
        } catch (error) {
          console.error('❌ Online download increment failed:', error);

          // If it's a "No document to update" error, cache offline
          if (error instanceof Error && error.message.includes('No document to update')) {
            console.log('⚠️ BusinessPostsService: Document not found, caching download increment offline');
            await this.cacheDownloadOffline(postId);
            return { success: true, data: true };
          }
        }
      }

      // Cache for later sync
      await this.cacheDownloadOffline(postId);
      return { success: true, data: true };
    } catch (error) {
      console.error('❌ Error incrementing downloads:', error);
      return { success: false, error: 'Failed to increment downloads' };
    }
  }

  // Cache interaction methods
  private async cacheLikeOffline(postId: string, userId: string): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_post_interactions (
          postId, userId, type, timestamp, syncStatus
        ) VALUES (?, ?, ?, ?, ?)
      `, [postId, userId, 'like', Date.now(), 'pending']);
    } catch (error) {
      console.error('❌ Error caching like offline:', error);
    }
  }

  private async cacheViewOffline(postId: string): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_post_interactions (
          postId, userId, type, timestamp, syncStatus
        ) VALUES (?, ?, ?, ?, ?)
      `, [postId, 'system', 'view', Date.now(), 'pending']);
    } catch (error) {
      console.error('❌ Error caching view offline:', error);
    }
  }

  private async cacheShareOffline(postId: string): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_post_interactions (
          postId, userId, type, timestamp, syncStatus
        ) VALUES (?, ?, ?, ?, ?)
      `, [postId, 'system', 'share', Date.now(), 'pending']);
    } catch (error) {
      console.error('❌ Error caching share offline:', error);
    }
  }

  private async cacheDownloadOffline(postId: string): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO business_post_interactions (
          postId, userId, type, timestamp, syncStatus
        ) VALUES (?, ?, ?, ?, ?)
      `, [postId, 'system', 'download', Date.now(), 'pending']);
    } catch (error) {
      console.error('❌ Error caching download offline:', error);
    }
  }

  // Update a business post
  async updatePost(postId: string, updateData: Partial<BusinessPost>): Promise<ApiResponse<BusinessPost>> {
    try {
      console.log('📝 BusinessPostsService: Updating post:', postId, updateData);

      // Handle price changes - store old price and update price history
      let enhancedUpdateData = { ...updateData };
      if (updateData.price !== undefined) {
        try {
          // Get current post to check existing price
          const postRef = doc(this.postsCollection, postId);
          const { getDoc } = await import('firebase/firestore');
          const docSnapshot = await getDoc(postRef);

          if (docSnapshot.exists()) {
            const currentData = docSnapshot.data();
            const currentPrice = currentData.price;
            const newPrice = updateData.price;

            if (currentPrice && currentPrice !== newPrice) {
              console.log('💰 Price change detected:', currentPrice, '->', newPrice);

              // Set old price for strikethrough display
              enhancedUpdateData.oldPrice = currentPrice;

              // Update price history
              const existingHistory = currentData.priceHistory || [];
              const newPriceEntry = {
                price: currentPrice,
                date: new Date(),
                reason: (updateData as any).priceChangeReason || 'Price updated'
              };

              // Add current price to history before updating to new price
              enhancedUpdateData.priceHistory = [...existingHistory, newPriceEntry];

              console.log('📊 Price history updated. Total entries:', enhancedUpdateData.priceHistory.length);
            } else if (!currentPrice) {
              // First time setting a price - initialize price history
              enhancedUpdateData.priceHistory = [];
              console.log('🆕 First price set, initialized empty price history');
            }
          } else {
            // Document doesn't exist in Firebase, initialize price history
            enhancedUpdateData.priceHistory = [];
            console.log('🆕 New document, initialized empty price history');
          }
        } catch (error) {
          console.log('⚠️ Could not get current post for price comparison:', error);
          // Initialize empty price history as fallback
          enhancedUpdateData.priceHistory = enhancedUpdateData.priceHistory || [];
        }
      }

      const now = new Date();
      const updatePayload = {
        ...enhancedUpdateData,
        updatedAt: now,
      };

      // Check if this is a local post that hasn't been synced yet
      if (postId.startsWith('temp_') || postId.startsWith('offline_')) {
        console.log('⚠️ BusinessPostsService: Skipping Firebase update for local post:', postId);
        // Only update offline cache for local posts
        await this.updatePostOffline(postId, updatePayload);
        return { success: true, data: undefined };
      }

      // Force online update - this is critical for data consistency
      try {
        console.log('📝 BusinessPostsService: Updating post online in Firebase...');
        const postRef = doc(this.postsCollection, postId);

        // First check if the document exists
        const { getDoc } = await import('firebase/firestore');
        const docSnapshot = await getDoc(postRef);

        if (!docSnapshot.exists()) {
          console.error('❌ BusinessPostsService: Document does not exist in Firebase:', postId);
          // This might be a post that failed to sync - just update offline cache
          await this.updatePostOffline(postId, updatePayload);
          return { success: true, data: undefined };
        }

        await updateDoc(postRef, {
          ...updatePayload,
          updatedAt: serverTimestamp(),
        });
        console.log('✅ BusinessPostsService: Post updated in Firebase successfully');
      } catch (error) {
        console.error('❌ BusinessPostsService: Firebase update failed:', error);

        // If it's a "No document to update" error, just update offline cache
        if (error instanceof Error && error.message.includes('No document to update')) {
          console.log('⚠️ BusinessPostsService: Document not found in Firebase, updating offline cache only');
          try {
            await this.updatePostOffline(postId, updatePayload);
            return { success: true, data: undefined };
          } catch (offlineError) {
            console.error('❌ BusinessPostsService: Offline update also failed:', offlineError);
            return { success: false, error: `Post not found in any storage location. This post may have been deleted or corrupted.` };
          }
        }

        return { success: false, error: `Failed to update in Firebase: ${error}` };
      }

      // Update offline cache
      try {
        await this.updatePostOffline(postId, updatePayload);
      } catch (offlineError) {
        console.error('❌ BusinessPostsService: Offline cache update failed:', offlineError);
        // Don't fail the entire operation if offline cache update fails
        console.log('⚠️ BusinessPostsService: Continuing despite offline cache update failure');
      }

      // Return success - the post will be updated in the UI
      return { success: true, data: undefined };
    } catch (error) {
      console.error('❌ BusinessPostsService: Error updating post:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Failed to update post' };
    }
  }

  // Update post in offline cache
  private async updatePostOffline(postId: string, updateData: Partial<BusinessPost>): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();

      // Get current post data
      const result = await database.getFirstAsync(`
        SELECT * FROM business_posts WHERE id = ?
      `, [postId]);

      if (!result) {
        console.log('⚠️ BusinessPostsService: Post not found in offline cache, checking if it\'s a pending post...');

        // Check if this might be a pending post in postStorageService
        try {
          const { postStorageService } = await import('./postStorageService');
          const pendingPosts = await postStorageService.getPendingPosts();
          const pendingPost = pendingPosts.find(p => p.tempId === postId);

          if (pendingPost) {
            console.log('✅ Found post in pending storage, updating there instead');
            // Update the pending post directly
            const updatedPendingPost = { ...pendingPost, ...updateData };

            // Update the pending post in storage
            const allPendingPosts = pendingPosts.map(p =>
              p.tempId === postId ? updatedPendingPost : p
            );

            const AsyncStorage = await import('@react-native-async-storage/async-storage');
            await AsyncStorage.default.setItem('pending_posts', JSON.stringify(allPendingPosts));

            console.log('✅ BusinessPostsService: Pending post updated successfully:', postId);
            return;
          }
        } catch (pendingError) {
          console.error('❌ Error checking pending posts:', pendingError);
        }

        // If not found anywhere, create a new entry in offline cache
        console.log('⚠️ Post not found anywhere, creating new offline entry');
        await this.createOfflinePostEntry(postId, updateData);
        return;
      }

      // Prepare update fields
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      Object.entries(updateData).forEach(([key, value]) => {
        if (key === 'media' || key === 'tags' || key === 'location' || key === 'likes' || key === 'comments' || key === 'priceHistory') {
          updateFields.push(`${key} = ?`);
          updateValues.push(JSON.stringify(value));
        } else if (key === 'updatedAt' || key === 'createdAt') {
          updateFields.push(`${key} = ?`);
          updateValues.push((value as Date).getTime());
        } else if (typeof value === 'boolean') {
          updateFields.push(`${key} = ?`);
          updateValues.push(value ? 1 : 0);
        } else if (key === 'oldPrice' || key === 'price') {
          // Handle price fields specifically
          updateFields.push(`${key} = ?`);
          updateValues.push(value);
        } else {
          updateFields.push(`${key} = ?`);
          updateValues.push(value);
        }
      });

      updateFields.push('syncStatus = ?');
      updateValues.push(this.isOnline ? 'synced' : 'pending');
      updateValues.push(postId);

      await database.runAsync(`
        UPDATE business_posts
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `, updateValues);

      console.log('✅ BusinessPostsService: Post updated in offline cache:', postId);
    } catch (error) {
      console.error('❌ BusinessPostsService: Error updating post offline:', error);
      throw error;
    }
  }

  // Create a new offline post entry when post is not found
  private async createOfflinePostEntry(postId: string, updateData: Partial<BusinessPost>): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();

      // Create a minimal post entry with the update data
      const now = Date.now();
      const postEntry = {
        id: postId,
        businessId: updateData.businessId || 'unknown',
        businessName: updateData.businessName || 'Unknown Business',
        businessLogo: updateData.businessLogo || null,
        businessType: updateData.businessType || 'general',
        isVerified: updateData.isVerified ? 1 : 0,
        title: updateData.title || 'Updated Post',
        description: updateData.description || '',
        media: JSON.stringify(updateData.media || []),
        tags: JSON.stringify(updateData.tags || []),
        category: updateData.category || 'general',
        price: updateData.price || null,
        oldPrice: (updateData as any).oldPrice || null,
        currency: updateData.currency || 'EUR',
        isNegotiable: updateData.isNegotiable ? 1 : 0,
        availability: updateData.availability || 'available',
        location: JSON.stringify(updateData.location || {}),
        views: updateData.views || 0,
        likes: JSON.stringify(updateData.likes || []),
        comments: JSON.stringify(updateData.comments || []),
        shares: updateData.shares || 0,
        downloads: updateData.downloads || 0,
        isActive: updateData.isActive !== false ? 1 : 0,
        isPinned: updateData.isPinned ? 1 : 0,
        isPromoted: updateData.isPromoted ? 1 : 0,
        promotionEndsAt: updateData.promotionEndsAt ? (updateData.promotionEndsAt as Date).getTime() : null,
        priceHistory: JSON.stringify((updateData as any).priceHistory || []),
        createdAt: updateData.createdAt ? (updateData.createdAt as Date).getTime() : now,
        updatedAt: now,
        syncStatus: 'pending'
      };

      await database.runAsync(`
        INSERT OR REPLACE INTO business_posts (
          id, businessId, businessName, businessLogo, businessType, isVerified,
          title, description, media, tags, category, price, oldPrice, currency, isNegotiable,
          availability, location, views, likes, comments, shares, downloads,
          isActive, isPinned, isPromoted, promotionEndsAt, priceHistory,
          createdAt, updatedAt, syncStatus
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        postEntry.id, postEntry.businessId, postEntry.businessName, postEntry.businessLogo,
        postEntry.businessType, postEntry.isVerified, postEntry.title, postEntry.description,
        postEntry.media, postEntry.tags, postEntry.category, postEntry.price, postEntry.oldPrice, postEntry.currency,
        postEntry.isNegotiable, postEntry.availability, postEntry.location, postEntry.views,
        postEntry.likes, postEntry.comments, postEntry.shares, postEntry.downloads,
        postEntry.isActive, postEntry.isPinned, postEntry.isPromoted, postEntry.promotionEndsAt, postEntry.priceHistory,
        postEntry.createdAt, postEntry.updatedAt, postEntry.syncStatus
      ]);

      console.log('✅ BusinessPostsService: Created new offline post entry:', postId);
    } catch (error) {
      console.error('❌ BusinessPostsService: Error creating offline post entry:', error);
      throw error;
    }
  }

  // Debug function to check where a post exists
  async debugPostLocation(postId: string): Promise<void> {
    console.log('🔍 === POST LOCATION DEBUG ===');
    console.log('🔍 Searching for post:', postId);

    // Check Firebase
    try {
      const postRef = doc(this.postsCollection, postId);
      const docSnapshot = await getDoc(postRef);
      console.log('🔥 Firebase:', docSnapshot.exists() ? '✅ EXISTS' : '❌ NOT FOUND');
    } catch (error) {
      console.log('🔥 Firebase: ❌ ERROR -', error);
    }

    // Check offline cache
    try {
      const database = offlineDatabaseService.getDatabase();
      const result = await database.getFirstAsync(`
        SELECT id, title, syncStatus FROM business_posts WHERE id = ?
      `, [postId]);
      console.log('💾 Offline Cache:', result ? `✅ EXISTS (${(result as any).title}, status: ${(result as any).syncStatus})` : '❌ NOT FOUND');
    } catch (error) {
      console.log('💾 Offline Cache: ❌ ERROR -', error);
    }

    // Check pending posts
    try {
      const { postStorageService } = await import('./postStorageService');
      const pendingPosts = await postStorageService.getPendingPosts();
      const pendingPost = pendingPosts.find(p => p.tempId === postId);
      console.log('⏳ Pending Posts:', pendingPost ? `✅ EXISTS (${pendingPost.title}, status: ${pendingPost.syncStatus})` : '❌ NOT FOUND');
    } catch (error) {
      console.log('⏳ Pending Posts: ❌ ERROR -', error);
    }

    console.log('🔍 === END POST LOCATION DEBUG ===');
  }

  // Delete a business post
  async deletePost(postId: string): Promise<ApiResponse<void>> {
    try {
      console.log('🗑️ BusinessPostsService: Deleting post:', postId);

      // Force online deletion - this is critical for data consistency
      try {
        console.log('🗑️ BusinessPostsService: Deleting post online from Firebase...');
        const postRef = doc(this.postsCollection, postId);
        await deleteDoc(postRef);
        console.log('✅ BusinessPostsService: Post deleted from Firebase successfully');
      } catch (error) {
        console.error('❌ BusinessPostsService: Firebase deletion failed:', error);
        return { success: false, error: `Failed to delete from Firebase: ${error}` };
      }

      // Delete from offline cache
      console.log('🗑️ BusinessPostsService: Deleting post from offline cache...');
      const database = offlineDatabaseService.getDatabase();
      if (database) {
        await database.runAsync('DELETE FROM business_posts WHERE id = ?', [postId]);
        console.log('🗑️ BusinessPostsService: Post deleted from offline cache');
      }

      return { success: true };
    } catch (error) {
      console.error('❌ BusinessPostsService: Error deleting business post:', error);
      return { success: false, error: 'Failed to delete business post' };
    }
  }
}

// ==================== MAIN BUSINESS SERVICE ====================

export class BusinessService {
  public users = new BusinessUserService();
  public posts = new BusinessPostsService();

  // Get all user's business profiles
  async getUserBusinessProfiles(userId: string): Promise<{ success: boolean; data?: BusinessProfile[]; error?: string }> {
    try {
      console.log('👤 BusinessService: Getting all business profiles for user:', userId);
      const result = await this.users.getUserBusinessProfiles(userId);
      console.log('👤 BusinessService: Profiles fetch result:', {
        success: result.success,
        count: result.data?.length || 0,
        error: result.error
      });
      return result;
    } catch (error) {
      console.error('❌ BusinessService: Error getting user business profiles:', error);
      return { success: false, error: 'Failed to get user business profiles' };
    }
  }

  // Get user's business profile
  async getUserBusinessProfile(userId: string): Promise<BusinessProfile | null> {
    try {
      console.log('👤 BusinessService: Getting business profile for user:', userId);
      const result = await this.users.getBusinessProfile(userId);
      console.log('👤 BusinessService: Profile fetch result:', {
        success: result.success,
        hasData: !!result.data,
        error: result.error
      });
      return result.success ? (result.data || null) : null;
    } catch (error) {
      console.error('❌ BusinessService: Error getting user business profile:', error);
      return null;
    }
  }

  // Get business profile by business ID
  async getBusinessProfileById(businessId: string): Promise<BusinessProfile | null> {
    try {
      console.log('👤 BusinessService: Getting business profile by ID:', businessId);
      const result = await this.users.getBusinessProfileById(businessId);
      console.log('👤 BusinessService: Profile fetch result:', {
        success: result.success,
        hasData: !!result.data,
        error: result.error
      });
      return result.success ? (result.data || null) : null;
    } catch (error) {
      console.error('❌ BusinessService: Error getting business profile by ID:', error);
      return null;
    }
  }

  // Update business profile statistics
  async updateBusinessStats(businessId: string, statsUpdate: {
    views?: number;
    likes?: number;
    shares?: number;
    comments?: number;
  }): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('📊 BusinessService: Updating business stats:', businessId, statsUpdate);
      const result = await this.users.updateBusinessStats(businessId, statsUpdate);
      console.log('📊 BusinessService: Stats update result:', {
        success: result.success,
        error: result.error
      });
      return { success: result.success, error: result.error };
    } catch (error) {
      console.error('❌ BusinessService: Error updating business stats:', error);
      return { success: false, error: 'Failed to update business stats' };
    }
  }

  // Delete business profile
  async deleteBusinessProfile(profileId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('🏢 BusinessService: Deleting business profile:', profileId);
      const result = await this.users.deleteBusinessProfile(profileId);
      console.log('🏢 BusinessService: Profile deletion result:', {
        success: result.success,
        error: result.error
      });
      return { success: result.success, error: result.error };
    } catch (error) {
      console.error('❌ BusinessService: Error deleting business profile:', error);
      return { success: false, error: 'Failed to delete business profile' };
    }
  }

  // Update business profile
  async updateBusinessProfile(profileId: string, profileData: Partial<BusinessProfile>): Promise<{ success: boolean; data?: BusinessProfile; error?: string }> {
    try {
      console.log('🏢 BusinessService: Updating business profile:', profileId);
      const result = await this.users.updateBusinessProfile(profileId, profileData);
      console.log('🏢 BusinessService: Profile update result:', {
        success: result.success,
        hasData: !!result.data,
        error: result.error
      });
      return result;
    } catch (error) {
      console.error('❌ BusinessService: Error updating business profile:', error);
      return { success: false, error: 'Failed to update business profile' };
    }
  }

  // Create business profile
  async createBusinessProfile(profileData: Omit<BusinessProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<{ success: boolean; data?: BusinessProfile; error?: string }> {
    try {
      console.log('🏢 BusinessService: Creating business profile for user:', profileData.userId);

      // Add timestamps
      const fullProfileData = {
        ...profileData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      console.log('🏢 BusinessService: Calling users.createBusinessProfile...');
      const result = await this.users.createBusinessProfile(fullProfileData);

      console.log('🏢 BusinessService: Profile creation result:', {
        success: result.success,
        hasData: !!result.data,
        error: result.error
      });

      return result;
    } catch (error) {
      console.error('❌ BusinessService: Error creating business profile:', error);
      return { success: false, error: 'Failed to create business profile' };
    }
  }

  // Schedule trial expiration notification
  async scheduleTrialExpirationNotification(businessId: string, email: string, trialEndDate: Date): Promise<void> {
    try {
      // In a real app, this would integrate with email service and scheduling system
      console.log(`📧 Scheduled trial expiration notification for business ${businessId} to ${email} on ${trialEndDate}`);

      // Simulate scheduling email notification
      const timeUntilExpiration = trialEndDate.getTime() - Date.now();

      if (timeUntilExpiration > 0) {
        // Schedule reminder emails
        // 1 day before expiration
        const oneDayBefore = timeUntilExpiration - (24 * 60 * 60 * 1000);
        if (oneDayBefore > 0) {
          setTimeout(() => {
            this.sendTrialExpirationEmail(email, businessId, 'reminder');
          }, oneDayBefore);
        }

        // On expiration day
        setTimeout(() => {
          this.sendTrialExpirationEmail(email, businessId, 'expired');
          this.flagBusinessForPayment(businessId);
        }, timeUntilExpiration);
      }
    } catch (error) {
      console.error('❌ Error scheduling trial expiration notification:', error);
    }
  }

  // Send trial expiration email
  private async sendTrialExpirationEmail(email: string, businessId: string, type: 'reminder' | 'expired'): Promise<void> {
    try {
      const subject = type === 'reminder'
        ? 'Your IraChat Business Trial Expires Tomorrow'
        : 'Your IraChat Business Trial Has Expired';

      const message = type === 'reminder'
        ? `Your 3-day free trial expires tomorrow. Please complete your payment to continue using IraChat Business features.`
        : `Your 3-day free trial has expired. Your business posts are now hidden from public view. Please complete your payment to reactivate your business.`;

      // In a real app, integrate with email service like SendGrid, AWS SES, etc.
      console.log(`📧 Sending email to ${email}: ${subject} - ${message}`);

      // Simulate email sending
      return Promise.resolve();
    } catch (error) {
      console.error('❌ Error sending trial expiration email:', error);
    }
  }

  // Flag business for payment
  private async flagBusinessForPayment(businessId: string): Promise<void> {
    try {
      // In a real app, this would update the business profile in the database
      console.log(`🚩 Flagging business ${businessId} for payment - hiding posts from public view`);

      // Update business status to flagged
      // This would typically update the database record
      return Promise.resolve();
    } catch (error) {
      console.error('❌ Error flagging business for payment:', error);
    }
  }

  // Toggle like on business post
  async toggleLike(postId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // In a real app, this would update the post likes in the database
      console.log(`👍 Toggling like for post ${postId} by user ${userId}`);

      // Simulate successful like toggle
      return { success: true };
    } catch (error) {
      console.error('❌ Error toggling like:', error);
      return { success: false, error: 'Failed to toggle like' };
    }
  }

  // Increment share count
  async incrementShares(postId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // In a real app, this would increment the share count in the database
      console.log(`📤 Incrementing share count for post ${postId}`);

      // Simulate successful share increment
      return { success: true };
    } catch (error) {
      console.error('❌ Error incrementing shares:', error);
      return { success: false, error: 'Failed to increment shares' };
    }
  }

  // Get business posts for marketplace
  async getMarketplacePosts(filters: SearchFilters = {}): Promise<BusinessPost[]> {
    try {
      console.log('📡 BusinessService: Getting marketplace posts with filters:', filters);
      const result = await this.posts.getPosts(filters, 1, 50);
      console.log('📡 BusinessService: Posts fetch result:', {
        success: result.success,
        dataLength: result.data?.length || 0,
        error: result.error
      });
      return result.success ? (result.data || []) : [];
    } catch (error) {
      console.error('❌ BusinessService: Error getting marketplace posts:', error);
      // Return empty array to prevent infinite loops
      return [];
    }
  }

  // Get education/skills posts
  async getEducationPosts(filters: SearchFilters = {}): Promise<BusinessPost[]> {
    try {
      const educationFilters = {
        ...filters,
        businessType: 'school' as const,
      };
      const result = await this.posts.getPosts(educationFilters, 1, 50);
      return result.success ? result.data : [];
    } catch (error) {
      console.error('❌ Error getting education posts:', error);
      return [];
    }
  }

  // Search across all business content
  async searchBusinessContent(query: string, filters: SearchFilters = {}): Promise<BusinessPost[]> {
    try {
      const searchFilters = {
        ...filters,
        query,
      };
      const result = await this.posts.getPosts(searchFilters, 1, 100);
      return result.success ? result.data : [];
    } catch (error) {
      console.error('❌ Error searching business content:', error);
      return [];
    }
  }

  // Get sample posts for empty state (real Firebase data structure)
  async getSampleBusinessPosts(): Promise<BusinessPost[]> {
    try {
      // Try to get real posts first
      const realPosts = await this.getMarketplacePosts({});
      if (realPosts.length > 0) {
        return realPosts;
      }

      // If no real posts exist, return empty array to encourage real content creation
      return [];
    } catch (error) {
      console.error('❌ Error getting sample business posts:', error);
      return [];
    }
  }

  // Get posts for a specific business
  async getBusinessPosts(businessId: string): Promise<BusinessPost[]> {
    try {
      console.log('📡 BusinessService: Getting posts for business:', businessId);
      // Get all posts and filter by businessId since SearchFilters doesn't support businessId
      const result = await this.posts.getPosts({}, 1, 1000); // Get more posts to ensure we get all for this business
      console.log('📡 BusinessService: Posts fetch result:', {
        success: result.success,
        dataLength: result.data?.length || 0,
        error: result.error
      });

      if (result.success && result.data) {
        // Filter posts by businessId
        const businessPosts = result.data.filter(post => post.businessId === businessId);
        console.log('📡 BusinessService: Filtered to', businessPosts.length, 'posts for business:', businessId);
        return businessPosts;
      }

      return [];
    } catch (error) {
      console.error('❌ BusinessService: Error getting business posts:', error);
      return [];
    }
  }

  // Delete a business post
  async deletePost(postId: string): Promise<boolean> {
    try {
      console.log('🗑️ BusinessService: Deleting post:', postId);
      const result = await this.posts.deletePost(postId);
      console.log('🗑️ BusinessService: Delete post result:', {
        success: result.success,
        error: result.error
      });
      return result.success;
    } catch (error) {
      console.error('❌ BusinessService: Error deleting post:', error);
      return false;
    }
  }
}

// Export singleton instance
export const businessService = new BusinessService();

// Debug helper function - can be called from console
(global as any).debugPost = async (postId: string) => {
  console.log('🔍 Starting post debug for:', postId);
  await businessService.posts.debugPostLocation(postId);

  // Also check postStorageService debug
  try {
    const { postStorageService } = await import('./postStorageService');
    await postStorageService.debugCurrentState();
  } catch (error) {
    console.error('❌ Error running postStorageService debug:', error);
  }
};

// Test price change function - can be called from console
(global as any).testPriceChange = async (postId: string, newPrice: number) => {
  console.log('💰 Testing price change for post:', postId, 'New price:', newPrice);

  try {
    const result = await businessService.posts.updatePost(postId, {
      price: newPrice,
      priceChangeReason: 'Test price change'
    } as any);

    if (result.success) {
      console.log('✅ Price change successful!');
      await businessService.posts.debugPostLocation(postId);
    } else {
      console.error('❌ Price change failed:', result.error);
    }
  } catch (error) {
    console.error('❌ Error testing price change:', error);
  }
};
