// Enhanced Firestore Initialization with Offline Support for IraChat
import { collection, doc, serverTimestamp, setDoc, getDocs, limit, query } from 'firebase/firestore';
import { auth, db } from '../services/firebaseSimple';
import { Dimensions } from 'react-native';

// Get device dimensions for responsive initialization
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;

// Initialization result interface
export interface FirestoreInitResult {
  success: boolean;
  userId?: string;
  message: string;
  error?: string;
  isOffline?: boolean;
  collectionsCreated?: string[];
}

// Collection status interface
export interface CollectionStatus {
  name: string;
  exists: boolean;
  documentCount?: number;
  lastUpdated?: Date;
}

/**
 * Enhanced Firestore initialization with offline support and mobile optimization
 */
export const initializeFirestoreCollections = async (): Promise<FirestoreInitResult> => {
  try {
    const currentUser = auth?.currentUser;
    if (!currentUser) {
      return {
        success: false,
        message: "No authenticated user found",
        error: "Authentication required"
      };
    }

    const userId = currentUser.uid;
    const userEmail = currentUser.email || '';
    const collectionsCreated: string[] = [];

    // Check if we're offline
    const isOffline = !db;

    if (isOffline) {
      // Handle offline initialization
      try {
        const { offlineDatabaseService } = await import('../services/offlineDatabase');
        const database = offlineDatabaseService.getDatabase();

        // Store user data offline for later sync
        await database.runAsync(`
          INSERT OR REPLACE INTO pending_user_creation
          (user_id, email, display_name, phone_number, avatar, timestamp)
          VALUES (?, ?, ?, ?, ?, ?)
        `, [
          userId,
          userEmail,
          currentUser.displayName || 'IraChat User',
          currentUser.phoneNumber || '',
          currentUser.photoURL || '',
          Date.now()
        ]);

        return {
          success: true,
          userId,
          message: isSmallDevice
            ? "User saved offline"
            : "User data saved offline. Will sync when connection is restored.",
          isOffline: true
        };
      } catch (offlineError) {
        return {
          success: false,
          message: "Firebase unavailable and offline storage failed",
          error: offlineError instanceof Error ? offlineError.message : "Unknown error",
          isOffline: true
        };
      }
    }

    // Create user document with mobile-optimized settings
    const userSettings = {
      notifications: true,
      darkMode: false,
      language: 'en',
      privacy: {
        lastSeen: 'everyone',
        profilePhoto: 'everyone',
        status: 'everyone'
      },
      // Mobile-specific settings
      mobile: {
        compactMode: isSmallDevice,
        autoDownloadMedia: isSmallDevice ? "never" : "wifi", // Conservative on small devices
        highQualityImages: !isSmallDevice,
        animationsEnabled: true,
        hapticFeedback: true
      }
    };

    await setDoc(doc(db, 'users', userId), {
      uid: userId,
      email: userEmail,
      displayName: currentUser.displayName || 'IraChat User',
      phoneNumber: currentUser.phoneNumber || '',
      avatar: currentUser.photoURL || '',
      isOnline: false, // Users should not be marked as online during registration
      lastSeen: serverTimestamp(),
      createdAt: serverTimestamp(),
      status: isSmallDevice ? 'Using IraChat' : 'I use IraChat',
      settings: userSettings,
      deviceInfo: {
        type: isSmallDevice ? 'mobile-small' : 'mobile',
        screenWidth: SCREEN_WIDTH,
        platform: 'react-native'
      }
    });

    collectionsCreated.push('users');

    // Initialize essential collections structure
    const essentialCollections = ['chats', 'messages', 'groups', 'media'];

    // Verify collections are accessible (this creates them if they don't exist)
    for (const collectionName of essentialCollections) {
      try {
        const testQuery = query(collection(db, collectionName), limit(1));
        await getDocs(testQuery);
        collectionsCreated.push(collectionName);
      } catch (collectionError) {
        // Collection might not exist yet - that's okay
      }
    }

    // Cache user data offline for faster access
    try {
      const { offlineDatabaseService } = await import('../services/offlineDatabase');
      const database = offlineDatabaseService.getDatabase();

      await database.runAsync(`
        INSERT OR REPLACE INTO cached_users
        (id, data, timestamp)
        VALUES (?, ?, ?)
      `, [userId, JSON.stringify({
        uid: userId,
        email: userEmail,
        displayName: currentUser.displayName || 'IraChat User',
        avatar: currentUser.photoURL || '',
        settings: userSettings
      }), Date.now()]);
    } catch (cacheError) {
      // Cache failed - continue without it
    }

    return {
      success: true,
      userId,
      message: isSmallDevice
        ? "Setup complete"
        : "Firestore initialization completed successfully",
      collectionsCreated
    };

  } catch (error) {
    // Removed console.error
    const errorMessage = error instanceof Error ? error.message : "Initialization failed";
    return {
      success: false,
      message: errorMessage,
      error: errorMessage
    };
  }
};

/**
 * Enhanced collection status checker with offline support
 */
export const checkCollectionsExist = async (): Promise<Record<string, boolean>> => {
  try {
    const collections = ['users', 'chats', 'messages', 'groups', 'documents', 'media'];
    const results: Record<string, boolean> = {};

    if (!db) {
      // Check offline cache
      try {
        const { offlineDatabaseService } = await import('../services/offlineDatabase');
        const database = offlineDatabaseService.getDatabase();

        for (const collectionName of collections) {
          try {
            const result = await database.getAllAsync(`
              SELECT COUNT(*) as count FROM cached_${collectionName} LIMIT 1
            `);
            results[collectionName] = (result[0] as any)?.count > 0;
          } catch (offlineError) {
            results[collectionName] = false;
          }
        }

        return results;
      } catch (offlineError) {
        // Return all false if offline check fails
        collections.forEach(name => results[name] = false);
        return results;
      }
    }

    for (const collectionName of collections) {
      try {
        const q = query(collection(db, collectionName), limit(1));
        const snapshot = await getDocs(q);
        results[collectionName] = !snapshot.empty;
      } catch (collectionError) {
        results[collectionName] = false;
      }
    }

    return results;
  } catch (error) {
    // Removed console.error
    return {};
  }
};

/**
 * Get detailed collection status with document counts
 */
export const getCollectionStatus = async (): Promise<CollectionStatus[]> => {
  try {
    const collections = ['users', 'chats', 'messages', 'groups', 'documents', 'media'];
    const statuses: CollectionStatus[] = [];

    if (!db) {
      // Return offline status
      for (const collectionName of collections) {
        statuses.push({
          name: collectionName,
          exists: false,
          documentCount: 0,
          lastUpdated: new Date()
        });
      }
      return statuses;
    }

    for (const collectionName of collections) {
      try {
        const snapshot = await getDocs(collection(db, collectionName));
        statuses.push({
          name: collectionName,
          exists: !snapshot.empty,
          documentCount: snapshot.size,
          lastUpdated: new Date()
        });
      } catch (collectionError) {
        statuses.push({
          name: collectionName,
          exists: false,
          documentCount: 0,
          lastUpdated: new Date()
        });
      }
    }

    return statuses;
  } catch (error) {
    return [];
  }
};

/**
 * Sync offline user creation when connection is restored
 */
export const syncOfflineUserCreation = async (): Promise<FirestoreInitResult> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const pendingUsers = await database.getAllAsync(`
      SELECT * FROM pending_user_creation ORDER BY timestamp ASC
    `);

    if (pendingUsers.length === 0) {
      return {
        success: true,
        message: "No pending user creations to sync"
      };
    }

    let syncedCount = 0;

    for (const userRow of pendingUsers) {
      try {
        const user = userRow as any;

        if (db) {
          await setDoc(doc(db, 'users', user.user_id), {
            uid: user.user_id,
            email: user.email,
            displayName: user.display_name,
            phoneNumber: user.phone_number,
            avatar: user.avatar,
            isOnline: false, // Users should not be marked as online during offline sync
            lastSeen: serverTimestamp(),
            createdAt: serverTimestamp(),
            status: isSmallDevice ? 'Using IraChat' : 'I use IraChat',
            settings: {
              notifications: true,
              darkMode: false,
              language: 'en',
              privacy: {
                lastSeen: 'everyone',
                profilePhoto: 'everyone',
                status: 'everyone'
              },
              mobile: {
                compactMode: isSmallDevice,
                autoDownloadMedia: isSmallDevice ? "never" : "wifi",
                highQualityImages: !isSmallDevice,
                animationsEnabled: true,
                hapticFeedback: true
              }
            }
          });

          // Remove from offline storage
          await database.runAsync(`
            DELETE FROM pending_user_creation WHERE user_id = ?
          `, [user.user_id]);

          syncedCount++;
        }
      } catch (syncError) {
        // Skip this user and continue
      }
    }

    return {
      success: true,
      message: `Synced ${syncedCount} user creation${syncedCount !== 1 ? 's' : ''}`
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to sync offline user creations",
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
};

// Export default object with all functions
export default {
  initializeFirestoreCollections,
  checkCollectionsExist,
  getCollectionStatus,
  syncOfflineUserCreation,
};
