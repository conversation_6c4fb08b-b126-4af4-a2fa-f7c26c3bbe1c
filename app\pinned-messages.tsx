import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import React, { useState, useEffect } from "react";
import {
  Alert,
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useSelector } from 'react-redux';
import { RootState } from '../src/redux/store';
import { collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import { db } from '../src/services/firebaseSimple';

interface PinnedMessage {
  id: string;
  content: string;
  sender: string;
  chatName: string;
  timestamp: string;
  type: 'text' | 'image' | 'video' | 'document';
  isGroup: boolean;
}

export default function PinnedMessagesScreen() {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  // Real Firebase pinned messages data
  const [pinnedMessages, setPinnedMessages] = useState<PinnedMessage[]>([]);
  const [loading, setLoading] = useState(true);

  // Load real pinned messages from Firebase
  useEffect(() => {
    const loadPinnedMessages = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Query pinned messages from Firebase
        const pinnedQuery = query(
          collection(db, 'pinnedMessages'),
          where('userId', '==', currentUser.id),
          orderBy('pinnedAt', 'desc')
        );

        const querySnapshot = await getDocs(pinnedQuery);
        const messages: PinnedMessage[] = [];

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          messages.push({
            id: doc.id,
            content: data.content || data.text || 'Message content',
            sender: data.senderName || 'Unknown',
            chatName: data.chatName || 'Unknown Chat',
            timestamp: data.pinnedAt?.toDate?.()?.toLocaleString() || new Date().toLocaleString(),
            type: data.type || 'text',
            isGroup: data.isGroup || false,
          });
        });

        setPinnedMessages(messages);
      } catch (error) {
        console.error('Error loading pinned messages:', error);
        // Show empty state if error
        setPinnedMessages([]);
      } finally {
        setLoading(false);
      }
    };

    loadPinnedMessages();
  }, [currentUser]);

  const handleMessagePress = (message: PinnedMessage) => {
    Alert.alert(
      "Pinned Message",
      `From: ${message.sender}\nChat: ${message.chatName}\nTime: ${message.timestamp}`,
      [
        { text: "Go to Chat", onPress: () => router.push(`/chat/${message.id}`) },
        { text: "Unpin", style: "destructive", onPress: () => handleUnpinMessage(message.id) },
        { text: "Cancel", style: "cancel" }
      ]
    );
  };

  const handleUnpinMessage = async (messageId: string) => {
    try {
      // Remove from Firebase pinned messages
      // Implementation would go here
      Alert.alert("Success", "Message unpinned successfully");
    } catch (error) {
      console.error("Error unpinning message:", error);
      Alert.alert("Error", "Failed to unpin message");
    }
  };

  const getMessageIcon = (type: string) => {
    switch (type) {
      case 'image': return 'image-outline';
      case 'video': return 'videocam-outline';
      case 'audio': return 'musical-notes-outline';
      case 'voice': return 'mic-outline';
      case 'document': return 'document-text-outline';
      case 'file': return 'attach-outline';
      case 'location': return 'location-outline';
      case 'contact': return 'person-outline';
      case 'poll': return 'bar-chart-outline';
      case 'announcement': return 'megaphone-outline';
      case 'call': return 'call-outline';
      default: return 'chatbubble-outline';
    }
  };

  const getMessageCaption = (item: any) => {
    switch (item.type) {
      case 'image': return item.caption || 'Photo';
      case 'video': return item.caption || 'Video';
      case 'audio': return item.fileName || 'Audio file';
      case 'voice': return `Voice message (${item.duration || '0:00'})`;
      case 'document': return item.fileName || 'Document';
      case 'file': return item.fileName || 'File';
      case 'location': return 'Location shared';
      case 'contact': return `Contact: ${item.contactName || 'Unknown'}`;
      case 'poll': return `Poll: ${item.pollQuestion || 'Untitled poll'}`;
      case 'announcement': return 'Announcement';
      case 'call': return `${item.callType || 'Voice'} call`;
      default: return item.text || 'Message';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const renderPinnedMessage = ({ item }: { item: PinnedMessage }) => (
    <TouchableOpacity
      style={{
        backgroundColor: '#FFFFFF',
        marginHorizontal: 16,
        marginVertical: 6,
        padding: 16,
        borderRadius: 12,
        borderLeftWidth: 4,
        borderLeftColor: '#667eea',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}
      onPress={() => handleMessagePress(item)}
    >
      {/* Header */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
      }}>
        <View style={{
          width: 32,
          height: 32,
          borderRadius: 16,
          backgroundColor: '#F0F9FF',
          alignItems: 'center',
          justifyContent: 'center',
          marginRight: 12,
        }}>
          <Ionicons name={getMessageIcon(item.type) as any} size={16} color="#667eea" />
        </View>
        
        <View style={{ flex: 1 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={{
              fontSize: 14,
              fontWeight: '600',
              color: '#374151',
              marginRight: 8,
            }}>
              {item.sender}
            </Text>
            {item.isGroup && (
              <View style={{
                backgroundColor: '#E5E7EB',
                paddingHorizontal: 6,
                paddingVertical: 2,
                borderRadius: 8,
              }}>
                <Text style={{
                  fontSize: 10,
                  fontWeight: '500',
                  color: '#6B7280',
                }}>
                  GROUP
                </Text>
              </View>
            )}
          </View>
          <Text style={{
            fontSize: 12,
            color: '#9CA3AF',
            marginTop: 2,
          }}>
            {item.chatName} • {formatTimestamp(item.timestamp)}
          </Text>
        </View>

        <Ionicons name="bookmark" size={20} color="#F59E0B" />
      </View>

      {/* Message Content */}
      <Text style={{
        fontSize: 15,
        color: '#374151',
        lineHeight: 22,
        marginLeft: 44,
      }}>
        {getMessageCaption(item)}
      </Text>

      {/* Media Preview for images and videos */}
      {(item.type === 'image' || item.type === 'video') && item.content && (
        <View style={{
          marginTop: 8,
          marginLeft: 44,
          borderRadius: 8,
          overflow: 'hidden',
          backgroundColor: '#F3F4F6',
        }}>
          <Image
            source={{ uri: item.content }}
            style={{
              width: '100%',
              height: 120,
              resizeMode: 'cover',
            }}
          />
          {item.type === 'video' && (
            <View style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'rgba(0,0,0,0.3)',
            }}>
              <Ionicons name="play-circle" size={40} color="white" />
            </View>
          )}
        </View>
      )}
    </TouchableOpacity>
  );

  return (
    <View style={{ flex: 1, backgroundColor: '#F0F9FF' }}>
      {/* Header */}
      <View style={{
        backgroundColor: '#667eea',
        paddingTop: 55,
        paddingBottom: 8,
        paddingHorizontal: 20,
      }}>
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <TouchableOpacity
            onPress={() => router.back()}
            style={{ marginRight: 16, padding: 8 }}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={{
            fontSize: 20,
            fontWeight: 'bold',
            color: '#FFFFFF',
          }}>
            Pinned Messages
          </Text>
        </View>
      </View>

      {/* Info Banner */}
      <View style={{
        backgroundColor: '#FEF3C7',
        marginHorizontal: 16,
        marginTop: 16,
        padding: 12,
        borderRadius: 8,
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <Ionicons name="information-circle" size={20} color="#F59E0B" style={{ marginRight: 8 }} />
        <Text style={{
          fontSize: 14,
          color: '#92400E',
          flex: 1,
        }}>
          Tap any pinned message to go to the original chat or unpin it
        </Text>
      </View>

      {/* Messages List */}
      <FlatList
        data={pinnedMessages}
        renderItem={renderPinnedMessage}
        keyExtractor={(item) => item.id}
        contentContainerStyle={{ paddingVertical: 8 }}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={{
            alignItems: 'center',
            justifyContent: 'center',
            paddingVertical: 80,
          }}>
            <Ionicons name="bookmark-outline" size={64} color="#9CA3AF" />
            <Text style={{
              fontSize: 18,
              fontWeight: '600',
              color: '#6B7280',
              marginTop: 16,
              marginBottom: 8,
            }}>
              No Pinned Messages
            </Text>
            <Text style={{
              fontSize: 14,
              color: '#9CA3AF',
              textAlign: 'center',
              paddingHorizontal: 40,
            }}>
              Messages you pin in chats will appear here for quick access
            </Text>
          </View>
        }
      />
    </View>
  );
}
