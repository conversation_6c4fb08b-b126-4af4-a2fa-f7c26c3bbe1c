/**
 * Username Input Component with @ prefix
 * Automatically handles @ symbol for usernames
 */

import React, { useState } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  TextInputProps,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, BORDER_RADIUS } from '../../styles/iraChatDesignSystem';
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing } from '../../utils/responsiveUtils';

interface UsernameInputProps extends Omit<TextInputProps, 'value' | 'onChangeText'> {
  label?: string;
  placeholder?: string;
  error?: string;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  size?: 'small' | 'medium' | 'large';
  value: string;
  onChangeText: (text: string) => void;
}

export const UsernameInput: React.FC<UsernameInputProps> = ({
  label,
  placeholder,
  error,
  containerStyle,
  inputStyle,
  labelStyle,
  size = 'large',
  value,
  onChangeText,
  ...textInputProps
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleChangeText = (text: string) => {
    // System automatically adds @ symbol - user can enter username in any format
    // Just ensure no spaces and enforce maximum length
    let newText = text;

    // Remove any @ symbols that user might type (system will add it automatically)
    newText = newText.replace(/@/g, '');

    // Enforce maximum length of 12 characters
    if (newText.length > 12) {
      newText = newText.substring(0, 12);
    }

    // The @ symbol will be displayed by the UI but the actual value stored will be without @
    // The system will add @ when saving to backend
    onChangeText(newText);
  };

  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: ResponsiveScale.borderRadius(BORDER_RADIUS.lg),
      borderWidth: 2,
      backgroundColor: IRACHAT_COLORS.surface,
      minHeight: ResponsiveSpacing.minTouchTarget,
    };

    // Responsive size variations
    switch (size) {
      case 'small':
        baseStyle.paddingVertical = ResponsiveSpacing.sm;
        baseStyle.paddingHorizontal = ResponsiveSpacing.sm;
        baseStyle.minHeight = ComponentSizes.inputHeight.small;
        break;
      case 'large':
        baseStyle.paddingVertical = ResponsiveSpacing.lg;
        baseStyle.paddingHorizontal = ResponsiveSpacing.lg;
        baseStyle.minHeight = ComponentSizes.inputHeight.large;
        break;
      default:
        baseStyle.paddingVertical = ResponsiveSpacing.md;
        baseStyle.paddingHorizontal = ResponsiveSpacing.md;
        baseStyle.minHeight = ComponentSizes.inputHeight.medium;
    }

    // Border color based on state
    if (error) {
      baseStyle.borderColor = IRACHAT_COLORS.error;
    } else if (isFocused) {
      baseStyle.borderColor = IRACHAT_COLORS.primary;
    } else {
      baseStyle.borderColor = IRACHAT_COLORS.border;
    }

    return baseStyle;
  };

  const getInputStyle = (): TextStyle => {
    return {
      flex: 1,
      fontSize: size === 'large' ? TYPOGRAPHY.fontSize.lg : size === 'small' ? TYPOGRAPHY.fontSize.sm : TYPOGRAPHY.fontSize.base,
      fontFamily: TYPOGRAPHY.fontFamily,
      color: IRACHAT_COLORS.text,
      paddingLeft: ResponsiveSpacing.xs, // Reduced padding to ensure @ symbol is visible
      paddingRight: ResponsiveSpacing.sm, // Add right padding to ensure text doesn't get cut off
    };
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Label */}
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
        </Text>
      )}
      
      {/* Input container */}
      <View style={getInputContainerStyle()}>
        {/* @ Icon */}
        <Ionicons
          name="at"
          size={size === 'large' ? 24 : size === 'small' ? 16 : 20}
          color={isFocused ? IRACHAT_COLORS.primary : IRACHAT_COLORS.textMuted}
          style={styles.atIcon}
        />
        
        <TextInput
          {...textInputProps}
          style={[getInputStyle(), inputStyle]}
          placeholder={placeholder || 'username'}
          placeholderTextColor={IRACHAT_COLORS.textMuted}
          value={value}
          onChangeText={handleChangeText}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          autoCapitalize="none"
          autoCorrect={false}
          selectionColor={IRACHAT_COLORS.primary}
          cursorColor={IRACHAT_COLORS.primary}
        />
      </View>
      
      {/* Error message */}
      {error && (
        <Text style={styles.errorText}>
          {error}
        </Text>
      )}

      {/* Character counter */}
      <Text style={[
        styles.charCounter,
        value.length > 10 && styles.charCounterWarning,
        value.length >= 12 && styles.charCounterLimit
      ]}>
        {value.length}/12 characters
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: ResponsiveSpacing.sm,
  },
  label: {
    fontSize: ResponsiveTypography.fontSize.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
    fontWeight: '500',
    color: IRACHAT_COLORS.textSecondary,
    marginBottom: ResponsiveSpacing.xs,
    marginLeft: ResponsiveSpacing.sm,
  },
  atIcon: {
    marginLeft: ResponsiveSpacing.sm,
  },
  errorText: {
    marginTop: ResponsiveSpacing.xs,
    marginLeft: ResponsiveSpacing.sm,
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.error,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  charCounter: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: IRACHAT_COLORS.textMuted,
    marginTop: ResponsiveSpacing.xs,
    marginLeft: ResponsiveSpacing.sm,
    textAlign: 'right',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  charCounterWarning: {
    color: IRACHAT_COLORS.warning || '#FFA500',
  },
  charCounterLimit: {
    color: IRACHAT_COLORS.error,
    fontWeight: '600',
  },
});

export default UsernameInput;
