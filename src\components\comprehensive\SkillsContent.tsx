import React, { useState } from 'react';
import {
  View,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { BusinessPost } from '../../types/Business';
import { BusinessPostCard } from '../business/BusinessPostCard';
import { COLORS } from '../../constants/theme';

interface SkillsContentProps {
  educationPosts: BusinessPost[];
  currentUserId?: string;
  isLoading: boolean;
  isRefreshing: boolean;
  onRefresh: () => void;
  onLoadMore: () => void;
  onPostPress: (post: BusinessPost) => void;
  onChatPress: (businessId: string) => void;
  onFilterPress: () => void;
  onCreateCoursePress: () => void;
  isOnline: boolean;
  hasBusinessProfile: boolean;
}

export const SkillsContent: React.FC<SkillsContentProps> = ({
  educationPosts,
  currentUserId,
  isLoading,
  isRefreshing,
  onRefresh,
  onLoadMore,
  onPostPress,
  onChatPress,
  onFilterPress,
  onCreateCoursePress,
  isOnline,
  hasBusinessProfile,
}) => {
  const [selectedSkillCategory, setSelectedSkillCategory] = useState<string>('all');

  const skillCategories = [
    { key: 'all', label: 'All Skills', icon: 'school-outline' },
    { key: 'technology', label: 'Technology', icon: 'laptop-outline' },
    { key: 'business', label: 'Business', icon: 'briefcase-outline' },
    { key: 'creative', label: 'Creative', icon: 'color-palette-outline' },
    { key: 'language', label: 'Language', icon: 'language-outline' },
    { key: 'health', label: 'Health', icon: 'fitness-outline' },
    { key: 'trades', label: 'Trades', icon: 'hammer-outline' },
  ];

  const filteredPosts = selectedSkillCategory === 'all' 
    ? educationPosts 
    : educationPosts.filter(post => 
        post.tags?.some(tag => tag.toLowerCase().includes(selectedSkillCategory.toLowerCase()))
      );

  const renderSkillCategoryFilter = () => (
    <View style={styles.categoryContainer}>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={skillCategories}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.categoryChip,
              selectedSkillCategory === item.key && styles.activeCategoryChip
            ]}
            onPress={() => setSelectedSkillCategory(item.key)}
          >
            <Ionicons
              name={item.icon as any}
              size={16}
              color={selectedSkillCategory === item.key ? '#FFFFFF' : COLORS.textSecondary}
              style={styles.categoryIcon}
            />
            <Text style={[
              styles.categoryText,
              selectedSkillCategory === item.key && styles.activeCategoryText
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item.key}
        contentContainerStyle={styles.categoryContent}
      />
    </View>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <View style={styles.headerTop}>
        <Text style={styles.headerTitle}>Skills & Education</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerButton} onPress={onFilterPress}>
            <Ionicons name="filter-outline" size={20} color={COLORS.textSecondary} />
          </TouchableOpacity>
          
          {hasBusinessProfile && (
            <TouchableOpacity 
              style={[styles.headerButton, styles.createButton]} 
              onPress={onCreateCoursePress}
            >
              <Ionicons name="add" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Skills Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{educationPosts.length}</Text>
          <Text style={styles.statLabel}>Courses</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {educationPosts.reduce((sum, post) => sum + (post.views || 0), 0)}
          </Text>
          <Text style={styles.statLabel}>Total Views</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>
            {new Set(educationPosts.map(post => post.businessId)).size}
          </Text>
          <Text style={styles.statLabel}>Educators</Text>
        </View>
      </View>
      
      {renderSkillCategoryFilter()}
      
      {!isOnline && (
        <View style={styles.offlineNotice}>
          <Ionicons name="cloud-offline" size={16} color="#EF4444" />
          <Text style={styles.offlineText}>
            Showing cached courses. Connect to see latest content.
          </Text>
        </View>
      )}
    </View>
  );

  const renderEducationPost = ({ item }: { item: BusinessPost }) => (
    <View style={styles.educationPostWrapper}>
      <BusinessPostCard
        post={item}
        currentUserId={currentUserId || ''}
        onPress={onPostPress}
        onChatPress={onChatPress}
      />
      
      {/* Education-specific overlay */}
      <View style={styles.educationOverlay}>
        <View style={styles.educationBadge}>
          <Ionicons name="school" size={12} color="#FFFFFF" />
          <Text style={styles.educationBadgeText}>Course</Text>
        </View>
        
        {item.price && (
          <View style={styles.priceBadge}>
            <Text style={styles.priceText}>
              {item.currency} {item.price.toLocaleString()}
            </Text>
          </View>
        )}
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="school-outline" size={64} color={COLORS.textSecondary} />
      <Text style={styles.emptyTitle}>
        {selectedSkillCategory === 'all' 
          ? 'No Courses Available' 
          : `No ${selectedSkillCategory} Courses`
        }
      </Text>
      <Text style={styles.emptySubtitle}>
        {isOnline 
          ? hasBusinessProfile 
            ? 'Share your knowledge by creating a course!'
            : 'Register as an educator to start teaching'
          : 'Connect to internet to see available courses'
        }
      </Text>
      
      {isOnline && hasBusinessProfile && (
        <TouchableOpacity style={styles.emptyAction} onPress={onCreateCoursePress}>
          <Ionicons name="add" size={20} color="#FFFFFF" />
          <Text style={styles.emptyActionText}>Create Course</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderFooter = () => {
    if (!isLoading) return null;
    
    return (
      <View style={styles.footer}>
        <Text style={styles.loadingText}>Loading more courses...</Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredPosts}
        renderItem={renderEducationPost}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={!isLoading ? renderEmptyState : null}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={onRefresh}
            tintColor={COLORS.primary}
            colors={[COLORS.primary]}
          />
        }
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={filteredPosts.length === 0 ? styles.emptyContainer : undefined}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    backgroundColor: '#1F2937',
    paddingBottom: 16,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  headerTitle: {
    color: COLORS.text,
    fontSize: 20,
    fontWeight: '700',
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#374151',
  },
  createButton: {
    backgroundColor: COLORS.primary,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#374151',
    marginHorizontal: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    color: COLORS.text,
    fontSize: 18,
    fontWeight: '700',
  },
  statLabel: {
    color: COLORS.textSecondary,
    fontSize: 12,
    marginTop: 2,
  },
  statDivider: {
    width: 1,
    backgroundColor: '#4B5563',
  },
  categoryContainer: {
    paddingBottom: 8,
  },
  categoryContent: {
    paddingHorizontal: 16,
  },
  categoryChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#374151',
    marginRight: 8,
  },
  activeCategoryChip: {
    backgroundColor: COLORS.primary,
  },
  categoryIcon: {
    marginRight: 6,
  },
  categoryText: {
    color: COLORS.textSecondary,
    fontSize: 12,
    fontWeight: '500',
  },
  activeCategoryText: {
    color: '#FFFFFF',
  },
  offlineNotice: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FEF3C7',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  offlineText: {
    color: '#92400E',
    fontSize: 12,
    marginLeft: 8,
    flex: 1,
  },
  educationPostWrapper: {
    position: 'relative',
  },
  educationOverlay: {
    position: 'absolute',
    top: 16,
    right: 16,
    flexDirection: 'row',
    gap: 8,
  },
  educationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  educationBadgeText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    marginLeft: 4,
  },
  priceBadge: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priceText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyTitle: {
    color: COLORS.text,
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubtitle: {
    color: COLORS.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 20,
  },
  emptyAction: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 24,
    marginTop: 24,
  },
  emptyActionText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  footer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  emptyContainer: {
    flexGrow: 1,
  },
});
