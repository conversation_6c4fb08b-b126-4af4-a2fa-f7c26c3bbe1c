import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useEffect, useState, useCallback } from "react";
import { navigationService, ROUTES } from "../src/services/navigationService";
import {
    <PERSON>ert,
    ScrollView,
    Switch,
    Text,
    TouchableOpacity,
    View,
    ActivityIndicator,
    Modal,
    Dimensions,
    TextInput,
    StyleSheet,
    FlatList,
    Image,
} from "react-native";
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "../src/redux/store";
import { updateUser } from "../src/redux/userSlice";
import { realPrivacyService, PrivacySettings } from "../src/services/realPrivacyService";
import { networkStateManager } from "../src/services/networkStateManager";
import { realSyncService } from "../src/services/realSyncService";

import { ResponsiveContainer } from "../src/components/ui/ResponsiveContainer";
import { ResponsiveCard, ResponsiveListCard } from "../src/components/ui/ResponsiveCard";
import { ResponsiveHeader } from "../src/components/ui/ResponsiveHeader";
import { AnimatedButton } from "../src/components/ui/AnimatedButton";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from "../src/styles/iraChatDesignSystem";
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../src/utils/responsiveUtils";
import PrivacySettingsComponent from "../src/components/privacy/PrivacySettings";
import optimizedContactsService, { Contact } from "../src/services/optimizedContactsService";

const { width: SCREEN_WIDTH } = Dimensions.get('window');

const defaultSettings: Partial<PrivacySettings> = {
  lastSeen: "contacts",
  lastSeenCustomContacts: [],
  profilePhoto: "everyone",
  profilePhotoCustomContacts: [],
  status: "everyone",
  statusCustomContacts: [],
  about: "everyone",
  aboutCustomContacts: [],
  readReceipts: true,
  groupsAddMe: "contacts",
  groupsAddMeCustomContacts: [],
  liveLocation: false,
  callsFrom: "contacts",
  blockedContacts: [],
  twoStepVerification: false,
  disappearingMessages: false,
  disappearingMessagesDuration: "1day",
  disappearingMessagesScope: "nobody",
  disappearingMessagesCustomContacts: [],
  disappearingMessagesStorage: "delete_chat_only",
  screenshotNotification: true,
  screenshotControl: true,
  screenshotControlScope: "contacts",
  screenshotControlCustomContacts: [],
  onlineStatus: "everyone",
  forwardedMessages: true,
  forwardedMessagesScope: "contacts",
  forwardedMessagesCustomContacts: [],
  autoDownloadMedia: "wifi", // "never", "wifi", "always"
  securityNotifications: true,
};

export default function PrivacySettingsScreen() {
  const router = useRouter();
  const dispatch = useDispatch();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  
  const [settings, setSettings] = useState<PrivacySettings>(defaultSettings as PrivacySettings);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showTwoStepModal, setShowTwoStepModal] = useState(false);
  const [twoStepPin, setTwoStepPin] = useState('');
  const [blockedContacts, setBlockedContacts] = useState<any[]>([]);
  const [showAppLockSettings, setShowAppLockSettings] = useState(false);
  const [showContactSelection, setShowContactSelection] = useState(false);
  const [contactSelectionType, setContactSelectionType] = useState<'lastSeen' | 'profilePhoto' | 'about' | 'groupsAddMe' | 'disappearingMessagesScope' | 'forwardedMessagesScope' | 'screenshotControlScope'>('lastSeen');
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [loadingContacts, setLoadingContacts] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);

  // Global sync state management for the entire app
  const [globalSyncState, setGlobalSyncState] = useState({
    isOnline: false,
    isSyncing: false,
    syncProgress: 0,
    pendingSyncCount: 0,
    lastSyncTime: null as Date | null,
  });

  useEffect(() => {
    if (currentUser?.id) {
      loadSettings();
      loadBlockedContacts();

      // Initialize real sync service
      realSyncService.initialize().catch(error => {
        console.error('Failed to initialize sync service:', error);
      });
    }
  }, [currentUser]);

  // Global sync status monitoring for the entire app
  const updateGlobalSyncStatus = useCallback(async () => {
    try {
      const networkState = networkStateManager.getState();
      const syncStatus = realSyncService.getSyncStatus();

      const progress = syncStatus.inProgress && syncStatus.pending > 0 ?
        Math.max(10, 100 - (syncStatus.pending * 10)) : 100;

      setGlobalSyncState(prev => ({
        ...prev,
        isOnline: networkState.isConnected,
        isSyncing: syncStatus.inProgress,
        syncProgress: progress,
        pendingSyncCount: syncStatus.pending,
        lastSyncTime: !syncStatus.inProgress && syncStatus.pending === 0 ? new Date() : prev.lastSyncTime,
      }));
    } catch (error) {
      console.error('Error updating global sync status:', error);
    }
  }, []);

  // Listen for network status changes to update global sync indicator
  useEffect(() => {
    const handleNetworkChange = (networkState: any) => {
      if (networkState.isConnected) {
        // User came online - start syncing
        updateGlobalSyncStatus();
      } else {
        // User went offline
        setGlobalSyncState(prev => ({
          ...prev,
          isOnline: false,
          isSyncing: false,
        }));
      }
    };

    // Add network listener
    networkStateManager.addListener('privacySettings', handleNetworkChange, 1);

    // Monitor global sync status periodically
    const syncInterval = setInterval(() => {
      updateGlobalSyncStatus();
    }, 1000); // Check every 1 second for real-time updates

    // Initial sync status check
    updateGlobalSyncStatus();

    return () => {
      // Cleanup listener
      networkStateManager.removeListener('privacySettings');
      clearInterval(syncInterval);
    };
  }, [updateGlobalSyncStatus]);

  const loadSettings = async () => {
    if (!currentUser?.id) {
      console.log('🔐 No current user, setting loading to false');
      setLoading(false);
      return;
    }

    console.log('🔐 Loading privacy settings for user:', currentUser.id);
    setLoading(true);
    try {
      const result = await realPrivacyService.getPrivacySettings(currentUser.id);
      console.log('🔐 Privacy settings result:', result);
      if (result.success && result.settings) {
        setSettings(result.settings);
      } else {
        console.log('🔐 Using default settings');
        setSettings(defaultSettings as PrivacySettings);
      }
    } catch (error) {
      console.error("🔐 Error loading privacy settings:", error);
      // Use default settings on error
      setSettings(defaultSettings as PrivacySettings);
    } finally {
      console.log('🔐 Setting loading to false');
      setLoading(false);
    }
  };

  const loadBlockedContacts = async () => {
    if (!currentUser?.id) return;
    
    try {
      const result = await realPrivacyService.getBlockedContacts(currentUser.id);
      if (result.success && result.contacts) {
        setBlockedContacts(result.contacts);
      }
    } catch (error) {
      console.error("Error loading blocked contacts:", error);
    }
  };



  const saveSettings = async (newSettings: Partial<PrivacySettings>) => {
    if (!currentUser?.id) return;
    
    setSaving(true);
    try {
      const result = await realPrivacyService.updatePrivacySettings(currentUser.id, newSettings);
      if (result.success) {
        setSettings(prev => ({ ...prev, ...newSettings }));
        Alert.alert("Success", "Privacy settings updated successfully!");
      } else {
        Alert.alert("Error", result.error || "Failed to update privacy settings");
      }
    } catch (error) {
      console.error("Error saving privacy settings:", error);
      Alert.alert("Error", "Failed to save privacy settings.");
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = async (key: keyof PrivacySettings, value: any) => {
    if (!currentUser?.id) return;

    // Validate the setting value
    if (!validateMessagingSettings(key, value)) {
      Alert.alert("Invalid Setting", `Invalid value for ${key}: ${value}`);
      return;
    }

    // Update local state immediately for responsive UI
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);

    // Determine setting type for user feedback
    const settingNames: Record<string, string> = {
      readReceipts: 'Read Receipts',
      disappearingMessages: 'Disappearing Messages',
      screenshotNotification: 'Screenshot Notifications',
      screenshotControl: 'Screenshot Control',
      forwardedMessages: 'Forwarded Messages',
      autoDownloadMedia: 'Auto Download Media',
      liveLocation: 'Live Location',
      callsFrom: 'Calls From',
      groupsAddMe: 'Groups',
      lastSeen: 'Last Seen & Online',
      profilePhoto: 'Profile Photo',
      about: 'About',
    };

    const settingName = settingNames[key] || key;
    const isOnline = networkStateManager.isOnline();

    try {
      const result = await realPrivacyService.updatePrivacySettings(currentUser.id, { [key]: value });

      if (result.success) {
        // Show success message with sync status
        const statusMessage = isOnline ? "Updated and synced to cloud" : "Saved offline - Will sync when online";

        // For boolean settings, show enabled/disabled status
        let valueText = '';
        if (typeof value === 'boolean') {
          valueText = value ? 'enabled' : 'disabled';
        } else {
          valueText = `set to ${value}`;
        }

        Alert.alert(
          "Success",
          `${settingName} ${valueText} - ${statusMessage}`,
          [{ text: "OK" }]
        );
      } else {
        Alert.alert("Error", result.error || `Failed to update ${settingName}`);
        // Revert local changes on error
        loadSettings();
      }
    } catch (error) {
      console.error(`Error updating ${settingName}:`, error);
      Alert.alert("Error", `Failed to update ${settingName}`);
      // Revert local changes on error
      loadSettings();
    }
  };

  // Get display text for auto download setting
  const getAutoDownloadDisplay = (value: "never" | "wifi" | "always"): string => {
    switch (value) {
      case 'never':
        return 'Never';
      case 'wifi':
        return 'Wi-Fi Only';
      case 'always':
        return 'Always';
      default:
        return 'Wi-Fi Only';
    }
  };

  // Get display text for privacy setting
  const getPrivacySettingDisplay = (settingType: 'lastSeen' | 'profilePhoto' | 'about' | 'groupsAddMe' | 'disappearingMessagesScope' | 'forwardedMessagesScope' | 'screenshotControlScope'): string => {
    const settingValue = settings[settingType];
    const customContactsField = `${settingType}CustomContacts` as keyof PrivacySettings;
    const customContacts = settings[customContactsField] as string[] || [];

    switch (settingValue) {
      case 'everyone':
        return 'Everyone';
      case 'contacts':
        return 'My Contacts';
      case 'nobody':
        return 'Nobody';
      case 'custom':
        const count = customContacts.length;
        return `${count} contact${count !== 1 ? 's' : ''}`;
      default:
        return 'My Contacts';
    }
  };

  // Get display text for duration setting
  const getDurationDisplay = (duration: string): string => {
    switch (duration) {
      case '1hour':
        return '1 Hour';
      case '1day':
        return '1 Day';
      case '1week':
        return '1 Week';
      case '1month':
        return '1 Month';
      case 'never':
        return 'Never';
      default:
        return '1 Day';
    }
  };

  // Get display text for storage setting
  const getStorageDisplay = (storage: string): string => {
    switch (storage) {
      case 'delete_everywhere':
        return 'Delete Everywhere';
      case 'delete_chat_only':
        return 'Delete from Chat Only';
      case 'archive_locally':
        return 'Archive Locally';
      default:
        return 'Delete from Chat Only';
    }
  };

  // Load contacts for selection
  const loadContactsForSelection = async () => {
    setLoadingContacts(true);
    try {
      const contactsList = await optimizedContactsService.getIraChatContacts();
      // Filter to only IraChat users (contacts already on the platform)
      const iraChatContacts = contactsList.filter(contact =>
        contact.isIraChatUser === true
      );

      console.log(`🔐 Loaded ${iraChatContacts.length} IraChat contacts for privacy selection`);
      setContacts(iraChatContacts);
      setFilteredContacts(iraChatContacts); // Initialize filtered contacts
    } catch (error) {
      console.error('Error loading contacts:', error);
      Alert.alert('Error', 'Failed to load contacts');
    } finally {
      setLoadingContacts(false);
    }
  };

  // Filter contacts based on search query
  const filterContacts = (query: string) => {
    if (!query.trim()) {
      setFilteredContacts(contacts);
      return;
    }

    const filtered = contacts.filter(contact => {
      if (!contact) return false;

      try {
        const nameMatch = contact.name && typeof contact.name === 'string' ?
          contact.name.toLowerCase().includes(query.toLowerCase()) : false;
        const usernameMatch = contact.username && typeof contact.username === 'string' ?
          contact.username.toLowerCase().includes(query.toLowerCase()) : false;
        const phoneMatch = contact.phoneNumber && typeof contact.phoneNumber === 'string' ?
          contact.phoneNumber.includes(query) : false;
        return nameMatch || usernameMatch || phoneMatch;
      } catch (error) {
        console.error('Error filtering contact in privacy-settings:', contact, error);
        return false;
      }
    });

    setFilteredContacts(filtered);
  };

  // Handle search input change
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    filterContacts(text);
  };

  // Toggle search visibility
  const toggleSearch = () => {
    if (showSearch) {
      // Closing search - clear query and reset contacts
      setSearchQuery('');
      setFilteredContacts(contacts);
    }
    setShowSearch(!showSearch);
  };

  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setFilteredContacts(contacts);
    setShowSearch(false);
  };

  // Validate messaging settings
  const validateMessagingSettings = (key: keyof PrivacySettings, value: any): boolean => {
    switch (key) {
      case 'readReceipts':
      case 'disappearingMessages':
      case 'screenshotNotification':
      case 'forwardedMessages':
      case 'liveLocation':
        return typeof value === 'boolean';
      case 'autoDownloadMedia':
        return ['never', 'wifi', 'always'].includes(value);
      case 'callsFrom':
        return ['everyone', 'contacts', 'nobody'].includes(value);
      default:
        return true;
    }
  };

  // Get global sync status indicator with real-time progress for entire app
  const getSyncStatusIndicator = () => {
    const { isOnline, isSyncing, syncProgress, pendingSyncCount } = globalSyncState;

    return (
      <TouchableOpacity
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          backgroundColor: isSyncing ? '#3B82F6' : (isOnline ? '#10B981' : '#F59E0B'),
          paddingHorizontal: 12,
          paddingVertical: 8,
          borderRadius: 12,
          marginBottom: 16,
          elevation: 2,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.2,
          shadowRadius: 2,
        }}
        onPress={() => {
          if (isSyncing) {
            Alert.alert(
              'Syncing in Progress',
              `Syncing your data to the cloud...\n\nProgress: ${syncProgress}%\nPending items: ${pendingSyncCount}`,
              [{ text: 'OK' }]
            );
          } else if (isOnline) {
            Alert.alert(
              'Synced to Cloud',
              'All your data is synced to the cloud and up to date.',
              [{ text: 'OK' }]
            );
          } else {
            Alert.alert(
              'Offline Mode',
              'You are currently offline. Your data will sync automatically when you come back online.',
              [{ text: 'OK' }]
            );
          }
        }}
        activeOpacity={0.7}
      >
        {isSyncing ? (
          <ActivityIndicator size="small" color="white" />
        ) : (
          <Ionicons
            name={isOnline ? "cloud-done" : "cloud-offline"}
            size={16}
            color="white"
          />
        )}
        <Text style={{
          color: 'white',
          fontSize: 12,
          fontWeight: '600',
          marginLeft: 6
        }}>
          {isSyncing
            ? `Syncing... ${syncProgress}%${pendingSyncCount > 0 ? ` (${pendingSyncCount} pending)` : ''}`
            : (isOnline ? 'Synced to Cloud' : 'Offline - Will sync when online')
          }
        </Text>
        {isSyncing && (
          <View style={{
            marginLeft: 8,
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: 8,
            paddingHorizontal: 6,
            paddingVertical: 2,
          }}>
            <Text style={{
              color: 'white',
              fontSize: 10,
              fontWeight: '500',
            }}>
              {syncProgress}%
            </Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  // Handle contact selection for privacy settings
  const handleContactSelection = (type: 'lastSeen' | 'profilePhoto' | 'about' | 'groupsAddMe' | 'disappearingMessagesScope' | 'forwardedMessagesScope' | 'screenshotControlScope') => {
    setContactSelectionType(type);

    // Pre-select contacts if they were previously selected
    const customContactsField = `${type}CustomContacts` as keyof PrivacySettings;
    const previouslySelected = settings[customContactsField] as string[] || [];
    setSelectedContacts(previouslySelected);

    setShowContactSelection(true);
    loadContactsForSelection();
  };

  // Save selected contacts for privacy setting
  const saveContactSelection = async () => {
    if (!currentUser?.id) return;

    let settingValue: string;
    let customContactsField: string;
    let updates: any = {};

    // Determine the setting value based on selection
    if (selectedContacts.length === 0) {
      settingValue = 'nobody';
    } else if (selectedContacts.length === contacts.length) {
      settingValue = 'contacts'; // All contacts selected
    } else {
      settingValue = 'custom'; // Custom selection
    }

    // Set the main setting and custom contacts list
    updates[contactSelectionType] = settingValue;

    // Set custom contacts field if needed
    if (settingValue === 'custom') {
      customContactsField = `${contactSelectionType}CustomContacts`;
      updates[customContactsField] = selectedContacts;
    } else if (settingValue === 'nobody') {
      // Clear custom contacts when nobody is selected
      customContactsField = `${contactSelectionType}CustomContacts`;
      updates[customContactsField] = [];
    }

    console.log('🔐 Saving privacy setting:', updates);

    // Update local state immediately
    setSettings(prev => ({ ...prev, ...updates }));
    setShowContactSelection(false);

    const contactCount = selectedContacts.length;
    const settingName = contactSelectionType.replace(/([A-Z])/g, ' $1').toLowerCase();

    let message = '';
    if (settingValue === 'nobody') {
      message = `${settingName} set to Nobody`;
    } else if (settingValue === 'contacts') {
      message = `${settingName} set to All Contacts`;
    } else {
      message = `${settingName} set to ${contactCount} selected contact${contactCount !== 1 ? 's' : ''}`;
    }

    // Save to Firebase (handles offline/online automatically)
    setSaving(true);
    try {
      const result = await realPrivacyService.updatePrivacySettings(currentUser.id, updates);
      if (result.success) {
        const isOnline = networkStateManager.isOnline();
        const statusMessage = isOnline ? "Synced to cloud" : "Saved offline - Will sync when online";
        Alert.alert("Success", `${message} - ${statusMessage}`);
      } else {
        Alert.alert("Error", result.error || "Failed to update privacy settings");
        // Revert local changes on error
        loadSettings();
      }
    } catch (error) {
      console.error("Error saving privacy settings:", error);
      Alert.alert("Error", "Failed to save privacy settings");
      // Revert local changes on error
      loadSettings();
    } finally {
      setSaving(false);
    }
  };

  const handleUnblockUser = async (blockedUserId: string, contactName?: string) => {
    if (!currentUser?.id) return;

    Alert.alert(
      "Unblock Contact",
      contactName
        ? `You need to unblock ${contactName} first to include them in your privacy settings.`
        : "Are you sure you want to unblock this user?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Unblock",
          style: "destructive",
          onPress: async () => {
            if (!networkStateManager.isOnline()) {
              Alert.alert("Offline", "You need to be online to unblock contacts");
              return;
            }

            try {
              // Update blocked contacts list
              const updatedBlockedContacts = settings.blockedContacts.filter(id => id !== blockedUserId);
              const result = await realPrivacyService.updatePrivacySettings(currentUser.id, {
                blockedContacts: updatedBlockedContacts
              });

              if (result.success) {
                // Update local settings
                setSettings(prev => ({
                  ...prev,
                  blockedContacts: updatedBlockedContacts
                }));

                Alert.alert("Success", contactName ? `${contactName} has been unblocked` : "User unblocked successfully");
                // Reload contacts to reflect the change
                if (contactName) {
                  loadContactsForSelection();
                } else {
                  loadBlockedContacts();
                }
              } else {
                Alert.alert("Error", result.error || "Failed to unblock user");
              }
            } catch (error) {
              console.error("Error unblocking user:", error);
              Alert.alert("Error", "Failed to unblock contact");
            }
          }
        }
      ]
    );
  };

  const handleClearAllChats = () => {
    Alert.alert(
      "Clear All Chats",
      "This will permanently delete all your chat history. This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear All",
          style: "destructive",
          onPress: () => {
            // Implement actual chat clearing logic here
            Alert.alert("Success", "All chat history has been cleared");
          }
        }
      ]
    );
  };

  const handleClearIndividualChats = () => {
    Alert.alert(
      "Clear Individual Chats",
      "Choose specific chats to clear:",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Select Chats",
          onPress: () => {
            // Navigate to chat selection screen
            navigationService.navigate(ROUTES.SETTINGS.CHAT_MANAGEMENT);
          }
        }
      ]
    );
  };

  const handleClearMediaCache = () => {
    Alert.alert(
      "Clear Media Cache",
      "This will clear cached images, videos, and documents to free up storage space.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear Cache",
          onPress: () => {
            // Implement media cache clearing
            Alert.alert("Success", "Media cache has been cleared");
          }
        }
      ]
    );
  };

  const handleExportChatData = () => {
    Alert.alert(
      "Export Chat Data",
      "Choose export format:",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Export as JSON",
          onPress: () => {
            Alert.alert("Export Started", "Your chat data is being prepared for export. You'll receive a download link via email.");
          }
        },
        {
          text: "Export as PDF",
          onPress: () => {
            Alert.alert("Export Started", "Your chat data is being converted to PDF format. You'll receive a download link via email.");
          }
        }
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      "Delete Account",
      "This will permanently delete your account and all associated data. This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete Account",
          style: "destructive",
          onPress: () => {
            Alert.prompt(
              "Confirm Deletion",
              "Type 'DELETE' to confirm account deletion:",
              [
                { text: "Cancel", style: "cancel" },
                {
                  text: "Delete",
                  style: "destructive",
                  onPress: (text) => {
                    if (text === "DELETE") {
                      // Implement account deletion
                      Alert.alert("Account Deleted", "Your account has been permanently deleted.");
                    } else {
                      Alert.alert("Error", "Please type 'DELETE' to confirm");
                    }
                  }
                }
              ],
              "plain-text"
            );
          }
        }
      ]
    );
  };

  const renderPrivacyOption = (
    title: string,
    description: string,
    currentValue: string,
    onPress: () => void,
    icon: string
  ) => (
    <TouchableOpacity
      onPress={onPress}
      style={{
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
      }}
    >
      <View style={{
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#87CEEB',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
      }}>
        <Ionicons name={icon as any} size={20} color="#FFFFFF" />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#333',
          marginBottom: 4,
        }}>
          {title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#666',
        }}>
          {description}
        </Text>
        <Text style={{
          fontSize: 12,
          color: '#87CEEB',
          marginTop: 4,
          fontWeight: '500',
        }}>
          {currentValue}
        </Text>
      </View>
      
      <Ionicons name="chevron-forward" size={20} color="#87CEEB" />
    </TouchableOpacity>
  );

  const renderSwitchOption = (
    title: string,
    description: string,
    value: boolean,
    onValueChange: (value: boolean) => void,
    icon: string
  ) => (
    <View style={{
      backgroundColor: '#FFFFFF',
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      flexDirection: 'row',
      alignItems: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.1,
      shadowRadius: 2,
      elevation: 2,
    }}>
      <View style={{
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#87CEEB',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
      }}>
        <Ionicons name={icon as any} size={20} color="#FFFFFF" />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: '#333',
          marginBottom: 4,
        }}>
          {title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#666',
        }}>
          {description}
        </Text>
      </View>
      
      <Switch
        value={value}
        onValueChange={onValueChange}
        trackColor={{ false: '#E5E7EB', true: '#87CEEB' }}
        thumbColor={value ? '#FFFFFF' : '#F3F4F6'}
      />
    </View>
  );

  const renderActionOption = (
    title: string,
    description: string,
    onPress: () => void,
    icon: string,
    color: string = '#87CEEB',
    destructive: boolean = false
  ) => (
    <TouchableOpacity
      onPress={onPress}
      style={{
        backgroundColor: '#FFFFFF',
        borderRadius: 12,
        padding: 16,
        marginBottom: 12,
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
      }}
    >
      <View style={{
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: color,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
      }}>
        <Ionicons name={icon as any} size={20} color="#FFFFFF" />
      </View>
      
      <View style={{ flex: 1 }}>
        <Text style={{
          fontSize: 16,
          fontWeight: '600',
          color: destructive ? '#DC3545' : '#333',
          marginBottom: 4,
        }}>
          {title}
        </Text>
        <Text style={{
          fontSize: 14,
          color: '#666',
        }}>
          {description}
        </Text>
      </View>
      
      <Ionicons name="chevron-forward" size={20} color={color} />
    </TouchableOpacity>
  );

  console.log('🔐 Loading state:', loading);

  if (loading) {
    console.log('🔐 Showing loading screen');
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#F8F9FA',
      }}>
        <ActivityIndicator size="large" color="#87CEEB" />
        <Text style={{
          marginTop: 16,
          fontSize: 16,
          color: '#666',
        }}>
          Loading privacy settings...
        </Text>
      </View>
    );
  }

  console.log('🔐 Rendering main content');

  return (
    <View style={{ flex: 1, backgroundColor: '#F8F9FA' }}>
      {/* Header */}
      <View style={{
        backgroundColor: '#87CEEB',
        paddingTop: 60,
        paddingBottom: 20,
        paddingHorizontal: 20,
        flexDirection: 'row',
        alignItems: 'center',
      }}>
        <TouchableOpacity
          onPress={() => navigationService.goBack()}
          style={{ marginRight: 16 }}
        >
          <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
        </TouchableOpacity>
        
        <Text style={{
          fontSize: 20,
          fontWeight: 'bold',
          color: '#FFFFFF',
        }}>
          Privacy & Security
        </Text>
      </View>

      <ScrollView style={{ flex: 1, padding: 20 }}>
        {/* Who can see my personal info */}
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          marginBottom: 16,
        }}>
          Who can see my personal info
        </Text>

        {renderPrivacyOption(
          "Last Seen & Online",
          "Control who can see when you were last online",
          getPrivacySettingDisplay('lastSeen'),
          () => {
            Alert.alert(
              "Last Seen & Online",
              "Choose who can see when you were last online",
              [
                { text: "Cancel", style: "cancel" },
                { text: "Everyone", onPress: () => updateSetting('lastSeen', 'everyone') },
                { text: "My Contacts", onPress: () => handleContactSelection('lastSeen') },
                { text: "Nobody", onPress: () => updateSetting('lastSeen', 'nobody') },
              ]
            );
          },
          "time"
        )}

        {renderPrivacyOption(
          "Profile Photo",
          "Control who can see your profile photo",
          getPrivacySettingDisplay('profilePhoto'),
          () => {
            Alert.alert(
              "Profile Photo",
              "Choose who can see your profile photo",
              [
                { text: "Cancel", style: "cancel" },
                { text: "Everyone", onPress: () => updateSetting('profilePhoto', 'everyone') },
                { text: "My Contacts", onPress: () => handleContactSelection('profilePhoto') },
                { text: "Nobody", onPress: () => updateSetting('profilePhoto', 'nobody') },
              ]
            );
          },
          "person-circle"
        )}

        {renderPrivacyOption(
          "About",
          "Control who can see your about info",
          getPrivacySettingDisplay('about'),
          () => {
            Alert.alert(
              "About",
              "Choose who can see your about info",
              [
                { text: "Cancel", style: "cancel" },
                { text: "Everyone", onPress: () => updateSetting('about', 'everyone') },
                { text: "My Contacts", onPress: () => handleContactSelection('about') },
                { text: "Nobody", onPress: () => updateSetting('about', 'nobody') },
              ]
            );
          },
          "information-circle"
        )}

        {/* Messaging */}
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          marginTop: 24,
          marginBottom: 16,
        }}>
          Messaging
        </Text>

        {/* Sync Status Indicator */}
        {getSyncStatusIndicator()}

        {renderSwitchOption(
          "Read Receipts",
          "Show when you've read messages",
          settings.readReceipts || false,
          (value) => updateSetting('readReceipts', value),
          "checkmark-done"
        )}

        {renderSwitchOption(
          "Disappearing Messages",
          "Enable disappearing messages feature",
          settings.disappearingMessages || false,
          (value) => updateSetting('disappearingMessages', value),
          "timer"
        )}

        {settings.disappearingMessages && (
          <>
            {renderPrivacyOption(
              "Disappearing Duration",
              "How long before messages disappear",
              getDurationDisplay(settings.disappearingMessagesDuration || "1day"),
              () => {
                Alert.alert(
                  "Disappearing Duration",
                  "Choose how long messages should remain visible",
                  [
                    { text: "Cancel", style: "cancel" },
                    { text: "1 Hour", onPress: () => updateSetting('disappearingMessagesDuration', '1hour') },
                    { text: "1 Day", onPress: () => updateSetting('disappearingMessagesDuration', '1day') },
                    { text: "1 Week", onPress: () => updateSetting('disappearingMessagesDuration', '1week') },
                    { text: "1 Month", onPress: () => updateSetting('disappearingMessagesDuration', '1month') },
                    { text: "Never", onPress: () => updateSetting('disappearingMessagesDuration', 'never') },
                  ]
                );
              },
              "time"
            )}

            {renderPrivacyOption(
              "Whose Messages Disappear",
              "Choose whose messages should disappear",
              getPrivacySettingDisplay('disappearingMessagesScope'),
              () => {
                Alert.alert(
                  "Whose Messages Disappear",
                  "Choose whose messages should have disappearing enabled",
                  [
                    { text: "Cancel", style: "cancel" },
                    { text: "Everyone", onPress: () => updateSetting('disappearingMessagesScope', 'everyone') },
                    { text: "My Contacts", onPress: () => handleContactSelection('disappearingMessagesScope') },
                    { text: "Nobody", onPress: () => updateSetting('disappearingMessagesScope', 'nobody') },
                  ]
                );
              },
              "people"
            )}

            {renderPrivacyOption(
              "Message Storage",
              "What happens to disappeared messages",
              getStorageDisplay(settings.disappearingMessagesStorage || "delete_chat_only"),
              () => {
                Alert.alert(
                  "Message Storage",
                  "Choose what happens to messages after they disappear",
                  [
                    { text: "Cancel", style: "cancel" },
                    { text: "Delete Everywhere", onPress: () => updateSetting('disappearingMessagesStorage', 'delete_everywhere') },
                    { text: "Delete from Chat Only", onPress: () => updateSetting('disappearingMessagesStorage', 'delete_chat_only') },
                    { text: "Archive Locally", onPress: () => updateSetting('disappearingMessagesStorage', 'archive_locally') },
                  ]
                );
              },
              "archive"
            )}
          </>
        )}

        {renderSwitchOption(
          "Screenshot Notifications",
          "Notify when someone screenshots your messages",
          settings.screenshotNotification || false,
          (value) => updateSetting('screenshotNotification', value),
          "camera"
        )}

        {renderSwitchOption(
          "Screenshot Control",
          "Control who can take screenshots of your chats",
          settings.screenshotControl || false,
          (value) => updateSetting('screenshotControl', value),
          "shield-checkmark"
        )}

        {settings.screenshotControl && (
          <View style={{ marginLeft: 20, marginTop: 8 }}>
            {renderPrivacyOption(
              "Who Can Screenshot",
              "Choose who can take screenshots of your chats",
              getPrivacySettingDisplay('screenshotControlScope'),
              () => {
                Alert.alert(
                  "Who Can Screenshot Your Chats",
                  "Choose who is allowed to take screenshots of your conversations",
                  [
                    { text: "Cancel", style: "cancel" },
                    { text: "Everyone", onPress: () => updateSetting('screenshotControlScope', 'everyone') },
                    { text: "My Contacts", onPress: () => handleContactSelection('screenshotControlScope') },
                    { text: "Nobody", onPress: () => updateSetting('screenshotControlScope', 'nobody') },
                  ]
                );
              },
              "camera-outline"
            )}
          </View>
        )}

        {renderSwitchOption(
          "Forwarded Messages",
          "Allow your messages to be forwarded",
          settings.forwardedMessages || false,
          (value) => updateSetting('forwardedMessages', value),
          "arrow-forward"
        )}

        {settings.forwardedMessages && (
          <View style={{ marginLeft: 20, marginTop: 8 }}>
            {renderPrivacyOption(
              "Who Can Forward",
              "Choose who can forward your messages",
              getPrivacySettingDisplay('forwardedMessagesScope'),
              () => {
                Alert.alert(
                  "Who Can Forward Your Messages",
                  "Choose who is allowed to forward your messages to others",
                  [
                    { text: "Cancel", style: "cancel" },
                    { text: "Everyone", onPress: () => updateSetting('forwardedMessagesScope', 'everyone') },
                    { text: "My Contacts", onPress: () => handleContactSelection('forwardedMessagesScope') },
                    { text: "Nobody", onPress: () => updateSetting('forwardedMessagesScope', 'nobody') },
                  ]
                );
              },
              "people"
            )}
          </View>
        )}

        {renderActionOption(
          "Auto Download Media",
          `Currently: ${getAutoDownloadDisplay(settings.autoDownloadMedia || "wifi")}`,
          () => {
            Alert.alert(
              'Auto Download Media',
              'When should media be downloaded automatically?',
              [
                {
                  text: 'Never',
                  onPress: () => updateSetting('autoDownloadMedia', 'never'),
                  style: settings.autoDownloadMedia === 'never' ? 'default' : 'default'
                },
                {
                  text: 'Wi-Fi Only',
                  onPress: () => updateSetting('autoDownloadMedia', 'wifi'),
                  style: settings.autoDownloadMedia === 'wifi' ? 'default' : 'default'
                },
                {
                  text: 'Always',
                  onPress: () => updateSetting('autoDownloadMedia', 'always'),
                  style: settings.autoDownloadMedia === 'always' ? 'default' : 'default'
                },
                { text: 'Cancel', style: 'cancel' },
              ]
            );
          },
          "download"
        )}

        {/* Calls */}
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          marginTop: 24,
          marginBottom: 16,
        }}>
          Calls
        </Text>

        {renderPrivacyOption(
          "Calls From",
          "Control who can call you",
          settings.callsFrom || "contacts",
          () => {
            Alert.alert(
              "Calls From",
              "Choose who can call you",
              [
                { text: "Cancel", style: "cancel" },
                { text: "Everyone", onPress: () => updateSetting('callsFrom', 'everyone') },
                { text: "My Contacts", onPress: () => updateSetting('callsFrom', 'contacts') },
                { text: "Nobody", onPress: () => updateSetting('callsFrom', 'nobody') },
              ]
            );
          },
          "call"
        )}

        {renderSwitchOption(
          "Live Location",
          "Share your live location in calls",
          settings.liveLocation || false,
          (value) => updateSetting('liveLocation', value),
          "location"
        )}

        {/* Groups */}
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          marginTop: 24,
          marginBottom: 16,
        }}>
          Groups
        </Text>

        {renderPrivacyOption(
          "Groups",
          "Control who can add you to groups",
          getPrivacySettingDisplay('groupsAddMe'),
          () => {
            Alert.alert(
              "Groups",
              "Choose who can add you to groups",
              [
                { text: "Cancel", style: "cancel" },
                { text: "Everyone", onPress: () => updateSetting('groupsAddMe', 'everyone') },
                { text: "My Contacts", onPress: () => handleContactSelection('groupsAddMe') },
                { text: "Nobody", onPress: () => updateSetting('groupsAddMe', 'nobody') },
              ]
            );
          },
          "people"
        )}

        {/* Security */}
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          marginTop: 24,
          marginBottom: 16,
        }}>
          Security
        </Text>

        {renderSwitchOption(
          "Two-Step Verification",
          "Add extra security to your account",
          settings.twoStepVerification || false,
          (value) => {
            if (value) {
              setShowTwoStepModal(true);
            } else {
              Alert.alert(
                "Disable Two-Step Verification",
                "Are you sure you want to disable two-step verification?",
                [
                  { text: "Cancel", style: "cancel" },
                  { text: "Disable", style: "destructive", onPress: () => updateSetting('twoStepVerification', false) }
                ]
              );
            }
          },
          "shield-checkmark"
        )}

        {renderSwitchOption(
          "Security Notifications",
          "Get notified about security events",
          settings.securityNotifications || false,
          (value) => updateSetting('securityNotifications', value),
          "notifications"
        )}

        {/* App Lock - Debug */}
        <TouchableOpacity
          onPress={() => {
            console.log('🔐 App Lock tapped!');
            setShowAppLockSettings(true);
          }}
          style={{
            backgroundColor: '#FFFFFF', // White background like other containers
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
            flexDirection: 'row',
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: {
              width: 0,
              height: 2,
            },
            shadowOpacity: 0.1,
            shadowRadius: 3.84,
            elevation: 5,
          }}
        >
          <View style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: '#F59318',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 12,
          }}>
            <Ionicons name="lock-closed" size={20} color="#FFFFFF" />
          </View>

          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#333',
              marginBottom: 4,
            }}>
              App Lock
            </Text>
            <Text style={{
              fontSize: 14,
              color: '#666',
            }}>
              Protect your app with PIN, password, or biometric
            </Text>
          </View>

          <Ionicons name="chevron-forward" size={20} color="#87CEEB" />
        </TouchableOpacity>

        {/* Data Management */}
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          marginTop: 24,
          marginBottom: 16,
        }}>
          Data Management
        </Text>

        {renderActionOption(
          "Clear All Chats",
          "Delete all chat history permanently",
          handleClearAllChats,
          "trash",
          "#DC3545",
          true
        )}

        {renderActionOption(
          "Clear Individual Chats",
          "Select specific chats to clear",
          handleClearIndividualChats,
          "trash-bin",
          "#F59E0B"
        )}

        {renderActionOption(
          "Clear Media Cache",
          "Free up storage space by clearing cached media",
          handleClearMediaCache,
          "folder",
          "#10B981"
        )}

        {renderActionOption(
          "Export Chat Data",
          "Download your chat history",
          handleExportChatData,
          "download",
          "#6366F1"
        )}

        {/* Blocked Contacts */}
        <TouchableOpacity
          onPress={() => {
            if (blockedContacts.length === 0) {
              Alert.alert("Blocked Contacts", "You haven't blocked any contacts yet.");
            } else {
              Alert.alert(
                "Blocked Contacts",
                `You have ${blockedContacts.length} blocked contact${blockedContacts.length !== 1 ? 's' : ''}.\n\nTo manage blocked contacts, go to individual chat settings.`
              );
            }
          }}
          style={{
            backgroundColor: '#FFFFFF',
            borderRadius: 12,
            padding: 16,
            marginBottom: 12,
            flexDirection: 'row',
            alignItems: 'center',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 1 },
            shadowOpacity: 0.1,
            shadowRadius: 2,
            elevation: 2,
          }}
        >
          <View style={{
            width: 40,
            height: 40,
            borderRadius: 20,
            backgroundColor: '#DC3545',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 16,
          }}>
            <Ionicons name="ban" size={20} color="#FFFFFF" />
          </View>
          
          <View style={{ flex: 1 }}>
            <Text style={{
              fontSize: 16,
              fontWeight: '600',
              color: '#333',
              marginBottom: 4,
            }}>
              Blocked Contacts
            </Text>
            <Text style={{
              fontSize: 14,
              color: '#666',
            }}>
              {blockedContacts.length} blocked contacts
            </Text>
          </View>
          
          <Ionicons name="chevron-forward" size={20} color="#87CEEB" />
        </TouchableOpacity>

        {/* Account Deletion */}
        <Text style={{
          fontSize: 18,
          fontWeight: 'bold',
          color: '#333',
          marginTop: 24,
          marginBottom: 16,
        }}>
          Account
        </Text>

        {renderActionOption(
          "Delete Account",
          "Permanently delete your account and all data",
          handleDeleteAccount,
          "person-remove",
          "#DC3545",
          true
        )}
      </ScrollView>

      {/* Two-Step Verification Modal */}
      <Modal
        visible={showTwoStepModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowTwoStepModal(false)}
      >
        <View style={{
          flex: 1,
          backgroundColor: "rgba(0,0,0,0.5)",
          justifyContent: "center",
          alignItems: "center",
          padding: 20,
        }}>
          <View style={{
            backgroundColor: "white",
            borderRadius: 20,
            padding: 20,
            width: "100%",
          }}>
            <Text style={{
              fontSize: 20,
              fontWeight: "bold",
              textAlign: "center",
              marginBottom: 20,
              color: "#333",
            }}>
              Enable Two-Step Verification
            </Text>

            <Text style={{ fontSize: 16, marginBottom: 8, color: "#333" }}>
              Create a 6-digit PIN
            </Text>
            <TextInput
              placeholder="Enter 6-digit PIN"
              value={twoStepPin}
              onChangeText={setTwoStepPin}
              style={{
                borderWidth: 1,
                borderColor: "#ddd",
                borderRadius: 8,
                padding: 12,
                fontSize: 16,
                marginBottom: 20,
                textAlign: "center",
              }}
              keyboardType="numeric"
              maxLength={6}
              secureTextEntry
            />

            <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
              <TouchableOpacity
                onPress={() => setShowTwoStepModal(false)}
                style={{
                  flex: 1,
                  backgroundColor: "#f0f0f0",
                  padding: 16,
                  borderRadius: 8,
                  marginRight: 8,
                }}
              >
                <Text style={{
                  textAlign: "center",
                  fontSize: 16,
                  fontWeight: "600",
                  color: "#666",
                }}>
                  Cancel
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  if (twoStepPin.length === 6) {
                    updateSetting('twoStepVerification', true);
                    setShowTwoStepModal(false);
                    setTwoStepPin('');
                    Alert.alert('Success', 'Two-step verification enabled successfully!');
                  } else {
                    Alert.alert('Error', 'Please enter a 6-digit PIN');
                  }
                }}
                style={{
                  flex: 1,
                  backgroundColor: "#87CEEB",
                  padding: 16,
                  borderRadius: 8,
                  marginLeft: 8,
                }}
              >
                <Text style={{
                  textAlign: "center",
                  fontSize: 16,
                  fontWeight: "600",
                  color: "white",
                }}>
                  Enable
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* App Lock Settings Modal */}
      {showAppLockSettings && (
        <Modal
          visible={showAppLockSettings}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowAppLockSettings(false)}
        >
          <PrivacySettingsComponent
            onClose={() => setShowAppLockSettings(false)}
            onLockEnabled={() => {
              setShowAppLockSettings(false);
              Alert.alert(
                "App Lock Enabled",
                "Your app is now protected with privacy lock. You can lock it anytime from the settings.",
                [{ text: "OK" }]
              );
            }}
          />
        </Modal>
      )}

      {/* Contact Selection Modal */}
      {showContactSelection && (
        <Modal
          visible={showContactSelection}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setShowContactSelection(false)}
        >
          <View style={{ flex: 1, backgroundColor: '#F8F9FA' }}>
            {/* Header */}
            <View style={{
              backgroundColor: '#87CEEB',
              paddingTop: 50,
              paddingBottom: 20,
              paddingHorizontal: 20,
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 5,
            }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                <TouchableOpacity onPress={() => setShowContactSelection(false)}>
                  <Ionicons name="close" size={24} color="#1E3A8A" />
                </TouchableOpacity>

                {/* Title or Search Input */}
                {showSearch ? (
                  <View style={{
                    flex: 1,
                    marginHorizontal: 16,
                    backgroundColor: 'white',
                    borderRadius: 20,
                    paddingHorizontal: 16,
                    paddingVertical: 8,
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                    <Ionicons name="search" size={20} color="#666" />
                    <TextInput
                      style={{
                        flex: 1,
                        marginLeft: 8,
                        fontSize: 16,
                        color: '#333',
                      }}
                      placeholder="Search contacts..."
                      placeholderTextColor="#999"
                      value={searchQuery}
                      onChangeText={handleSearchChange}
                      autoFocus={true}
                    />
                    {searchQuery.length > 0 && (
                      <TouchableOpacity onPress={clearSearch}>
                        <Ionicons name="close-circle" size={20} color="#666" />
                      </TouchableOpacity>
                    )}
                  </View>
                ) : (
                  <Text style={{ color: '#1E3A8A', fontSize: 18, fontWeight: 'bold' }}>
                    Select Contacts
                  </Text>
                )}

                {/* Search Icon or Done Button */}
                {showSearch ? (
                  <TouchableOpacity onPress={toggleSearch}>
                    <Text style={{ color: '#1E3A8A', fontSize: 16, fontWeight: '600' }}>Cancel</Text>
                  </TouchableOpacity>
                ) : (
                  <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                    <TouchableOpacity onPress={toggleSearch} style={{ marginRight: 16 }}>
                      <Ionicons name="search" size={24} color="#1E3A8A" />
                    </TouchableOpacity>
                    <TouchableOpacity onPress={saveContactSelection}>
                      <Text style={{ color: '#1E3A8A', fontSize: 16, fontWeight: '600' }}>Done</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>

              {/* Subtitle - only show when not searching */}
              {!showSearch && (
                <Text style={{ color: '#1E3A8A', fontSize: 14, marginTop: 8, opacity: 0.8 }}>
                  Choose contacts for {contactSelectionType.replace(/([A-Z])/g, ' $1').toLowerCase()} privacy
                </Text>
              )}
            </View>

            {/* Select All Button */}
            <TouchableOpacity
              onPress={() => {
                const visibleContactIds = filteredContacts.map(c => c.id);
                const allVisibleSelected = visibleContactIds.every(id => selectedContacts.includes(id));

                if (allVisibleSelected) {
                  // Deselect all visible contacts
                  setSelectedContacts(prev => prev.filter(id => !visibleContactIds.includes(id)));
                } else {
                  // Select all visible contacts (add to existing selection)
                  setSelectedContacts(prev => [...new Set([...prev, ...visibleContactIds])]);
                }
              }}
              style={{
                backgroundColor: '#F0F8FF',
                paddingHorizontal: 20,
                paddingVertical: 16,
                flexDirection: 'row',
                alignItems: 'center',
                borderBottomWidth: 2,
                borderBottomColor: '#87CEEB',
              }}
            >
              <Ionicons
                name={filteredContacts.every(c => selectedContacts.includes(c.id)) ? "checkbox" : "square-outline"}
                size={24}
                color="#87CEEB"
              />
              <Text style={{ marginLeft: 12, fontSize: 16, fontWeight: '600', color: '#333' }}>
                {showSearch && searchQuery ?
                  `Select All Filtered (${filteredContacts.length})` :
                  `Select All Contacts (${filteredContacts.length})`
                }
              </Text>
            </TouchableOpacity>

            {/* Contacts List */}
            {loadingContacts ? (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" color="#87CEEB" />
                <Text style={{ marginTop: 16, color: '#666' }}>Loading contacts...</Text>
              </View>
            ) : filteredContacts.length === 0 && searchQuery ? (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', paddingVertical: 60 }}>
                <Ionicons name="search-outline" size={64} color="#d1d5db" />
                <Text style={{ fontSize: 18, fontWeight: '600', color: '#666', marginTop: 16 }}>
                  No contacts found
                </Text>
                <Text style={{ fontSize: 14, color: '#999', marginTop: 8, textAlign: 'center', paddingHorizontal: 40 }}>
                  Try searching with a different name or clear the search to see all contacts
                </Text>
                <TouchableOpacity
                  onPress={clearSearch}
                  style={{
                    backgroundColor: '#87CEEB',
                    paddingHorizontal: 20,
                    paddingVertical: 10,
                    borderRadius: 20,
                    marginTop: 20,
                  }}
                >
                  <Text style={{ color: 'white', fontWeight: '600' }}>Clear Search</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                data={filteredContacts}
                keyExtractor={(item) => item.id}
                contentContainerStyle={{ paddingBottom: 40 }}
                renderItem={({ item }) => {
                  const isBlocked = settings.blockedContacts.includes(item.id);
                  const isSelected = selectedContacts.includes(item.id);

                  return (
                    <TouchableOpacity
                      onPress={() => {
                        if (isBlocked) {
                          // Show unblock dialog for blocked contacts
                          handleUnblockUser(item.id, item.name);
                        } else {
                          // Normal selection for non-blocked contacts
                          if (isSelected) {
                            setSelectedContacts(prev => prev.filter(id => id !== item.id));
                          } else {
                            setSelectedContacts(prev => [...prev, item.id]);
                          }
                        }
                      }}
                      style={{
                        backgroundColor: isBlocked ? '#FFF5F5' : '#FFFFFF',
                        paddingHorizontal: 20,
                        paddingVertical: 12,
                        flexDirection: 'row',
                        alignItems: 'center',
                        borderBottomWidth: 1,
                        borderBottomColor: '#87CEEB',
                        borderLeftWidth: isBlocked ? 3 : 0,
                        borderLeftColor: isBlocked ? '#EF4444' : 'transparent',
                      }}
                    >
                      {/* Checkbox or Blocked Icon */}
                      {isBlocked ? (
                        <Ionicons name="ban" size={24} color="#EF4444" />
                      ) : (
                        <Ionicons
                          name={isSelected ? "checkbox" : "square-outline"}
                          size={24}
                          color="#87CEEB"
                        />
                      )}

                      {/* Avatar */}
                      <View style={{
                        width: 40,
                        height: 40,
                        borderRadius: 20,
                        backgroundColor: isBlocked ? '#FCA5A5' : '#87CEEB',
                        justifyContent: 'center',
                        alignItems: 'center',
                        marginLeft: 12,
                        marginRight: 12,
                      }}>
                        {item.avatar ? (
                          <Image source={{ uri: item.avatar }} style={{ width: 40, height: 40, borderRadius: 20 }} />
                        ) : (
                          <Text style={{ color: 'white', fontWeight: 'bold' }}>
                            {item.name.charAt(0).toUpperCase()}
                          </Text>
                        )}
                      </View>

                      {/* Contact Info */}
                      <View style={{ flex: 1 }}>
                        <Text style={{ fontSize: 16, fontWeight: '600', color: isBlocked ? '#EF4444' : '#333' }}>
                          {item.name} {isBlocked && '(Blocked)'}
                        </Text>
                        <Text style={{ fontSize: 14, color: isBlocked ? '#FCA5A5' : '#666', marginTop: 2 }}>
                          {isBlocked ? 'Tap to unblock' : (item.isOnline ? 'Online' : 'Last seen recently')}
                        </Text>
                      </View>
                    </TouchableOpacity>
                  );
                }}
              />
            )}
          </View>
        </Modal>
      )}


    </View>
  );
}
