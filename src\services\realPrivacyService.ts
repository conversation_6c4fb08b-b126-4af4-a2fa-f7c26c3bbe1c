import {
  doc,
  getDoc,
  updateDoc,
  setDoc,
  serverTimestamp,
  collection,
  query,
  where,
  getDocs,
  addDoc,
  deleteDoc,
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface PrivacySettings {
  userId: string;
  lastSeen: "everyone" | "contacts" | "nobody" | "custom";
  lastSeenCustomContacts?: string[]; // Contact IDs for custom selection
  profilePhoto: "everyone" | "contacts" | "nobody" | "custom";
  profilePhotoCustomContacts?: string[]; // Contact IDs for custom selection
  status: "everyone" | "contacts" | "nobody" | "custom";
  statusCustomContacts?: string[]; // Contact IDs for custom selection
  about: "everyone" | "contacts" | "nobody" | "custom";
  aboutCustomContacts?: string[]; // Contact IDs for custom selection
  readReceipts: boolean;
  groupsAddMe: "everyone" | "contacts" | "nobody" | "custom";
  groupsAddMeCustomContacts?: string[]; // Contact IDs for custom selection
  liveLocation: boolean;
  callsFrom: "everyone" | "contacts" | "nobody";
  blockedContacts: string[];
  twoStepVerification: boolean;
  disappearingMessages: boolean;
  disappearingMessagesDuration: "1hour" | "1day" | "1week" | "1month" | "never";
  disappearingMessagesScope: "everyone" | "contacts" | "nobody" | "custom";
  disappearingMessagesCustomContacts?: string[];
  disappearingMessagesStorage: "delete_everywhere" | "delete_chat_only" | "archive_locally";
  screenshotNotification: boolean;
  screenshotControl: boolean;
  screenshotControlScope: "everyone" | "contacts" | "nobody" | "custom";
  screenshotControlCustomContacts?: string[];
  onlineStatus: "everyone" | "contacts" | "nobody";
  forwardedMessages: boolean;
  forwardedMessagesScope: "everyone" | "contacts" | "nobody" | "custom";
  forwardedMessagesCustomContacts?: string[];
  autoDownloadMedia: "never" | "wifi" | "always";
  securityNotifications: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface BlockedContact {
  id: string;
  userId: string;
  blockedUserId: string;
  blockedUserName: string;
  blockedUserAvatar?: string;
  reason?: string;
  blockedAt: Date;
}

export interface SecurityEvent {
  id: string;
  userId: string;
  eventType: 'login' | 'logout' | 'password_change' | 'privacy_change' | 'block' | 'unblock';
  description: string;
  ipAddress?: string;
  deviceInfo?: string;
  location?: string;
  timestamp: Date;
}

const defaultPrivacySettings: Omit<PrivacySettings, 'userId' | 'createdAt' | 'updatedAt'> = {
  lastSeen: "contacts",
  lastSeenCustomContacts: [],
  profilePhoto: "everyone",
  profilePhotoCustomContacts: [],
  status: "everyone",
  statusCustomContacts: [],
  about: "everyone",
  aboutCustomContacts: [],
  readReceipts: true,
  groupsAddMe: "contacts",
  groupsAddMeCustomContacts: [],
  liveLocation: false,
  callsFrom: "contacts",
  blockedContacts: [],
  twoStepVerification: false,
  disappearingMessages: false,
  disappearingMessagesDuration: "1day",
  disappearingMessagesScope: "nobody",
  disappearingMessagesCustomContacts: [],
  disappearingMessagesStorage: "delete_chat_only",
  screenshotNotification: true,
  screenshotControl: true,
  screenshotControlScope: "contacts",
  screenshotControlCustomContacts: [],
  onlineStatus: "everyone",
  forwardedMessages: true,
  forwardedMessagesScope: "contacts",
  forwardedMessagesCustomContacts: [],
  autoDownloadMedia: "wifi",
  securityNotifications: true,
};

class RealPrivacyService {
  private isInitialized = false;
  private privacyCache: Map<string, PrivacySettings> = new Map();
  private syncQueue: Set<string> = new Set();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();
      await this.loadPrivacySettingsIntoCache();

      // Set up network state listener for sync
      networkStateManager.addListener('realPrivacyService', this.handleNetworkStateChange.bind(this), 5);

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async loadPrivacySettingsIntoCache(): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync('SELECT * FROM privacy_settings');

      result.forEach((row: any) => {
        const settings = this.rowToPrivacySettings(row);
        this.privacyCache.set(settings.userId, settings);
      });
    } catch (error) {
      // Continue without cache if loading fails
    }
  }

  private rowToPrivacySettings(row: any): PrivacySettings {
    return {
      userId: row.userId,
      lastSeen: row.lastSeen,
      profilePhoto: row.profilePhoto,
      status: row.status,
      about: row.about,
      readReceipts: Boolean(row.readReceipts),
      groupsAddMe: row.groupsAddMe,
      liveLocation: Boolean(row.liveLocation),
      callsFrom: row.callsFrom,
      blockedContacts: row.blockedContacts ? JSON.parse(row.blockedContacts) : [],
      twoStepVerification: Boolean(row.twoStepVerification),
      disappearingMessages: Boolean(row.disappearingMessages),
      screenshotNotification: Boolean(row.screenshotNotification),
      screenshotControl: Boolean(row.screenshotControl),
      screenshotControlScope: row.screenshotControlScope || "contacts",
      screenshotControlCustomContacts: row.screenshotControlCustomContacts ? JSON.parse(row.screenshotControlCustomContacts) : [],
      onlineStatus: row.onlineStatus,
      disappearingMessagesDuration: row.disappearingMessagesDuration || "1day",
      disappearingMessagesScope: row.disappearingMessagesScope || "nobody",
      disappearingMessagesCustomContacts: row.disappearingMessagesCustomContacts ? JSON.parse(row.disappearingMessagesCustomContacts) : [],
      disappearingMessagesStorage: row.disappearingMessagesStorage || "delete_chat_only",
      forwardedMessages: Boolean(row.forwardedMessages),
      forwardedMessagesScope: row.forwardedMessagesScope || "contacts",
      forwardedMessagesCustomContacts: row.forwardedMessagesCustomContacts ? JSON.parse(row.forwardedMessagesCustomContacts) : [],
      autoDownloadMedia: row.autoDownloadMedia || "wifi",
      securityNotifications: Boolean(row.securityNotifications),
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
    };
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected && this.syncQueue.size > 0) {
      this.processSyncQueue();
    }
  }

  private async processSyncQueue(): Promise<void> {
    if (!networkStateManager.isOnline() || this.syncQueue.size === 0) return;

    const userIds = Array.from(this.syncQueue);
    this.syncQueue.clear();

    for (const userId of userIds) {
      try {
        await this.syncPrivacySettingsWithFirebase(userId);
      } catch (error) {
        // Re-add to queue for retry
        this.syncQueue.add(userId);
      }
    }
  }

  private async syncPrivacySettingsWithFirebase(userId: string): Promise<void> {
    const localSettings = this.privacyCache.get(userId);
    if (!localSettings) return;

    try {
      const settingsRef = doc(db, 'privacy_settings', userId);
      await setDoc(settingsRef, {
        ...localSettings,
        updatedAt: serverTimestamp(),
      });

      // Update sync status in local database
      await this.updatePrivacySyncStatus(userId, 'synced');
    } catch (error) {
      await this.updatePrivacySyncStatus(userId, 'failed');
      throw error;
    }
  }

  private async updatePrivacySyncStatus(userId: string, status: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE privacy_settings SET syncStatus = ?, lastSyncAttempt = ? WHERE userId = ?
    `, [status, Date.now(), userId]);
  }

  private async savePrivacySettingsOffline(settings: PrivacySettings): Promise<void> {
    const db = offlineDatabaseService.getDatabase();

    await db.runAsync(`
      INSERT OR REPLACE INTO privacy_settings (
        userId, lastSeen, lastSeenCustomContacts, profilePhoto, profilePhotoCustomContacts,
        status, statusCustomContacts, about, aboutCustomContacts, readReceipts, groupsAddMe,
        groupsAddMeCustomContacts, liveLocation, callsFrom, blockedContacts, twoStepVerification,
        disappearingMessages, disappearingMessagesDuration, disappearingMessagesScope,
        disappearingMessagesCustomContacts, disappearingMessagesStorage, screenshotNotification,
        screenshotControl, screenshotControlScope, screenshotControlCustomContacts,
        onlineStatus, forwardedMessages, forwardedMessagesScope, forwardedMessagesCustomContacts,
        autoDownloadMedia, securityNotifications, createdAt, updatedAt, syncStatus
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      settings.userId,
      settings.lastSeen,
      JSON.stringify(settings.lastSeenCustomContacts || []),
      settings.profilePhoto,
      JSON.stringify(settings.profilePhotoCustomContacts || []),
      settings.status,
      JSON.stringify(settings.statusCustomContacts || []),
      settings.about,
      JSON.stringify(settings.aboutCustomContacts || []),
      settings.readReceipts ? 1 : 0,
      settings.groupsAddMe,
      JSON.stringify(settings.groupsAddMeCustomContacts || []),
      settings.liveLocation ? 1 : 0,
      settings.callsFrom,
      JSON.stringify(settings.blockedContacts),
      settings.twoStepVerification ? 1 : 0,
      settings.disappearingMessages ? 1 : 0,
      settings.disappearingMessagesDuration || "1day",
      settings.disappearingMessagesScope || "nobody",
      JSON.stringify(settings.disappearingMessagesCustomContacts || []),
      settings.disappearingMessagesStorage || "delete_chat_only",
      settings.screenshotNotification ? 1 : 0,
      settings.screenshotControl ? 1 : 0,
      settings.screenshotControlScope || "contacts",
      JSON.stringify(settings.screenshotControlCustomContacts || []),
      settings.onlineStatus,
      settings.forwardedMessages ? 1 : 0,
      settings.forwardedMessagesScope || "contacts",
      JSON.stringify(settings.forwardedMessagesCustomContacts || []),
      settings.autoDownloadMedia,
      settings.securityNotifications ? 1 : 0,
      settings.createdAt.getTime(),
      settings.updatedAt.getTime(),
      'pending'
    ]);
  }

  /**
   * Get user's privacy settings (with offline support)
   */
  async getPrivacySettings(userId: string): Promise<{ success: boolean; settings?: PrivacySettings; error?: string }> {
    try {
      // Try cache first
      const cachedSettings = this.privacyCache.get(userId);
      if (cachedSettings) {
        return { success: true, settings: cachedSettings };
      }

      // Try offline database
      const offlineDb = offlineDatabaseService.getDatabase();
      const result = await offlineDb.getFirstAsync(`
        SELECT * FROM privacy_settings WHERE userId = ?
      `, [userId]);

      if (result) {
        const settings = this.rowToPrivacySettings(result as any);
        this.privacyCache.set(userId, settings);
        return { success: true, settings };
      }

      // Try online if available
      if (networkStateManager.isOnline()) {
        try {
          const settingsRef = doc(db, 'privacy_settings', userId);
          const settingsDoc = await getDoc(settingsRef);

          if (settingsDoc.exists()) {
            const settingsData = settingsDoc.data();
            const settings: PrivacySettings = {
              ...settingsData,
              createdAt: settingsData.createdAt?.toDate() || new Date(),
              updatedAt: settingsData.updatedAt?.toDate() || new Date(),
            } as PrivacySettings;

            // Cache offline
            await this.savePrivacySettingsOffline(settings);
            this.privacyCache.set(userId, settings);
            return { success: true, settings };
          }
        } catch (onlineError) {
          // Continue to create default settings
        }
      }

      // Create default settings if none exist
      const newSettings: PrivacySettings = {
        userId,
        ...defaultPrivacySettings,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save offline first
      await this.savePrivacySettingsOffline(newSettings);
      this.privacyCache.set(userId, newSettings);

      // Try to save online if connected
      if (networkStateManager.isOnline()) {
        try {
          const settingsRef = doc(db, 'privacy_settings', userId);
          await setDoc(settingsRef, {
            ...newSettings,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });
        } catch (onlineError) {
          // Queue for sync when online
          this.syncQueue.add(userId);
        }
      } else {
        // Queue for sync when online
        this.syncQueue.add(userId);
      }

      return { success: true, settings: newSettings };
    } catch (error) {
      return { success: false, error: 'Failed to get privacy settings' };
    }
  }

  /**
   * Update privacy settings
   */
  async updatePrivacySettings(
    userId: string,
    updates: Partial<Omit<PrivacySettings, 'userId' | 'createdAt' | 'updatedAt'>>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const settingsRef = doc(db, 'privacy_settings', userId);
      
      await updateDoc(settingsRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });

      // Log security event
      await this.logSecurityEvent(userId, 'privacy_change', 'Privacy settings updated');

      // Update cache
      const cachedSettings = this.privacyCache.get(userId);
      if (cachedSettings) {
        const updatedSettings = { ...cachedSettings, ...updates, updatedAt: new Date() };
        this.privacyCache.set(userId, updatedSettings);
        await this.savePrivacySettingsOffline(updatedSettings);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to update privacy settings' };
    }
  }

  /**
   * Block a user
   */
  async blockUser(
    userId: string,
    blockedUserId: string,
    blockedUserName: string,
    blockedUserAvatar?: string,
    reason?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // Add to blocked contacts collection
      const blockedContactRef = collection(db, 'blocked_contacts');
      await addDoc(blockedContactRef, {
        userId,
        blockedUserId,
        blockedUserName,
        blockedUserAvatar,
        reason,
        blockedAt: serverTimestamp(),
      });

      // Update privacy settings to include blocked user
      const settingsRef = doc(db, 'privacy_settings', userId);
      const settingsDoc = await getDoc(settingsRef);
      
      if (settingsDoc.exists()) {
        const currentSettings = settingsDoc.data();
        const updatedBlockedContacts = [...(currentSettings.blockedContacts || []), blockedUserId];
        
        await updateDoc(settingsRef, {
          blockedContacts: updatedBlockedContacts,
          updatedAt: serverTimestamp(),
        });
      }

      // Log security event
      await this.logSecurityEvent(userId, 'block', `Blocked user: ${blockedUserName}`);

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to block user' };
    }
  }

  /**
   * Unblock a user
   */
  async unblockUser(userId: string, blockedUserId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Remove from blocked contacts collection
      const blockedContactsRef = collection(db, 'blocked_contacts');
      const q = query(
        blockedContactsRef,
        where('userId', '==', userId),
        where('blockedUserId', '==', blockedUserId)
      );
      
      const querySnapshot = await getDocs(q);
      querySnapshot.docs.forEach(async (docSnapshot) => {
        await deleteDoc(docSnapshot.ref);
      });

      // Update privacy settings to remove blocked user
      const settingsRef = doc(db, 'privacy_settings', userId);
      const settingsDoc = await getDoc(settingsRef);
      
      if (settingsDoc.exists()) {
        const currentSettings = settingsDoc.data();
        const updatedBlockedContacts = (currentSettings.blockedContacts || []).filter(
          (id: string) => id !== blockedUserId
        );
        
        await updateDoc(settingsRef, {
          blockedContacts: updatedBlockedContacts,
          updatedAt: serverTimestamp(),
        });
      }

      // Log security event
      await this.logSecurityEvent(userId, 'unblock', `Unblocked user: ${blockedUserId}`);

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to unblock user' };
    }
  }

  /**
   * Get blocked contacts
   */
  async getBlockedContacts(userId: string): Promise<{ success: boolean; contacts?: BlockedContact[]; error?: string }> {
    try {
      const blockedContactsRef = collection(db, 'blocked_contacts');
      const q = query(blockedContactsRef, where('userId', '==', userId));
      
      const querySnapshot = await getDocs(q);
      const contacts: BlockedContact[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        blockedAt: doc.data().blockedAt?.toDate() || new Date(),
      })) as BlockedContact[];

      return { success: true, contacts };
    } catch (error) {
      return { success: false, error: 'Failed to get blocked contacts' };
    }
  }

  /**
   * Check if user is blocked
   */
  async isUserBlocked(userId: string, targetUserId: string): Promise<boolean> {
    try {
      const blockedContactsRef = collection(db, 'blocked_contacts');
      const q = query(
        blockedContactsRef,
        where('userId', '==', userId),
        where('blockedUserId', '==', targetUserId)
      );
      
      const querySnapshot = await getDocs(q);
      return !querySnapshot.empty;
    } catch (error) {
      return false;
    }
  }

  /**
   * Enable two-step verification
   */
  async enableTwoStepVerification(userId: string, pin: string): Promise<{ success: boolean; error?: string }> {
    try {
      // In a real implementation, you'd hash the PIN
      const settingsRef = doc(db, 'privacy_settings', userId);
      
      await updateDoc(settingsRef, {
        twoStepVerification: true,
        twoStepPin: pin, // In production, this should be hashed
        updatedAt: serverTimestamp(),
      });

      // Log security event
      await this.logSecurityEvent(userId, 'privacy_change', 'Two-step verification enabled');

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to enable two-step verification' };
    }
  }

  /**
   * Disable two-step verification
   */
  async disableTwoStepVerification(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const settingsRef = doc(db, 'privacy_settings', userId);
      
      await updateDoc(settingsRef, {
        twoStepVerification: false,
        twoStepPin: null,
        updatedAt: serverTimestamp(),
      });

      // Log security event
      await this.logSecurityEvent(userId, 'privacy_change', 'Two-step verification disabled');

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to disable two-step verification' };
    }
  }

  /**
   * Get security events
   */
  async getSecurityEvents(userId: string, limit: number = 50): Promise<{ success: boolean; events?: SecurityEvent[]; error?: string }> {
    try {
      const eventsRef = collection(db, 'security_events');
      const q = query(
        eventsRef,
        where('userId', '==', userId),
        // orderBy('timestamp', 'desc'),
        // limit(limit)
      );
      
      const querySnapshot = await getDocs(q);
      const events: SecurityEvent[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        timestamp: doc.data().timestamp?.toDate() || new Date(),
      })) as SecurityEvent[];

      // Sort by timestamp descending
      events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

      return { success: true, events: events.slice(0, limit) };
    } catch (error) {
      return { success: false, error: 'Failed to get security events' };
    }
  }

  /**
   * Log security event
   */
  async logSecurityEvent(
    userId: string,
    eventType: SecurityEvent['eventType'],
    description: string,
    additionalData?: {
      ipAddress?: string;
      deviceInfo?: string;
      location?: string;
    }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const eventsRef = collection(db, 'security_events');
      
      await addDoc(eventsRef, {
        userId,
        eventType,
        description,
        ipAddress: additionalData?.ipAddress,
        deviceInfo: additionalData?.deviceInfo,
        location: additionalData?.location,
        timestamp: serverTimestamp(),
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to log security event' };
    }
  }

  /**
   * Clear all data (for account deletion)
   */
  async clearAllUserData(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Delete privacy settings
      const settingsRef = doc(db, 'privacy_settings', userId);
      await deleteDoc(settingsRef);

      // Delete blocked contacts
      const blockedContactsRef = collection(db, 'blocked_contacts');
      const blockedQuery = query(blockedContactsRef, where('userId', '==', userId));
      const blockedSnapshot = await getDocs(blockedQuery);
      
      blockedSnapshot.docs.forEach(async (docSnapshot) => {
        await deleteDoc(docSnapshot.ref);
      });

      // Delete security events
      const eventsRef = collection(db, 'security_events');
      const eventsQuery = query(eventsRef, where('userId', '==', userId));
      const eventsSnapshot = await getDocs(eventsQuery);
      
      eventsSnapshot.docs.forEach(async (docSnapshot) => {
        await deleteDoc(docSnapshot.ref);
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to clear user data' };
    }
  }

  /**
   * Sync privacy settings to Firebase (called by sync service)
   */
  async syncPrivacySettingsToFirebase(settings: PrivacySettings): Promise<{ success: boolean; error?: string }> {
    try {
      if (!networkStateManager.isOnline()) {
        throw new Error('Cannot sync to Firebase while offline');
      }

      const settingsDoc = doc(db, 'privacy_settings', settings.userId);
      await setDoc(settingsDoc, {
        ...settings,
        updatedAt: serverTimestamp()
      }, { merge: true });

      // Update cache
      this.privacyCache.set(settings.userId, settings);

      return { success: true };
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Set disappearing messages for a specific chat
   */
  async setDisappearingMessages(chatId: string, duration: number | null): Promise<void> {
    try {
      const chatSettingsRef = doc(db, 'chatSettings', chatId);

      if (duration === null) {
        // Turn off disappearing messages
        await updateDoc(chatSettingsRef, {
          disappearingMessages: false,
          disappearingMessagesDuration: null,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Set disappearing messages duration
        await setDoc(chatSettingsRef, {
          chatId,
          disappearingMessages: true,
          disappearingMessagesDuration: duration,
          updatedAt: serverTimestamp(),
        }, { merge: true });
      }

      // Also save to offline storage
      try {
        const localDb = offlineDatabaseService.getDatabase();
        await localDb.runAsync(`
          INSERT OR REPLACE INTO chat_settings (chat_id, disappearing_messages, disappearing_duration, updated_at)
          VALUES (?, ?, ?, ?)
        `, [chatId, duration !== null, duration, new Date().toISOString()]);
      } catch (offlineError) {
        console.warn('Failed to save disappearing messages setting offline:', offlineError);
      }

      console.log(`Disappearing messages ${duration ? 'enabled' : 'disabled'} for chat ${chatId}`);
    } catch (error) {
      console.error('Error setting disappearing messages:', error);
      throw error;
    }
  }

  /**
   * Cleanup method
   */
  cleanup(): void {
    networkStateManager.removeListener('realPrivacyService');
    this.privacyCache.clear();
    this.syncQueue.clear();
    this.isInitialized = false;
  }
}

export const realPrivacyService = new RealPrivacyService();
