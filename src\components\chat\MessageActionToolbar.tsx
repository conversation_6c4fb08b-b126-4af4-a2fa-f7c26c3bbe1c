/**
 * Message Action Toolbar for IraChat
 * Shows context-aware actions when messages are selected
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Platform,
  StatusBar,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useMessageSelection } from '../../contexts/MessageSelectionContext';
import { useTheme } from '../../contexts/ThemeContext';
import { messageActionsService } from '../../services/messageActionsService';

interface MessageActionToolbarProps {
  currentUserId: string;
  currentUserName: string;
  onReply?: (message: any) => void;
  onDelete?: () => void;
  onForward?: () => void;
  onCopy?: () => void;
  onStar?: () => void;
  onPin?: () => void;
  onEdit?: (message: any) => void;
  onInfo?: () => void;
  onArchive?: () => void;
}

export const MessageActionToolbar: React.FC<MessageActionToolbarProps> = ({
  currentUserId,
  currentUserName: _currentUserName, // Prefix with underscore to indicate intentionally unused
  onReply,
  onDelete,
  onForward,
  onCopy,
  onStar,
  onPin,
  onEdit,
  onInfo,
  onArchive,
}) => {
  const { colors } = useTheme();
  const {
    isSelectionMode,
    getSelectedCount,
    getSelectedMessages,
    exitSelectionMode,
    canReply,
    canCopy,
    canForward,
    canDelete,
    canStar,
    canPin,
    canEdit,
  } = useMessageSelection();

  const [slideAnim] = useState(new Animated.Value(-100));
  const [fadeAnim] = useState(new Animated.Value(0));
  const [showMoreMenu, setShowMoreMenu] = useState(false);

  useEffect(() => {
    if (isSelectionMode) {
      // Animate toolbar in
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: false, // Changed to false for consistency
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 250,
          useNativeDriver: false, // Changed to false for consistency
        }),
      ]).start();
    } else {
      // Animate toolbar out
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: -100,
          duration: 200,
          useNativeDriver: false, // Changed to false for consistency
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: false, // Changed to false for consistency
        }),
      ]).start();
    }
  }, [isSelectionMode]);

  if (!isSelectionMode) {
    return null;
  }

  const selectedCount = getSelectedCount();
  const isMultipleSelected = selectedCount > 1;

  const handleClose = () => {
    exitSelectionMode();
  };

  // Action handlers
  const handleReply = () => {
    const selectedMessages = getSelectedMessages();
    if (selectedMessages.length === 1) {
      onReply?.(selectedMessages[0]);
    }
  };

  const handleCopy = async () => {
    const selectedMessages = getSelectedMessages();
    const result = await messageActionsService.copyMessages(selectedMessages, currentUserId);

    if (result.success) {
      console.log(`✅ ${selectedMessages.length} message(s) copied to clipboard`);
      exitSelectionMode();
    } else {
      console.error('❌ Failed to copy messages:', result.error);
    }
    onCopy?.();
  };

  const handleDelete = () => {
    const selectedMessages = getSelectedMessages();
    const hasOwnMessages = selectedMessages.some(msg => msg.isOwn);
    const hasOthersMessages = selectedMessages.some(msg => !msg.isOwn);

    if (hasOthersMessages && hasOwnMessages) {
      // Only delete own messages
      performDelete(selectedMessages.filter(msg => msg.isOwn), false);
    } else if (hasOthersMessages) {
      console.warn('Cannot delete others\' messages');
      exitSelectionMode();
    } else {
      // All are own messages - delete for everyone by default
      performDelete(selectedMessages, true);
    }
  };

  const performDelete = async (messages: any[], deleteForEveryone: boolean) => {
    const result = await messageActionsService.deleteMessages(messages, currentUserId, deleteForEveryone);

    if (result.success) {
      console.log(`✅ Messages deleted ${deleteForEveryone ? 'for everyone' : 'for you'}`);
      exitSelectionMode();
    } else {
      console.error('❌ Failed to delete messages:', result.error);
      exitSelectionMode();
    }
    onDelete?.();
  };

  const handleStar = async () => {
    const selectedMessages = getSelectedMessages();
    const result = await messageActionsService.toggleStarMessages(selectedMessages, currentUserId, true);

    if (result.success) {
      console.log(`⭐ ${selectedMessages.length} message(s) starred`);
      exitSelectionMode();
    } else {
      console.error('❌ Failed to star messages:', result.error);
      exitSelectionMode();
    }
    onStar?.();
  };

  const handlePin = () => {
    const selectedMessages = getSelectedMessages();
    if (selectedMessages.length === 1) {
      // Pin for 24 hours by default
      pinMessage(selectedMessages[0], 24 * 60 * 60 * 1000);
    }
    onPin?.();
  };

  const pinMessage = async (message: any, duration: number | null) => {
    try {
      const result = await messageActionsService.togglePinMessage(message, currentUserId, true, duration);

      if (result.success) {
        console.log(`📌 Message pinned${duration ? ` for ${formatDuration(duration)}` : ' forever'}`);
        exitSelectionMode();
      } else {
        console.error('❌ Failed to pin message:', result.error);
        exitSelectionMode();
      }
    } catch (error) {
      console.error('❌ Failed to pin message:', error);
      exitSelectionMode();
    }
  };

  const formatDuration = (ms: number): string => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} day${days > 1 ? 's' : ''}`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''}`;
    return 'a short time';
  };

  const handleEdit = () => {
    const selectedMessages = getSelectedMessages();
    if (selectedMessages.length === 1) {
      onEdit?.(selectedMessages[0]);
    }
  };

  const handleForward = async () => {
    const selectedMessages = getSelectedMessages();

    // For now, just log the forward action
    // In a full implementation, you'd open a chat selector
    console.log(`🔄 Forward ${selectedMessages.length} message(s) - Chat selector needed`);
    exitSelectionMode();
    onForward?.();
  };

  const handleArchive = async () => {
    const selectedMessages = getSelectedMessages();
    const result = await messageActionsService.archiveMessages(selectedMessages, currentUserId);

    if (result.success) {
      console.log(`📦 ${selectedMessages.length} message(s) archived`);
      exitSelectionMode();
    } else {
      console.error('❌ Failed to archive messages:', result.error);
      exitSelectionMode();
    }
    onArchive?.();
  };

  const handleInfo = () => {
    const selectedMessages = getSelectedMessages();
    if (selectedMessages.length === 1) {
      const message = selectedMessages[0];
      // Show delivery/read status instead of basic info
      console.log(`ℹ️ Message Info - ID: ${message.id}, Type: ${message.type}, Sent: ${message.timestamp.toLocaleString()}`);
      // TODO: Implement proper message info modal with delivery/read status
      exitSelectionMode();
    }
    onInfo?.();
  };



  const ActionButton: React.FC<{
    icon: string;
    onPress?: () => void;
    disabled?: boolean;
    testID?: string;
  }> = ({ icon, onPress, disabled = false, testID }) => (
    <TouchableOpacity
      style={[
        styles.actionButton,
        disabled && styles.actionButtonDisabled,
      ]}
      onPress={onPress}
      disabled={disabled}
      testID={testID}
    >
      <Ionicons
        name={icon as any}
        size={20} // Reduced from 24 to 20
        color={disabled ? colors.textSecondary : colors.primary}
      />
    </TouchableOpacity>
  );

  return (
    <Animated.View
      style={[
        styles.toolbar,
        {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
          transform: [{ translateY: slideAnim }],
          opacity: fadeAnim,
        },
      ]}
    >
      {/* Left side - Close button and count */}
      <View style={styles.leftSection}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={handleClose}
          testID="close-selection"
        >
          <Ionicons name="close" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.countText, { color: colors.text }]}>
          {selectedCount}
        </Text>
      </View>

      {/* Right side - Action buttons */}
      <View style={styles.rightSection}>
        {/* Primary Actions - Always Visible */}

        {/* Reply - only for single message */}
        {!isMultipleSelected && (
          <ActionButton
            icon="arrow-undo"
            onPress={handleReply}
            disabled={!canReply()}
            testID="reply-action"
          />
        )}

        {/* Copy - only if messages have text content */}
        <ActionButton
          icon="copy"
          onPress={handleCopy}
          disabled={!canCopy()}
          testID="copy-action"
        />

        {/* Forward */}
        <ActionButton
          icon="arrow-forward"
          onPress={handleForward}
          disabled={!canForward()}
          testID="forward-action"
        />

        {/* Delete - only for own messages */}
        <ActionButton
          icon="trash"
          onPress={handleDelete}
          disabled={!canDelete()}
          testID="delete-action"
        />

        {/* Three-dot menu for additional actions */}
        <View style={styles.moreMenuContainer}>
          <ActionButton
            icon="ellipsis-vertical"
            onPress={() => setShowMoreMenu(true)}
            testID="more-actions"
          />

          {/* More Actions Menu positioned relative to three-dot button */}
          {showMoreMenu && (
            <>
              {/* Invisible overlay to close menu when clicking outside */}
              <TouchableOpacity
                style={styles.menuOverlay}
                onPress={() => setShowMoreMenu(false)}
                activeOpacity={1}
              />
              <View style={[styles.moreMenu, { backgroundColor: colors.surface }]}>
                {/* Star */}
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setShowMoreMenu(false);
                    handleStar();
                  }}
                  disabled={!canStar()}
                >
                  <Ionicons name="star" size={20} color={colors.text} />
                  <Text style={[styles.menuText, { color: colors.text }]}>Star</Text>
                </TouchableOpacity>

                {/* Pin - only for single message */}
                {!isMultipleSelected && (
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowMoreMenu(false);
                      handlePin();
                    }}
                    disabled={!canPin()}
                  >
                    <Ionicons name="pin" size={20} color={colors.text} />
                    <Text style={[styles.menuText, { color: colors.text }]}>Pin</Text>
                  </TouchableOpacity>
                )}

                {/* Edit - only for single text message from current user */}
                {!isMultipleSelected && (
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => {
                      setShowMoreMenu(false);
                      handleEdit();
                    }}
                    disabled={!canEdit()}
                  >
                    <Ionicons name="create" size={20} color={colors.text} />
                    <Text style={[styles.menuText, { color: colors.text }]}>Edit</Text>
                  </TouchableOpacity>
                )}

                {/* Info */}
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setShowMoreMenu(false);
                    handleInfo();
                  }}
                >
                  <Ionicons name="information-circle" size={20} color={colors.text} />
                  <Text style={[styles.menuText, { color: colors.text }]}>Info</Text>
                </TouchableOpacity>

                {/* Archive */}
                <TouchableOpacity
                  style={styles.menuItem}
                  onPress={() => {
                    setShowMoreMenu(false);
                    handleArchive();
                  }}
                >
                  <Ionicons name="archive" size={20} color={colors.text} />
                  <Text style={[styles.menuText, { color: colors.text }]}>Archive</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </View>


    </Animated.View>
  );
};

const styles = StyleSheet.create({
  toolbar: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 100 : (StatusBar.currentHeight || 0) + 56, // Position below header
    left: 0,
    right: 0,
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    zIndex: 1000,
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  closeButton: {
    padding: 8,
    marginRight: 16,
  },
  countText: {
    fontSize: 18,
    fontWeight: '600',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 6, // Reduced from 8
    marginLeft: 4, // Reduced from 8
    borderRadius: 16, // Reduced from 20
    minWidth: 32, // Reduced from 40
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButtonDisabled: {
    opacity: 0.3,
  },
  moreMenuContainer: {
    position: 'relative',
  },
  menuOverlay: {
    position: 'absolute',
    top: -1000, // Cover entire screen
    left: -1000,
    right: -1000,
    bottom: -1000,
    zIndex: 1000,
  },
  moreMenu: {
    position: 'absolute',
    top: 40, // Position below the three-dot button
    right: 0, // Align to the right edge of the three-dot button
    borderRadius: 8,
    paddingVertical: 8,
    minWidth: 150,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    zIndex: 1001, // Ensure it appears above other elements
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  menuText: {
    marginLeft: 12,
    fontSize: 16,
  },
});

export default MessageActionToolbar;
