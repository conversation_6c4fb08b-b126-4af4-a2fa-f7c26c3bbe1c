import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const COLORS = {
  primary: '#1DA1F2',
  success: '#17BF63',
  warning: '#FFAD1F',
  error: '#E0245E',
  textSecondary: '#657786',
  background: '#FFFFFF',
};

interface SyncIndicatorProps {
  syncStatus?: string;
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
}

export const SyncIndicator: React.FC<SyncIndicatorProps> = ({
  syncStatus,
  size = 'small',
  showText = false,
}) => {
  if (!syncStatus || syncStatus === 'synced') {
    return null; // Don't show indicator for synced posts
  }

  const getIndicatorConfig = () => {
    switch (syncStatus) {
      case 'pending':
        return {
          icon: 'cloud-upload-outline' as const,
          color: COLORS.warning,
          text: 'Pending sync',
          backgroundColor: COLORS.warning + '20',
        };
      case 'syncing':
        return {
          icon: 'cloud-upload' as const,
          color: COLORS.primary,
          text: 'Syncing...',
          backgroundColor: COLORS.primary + '20',
        };
      case 'failed':
        return {
          icon: 'warning' as const,
          color: COLORS.error,
          text: 'Sync failed - Tap to retry',
          backgroundColor: COLORS.error + '20',
        };
      default:
        return {
          icon: 'cloud-outline' as const,
          color: COLORS.textSecondary,
          text: 'Unknown',
          backgroundColor: COLORS.textSecondary + '20',
        };
    }
  };

  const config = getIndicatorConfig();
  
  const iconSize = size === 'small' ? 12 : size === 'medium' ? 16 : 20;
  const fontSize = size === 'small' ? 10 : size === 'medium' ? 12 : 14;
  const padding = size === 'small' ? 4 : size === 'medium' ? 6 : 8;

  if (showText) {
    return (
      <View style={[
        styles.container,
        { backgroundColor: config.backgroundColor, padding }
      ]}>
        <Ionicons 
          name={config.icon} 
          size={iconSize} 
          color={config.color} 
        />
        <Text style={[
          styles.text,
          { color: config.color, fontSize, marginLeft: 4 }
        ]}>
          {config.text}
        </Text>
      </View>
    );
  }

  return (
    <View style={[
      styles.iconContainer,
      { backgroundColor: config.backgroundColor, padding: padding / 2 }
    ]}>
      <Ionicons 
        name={config.icon} 
        size={iconSize} 
        color={config.color} 
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  iconContainer: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '500',
  },
});
