import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import {
  PanGestureHandler,
  PinchGestureHandler,
  RotationGestureHandler,
  State,
} from 'react-native-gesture-handler';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  runOnJS,
  withSpring,
} from 'react-native-reanimated';
import { TextOverlay } from '../../types/Update';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface TextOverlayEditorProps {
  visible: boolean;
  mediaWidth: number;
  mediaHeight: number;
  onClose: () => void;
  onSave: (textOverlays: TextOverlay[]) => void;
  initialOverlays?: TextOverlay[];
}

export const TextOverlayEditor: React.FC<TextOverlayEditorProps> = ({
  visible,
  mediaWidth,
  mediaHeight,
  onClose,
  onSave,
  initialOverlays = [],
}) => {
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>(initialOverlays);
  const [selectedOverlayId, setSelectedOverlayId] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editingText, setEditingText] = useState('');
  const [showStylePanel, setShowStylePanel] = useState(false);

  // Style options
  const [fontSize, setFontSize] = useState(24);
  const [textColor, setTextColor] = useState('#FFFFFF');
  const [backgroundColor, setBgColor] = useState('transparent');
  const [textAlign, setTextAlign] = useState<'left' | 'center' | 'right'>('center');
  const [fontWeight, setFontWeight] = useState<'normal' | 'bold'>('normal');

  const colors = ['#FFFFFF', '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
  const bgColors = ['transparent', '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF'];

  const addTextOverlay = () => {
    const newOverlay: TextOverlay = {
      id: `text_${Date.now()}`,
      text: 'Tap to edit',
      position: {
        x: 50, // Center horizontally
        y: 50, // Center vertically
      },
      size: {
        width: 40, // 40% of media width
        height: 10, // 10% of media height
      },
      style: {
        fontSize,
        color: textColor,
        backgroundColor,
        textAlign,
        fontWeight,
        opacity: 1,
      },
      rotation: 0,
      zIndex: textOverlays.length + 1,
    };

    setTextOverlays([...textOverlays, newOverlay]);
    setSelectedOverlayId(newOverlay.id);
  };

  const deleteOverlay = (id: string) => {
    setTextOverlays(textOverlays.filter(overlay => overlay.id !== id));
    if (selectedOverlayId === id) {
      setSelectedOverlayId(null);
    }
  };

  const updateOverlay = (id: string, updates: Partial<TextOverlay>) => {
    setTextOverlays(textOverlays.map(overlay =>
      overlay.id === id ? { ...overlay, ...updates } : overlay
    ));
  };

  const startEditing = (overlay: TextOverlay) => {
    setSelectedOverlayId(overlay.id);
    setEditingText(overlay.text);
    setIsEditing(true);
  };

  const finishEditing = () => {
    if (selectedOverlayId && editingText.trim()) {
      updateOverlay(selectedOverlayId, { text: editingText.trim() });
    }
    setIsEditing(false);
    setEditingText('');
  };

  const handleSave = () => {
    onSave(textOverlays);
    onClose();
  };

  if (!visible) return null;

  return (
    <View style={styles.container}>
      {/* Media Preview Area */}
      <View style={[styles.mediaArea, { width: mediaWidth, height: mediaHeight }]}>
        {/* Text Overlays */}
        {textOverlays.map((overlay) => (
          <TextOverlayComponent
            key={overlay.id}
            overlay={overlay}
            mediaWidth={mediaWidth}
            mediaHeight={mediaHeight}
            isSelected={selectedOverlayId === overlay.id}
            onSelect={() => setSelectedOverlayId(overlay.id)}
            onEdit={() => startEditing(overlay)}
            onUpdate={(updates) => updateOverlay(overlay.id, updates)}
            onDelete={() => deleteOverlay(overlay.id)}
          />
        ))}
      </View>

      {/* Text Input Modal */}
      {isEditing && (
        <View style={styles.editModal}>
          <TextInput
            style={styles.textInput}
            value={editingText}
            onChangeText={setEditingText}
            placeholder="Enter text..."
            placeholderTextColor="#999"
            multiline
            autoFocus
          />
          <View style={styles.editActions}>
            <TouchableOpacity style={styles.editButton} onPress={() => setIsEditing(false)}>
              <Text style={styles.editButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.editButton} onPress={finishEditing}>
              <Text style={styles.editButtonText}>Done</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Style Panel */}
      {showStylePanel && selectedOverlayId && (
        <View style={styles.stylePanel}>
          {/* Font Size */}
          <View style={styles.styleRow}>
            <Text style={styles.styleLabel}>Size</Text>
            <View style={styles.sizeButtons}>
              {[16, 20, 24, 32, 40].map(size => (
                <TouchableOpacity
                  key={size}
                  style={[styles.sizeButton, fontSize === size && styles.sizeButtonActive]}
                  onPress={() => {
                    setFontSize(size);
                    if (selectedOverlayId) {
                      const currentOverlay = textOverlays.find(o => o.id === selectedOverlayId);
                      if (currentOverlay) {
                        updateOverlay(selectedOverlayId, {
                          style: { ...currentOverlay.style, fontSize: size }
                        });
                      }
                    }
                  }}
                >
                  <Text style={styles.sizeButtonText}>{size}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Text Colors */}
          <View style={styles.styleRow}>
            <Text style={styles.styleLabel}>Color</Text>
            <View style={styles.colorRow}>
              {colors.map(color => (
                <TouchableOpacity
                  key={color}
                  style={[styles.colorButton, { backgroundColor: color }]}
                  onPress={() => {
                    setTextColor(color);
                    if (selectedOverlayId) {
                      const currentOverlay = textOverlays.find(o => o.id === selectedOverlayId);
                      if (currentOverlay) {
                        updateOverlay(selectedOverlayId, {
                          style: { ...currentOverlay.style, color }
                        });
                      }
                    }
                  }}
                />
              ))}
            </View>
          </View>

          {/* Background Colors */}
          <View style={styles.styleRow}>
            <Text style={styles.styleLabel}>Background</Text>
            <View style={styles.colorRow}>
              {bgColors.map(color => (
                <TouchableOpacity
                  key={color}
                  style={[
                    styles.colorButton,
                    { backgroundColor: color === 'transparent' ? '#f0f0f0' : color },
                    color === 'transparent' && styles.transparentButton
                  ]}
                  onPress={() => {
                    setBgColor(color);
                    if (selectedOverlayId) {
                      const currentOverlay = textOverlays.find(o => o.id === selectedOverlayId);
                      if (currentOverlay) {
                        updateOverlay(selectedOverlayId, {
                          style: { ...currentOverlay.style, backgroundColor: color }
                        });
                      }
                    }
                  }}
                />
              ))}
            </View>
          </View>
        </View>
      )}

      {/* Bottom Controls */}
      <View style={styles.controls}>
        <TouchableOpacity style={styles.controlButton} onPress={addTextOverlay}>
          <Ionicons name="add" size={24} color="#FFFFFF" />
          <Text style={styles.controlText}>Add Text</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => setShowStylePanel(!showStylePanel)}
          disabled={!selectedOverlayId}
        >
          <Ionicons name="color-palette" size={24} color={selectedOverlayId ? "#FFFFFF" : "#666"} />
          <Text style={[styles.controlText, !selectedOverlayId && { color: '#666' }]}>Style</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton} onPress={onClose}>
          <Ionicons name="close" size={24} color="#FFFFFF" />
          <Text style={styles.controlText}>Cancel</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton} onPress={handleSave}>
          <Ionicons name="checkmark" size={24} color="#FFFFFF" />
          <Text style={styles.controlText}>Save</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

// Individual Text Overlay Component
interface TextOverlayComponentProps {
  overlay: TextOverlay;
  mediaWidth: number;
  mediaHeight: number;
  isSelected: boolean;
  onSelect: () => void;
  onEdit: () => void;
  onUpdate: (updates: Partial<TextOverlay>) => void;
  onDelete: () => void;
}

const TextOverlayComponent: React.FC<TextOverlayComponentProps> = ({
  overlay,
  mediaWidth,
  mediaHeight,
  isSelected,
  onSelect,
  onEdit,
  onUpdate,
  onDelete,
}) => {
  const translateX = useSharedValue((overlay.position.x / 100) * mediaWidth);
  const translateY = useSharedValue((overlay.position.y / 100) * mediaHeight);
  const scale = useSharedValue(1);
  const rotation = useSharedValue(overlay.rotation || 0);

  // Handle pan gesture events
  const handlePanStart = () => {
    onSelect();
  };

  const handlePanUpdate = (event: any) => {
    translateX.value = event.translationX + (overlay.position.x / 100) * mediaWidth;
    translateY.value = event.translationY + (overlay.position.y / 100) * mediaHeight;
  };

  const handlePanEnd = () => {
    const newX = Math.max(0, Math.min(100, (translateX.value / mediaWidth) * 100));
    const newY = Math.max(0, Math.min(100, (translateY.value / mediaHeight) * 100));

    onUpdate({
      position: { x: newX, y: newY }
    });
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [
      { translateX: translateX.value },
      { translateY: translateY.value },
      { scale: scale.value },
      { rotate: `${rotation.value}deg` },
    ],
  }));

  return (
    <PanGestureHandler
      onGestureEvent={(event) => {
        handlePanUpdate(event.nativeEvent);
      }}
      onHandlerStateChange={(event) => {
        if (event.nativeEvent.state === State.BEGAN) {
          handlePanStart();
        } else if (event.nativeEvent.state === State.END) {
          handlePanEnd();
        }
      }}
    >
      <Animated.View
        style={[
          styles.textOverlay,
          {
            width: (overlay.size.width / 100) * mediaWidth,
            minHeight: (overlay.size.height / 100) * mediaHeight,
            zIndex: overlay.zIndex,
          },
          animatedStyle,
          isSelected && styles.selectedOverlay,
        ]}
      >
        <TouchableOpacity onPress={onEdit} style={styles.textContainer}>
          <Text
            style={[
              styles.overlayText,
              {
                fontSize: overlay.style.fontSize,
                color: overlay.style.color,
                backgroundColor: overlay.style.backgroundColor,
                textAlign: overlay.style.textAlign,
                fontWeight: overlay.style.fontWeight,
                opacity: overlay.style.opacity,
              },
            ]}
          >
            {overlay.text}
          </Text>
        </TouchableOpacity>

        {isSelected && (
          <TouchableOpacity style={styles.deleteButton} onPress={onDelete}>
            <Ionicons name="close-circle" size={20} color="#ff4444" />
          </TouchableOpacity>
        )}
      </Animated.View>
    </PanGestureHandler>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
    alignItems: 'center',
    justifyContent: 'center',
  },
  mediaArea: {
    backgroundColor: '#1a1a1a',
    position: 'relative',
    overflow: 'hidden',
  },
  textOverlay: {
    position: 'absolute',
    borderWidth: 1,
    borderColor: 'transparent',
    borderRadius: 4,
  },
  selectedOverlay: {
    borderColor: '#1DA1F2',
    borderWidth: 2,
  },
  textContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  overlayText: {
    textAlign: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  deleteButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: '#FF0000',
    borderRadius: 10,
  },
  editModal: {
    position: 'absolute',
    top: '30%',
    left: 20,
    right: 20,
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 20,
    zIndex: 1000,
  },
  textInput: {
    backgroundColor: '#333',
    color: '#FFFFFF',
    padding: 12,
    borderRadius: 8,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  editButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  editButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  stylePanel: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 20,
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
    padding: 16,
    zIndex: 999,
  },
  styleRow: {
    marginBottom: 16,
  },
  styleLabel: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  sizeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  sizeButton: {
    backgroundColor: '#333',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
  },
  sizeButtonActive: {
    backgroundColor: '#667eea',
  },
  sizeButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  colorRow: {
    flexDirection: 'row',
    gap: 8,
  },
  colorButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#333',
  },
  transparentButton: {
    borderStyle: 'dashed',
  },
  controls: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: '#1a1a1a',
    paddingVertical: 16,
    paddingHorizontal: 20,
    justifyContent: 'space-around',
  },
  controlButton: {
    alignItems: 'center',
    gap: 4,
  },
  controlText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
});
