import { addDoc, collection, serverTimestamp, Timestamp } from 'firebase/firestore';
import { db } from './firebaseSimple';
import { localUpdatesStorage, LocalUpdate } from './localUpdatesStorage';
import { networkStateManager } from './networkStateManager';
import { Update } from '../types/Update';

class UpdatesSyncService {
  private syncInProgress = false;
  private syncQueue: Set<string> = new Set();
  private backgroundSyncInterval: NodeJS.Timeout | null = null;

  async queueForSync(update: Update): Promise<void> {
    // For Update objects, we need to find the localId or use the id if it's a local update
    const localId = (update as any).localId || update.id;
    this.syncQueue.add(localId);
    await localUpdatesStorage.updateSyncStatus(localId, 'pending');

    if (networkStateManager.isOnline()) {
      this.syncPendingUpdates();
    }
  }

  async syncPendingUpdates(): Promise<void> {
    if (!networkStateManager.isOnline() || this.syncInProgress) {
      return;
    }

    this.syncInProgress = true;

    try {
      const pendingUpdates = await localUpdatesStorage.getPendingSyncUpdates();

      for (const localUpdate of pendingUpdates) {
        try {
          await this.syncSingleUpdate(localUpdate);
          this.syncQueue.delete(localUpdate.localId);
        } catch (error) {
          await localUpdatesStorage.updateSyncStatus(localUpdate.localId, 'failed');
        }
      }
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncSingleUpdate(localUpdate: LocalUpdate): Promise<void> {
    try {
      // Mark as pending while syncing (no 'syncing' status available)

      // Load media for this local update if not already loaded
      let mediaData = localUpdate.media;
      if (!mediaData || mediaData.length === 0) {
        // Load media from database using the localId
        const fullUpdate = await localUpdatesStorage.getUpdateById(localUpdate.localId);
        mediaData = fullUpdate?.media || [];
      }

      const updateData = {
        userId: localUpdate.userId,
        userName: localUpdate.userName,
        userAvatar: localUpdate.userAvatar,
        type: localUpdate.type,
        mediaUrl: localUpdate.mediaUrl || mediaData[0]?.url,
        thumbnailUrl: mediaData[0]?.thumbnailUrl,
        caption: localUpdate.caption,
        duration: mediaData[0]?.duration,
        timestamp: serverTimestamp(),
        expiresAt: localUpdate.expiresAt ? Timestamp.fromDate(new Date(localUpdate.expiresAt)) : null,
        views: localUpdate.views,
        likes: localUpdate.likes,
        comments: localUpdate.comments,
        shares: localUpdate.shares,
        downloads: localUpdate.downloads,
        isVisible: localUpdate.isVisible,
        isStory: localUpdate.isStory,
        privacy: localUpdate.privacy,
        location: localUpdate.location,
        hashtags: localUpdate.hashtags,
        mentions: localUpdate.mentions,
        groupTags: localUpdate.groupTags,
        // Media information
        media: mediaData,
        // Analytics data
        viewCount: localUpdate.viewCount,
        likeCount: localUpdate.likeCount,
        commentCount: localUpdate.commentCount,
        shareCount: localUpdate.shareCount,
        downloadCount: localUpdate.downloadCount,
        // User interaction states
        isLikedByCurrentUser: localUpdate.isLikedByCurrentUser,
        isViewedByCurrentUser: localUpdate.isViewedByCurrentUser,
        isSharedByCurrentUser: localUpdate.isSharedByCurrentUser,
        isDownloadedByCurrentUser: localUpdate.isDownloadedByCurrentUser,
        // Moderation flags
        isReported: localUpdate.isReported,
        reportCount: localUpdate.reportCount,
        isFlagged: localUpdate.isFlagged,
        // Additional features
        isPinned: localUpdate.isPinned,
        isHighlight: localUpdate.isHighlight,
        musicTrack: localUpdate.musicTrack
      };

      const docRef = await addDoc(collection(db, "updates"), updateData);

      await localUpdatesStorage.updateSyncStatus(localUpdate.localId, 'synced', docRef.id);
      this.syncQueue.delete(localUpdate.localId);
    } catch (error) {
      await localUpdatesStorage.updateSyncStatus(localUpdate.localId, 'failed');
      throw error;
    }
  }

  async retryFailedSyncs(): Promise<void> {
    const failedUpdates = await localUpdatesStorage.getFailedSyncUpdates();

    for (const update of failedUpdates) {
      this.syncQueue.add(update.localId);
      await localUpdatesStorage.updateSyncStatus(update.localId, 'pending');
    }

    if (networkStateManager.isOnline()) {
      await this.syncPendingUpdates();
    }
  }

  async getSyncStatus(): Promise<{ pending: number; failed: number; syncing: boolean }> {
    const failedUpdates = await localUpdatesStorage.getFailedSyncUpdates();
    return {
      pending: this.syncQueue.size,
      failed: failedUpdates.length,
      syncing: this.syncInProgress
    };
  }

  startBackgroundSync(): void {
    // Clear any existing interval
    if (this.backgroundSyncInterval) {
      clearInterval(this.backgroundSyncInterval);
      this.backgroundSyncInterval = null;
    }

    // Start periodic sync every 30 seconds when online
    this.backgroundSyncInterval = setInterval(() => {
      if (networkStateManager.isOnline() && !this.syncInProgress) {
        this.syncPendingUpdates().catch(error => {
          console.error('❌ Background sync failed:', error);
        });
      }
    }, 30000);

    // Also sync immediately if online
    if (networkStateManager.isOnline()) {
      this.syncPendingUpdates().catch(error => {
        console.error('❌ Initial sync failed:', error);
      });
    }
  }

  stopBackgroundSync(): void {
    try {
      if (this.backgroundSyncInterval) {
        clearInterval(this.backgroundSyncInterval);
        this.backgroundSyncInterval = null;
      }
    } catch (error) {
      console.error('Error stopping background sync:', error);
    }
  }

  async forceSyncAll(): Promise<{ synced: number; failed: number }> {
    try {
      const pendingUpdates = await localUpdatesStorage.getPendingSyncUpdates();
      let synced = 0;
      let failed = 0;

      for (const localUpdate of pendingUpdates) {
        try {
          await this.syncSingleUpdate(localUpdate);
          synced++;
          this.syncQueue.delete(localUpdate.localId);
        } catch (error) {
          failed++;
          await localUpdatesStorage.updateSyncStatus(localUpdate.localId, 'failed');
        }
      }

      return { synced, failed };
    } catch (error) {
      console.error('Error in forceSyncAll:', error);
      return { synced: 0, failed: 0 };
    }
  }
}

// Create and export a single instance
export const updatesSyncService = new UpdatesSyncService();

