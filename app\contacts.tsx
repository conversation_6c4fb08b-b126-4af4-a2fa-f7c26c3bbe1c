import { Ionicons } from "@expo/vector-icons";
import { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { useRouter } from "expo-router";
import {
    FlatList,
    Text,
    TextInput,
    TouchableOpacity,
    View,
    ActivityIndicator,
    StyleSheet,
    RefreshControl,
    Alert,
    Image,
    ListRenderItem,
    Linking,
    Share,
    Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import optimizedContactsService from "../src/services/optimizedContactsService";
import { navigationService } from "../src/services/navigationService";
import { onlineStatusService } from "../src/services/onlineStatusService";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS } from "../src/styles/iraChatDesignSystem";
import { ResponsiveSpacing, ResponsiveTypography, DeviceInfo } from "../src/utils/responsiveUtils";
import { ResponsiveContainer } from "../src/components/ui/ResponsiveContainer";
import { AppHeader } from "../src/components/AppHeader";
import ErrorBoundary from "../src/components/ErrorBoundary";

// Enhanced Contact interface for IraChat users and phone contacts
interface Contact {
  id: string;
  name: string;
  phoneNumber?: string;
  primaryEmail?: string; // Primary email address
  company?: string; // Company/organization info
  avatar?: string;
  isOnline?: boolean;
  lastSeen?: Date | string;
  userId?: string;
  isIraChatUser: boolean; // Whether this contact is registered on IraChat
  profilePhotoVisible?: boolean; // Whether profile photo should be visible based on privacy settings
  initials?: string; // Name initials for display when no photo
}

// No mock contacts - all contacts will come from phone and Firebase
export default function ContactsScreen() {
  const router = useRouter();
  const [searchText, setSearchText] = useState("");
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState('Initializing...');
  const [invitedContacts, setInvitedContacts] = useState<Set<string>>(new Set());
  const [onlineStatusMap, setOnlineStatusMap] = useState<Map<string, boolean>>(new Map());

  // MEMORY OPTIMIZATION: Use refs to prevent memory leaks
  const isMountedRef = useRef(true);
  const flatListRef = useRef<FlatList>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const onlineStatusListeners = useRef<Map<string, () => void>>(new Map());

  // Load both phone contacts and IraChat users - OPTIMIZED with progress
  const loadContacts = useCallback(async (isRefresh = false) => {
    // Safety timeout to ensure loading state is cleared
    const safetyTimeout = setTimeout(() => {
      console.warn('⚠️ Loading timeout - clearing loading state');
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }, 10000); // 10 second safety timeout

    try {
      console.log(`🔄 [DEBUG] loadContacts called with isRefresh: ${isRefresh}`);
      if (isRefresh) {
        console.log(`🔄 [DEBUG] Setting refreshing state to true`);
        setRefreshing(true);
      } else {
        console.log(`🔄 [DEBUG] Setting loading state to true`);
        setLoading(true);
        setLoadingProgress(0);
        setLoadingMessage('Requesting permissions...');
      }
      console.log('📱 Starting to load contacts...');

      // First, explicitly request contacts permission
      setLoadingProgress(10);
      setLoadingMessage('Checking permissions...');
      const hasPermission = await optimizedContactsService.requestPermission();
      console.log('📱 Contacts permission granted:', hasPermission);

      if (!hasPermission) {
        console.warn("❌ No contacts permission - showing permission dialog");
        Alert.alert(
          'Contacts Permission Required',
          'IraChat needs access to your contacts to help you find friends who are already using the app and invite others.\n\nAfter clearing app data, you need to grant permission again.',
          [
            {
              text: 'Not Now',
              style: 'cancel',
              onPress: () => {
                // Show empty state with explanation
                setContacts([]);
                setFilteredContacts([]);
                setLoadingMessage('Contacts permission required');
              }
            },
            {
              text: 'Grant Permission',
              onPress: async () => {
                console.log("🔐 User chose to grant permission");
                const granted = await optimizedContactsService.requestPermission();
                if (granted) {
                  console.log("✅ Permission granted, retrying contact load");
                  loadContacts(isRefresh); // Retry loading
                } else {
                  console.warn("❌ Permission still denied after user action");
                  Alert.alert(
                    'Permission Denied',
                    'To use contacts, please go to Settings > Apps > IraChat > Permissions and enable Contacts access.',
                    [{ text: 'OK' }]
                  );
                  setContacts([]);
                  setFilteredContacts([]);
                }
              }
            }
          ]
        );
        if (isRefresh) {
          setRefreshing(false);
        } else {
          setLoading(false);
        }
        return;
      }

      // OPTIMIZED: Use the optimized service directly with progress
      setLoadingProgress(30);
      setLoadingMessage('Loading device contacts...');
      console.log('📱 Using optimized contacts service...');

      // Debug service status before loading
      const serviceStatus = optimizedContactsService.getServiceStatus();
      console.log(`🔍 Service status before loading:`, serviceStatus);

      // Use cache for better performance, only refresh when explicitly requested
      console.log(`🔄 Loading contacts with cache optimization (isRefresh: ${isRefresh})...`);
      const allContacts = isRefresh
        ? await optimizedContactsService.refreshContacts()
        : await optimizedContactsService.getIraChatContacts();
      console.log(`✅ Contacts service returned ${allContacts.length} contacts`);

      // Debug service status after loading
      const serviceStatusAfter = optimizedContactsService.getServiceStatus();
      console.log(`🔍 Service status after loading:`, serviceStatusAfter);

      // Debug: Check if contacts are valid
      if (allContacts.length > 0) {
        console.log(`📱 Sample contact:`, allContacts[0]);
      } else {
        console.warn(`⚠️ No contacts returned from service - this might be normal for first run`);
      }

      // Ensure allContacts is always an array
      const safeContacts = Array.isArray(allContacts) ? allContacts : [];
      console.log('📱 Loaded contacts:', safeContacts.length);

      setLoadingProgress(70);
      setLoadingMessage('Showing contacts...');

      // Process contacts with initials immediately
      const processedContacts = safeContacts.map(contact => ({
        ...contact,
        primaryEmail: contact.primaryEmail || '',
        company: contact.company || '',
        initials: contact.name ? contact.name.split(' ').map((n: string) => n[0]).join('').toUpperCase().slice(0, 2) : '??',
      }));

      // Show processed contacts immediately
      setContacts(processedContacts);
      setFilteredContacts(processedContacts);

      setLoadingProgress(100);
      setLoadingMessage('Complete!');

      // Contacts are already set above, no need for duplicate processing

      console.log(`✅ Loaded ${allContacts.length} total contacts from ALL sources`);
      console.log(`📱 ${allContacts.filter(c => c.isIraChatUser).length} IraChat users`);
      console.log(`📞 ${allContacts.filter(c => !c.isIraChatUser).length} contacts to invite`);
      console.log(`📧 ${allContacts.filter(c => c.primaryEmail).length} contacts with emails`);
      console.log(`🏢 ${allContacts.filter(c => c.company).length} contacts with company info`);

      // Show helpful message if no contacts found (only if not refreshing and component is still mounted)
      if (allContacts.length === 0 && !isRefresh && isMountedRef.current) {
        // Add a small delay to prevent false alerts during rapid loading
        setTimeout(() => {
          if (isMountedRef.current && contacts.length === 0) {
            Alert.alert(
              'No Contacts Found',
              'No contacts were found on your device. Make sure you have contacts saved in your phone\'s contact list.',
              [{ text: 'OK' }]
            );
          }
        }, 1000);
      }

    } catch (error) {
      console.error('❌ Error loading contacts:', error);
      Alert.alert(
        'Error Loading Contacts',
        'Failed to load contacts. Please check your permissions and try again.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Retry', onPress: () => loadContacts() }
        ]
      );
      // Set empty arrays on error
      setContacts([]);
      setFilteredContacts([]);
    } finally {
      clearTimeout(safetyTimeout);
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  }, []);

  // MEMORY OPTIMIZATION: Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      // Clean up all online status listeners
      onlineStatusListeners.current.forEach((unsubscribe) => {
        unsubscribe();
      });
      onlineStatusListeners.current.clear();
      console.log("🧹 Contacts screen cleanup complete");
    };
  }, []);

  useEffect(() => {
    loadContacts();
  }, [loadContacts]);

  // Re-filter contacts when contacts array changes or search text changes
  useEffect(() => {
    // Ensure contacts is a valid array before filtering
    if (!Array.isArray(contacts)) {
      setFilteredContacts([]);
      return;
    }

    if (searchText.trim() === "") {
      setFilteredContacts(contacts);
    } else {
      const searchTerm = searchText.toLowerCase().trim();
      const filtered = contacts.filter((contact) => {
        // Add null/undefined checks for all contact properties
        if (!contact) return false;

        try {
          // Safe string operations with proper null checks
          const nameMatch = contact.name && typeof contact.name === 'string' ?
            contact.name.toLowerCase().includes(searchTerm) : false;
          const phoneMatch = contact.phoneNumber && typeof contact.phoneNumber === 'string' ?
            contact.phoneNumber.toLowerCase().includes(searchTerm) : false;
          const emailMatch = contact.primaryEmail && typeof contact.primaryEmail === 'string' ?
            contact.primaryEmail.toLowerCase().includes(searchTerm) : false;

          return nameMatch || phoneMatch || emailMatch;
        } catch (error) {
          console.error('Error filtering contact in contacts.tsx:', contact, error);
          return false;
        }
      });
      setFilteredContacts(filtered);
    }
  }, [contacts, searchText]);

  // REAL-TIME: Monitor online status for IraChat users
  useEffect(() => {
    const setupOnlineStatusListeners = () => {
      // Clean up existing listeners
      onlineStatusListeners.current.forEach((unsubscribe) => {
        unsubscribe();
      });
      onlineStatusListeners.current.clear();

      // Set up listeners for IraChat users with safety checks
      const iraChatUsers = Array.isArray(contacts) ?
        contacts.filter(contact => contact && contact.isIraChatUser && contact.userId) : [];

      console.log(`🔄 Setting up online status listeners for ${iraChatUsers.length} IraChat users`);

      iraChatUsers.forEach((contact) => {
        if (contact.userId) {
          const unsubscribe = onlineStatusService.listenToUserStatus(
            contact.userId,
            (onlineUser) => {
              if (!isMountedRef.current) return;

              if (onlineUser) {
                console.log(`📡 Online status update: ${contact.name} - ${onlineUser.isOnline ? 'ONLINE' : 'OFFLINE'}`);

                // FIXED: Only update the online status map to prevent infinite loop
                // DO NOT update contacts or filteredContacts arrays here
                setOnlineStatusMap(prev => {
                  const newMap = new Map(prev);
                  newMap.set(contact.userId!, onlineUser.isOnline);
                  return newMap;
                });
              }
            }
          );

          onlineStatusListeners.current.set(contact.userId, unsubscribe);
        }
      });
    };

    if (contacts.length > 0) {
      setupOnlineStatusListeners();
    }

    return () => {
      // Cleanup listeners when effect re-runs
      onlineStatusListeners.current.forEach((unsubscribe) => {
        unsubscribe();
      });
      onlineStatusListeners.current.clear();
    };
  }, [contacts.length]); // FIXED: Only depend on contacts.length to prevent infinite loop

  // Auto-expand search when there's a query
  useEffect(() => {
    if (searchText && !isSearchExpanded) {
      setIsSearchExpanded(true);
    }
  }, [searchText, isSearchExpanded]);

  const formatLastSeen = (lastSeen: Date | string | undefined): string => {
    try {
      if (!lastSeen) {
        return "Unknown";
      }

      let date: Date;
      if (lastSeen instanceof Date) {
        date = lastSeen;
      } else if (typeof lastSeen === "string") {
        date = new Date(lastSeen);
      } else {
        return "Unknown";
      }

      // Validate the date
      if (isNaN(date.getTime())) {
        return "Unknown";
      }

      const now = new Date();
      const diff = now.getTime() - date.getTime();
      const minutes = Math.floor(diff / 60000);
      const hours = Math.floor(diff / 3600000);
      const days = Math.floor(diff / 86400000);

      if (minutes < 1) return "just now";
      if (minutes < 60) return `${minutes}m ago`;
      if (hours < 24) return `${hours}h ago`;
      return `${days}d ago`;
    } catch (error) {
      console.error("Error formatting last seen:", error);
      return "Unknown";
    }
  };

  // FIXED: Immediate search with proper filtering and debugging
  const handleSearch = useCallback((text: string) => {
    console.log(`🔍 Search input: "${text}"`);
    console.log(`📊 Total contacts available: ${contacts?.length || 0}`);
    console.log(`📊 Contacts array type: ${typeof contacts}, isArray: ${Array.isArray(contacts)}`);

    setSearchText(text);

    // Clear existing timeout if any
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Ensure contacts is a valid array before filtering
    if (!Array.isArray(contacts)) {
      console.warn('⚠️ Contacts array is invalid during search in contacts.tsx');
      setFilteredContacts([]);
      return;
    }

    // Immediate search for better UX
    if (text.trim() === "") {
      console.log(`🔍 Empty search - showing all ${contacts.length} contacts`);
      setFilteredContacts(contacts);
    } else {
      const searchTerm = text.toLowerCase().trim();
      console.log(`🔍 Searching for: "${searchTerm}"`);

      let filtered: any[] = [];

      try {
        if (!contacts || !Array.isArray(contacts)) {
          console.error('❌ Contacts is not a valid array:', contacts);
          filtered = [];
        } else {
          filtered = contacts.filter((contact) => {
            // Add null/undefined checks for all contact properties
            if (!contact) return false;

            try {
              // Safe string operations with proper null checks
              let nameMatch = false;
              let phoneMatch = false;
              let emailMatch = false;

          try {
            if (contact.name && typeof contact.name === 'string' && typeof contact.name.toLowerCase === 'function') {
              const lowerName = contact.name.toLowerCase();
              if (typeof lowerName === 'string' && typeof lowerName.includes === 'function') {
                nameMatch = lowerName.includes(searchTerm);
              }
            }
          } catch (nameError) {
            console.error('❌ Error in nameMatch:', nameError, 'contact.name:', contact.name, 'searchTerm:', searchTerm);
          }

          try {
            if (contact.phoneNumber && typeof contact.phoneNumber === 'string' && typeof contact.phoneNumber.toLowerCase === 'function') {
              const lowerPhone = contact.phoneNumber.toLowerCase();
              if (typeof lowerPhone === 'string' && typeof lowerPhone.includes === 'function') {
                phoneMatch = lowerPhone.includes(searchTerm);
              }
            }
          } catch (phoneError) {
            console.error('❌ Error in phoneMatch:', phoneError, 'contact.phoneNumber:', contact.phoneNumber, 'searchTerm:', searchTerm);
          }

          try {
            if (contact.primaryEmail && typeof contact.primaryEmail === 'string' && typeof contact.primaryEmail.toLowerCase === 'function') {
              const lowerEmail = contact.primaryEmail.toLowerCase();
              if (typeof lowerEmail === 'string' && typeof lowerEmail.includes === 'function') {
                emailMatch = lowerEmail.includes(searchTerm);
              }
            }
          } catch (emailError) {
            console.error('❌ Error in emailMatch:', emailError, 'contact.primaryEmail:', contact.primaryEmail, 'searchTerm:', searchTerm);
          }

          const isMatch = nameMatch || phoneMatch || emailMatch;

            return isMatch;
          } catch (error) {
            console.error('❌ Error filtering contact in handleSearch:', contact, error);
            return false;
          }
        });
        }
      } catch (filterError) {
        console.error('❌ Error during filtering operation:', filterError);
        filtered = [];
      }

      // Debug first few matches after filtering is complete
      if (filtered && Array.isArray(filtered) && filtered.length > 0) {
        try {
          console.log(`✅ First few matches:`, filtered.slice(0, 3).map(c => c && c.name ? c.name : 'Unknown'));
        } catch (debugError) {
          console.error('❌ Error in debug logging:', debugError, 'filtered:', filtered);
        }
      }

      console.log(`🔍 Search "${text}" found ${filtered ? filtered.length : 0} results out of ${contacts ? contacts.length : 0} total`);
      setFilteredContacts(filtered || []);
    }
  }, [contacts]);

  // FIXED: Clear search function
  const clearSearch = useCallback(() => {
    console.log(`🔍 Clearing search input`);
    setSearchText("");
    setFilteredContacts(contacts);

    // Clear any pending timeouts
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
  }, [contacts]);

  const openChat = (contact: Contact) => {
    try {
      console.log(`👆 Contact tapped: ${contact.name} (IraChat user: ${contact.isIraChatUser})`);

      // Validate required contact data
      if (!contact.id || !contact.name) {
        console.log(`❌ Invalid contact data for ${contact.name}`);
        return;
      }

      // Only allow chat with IraChat users
      if (!contact.isIraChatUser) {
        console.log(`📤 Non-IraChat user tapped - opening invite for ${contact.name}`);
        inviteContact(contact);
        return;
      }

      // Use navigation service for seamless routing (offline-first)
      const chatId = contact.userId || contact.id;
      console.log(`💬 Opening chat with IraChat user: ${contact.name} (ID: ${chatId})`);
      console.log(`🔄 Chat will work offline - no internet connection required`);

      // Navigate directly with proper parameters - the chat screen will handle offline creation
      try {
        console.log(`🔄 Navigating to chat with params:`, {
          chatId,
          partnerName: contact.name,
          partnerAvatar: contact.avatar || '',
          isOnline: contact.isOnline?.toString() || 'false',
        });

        // Use router.push for better navigation with parameters
        router.push({
          pathname: `/chat/${chatId}`,
          params: {
            partnerName: contact.name,
            partnerAvatar: contact.avatar || '',
            isOnline: contact.isOnline?.toString() || 'false',
          }
        });

        console.log(`✅ Navigation initiated successfully`);
      } catch (routerError) {
        console.warn('❌ Router navigation failed, falling back to navigationService:', routerError);
        // Fallback to navigation service
        try {
          navigationService.openChat(chatId, false);
          console.log(`✅ Fallback navigation successful`);
        } catch (fallbackError) {
          console.error('❌ Both navigation methods failed:', fallbackError);
          Alert.alert(
            "Navigation Error",
            `Unable to open chat with ${contact.name}. Please try again.`,
            [{ text: "OK" }]
          );
        }
      }
    } catch (error) {
      console.error(`❌ Error opening chat with ${contact.name}:`, error);

      // Fallback: Show user-friendly message instead of failing silently
      Alert.alert(
        "Chat Error",
        `Unable to open chat with ${contact.name}. Please try again.`,
        [{ text: "OK" }]
      );
    }
  };

  // FIXED: Direct native share functionality
  const inviteContact = useCallback(async (contact: Contact) => {
    try {
      const message = getInviteMessage(contact.name);
      const title = `Join me on IraChat!`;

      const shareOptions = {
        title,
        message,
        url: 'https://irachat.app', // Your app's download page
      };

      console.log(`📤 Opening native share for ${contact.name}`);

      const result = await Share.share(shareOptions);

      if (result.action === Share.sharedAction) {
        // User shared successfully
        markContactAsInvited(contact.id, 'Share');
        console.log(`✅ Invitation shared for ${contact.name}`);

        Alert.alert(
          "Invitation Sent! 🎉",
          `Invitation for ${contact.name} has been shared successfully!`,
          [{ text: "Great!" }]
        );
      } else if (result.action === Share.dismissedAction) {
        // User dismissed the share dialog
        console.log(`❌ Share dismissed for ${contact.name}`);
      }
    } catch (error: any) {
      console.error("❌ Error sharing invitation:", error);
      Alert.alert(
        "Share Error",
        `Unable to share invitation: ${error?.message || 'Unknown error'}`,
        [{ text: "OK" }]
      );
    }
  }, []);

  // Test basic linking functionality
  const testLinking = useCallback(async () => {
    try {
      console.log(`🔍 DEBUG: Testing basic linking functionality...`);

      // Test basic URL
      const testUrl = 'https://google.com';
      const canOpenWeb = await Linking.canOpenURL(testUrl);
      console.log(`🔍 DEBUG: Can open web URL: ${canOpenWeb}`);

      // Test SMS
      const smsTest = Platform.OS === 'ios' ? 'sms:' : 'sms:';
      const canOpenSMS = await Linking.canOpenURL(smsTest);
      console.log(`🔍 DEBUG: Can open SMS: ${canOpenSMS}`);

      // Test WhatsApp
      const whatsappTest = 'whatsapp://';
      const canOpenWhatsApp = await Linking.canOpenURL(whatsappTest);
      console.log(`🔍 DEBUG: Can open WhatsApp: ${canOpenWhatsApp}`);

      Alert.alert(
        "Linking Test Results",
        `Web: ${canOpenWeb ? '✅' : '❌'}\nSMS: ${canOpenSMS ? '✅' : '❌'}\nWhatsApp: ${canOpenWhatsApp ? '✅' : '❌'}`,
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error("❌ DEBUG: Linking test error:", error);
    }
  }, []);

  // Show invite platform options
  const showInviteOptions = useCallback((contact: Contact) => {
    const options: Array<{text: string, style?: 'cancel' | 'destructive', onPress?: () => void}> = [
      { text: "Cancel", style: "cancel" },
    ];

    // Add test option for debugging
    options.push({
      text: "🔍 Test Linking",
      onPress: () => testLinking()
    });

    // SMS option (always available)
    if (contact.phoneNumber) {
      options.push({
        text: "📱 SMS",
        onPress: () => inviteViaSMS(contact)
      });
    }

    // Email option (if email available)
    if (contact.primaryEmail) {
      options.push({
        text: "📧 Email",
        onPress: () => inviteViaEmail(contact)
      });
    }

    // WhatsApp option (if phone available)
    if (contact.phoneNumber) {
      options.push({
        text: "💬 WhatsApp",
        onPress: () => inviteViaWhatsApp(contact)
      });
    }

    // Telegram option (if phone available)
    if (contact.phoneNumber) {
      options.push({
        text: "✈️ Telegram",
        onPress: () => inviteViaTelegram(contact)
      });
    }

    // Share option (universal)
    options.push({
      text: "📤 Share Link",
      onPress: () => inviteViaShare(contact)
    });

    Alert.alert(
      "Choose Invitation Method",
      `How would you like to invite ${contact.name}?`,
      options
    );
  }, [testLinking]);

  // Generate invite message
  const getInviteMessage = useCallback((contactName: string) => {
    return `Hi ${contactName}! 👋

I'm using IraChat - an amazing messaging app with great features like:
• 🎥 HD Video Calls
• 📞 Crystal Clear Voice Calls
• 💬 Secure Messaging
• 👥 Group Chats
• 📁 File Sharing
• 🔒 End-to-End Encryption

Join me on IraChat! Download it here:

📱 Android: https://play.google.com/store/apps/details?id=com.irachat
🍎 iOS: https://apps.apple.com/app/irachat/id123456789
🌐 Web: https://web.irachat.app

Let's stay connected! 🚀

#IraChat #StayConnected #SecureMessaging`;
  }, []);

  // Invite via SMS - DEBUGGING VERSION
  const inviteViaSMS = useCallback(async (contact: Contact) => {
    try {
      console.log(`🔍 DEBUG: Starting SMS invite for ${contact.name}`);
      console.log(`🔍 DEBUG: Phone number: ${contact.phoneNumber}`);

      if (!contact.phoneNumber) {
        throw new Error("No phone number available");
      }

      const message = getInviteMessage(contact.name);
      const phoneNumber = contact.phoneNumber.replace(/[^\d+]/g, ''); // Clean phone number

      console.log(`🔍 DEBUG: Cleaned phone number: ${phoneNumber}`);
      console.log(`🔍 DEBUG: Platform: ${Platform.OS}`);

      const smsUrl = Platform.OS === 'ios'
        ? `sms:${phoneNumber}&body=${encodeURIComponent(message)}`
        : `sms:${phoneNumber}?body=${encodeURIComponent(message)}`;

      console.log(`🔍 DEBUG: SMS URL: ${smsUrl.substring(0, 100)}...`);

      const canOpen = await Linking.canOpenURL(smsUrl);
      console.log(`🔍 DEBUG: Can open SMS URL: ${canOpen}`);

      if (canOpen) {
        console.log(`🔍 DEBUG: Attempting to open SMS app...`);
        await Linking.openURL(smsUrl);
        console.log(`✅ DEBUG: SMS app opening command sent!`);

        // Give a moment for the app to open, then ask for confirmation
        setTimeout(() => {
          Alert.alert(
            "SMS App",
            `Did the SMS app open with a message for ${contact.name}?\n\nIf yes, please send the message and confirm below.`,
            [
              {
                text: "App didn't open",
                style: "cancel",
                onPress: () => {
                  console.log(`❌ SMS app failed to open for ${contact.name}`);
                  Alert.alert(
                    "SMS Failed",
                    "SMS app didn't open. This might be because:\n• SMS is not available on this device\n• Phone number format issue\n• Device restrictions\n\nTry another method.",
                    [{ text: "OK" }]
                  );
                }
              },
              {
                text: "Message Sent!",
                onPress: () => {
                  markContactAsInvited(contact.id, 'SMS');
                  console.log(`✅ SMS invite confirmed sent to ${contact.name}`);
                  Alert.alert(
                    "Invitation Sent! 🎉",
                    `SMS invitation sent to ${contact.name} successfully!`,
                    [{ text: "Great!" }]
                  );
                }
              }
            ]
          );
        }, 2000); // Give 2 seconds for app to open
      } else {
        console.log(`❌ DEBUG: Cannot open SMS URL - not supported`);
        throw new Error("SMS not supported on this device");
      }
    } catch (error: any) {
      console.error("❌ DEBUG: Error in SMS invite:", error);
      Alert.alert(
        "SMS Error",
        `Unable to open SMS app.\n\nError: ${error?.message || 'Unknown error'}\n\nThis might be because:\n• No SMS app installed\n• Invalid phone number\n• Device restrictions\n\nPlease try another method.`,
        [{ text: "OK" }]
      );
    }
  }, [getInviteMessage]);

  // Invite via Email - FIXED: Only mark as invited when user confirms
  const inviteViaEmail = useCallback(async (contact: Contact) => {
    try {
      const message = getInviteMessage(contact.name);
      const subject = "Join me on IraChat! 🚀";

      const emailUrl = `mailto:${contact.primaryEmail}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(message)}`;

      const canOpen = await Linking.canOpenURL(emailUrl);
      if (canOpen) {
        await Linking.openURL(emailUrl);

        // Ask user to confirm if they actually sent the email
        Alert.alert(
          "Email App Opened",
          `Email app opened with invitation for ${contact.name}.\n\nDid you send the email?`,
          [
            {
              text: "No, Cancel",
              style: "cancel",
              onPress: () => {
                console.log(`❌ Email invite cancelled for ${contact.name}`);
              }
            },
            {
              text: "Yes, Sent!",
              onPress: () => {
                markContactAsInvited(contact.id, 'Email');
                console.log(`✅ Email invite confirmed sent to ${contact.name}`);
                Alert.alert(
                  "Invitation Sent! 🎉",
                  `Email invitation sent to ${contact.name} successfully!`,
                  [{ text: "Great!" }]
                );
              }
            }
          ]
        );
      } else {
        throw new Error("Email not available");
      }
    } catch (error) {
      console.error("Error sending email invite:", error);
      Alert.alert(
        "Email Error",
        "Unable to open email app. Please try another method.",
        [{ text: "OK" }]
      );
    }
  }, [getInviteMessage]);

  // Invite via WhatsApp - DEBUGGING VERSION
  const inviteViaWhatsApp = useCallback(async (contact: Contact) => {
    try {
      console.log(`🔍 DEBUG: Starting WhatsApp invite for ${contact.name}`);
      console.log(`🔍 DEBUG: Phone number: ${contact.phoneNumber}`);

      if (!contact.phoneNumber) {
        throw new Error("No phone number available");
      }

      const message = getInviteMessage(contact.name);
      const phoneNumber = contact.phoneNumber.replace(/[^\d+]/g, ''); // Clean phone number

      console.log(`🔍 DEBUG: Cleaned phone number: ${phoneNumber}`);

      // WhatsApp URL scheme
      const whatsappUrl = `whatsapp://send?phone=${phoneNumber}&text=${encodeURIComponent(message)}`;

      console.log(`🔍 DEBUG: WhatsApp URL: ${whatsappUrl.substring(0, 100)}...`);

      const canOpen = await Linking.canOpenURL(whatsappUrl);
      console.log(`🔍 DEBUG: Can open WhatsApp URL: ${canOpen}`);

      if (canOpen) {
        console.log(`🔍 DEBUG: Attempting to open WhatsApp...`);
        await Linking.openURL(whatsappUrl);
        console.log(`✅ DEBUG: WhatsApp opening command sent!`);

        // Give a moment for the app to open, then ask for confirmation
        setTimeout(() => {
          Alert.alert(
            "WhatsApp",
            `Did WhatsApp open with a message for ${contact.name}?\n\nIf yes, please send the message and confirm below.`,
            [
              {
                text: "App didn't open",
                style: "cancel",
                onPress: () => {
                  console.log(`❌ WhatsApp failed to open for ${contact.name}`);
                  Alert.alert(
                    "WhatsApp Failed",
                    "WhatsApp didn't open. This might be because:\n• WhatsApp is not installed\n• Phone number format issue\n• WhatsApp restrictions\n\nTry installing WhatsApp or use another method.",
                    [{ text: "OK" }]
                  );
                }
              },
              {
                text: "Message Sent!",
                onPress: () => {
                  markContactAsInvited(contact.id, 'WhatsApp');
                  console.log(`✅ WhatsApp invite confirmed sent to ${contact.name}`);
                  Alert.alert(
                    "Invitation Sent! 🎉",
                    `WhatsApp invitation sent to ${contact.name} successfully!`,
                    [{ text: "Great!" }]
                  );
                }
              }
            ]
          );
        }, 2000); // Give 2 seconds for app to open
      } else {
        // Fallback to WhatsApp web or app store
        const fallbackUrl = Platform.OS === 'ios'
          ? 'https://apps.apple.com/app/whatsapp-messenger/id310633997'
          : 'https://play.google.com/store/apps/details?id=com.whatsapp';

        Alert.alert(
          "WhatsApp Not Found",
          "WhatsApp is not installed. Would you like to install it?",
          [
            { text: "Cancel", style: "cancel" },
            {
              text: "Install WhatsApp",
              onPress: () => Linking.openURL(fallbackUrl)
            }
          ]
        );
      }
    } catch (error) {
      console.error("Error sending WhatsApp invite:", error);
      Alert.alert(
        "WhatsApp Error",
        "Unable to open WhatsApp. Please try another method.",
        [{ text: "OK" }]
      );
    }
  }, [getInviteMessage]);

  // Invite via Share (universal) - FIXED: Only mark as invited when user confirms
  const inviteViaShare = useCallback(async (contact: Contact) => {
    try {
      const message = getInviteMessage(contact.name);
      const title = `Join me on IraChat!`;

      const shareOptions = {
        title,
        message,
        url: 'https://irachat.app', // Your app's website or download page
      };

      const result = await Share.share(shareOptions);

      if (result.action === Share.sharedAction) {
        // Ask user to confirm if they actually shared the invitation
        Alert.alert(
          "Share Completed",
          `Did you successfully share the invitation for ${contact.name}?`,
          [
            {
              text: "No, Cancel",
              style: "cancel",
              onPress: () => {
                console.log(`❌ Share invite cancelled for ${contact.name}`);
              }
            },
            {
              text: "Yes, Shared!",
              onPress: () => {
                markContactAsInvited(contact.id, 'Share');
                console.log(`✅ Share invite confirmed sent to ${contact.name}`);
                Alert.alert(
                  "Invitation Sent! 🎉",
                  `Invitation for ${contact.name} has been shared successfully!`,
                  [{ text: "Great!" }]
                );
              }
            }
          ]
        );
      }
    } catch (error) {
      console.error("Error sharing invite:", error);
      Alert.alert(
        "Share Error",
        "Unable to share invitation. Please try another method.",
        [{ text: "OK" }]
      );
    }
  }, [getInviteMessage]);

  // Invite via Telegram
  const inviteViaTelegram = useCallback(async (contact: Contact) => {
    try {
      const message = getInviteMessage(contact.name);
      const phoneNumber = contact.phoneNumber?.replace(/[^\d+]/g, ''); // Clean phone number

      // Telegram URL scheme
      const telegramUrl = `tg://msg?to=${phoneNumber}&text=${encodeURIComponent(message)}`;

      const canOpen = await Linking.canOpenURL(telegramUrl);
      if (canOpen) {
        await Linking.openURL(telegramUrl);

        // Ask user to confirm if they actually sent the Telegram message
        Alert.alert(
          "Telegram Opened",
          `Telegram opened with invitation message for ${contact.name}.\n\nDid you send the message?`,
          [
            {
              text: "No, Cancel",
              style: "cancel",
              onPress: () => {
                console.log(`❌ Telegram invite cancelled for ${contact.name}`);
              }
            },
            {
              text: "Yes, Sent!",
              onPress: () => {
                markContactAsInvited(contact.id, 'Telegram');
                console.log(`✅ Telegram invite confirmed sent to ${contact.name}`);
                Alert.alert(
                  "Invitation Sent! 🎉",
                  `Telegram invitation sent to ${contact.name} successfully!`,
                  [{ text: "Great!" }]
                );
              }
            }
          ]
        );
      } else {
        // Fallback to Telegram web or app store
        const fallbackUrl = Platform.OS === 'ios'
          ? 'https://apps.apple.com/app/telegram-messenger/id686449807'
          : 'https://play.google.com/store/apps/details?id=org.telegram.messenger';

        Alert.alert(
          "Telegram Not Found",
          "Telegram is not installed. Would you like to install it?",
          [
            { text: "Cancel", style: "cancel" },
            {
              text: "Install Telegram",
              onPress: () => Linking.openURL(fallbackUrl)
            }
          ]
        );
      }
    } catch (error) {
      console.error("Error sending Telegram invite:", error);
      Alert.alert(
        "Telegram Error",
        "Unable to open Telegram. Please try another method.",
        [{ text: "OK" }]
      );
    }
  }, [getInviteMessage]);

  // Track successful invitations
  const markContactAsInvited = useCallback((contactId: string, platform: string) => {
    setInvitedContacts(prev => new Set(prev).add(contactId));
    console.log(`✅ Contact ${contactId} marked as invited via ${platform}`);
  }, []);

  // Check if contact has been invited
  const isContactInvited = useCallback((contactId: string) => {
    return invitedContacts.has(contactId);
  }, [invitedContacts]);

  // OPTIMIZED: Render contact with memoization for performance
  const renderContact: ListRenderItem<Contact> = useCallback(({ item: contact }) => {
    // Add safety check for contact
    if (!contact || !contact.id) {
      console.warn('Invalid contact item in renderContact:', contact);
      return null;
    }

    return (
      <TouchableOpacity
        onPress={() => openChat(contact)}
        style={styles.contactItem}
        activeOpacity={0.7}
      >
        {/* Profile Photo or Initials */}
        <View style={styles.avatarContainer}>
          {contact.isIraChatUser && contact.avatar && contact.profilePhotoVisible ? (
            <Image
              source={{ uri: contact.avatar }}
              style={styles.profilePhoto}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.initialsContainer}>
              <Text style={styles.initialsText}>
                {contact.initials}
              </Text>
            </View>
          )}

          {/* Online status indicator for IraChat users */}
          {contact.isIraChatUser && contact.isOnline && (
            <View style={styles.onlineIndicator} />
          )}


        </View>

        {/* Contact Info */}
        <View style={styles.contactInfo}>
          <View style={styles.nameContainer}>
            <Text style={styles.contactName} numberOfLines={1}>
              {contact.name}
            </Text>
            {contact.isIraChatUser && (
              <View style={styles.iraChatLogoContainer}>
                <Image
                  source={require('../assets/images/LOGO.png')}
                  style={styles.iraChatLogo}
                  resizeMode="contain"
                />
              </View>
            )}
          </View>

          {/* Phone Number Display */}
          {contact.phoneNumber && (
            <Text style={styles.contactPhone} numberOfLines={1}>
              {contact.phoneNumber}
            </Text>
          )}

          <Text style={styles.contactStatus} numberOfLines={1}>
            {contact.isIraChatUser ? (
              contact.isOnline ? 'Online' : `Last seen ${formatLastSeen(contact.lastSeen)}`
            ) : isContactInvited(contact.id) ? (
              '✅ Invitation sent • Tap to share again'
            ) : (
              'Not on IraChat • Tap to share invite'
            )}
          </Text>
        </View>

        {/* Action Button */}
        <View style={styles.actionContainer}>
          {contact.isIraChatUser ? (
            <TouchableOpacity
              onPress={() => openChat(contact)}
              style={styles.chatButton}
            >
              <Image
                source={require('../assets/images/LOGO.png')}
                style={styles.chatButtonLogo}
                resizeMode="contain"
              />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              onPress={() => inviteContact(contact)}
              onLongPress={() => showInviteOptions(contact)}
              style={[
                styles.inviteButton,
                isContactInvited(contact.id) && styles.invitedButton
              ]}
            >
              <Ionicons
                name={isContactInvited(contact.id) ? "checkmark-done" : "person-add"}
                size={20}
                color={isContactInvited(contact.id) ? IRACHAT_COLORS.success : IRACHAT_COLORS.primary}
              />
            </TouchableOpacity>
          )}
        </View>
      </TouchableOpacity>
    );
  }, []);

  // OPTIMIZED: Memoized helper functions for FlatList
  const keyExtractor = useCallback((item: Contact) => item.id, []);

  const getItemLayout = useCallback((_data: any, index: number) => ({
    length: 80, // Approximate height of each contact item
    offset: 80 * index,
    index,
  }), []);

  // SKELETON LOADING COMPONENT for better UX
  const SkeletonContact = useCallback(() => (
    <View style={styles.contactItem}>
      <View style={[styles.initialsContainer, { backgroundColor: IRACHAT_COLORS.border }]}>
        <View style={styles.skeletonCircle} />
      </View>
      <View style={styles.contactInfo}>
        <View style={[styles.skeletonLine, { width: '60%', marginBottom: 8 }]} />
        <View style={[styles.skeletonLine, { width: '40%' }]} />
      </View>
      <View style={[styles.skeletonLine, { width: 40, height: 40, borderRadius: 20 }]} />
    </View>
  ), []);

  const renderSkeletonList = useMemo(() => (
    Array.from({ length: 10 }, (_, index) => (
      <SkeletonContact key={`skeleton-${index}`} />
    ))
  ), [SkeletonContact]);

  const onRefresh = useCallback(() => {
    console.log(`🔄 [DEBUG] onRefresh called - triggering loadContacts(true)`);
    loadContacts(true);
  }, [loadContacts]);

  return (
    <ErrorBoundary>
      <ResponsiveContainer style={styles.container} paddingHorizontal={false}>
        <StatusBar style="light" backgroundColor="transparent" translucent={true} />

        {/* Header with LinearGradient - Matching Chat Management */}
        <LinearGradient
          colors={[IRACHAT_COLORS.primary, IRACHAT_COLORS.primaryDark]}
          style={styles.headerGradient}
        >
          <AppHeader
            title={isSearchExpanded ? "" : "Select Contact"}
            showBackButton={true}
            onBackPress={() => navigationService.goBack()}
            style={{ paddingTop: ResponsiveSpacing.md, paddingBottom: ResponsiveSpacing.md }}
            rightComponent={
              <View style={{ flexDirection: 'row', alignItems: 'center', gap: ResponsiveSpacing.xs }}>
                {!isSearchExpanded && (
                  <TouchableOpacity
                    onPress={() => setIsSearchExpanded(true)}
                    style={{
                      backgroundColor: 'rgba(255,255,255,0.2)',
                      borderRadius: BORDER_RADIUS.md,
                      paddingHorizontal: ResponsiveSpacing.sm,
                      paddingVertical: ResponsiveSpacing.xs,
                    }}
                  >
                    <Ionicons name="search-outline" size={20} color="#FFFFFF" />
                  </TouchableOpacity>
                )}
                <TouchableOpacity
                  onPress={() => loadContacts()}
                  style={{
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    borderRadius: BORDER_RADIUS.md,
                    paddingHorizontal: ResponsiveSpacing.sm,
                    paddingVertical: ResponsiveSpacing.xs,
                    marginRight: ResponsiveSpacing.sm,
                    opacity: (loading || refreshing) ? 0.5 : 1,
                  }}
                  disabled={loading || refreshing}
                >
                  <Ionicons name="refresh" size={20} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            }
            centerComponent={
              isSearchExpanded ? (
                <View style={styles.headerSearchContainer}>
                  <Ionicons name="search" size={18} color="#FFFFFF" />
                  <TextInput
                    placeholder="Search contacts..."
                    value={searchText}
                    onChangeText={handleSearch}
                    style={styles.headerSearchInput}
                    placeholderTextColor="rgba(255,255,255,0.7)"
                    autoFocus={true}
                  />
                  <TouchableOpacity
                    onPress={() => {
                      setSearchText('');
                      setIsSearchExpanded(false);
                    }}
                    style={styles.headerSearchClear}
                  >
                    <Ionicons name="close" size={16} color="#FFFFFF" />
                  </TouchableOpacity>
                </View>
              ) : (
                <Text style={styles.headerSubtitle}>
                  {Array.isArray(filteredContacts) ? filteredContacts.filter(c => c && c.isIraChatUser).length : 0} IraChat users • {Array.isArray(filteredContacts) ? filteredContacts.filter(c => c && !c.isIraChatUser).length : 0} to invite
                </Text>
              )
            }
          />
        </LinearGradient>

        {/* OPTIMIZED Contacts List with FlatList and Progressive Loading */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
            <Text style={styles.loadingText}>{loadingMessage}</Text>
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View style={[styles.progressFill, { width: `${loadingProgress}%` }]} />
              </View>
              <Text style={styles.progressText}>{loadingProgress}%</Text>
            </View>
            {/* Show skeleton contacts while loading */}
            <View style={styles.skeletonContainer}>
              {renderSkeletonList}
            </View>
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={Array.isArray(filteredContacts) ? filteredContacts : []}
            renderItem={renderContact}
            keyExtractor={keyExtractor}
            getItemLayout={getItemLayout}
            style={styles.contactsList}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[IRACHAT_COLORS.primary]}
              />
            }
            ListEmptyComponent={
              <View style={styles.emptyContainer}>
                <Ionicons name="people-outline" size={64} color={IRACHAT_COLORS.textMuted} />
                <Text style={styles.emptyTitle}>No contacts found</Text>
                <Text style={styles.emptySubtitle}>
                  {searchText ? 'Try adjusting your search terms' : 'Your contacts will appear here'}
                </Text>
              </View>
            }
            // PERFORMANCE OPTIMIZATIONS for 1400+ contacts
            removeClippedSubviews={true}
            maxToRenderPerBatch={15}
            updateCellsBatchingPeriod={100}
            initialNumToRender={15}
            windowSize={5}
            legacyImplementation={false}
            disableVirtualization={false}
            // Add item separator for better performance
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        )}
      </ResponsiveContainer>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000', // Match chat management background
  },
  headerGradient: {
    paddingTop: DeviceInfo.statusBarHeight + ResponsiveSpacing.md, // Proper status bar respect + padding
    paddingBottom: 0, // Remove bottom padding to eliminate gaps
    paddingHorizontal: 0, // Removed left and right margins
  },
  headerSubtitle: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: TYPOGRAPHY.fontFamily,
    textAlign: 'center',
  },
  headerSearchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: ResponsiveSpacing.sm,
    paddingVertical: ResponsiveSpacing.xs,
    flex: 1,
    maxWidth: 350, // Increased width for better usability
  },
  headerSearchInput: {
    flex: 1,
    color: '#FFFFFF',
    fontSize: 16,
    marginLeft: ResponsiveSpacing.xs,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  headerSearchClear: {
    padding: ResponsiveSpacing.xs,
    marginLeft: ResponsiveSpacing.xs,
  },
  searchContainer: {
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: ResponsiveSpacing.sm,
    paddingVertical: ResponsiveSpacing.xs,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.border,
  },
  searchIcon: {
    marginRight: ResponsiveSpacing.sm,
  },
  searchInput: {
    flex: 1,
    fontSize: ResponsiveTypography.fontSize.md,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  clearButton: {
    padding: ResponsiveSpacing.xs,
  },
  contactsList: {
    flex: 1,
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.md,
    backgroundColor: IRACHAT_COLORS.surface,
    minHeight: 80, // Fixed height for better FlatList performance
  },
  separator: {
    height: 2,
    backgroundColor: IRACHAT_COLORS.primary,
    marginHorizontal: ResponsiveSpacing.md,
  },
  avatarContainer: {
    position: 'relative',
    marginRight: ResponsiveSpacing.md,
  },
  profilePhoto: {
    width: 50,
    height: 50,
    borderRadius: 25,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.primary,
  },
  initialsContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: IRACHAT_COLORS.primaryLight,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.primary,
  },
  initialsText: {
    fontSize: ResponsiveTypography.fontSize.md,
    fontWeight: 'bold',
    color: IRACHAT_COLORS.primary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: IRACHAT_COLORS.success,
    borderWidth: 2,
    borderColor: 'white',
  },
  contactInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  nameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.xs,
  },
  contactName: {
    fontSize: ResponsiveTypography.fontSize.md,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
    flex: 1,
  },
  verifiedIcon: {
    marginLeft: ResponsiveSpacing.xs,
  },
  contactPhone: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
    marginBottom: ResponsiveSpacing.xs,
  },
  contactStatus: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  actionContainer: {
    marginLeft: ResponsiveSpacing.sm,
  },
  chatButton: {
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: 20,
    padding: ResponsiveSpacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inviteButton: {
    backgroundColor: IRACHAT_COLORS.primaryLight,
    borderRadius: 20,
    padding: ResponsiveSpacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.primary,
  },
  invitedButton: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderColor: IRACHAT_COLORS.success,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.xl,
  },
  loadingText: {
    marginTop: ResponsiveSpacing.md,
    fontSize: ResponsiveTypography.fontSize.md,
    color: IRACHAT_COLORS.textMuted,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  progressContainer: {
    width: '80%',
    marginTop: ResponsiveSpacing.md,
    alignItems: 'center',
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: IRACHAT_COLORS.border,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: 2,
  },
  progressText: {
    marginTop: ResponsiveSpacing.xs,
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textMuted,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  skeletonContainer: {
    width: '100%',
    marginTop: ResponsiveSpacing.lg,
  },
  skeletonCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: IRACHAT_COLORS.border,
  },
  skeletonLine: {
    height: 12,
    backgroundColor: IRACHAT_COLORS.border,
    borderRadius: 6,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.xl,
    paddingHorizontal: ResponsiveSpacing.lg,
  },
  emptyTitle: {
    marginTop: ResponsiveSpacing.md,
    fontSize: ResponsiveTypography.fontSize.lg,
    fontWeight: '600',
    color: IRACHAT_COLORS.textMuted,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  emptySubtitle: {
    marginTop: ResponsiveSpacing.sm,
    fontSize: ResponsiveTypography.fontSize.md,
    color: IRACHAT_COLORS.textMuted,
    textAlign: 'center',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  iraChatLogoContainer: {
    marginLeft: ResponsiveSpacing.xs,
    backgroundColor: 'rgba(29, 161, 242, 0.1)', // Light blue background
    borderRadius: 8,
    padding: 2,
  },
  iraChatLogo: {
    width: 14,
    height: 14,
    tintColor: IRACHAT_COLORS.primary,
  },
  chatButtonLogo: {
    width: 20,
    height: 20,
    tintColor: 'white',
  },
});
