@echo off
echo ========================================
echo F: Drive Development Environment Setup
echo ========================================

REM Check if running as Administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Running as Administrator
) else (
    echo ❌ ERROR: This script must be run as Administrator!
    echo Right-click this file and select "Run as administrator"
    pause
    exit /b 1
)

echo.
echo Setting up Java JDK 17...
if exist "F:\IraChat\java17\jdk-17.0.12+7\bin\java.exe" (
    setx JAVA_HOME "F:\IraChat\java17\jdk-17.0.12+7" /M >nul 2>&1
    echo ✓ JAVA_HOME set to F:\IraChat\java17\jdk-17.0.12+7
) else (
    echo ❌ Java JDK 17 not found at F:\IraChat\java17\jdk-17.0.12+7
)

echo.
echo Setting up Android SDK...
if exist "F:\Android\Sdk\platform-tools\adb.exe" (
    setx ANDROID_HOME "F:\Android\Sdk" /M >nul 2>&1
    setx ANDROID_SDK_ROOT "F:\Android\Sdk" /M >nul 2>&1
    echo ✓ ANDROID_HOME set to F:\Android\Sdk
    echo ✓ ANDROID_SDK_ROOT set to F:\Android\Sdk
) else (
    echo ❌ Android SDK not found at F:\Android\Sdk
    echo Please install Android Studio or Android SDK to F:\Android\Sdk
)

echo.
echo Adding tools to System PATH...

REM Get current system PATH
for /f "tokens=2*" %%A in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "systemPath=%%B"

REM Define paths to add
set "javaPath=F:\IraChat\java17\jdk-17.0.12+7\bin"
set "androidPlatformTools=F:\Android\Sdk\platform-tools"
set "androidCmdTools=F:\Android\Sdk\cmdline-tools\latest\bin"
set "androidTools=F:\Android\Sdk\tools"
set "androidToolsBin=F:\Android\Sdk\tools\bin"
set "androidEmulator=F:\Android\Sdk\emulator"

REM Check and add Java to PATH
echo %systemPath% | findstr /C:"%javaPath%" >nul
if %errorLevel% neq 0 (
    if exist "%javaPath%" (
        setx PATH "%systemPath%;%javaPath%" /M >nul 2>&1
        echo ✓ Added Java to PATH
        set "systemPath=%systemPath%;%javaPath%"
    )
) else (
    echo ✓ Java already in PATH
)

REM Check and add Android Platform Tools to PATH
echo %systemPath% | findstr /C:"%androidPlatformTools%" >nul
if %errorLevel% neq 0 (
    if exist "%androidPlatformTools%" (
        setx PATH "%systemPath%;%androidPlatformTools%" /M >nul 2>&1
        echo ✓ Added Android Platform Tools to PATH
        set "systemPath=%systemPath%;%androidPlatformTools%"
    )
) else (
    echo ✓ Android Platform Tools already in PATH
)

REM Check and add Android Command Line Tools to PATH
echo %systemPath% | findstr /C:"%androidCmdTools%" >nul
if %errorLevel% neq 0 (
    if exist "%androidCmdTools%" (
        setx PATH "%systemPath%;%androidCmdTools%" /M >nul 2>&1
        echo ✓ Added Android Command Line Tools to PATH
        set "systemPath=%systemPath%;%androidCmdTools%"
    )
) else (
    echo ✓ Android Command Line Tools already in PATH
)

REM Check and add Android Emulator to PATH
echo %systemPath% | findstr /C:"%androidEmulator%" >nul
if %errorLevel% neq 0 (
    if exist "%androidEmulator%" (
        setx PATH "%systemPath%;%androidEmulator%" /M >nul 2>&1
        echo ✓ Added Android Emulator to PATH
    )
) else (
    echo ✓ Android Emulator already in PATH
)

echo.
echo ========================================
echo Environment Setup Complete!
echo ========================================
echo.
echo ⚠️  IMPORTANT: You must restart ALL terminals and applications
echo    for the environment variables to take effect!
echo.
echo Next steps:
echo 1. Close this window
echo 2. Close ALL Command Prompt/PowerShell windows
echo 3. Open a NEW Command Prompt or PowerShell
echo 4. Navigate to F:\IraChat
echo 5. Run: npx expo start
echo 6. Press 'a' to open Android
echo.
echo Verification commands (after restart):
echo   java -version
echo   adb version
echo   echo %%ANDROID_HOME%%
echo   echo %%JAVA_HOME%%
echo.
pause
