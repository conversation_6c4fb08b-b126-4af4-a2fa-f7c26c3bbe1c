/**
 * Media Upload Modal for IraChat
 * Handles media selection, caption input, and upload
 */

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  Image,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { Video, ResizeMode } from 'expo-av';
import { useTheme } from '../../contexts/ThemeContext';

interface MediaUploadModalProps {
  visible: boolean;
  onClose: () => void;
  onSend: (mediaData: MediaUploadData) => void;
  chatId: string;
  senderId: string;
}

export interface MediaUploadData {
  type: 'image' | 'video' | 'audio' | 'document';
  uri: string;
  caption?: string;
  fileName?: string;
  fileSize?: number;
  duration?: number;
  width?: number;
  height?: number;
}

export const MediaUploadModal: React.FC<MediaUploadModalProps> = ({
  visible,
  onClose,
  onSend,
  chatId,
  senderId,
}) => {
  const { colors } = useTheme();
  const [selectedMedia, setSelectedMedia] = useState<MediaUploadData | null>(null);
  const [caption, setCaption] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const captionInputRef = useRef<TextInput>(null);

  const resetState = () => {
    setSelectedMedia(null);
    setCaption('');
    setIsUploading(false);
  };

  const handleClose = () => {
    if (!isUploading) {
      resetState();
      onClose();
    }
  };

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant permission to access your media library.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const pickImage = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        quality: 0.8,
        exif: false,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedMedia({
          type: 'image',
          uri: asset.uri,
          width: asset.width,
          height: asset.height,
          fileSize: asset.fileSize,
          fileName: asset.fileName || `image_${Date.now()}.jpg`,
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const pickVideo = async () => {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return;

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Videos,
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedMedia({
          type: 'video',
          uri: asset.uri,
          width: asset.width,
          height: asset.height,
          duration: asset.duration || undefined,
          fileSize: asset.fileSize,
          fileName: asset.fileName || `video_${Date.now()}.mp4`,
        });
      }
    } catch (error) {
      console.error('Error picking video:', error);
      Alert.alert('Error', 'Failed to pick video');
    }
  };

  const takePhoto = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Please grant permission to access your camera.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedMedia({
          type: 'image',
          uri: asset.uri,
          width: asset.width,
          height: asset.height,
          fileSize: asset.fileSize,
          fileName: `photo_${Date.now()}.jpg`,
        });
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', 'Failed to take photo');
    }
  };

  const pickDocument = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: '*/*',
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        setSelectedMedia({
          type: 'document',
          uri: asset.uri,
          fileName: asset.name,
          fileSize: asset.size,
        });
      }
    } catch (error) {
      console.error('Error picking document:', error);
      Alert.alert('Error', 'Failed to pick document');
    }
  };

  const handleSend = async () => {
    if (!selectedMedia) return;

    setIsUploading(true);

    try {
      const mediaData: MediaUploadData = {
        ...selectedMedia,
        caption: caption.trim() || undefined,
      };

      await onSend(mediaData);
      resetState();
      onClose();
    } catch (error) {
      console.error('Error sending media:', error);
      Alert.alert('Error', 'Failed to send media');
    } finally {
      setIsUploading(false);
    }
  };

  const renderMediaPreview = () => {
    if (!selectedMedia) return null;

    switch (selectedMedia.type) {
      case 'image':
        return (
          <Image
            source={{ uri: selectedMedia.uri }}
            style={styles.mediaPreview}
            resizeMode="cover"
          />
        );
      case 'video':
        return (
          <Video
            source={{ uri: selectedMedia.uri }}
            style={styles.mediaPreview}
            useNativeControls
            resizeMode={ResizeMode.CONTAIN}
            shouldPlay={false}
          />
        );
      case 'document':
        return (
          <View style={[styles.documentPreview, { backgroundColor: colors.surface }]}>
            <Ionicons name="document" size={48} color={colors.primary} />
            <Text style={[styles.documentName, { color: colors.text }]} numberOfLines={2}>
              {selectedMedia.fileName}
            </Text>
            {selectedMedia.fileSize && (
              <Text style={[styles.documentSize, { color: colors.textSecondary }]}>
                {(selectedMedia.fileSize / 1024 / 1024).toFixed(1)} MB
              </Text>
            )}
          </View>
        );
      default:
        return null;
    }
  };

  const renderMediaOptions = () => (
    <View style={styles.mediaOptions}>
      <TouchableOpacity style={styles.mediaOption} onPress={takePhoto}>
        <View style={[styles.mediaOptionIcon, { backgroundColor: colors.primary }]}>
          <Ionicons name="camera" size={24} color="white" />
        </View>
        <Text style={[styles.mediaOptionText, { color: colors.text }]}>Camera</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.mediaOption} onPress={pickImage}>
        <View style={[styles.mediaOptionIcon, { backgroundColor: colors.primary }]}>
          <Ionicons name="image" size={24} color="white" />
        </View>
        <Text style={[styles.mediaOptionText, { color: colors.text }]}>Photo</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.mediaOption} onPress={pickVideo}>
        <View style={[styles.mediaOptionIcon, { backgroundColor: colors.primary }]}>
          <Ionicons name="videocam" size={24} color="white" />
        </View>
        <Text style={[styles.mediaOptionText, { color: colors.text }]}>Video</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.mediaOption} onPress={pickDocument}>
        <View style={[styles.mediaOptionIcon, { backgroundColor: colors.primary }]}>
          <Ionicons name="document" size={24} color="white" />
        </View>
        <Text style={[styles.mediaOptionText, { color: colors.text }]}>Document</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={handleClose} disabled={isUploading}>
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Send Media
          </Text>
          {selectedMedia && (
            <TouchableOpacity
              onPress={handleSend}
              disabled={isUploading}
              style={[
                styles.sendButton,
                { backgroundColor: colors.primary },
                isUploading && styles.sendButtonDisabled,
              ]}
            >
              {isUploading ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Ionicons name="send" size={20} color="white" />
              )}
            </TouchableOpacity>
          )}
        </View>

        <ScrollView style={styles.content}>
          {selectedMedia ? (
            <View style={styles.previewContainer}>
              {renderMediaPreview()}
              
              {/* Caption input */}
              <View style={styles.captionContainer}>
                <TextInput
                  ref={captionInputRef}
                  style={[
                    styles.captionInput,
                    {
                      backgroundColor: colors.surface,
                      color: colors.text,
                      borderColor: colors.border,
                    },
                  ]}
                  placeholder="Add a caption..."
                  placeholderTextColor={colors.textSecondary}
                  value={caption}
                  onChangeText={setCaption}
                  multiline
                  maxLength={1000}
                  editable={!isUploading}
                />
              </View>

              {/* Remove media button */}
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => setSelectedMedia(null)}
                disabled={isUploading}
              >
                <Text style={[styles.removeButtonText, { color: colors.error }]}>
                  Remove Media
                </Text>
              </TouchableOpacity>
            </View>
          ) : (
            renderMediaOptions()
          )}
        </ScrollView>
      </View>
    </Modal>
  );
};

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
  },
  mediaOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 20,
    justifyContent: 'space-around',
  },
  mediaOption: {
    alignItems: 'center',
    marginVertical: 20,
    width: screenWidth / 4,
  },
  mediaOptionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  mediaOptionText: {
    fontSize: 14,
    fontWeight: '500',
  },
  previewContainer: {
    padding: 16,
  },
  mediaPreview: {
    width: '100%',
    height: 300,
    borderRadius: 12,
    marginBottom: 16,
  },
  documentPreview: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 200,
    borderRadius: 12,
    marginBottom: 16,
  },
  documentName: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    marginTop: 8,
    paddingHorizontal: 16,
  },
  documentSize: {
    fontSize: 14,
    marginTop: 4,
  },
  captionContainer: {
    marginBottom: 16,
  },
  captionInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 12,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  removeButton: {
    alignItems: 'center',
    padding: 12,
  },
  removeButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default MediaUploadModal;
