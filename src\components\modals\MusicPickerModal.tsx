/**
 * Music Picker Modal Component
 * Allows users to search and select music from local device and web
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TextInput,
  ActivityIndicator,
  Image,
  Animated,
  PanResponder,
  TouchableWithoutFeedback,
  Dimensions,
  Keyboard,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { musicService, LocalMusicTrack, WebMusicTrack, MusicSearchResult, DownloadProgress } from '../../services/musicService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Colors
const COLORS = {
  primary: '#87CEEB',
  primaryDark: '#4682B4',
  secondary: '#1E90FF',
  background: '#2A2A2A',
  surface: '#1A1A1A',
  text: '#FFFFFF',
  textSecondary: '#B0B0B0',
  textMuted: '#808080',
};

interface MusicPickerModalProps {
  visible: boolean;
  onClose: () => void;
  onMusicSelect: (track: LocalMusicTrack | WebMusicTrack) => void;
}

export const MusicPickerModal: React.FC<MusicPickerModalProps> = ({
  visible,
  onClose,
  onMusicSelect,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<MusicSearchResult>({
    localTracks: [],
    webTracks: [],
    hasMore: false,
  });
  const [localMusic, setLocalMusic] = useState<LocalMusicTrack[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'local' | 'search'>('local');
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [playingTrackId, setPlayingTrackId] = useState<string | null>(null);
  const [playingStates, setPlayingStates] = useState<Record<string, boolean>>({});
  const [downloadProgress, setDownloadProgress] = useState<Record<string, DownloadProgress>>({});
  const [finishedTracks, setFinishedTracks] = useState<Record<string, boolean>>({});
  const [filteredLocalMusic, setFilteredLocalMusic] = useState<LocalMusicTrack[]>([]);
  const [localSearchQuery, setLocalSearchQuery] = useState('');

  // Animation and gesture handling
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT * 0.3)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.9)).current;

  // Gesture handling for modal
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Disable pan gestures when keyboard is visible
        if (isKeyboardVisible) return false;
        return Math.abs(gestureState.dy) > 10;
      },
      onPanResponderMove: (_, gestureState) => {
        const newTranslateY = Math.max(0, gestureState.dy);
        translateY.setValue(newTranslateY);

        // Scale down slightly as user drags
        const scaleValue = Math.max(0.85, 1 - (gestureState.dy / SCREEN_HEIGHT) * 0.15);
        scale.setValue(scaleValue);
      },
      onPanResponderRelease: (_, gestureState) => {
        const { dy, vy } = gestureState;
        
        if (dy > SCREEN_HEIGHT * 0.2 || vy > 0.5) {
          closeModal();
        } else {
          Animated.parallel([
            Animated.spring(translateY, {
              toValue: 0,
              useNativeDriver: true,
            }),
            Animated.spring(scale, {
              toValue: 1,
              useNativeDriver: true,
            }),
          ]).start();
        }
      },
    })
  ).current;

  // Animation functions
  const openModal = () => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(translateY, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.spring(scale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const closeModal = () => {
    // Stop any playing music when closing modal
    musicService.stopCurrentTrack();
    setPlayingTrackId(null);
    setPlayingStates({});

    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: SCREEN_HEIGHT * 0.3,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  // Keyboard event listeners - only track state, don't move modal
  useEffect(() => {
    const keyboardWillShow = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      (event) => {
        setKeyboardHeight(event.endCoordinates.height);
        setIsKeyboardVisible(true);
      }
    );

    const keyboardWillHide = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      (event) => {
        setKeyboardHeight(0);
        setIsKeyboardVisible(false);
      }
    );

    return () => {
      keyboardWillShow.remove();
      keyboardWillHide.remove();
    };
  }, []);

  // Listen for track completion
  useEffect(() => {
    const checkTrackCompletion = setInterval(async () => {
      if (playingTrackId) {
        const isFinished = await musicService.isTrackFinished(playingTrackId);
        if (isFinished) {
          console.log('🏁 Track finished:', playingTrackId);
          setFinishedTracks(prev => ({ ...prev, [playingTrackId]: true }));
          setPlayingStates(prev => ({ ...prev, [playingTrackId]: false }));
          setPlayingTrackId(null);
        }
      }
    }, 1000); // Check every second

    return () => clearInterval(checkTrackCompletion);
  }, [playingTrackId]);

  // Load local music on modal open
  useEffect(() => {
    if (visible) {
      openModal();
      loadLocalMusic();
    } else {
      translateY.setValue(SCREEN_HEIGHT * 0.3);
      opacity.setValue(0);
      scale.setValue(0.9);
      // Reset keyboard state when modal closes
      setKeyboardHeight(0);
      setIsKeyboardVisible(false);
      // Reset track states
      setFinishedTracks({});
      setPlayingStates({});
      setPlayingTrackId(null);
    }
  }, [visible]);

  const loadLocalMusic = async () => {
    setLoading(true);
    try {
      const music = await musicService.getLocalMusic(500); // Load more music files
      setLocalMusic(music);
      setFilteredLocalMusic(music); // Initialize filtered list
      console.log(`🎵 Loaded ${music.length} local music tracks`);
    } catch (error) {
      console.error('❌ Error loading local music:', error);
    } finally {
      setLoading(false);
    }
  };

  // Filter local music as user types
  const handleLocalSearch = (query: string) => {
    setLocalSearchQuery(query);

    if (query.trim() === '') {
      setFilteredLocalMusic(localMusic);
      return;
    }

    const searchTerms = query.toLowerCase().trim().split(/\s+/);
    const filtered = localMusic.filter(track => {
      const searchText = `${track.title} ${track.artist || ''} ${track.album || ''} ${track.filename}`.toLowerCase();

      return searchTerms.every(term => {
        // Direct text match
        if (searchText.includes(term)) return true;

        // File type search
        const fileExtension = track.filename.split('.').pop()?.toLowerCase() || '';
        if (fileExtension === term) return true;

        // Artist-specific search
        if (term.startsWith('by:') && track.artist) {
          const artistQuery = term.substring(3);
          return track.artist.toLowerCase().includes(artistQuery);
        }

        // Duration-based search
        if (term === 'long' && track.duration > 300) return true;
        if (term === 'short' && track.duration < 120) return true;

        return false;
      });
    });

    setFilteredLocalMusic(filtered);
  };

  const handleSearch = async (query: string) => {
    // Update the search query state first
    setSearchQuery(query);

    if (!query.trim()) {
      setSearchResults({ localTracks: [], webTracks: [], hasMore: false });
      return;
    }

    setSearchLoading(true);
    try {
      // Search ONLY web music - no local results mixed in
      const webResults = await musicService.searchWebMusicOnly(query.trim(), 20);
      setSearchResults({
        localTracks: [], // No local tracks in web search
        webTracks: webResults,
        hasMore: false
      });
      console.log(`🌐 Web search found ${webResults.length} results for: "${query.trim()}"`);
    } catch (error) {
      console.error('❌ Error searching web music:', error);
      setSearchResults({ localTracks: [], webTracks: [], hasMore: false });
    } finally {
      setSearchLoading(false);
    }
  };

  const handleMusicSelect = (track: LocalMusicTrack | WebMusicTrack) => {
    // Stop any playing music before selecting
    musicService.stopCurrentTrack();

    // Use setTimeout to avoid useInsertionEffect scheduling updates error
    setTimeout(() => {
      setPlayingTrackId(null);
      setPlayingStates({});
      onMusicSelect(track);
      closeModal();
    }, 0);
  };

  const handlePlayPause = async (track: LocalMusicTrack | WebMusicTrack) => {
    try {
      // Check if track has finished playing
      const isFinished = await musicService.isTrackFinished(track.id);

      if (isFinished || finishedTracks[track.id]) {
        // Replay from beginning
        console.log('🔄 Replaying track from beginning:', track.title);
        const result = await musicService.replayTrack(track);

        if (result.error) {
          console.error('❌ Replay error:', result.error);
          return;
        }

        // Clear finished state and update playing state
        setFinishedTracks(prev => ({ ...prev, [track.id]: false }));
        setPlayingTrackId(track.id);
        setPlayingStates({ [track.id]: true });
        return;
      }

      // Handle download progress for web tracks
      const onDownloadProgress = 'previewUrl' in track ? (progress: DownloadProgress) => {
        setDownloadProgress(prev => ({
          ...prev,
          [track.id]: progress
        }));

        // When download completes, refresh local music to show the new track
        if (progress.isComplete) {
          console.log('🔄 Download completed, refreshing local music cache');
          musicService.refreshLocalMusicCache();
          // Reload local music to show the newly downloaded track
          setTimeout(() => {
            loadLocalMusic();
          }, 1000);
        }
      } : undefined;

      const result = await musicService.playPauseTrack(track, onDownloadProgress);

      if (result.error) {
        console.error('❌ Playback error:', result.error);
        return;
      }

      // Update playing states
      if (result.isPlaying) {
        setPlayingTrackId(track.id);
        setPlayingStates({ [track.id]: true }); // Only one track can play at a time
        setFinishedTracks(prev => ({ ...prev, [track.id]: false })); // Clear finished state
      } else {
        setPlayingTrackId(null);
        setPlayingStates(prev => ({ ...prev, [track.id]: false }));
      }
    } catch (error) {
      console.error('❌ Error handling play/pause:', error);
    }
  };

  const renderLocalMusicItem = ({ item }: { item: LocalMusicTrack }) => {
    const isPlaying = playingTrackId === item.id && playingStates[item.id];
    const isFinished = finishedTracks[item.id];

    // Determine the correct icon
    const getPlayIcon = () => {
      if (isFinished) return "refresh"; // Replay icon when finished
      if (isPlaying) return "pause";   // Pause icon when playing
      return "play";                   // Play icon when stopped/paused
    };

    return (
      <View style={styles.musicItem}>
        <View style={styles.musicIcon}>
          <Ionicons name="musical-notes" size={24} color={COLORS.primary} />
          {/* Downloaded indicator for web music that's now local */}
          {item.album === 'Downloaded Music' && (
            <View style={styles.downloadedBadge}>
              <Ionicons name="cloud-download" size={12} color={COLORS.text} />
            </View>
          )}
        </View>
        <TouchableOpacity
          style={styles.musicInfo}
          onPress={() => handleMusicSelect(item)}
        >
          <Text style={styles.musicTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={styles.musicArtist} numberOfLines={1}>
            {item.artist || 'Unknown Artist'}
          </Text>
          <Text style={styles.musicDuration}>
            {musicService.formatDuration(item.duration)}
          </Text>
          {item.album === 'Downloaded Music' && (
            <Text style={styles.downloadedLabel}>
              Downloaded
            </Text>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.playButton,
            isFinished && styles.replayButton
          ]}
          onPress={() => handlePlayPause(item)}
        >
          <Ionicons
            name={getPlayIcon()}
            size={20}
            color={isFinished ? COLORS.background : COLORS.primary}
          />
        </TouchableOpacity>
      </View>
    );
  };

  const renderWebMusicItem = ({ item }: { item: WebMusicTrack }) => {
    const isPlaying = playingTrackId === item.id && playingStates[item.id];
    const isFinished = finishedTracks[item.id];
    const progress = downloadProgress[item.id];
    const isDownloading = progress && !progress.isComplete && !progress.error;
    const downloadComplete = progress?.isComplete;

    // Determine the correct icon
    const getPlayIcon = () => {
      if (isFinished) return "refresh"; // Replay icon when finished
      if (isPlaying) return "pause";   // Pause icon when playing
      return "play";                   // Play icon when stopped/paused
    };

    return (
      <View style={styles.musicItem}>
        <View style={styles.musicThumbnail}>
          {item.thumbnailUrl ? (
            <Image source={{ uri: item.thumbnailUrl }} style={styles.thumbnailImage} />
          ) : (
            <View style={styles.musicIcon}>
              <Ionicons name="musical-notes" size={24} color={COLORS.primary} />
            </View>
          )}

          {/* Download Progress Overlay */}
          {isDownloading && (
            <View style={styles.downloadOverlay}>
              <View style={styles.progressContainer}>
                <View style={[styles.progressBar, { width: `${(progress.progress * 100)}%` }]} />
              </View>
              <Text style={styles.progressText}>
                {Math.round(progress.progress * 100)}%
              </Text>
            </View>
          )}

          {/* Download Complete Indicator */}
          {downloadComplete && (
            <View style={styles.downloadCompleteOverlay}>
              <Ionicons name="checkmark-circle" size={16} color={COLORS.primary} />
            </View>
          )}
        </View>

        <TouchableOpacity
          style={styles.musicInfo}
          onPress={() => handleMusicSelect(item)}
        >
          <Text style={styles.musicTitle} numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={styles.musicArtist} numberOfLines={1}>
            {item.artist}
          </Text>
          <View style={styles.musicMeta}>
            <Text style={styles.musicDuration}>
              {musicService.formatDuration(item.duration)}
            </Text>
            <Text style={styles.musicSource}>
              {item.source.toUpperCase()}
            </Text>
          </View>

          {/* Download Status Text */}
          {isDownloading && (
            <Text style={styles.downloadStatusText}>
              Downloading full song... {Math.round((progress.downloadedBytes || 0) / 1024)}KB
            </Text>
          )}
          {downloadComplete && (
            <Text style={styles.downloadStatusText}>
              Full song downloaded ✓ - Added to local music
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.playButton,
            isDownloading && styles.playButtonDownloading,
            isFinished && styles.replayButton
          ]}
          onPress={() => handlePlayPause(item)}
          disabled={isDownloading && progress.progress < 0.1} // Disable until some progress
        >
          {isDownloading && progress.progress < 0.1 ? (
            <ActivityIndicator size="small" color={COLORS.primary} />
          ) : (
            <Ionicons
              name={getPlayIcon()}
              size={20}
              color={isFinished ? COLORS.background : COLORS.primary}
            />
          )}
        </TouchableOpacity>
      </View>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={closeModal}
    >
      <TouchableWithoutFeedback onPress={closeModal}>
        <Animated.View style={[styles.overlay, { opacity }]}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <Animated.View
              style={[
                styles.modalContainer,
                {
                  transform: [{ translateY }, { scale }],
                  // Adjust height when keyboard is visible
                  height: isKeyboardVisible
                    ? SCREEN_HEIGHT - keyboardHeight - 50 // Leave space for keyboard + padding
                    : SCREEN_HEIGHT * 0.85,
                }
              ]}
              {...panResponder.panHandlers}
            >
              <SafeAreaView style={styles.container}>
                {/* Drag Handle */}
                <View style={styles.dragHandle} />
                
                {/* Header */}
                <View style={styles.header}>
                  <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
                    <Ionicons name="close" size={24} color={COLORS.text} />
                  </TouchableOpacity>
                  <Text style={styles.title}>Select Music</Text>
                  <View style={styles.placeholder} />
                </View>

                {/* Tabs */}
                <View style={styles.tabContainer}>
                  <TouchableOpacity
                    style={[styles.tab, activeTab === 'local' && styles.activeTab]}
                    onPress={() => setActiveTab('local')}
                  >
                    <Text style={[styles.tabText, activeTab === 'local' && styles.activeTabText]}>
                      Local Music ({localMusic.length})
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.tab, activeTab === 'search' && styles.activeTab]}
                    onPress={() => setActiveTab('search')}
                  >
                    <Text style={[styles.tabText, activeTab === 'search' && styles.activeTabText]}>
                      Search Web
                    </Text>
                  </TouchableOpacity>
                </View>

                {/* Content */}
                <View style={styles.content}>
                  {activeTab === 'local' ? (
                    <View style={styles.localTabContainer}>
                      {/* Local Search Bar */}
                      <View style={styles.searchContainer}>
                        <View style={styles.searchBar}>
                          <Ionicons name="search" size={20} color={COLORS.textMuted} />
                          <TextInput
                            style={styles.searchInput}
                            placeholder="Search local music..."
                            placeholderTextColor={COLORS.textMuted}
                            value={localSearchQuery}
                            onChangeText={handleLocalSearch}
                          />
                          {localSearchQuery.length > 0 && (
                            <TouchableOpacity onPress={() => {
                              setLocalSearchQuery('');
                              handleLocalSearch('');
                            }}>
                              <Ionicons name="close-circle" size={20} color={COLORS.textMuted} />
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>

                      {loading ? (
                        <View style={styles.loadingContainer}>
                          <ActivityIndicator size="large" color={COLORS.primary} />
                          <Text style={styles.loadingText}>Loading local music...</Text>
                        </View>
                      ) : filteredLocalMusic.length === 0 && localSearchQuery.length > 0 ? (
                        <View style={styles.emptyContainer}>
                          <Ionicons name="musical-notes-outline" size={64} color={COLORS.textMuted} />
                          <Text style={styles.emptyText}>No local music found</Text>
                          <Text style={styles.searchHelpText}>
                            Try: "song name", "by:artist", "mp3", "long", "short"
                          </Text>
                        </View>
                      ) : (
                        <FlatList
                          data={filteredLocalMusic}
                          renderItem={renderLocalMusicItem}
                          keyExtractor={(item) => item.id}
                          showsVerticalScrollIndicator={false}
                          contentContainerStyle={styles.listContainer}
                        />
                      )}
                    </View>
                  ) : (
                    <View style={styles.searchTabContainer}>
                      {/* Web Search Bar */}
                      <View style={styles.searchContainer}>
                        <View style={styles.searchBar}>
                          <Ionicons name="search" size={20} color={COLORS.textMuted} />
                          <TextInput
                            style={styles.searchInput}
                            placeholder="Search web music..."
                            placeholderTextColor={COLORS.textMuted}
                            value={searchQuery}
                            onChangeText={handleSearch}
                          />
                          {searchQuery.length > 0 && (
                            <TouchableOpacity onPress={() => {
                              setSearchQuery('');
                              setSearchResults({ localTracks: [], webTracks: [], hasMore: false });
                            }}>
                              <Ionicons name="close-circle" size={20} color={COLORS.textMuted} />
                            </TouchableOpacity>
                          )}
                        </View>
                      </View>

                      {searchLoading ? (
                        <View style={styles.loadingContainer}>
                          <ActivityIndicator size="large" color={COLORS.primary} />
                          <Text style={styles.loadingText}>Searching web music...</Text>
                        </View>
                      ) : searchQuery.trim() === '' ? (
                        <View style={styles.emptyContainer}>
                          <Ionicons name="search" size={64} color={COLORS.textMuted} />
                          <Text style={styles.emptyText}>Search for music on the web</Text>
                        </View>
                      ) : searchResults.webTracks.length === 0 ? (
                        <View style={styles.emptyContainer}>
                          <Ionicons name="musical-notes-outline" size={64} color={COLORS.textMuted} />
                          <Text style={styles.emptyText}>No web music found</Text>
                          <Text style={styles.searchHelpText}>
                            Try different keywords or check your internet connection
                          </Text>
                        </View>
                      ) : (
                        <FlatList
                          data={searchResults.webTracks}
                          renderItem={renderWebMusicItem}
                          keyExtractor={(item) => item.id}
                          showsVerticalScrollIndicator={false}
                          contentContainerStyle={styles.listContainer}
                        />
                      )}
                    </View>
                  )}
                </View>
              </SafeAreaView>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
    height: SCREEN_HEIGHT * 0.85,
    maxHeight: SCREEN_HEIGHT * 0.85,
    minHeight: SCREEN_HEIGHT * 0.6, // Ensure minimum height when keyboard is visible
  },
  container: {
    flex: 1,
  },
  dragHandle: {
    width: 40,
    height: 4,
    backgroundColor: COLORS.textMuted,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  placeholder: {
    width: 40,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: COLORS.text,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: COLORS.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.textSecondary,
  },
  activeTabText: {
    color: COLORS.primary,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.textSecondary,
    fontSize: 16,
    marginTop: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyText: {
    color: COLORS.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  searchHelpText: {
    color: COLORS.textMuted,
    fontSize: 12,
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 16,
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  musicItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surface,
  },
  playButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 12,
  },
  playButtonDownloading: {
    backgroundColor: COLORS.primaryDark,
  },
  downloadOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 4,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  progressContainer: {
    height: 3,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 1.5,
    marginBottom: 2,
  },
  progressBar: {
    height: '100%',
    backgroundColor: COLORS.primary,
    borderRadius: 1.5,
  },
  progressText: {
    fontSize: 8,
    color: COLORS.text,
    textAlign: 'center',
  },
  downloadCompleteOverlay: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 10,
    padding: 2,
  },
  downloadStatusText: {
    fontSize: 10,
    color: COLORS.primary,
    marginTop: 2,
  },
  replayButton: {
    backgroundColor: COLORS.primary,
    borderWidth: 2,
    borderColor: COLORS.text,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  downloadedBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    padding: 2,
  },
  downloadedLabel: {
    fontSize: 9,
    color: COLORS.primary,
    fontWeight: '600',
    marginTop: 2,
  },
  localTabContainer: {
    flex: 1,
  },
  searchTabContainer: {
    flex: 1,
  },
  musicIcon: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  musicThumbnail: {
    width: 48,
    height: 48,
    borderRadius: 8,
    marginRight: 12,
    overflow: 'hidden',
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  musicInfo: {
    flex: 1,
  },
  musicTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 2,
  },
  musicArtist: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 2,
  },
  musicDuration: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  musicMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  musicSource: {
    fontSize: 10,
    color: COLORS.primary,
    fontWeight: '600',
    backgroundColor: COLORS.surface,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
});
