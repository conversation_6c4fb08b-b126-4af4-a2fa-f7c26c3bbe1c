/**
 * 🧪 OFFLINE FUNCTIONALITY TEST SCREEN
 * Test route for demonstrating offline message queuing and sync
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { OfflineDemo } from '../src/components/OfflineDemo';

export default function OfflineTestScreen() {
  return (
    <View style={styles.container}>
      <StatusBar style="light" backgroundColor="#87CEEB" />
      <OfflineDemo />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F8FF',
  },
});
