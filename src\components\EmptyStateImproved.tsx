/**
 * Improved Empty State Component for IraChat
 * 
 * Engaging empty states with illustrations, helpful messages,
 * and clear calls to action
 */

import { Ionicons } from '@expo/vector-icons';
import React, { useMemo, useCallback } from 'react';
import { Text, TouchableOpacity, View, ViewStyle, ImageBackground } from 'react-native';
import { colors, spacing } from '../styles/designSystem';

interface EmptyStateProps {
  type: 'chats' | 'groups' | 'business'| 'calls' | 'updates' | 'contacts' | 'search' | 'generic';
  title?: string;
  subtitle?: string;
  actionText?: string;
  onActionPress?: () => void;
  style?: ViewStyle;
  showIllustration?: boolean;
  showBackground?: boolean;
}

const emptyStateConfig = {
  chats: {
    icon: 'chatbubbles-outline' as const,
    title: 'No conversations yet',
    subtitle: 'Start messaging with your contacts to see your chats here',
    actionText: 'Start a new chat',
    color: colors.primary,
  },
  groups: {
    icon: 'people-outline' as const,
    title: 'No groups yet',
    subtitle: 'Create or join groups to chat with multiple people at once',
    actionText: 'Create a group',
    color: colors.success,
  },
  business: {
    icon: 'briefcase-outline' as const,
    title: 'No business chats yet',
    subtitle: 'Connect with business contacts and manage professional conversations',
    actionText: 'Start business chat',
    color: colors.primary,
  },
  calls: {
    icon: 'call-outline' as const,
    title: 'No call history',
    subtitle: 'Your voice and video calls will appear here',
    actionText: 'Make a call',
    color: colors.info,
  },
  updates: {
    icon: 'camera-outline' as const,
    title: 'No updates yet',
    subtitle: 'Share photos and videos to see updates from your contacts',
    actionText: 'Share an update',
    color: colors.warning,
  },
  contacts: {
    icon: 'person-add-outline' as const,
    title: 'No contacts found',
    subtitle: 'Add contacts to start chatting and sharing updates',
    actionText: 'Add contacts',
    color: colors.primary,
  },
  search: {
    icon: 'search-outline' as const,
    title: 'No results found',
    subtitle: 'Try adjusting your search terms or check the spelling',
    actionText: 'Clear search',
    color: colors.gray500,
  },
  generic: {
    icon: 'document-outline' as const,
    title: 'Nothing here yet',
    subtitle: 'Content will appear here when available',
    actionText: 'Refresh',
    color: colors.gray500,
  },
};

export const EmptyStateImproved: React.FC<EmptyStateProps> = ({
  type,
  title,
  subtitle,
  actionText,
  onActionPress,
  style,
  showIllustration = true,
  showBackground = false,
}) => {
  // FIXED: Safe config access with validation
  const config = useMemo(() => {
    const configItem = emptyStateConfig[type];
    if (!configItem) {
      console.error(`EmptyStateImproved: Invalid type "${type}". Using generic fallback.`);
      return emptyStateConfig.generic;
    }
    return configItem;
  }, [type]);

  // FIXED: Safe text handling with validation
  const displayTitle = useMemo(() => {
    const titleText = title || config.title;
    return titleText && titleText.trim() !== '' ? titleText : 'No content available';
  }, [title, config.title]);

  const displaySubtitle = useMemo(() => {
    const subtitleText = subtitle || config.subtitle;
    return subtitleText && subtitleText.trim() !== '' ? subtitleText : '';
  }, [subtitle, config.subtitle]);

  const displayActionText = useMemo(() => {
    const actionTextValue = actionText || config.actionText;
    return actionTextValue && actionTextValue.trim() !== '' ? actionTextValue : 'Action';
  }, [actionText, config.actionText]);

  // FIXED: Safe action handler with error handling
  const handleActionPress = useCallback(() => {
    try {
      if (onActionPress && typeof onActionPress === 'function') {
        onActionPress();
      }
    } catch (error) {
      console.error('Error in EmptyStateImproved action handler:', error);
    }
  }, [onActionPress]);
  
  return (
    <View
      style={[
        {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: spacing['4xl'],
          paddingVertical: showBackground ? spacing['4xl'] : spacing['6xl'],
        },
        style,
      ]}
      accessible={true}
      accessibilityRole="text"
      accessibilityLabel={`Empty state: ${displayTitle}. ${displaySubtitle}`}
    >
      {/* FIXED: Illustration with accessibility and error handling */}
      {showIllustration && (
        <View
          style={{
            width: 120,
            height: 120,
            borderRadius: 60,
            backgroundColor: `${config.color}15`, // 15% opacity
            justifyContent: 'center',
            alignItems: 'center',
            marginBottom: spacing['3xl'],
          }}
          accessible={true}
          accessibilityRole="image"
          accessibilityLabel={`${type} illustration`}
        >
          <Ionicons
            name={config.icon}
            size={48}
            color={config.color}
          />
        </View>
      )}
      
      {/* Title */}
      <Text
        style={[
          {
            fontSize: 24,
            fontWeight: '600' as const,
            color: colors.gray800,
            textAlign: 'center' as const,
            marginBottom: spacing.md,
          },
        ]}
      >
        {displayTitle}
      </Text>
      
      {/* Subtitle */}
      <Text
        style={[
          {
            fontSize: 16,
            fontWeight: '400' as const,
            color: colors.gray500,
            textAlign: 'center' as const,
            marginBottom: spacing['3xl'],
            lineHeight: 22,
          },
        ]}
      >
        {displaySubtitle}
      </Text>
      
      {/* FIXED: Action Button with better logic and accessibility */}
      {(onActionPress || displayActionText) && (
        <TouchableOpacity
          onPress={handleActionPress}
          disabled={!onActionPress}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel={`${displayActionText} button`}
          accessibilityHint="Tap to perform the action"
          style={{
            backgroundColor: onActionPress ? config.color : colors.gray300,
            paddingVertical: spacing.md,
            paddingHorizontal: spacing['2xl'],
            borderRadius: 12,
            flexDirection: 'row',
            alignItems: 'center',
            shadowColor: config.color,
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: onActionPress ? 0.3 : 0.1,
            shadowRadius: 8,
            elevation: 4,
            opacity: onActionPress ? 1 : 0.6,
          }}
        >
          <Text
            style={[
              {
                fontSize: 16,
                fontWeight: '600' as const,
                color: onActionPress ? colors.white : colors.gray600,
                marginRight: spacing.sm,
              },
            ]}
          >
            {displayActionText}
          </Text>
          <Ionicons
            name="arrow-forward"
            size={16}
            color={onActionPress ? colors.white : colors.gray600}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

// FIXED: Specific empty state components with proper error handling
export const ChatsEmptyState: React.FC<Partial<EmptyStateProps>> = (props) => {
  // FIXED: Safe background image handling
  const renderBackground = () => {
    try {
      return (
        <>
          {/* IraChat Tiled Background */}
          <ImageBackground
            source={require('../../assets/images/BACKGROUND.png')}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              opacity: 0.3,
            }}
            resizeMode="repeat"
            onError={(error) => {
              console.warn('Failed to load background image:', error);
            }}
          />

          {/* Overlay for better readability */}
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(255, 255, 255, 0.8)',
            }}
          />
        </>
      );
    } catch (error) {
      console.warn('Error rendering background:', error);
      return null;
    }
  };

  return (
    <View style={{ flex: 1 }}>
      {props?.showBackground !== false && renderBackground()}

      {/* Empty State Content */}
      <EmptyStateImproved type="chats" showBackground={true} {...props} />
    </View>
  );
};

export const GroupsEmptyState: React.FC<Partial<EmptyStateProps>> = (props) => (
  <EmptyStateImproved type="groups" {...props} />
);

export const CallsEmptyState: React.FC<Partial<EmptyStateProps>> = (props) => (
  <EmptyStateImproved type="calls" {...props} />
);

export const UpdatesEmptyState: React.FC<Partial<EmptyStateProps>> = (props) => (
  <EmptyStateImproved type="updates" {...props} />
);

export const ContactsEmptyState: React.FC<Partial<EmptyStateProps>> = (props) => (
  <EmptyStateImproved type="contacts" {...props} />
);

export const SearchEmptyState: React.FC<Partial<EmptyStateProps>> = (props) => (
  <EmptyStateImproved type="search" {...props} />
);

// Loading state component
interface LoadingStateProps {
  message?: string;
  style?: ViewStyle;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  message = 'Loading...',
  style,
}) => {
  // FIXED: Safe message handling
  const displayMessage = message && message.trim() !== '' ? message : 'Loading...';

  return (
    <View
      style={[
        {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: spacing['4xl'],
        },
        style,
      ]}
      accessible={true}
      accessibilityRole="text"
      accessibilityLabel={`Loading state: ${displayMessage}`}
    >
      <View
        style={{
          width: 80,
          height: 80,
          borderRadius: 40,
          backgroundColor: `${colors.primary}15`,
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: spacing['2xl'],
        }}
        accessible={true}
        accessibilityRole="image"
        accessibilityLabel="Loading indicator"
      >
        <Ionicons
          name="hourglass-outline"
          size={32}
          color={colors.primary}
        />
      </View>

      <Text
        style={[
          {
            fontSize: 16,
            fontWeight: '400' as const,
            color: colors.gray600,
            textAlign: 'center' as const,
          },
        ]}
        accessible={true}
        accessibilityRole="text"
      >
        {displayMessage}
      </Text>
    </View>
  );
};

// Error state component
interface ErrorStateProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  style?: ViewStyle;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  title = 'Something went wrong',
  message = 'Please try again or contact support if the problem persists',
  onRetry,
  style,
}) => {
  // FIXED: Safe text handling
  const displayTitle = title && title.trim() !== '' ? title : 'Something went wrong';
  const displayMessage = message && message.trim() !== '' ? message : 'Please try again later';

  // FIXED: Safe retry handler
  const handleRetry = useCallback(() => {
    try {
      if (onRetry && typeof onRetry === 'function') {
        onRetry();
      }
    } catch (error) {
      console.error('Error in retry handler:', error);
    }
  }, [onRetry]);

  return (
    <View
      style={[
        {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingHorizontal: spacing['4xl'],
        },
        style,
      ]}
      accessible={true}
      accessibilityRole="text"
      accessibilityLabel={`Error state: ${displayTitle}. ${displayMessage}`}
    >
      <View
        style={{
          width: 80,
          height: 80,
          borderRadius: 40,
          backgroundColor: `${colors.error}15`,
          justifyContent: 'center',
          alignItems: 'center',
          marginBottom: spacing['2xl'],
        }}
        accessible={true}
        accessibilityRole="image"
        accessibilityLabel="Error indicator"
      >
        <Ionicons
          name="alert-circle-outline"
          size={32}
          color={colors.error}
        />
      </View>

      <Text
        style={[
          {
            fontSize: 20,
            fontWeight: '600' as const,
            color: colors.gray800,
            textAlign: 'center' as const,
            marginBottom: spacing.md,
          },
        ]}
        accessible={true}
        accessibilityRole="header"
      >
        {displayTitle}
      </Text>

      <Text
        style={[
          {
            fontSize: 16,
            fontWeight: '400' as const,
            color: colors.gray500,
            textAlign: 'center' as const,
            marginBottom: spacing['3xl'],
            lineHeight: 22,
          },
        ]}
        accessible={true}
        accessibilityRole="text"
      >
        {displayMessage}
      </Text>

      {onRetry && (
        <TouchableOpacity
          onPress={handleRetry}
          accessible={true}
          accessibilityRole="button"
          accessibilityLabel="Try again button"
          accessibilityHint="Tap to retry the failed operation"
          style={{
            backgroundColor: colors.error,
            paddingVertical: spacing.md,
            paddingHorizontal: spacing['2xl'],
            borderRadius: 12,
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <Ionicons
            name="refresh"
            size={16}
            color={colors.white}
            style={{ marginRight: spacing.sm }}
          />
          <Text
            style={[
              {
                fontSize: 16,
                fontWeight: '600' as const,
                color: colors.white,
              },
            ]}
          >
            Try again
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default EmptyStateImproved;
