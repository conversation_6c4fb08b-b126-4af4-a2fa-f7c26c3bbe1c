// 👥 REAL GROUP SERVICE - Complete group management functionality
// Real group creation, member management, permissions, and group messaging

import {
  doc,
  setDoc,
  getDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  updateDoc,
  getDocs,
  arrayUnion,
  arrayRemove,
  deleteDoc,
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { realTimeMessagingService } from './realTimeMessagingService';
import { offlineDatabaseService } from './offlineDatabase';
import { memoryCacheService } from './memoryCache';
import { networkStateManager } from './networkStateManager';
import { Chat } from '../types';

export type GroupRole = 'owner' | 'admin' | 'member';
export type GroupPrivacy = 'public' | 'private' | 'secret';

export interface RealGroup {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  privacy: GroupPrivacy;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  // Members
  members: string[]; // Array of user IDs
  memberRoles: { [userId: string]: GroupRole };
  memberNames: { [userId: string]: string };
  memberAvatars: { [userId: string]: string };
  memberJoinedAt: { [userId: string]: Date };
  // Settings
  allowMemberInvites: boolean;
  allowMemberMessages: boolean;
  requireApproval: boolean;
  maxMembers: number;
  // Activity
  lastMessage?: {
    id: string;
    content: string;
    senderId: string;
    senderName: string;
    timestamp: Date;
    type: string;
  };
  lastActivity: Date;
  messageCount: number;
  // Invite link
  inviteCode?: string;
  inviteLink?: string;
}

export interface CachedGroupData {
  data: RealGroup[];
  timestamp: number;
}

export interface GroupInvite {
  id: string;
  groupId: string;
  groupName: string;
  invitedBy: string;
  invitedByName: string;
  invitedUser: string;
  invitedUserName: string;
  message?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  createdAt: Date;
  expiresAt?: Date;
}

export interface GroupJoinRequest {
  id: string;
  groupId: string;
  groupName: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  message?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
}

class RealGroupService {
  private isInitialized = false;
  private groupCache: Map<string, RealGroup> = new Map();
  private groupListCache: Map<string, CachedGroupData> = new Map();
  private syncQueue: Set<string> = new Set();

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();
      await this.loadGroupsIntoCache();

      // Set up network state listener for sync
      networkStateManager.addListener('realGroupService', this.handleNetworkStateChange.bind(this), 5);

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async loadGroupsIntoCache(): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync('SELECT * FROM groups WHERE isDeleted = 0');

      result.forEach((row: any) => {
        const group = this.rowToGroup(row);
        this.groupCache.set(group.id, group);
        memoryCacheService.setChat(group.id, this.groupToChat(group));
      });
    } catch (error) {
      // Continue without cache if loading fails
    }
  }

  private rowToGroup(row: any): RealGroup {
    return {
      id: row.id,
      name: row.name,
      description: row.description,
      avatar: row.avatar,
      privacy: row.privacy as GroupPrivacy,
      createdBy: row.createdBy,
      createdAt: new Date(row.createdAt),
      updatedAt: new Date(row.updatedAt),
      members: row.members ? JSON.parse(row.members) : [],
      memberRoles: row.memberRoles ? JSON.parse(row.memberRoles) : {},
      memberNames: row.memberNames ? JSON.parse(row.memberNames) : {},
      memberAvatars: row.memberAvatars ? JSON.parse(row.memberAvatars) : {},
      memberJoinedAt: row.memberJoinedAt ? JSON.parse(row.memberJoinedAt) : {},
      allowMemberInvites: Boolean(row.allowMemberInvites),
      allowMemberMessages: Boolean(row.allowMemberMessages),
      requireApproval: Boolean(row.requireApproval),
      maxMembers: row.maxMembers,
      lastActivity: new Date(row.lastActivity),
      messageCount: row.messageCount,
      inviteCode: row.inviteCode,
      inviteLink: row.inviteLink,
    };
  }

  /**
   * Convert RealGroup to Chat type for memory cache compatibility
   */
  private groupToChat(group: RealGroup): Chat {
    return {
      id: group.id,
      name: group.name,
      isGroup: true,
      participants: group.members,
      timestamp: group.lastActivity.toISOString(),
      avatar: group.avatar,
      description: group.description,
      createdBy: group.createdBy,
      lastMessage: group.lastMessage?.content,
      lastMessageAt: group.lastMessage?.timestamp.toISOString(),
    };
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected && this.syncQueue.size > 0) {
      this.processSyncQueue();
    }
  }

  private async processSyncQueue(): Promise<void> {
    if (!networkStateManager.isOnline() || this.syncQueue.size === 0) return;

    const groupIds = Array.from(this.syncQueue);
    this.syncQueue.clear();

    for (const groupId of groupIds) {
      try {
        await this.syncGroupWithFirebase(groupId);
      } catch (error) {
        // Re-add to queue for retry
        this.syncQueue.add(groupId);
      }
    }
  }

  private async syncGroupWithFirebase(groupId: string): Promise<void> {
    const localGroup = this.groupCache.get(groupId);
    if (!localGroup) return;

    try {
      const groupRef = doc(db, 'groups', groupId);
      await setDoc(groupRef, {
        name: localGroup.name,
        description: localGroup.description,
        avatar: localGroup.avatar,
        privacy: localGroup.privacy,
        createdBy: localGroup.createdBy,
        createdAt: localGroup.createdAt,
        updatedAt: serverTimestamp(),
        members: localGroup.members,
        memberRoles: localGroup.memberRoles,
        memberNames: localGroup.memberNames,
        memberAvatars: localGroup.memberAvatars,
        memberJoinedAt: localGroup.memberJoinedAt,
        allowMemberInvites: localGroup.allowMemberInvites,
        allowMemberMessages: localGroup.allowMemberMessages,
        requireApproval: localGroup.requireApproval,
        maxMembers: localGroup.maxMembers,
        lastActivity: serverTimestamp(),
        messageCount: localGroup.messageCount,
        inviteCode: localGroup.inviteCode,
        inviteLink: localGroup.inviteLink,
      });

      // Update sync status in local database
      await this.updateGroupSyncStatus(groupId, 'synced');
    } catch (error) {
      await this.updateGroupSyncStatus(groupId, 'failed');
      throw error;
    }
  }

  private async updateGroupSyncStatus(groupId: string, status: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE groups SET syncStatus = ?, lastSyncAttempt = ? WHERE id = ?
    `, [status, Date.now(), groupId]);
  }

  /**
   * Generate a unique invite code
   */
  private generateInviteCode(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Create a new group (with offline support)
   */
  async createGroup(
    creatorId: string,
    creatorName: string,
    creatorAvatar: string | undefined,
    groupData: {
      name: string;
      description?: string;
      avatar?: string;
      privacy?: GroupPrivacy;
      allowMemberInvites?: boolean;
      allowMemberMessages?: boolean;
      requireApproval?: boolean;
      maxMembers?: number;
    }
  ): Promise<{ success: boolean; groupId?: string; error?: string }> {
    try {
      console.log('🔄 Starting group creation in realGroupService...');
      console.log('📊 Input parameters:', {
        creatorId,
        creatorName,
        creatorAvatar,
        groupData
      });

      // Validate inputs
      if (!creatorId) {
        console.error('❌ No creator ID provided');
        return { success: false, error: 'Creator ID is required' };
      }

      if (!groupData.name || !groupData.name.trim()) {
        console.error('❌ No group name provided');
        return { success: false, error: 'Group name is required' };
      }

      // Check if Firebase is available
      if (!db) {
        console.error('❌ Firebase database not initialized');
        return { success: false, error: 'Database connection error' };
      }

      const groupId = `group_${Date.now()}_${creatorId}`;
      const inviteCode = this.generateInviteCode();

      console.log('🆔 Generated group ID:', groupId);
      console.log('🔑 Generated invite code:', inviteCode);

      const group: Omit<RealGroup, 'id'> = {
        name: groupData.name,
        description: groupData.description,
        avatar: groupData.avatar,
        privacy: groupData.privacy || 'private',
        createdBy: creatorId,
        createdAt: new Date(),
        updatedAt: new Date(),
        members: [creatorId],
        memberRoles: { [creatorId]: 'owner' },
        memberNames: { [creatorId]: creatorName },
        memberAvatars: { [creatorId]: creatorAvatar || '' },
        memberJoinedAt: { [creatorId]: new Date() },
        allowMemberInvites: groupData.allowMemberInvites ?? true,
        allowMemberMessages: groupData.allowMemberMessages ?? true,
        requireApproval: groupData.requireApproval ?? false,
        maxMembers: groupData.maxMembers || 256,
        lastActivity: new Date(),
        messageCount: 0,
        inviteCode,
        inviteLink: `https://irachat.app/join/${inviteCode}`,
      };

      console.log('📝 Group object created:', group);

      // Save group to Firebase
      console.log('💾 Saving group to Firebase...');
      console.log('🔥 Firebase db instance:', !!db);
      console.log('📄 Group document ID:', groupId);
      console.log('📊 Group data to save:', JSON.stringify(group, null, 2));

      const groupRef = doc(db, 'groups', groupId);
      console.log('📍 Group reference created:', groupRef.path);

      const groupDataToSave = {
        ...group,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        lastActivity: serverTimestamp(),
      };

      console.log('💾 Final data being saved:', JSON.stringify(groupDataToSave, null, 2));

      await setDoc(groupRef, groupDataToSave);

      console.log('✅ Group saved to Firebase successfully');

      // Verify the document was created
      try {
        const savedDoc = await getDoc(groupRef);
        if (savedDoc.exists()) {
          console.log('✅ Verified: Document exists in Firebase');
          console.log('📄 Saved document data:', savedDoc.data());
        } else {
          console.error('❌ Document was not saved to Firebase');
        }
      } catch (verifyError) {
        console.error('❌ Error verifying document:', verifyError);
      }

      // Create corresponding chat for the group
      console.log('💬 Creating group chat...');
      try {
        const chatResult = await realTimeMessagingService.createGroupChat(
          groupId,
          groupData.name,
          [creatorId],
          { [creatorId]: creatorName },
          { [creatorId]: creatorAvatar || '' },
          groupData.avatar
        );

        if (chatResult.success) {
          console.log('✅ Group chat created successfully');
        } else {
          console.warn('⚠️ Group chat creation failed, but continuing with group creation');
        }
      } catch (chatError) {
        console.warn('⚠️ Error creating group chat:', chatError);
        // Continue with group creation even if chat fails
      }

      console.log('🎉 Group creation completed successfully!');
      return { success: true, groupId };
    } catch (error) {
      console.error('❌ Error in createGroup:', error);
      console.error('❌ Error details:', error instanceof Error ? error.message : String(error));
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create group'
      };
    }
  }

  /**
   * Get user's groups with performance optimization
   */
  async getUserGroups(userId: string): Promise<RealGroup[]> {
    try {
      // Check cache first
      const cacheKey = `user_groups_${userId}`;
      const cached = this.groupListCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < 30000) { // 30 second cache
        console.log('⚡ Returning cached groups');
        return cached.data;
      }

      // Skip network connectivity check to avoid timeout conflicts
      console.log('🌐 Proceeding with Firebase query (skipping network check)');

      // Check if user is authenticated before querying
      const { auth } = await import('../services/firebaseSimple');
      if (!auth?.currentUser) {
        console.warn('⚠️ User not authenticated, cannot query groups');
        throw new Error('User not authenticated');
      }

      // Check if database connection is available
      if (!db) {
        console.warn('⚠️ Firestore database not available');
        throw new Error('Database not available');
      }

      const groupsRef = collection(db, 'groups');
      const q = query(
        groupsRef,
        where('members', 'array-contains', userId),
        orderBy('lastActivity', 'desc'),
        limit(50) // Limit to 50 groups for performance
      );

      // Execute query with increased timeout and better error handling
      console.log('🔄 Executing Firebase groups query...');
      const queryPromise = getDocs(q);
      const timeoutPromise = new Promise<any>((_, reject) =>
        setTimeout(() => reject(new Error('Groups query timeout - Firebase took too long to respond')), 15000)
      );

      const snapshot = await Promise.race([queryPromise, timeoutPromise]);
      console.log('✅ Firebase query completed successfully');
      const groups: RealGroup[] = [];

      snapshot.docs.forEach((doc: any) => {
        const data = doc.data();
        groups.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          lastActivity: data.lastActivity?.toDate() || new Date(),
          lastMessage: data.lastMessage ? {
            ...data.lastMessage,
            timestamp: data.lastMessage.timestamp?.toDate() || new Date(),
          } : undefined,
        } as RealGroup);
      });

      // Cache results
      this.groupListCache.set(cacheKey, {
        data: groups,
        timestamp: Date.now()
      });

      return groups;
    } catch (error) {
      console.error('❌ Error fetching user groups:', error);

      // Log specific error details for debugging
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack?.substring(0, 200)
        });

        // Handle timeout specifically
        if (error.message.includes('timeout')) {
          console.log('⏰ Query timeout detected, falling back to cached data');
        }
      }

      // Try to return cached data even if expired
      const cacheKey = `user_groups_${userId}`;
      const cached = this.groupListCache.get(cacheKey);
      if (cached) {
        console.log('⚠️ Returning expired cached groups due to error');
        return cached.data;
      }

      // Try to load from offline database as last resort
      try {
        console.log('🔄 Attempting to load groups from offline database...');

        // Check if offline database service is ready
        if (!offlineDatabaseService.isReady()) {
          console.warn('⚠️ Offline database service not ready');
          return [];
        }

        const offlineDb = offlineDatabaseService.getDatabase();
        if (!offlineDb) {
          console.warn('⚠️ Offline database is null');
          return [];
        }

        const offlineGroups = await offlineDb.getAllAsync(`
          SELECT * FROM chats
          WHERE isGroup = 1 AND isDeleted = 0
          AND (participants LIKE '%${userId}%' OR partnerId = '${userId}')
          ORDER BY lastMessageTime DESC
          LIMIT 50
        `);

        if (offlineGroups.length > 0) {
          console.log(`📦 Found ${offlineGroups.length} groups in offline database`);
          const groups: RealGroup[] = offlineGroups.map((row: any) => {
            const members = row.participants ? JSON.parse(row.participants) : [];
            const admins = row.groupAdmins ? JSON.parse(row.groupAdmins) : [];

            return {
              id: row.id,
              name: row.name || 'Unknown Group',
              avatar: row.avatar,
              description: row.groupDescription || '',
              members,
              admins,
              memberCount: members.length,
              memberRoles: {},
              memberNames: row.participantNames ? JSON.parse(row.participantNames) : {},
              memberAvatars: row.participantAvatars ? JSON.parse(row.participantAvatars) : {},
              memberJoinedAt: {},
              memberLastSeen: {},
              privacy: 'private' as const,
              createdBy: '',
              createdAt: new Date(row.createdAt || Date.now()),
              updatedAt: new Date(row.updatedAt || Date.now()),
              lastActivity: new Date(row.lastMessageTime || Date.now()),
              lastMessage: row.lastMessage ? {
                id: '',
                content: row.lastMessage,
                senderId: row.lastMessageSender || '',
                senderName: '',
                timestamp: new Date(row.lastMessageTime || Date.now()),
                type: row.lastMessageType || 'text'
              } : undefined,
              settings: {
                allowMemberInvites: true,
                allowMemberMessages: true,
                allowMediaSharing: true,
                allowLinkSharing: true,
                muteNotifications: false,
                autoDeleteMessages: false,
                autoDeleteDuration: 0,
              },
              inviteCode: '',
              tags: [],
              allowMemberInvites: true,
              allowMemberMessages: true,
              requireApproval: false,
              maxMembers: 1000,
              messageCount: 0,
            };
          });

          return groups;
        }
      } catch (offlineError) {
        console.error('❌ Failed to load groups from offline database:', offlineError);
      }

      return [];
    }
  }

  /**
   * Subscribe to user's groups
   */
  subscribeToUserGroups(
    userId: string,
    callback: (_groups: RealGroup[]) => void
  ): () => void {
    const groupsRef = collection(db, 'groups');
    const q = query(
      groupsRef,
      where('members', 'array-contains', userId),
      orderBy('lastActivity', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const groups: RealGroup[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        groups.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          lastActivity: data.lastActivity?.toDate() || new Date(),
          lastMessage: data.lastMessage ? {
            ...data.lastMessage,
            timestamp: data.lastMessage.timestamp?.toDate() || new Date(),
          } : undefined,
        } as RealGroup);
      });

      callback(groups);
    });

    return unsubscribe;
  }

  /**
   * Join group by invite code
   */
  async joinGroupByCode(
    inviteCode: string,
    userId: string,
    userName: string,
    userAvatar?: string
  ): Promise<{ success: boolean; groupId?: string; error?: string }> {
    try {
      // Find group by invite code
      const groupsRef = collection(db, 'groups');
      const q = query(groupsRef, where('inviteCode', '==', inviteCode));
      const snapshot = await getDocs(q);

      if (snapshot.empty) {
        return { success: false, error: 'Invalid invite code' };
      }

      const groupDoc = snapshot.docs[0];
      const groupData = groupDoc.data() as RealGroup;
      const groupId = groupDoc.id;

      // Check if user is already a member
      if (groupData.members.includes(userId)) {
        return { success: false, error: 'You are already a member of this group' };
      }

      // Check if group is full
      if (groupData.members.length >= groupData.maxMembers) {
        return { success: false, error: 'Group is full' };
      }

      // Check if approval is required
      if (groupData.requireApproval) {
        // Create join request
        const requestId = `request_${Date.now()}_${userId}_${groupId}`;
        const requestRef = doc(db, 'groupJoinRequests', requestId);
        
        await setDoc(requestRef, {
          groupId,
          groupName: groupData.name,
          userId,
          userName,
          userAvatar,
          status: 'pending',
          createdAt: serverTimestamp(),
        });

        return { success: true, groupId, error: 'Join request sent. Waiting for approval.' };
      }

      // Add user to group
      const result = await this.addMemberToGroup(groupId, userId, userName, userAvatar);
      return result;
    } catch (error) {
      return { success: false, error: 'Failed to join group' };
    }
  }

  /**
   * Add member to group
   */
  async addMemberToGroup(
    groupId: string,
    userId: string,
    userName: string,
    userAvatar?: string,
    role: GroupRole = 'member'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (!groupDoc.exists()) {
        return { success: false, error: 'Group not found' };
      }

      const groupData = groupDoc.data() as RealGroup;

      // Check if user is already a member
      if (groupData.members.includes(userId)) {
        return { success: false, error: 'User is already a member' };
      }

      // Check if group is full
      if (groupData.members.length >= groupData.maxMembers) {
        return { success: false, error: 'Group is full' };
      }

      // Update group
      await updateDoc(groupRef, {
        members: arrayUnion(userId),
        [`memberRoles.${userId}`]: role,
        [`memberNames.${userId}`]: userName,
        [`memberAvatars.${userId}`]: userAvatar || '',
        [`memberJoinedAt.${userId}`]: serverTimestamp(),
        updatedAt: serverTimestamp(),
        lastActivity: serverTimestamp(),
      });

      // Add user to group chat
      await realTimeMessagingService.addUserToGroupChat(
        groupId,
        userId,
        userName,
        userAvatar || ''
      );

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to add member' };
    }
  }

  /**
   * Remove member from group
   */
  async removeMemberFromGroup(
    groupId: string,
    userId: string,
    removedBy: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (!groupDoc.exists()) {
        return { success: false, error: 'Group not found' };
      }

      const groupData = groupDoc.data() as RealGroup;

      // Check permissions
      const removerRole = groupData.memberRoles[removedBy];
      const targetRole = groupData.memberRoles[userId];

      if (removedBy !== userId && removerRole !== 'owner' && removerRole !== 'admin') {
        return { success: false, error: 'Not authorized to remove members' };
      }

      if (targetRole === 'owner' && removedBy !== userId) {
        return { success: false, error: 'Cannot remove group owner' };
      }

      // Remove user from group
      await updateDoc(groupRef, {
        members: arrayRemove(userId),
        [`memberRoles.${userId}`]: null,
        [`memberNames.${userId}`]: null,
        [`memberAvatars.${userId}`]: null,
        [`memberJoinedAt.${userId}`]: null,
        updatedAt: serverTimestamp(),
        lastActivity: serverTimestamp(),
      });

      // Remove user from group chat
      await realTimeMessagingService.removeUserFromGroupChat(groupId, userId);

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to remove member' };
    }
  }

  /**
   * Update group settings
   */
  async updateGroupSettings(
    groupId: string,
    userId: string,
    updates: Partial<Pick<RealGroup, 'name' | 'description' | 'avatar' | 'privacy' | 'allowMemberInvites' | 'allowMemberMessages' | 'requireApproval' | 'maxMembers'>> & { coverImage?: string }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (!groupDoc.exists()) {
        return { success: false, error: 'Group not found' };
      }

      const groupData = groupDoc.data() as RealGroup;

      // Check permissions
      const userRole = groupData.memberRoles[userId];
      if (userRole !== 'owner' && userRole !== 'admin') {
        return { success: false, error: 'Not authorized to update group settings' };
      }

      // Update group
      await updateDoc(groupRef, {
        ...updates,
        updatedAt: serverTimestamp(),
        lastActivity: serverTimestamp(),
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to update group settings' };
    }
  }

  /**
   * Delete group
   */
  async deleteGroup(
    groupId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (!groupDoc.exists()) {
        return { success: false, error: 'Group not found' };
      }

      const groupData = groupDoc.data() as RealGroup;

      // Check if user is owner
      if (groupData.memberRoles[userId] !== 'owner') {
        return { success: false, error: 'Only group owner can delete the group' };
      }

      // Delete group
      await deleteDoc(groupRef);

      // Delete group chat
      await realTimeMessagingService.deleteGroupChat(groupId);

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete group' };
    }
  }



  /**
   * Search public groups
   */
  async searchPublicGroups(searchQuery: string, limitCount: number = 20): Promise<RealGroup[]> {
    try {
      const groupsRef = collection(db, 'groups');
      const q = query(
        groupsRef,
        where('privacy', '==', 'public'),
        orderBy('memberCount', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      const groups: RealGroup[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        const group = {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          lastActivity: data.lastActivity?.toDate() || new Date(),
        } as RealGroup;

        // Filter by name if searchQuery provided
        if (!searchQuery || group.name.toLowerCase().includes(searchQuery.toLowerCase())) {
          groups.push(group);
        }
      });

      return groups;
    } catch (error) {
      return [];
    }
  }

  // ==================== GROUP MESSAGING ====================

  /**
   * Get group by ID
   */
  async getGroupById(groupId: string): Promise<any> {
    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (!groupDoc.exists()) {
        return null;
      }

      const data = groupDoc.data();
      return {
        id: groupDoc.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Get messages for a group
   */
  async getMessages(groupId: string, limitCount: number = 50): Promise<{ success: boolean; messages?: any[]; error?: string }> {
    try {
      const messagesRef = collection(db, 'groups', groupId, 'messages');
      const q = query(
        messagesRef,
        orderBy('timestamp', 'desc'),
        limit(limitCount)
      );

      const snapshot = await getDocs(q);
      const messages: any[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        messages.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
        });
      });

      return { success: true, messages };
    } catch (error) {
      return { success: false, error: 'Failed to load messages' };
    }
  }

  /**
   * Send message to group
   */
  async sendMessage(groupId: string, message: any): Promise<{ success: boolean; error?: string }> {
    try {
      const messagesRef = collection(db, 'groups', groupId, 'messages');
      await setDoc(doc(messagesRef), {
        ...message,
        timestamp: serverTimestamp(),
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to send message' };
    }
  }

  /**
   * Get user contacts
   */
  async getUserContacts(_userId: string): Promise<any[]> {
    try {
      // Mock implementation - replace with actual contacts API
      return [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Get group members
   */
  async getGroupMembers(groupId: string): Promise<any[]> {
    try {
      const groupRef = doc(db, 'groups', groupId);
      const groupDoc = await getDoc(groupRef);

      if (groupDoc.exists()) {
        const groupData = groupDoc.data();
        return groupData.members || [];
      }

      return [];
    } catch (error) {
      return [];
    }
  }

  /**
   * Get member preferences
   */
  async getMemberPreferences(groupId: string, userId: string): Promise<any> {
    try {
      // Real implementation - get from local storage or Firebase
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getFirstAsync(`
        SELECT preferences FROM group_member_preferences
        WHERE groupId = ? AND userId = ?
      `, [groupId, userId]);

      if (result) {
        return JSON.parse((result as any).preferences);
      }

      // Default preferences
      const defaultPrefs = {
        notifications: true,
        soundEnabled: true,
        vibrationEnabled: true,
      };

      // Save default preferences
      await db.runAsync(`
        INSERT OR REPLACE INTO group_member_preferences (groupId, userId, preferences)
        VALUES (?, ?, ?)
      `, [groupId, userId, JSON.stringify(defaultPrefs)]);

      return defaultPrefs;
    } catch (error) {
      return {
        notifications: true,
        soundEnabled: true,
        vibrationEnabled: true,
      };
    }
  }

  /**
   * Get group details
   */
  async getGroupDetails(groupId: string): Promise<any> {
    try {
      // Try cache first
      const cachedGroup = this.groupCache.get(groupId);
      if (cachedGroup) {
        return cachedGroup;
      }

      // Try offline database
      const offlineDb = offlineDatabaseService.getDatabase();
      const result = await offlineDb.getFirstAsync(`
        SELECT * FROM groups WHERE id = ? AND isDeleted = 0
      `, [groupId]);

      if (result) {
        const group = this.rowToGroup(result as any);
        this.groupCache.set(groupId, group);
        return group;
      }

      // Try online if available
      if (networkStateManager.isOnline()) {
        try {
          const groupRef = doc(db, 'groups', groupId);
          const groupDoc = await getDoc(groupRef);

          if (groupDoc.exists()) {
            const data = groupDoc.data();
            const group: RealGroup = {
              id: groupDoc.id,
              name: data.name,
              description: data.description,
              avatar: data.avatar,
              privacy: data.privacy,
              createdBy: data.createdBy,
              createdAt: data.createdAt?.toDate() || new Date(),
              updatedAt: data.updatedAt?.toDate() || new Date(),
              members: data.members || [],
              memberRoles: data.memberRoles || {},
              memberNames: data.memberNames || {},
              memberAvatars: data.memberAvatars || {},
              memberJoinedAt: data.memberJoinedAt || {},
              allowMemberInvites: data.allowMemberInvites ?? true,
              allowMemberMessages: data.allowMemberMessages ?? true,
              requireApproval: data.requireApproval ?? false,
              maxMembers: data.maxMembers || 256,
              lastActivity: data.lastActivity?.toDate() || new Date(),
              messageCount: data.messageCount || 0,
              inviteCode: data.inviteCode,
              inviteLink: data.inviteLink,
            };

            // Cache offline
            await this.saveGroupOffline(group);
            this.groupCache.set(groupId, group);
            return group;
          }
        } catch (onlineError) {
          // Continue to return null if offline data not found
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private async saveGroupOffline(group: RealGroup): Promise<void> {
    const offlineDb = offlineDatabaseService.getDatabase();

    await offlineDb.runAsync(`
      INSERT OR REPLACE INTO groups (
        id, name, description, avatar, privacy, createdBy, createdAt, updatedAt,
        members, memberRoles, memberNames, memberAvatars, memberJoinedAt,
        allowMemberInvites, allowMemberMessages, requireApproval, maxMembers,
        lastActivity, messageCount, inviteCode, inviteLink, syncStatus, isDeleted
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      group.id,
      group.name || null,
      group.description || null,
      group.avatar || null,
      group.privacy,
      group.createdBy,
      group.createdAt.getTime(),
      group.updatedAt.getTime(),
      JSON.stringify(group.members),
      JSON.stringify(group.memberRoles),
      JSON.stringify(group.memberNames),
      JSON.stringify(group.memberAvatars),
      JSON.stringify(group.memberJoinedAt),
      group.allowMemberInvites ? 1 : 0,
      group.allowMemberMessages ? 1 : 0,
      group.requireApproval ? 1 : 0,
      group.maxMembers,
      group.lastActivity.getTime(),
      group.messageCount,
      group.inviteCode || null,
      group.inviteLink || null,
      'synced',
      0
    ]);
  }

  /**
   * Cleanup method
   */
  cleanup(): void {
    networkStateManager.removeListener('realGroupService');
    this.groupCache.clear();
    this.syncQueue.clear();
    this.isInitialized = false;
  }
}

// Export singleton instance
export const realGroupService = new RealGroupService();
export default realGroupService;
