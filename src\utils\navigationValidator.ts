/**
 * 🧭 COMPREHENSIVE NAVIGATION VALIDATOR
 * Validates all navigation routes, checks for unmatched routes, and ensures proper navigation flow
 */

import { ROUTES } from '../services/navigationService';

interface ValidationResult {
  isValid: boolean;
  route: string;
  error?: string;
  category: string;
}

interface NavigationValidationReport {
  totalRoutes: number;
  validRoutes: number;
  invalidRoutes: number;
  missingFiles: string[];
  unmatchedRoutes: string[];
  authFlowValid: boolean;
  tabNavigationValid: boolean;
  criticalPathsValid: boolean;
  issues: string[];
  recommendations: string[];
}

/**
 * Get all actual route files that exist in the app directory
 */
function getExistingRouteFiles(): string[] {
  // FIXED: Updated to reflect actual file structure
  return [
    // Root routes
    '/', // index.tsx

    // Auth routes
    '/(auth)/welcome',
    '/(auth)/register-info', // Registration info screen (explains phone requirement)
    '/(auth)/phone-register',
    '/(auth)/register', // Main registration form
    '/(auth)/index',

    // Tab routes
    '/(tabs)/index', // Main chats tab
    '/(tabs)/groups',
    '/(tabs)/business',
    '/(tabs)/calls',
    '/(tabs)/updates',
    '/(tabs)/profile',
    '/(tabs)/settings',

    // Chat routes
    '/new-chat',
    '/individual-chat',
    '/group-chat',
    '/enhanced-group-chat',
    '/chat-management',

    // Group routes
    '/create-group',
    '/group-settings',
    '/select-group-members',

    // Call routes
    '/video-call',
    '/voice-call',
    '/incoming-call',
    '/incoming-call-real',
    '/real-call',
    '/video-call-safe',
    '/call',

    // Profile routes
    '/edit-profile',
    '/account-settings',
    '/privacy-settings',
    '/notifications-settings',
    '/theme-settings',

    // Social routes
    '/social-feed',
    '/create-update',

    // Search routes
    '/global-search',
    '/contacts',
    '/fast-contacts',

    // Media routes
    '/camera',
    '/media-gallery',
    '/downloaded-media',

    // Help routes
    '/help',
    '/help-support',
    '/about',

    // Organization routes
    '/archives',
    '/pinned-messages',
    '/invite-friends',
    '/export-data',

    // Dynamic routes
    '/chat/[id]', // Dynamic chat route
    '/update/[id]', // Dynamic update route
  ];
}

/**
 * Extract all routes from ROUTES constant
 */
function extractAllRoutes(): string[] {
  const routes: string[] = [];
  
  function extractFromObject(obj: any) {
    for (const key in obj) {
      if (typeof obj[key] === 'string') {
        routes.push(obj[key]);
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        extractFromObject(obj[key]);
      }
    }
  }
  
  extractFromObject(ROUTES);
  return routes;
}

/**
 * Validate individual route
 */
function validateRoute(route: string): ValidationResult {
  const existingFiles = getExistingRouteFiles();

  // Check exact match first
  let isValid = existingFiles.includes(route);

  // If not exact match, check if it's a dynamic route pattern
  if (!isValid) {
    // Check for dynamic routes like /chat/[id] matching /chat/123
    const dynamicMatches = existingFiles.filter(file => {
      if (file.includes('[id]')) {
        const pattern = file.replace('[id]', '.*');
        const regex = new RegExp(`^${pattern.replace(/\//g, '\\/')}$`);
        return regex.test(route);
      }
      return false;
    });
    isValid = dynamicMatches.length > 0;
  }

  return {
    isValid,
    route,
    error: isValid ? undefined : `Route file not found: ${route}`,
    category: getRouteCategory(route)
  };
}

/**
 * Get route category
 */
function getRouteCategory(route: string): string {
  if (route.includes('(auth)')) return 'Authentication';
  if (route.includes('(tabs)')) return 'Tabs';
  if (route.includes('/chat')) return 'Chat';
  if (route.includes('/call')) return 'Call';
  if (route.includes('/group')) return 'Group';
  if (route.includes('/profile') || route.includes('/account') || route.includes('/privacy')) return 'Profile';
  if (route.includes('/media') || route.includes('/camera')) return 'Media';
  if (route.includes('/help') || route.includes('/about')) return 'Help';
  return 'Other';
}

/**
 * Validate authentication flow
 */
function validateAuthFlow(): boolean {
  const authRoutes = [
    ROUTES.AUTH.WELCOME,
    ROUTES.AUTH.PHONE_REGISTER,
    ROUTES.AUTH.INFO,
    ROUTES.AUTH.REGISTER,
  ];

  return authRoutes.every(route => validateRoute(route).isValid);
}

/**
 * Validate tab navigation (should only be five tabs as described in (auth)/tabs)
 */
function validateTabNavigation(): boolean {
  const tabRoutes = [
    ROUTES.TABS.CHATS,
    ROUTES.TABS.GROUPS,
    ROUTES.TABS.BUSINESS, // Business marketplace tab
    ROUTES.TABS.CALLS,
    ROUTES.TABS.STORIES, // Stories content (video feed)

  ];

  return tabRoutes.every(route => validateRoute(route).isValid);
}

/**
 * Validate critical navigation paths
 */
function validateCriticalPaths(): boolean {
  const criticalRoutes = [
    ROUTES.AUTH.WELCOME,
    ROUTES.TABS.CHATS,
    ROUTES.CHAT.NEW_CHAT,
    ROUTES.GROUP.CREATE,
    ROUTES.TABS.BUSINESS,
    ROUTES.SEARCH.CONTACTS,
    ROUTES.MEDIA.CAMERA,
  ];

  return criticalRoutes.every(route => validateRoute(route).isValid);
}

/**
 * Generate comprehensive navigation validation report
 */
export function generateNavigationValidationReport(): NavigationValidationReport {
  const allRoutes = extractAllRoutes();
  const existingFiles = getExistingRouteFiles();
  
  const validationResults = allRoutes.map(route => validateRoute(route));
  const validRoutes = validationResults.filter(result => result.isValid);
  const invalidRoutes = validationResults.filter(result => !result.isValid);
  
  const missingFiles = invalidRoutes.map(result => result.route);
  const unmatchedRoutes = existingFiles.filter(file => !allRoutes.includes(file));
  
  const authFlowValid = validateAuthFlow();
  const tabNavigationValid = validateTabNavigation();
  const criticalPathsValid = validateCriticalPaths();
  
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  if (missingFiles.length > 0) {
    issues.push(`${missingFiles.length} routes defined but files missing`);
    recommendations.push('Create missing route files or remove unused route definitions');
  }
  
  if (unmatchedRoutes.length > 0) {
    issues.push(`${unmatchedRoutes.length} route files exist but not defined in ROUTES`);
    recommendations.push('Add missing routes to ROUTES constant or remove unused files');
  }
  
  if (!authFlowValid) {
    issues.push('Authentication flow has invalid routes');
    recommendations.push('Fix authentication route definitions and ensure all auth files exist');
  }
  
  if (!tabNavigationValid) {
    issues.push('Tab navigation has invalid routes');
    recommendations.push('Fix tab route definitions and ensure all tab files exist');
  }
  
  if (!criticalPathsValid) {
    issues.push('Critical navigation paths have invalid routes');
    recommendations.push('Fix critical route definitions to ensure app functionality');
  }
  
  return {
    totalRoutes: allRoutes.length,
    validRoutes: validRoutes.length,
    invalidRoutes: invalidRoutes.length,
    missingFiles,
    unmatchedRoutes,
    authFlowValid,
    tabNavigationValid,
    criticalPathsValid,
    issues,
    recommendations
  };
}

/**
 * Run comprehensive navigation validation
 */
export function runNavigationValidation(): void {
  if (__DEV__) {
    console.log('🔍 Running Comprehensive Navigation Validation...\n');
    
    const report = generateNavigationValidationReport();
    
    console.log('📊 NAVIGATION VALIDATION REPORT');
    console.log('================================');
    console.log(`Total Routes: ${report.totalRoutes}`);
    console.log(`Valid Routes: ${report.validRoutes}`);
    console.log(`Invalid Routes: ${report.invalidRoutes}`);
    console.log(`Auth Flow Valid: ${report.authFlowValid ? '✅' : '❌'}`);
    console.log(`Tab Navigation Valid: ${report.tabNavigationValid ? '✅' : '❌'}`);
    console.log(`Critical Paths Valid: ${report.criticalPathsValid ? '✅' : '❌'}`);
    
    if (report.missingFiles.length > 0) {
      console.log('\n❌ Missing Route Files:');
      report.missingFiles.forEach(file => console.log(`  - ${file}`));
    }
    
    if (report.unmatchedRoutes.length > 0) {
      console.log('\n⚠️ Unmatched Route Files:');
      report.unmatchedRoutes.forEach(file => console.log(`  - ${file}`));
    }
    
    if (report.issues.length > 0) {
      console.log('\n🚨 Issues Found:');
      report.issues.forEach(issue => console.log(`  - ${issue}`));
    }
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
    
    if (report.issues.length === 0) {
      console.log('\n🎉 All navigation routes are properly configured!');
    }
  }
}
