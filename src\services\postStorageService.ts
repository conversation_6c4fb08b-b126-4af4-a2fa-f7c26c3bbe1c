import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
import { BusinessPost } from '../types/Business';
import {
  collection,
  addDoc,
  serverTimestamp
} from 'firebase/firestore';
import {
  ref,
  getDownloadURL,
  uploadBytesResumable,
  uploadBytes
} from 'firebase/storage';
import { db, storage } from './firebaseSimple';

export interface PendingPost extends Omit<BusinessPost, 'id' | 'createdAt' | 'updatedAt'> {
  tempId: string;
  localCreatedAt: Date;
  syncStatus: 'pending' | 'syncing' | 'synced' | 'failed';
  retryCount: number;
  lastSyncAttempt?: Date;
  errorMessage?: string;
}

class PostStorageService {
  private readonly PENDING_POSTS_KEY = 'pending_posts';
  private readonly SYNCED_POSTS_KEY = 'synced_posts';
  private syncInProgress = false;

  // Save a post locally
  async savePostLocally(post: Omit<PendingPost, 'tempId' | 'syncStatus' | 'retryCount' | 'localCreatedAt'>): Promise<PendingPost> {
    const pendingPost: PendingPost = {
      ...post,
      tempId: `temp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      syncStatus: 'pending',
      retryCount: 0,
      localCreatedAt: new Date(),
    };

    const pendingPosts = await this.getPendingPosts();
    pendingPosts.push(pendingPost);
    await AsyncStorage.setItem(this.PENDING_POSTS_KEY, JSON.stringify(pendingPosts));

    // Try to sync immediately if online
    this.attemptSync();

    return pendingPost;
  }

  // Get all pending posts
  async getPendingPosts(): Promise<PendingPost[]> {
    try {
      const data = await AsyncStorage.getItem(this.PENDING_POSTS_KEY);
      if (!data) return [];
      
      const posts = JSON.parse(data);
      return posts.map((post: any) => ({
        ...post,
        localCreatedAt: new Date(post.localCreatedAt),
        lastSyncAttempt: post.lastSyncAttempt ? new Date(post.lastSyncAttempt) : undefined,
      }));
    } catch (error) {
      console.error('Error getting pending posts:', error);
      return [];
    }
  }

  // Get all synced posts from local storage
  async getSyncedPosts(): Promise<BusinessPost[]> {
    try {
      const data = await AsyncStorage.getItem(this.SYNCED_POSTS_KEY);
      if (!data) return [];
      
      const posts = JSON.parse(data);
      return posts.map((post: any) => ({
        ...post,
        createdAt: new Date(post.createdAt),
        updatedAt: new Date(post.updatedAt),
      }));
    } catch (error) {
      console.error('Error getting synced posts:', error);
      return [];
    }
  }

  // Save synced posts to local storage
  async saveSyncedPosts(posts: BusinessPost[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.SYNCED_POSTS_KEY, JSON.stringify(posts));
    } catch (error) {
      console.error('Error saving synced posts:', error);
    }
  }

  // Get all posts (pending + synced) for display
  async getAllPosts(): Promise<BusinessPost[]> {
    const [pendingPosts, syncedPosts] = await Promise.all([
      this.getPendingPosts(),
      this.getSyncedPosts(),
    ]);

    // Convert pending posts to display format
    const pendingAsBusinessPosts: BusinessPost[] = pendingPosts.map(post => ({
      ...post,
      id: post.tempId,
      createdAt: post.localCreatedAt,
      updatedAt: post.localCreatedAt,
      // Add sync status as a custom property for UI
      _syncStatus: post.syncStatus,
      _tempId: post.tempId,
    } as BusinessPost & { _syncStatus?: string; _tempId?: string }));

    // Combine and sort by creation date (newest first)
    const allPosts = [...pendingAsBusinessPosts, ...syncedPosts];
    return allPosts.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  // Check if a post is pending sync
  isPendingPost(post: BusinessPost | PendingPost): post is PendingPost {
    return 'tempId' in post && 'syncStatus' in post;
  }

  // Attempt to sync pending posts
  async attemptSync(): Promise<void> {
    if (this.syncInProgress) return;

    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) return;

    this.syncInProgress = true;

    try {
      const pendingPosts = await this.getPendingPosts();
      const postsToSync = pendingPosts.filter(post => 
        post.syncStatus === 'pending' || 
        (post.syncStatus === 'failed' && post.retryCount < 3)
      );

      for (const post of postsToSync) {
        await this.syncSinglePost(post);
      }
    } catch (error) {
      console.error('Error during sync:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  // Sync a single post
  private async syncSinglePost(post: PendingPost): Promise<void> {
    console.log('🔄 Starting sync for post:', post.tempId, 'with media:', post.media?.length || 0, 'files');

    try {
      // Update status to syncing
      await this.updatePostSyncStatus(post.tempId, 'syncing');
      console.log('✅ Post status updated to syncing:', post.tempId);

      // Upload post to server
      const syncedPost = await this.uploadPostToServer(post);
      console.log('✅ Post uploaded to server successfully:', post.tempId, '-> Firebase ID:', syncedPost.id);

      // Remove from pending and add to synced
      await this.markPostAsSynced(post.tempId, syncedPost);
      console.log('✅ Post marked as synced and moved to synced storage:', post.tempId);

    } catch (error) {
      console.error('❌ Error syncing post:', post.tempId, error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Log detailed error information
      console.error('❌ Sync error details:', {
        tempId: post.tempId,
        errorMessage,
        hasMedia: !!post.media && post.media.length > 0,
        mediaCount: post.media?.length || 0,
        retryCount: post.retryCount,
        lastSyncAttempt: post.lastSyncAttempt
      });

      await this.updatePostSyncStatus(post.tempId, 'failed', errorMessage);
      console.log('⚠️ Post marked as failed:', post.tempId, 'Error:', errorMessage);
    }
  }

  // Update sync status of a pending post
  private async updatePostSyncStatus(
    tempId: string, 
    status: PendingPost['syncStatus'], 
    errorMessage?: string
  ): Promise<void> {
    const pendingPosts = await this.getPendingPosts();
    const postIndex = pendingPosts.findIndex(p => p.tempId === tempId);
    
    if (postIndex !== -1) {
      pendingPosts[postIndex].syncStatus = status;
      pendingPosts[postIndex].lastSyncAttempt = new Date();
      
      if (status === 'failed') {
        pendingPosts[postIndex].retryCount += 1;
        pendingPosts[postIndex].errorMessage = errorMessage;
      }

      await AsyncStorage.setItem(this.PENDING_POSTS_KEY, JSON.stringify(pendingPosts));
    }
  }

  // Mark post as synced and move to synced storage
  private async markPostAsSynced(tempId: string, syncedPost: BusinessPost): Promise<void> {
    // Remove from pending
    const pendingPosts = await this.getPendingPosts();
    const filteredPending = pendingPosts.filter(p => p.tempId !== tempId);
    await AsyncStorage.setItem(this.PENDING_POSTS_KEY, JSON.stringify(filteredPending));

    // Add to synced
    const syncedPosts = await this.getSyncedPosts();
    syncedPosts.unshift(syncedPost); // Add to beginning
    await this.saveSyncedPosts(syncedPosts);
  }

  // Upload media files to Firebase Storage
  private async uploadMediaFiles(mediaItems: any[], businessId: string): Promise<any[]> {
    if (!mediaItems || mediaItems.length === 0) {
      return [];
    }

    // Debug authentication
    const { auth } = await import('./firebaseSimple');
    const currentUser = auth.currentUser;
    console.log('🔐 Current user for upload:', {
      uid: currentUser?.uid,
      email: currentUser?.email,
      isAuthenticated: !!currentUser
    });

    // Validate Firebase Storage setup
    if (!currentUser) {
      throw new Error('User not authenticated - cannot upload to Firebase Storage');
    }

    try {
      console.log('📸 Uploading', mediaItems.length, 'media files to Firebase Storage...');
      const uploadedMedia: any[] = [];

      for (let i = 0; i < mediaItems.length; i++) {
        const media = mediaItems[i];
        try {
          console.log(`📸 Uploading media ${i + 1}/${mediaItems.length}:`, media.type);

          // Generate unique filename
          const timestamp = Date.now();
          const randomId = Math.random().toString(36).substring(2, 11);
          const extension = (media.type === 'image' || media.type === 'photo') ? 'jpg' : 'mp4';
          const filename = `business_posts/${businessId}/${timestamp}_${randomId}.${extension}`;

          // Get the file as blob
          let blob: Blob;
          if (media.url.startsWith('file://') || media.url.startsWith('content://')) {
            // Local file - fetch as blob
            console.log(`📁 Fetching local file: ${media.url}`);
            const response = await fetch(media.url);

            if (!response.ok) {
              throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
            }

            blob = await response.blob();

            // Validate blob
            if (!blob || blob.size === 0) {
              throw new Error('Invalid blob: empty or null');
            }

            console.log(`✅ Blob created successfully: ${blob.size} bytes, type: ${blob.type}`);
          } else {
            // Already a URL - this shouldn't happen for new posts, but handle it
            throw new Error('Media URL is not a local file path');
          }

          // Create storage reference
          const storageRef = ref(storage, filename);

          // Debug storage configuration
          console.log('🔧 Storage config:', {
            bucket: storage.app.options.storageBucket,
            projectId: storage.app.options.projectId,
            filename,
            blobSize: blob.size,
            blobType: blob.type,
            storageApp: storage.app.name
          });

          // Validate storage bucket exists
          if (!storage.app.options.storageBucket) {
            throw new Error('Firebase Storage bucket not configured');
          }

          // Upload the file
          const uploadResult = await uploadBytes(storageRef, blob, {
            contentType: (media.type === 'image' || media.type === 'photo') ? 'image/jpeg' : 'video/mp4',
          });

          console.log(`✅ Upload completed for ${filename}`);

          const downloadURL = await getDownloadURL(uploadResult.ref);
          console.log(`✅ Download URL obtained for ${filename}: ${downloadURL}`);

          uploadedMedia.push({
            ...media,
            url: downloadURL,
            path: filename,
          });

        } catch (error) {
          console.error(`❌ Failed to upload media ${i + 1}:`, error);
          throw new Error(`Failed to upload media file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      console.log('✅ All media files uploaded successfully');
      return uploadedMedia;

    } catch (error) {
      console.error('❌ Firebase Storage upload failed, falling back to local storage:', error);

      // FALLBACK: Return media items with local paths when Firebase Storage fails
      console.log('⚠️ FALLBACK: Saving media posts with local file paths due to upload error');

      const localMediaItems = mediaItems.map(media => ({
        ...media,
        url: media.url, // Keep local file path
        path: `local_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        uploadStatus: 'pending_storage_fix',
        uploadError: error instanceof Error ? error.message : 'Unknown upload error'
      }));

      console.log('✅ Returning media items with local paths:', localMediaItems.length, 'files');
      return localMediaItems;
    }
  }

  // Real Firebase upload implementation
  private async uploadPostToServer(post: PendingPost): Promise<BusinessPost> {
    try {
      console.log('🔥 Starting real Firebase upload for post:', post.tempId);
      console.log('📋 Post details:', {
        tempId: post.tempId,
        title: post.title,
        hasMedia: !!post.media && post.media.length > 0,
        mediaCount: post.media?.length || 0,
        businessId: post.businessId
      });

      // Step 1: Upload media files to Firebase Storage (with fallback)
      let uploadedMedia: any[] = [];
      let mediaUploadStatus = 'completed';

      if (post.media && post.media.length > 0) {
        try {
          console.log('📸 Attempting to upload', post.media.length, 'media files...');
          uploadedMedia = await this.uploadMediaFiles(post.media, post.businessId);

          // Check if any media failed to upload to Firebase Storage
          const hasFailedUploads = uploadedMedia.some(m => m.uploadStatus === 'pending_storage_fix');
          if (hasFailedUploads) {
            mediaUploadStatus = 'pending_storage_fix';
            console.log('⚠️ Some media files failed to upload to Firebase Storage, using local paths');
          } else {
            console.log('✅ All media files uploaded successfully to Firebase Storage');
          }
        } catch (mediaError) {
          console.error('❌ Media upload failed completely, proceeding with post creation without media:', mediaError);
          // Don't fail the entire post creation if media upload fails
          uploadedMedia = [];
          mediaUploadStatus = 'failed';
        }
      } else {
        console.log('📝 No media files to upload, creating text-only post');
      }

      // Step 2: Create the post document in Firestore (always proceed, even if media failed)
      const postData = {
        businessId: post.businessId,
        businessName: post.businessName,
        businessLogo: post.businessLogo || null,
        businessType: post.businessType,
        isVerified: post.isVerified,
        title: post.title,
        description: post.description,
        media: uploadedMedia,
        mediaUploadStatus,
        tags: post.tags,
        category: post.category,
        price: post.price || null,
        oldPrice: null, // No old price for new posts
        priceHistory: [], // Initialize empty price history for new posts
        currency: post.currency,
        isNegotiable: post.isNegotiable,
        availability: post.availability,
        status: post.status,
        productDetails: post.productDetails || null,
        location: post.location,
        additionalLocations: post.additionalLocations || [],
        contact: post.contact,
        likes: [],
        comments: [],
        shares: 0,
        views: 0,
        downloads: 0,
        isActive: true,
        isPinned: false,
        isPromoted: false,
        promotionEndsAt: post.promotionEndsAt || null,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      console.log('🔥 Creating Firestore document with data:', {
        title: postData.title,
        mediaCount: postData.media.length,
        mediaUploadStatus: postData.mediaUploadStatus
      });

      const docRef = await addDoc(collection(db, 'businessPosts'), postData);
      console.log('✅ Firestore document created with ID:', docRef.id);

      // Step 3: Return the synced post
      const syncedPost: BusinessPost = {
        ...post,
        id: docRef.id,
        media: uploadedMedia,
        likes: [],
        comments: [],
        shares: 0,
        views: 0,
        downloads: 0,
        isActive: true,
        isPinned: false,
        isPromoted: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      console.log('✅ Post successfully synced to Firebase:', docRef.id, 'Media status:', mediaUploadStatus);
      return syncedPost;

    } catch (error) {
      console.error('❌ Firebase upload failed completely:', error);

      // Log detailed error information
      if (error instanceof Error) {
        console.error('❌ Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }

      throw new Error(`Firebase upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Get failed posts for debugging
  async getFailedPosts(): Promise<PendingPost[]> {
    const pendingPosts = await this.getPendingPosts();
    return pendingPosts.filter(post => post.syncStatus === 'failed');
  }

  // Retry failed posts
  async retryFailedPosts(): Promise<void> {
    console.log('🔄 Retrying failed posts...');
    const pendingPosts = await this.getPendingPosts();
    const failedPosts = pendingPosts.filter(post => post.syncStatus === 'failed');

    console.log('📊 Found', failedPosts.length, 'failed posts to retry');

    for (const post of failedPosts) {
      if (post.retryCount < 3) {
        console.log('🔄 Retrying post:', post.tempId, 'Retry count:', post.retryCount);
        await this.updatePostSyncStatus(post.tempId, 'pending');
      } else {
        console.log('❌ Post exceeded max retries:', post.tempId, 'Retry count:', post.retryCount);
      }
    }

    this.attemptSync();
  }

  // Force retry a specific post (for manual retry)
  async retrySpecificPost(tempId: string): Promise<void> {
    console.log('🔄 Force retrying specific post:', tempId);
    await this.updatePostSyncStatus(tempId, 'pending');
    this.attemptSync();
  }

  // Debug function to log current state
  async debugCurrentState(): Promise<void> {
    console.log('🔍 === POST STORAGE DEBUG STATE ===');

    const pendingPosts = await this.getPendingPosts();
    const syncedPosts = await this.getSyncedPosts();
    const allPosts = await this.getAllPosts();

    console.log('📊 Pending posts:', pendingPosts.length);
    pendingPosts.forEach(post => {
      console.log(`  - ${post.tempId}: ${post.title} (${post.syncStatus}) - Retry: ${post.retryCount}`);
    });

    console.log('📊 Synced posts:', syncedPosts.length);
    console.log('📊 All posts (for display):', allPosts.length);

    const stats = await this.getSyncStats();
    console.log('📊 Sync stats:', stats);

    console.log('🔍 === END DEBUG STATE ===');
  }

  // Clear all local data (for testing/reset)
  async clearAllData(): Promise<void> {
    await AsyncStorage.multiRemove([this.PENDING_POSTS_KEY, this.SYNCED_POSTS_KEY]);
  }

  // Get sync statistics
  async getSyncStats(): Promise<{
    pending: number;
    syncing: number;
    failed: number;
    synced: number;
  }> {
    const [pendingPosts, syncedPosts] = await Promise.all([
      this.getPendingPosts(),
      this.getSyncedPosts(),
    ]);

    return {
      pending: pendingPosts.filter(p => p.syncStatus === 'pending').length,
      syncing: pendingPosts.filter(p => p.syncStatus === 'syncing').length,
      failed: pendingPosts.filter(p => p.syncStatus === 'failed').length,
      synced: syncedPosts.length,
    };
  }
}

export const postStorageService = new PostStorageService();

// Auto-sync when network becomes available
NetInfo.addEventListener((state: any) => {
  if (state.isConnected) {
    postStorageService.attemptSync();
  }
});
