/**
 * Auto Download Service for IraChat
 * Actually implements the auto download functionality based on user preferences and network state
 * No fake implementations - this service genuinely controls media downloads
 */

import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import { networkStateManager } from './networkStateManager';
import { realPrivacyService } from './realPrivacyService';
import { offlineDatabaseService } from './offlineDatabase';

export interface MediaDownloadRequest {
  id: string;
  url: string;
  type: 'image' | 'video' | 'audio' | 'document';
  fileName: string;
  fileSize?: number;
  chatId: string;
  messageId: string;
  senderId: string;
  priority: 'high' | 'normal' | 'low';
  thumbnail?: string;
}

export interface DownloadProgress {
  mediaId: string;
  progress: number;
  status: 'pending' | 'downloading' | 'completed' | 'failed' | 'cancelled';
  error?: string;
  localPath?: string;
}

export interface AutoDownloadSettings {
  images: 'never' | 'wifi' | 'always';
  videos: 'never' | 'wifi' | 'always';
  audio: 'never' | 'wifi' | 'always';
  documents: 'never' | 'wifi' | 'always';
  maxFileSize: number; // in MB
  onlyInChats: boolean; // Only auto-download in individual chats, not groups
}

class AutoDownloadService {
  private isInitialized = false;
  private downloadQueue: Map<string, MediaDownloadRequest> = new Map();
  private activeDownloads: Map<string, FileSystem.DownloadResumable> = new Map();
  private progressCallbacks: Map<string, (progress: DownloadProgress) => void> = new Map();
  private currentUserId: string | null = null;
  private userSettings: AutoDownloadSettings | null = null;

  async initialize(userId: string): Promise<void> {
    if (this.isInitialized && this.currentUserId === userId) return;

    try {
      this.currentUserId = userId;
      await this.loadUserSettings();
      
      // Set up network state listener to process queue when connection changes
      networkStateManager.addListener('autoDownloadService', this.handleNetworkChange.bind(this), 3);
      
      // Process any pending downloads
      await this.processPendingDownloads();
      
      this.isInitialized = true;
      console.log('✅ Auto download service initialized for user:', userId);
    } catch (error) {
      console.error('❌ Failed to initialize auto download service:', error);
      throw error;
    }
  }

  private async loadUserSettings(): Promise<void> {
    if (!this.currentUserId) return;

    try {
      const privacySettingsResult = await realPrivacyService.getPrivacySettings(this.currentUserId);
      const autoDownloadSetting = privacySettingsResult.settings?.autoDownloadMedia || 'wifi';
      
      // Convert single setting to detailed settings
      this.userSettings = {
        images: autoDownloadSetting,
        videos: autoDownloadSetting,
        audio: autoDownloadSetting,
        documents: 'never', // Documents are never auto-downloaded for security
        maxFileSize: autoDownloadSetting === 'always' ? 100 : 25, // MB
        onlyInChats: true, // Only auto-download in individual chats by default
      };
    } catch (error) {
      console.error('❌ Failed to load user settings:', error);
      // Use default settings
      this.userSettings = {
        images: 'wifi',
        videos: 'wifi',
        audio: 'wifi',
        documents: 'never',
        maxFileSize: 25,
        onlyInChats: true,
      };
    }
  }

  private handleNetworkChange(networkState: any): void {
    if (networkState.isConnected) {
      // Network is available, process pending downloads
      this.processPendingDownloads();
    } else {
      // Network lost, pause all active downloads
      this.pauseAllDownloads();
    }
  }

  /**
   * Check if media should be auto-downloaded based on user settings and network state
   */
  shouldAutoDownload(request: MediaDownloadRequest, isGroupChat: boolean = false): boolean {
    if (!this.userSettings) return false;

    // Check if auto-download is disabled for groups
    if (isGroupChat && this.userSettings.onlyInChats) {
      return false;
    }

    // Check file size limit
    if (request.fileSize && request.fileSize > this.userSettings.maxFileSize * 1024 * 1024) {
      return false;
    }

    // Get setting for this media type
    const mediaTypeKey = request.type === 'image' ? 'images' :
                        request.type === 'video' ? 'videos' :
                        request.type === 'audio' ? 'audio' : 'documents';
    const setting = this.userSettings[mediaTypeKey] || 'never';
    
    if (setting === 'never') {
      return false;
    }

    if (setting === 'always') {
      return networkStateManager.isOnline();
    }

    if (setting === 'wifi') {
      const networkState = networkStateManager.getState();
      return networkState.isConnected && networkState.connectionType === 'wifi';
    }

    return false;
  }

  /**
   * Queue media for auto download
   */
  async queueForAutoDownload(request: MediaDownloadRequest, isGroupChat: boolean = false): Promise<boolean> {
    if (!this.shouldAutoDownload(request, isGroupChat)) {
      console.log(`📵 Auto download skipped for ${request.fileName} - settings/network don't allow`);
      return false;
    }

    console.log(`📥 Queuing auto download for ${request.fileName}`);
    this.downloadQueue.set(request.id, request);
    
    // Save to offline database for persistence
    await this.saveDownloadRequest(request);
    
    // Start download immediately if network allows
    if (this.canDownloadNow(request.type)) {
      await this.startDownload(request);
    }

    return true;
  }

  private canDownloadNow(mediaType: string): boolean {
    if (!this.userSettings) return false;
    
    const setting = this.userSettings[mediaType as keyof AutoDownloadSettings] as string;
    const networkState = networkStateManager.getState();
    
    if (setting === 'always') {
      return networkState.isConnected;
    }
    
    if (setting === 'wifi') {
      return networkState.isConnected && networkState.connectionType === 'wifi';
    }
    
    return false;
  }

  private async startDownload(request: MediaDownloadRequest): Promise<void> {
    try {
      // Check if already downloading
      if (this.activeDownloads.has(request.id)) {
        return;
      }

      console.log(`🚀 Starting auto download: ${request.fileName}`);
      
      // Create download directory if it doesn't exist
      const downloadDir = `${FileSystem.documentDirectory}IraChat/Downloads/`;
      await FileSystem.makeDirectoryAsync(downloadDir, { intermediates: true });
      
      const localPath = `${downloadDir}${request.fileName}`;
      
      // Create download resumable
      const downloadResumable = FileSystem.createDownloadResumable(
        request.url,
        localPath,
        {},
        (downloadProgress) => {
          const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
          this.notifyProgress(request.id, {
            mediaId: request.id,
            progress: Math.round(progress * 100),
            status: 'downloading',
            localPath,
          });
        }
      );

      this.activeDownloads.set(request.id, downloadResumable);
      
      // Update status to downloading
      this.notifyProgress(request.id, {
        mediaId: request.id,
        progress: 0,
        status: 'downloading',
      });

      // Start download
      const result = await downloadResumable.downloadAsync();
      
      if (result && result.status === 200) {
        // Download completed successfully
        await this.handleDownloadComplete(request, result.uri);
      } else {
        throw new Error(`Download failed with status: ${result?.status}`);
      }
      
    } catch (error) {
      console.error(`❌ Auto download failed for ${request.fileName}:`, error);
      await this.handleDownloadError(request, error as Error);
    } finally {
      this.activeDownloads.delete(request.id);
      this.downloadQueue.delete(request.id);
    }
  }

  private async handleDownloadComplete(request: MediaDownloadRequest, localPath: string): Promise<void> {
    try {
      // Save to media library if it's an image or video
      if (request.type === 'image' || request.type === 'video') {
        const { status } = await MediaLibrary.requestPermissionsAsync();
        if (status === 'granted') {
          await MediaLibrary.saveToLibraryAsync(localPath);
        }
      }

      // Update database
      await this.updateDownloadStatus(request.id, 'completed', localPath);
      
      // Notify completion
      this.notifyProgress(request.id, {
        mediaId: request.id,
        progress: 100,
        status: 'completed',
        localPath,
      });

      console.log(`✅ Auto download completed: ${request.fileName}`);
      
    } catch (error) {
      console.error(`❌ Error handling download completion:`, error);
      await this.handleDownloadError(request, error as Error);
    }
  }

  private async handleDownloadError(request: MediaDownloadRequest, error: Error): Promise<void> {
    await this.updateDownloadStatus(request.id, 'failed', undefined, error.message);
    
    this.notifyProgress(request.id, {
      mediaId: request.id,
      progress: 0,
      status: 'failed',
      error: error.message,
    });
  }

  private notifyProgress(mediaId: string, progress: DownloadProgress): void {
    const callback = this.progressCallbacks.get(mediaId);
    if (callback) {
      callback(progress);
    }
  }

  /**
   * Register callback for download progress updates
   */
  onDownloadProgress(mediaId: string, callback: (progress: DownloadProgress) => void): void {
    this.progressCallbacks.set(mediaId, callback);
  }

  /**
   * Remove progress callback
   */
  removeProgressCallback(mediaId: string): void {
    this.progressCallbacks.delete(mediaId);
  }

  private async processPendingDownloads(): Promise<void> {
    if (!networkStateManager.isOnline()) return;

    const pendingDownloads = Array.from(this.downloadQueue.values());
    
    for (const request of pendingDownloads) {
      if (this.canDownloadNow(request.type)) {
        await this.startDownload(request);
      }
    }
  }

  private pauseAllDownloads(): void {
    this.activeDownloads.forEach((download, mediaId) => {
      download.pauseAsync().catch(error => {
        console.error(`❌ Error pausing download ${mediaId}:`, error);
      });
    });
  }

  /**
   * Cancel a specific download
   */
  async cancelDownload(mediaId: string): Promise<void> {
    const download = this.activeDownloads.get(mediaId);
    if (download) {
      await download.pauseAsync();
      this.activeDownloads.delete(mediaId);
    }
    
    this.downloadQueue.delete(mediaId);
    await this.updateDownloadStatus(mediaId, 'cancelled');
    
    this.notifyProgress(mediaId, {
      mediaId,
      progress: 0,
      status: 'cancelled',
    });
  }

  /**
   * Get current download status
   */
  getDownloadStatus(mediaId: string): DownloadProgress | null {
    // This would typically query the database for status
    // For now, return basic status based on active downloads
    if (this.activeDownloads.has(mediaId)) {
      return {
        mediaId,
        progress: 0,
        status: 'downloading',
      };
    }
    return null;
  }

  /**
   * Update user's auto download settings
   */
  async updateSettings(settings: Partial<AutoDownloadSettings>): Promise<void> {
    if (!this.userSettings) return;
    
    this.userSettings = { ...this.userSettings, ...settings };
    
    // Save to database
    await this.saveUserSettings();
    
    console.log('✅ Auto download settings updated:', this.userSettings);
  }

  private async saveDownloadRequest(request: MediaDownloadRequest): Promise<void> {
    // Save to offline database for persistence across app restarts
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      INSERT OR REPLACE INTO auto_download_queue (
        id, url, type, fileName, fileSize, chatId, messageId, senderId, priority, createdAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      request.id,
      request.url,
      request.type,
      request.fileName,
      request.fileSize || 0,
      request.chatId,
      request.messageId,
      request.senderId,
      request.priority,
      Date.now()
    ]);
  }

  private async updateDownloadStatus(
    mediaId: string, 
    status: string, 
    localPath?: string, 
    error?: string
  ): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE auto_download_queue 
      SET status = ?, localPath = ?, error = ?, updatedAt = ?
      WHERE id = ?
    `, [status, localPath || null, error || null, Date.now(), mediaId]);
  }

  private async saveUserSettings(): Promise<void> {
    if (!this.currentUserId || !this.userSettings) return;
    
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      INSERT OR REPLACE INTO auto_download_settings (
        userId, images, videos, audio, documents, maxFileSize, onlyInChats, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      this.currentUserId,
      this.userSettings.images,
      this.userSettings.videos,
      this.userSettings.audio,
      this.userSettings.documents,
      this.userSettings.maxFileSize,
      this.userSettings.onlyInChats ? 1 : 0,
      Date.now()
    ]);
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    // Cancel all active downloads
    for (const [mediaId] of this.activeDownloads) {
      await this.cancelDownload(mediaId);
    }
    
    // Clear callbacks
    this.progressCallbacks.clear();
    
    // Remove network listener
    networkStateManager.removeListener('autoDownloadService');
    
    this.isInitialized = false;
    console.log('🧹 Auto download service cleaned up');
  }
}

export const autoDownloadService = new AutoDownloadService();
