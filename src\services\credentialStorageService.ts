// 🔐 CREDENTIAL STORAGE SERVICE
// Securely stores and retrieves user credentials for auto-fill functionality

import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

interface StoredCredentials {
  email?: string;
  password?: string;
  phoneNumber?: string;
  rememberCredentials: boolean;
  lastUsed: number;
}

class CredentialStorageService {
  private readonly STORAGE_KEYS = {
    EMAIL_CREDENTIALS: 'irachat_email_credentials',
    PHONE_CREDENTIALS: 'irachat_phone_credentials',
    REMEMBER_PREFERENCE: 'irachat_remember_preference',
  };

  /**
   * Save email credentials securely
   */
  async saveEmailCredentials(email: string, password: string, remember: boolean = true): Promise<void> {
    try {
      console.log('💾 Saving email credentials for auto-fill...');

      if (!remember) {
        console.log('🚫 User chose not to remember credentials');
        await this.clearEmailCredentials();
        return;
      }

      const credentials: StoredCredentials = {
        email: email.trim().toLowerCase(),
        password,
        rememberCredentials: remember,
        lastUsed: Date.now(),
      };

      const credentialsJson = JSON.stringify(credentials);

      // Use SecureStore on mobile platforms for better security
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        await SecureStore.setItemAsync(this.STORAGE_KEYS.EMAIL_CREDENTIALS, credentialsJson);
        console.log('✅ Email credentials saved to SecureStore');
      } else {
        // Fallback to AsyncStorage for other platforms
        await AsyncStorage.setItem(this.STORAGE_KEYS.EMAIL_CREDENTIALS, credentialsJson);
        console.log('✅ Email credentials saved to AsyncStorage');
      }

      // Save remember preference separately
      await AsyncStorage.setItem(this.STORAGE_KEYS.REMEMBER_PREFERENCE, remember.toString());
      
    } catch (error) {
      console.error('❌ Failed to save email credentials:', error);
      throw error;
    }
  }

  /**
   * Save phone number for auto-fill
   */
  async savePhoneCredentials(phoneNumber: string, remember: boolean = true): Promise<void> {
    try {
      console.log('💾 Saving phone credentials for auto-fill...');

      if (!remember) {
        console.log('🚫 User chose not to remember phone number');
        await this.clearPhoneCredentials();
        return;
      }

      const credentials: StoredCredentials = {
        phoneNumber: phoneNumber.trim(),
        rememberCredentials: remember,
        lastUsed: Date.now(),
      };

      const credentialsJson = JSON.stringify(credentials);

      // Use SecureStore on mobile platforms
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        await SecureStore.setItemAsync(this.STORAGE_KEYS.PHONE_CREDENTIALS, credentialsJson);
        console.log('✅ Phone credentials saved to SecureStore');
      } else {
        await AsyncStorage.setItem(this.STORAGE_KEYS.PHONE_CREDENTIALS, credentialsJson);
        console.log('✅ Phone credentials saved to AsyncStorage');
      }

      // Save remember preference
      await AsyncStorage.setItem(this.STORAGE_KEYS.REMEMBER_PREFERENCE, remember.toString());
      
    } catch (error) {
      console.error('❌ Failed to save phone credentials:', error);
      throw error;
    }
  }

  /**
   * Retrieve saved email credentials
   */
  async getEmailCredentials(): Promise<{ email: string; password: string } | null> {
    try {
      console.log('🔍 Retrieving saved email credentials...');

      let credentialsJson: string | null = null;

      // Try to get from SecureStore first
      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        credentialsJson = await SecureStore.getItemAsync(this.STORAGE_KEYS.EMAIL_CREDENTIALS);
      } else {
        credentialsJson = await AsyncStorage.getItem(this.STORAGE_KEYS.EMAIL_CREDENTIALS);
      }

      if (!credentialsJson) {
        console.log('📭 No saved email credentials found');
        return null;
      }

      const credentials: StoredCredentials = JSON.parse(credentialsJson);

      // Check if credentials are still valid (not older than 90 days)
      const maxAge = 90 * 24 * 60 * 60 * 1000; // 90 days
      if (Date.now() - credentials.lastUsed > maxAge) {
        console.log('⏰ Saved credentials are too old, clearing them');
        await this.clearEmailCredentials();
        return null;
      }

      if (credentials.email && credentials.password && credentials.rememberCredentials) {
        console.log('✅ Retrieved saved email credentials for:', credentials.email);
        return {
          email: credentials.email,
          password: credentials.password,
        };
      }

      return null;
    } catch (error) {
      console.error('❌ Failed to retrieve email credentials:', error);
      return null;
    }
  }

  /**
   * Retrieve saved phone number
   */
  async getPhoneCredentials(): Promise<{ phoneNumber: string } | null> {
    try {
      console.log('🔍 Retrieving saved phone credentials...');

      let credentialsJson: string | null = null;

      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        credentialsJson = await SecureStore.getItemAsync(this.STORAGE_KEYS.PHONE_CREDENTIALS);
      } else {
        credentialsJson = await AsyncStorage.getItem(this.STORAGE_KEYS.PHONE_CREDENTIALS);
      }

      if (!credentialsJson) {
        console.log('📭 No saved phone credentials found');
        return null;
      }

      const credentials: StoredCredentials = JSON.parse(credentialsJson);

      // Check if credentials are still valid
      const maxAge = 90 * 24 * 60 * 60 * 1000; // 90 days
      if (Date.now() - credentials.lastUsed > maxAge) {
        console.log('⏰ Saved phone credentials are too old, clearing them');
        await this.clearPhoneCredentials();
        return null;
      }

      if (credentials.phoneNumber && credentials.rememberCredentials) {
        console.log('✅ Retrieved saved phone number:', credentials.phoneNumber);
        return {
          phoneNumber: credentials.phoneNumber,
        };
      }

      return null;
    } catch (error) {
      console.error('❌ Failed to retrieve phone credentials:', error);
      return null;
    }
  }

  /**
   * Check if user has chosen to remember credentials
   */
  async getRememberPreference(): Promise<boolean> {
    try {
      const preference = await AsyncStorage.getItem(this.STORAGE_KEYS.REMEMBER_PREFERENCE);
      return preference === 'true';
    } catch (error) {
      console.error('❌ Failed to get remember preference:', error);
      return false;
    }
  }

  /**
   * Clear saved email credentials
   */
  async clearEmailCredentials(): Promise<void> {
    try {
      console.log('🗑️ Clearing saved email credentials...');

      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        await SecureStore.deleteItemAsync(this.STORAGE_KEYS.EMAIL_CREDENTIALS).catch(() => {});
      } else {
        await AsyncStorage.removeItem(this.STORAGE_KEYS.EMAIL_CREDENTIALS);
      }

      console.log('✅ Email credentials cleared');
    } catch (error) {
      console.error('❌ Failed to clear email credentials:', error);
    }
  }

  /**
   * Clear saved phone credentials
   */
  async clearPhoneCredentials(): Promise<void> {
    try {
      console.log('🗑️ Clearing saved phone credentials...');

      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        await SecureStore.deleteItemAsync(this.STORAGE_KEYS.PHONE_CREDENTIALS).catch(() => {});
      } else {
        await AsyncStorage.removeItem(this.STORAGE_KEYS.PHONE_CREDENTIALS);
      }

      console.log('✅ Phone credentials cleared');
    } catch (error) {
      console.error('❌ Failed to clear phone credentials:', error);
    }
  }

  /**
   * Clear all saved credentials
   */
  async clearAllCredentials(): Promise<void> {
    try {
      console.log('🗑️ Clearing all saved credentials...');
      
      await Promise.all([
        this.clearEmailCredentials(),
        this.clearPhoneCredentials(),
        AsyncStorage.removeItem(this.STORAGE_KEYS.REMEMBER_PREFERENCE),
      ]);

      console.log('✅ All credentials cleared');
    } catch (error) {
      console.error('❌ Failed to clear all credentials:', error);
    }
  }

  /**
   * Update last used timestamp for credentials
   */
  async updateLastUsed(type: 'email' | 'phone'): Promise<void> {
    try {
      const key = type === 'email' 
        ? this.STORAGE_KEYS.EMAIL_CREDENTIALS 
        : this.STORAGE_KEYS.PHONE_CREDENTIALS;

      let credentialsJson: string | null = null;

      if (Platform.OS === 'ios' || Platform.OS === 'android') {
        credentialsJson = await SecureStore.getItemAsync(key);
      } else {
        credentialsJson = await AsyncStorage.getItem(key);
      }

      if (credentialsJson) {
        const credentials: StoredCredentials = JSON.parse(credentialsJson);
        credentials.lastUsed = Date.now();

        const updatedJson = JSON.stringify(credentials);

        if (Platform.OS === 'ios' || Platform.OS === 'android') {
          await SecureStore.setItemAsync(key, updatedJson);
        } else {
          await AsyncStorage.setItem(key, updatedJson);
        }

        console.log(`✅ Updated last used timestamp for ${type} credentials`);
      }
    } catch (error) {
      console.error(`❌ Failed to update last used for ${type} credentials:`, error);
    }
  }
}

// Export singleton instance
export const credentialStorageService = new CredentialStorageService();
export default credentialStorageService;
