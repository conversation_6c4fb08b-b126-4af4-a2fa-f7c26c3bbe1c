/**
 * Voice Message Service
 * Handles voice message storage and retrieval from voiceMessages collection
 */

import { db } from './firebase';
import { 
  doc, 
  setDoc, 
  getDoc, 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  serverTimestamp,
  updateDoc
} from 'firebase/firestore';

export interface VoiceMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  audioUrl: string;
  duration: number; // in seconds
  waveform?: number[]; // Audio waveform data
  transcript?: string; // Speech-to-text transcript
  isPlayed: boolean;
  playedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

class VoiceMessageService {
  // Save voice message to voiceMessages collection
  async saveVoiceMessage(voiceMessageData: Omit<VoiceMessage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const voiceMessageRef = doc(collection(db, 'voiceMessages'));
      const voiceMessage: VoiceMessage = {
        ...voiceMessageData,
        id: voiceMessageRef.id,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await setDoc(voiceMessageRef, {
        ...voiceMessage,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Voice message saved to voiceMessages collection');
      return voiceMessageRef.id;
    } catch (error) {
      console.error('❌ Failed to save voice message:', error);
      throw error;
    }
  }

  // Get voice message by ID
  async getVoiceMessage(voiceMessageId: string): Promise<VoiceMessage | null> {
    try {
      const voiceMessageDoc = await getDoc(doc(db, 'voiceMessages', voiceMessageId));
      
      if (!voiceMessageDoc.exists()) {
        return null;
      }
      
      const data = voiceMessageDoc.data();
      return {
        ...data,
        id: voiceMessageDoc.id,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        playedAt: data.playedAt?.toDate(),
      } as VoiceMessage;
    } catch (error) {
      console.error('Failed to get voice message:', error);
      return null;
    }
  }

  // Get voice messages for a chat
  async getVoiceMessagesForChat(chatId: string, limitCount: number = 50): Promise<VoiceMessage[]> {
    try {
      const voiceMessagesRef = collection(db, 'voiceMessages');
      const voiceMessagesQuery = query(
        voiceMessagesRef,
        where('chatId', '==', chatId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const snapshot = await getDocs(voiceMessagesQuery);
      const voiceMessages: VoiceMessage[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        voiceMessages.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          playedAt: data.playedAt?.toDate(),
        } as VoiceMessage);
      });
      
      return voiceMessages;
    } catch (error) {
      console.error('Failed to get voice messages for chat:', error);
      return [];
    }
  }

  // Mark voice message as played
  async markVoiceMessageAsPlayed(voiceMessageId: string): Promise<void> {
    try {
      const voiceMessageRef = doc(db, 'voiceMessages', voiceMessageId);
      await updateDoc(voiceMessageRef, {
        isPlayed: true,
        playedAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      
      console.log('✅ Voice message marked as played');
    } catch (error) {
      console.error('Failed to mark voice message as played:', error);
      throw error;
    }
  }

  // Update voice message transcript
  async updateVoiceMessageTranscript(voiceMessageId: string, transcript: string): Promise<void> {
    try {
      const voiceMessageRef = doc(db, 'voiceMessages', voiceMessageId);
      await updateDoc(voiceMessageRef, {
        transcript,
        updatedAt: serverTimestamp(),
      });
      
      console.log('✅ Voice message transcript updated');
    } catch (error) {
      console.error('Failed to update voice message transcript:', error);
      throw error;
    }
  }

  // Update voice message waveform
  async updateVoiceMessageWaveform(voiceMessageId: string, waveform: number[]): Promise<void> {
    try {
      const voiceMessageRef = doc(db, 'voiceMessages', voiceMessageId);
      await updateDoc(voiceMessageRef, {
        waveform,
        updatedAt: serverTimestamp(),
      });
      
      console.log('✅ Voice message waveform updated');
    } catch (error) {
      console.error('Failed to update voice message waveform:', error);
      throw error;
    }
  }

  // Get voice messages by sender
  async getVoiceMessagesBySender(senderId: string, limitCount: number = 50): Promise<VoiceMessage[]> {
    try {
      const voiceMessagesRef = collection(db, 'voiceMessages');
      const voiceMessagesQuery = query(
        voiceMessagesRef,
        where('senderId', '==', senderId),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const snapshot = await getDocs(voiceMessagesQuery);
      const voiceMessages: VoiceMessage[] = [];
      
      snapshot.forEach((doc) => {
        const data = doc.data();
        voiceMessages.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
          playedAt: data.playedAt?.toDate(),
        } as VoiceMessage);
      });
      
      return voiceMessages;
    } catch (error) {
      console.error('Failed to get voice messages by sender:', error);
      return [];
    }
  }

  // Delete voice message
  async deleteVoiceMessage(voiceMessageId: string): Promise<void> {
    try {
      const voiceMessageRef = doc(db, 'voiceMessages', voiceMessageId);
      await updateDoc(voiceMessageRef, {
        isDeleted: true,
        deletedAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      
      console.log('✅ Voice message deleted');
    } catch (error) {
      console.error('Failed to delete voice message:', error);
      throw error;
    }
  }

  // Get unplayed voice messages count for a user
  async getUnplayedVoiceMessagesCount(userId: string): Promise<number> {
    try {
      const voiceMessagesRef = collection(db, 'voiceMessages');
      const unplayedQuery = query(
        voiceMessagesRef,
        where('senderId', '!=', userId), // Not sent by the user
        where('isPlayed', '==', false)
      );
      
      const snapshot = await getDocs(unplayedQuery);
      return snapshot.size;
    } catch (error) {
      console.error('Failed to get unplayed voice messages count:', error);
      return 0;
    }
  }
}

export const voiceMessageService = new VoiceMessageService();
