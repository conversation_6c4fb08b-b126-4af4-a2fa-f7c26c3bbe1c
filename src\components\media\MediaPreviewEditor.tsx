import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Dimensions,
  StyleSheet,
  Animated,
  Image,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  ScrollView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { VideoView, useVideoPlayer } from 'expo-video';
import { AudioCaptionRecorder } from './AudioCaptionRecorder';
import { TextOverlayEditor } from './TextOverlayEditor';
import { TextOverlayRenderer } from './TextOverlayRenderer';
import { EmojiPicker } from './EmojiPicker';
import { VideoTrimmer } from './VideoTrimmer';
import { ImageCropper } from './ImageCropper';
import { AudioCaption, TextOverlay } from '../../types/Update';
// import { BlurView } from 'expo-blur'; // Optional - using regular View for compatibility

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface MediaPreviewEditorProps {
  visible: boolean;
  mediaUri: string;
  mediaType: 'photo' | 'video';
  onClose: () => void;
  onPost: (caption: string, audioCaption?: AudioCaption, textOverlays?: TextOverlay[]) => void;
  isPosting?: boolean;
}

export const MediaPreviewEditor: React.FC<MediaPreviewEditorProps> = ({
  visible,
  mediaUri,
  mediaType,
  onClose,
  onPost,
  isPosting = false,
}) => {
  const [caption, setCaption] = useState('');
  const [isMuted, setIsMuted] = useState(false);
  const [showEditingTools, setShowEditingTools] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [audioCaption, setAudioCaption] = useState<AudioCaption | null>(null);
  const [showAudioRecorder, setShowAudioRecorder] = useState(false);
  const [textOverlays, setTextOverlays] = useState<TextOverlay[]>([]);
  const [showTextOverlayEditor, setShowTextOverlayEditor] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showVideoTrimmer, setShowVideoTrimmer] = useState(false);
  const [showImageCropper, setShowImageCropper] = useState(false);

  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const zoomAnim = useRef(new Animated.Value(1)).current;
  const captionInputRef = useRef<TextInput>(null);
  
  // Video player for video preview
  const player = useVideoPlayer(mediaType === 'video' ? mediaUri : '', (player) => {
    player.loop = true;
    player.muted = isMuted;
  });

  // Animation effects
  useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
      
      // Auto-play video if it's a video
      if (mediaType === 'video' && player) {
        setTimeout(() => {
          try {
            player.play();
          } catch (error) {
            console.log('Video play error:', error);
          }
        }, 500);
      }
    } else {
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 300,
        useNativeDriver: true,
      }).start();
      
      // Pause video when closing
      if (mediaType === 'video' && player) {
        try {
          player.pause();
        } catch (error) {
          console.log('Video pause error:', error);
        }
      }
    }
  }, [visible, mediaType, player]);

  // Update video mute state
  useEffect(() => {
    if (mediaType === 'video' && player) {
      player.muted = isMuted;
    }
  }, [isMuted, mediaType, player]);

  // Handle video tap (toggle mute)
  const handleVideoTap = () => {
    if (mediaType === 'video') {
      setIsMuted(!isMuted);
    }
  };

  // Handle photo tap (toggle zoom)
  const handlePhotoTap = () => {
    if (mediaType === 'photo') {
      const newZoomState = !isZoomed;
      setIsZoomed(newZoomState);

      Animated.spring(zoomAnim, {
        toValue: newZoomState ? 2 : 1,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    }
  };

  // Handle audio caption recording
  const handleAudioCaptionComplete = (newAudioCaption: AudioCaption) => {
    setAudioCaption(newAudioCaption);
    setShowAudioRecorder(false);
  };

  const removeAudioCaption = () => {
    setAudioCaption(null);
  };

  // Handle text overlay editing
  const handleTextOverlaySave = (overlays: TextOverlay[]) => {
    setTextOverlays(overlays);
    setShowTextOverlayEditor(false);
  };

  // Handle emoji selection
  const handleEmojiSelect = (emoji: string) => {
    setCaption(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  // Handle video trimming
  const handleVideoTrimComplete = (trimmedUri: string, startTime: number, endTime: number) => {
    // In a real implementation, you would update the mediaUri with the trimmed version
    console.log('Video trimmed:', { trimmedUri, startTime, endTime });
    setShowVideoTrimmer(false);
  };

  // Handle image cropping
  const handleImageCropComplete = (croppedUri: string) => {
    // In a real implementation, you would update the mediaUri with the cropped version
    console.log('Image cropped:', croppedUri);
    setShowImageCropper(false);
  };

  // Handle post
  const handlePost = () => {
    if (isPosting) return;
    onPost(caption.trim(), audioCaption || undefined, textOverlays.length > 0 ? textOverlays : undefined);
  };

  // Handle back
  const handleBack = () => {
    if (isPosting) return;
    onClose();
  };

  // Toggle editing tools
  const toggleEditingTools = () => {
    setShowEditingTools(!showEditingTools);
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={handleBack}
    >
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Background blur */}
        <View style={[StyleSheet.absoluteFill, { backgroundColor: 'rgba(0, 0, 0, 0.95)' }]} />

        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              onPress={handleBack}
              style={styles.headerButton}
              disabled={isPosting}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>

            <Text style={styles.headerTitle}>New Story</Text>

            <TouchableOpacity
              onPress={handlePost}
              style={[styles.postButton, isPosting && styles.postButtonDisabled]}
              disabled={isPosting}
            >
              {isPosting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={styles.postButtonText}>Post</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Media Preview - Flexible height that shrinks when keyboard appears */}
          <View style={styles.mediaContainer}>
            {mediaType === 'photo' ? (
              <TouchableOpacity
                style={styles.photoContainer}
                onPress={handlePhotoTap}
                activeOpacity={1}
              >
                <Animated.View style={{ transform: [{ scale: zoomAnim }] }}>
                  <Image
                    source={{ uri: mediaUri }}
                    style={styles.media}
                    resizeMode="contain"
                    onLoad={() => console.log('Image loaded successfully')}
                    onError={(error) => console.log('Image load error:', error)}
                  />
                  {/* Text Overlays */}
                  <TextOverlayRenderer
                    textOverlays={textOverlays}
                    mediaWidth={SCREEN_HEIGHT * 0.6}
                    mediaHeight={SCREEN_HEIGHT * 0.6}
                  />
                </Animated.View>
                {isZoomed && (
                  <View style={styles.zoomIndicator}>
                    <Text style={styles.zoomText}>Tap to zoom out</Text>
                  </View>
                )}
              </TouchableOpacity>
            ) : (
              <TouchableOpacity 
                style={styles.videoContainer} 
                onPress={handleVideoTap}
                activeOpacity={1}
              >
                <VideoView
                  style={styles.media}
                  player={player}
                  allowsFullscreen={false}
                  allowsPictureInPicture={false}
                  contentFit="cover"
                  nativeControls={false}
                />

                {/* Text Overlays */}
                <TextOverlayRenderer
                  textOverlays={textOverlays}
                  mediaWidth={SCREEN_HEIGHT * 0.6}
                  mediaHeight={SCREEN_HEIGHT * 0.6}
                />

                {/* Mute indicator */}
                {isMuted && (
                  <View style={styles.muteIndicator}>
                    <Ionicons name="volume-mute" size={24} color="white" />
                  </View>
                )}
                
                {/* Video tap hint */}
                <View style={styles.videoHint}>
                  <Text style={styles.videoHintText}>Tap to {isMuted ? 'unmute' : 'mute'}</Text>
                </View>
              </TouchableOpacity>
            )}
            
            {/* Editing tools button */}
            <TouchableOpacity 
              style={styles.editButton} 
              onPress={toggleEditingTools}
              disabled={isPosting}
            >
              <Ionicons name="create-outline" size={24} color="white" />
            </TouchableOpacity>
          </View>

          {/* Caption Input */}
          <View style={styles.captionContainer}>
            <TextInput
              ref={captionInputRef}
              style={styles.captionInput}
              placeholder="Add a caption..."
              placeholderTextColor="#999"
              value={caption}
              onChangeText={setCaption}
              multiline
              maxLength={500}
              editable={!isPosting}
            />
            <Text style={styles.characterCount}>{caption.length}/500</Text>
          </View>

          {/* Editing Tools (if shown) */}
          {showEditingTools && (
            <Animated.View style={styles.editingTools}>
              <TouchableOpacity
                style={styles.editingTool}
                onPress={() => setShowTextOverlayEditor(true)}
                disabled={isPosting}
              >
                <Ionicons name="text" size={24} color="white" />
                <Text style={styles.editingToolText}>Text</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.editingTool}
                onPress={() => setShowEmojiPicker(true)}
                disabled={isPosting}
              >
                <Ionicons name="happy-outline" size={24} color="white" />
                <Text style={styles.editingToolText}>Emoji</Text>
              </TouchableOpacity>

              {mediaType === 'photo' && (
                <TouchableOpacity
                  style={styles.editingTool}
                  onPress={() => setShowImageCropper(true)}
                  disabled={isPosting}
                >
                  <Ionicons name="crop" size={24} color="white" />
                  <Text style={styles.editingToolText}>Crop</Text>
                </TouchableOpacity>
              )}

              {mediaType === 'video' && (
                <TouchableOpacity
                  style={styles.editingTool}
                  onPress={() => setShowVideoTrimmer(true)}
                  disabled={isPosting}
                >
                  <Ionicons name="cut" size={24} color="white" />
                  <Text style={styles.editingToolText}>Trim</Text>
                </TouchableOpacity>
              )}

              {/* Audio Caption for photo posts */}
              {mediaType === 'photo' && (
                <TouchableOpacity
                  style={styles.editingTool}
                  onPress={() => setShowAudioRecorder(true)}
                  disabled={isPosting}
                >
                  <Ionicons name="mic" size={24} color="white" />
                  <Text style={styles.editingToolText}>Voice</Text>
                </TouchableOpacity>
              )}
            </Animated.View>
          )}

          {/* Audio Caption Display */}
          {audioCaption && (
            <View style={styles.audioCaptionContainer}>
              <View style={styles.audioCaptionInfo}>
                <Ionicons
                  name={audioCaption.type === 'voice' ? 'mic' : 'musical-notes'}
                  size={16}
                  color="#667eea"
                />
                <Text style={styles.audioCaptionText}>
                  {audioCaption.type === 'voice' ? 'Voice caption' : 'Audio caption'} • {Math.round(audioCaption.duration)}s
                </Text>
              </View>
              <TouchableOpacity
                style={styles.removeAudioButton}
                onPress={removeAudioCaption}
                disabled={isPosting}
              >
                <Ionicons name="close" size={16} color="#ff4444" />
              </TouchableOpacity>
            </View>
          )}

          {/* Bottom Actions */}
          <View style={styles.bottomActions}>
            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={() => captionInputRef.current?.focus()}
              disabled={isPosting}
            >
              <Ionicons name="chatbubble-outline" size={20} color="white" />
              <Text style={styles.actionButtonText}>Add Caption</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton} disabled={isPosting}>
              <Ionicons name="location-outline" size={20} color="white" />
              <Text style={styles.actionButtonText}>Add Location</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton} disabled={isPosting}>
              <Ionicons name="pricetag-outline" size={20} color="white" />
              <Text style={styles.actionButtonText}>Tag People</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Audio Caption Recorder */}
      <AudioCaptionRecorder
        visible={showAudioRecorder}
        onClose={() => setShowAudioRecorder(false)}
        onRecordingComplete={handleAudioCaptionComplete}
        maxDuration={30}
        type="voice"
      />

      {/* Text Overlay Editor */}
      <TextOverlayEditor
        visible={showTextOverlayEditor}
        mediaWidth={SCREEN_HEIGHT * 0.6}
        mediaHeight={SCREEN_HEIGHT * 0.6}
        onClose={() => setShowTextOverlayEditor(false)}
        onSave={handleTextOverlaySave}
        initialOverlays={textOverlays}
      />

      {/* Emoji Picker */}
      <EmojiPicker
        visible={showEmojiPicker}
        onClose={() => setShowEmojiPicker(false)}
        onEmojiSelect={handleEmojiSelect}
      />

      {/* Video Trimmer */}
      {mediaType === 'video' && (
        <VideoTrimmer
          visible={showVideoTrimmer}
          videoUri={mediaUri}
          onClose={() => setShowVideoTrimmer(false)}
          onTrimComplete={handleVideoTrimComplete}
          maxDuration={60}
        />
      )}

      {/* Image Cropper */}
      {mediaType === 'photo' && (
        <ImageCropper
          visible={showImageCropper}
          imageUri={mediaUri}
          onClose={() => setShowImageCropper(false)}
          onCropComplete={handleImageCropComplete}
          aspectRatio={1} // Square crop by default
        />
      )}
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    backgroundColor: 'black',
    minHeight: SCREEN_HEIGHT, // Ensure full screen height
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 3, // Reduced from 20 to 3 as requested
    flexShrink: 0, // Prevent header from shrinking but allow it to move up
  },
  headerButton: {
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    flex: 1,
  },
  postButton: {
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 60,
    alignItems: 'center',
  },
  postButtonDisabled: {
    backgroundColor: '#666',
  },
  postButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  mediaContainer: {
    height: SCREEN_HEIGHT * 0.5, // Fixed height that works well with ScrollView
    marginHorizontal: 20,
    marginTop: 3, // Reduced top margin to 3px as requested
    marginBottom: 10,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#1a1a1a',
  },
  media: {
    width: '100%',
    height: '100%',
    backgroundColor: '#1a1a1a',
  },
  videoContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  muteIndicator: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    padding: 8,
  },
  videoHint: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  videoHintText: {
    color: 'white',
    fontSize: 12,
  },
  editButton: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 20,
    padding: 8,
  },
  captionContainer: {
    paddingHorizontal: 20,
    paddingVertical: 12, // Reduced padding
    flexShrink: 0, // Prevent caption container from shrinking
  },
  captionInput: {
    color: 'white',
    fontSize: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 80,
    textAlignVertical: 'top',
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  characterCount: {
    color: '#999',
    fontSize: 12,
    textAlign: 'right',
    marginTop: 4,
  },
  editingTools: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    gap: 20,
  },
  editingTool: {
    alignItems: 'center',
    gap: 4,
  },
  editingToolText: {
    color: 'white',
    fontSize: 12,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    gap: 16,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },

  // Photo zoom styles
  photoContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomIndicator: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
  },
  zoomText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  audioCaptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    marginHorizontal: 20,
    marginVertical: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  audioCaptionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  audioCaptionText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  removeAudioButton: {
    padding: 4,
  },
});
