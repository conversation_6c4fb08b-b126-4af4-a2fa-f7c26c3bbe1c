import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  FlatList,
  Image,
  Dimensions,
  ActivityIndicator,
  Animated,
  PanResponder,
  TouchableWithoutFeedback,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as MediaLibrary from 'expo-media-library';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
};

interface MediaPickerModalProps {
  visible: boolean;
  onClose: () => void;
  onCamera: () => void;
  onGallery?: () => void;
  onMediaSelect?: (media: MediaLibrary.Asset) => void;
  allowMultiple?: boolean;
}

export const MediaPickerModal: React.FC<MediaPickerModalProps> = ({
  visible,
  onClose,
  onCamera,
  onGallery,
  onMediaSelect,
  allowMultiple = false,
}) => {
  const [mediaAssets, setMediaAssets] = useState<MediaLibrary.Asset[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<MediaLibrary.Asset[]>([]);

  // Animation and gesture handling
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT * 0.3)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const modalHeight = useRef(new Animated.Value(SCREEN_HEIGHT * 0.7)).current;

  // Gesture handling
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return Math.abs(gestureState.dy) > 10;
      },
      onPanResponderGrant: () => {
        // Start gesture
      },
      onPanResponderMove: (_, gestureState) => {
        const newTranslateY = Math.max(0, gestureState.dy);
        translateY.setValue(newTranslateY);

        // Adjust modal height based on gesture
        const heightRatio = Math.max(0.3, 1 - (gestureState.dy / SCREEN_HEIGHT));
        modalHeight.setValue(SCREEN_HEIGHT * heightRatio);
      },
      onPanResponderRelease: (_, gestureState) => {
        const { dy, vy } = gestureState;

        // If pulled down significantly or with high velocity, close modal
        if (dy > SCREEN_HEIGHT * 0.2 || vy > 0.5) {
          closeModal();
        } else {
          // Snap back to open position
          Animated.parallel([
            Animated.spring(translateY, {
              toValue: 0,
              useNativeDriver: true,
            }),
            Animated.spring(modalHeight, {
              toValue: SCREEN_HEIGHT * 0.7,
              useNativeDriver: false,
            }),
          ]).start();
        }
      },
    })
  ).current;

  // Animation functions
  const openModal = () => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(translateY, {
        toValue: 0,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.spring(modalHeight, {
        toValue: SCREEN_HEIGHT * 0.7,
        tension: 100,
        friction: 8,
        useNativeDriver: false,
      }),
    ]).start();
  };

  const closeModal = () => {
    Animated.parallel([
      Animated.timing(opacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(translateY, {
        toValue: SCREEN_HEIGHT * 0.3,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  useEffect(() => {
    if (visible) {
      requestPermissionAndLoadMedia();
      openModal();
    } else {
      // Reset animation values when modal closes
      translateY.setValue(SCREEN_HEIGHT * 0.3);
      opacity.setValue(0);
      modalHeight.setValue(SCREEN_HEIGHT * 0.7);
    }
  }, [visible]);

  const requestPermissionAndLoadMedia = async () => {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      if (status === 'granted') {
        setHasPermission(true);
        await loadMediaAssets();
      } else {
        setHasPermission(false);
        Alert.alert(
          'Permission Required',
          'Please grant permission to access your photo library.',
          [{ text: 'OK', onPress: closeModal }]
        );
      }
    } catch (error) {
      console.error('Error requesting media library permission:', error);
      Alert.alert('Error', 'Failed to access media library');
    }
  };

  const loadMediaAssets = async () => {
    setLoading(true);
    try {
      const media = await MediaLibrary.getAssetsAsync({
        mediaType: [MediaLibrary.MediaType.photo, MediaLibrary.MediaType.video],
        sortBy: MediaLibrary.SortBy.creationTime,
        first: 50,
      });
      setMediaAssets(media.assets);
    } catch (error) {
      console.error('Error loading media assets:', error);
      Alert.alert('Error', 'Failed to load media');
    } finally {
      setLoading(false);
    }
  };

  const handleMediaSelect = (asset: MediaLibrary.Asset) => {
    if (allowMultiple) {
      const isSelected = selectedMedia.find(item => item.id === asset.id);
      if (isSelected) {
        setSelectedMedia(prev => prev.filter(item => item.id !== asset.id));
      } else {
        setSelectedMedia(prev => [...prev, asset]);
      }
    } else {
      onMediaSelect?.(asset);
      closeModal();
    }
  };

  const handleConfirmSelection = () => {
    if (selectedMedia.length > 0) {
      // For now, just select the first one
      onMediaSelect?.(selectedMedia[0]);
      closeModal();
    }
  };

  const renderMediaItem = ({ item }: { item: MediaLibrary.Asset }) => {
    const isSelected = selectedMedia.find(media => media.id === item.id);
    const itemSize = (SCREEN_WIDTH - 48) / 3; // 3 columns with padding

    return (
      <TouchableOpacity
        style={[styles.mediaItem, { width: itemSize, height: itemSize }]}
        onPress={() => handleMediaSelect(item)}
      >
        <Image source={{ uri: item.uri }} style={styles.mediaImage} />
        {item.mediaType === MediaLibrary.MediaType.video && (
          <View style={styles.videoIndicator}>
            <Ionicons name="play" size={16} color="#FFFFFF" />
            <Text style={styles.videoDuration}>
              {Math.floor(item.duration / 60)}:{String(Math.floor(item.duration % 60)).padStart(2, '0')}
            </Text>
          </View>
        )}
        {allowMultiple && isSelected && (
          <View style={styles.selectedIndicator}>
            <Ionicons name="checkmark-circle" size={24} color={COLORS.primary} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={closeModal}
    >
      <TouchableWithoutFeedback onPress={closeModal}>
        <Animated.View style={[styles.overlay, { opacity }]}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <Animated.View
              style={[
                styles.modalContainer,
                {
                  height: modalHeight,
                  transform: [{ translateY }],
                }
              ]}
              {...panResponder.panHandlers}
            >
              <SafeAreaView style={styles.container}>
                {/* Drag Handle */}
                <View style={styles.dragHandle} />

                <View style={styles.header}>
                  <TouchableOpacity onPress={closeModal} style={styles.closeButton}>
                    <Ionicons name="close" size={24} color={COLORS.text} />
                  </TouchableOpacity>
                  <Text style={styles.title}>Select Media</Text>
                  {allowMultiple && selectedMedia.length > 0 && (
                    <TouchableOpacity onPress={handleConfirmSelection} style={styles.confirmButton}>
                      <Text style={styles.confirmText}>Done ({selectedMedia.length})</Text>
                    </TouchableOpacity>
                  )}
        </View>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <TouchableOpacity style={styles.actionButton} onPress={onCamera}>
            <Ionicons name="camera" size={24} color={COLORS.primary} />
            <Text style={styles.actionText}>Camera</Text>
          </TouchableOpacity>
          {onGallery && (
            <TouchableOpacity style={styles.actionButton} onPress={onGallery}>
              <Ionicons name="images" size={24} color={COLORS.primary} />
              <Text style={styles.actionText}>Gallery</Text>
            </TouchableOpacity>
          )}
        </View>

        {/* Media Grid */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading media...</Text>
          </View>
        ) : !hasPermission ? (
          <View style={styles.permissionContainer}>
            <Ionicons name="images-outline" size={64} color={COLORS.textMuted} />
            <Text style={styles.permissionText}>Permission Required</Text>
            <Text style={styles.permissionSubtext}>
              Grant access to your photo library to select media
            </Text>
            <TouchableOpacity style={styles.permissionButton} onPress={requestPermissionAndLoadMedia}>
              <Text style={styles.permissionButtonText}>Grant Permission</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            data={mediaAssets}
            renderItem={renderMediaItem}
            keyExtractor={(item) => item.id}
            numColumns={3}
            contentContainerStyle={styles.mediaGrid}
            showsVerticalScrollIndicator={false}
          />
        )}
              </SafeAreaView>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    overflow: 'hidden',
  },
  dragHandle: {
    width: 40,
    height: 4,
    backgroundColor: COLORS.textMuted,
    borderRadius: 2,
    alignSelf: 'center',
    marginTop: 8,
    marginBottom: 16,
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  closeButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  confirmButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  confirmText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.surfaceLight,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginRight: 12,
  },
  actionText: {
    color: COLORS.text,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
  mediaGrid: {
    padding: 16,
  },
  mediaItem: {
    margin: 2,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  mediaImage: {
    width: '100%',
    height: '100%',
  },
  videoIndicator: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  videoDuration: {
    color: '#FFFFFF',
    fontSize: 10,
    marginLeft: 2,
  },
  selectedIndicator: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 12,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.textSecondary,
    fontSize: 16,
    marginTop: 16,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  permissionText: {
    color: COLORS.text,
    fontSize: 18,
    fontWeight: '600',
    marginTop: 16,
  },
  permissionSubtext: {
    color: COLORS.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
  permissionButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});
