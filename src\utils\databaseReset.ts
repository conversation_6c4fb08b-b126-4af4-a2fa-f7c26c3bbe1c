/**
 * Database Reset Utility
 * Helps fix database initialization issues
 */

import * as SQLite from 'expo-sqlite';
import * as FileSystem from 'expo-file-system';

export class DatabaseResetService {
  private static instance: DatabaseResetService;

  static getInstance(): DatabaseResetService {
    if (!DatabaseResetService.instance) {
      DatabaseResetService.instance = new DatabaseResetService();
    }
    return DatabaseResetService.instance;
  }

  /**
   * Reset all databases by deleting database files
   */
  async resetAllDatabases(): Promise<void> {
    try {
      console.log('🔄 Starting database reset...');

      // List of database files to delete
      const dbFiles = [
        'irachat.db',
        'irachat_v2.db',
        'irachat_messages.db',
        'irachat_updates.db',
        'irachat-journal',
        'irachat_v2-journal',
        'irachat_messages-journal',
        'irachat_updates-journal',
        'irachat-wal',
        'irachat_v2-wal',
        'irachat_messages-wal',
        'irachat_updates-wal',
        'irachat-shm',
        'irachat_v2-shm',
        'irachat_messages-shm',
        'irachat_updates-shm'
      ];

      // Get the SQLite directory
      const sqliteDir = `${FileSystem.documentDirectory}SQLite/`;
      
      console.log('📂 Checking SQLite directory:', sqliteDir);

      // Check if SQLite directory exists
      const dirInfo = await FileSystem.getInfoAsync(sqliteDir);
      if (dirInfo.exists) {
        console.log('📁 SQLite directory exists, listing files...');
        
        try {
          const files = await FileSystem.readDirectoryAsync(sqliteDir);
          console.log('📄 Found database files:', files);

          // Delete each database file
          for (const file of files) {
            if (dbFiles.some(dbFile => file.includes(dbFile.split('.')[0]))) {
              const filePath = `${sqliteDir}${file}`;
              try {
                await FileSystem.deleteAsync(filePath);
                console.log(`🗑️ Deleted: ${file}`);
              } catch (deleteError) {
                console.warn(`⚠️ Could not delete ${file}:`, deleteError);
              }
            }
          }
        } catch (readError) {
          console.warn('⚠️ Could not read SQLite directory:', readError);
        }
      } else {
        console.log('📁 SQLite directory does not exist yet');
      }

      console.log('✅ Database reset completed');
    } catch (error) {
      console.error('❌ Database reset failed:', error);
      throw error;
    }
  }

  /**
   * Test database creation with simple operations
   */
  async testDatabaseCreation(): Promise<boolean> {
    let testDb: SQLite.SQLiteDatabase | null = null;
    
    try {
      console.log('🧪 Testing database creation...');
      
      // Try to create a simple test database
      testDb = await SQLite.openDatabaseAsync('test_db.db');
      console.log('✅ Test database opened');

      // Test basic operations
      await testDb.execAsync('CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, name TEXT)');
      console.log('✅ Test table created');

      await testDb.runAsync('INSERT INTO test_table (name) VALUES (?)', ['test']);
      console.log('✅ Test insert successful');

      const result = await testDb.getFirstAsync('SELECT * FROM test_table WHERE name = ?', ['test']);
      console.log('✅ Test select successful:', result);

      // Clean up test database
      await testDb.closeAsync();
      testDb = null;

      // Delete test database file
      try {
        const testDbPath = `${FileSystem.documentDirectory}SQLite/test_db.db`;
        await FileSystem.deleteAsync(testDbPath);
        console.log('✅ Test database cleaned up');
      } catch (cleanupError) {
        console.warn('⚠️ Could not cleanup test database:', cleanupError);
      }

      console.log('✅ Database test completed successfully');
      return true;
    } catch (error) {
      console.error('❌ Database test failed:', error);
      
      // Clean up on error
      if (testDb) {
        try {
          await testDb.closeAsync();
        } catch (closeError) {
          console.warn('⚠️ Could not close test database:', closeError);
        }
      }
      
      return false;
    }
  }

  /**
   * Get database diagnostics
   */
  async getDatabaseDiagnostics(): Promise<{
    sqliteDirectory: string;
    directoryExists: boolean;
    files: string[];
    canCreateDatabase: boolean;
  }> {
    const sqliteDir = `${FileSystem.documentDirectory}SQLite/`;
    
    try {
      const dirInfo = await FileSystem.getInfoAsync(sqliteDir);
      let files: string[] = [];
      
      if (dirInfo.exists) {
        try {
          files = await FileSystem.readDirectoryAsync(sqliteDir);
        } catch (readError) {
          console.warn('Could not read directory:', readError);
        }
      }

      const canCreateDatabase = await this.testDatabaseCreation();

      return {
        sqliteDirectory: sqliteDir,
        directoryExists: dirInfo.exists,
        files,
        canCreateDatabase
      };
    } catch (error) {
      console.error('Error getting diagnostics:', error);
      return {
        sqliteDirectory: sqliteDir,
        directoryExists: false,
        files: [],
        canCreateDatabase: false
      };
    }
  }
}

export const databaseResetService = DatabaseResetService.getInstance();
