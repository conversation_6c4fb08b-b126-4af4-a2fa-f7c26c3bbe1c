import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Image,
} from 'react-native';
import { PanGestureHandler, PinchGestureHandler, State } from 'react-native-gesture-handler';
import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface PhotoCropperProps {
  imageUri: string;
  onCropComplete: (croppedUri: string) => void;
  onCancel: () => void;
  aspectRatio?: number; // width/height ratio, default is free crop
}

export const PhotoCropper: React.FC<PhotoCropperProps> = ({
  imageUri,
  onCropComplete,
  onCancel,
  aspectRatio,
}) => {
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [cropArea, setCropArea] = useState({
    x: 50,
    y: 100,
    width: screenWidth - 100,
    height: screenWidth - 100,
  });
  const [scale, setScale] = useState(1);
  const [lastScale, setLastScale] = useState(1);
  const [isLoading, setIsLoading] = useState(false);

  const containerHeight = screenHeight - 200;
  const maxImageWidth = screenWidth - 40;
  const maxImageHeight = containerHeight - 100;

  React.useEffect(() => {
    Image.getSize(imageUri, (width, height) => {
      const imageAspectRatio = width / height;
      let displayWidth = maxImageWidth;
      let displayHeight = maxImageHeight;

      if (imageAspectRatio > maxImageWidth / maxImageHeight) {
        displayHeight = maxImageWidth / imageAspectRatio;
      } else {
        displayWidth = maxImageHeight * imageAspectRatio;
      }

      setImageSize({ width: displayWidth, height: displayHeight });
      
      // Initialize crop area
      const cropSize = Math.min(displayWidth, displayHeight) * 0.8;
      setCropArea({
        x: (displayWidth - cropSize) / 2,
        y: (displayHeight - cropSize) / 2,
        width: cropSize,
        height: aspectRatio ? cropSize / aspectRatio : cropSize,
      });
    });
  }, [imageUri, aspectRatio]);

  const onPinchGestureEvent = (event: any) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      const newScale = lastScale * event.nativeEvent.scale;
      setScale(Math.max(0.5, Math.min(3, newScale)));
    }
  };

  const onPinchHandlerStateChange = (event: any) => {
    if (event.nativeEvent.oldState === State.ACTIVE) {
      setLastScale(scale);
    }
  };

  const onPanGestureEvent = (event: any) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      const newX = Math.max(0, Math.min(
        imageSize.width - cropArea.width,
        cropArea.x + event.nativeEvent.translationX
      ));
      const newY = Math.max(0, Math.min(
        imageSize.height - cropArea.height,
        cropArea.y + event.nativeEvent.translationY
      ));
      
      setCropArea(prev => ({
        ...prev,
        x: newX,
        y: newY,
      }));
    }
  };

  const onCornerPanGestureEvent = (corner: string) => (event: any) => {
    if (event.nativeEvent.state === State.ACTIVE) {
      const { translationX, translationY } = event.nativeEvent;
      
      setCropArea(prev => {
        let newCrop = { ...prev };
        
        switch (corner) {
          case 'topLeft':
            newCrop.x = Math.max(0, prev.x + translationX);
            newCrop.y = Math.max(0, prev.y + translationY);
            newCrop.width = Math.max(50, prev.width - translationX);
            newCrop.height = aspectRatio 
              ? newCrop.width / aspectRatio 
              : Math.max(50, prev.height - translationY);
            break;
          case 'topRight':
            newCrop.y = Math.max(0, prev.y + translationY);
            newCrop.width = Math.max(50, Math.min(
              imageSize.width - prev.x,
              prev.width + translationX
            ));
            newCrop.height = aspectRatio 
              ? newCrop.width / aspectRatio 
              : Math.max(50, prev.height - translationY);
            break;
          case 'bottomLeft':
            newCrop.x = Math.max(0, prev.x + translationX);
            newCrop.width = Math.max(50, prev.width - translationX);
            newCrop.height = aspectRatio 
              ? newCrop.width / aspectRatio 
              : Math.max(50, Math.min(
                imageSize.height - prev.y,
                prev.height + translationY
              ));
            break;
          case 'bottomRight':
            newCrop.width = Math.max(50, Math.min(
              imageSize.width - prev.x,
              prev.width + translationX
            ));
            newCrop.height = aspectRatio 
              ? newCrop.width / aspectRatio 
              : Math.max(50, Math.min(
                imageSize.height - prev.y,
                prev.height + translationY
              ));
            break;
        }
        
        return newCrop;
      });
    }
  };

  const handleCrop = async () => {
    if (!imageSize.width || !imageSize.height) return;
    
    setIsLoading(true);
    
    try {
      // Calculate crop parameters relative to original image
      Image.getSize(imageUri, async (originalWidth, originalHeight) => {
        const scaleX = originalWidth / imageSize.width;
        const scaleY = originalHeight / imageSize.height;
        
        const cropParams = {
          originX: cropArea.x * scaleX,
          originY: cropArea.y * scaleY,
          width: cropArea.width * scaleX,
          height: cropArea.height * scaleY,
        };
        
        const manipResult = await manipulateAsync(
          imageUri,
          [{ crop: cropParams }],
          { compress: 0.8, format: SaveFormat.JPEG }
        );
        
        onCropComplete(manipResult.uri);
      });
    } catch (error) {
      console.error('Crop failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetCrop = () => {
    const cropSize = Math.min(imageSize.width, imageSize.height) * 0.8;
    setCropArea({
      x: (imageSize.width - cropSize) / 2,
      y: (imageSize.height - cropSize) / 2,
      width: cropSize,
      height: aspectRatio ? cropSize / aspectRatio : cropSize,
    });
    setScale(1);
    setLastScale(1);
  };

  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        <PinchGestureHandler
          onGestureEvent={onPinchGestureEvent}
          onHandlerStateChange={onPinchHandlerStateChange}
        >
          <View style={styles.imageWrapper}>
            <Image
              source={{ uri: imageUri }}
              style={[
                styles.image,
                {
                  width: imageSize.width,
                  height: imageSize.height,
                  transform: [{ scale }],
                }
              ]}
              resizeMode="contain"
            />
            
            {/* Crop overlay */}
            <View style={styles.overlay}>
              {/* Dark overlay areas */}
              <View style={[styles.overlayArea, { height: cropArea.y }]} />
              <View style={styles.overlayRow}>
                <View style={[styles.overlayArea, { width: cropArea.x }]} />
                <View style={styles.cropArea}>
                  {/* Crop border */}
                  <View style={styles.cropBorder} />
                  
                  {/* Corner handles */}
                  <PanGestureHandler onGestureEvent={onCornerPanGestureEvent('topLeft')}>
                    <View style={[styles.cornerHandle, styles.topLeft]} />
                  </PanGestureHandler>
                  <PanGestureHandler onGestureEvent={onCornerPanGestureEvent('topRight')}>
                    <View style={[styles.cornerHandle, styles.topRight]} />
                  </PanGestureHandler>
                  <PanGestureHandler onGestureEvent={onCornerPanGestureEvent('bottomLeft')}>
                    <View style={[styles.cornerHandle, styles.bottomLeft]} />
                  </PanGestureHandler>
                  <PanGestureHandler onGestureEvent={onCornerPanGestureEvent('bottomRight')}>
                    <View style={[styles.cornerHandle, styles.bottomRight]} />
                  </PanGestureHandler>
                  
                  {/* Center pan handle */}
                  <PanGestureHandler onGestureEvent={onPanGestureEvent}>
                    <View style={styles.centerHandle} />
                  </PanGestureHandler>
                </View>
                <View style={[styles.overlayArea, { flex: 1 }]} />
              </View>
              <View style={[styles.overlayArea, { flex: 1 }]} />
            </View>
          </View>
        </PinchGestureHandler>
      </View>

      <View style={styles.controls}>
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
            <Ionicons name="close" size={24} color="#6B7280" />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.resetButton} onPress={resetCrop}>
            <Ionicons name="refresh" size={24} color="#6B7280" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.doneButton, isLoading && styles.disabledButton]} 
            onPress={handleCrop}
            disabled={isLoading}
          >
            {isLoading ? (
              <Text style={styles.loadingText}>...</Text>
            ) : (
              <Ionicons name="checkmark" size={24} color="white" />
            )}
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  imageWrapper: {
    position: 'relative',
  },
  image: {
    backgroundColor: '#000',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  overlayArea: {
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  overlayRow: {
    flexDirection: 'row',
    height: 0, // Will be set dynamically
  },
  cropArea: {
    position: 'relative',
    width: 0, // Will be set dynamically
    height: 0, // Will be set dynamically
  },
  cropBorder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 2,
    borderColor: '#87CEEB',
    borderStyle: 'dashed',
  },
  cornerHandle: {
    position: 'absolute',
    width: 20,
    height: 20,
    backgroundColor: '#87CEEB',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'white',
  },
  topLeft: {
    top: -10,
    left: -10,
  },
  topRight: {
    top: -10,
    right: -10,
  },
  bottomLeft: {
    bottom: -10,
    left: -10,
  },
  bottomRight: {
    bottom: -10,
    right: -10,
  },
  centerHandle: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
  },
  controls: {
    backgroundColor: '#1F2937',
    padding: 20,
    paddingBottom: 40,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cancelButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#374151',
    justifyContent: 'center',
    alignItems: 'center',
  },
  resetButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#374151',
    justifyContent: 'center',
    alignItems: 'center',
  },
  doneButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#87CEEB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#6B7280',
  },
  loadingText: {
    color: 'white',
    fontSize: 16,
  },
});
