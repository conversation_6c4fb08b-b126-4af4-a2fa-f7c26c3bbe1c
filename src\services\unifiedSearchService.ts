/**
 * Unified Search Service for IraChat
 * Handles search across all tabs: Feed, Business, Education
 */

import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs,
  startAfter,
  DocumentSnapshot
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';
import { businessService } from './businessService';
import { BusinessPost, SearchFilters, ProductCategory } from '../types/Business';

// Unified search result types
export interface SearchResult {
  id: string;
  type: 'feed' | 'business' | 'education';
  title: string;
  description: string;
  imageUrl?: string;
  videoUrl?: string;
  userName?: string;
  businessName?: string;
  price?: number;
  currency?: string;
  location?: string;
  tags: string[];
  timestamp: Date;
  relevanceScore: number;
}

export interface UnifiedSearchFilters {
  query?: string;
  type?: 'all' | 'feed' | 'business' | 'education';
  category?: string;
  location?: {
    city?: string;
    district?: string;
    region?: string;
  };
  priceRange?: {
    min?: number;
    max?: number;
  };
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  sortBy?: 'relevance' | 'newest' | 'oldest' | 'price_low' | 'price_high' | 'most_viewed' | 'most_liked';
  isVerified?: boolean;
}

export interface SearchResponse {
  success: boolean;
  results: SearchResult[];
  hasMore: boolean;
  nextCursor?: string;
  totalCount: number;
  error?: string;
}

class UnifiedSearchService {
  private isOnline: boolean = true;
  private searchCache = new Map<string, SearchResult[]>();

  constructor() {
    // Monitor network state
    networkStateManager.addListener('unifiedSearchService', (networkState: any) => {
      this.isOnline = networkState.isConnected || false;
    }, 1);
  }

  /**
   * Main search method that searches across all content types
   */
  async search(
    filters: UnifiedSearchFilters,
    pageSize: number = 20,
    cursor?: string
  ): Promise<SearchResponse> {
    try {
      const cacheKey = this.generateCacheKey(filters, cursor);
      
      // Check cache first for quick results
      if (this.searchCache.has(cacheKey)) {
        const cachedResults = this.searchCache.get(cacheKey)!;
        return {
          success: true,
          results: cachedResults,
          hasMore: cachedResults.length === pageSize,
          totalCount: cachedResults.length,
        };
      }

      let allResults: SearchResult[] = [];

      if (this.isOnline) {
        try {
          // Search online across different content types
          const searchPromises: Promise<SearchResult[]>[] = [];

          if (!filters.type || filters.type === 'all' || filters.type === 'feed') {
            searchPromises.push(this.searchFeedContent(filters, pageSize));
          }

          if (!filters.type || filters.type === 'all' || filters.type === 'business') {
            searchPromises.push(this.searchBusinessContent(filters, pageSize));
          }

          if (!filters.type || filters.type === 'all' || filters.type === 'education') {
            searchPromises.push(this.searchEducationContent(filters, pageSize));
          }

          const results = await Promise.all(searchPromises);
          allResults = results.flat();

          // Cache results
          this.searchCache.set(cacheKey, allResults);
        } catch (error) {
          console.error('❌ Online search failed, falling back to offline:', error);
          // Fall through to offline search
        }
      }

      // If online search failed or we're offline, search cached content
      if (allResults.length === 0) {
        allResults = await this.searchOfflineContent(filters, pageSize);
      }

      // Sort and filter results
      const sortedResults = this.sortResults(allResults, filters.sortBy || 'relevance');
      const paginatedResults = this.paginateResults(sortedResults, pageSize, cursor);

      return {
        success: true,
        results: paginatedResults,
        hasMore: paginatedResults.length === pageSize,
        totalCount: sortedResults.length,
      };
    } catch (error) {
      console.error('❌ Search error:', error);
      return {
        success: false,
        results: [],
        hasMore: false,
        totalCount: 0,
        error: 'Search failed. Please try again.',
      };
    }
  }

  /**
   * Search feed content (updates/stories)
   */
  private async searchFeedContent(filters: UnifiedSearchFilters, limit: number): Promise<SearchResult[]> {
    try {
      // TODO: Implement feed content search when feed service is available
      // For now, return empty array
      return [];
    } catch (error) {
      console.error('❌ Error searching feed content:', error);
      return [];
    }
  }

  /**
   * Search business marketplace content
   */
  private async searchBusinessContent(filters: UnifiedSearchFilters, limit: number): Promise<SearchResult[]> {
    try {
      const businessFilters: SearchFilters = {
        query: filters.query,
        category: filters.category as ProductCategory,
        location: filters.location,
        priceRange: filters.priceRange,
        sortBy: filters.sortBy as any,
        isVerified: filters.isVerified,
      };

      const businessPosts = await businessService.getMarketplacePosts(businessFilters);
      
      return businessPosts.map(post => this.convertBusinessPostToSearchResult(post));
    } catch (error) {
      console.error('❌ Error searching business content:', error);
      return [];
    }
  }

  /**
   * Search education/skills content
   */
  private async searchEducationContent(filters: UnifiedSearchFilters, limit: number): Promise<SearchResult[]> {
    try {
      const educationFilters: SearchFilters = {
        query: filters.query,
        category: filters.category as ProductCategory,
        location: filters.location,
        priceRange: filters.priceRange,
        sortBy: filters.sortBy as any,
        isVerified: filters.isVerified,
        businessType: 'school',
      };

      const educationPosts = await businessService.getEducationPosts(educationFilters);
      
      return educationPosts.map(post => this.convertBusinessPostToSearchResult(post, 'education'));
    } catch (error) {
      console.error('❌ Error searching education content:', error);
      return [];
    }
  }

  /**
   * Search offline cached content
   */
  private async searchOfflineContent(filters: UnifiedSearchFilters, limit: number): Promise<SearchResult[]> {
    try {
      const database = offlineDatabaseService.getDatabase();
      let results: SearchResult[] = [];

      // Search business posts offline
      if (!filters.type || filters.type === 'all' || filters.type === 'business') {
        const businessResults = await this.searchOfflineBusinessPosts(database, filters, limit);
        results = results.concat(businessResults);
      }

      // Search education posts offline
      if (!filters.type || filters.type === 'all' || filters.type === 'education') {
        const educationResults = await this.searchOfflineEducationPosts(database, filters, limit);
        results = results.concat(educationResults);
      }

      // TODO: Add feed content search when available

      return results;
    } catch (error) {
      console.error('❌ Error searching offline content:', error);
      return [];
    }
  }

  /**
   * Search offline business posts
   */
  private async searchOfflineBusinessPosts(database: any, filters: UnifiedSearchFilters, limit: number): Promise<SearchResult[]> {
    try {
      let query = `
        SELECT * FROM business_posts 
        WHERE isActive = 1 AND businessType != 'education'
      `;
      const params: any[] = [];

      if (filters.query) {
        query += ` AND (title LIKE ? OR description LIKE ?)`;
        const searchTerm = `%${filters.query}%`;
        params.push(searchTerm, searchTerm);
      }

      if (filters.category) {
        query += ` AND category = ?`;
        params.push(filters.category);
      }

      if (filters.isVerified !== undefined) {
        query += ` AND isVerified = ?`;
        params.push(filters.isVerified ? 1 : 0);
      }

      query += ` ORDER BY createdAt DESC LIMIT ?`;
      params.push(limit);

      const results = await database.getAllAsync(query, params);
      
      return results.map((result: any) => ({
        id: result.id,
        type: 'business' as const,
        title: result.title,
        description: result.description,
        imageUrl: this.extractFirstImageUrl(result.media),
        businessName: result.businessName,
        price: result.price,
        currency: result.currency,
        location: this.extractLocationString(result.location),
        tags: JSON.parse(result.tags || '[]'),
        timestamp: new Date(result.createdAt),
        relevanceScore: this.calculateRelevanceScore(result.title, result.description, filters.query),
      }));
    } catch (error) {
      console.error('❌ Error searching offline business posts:', error);
      return [];
    }
  }

  /**
   * Search offline education posts
   */
  private async searchOfflineEducationPosts(database: any, filters: UnifiedSearchFilters, limit: number): Promise<SearchResult[]> {
    try {
      let query = `
        SELECT * FROM business_posts 
        WHERE isActive = 1 AND businessType = 'education'
      `;
      const params: any[] = [];

      if (filters.query) {
        query += ` AND (title LIKE ? OR description LIKE ?)`;
        const searchTerm = `%${filters.query}%`;
        params.push(searchTerm, searchTerm);
      }

      query += ` ORDER BY createdAt DESC LIMIT ?`;
      params.push(limit);

      const results = await database.getAllAsync(query, params);
      
      return results.map((result: any) => ({
        id: result.id,
        type: 'education' as const,
        title: result.title,
        description: result.description,
        imageUrl: this.extractFirstImageUrl(result.media),
        businessName: result.businessName,
        price: result.price,
        currency: result.currency,
        location: this.extractLocationString(result.location),
        tags: JSON.parse(result.tags || '[]'),
        timestamp: new Date(result.createdAt),
        relevanceScore: this.calculateRelevanceScore(result.title, result.description, filters.query),
      }));
    } catch (error) {
      console.error('❌ Error searching offline education posts:', error);
      return [];
    }
  }

  /**
   * Convert business post to search result
   */
  private convertBusinessPostToSearchResult(post: BusinessPost, type: 'business' | 'education' = 'business'): SearchResult {
    return {
      id: post.id,
      type,
      title: post.title,
      description: post.description,
      imageUrl: post.media.find(m => m.type === 'image')?.url,
      videoUrl: post.media.find(m => m.type === 'video')?.url,
      businessName: post.businessName,
      price: post.price,
      currency: post.currency,
      location: `${post.location.city}, ${post.location.district}`,
      tags: post.tags,
      timestamp: post.createdAt,
      relevanceScore: 1.0, // Will be calculated based on search query
    };
  }

  /**
   * Helper methods
   */
  private generateCacheKey(filters: UnifiedSearchFilters, cursor?: string): string {
    return JSON.stringify({ filters, cursor });
  }

  private extractFirstImageUrl(mediaJson: string): string | undefined {
    try {
      const media = JSON.parse(mediaJson || '[]');
      return media.find((m: any) => m.type === 'image')?.url;
    } catch {
      return undefined;
    }
  }

  private extractLocationString(locationJson: string): string {
    try {
      const location = JSON.parse(locationJson || '{}');
      return `${location.city || ''}, ${location.district || ''}`.trim().replace(/^,|,$/, '');
    } catch {
      return '';
    }
  }

  private calculateRelevanceScore(title: string, description: string, query?: string): number {
    if (!query) return 1.0;
    
    const searchTerm = query.toLowerCase();
    const titleLower = title.toLowerCase();
    const descLower = description.toLowerCase();
    
    let score = 0;
    
    // Exact title match gets highest score
    if (titleLower === searchTerm) score += 10;
    else if (titleLower.includes(searchTerm)) score += 5;
    
    // Description match
    if (descLower.includes(searchTerm)) score += 2;
    
    // Word boundary matches
    const words = searchTerm.split(' ');
    words.forEach(word => {
      if (titleLower.includes(word)) score += 1;
      if (descLower.includes(word)) score += 0.5;
    });
    
    return Math.max(score, 0.1);
  }

  private sortResults(results: SearchResult[], sortBy: string): SearchResult[] {
    switch (sortBy) {
      case 'newest':
        return results.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      case 'oldest':
        return results.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      case 'price_low':
        return results.sort((a, b) => (a.price || 0) - (b.price || 0));
      case 'price_high':
        return results.sort((a, b) => (b.price || 0) - (a.price || 0));
      case 'relevance':
      default:
        return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }
  }

  private paginateResults(results: SearchResult[], pageSize: number, cursor?: string): SearchResult[] {
    // Simple pagination for now - can be enhanced with proper cursor-based pagination
    const startIndex = cursor ? parseInt(cursor) : 0;
    return results.slice(startIndex, startIndex + pageSize);
  }

  /**
   * Clear search cache
   */
  clearCache(): void {
    this.searchCache.clear();
  }

  /**
   * Get search suggestions based on query
   */
  async getSearchSuggestions(query: string, limit: number = 5): Promise<string[]> {
    try {
      if (!query.trim()) return [];

      // Get suggestions from cached searches and popular terms
      const suggestions: string[] = [];
      
      // TODO: Implement suggestion logic based on:
      // - Popular search terms
      // - User's search history
      // - Business names and categories
      // - Product titles
      
      return suggestions.slice(0, limit);
    } catch (error) {
      console.error('❌ Error getting search suggestions:', error);
      return [];
    }
  }
}

export const unifiedSearchService = new UnifiedSearchService();
