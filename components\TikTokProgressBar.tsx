import React, { useEffect, useRef, useState } from 'react';
import { View, StyleSheet, Dimensions, PanResponder, Animated, Text } from 'react-native';

interface TikTokProgressBarProps {
  currentTime: number;
  duration: number;
  videoId: string;
  onSeek?: (time: number) => void;
  onPause?: () => void;
  onResume?: () => void;
  onHide?: () => void;
  isVisible?: boolean;
}

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const PROGRESS_HEIGHT = 3;
const SCRUBBER_SIZE = 12;
const AUTO_HIDE_DELAY = 2500; // 2.5 seconds like TikTok
const PROGRESS_BAR_TOUCH_AREA = 30; // Last 30px at bottom for touch detection

export const TikTokProgressBar: React.FC<TikTokProgressBarProps> = ({
  currentTime,
  duration,
  videoId,
  onSeek,
  onPause,
  onResume,
  onHide,
  isVisible = false,
}) => {
  // Animation values
  const opacity = useRef(new Animated.Value(0)).current;
  const progressWidth = useRef(new Animated.Value(0)).current;
  const scrubberPosition = useRef(new Animated.Value(0)).current;
  const tooltipOpacity = useRef(new Animated.Value(0)).current;

  // State
  const [isDragging, setIsDragging] = useState(false);
  const [dragProgress, setDragProgress] = useState(0);
  const [tooltipPosition, setTooltipPosition] = useState(0);
  const [tooltipTime, setTooltipTime] = useState('0:00');
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate progress
  const progress = duration > 0 ? currentTime / duration : 0;
  const displayProgress = isDragging ? dragProgress : progress;

  // Format time for tooltip
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  
  // Show/hide animation
  const showProgressBar = () => {
    console.log('🎯 TikTok Progress: Showing');
    Animated.timing(opacity, {
      toValue: 1,
      duration: 150, // Faster show animation like TikTok
      useNativeDriver: true,
    }).start();

    // Clear existing timeout
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
    }

    // Set auto-hide timeout (only if not dragging)
    if (!isDragging) {
      hideTimeoutRef.current = setTimeout(() => {
        hideProgressBar();
      }, AUTO_HIDE_DELAY);
    }
  };
  
  const hideProgressBar = () => {
    if (!isDragging) {
      console.log('🎯 TikTok Progress: Hiding');
      Animated.timing(opacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start(() => {
        // Call onHide callback to update parent state
        if (onHide) {
          onHide();
        }
      });
    }
  };
  
  // Update progress animation
  useEffect(() => {
    if (!isDragging && duration > 0) {
      const targetWidth = (progress * (SCREEN_WIDTH - 40)); // 20px padding on each side
      const targetPosition = progress * (SCREEN_WIDTH - 40 - SCRUBBER_SIZE);
      
      Animated.parallel([
        Animated.timing(progressWidth, {
          toValue: targetWidth,
          duration: 100,
          useNativeDriver: false,
        }),
        Animated.timing(scrubberPosition, {
          toValue: targetPosition,
          duration: 100,
          useNativeDriver: false,
        }),
      ]).start();
    }
  }, [progress, duration, isDragging]);
  
  // Show when visible prop changes
  useEffect(() => {
    if (isVisible) {
      console.log(`🎯 TikTok Progress [${videoId}]: Showing progress bar (duration=${duration})`);
      showProgressBar();
    } else {
      hideProgressBar();
    }
  }, [isVisible]);
  
  // Pan responder for TikTok-style progress bar interaction
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => {
        // Always respond to touches on the progress bar area
        console.log(`🎯 TikTok Progress: Touch on progress bar area`);
        return true;
      },

      onMoveShouldSetPanResponder: (_, gestureState) => {
        // Respond to any movement (horizontal or vertical)
        const isMovement = Math.abs(gestureState.dx) > 2 || Math.abs(gestureState.dy) > 2;
        return isMovement;
      },

      onPanResponderGrant: (evt) => {
        console.log('🎯 TikTok Progress: Started scrubbing - PAUSE VIDEO');
        setIsDragging(true);

        // PAUSE VIDEO when scrubbing starts
        if (onPause) {
          onPause();
        }

        // Clear auto-hide timeout
        if (hideTimeoutRef.current) {
          clearTimeout(hideTimeoutRef.current);
        }

        // Calculate initial position
        const touchX = evt.nativeEvent.locationX;
        const progressBarWidth = SCREEN_WIDTH - 40;
        const newProgress = Math.max(0, Math.min(1, touchX / progressBarWidth));
        setDragProgress(newProgress);

        // Update tooltip
        const newTime = newProgress * duration;
        setTooltipTime(formatTime(newTime));
        setTooltipPosition(touchX);

        // Show tooltip
        Animated.timing(tooltipOpacity, {
          toValue: 1,
          duration: 150,
          useNativeDriver: true,
        }).start();

        // Update visual position immediately
        const targetWidth = newProgress * progressBarWidth;
        const targetPosition = newProgress * (progressBarWidth - SCRUBBER_SIZE);

        progressWidth.setValue(targetWidth);
        scrubberPosition.setValue(targetPosition);
      },

      onPanResponderMove: (evt) => {
        const touchX = evt.nativeEvent.locationX;
        const progressBarWidth = SCREEN_WIDTH - 40;
        const newProgress = Math.max(0, Math.min(1, touchX / progressBarWidth));

        setDragProgress(newProgress);

        // Update tooltip
        const newTime = newProgress * duration;
        setTooltipTime(formatTime(newTime));
        setTooltipPosition(touchX);

        // Update visual position
        const targetWidth = newProgress * progressBarWidth;
        const targetPosition = newProgress * (progressBarWidth - SCRUBBER_SIZE);

        progressWidth.setValue(targetWidth);
        scrubberPosition.setValue(targetPosition);

        console.log(`🎯 TikTok Progress: Scrubbing to ${(newProgress * 100).toFixed(1)}% (${formatTime(newTime)})`);
      },

      onPanResponderRelease: () => {
        console.log('🎯 TikTok Progress: Released scrub - SEEK & RESUME');
        setIsDragging(false);

        // Hide tooltip
        Animated.timing(tooltipOpacity, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }).start();

        // Seek to the dragged position
        if (onSeek && duration > 0) {
          const seekTime = dragProgress * duration;
          console.log(`🎯 TikTok Progress: Seeking to ${seekTime.toFixed(1)}s`);
          onSeek(seekTime);
        }

        // RESUME VIDEO after seeking
        if (onResume) {
          onResume();
        }

        // Restart auto-hide timer
        hideTimeoutRef.current = setTimeout(() => {
          hideProgressBar();
        }, AUTO_HIDE_DELAY);
      },
    })
  ).current;
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current);
      }
    };
  }, []);
  
  // Always render the progress bar when visible, even if duration is 0
  if (!isVisible) {
    return null;
  }

  return (
    <>
      {/* Progress bar with touch area */}
      <Animated.View style={[styles.container, { opacity }]}>
        <View style={styles.progressContainer} {...panResponder.panHandlers}>
          {/* Background track */}
          <View style={styles.progressTrack} />

          {/* Progress fill */}
          <Animated.View
            style={[
              styles.progressFill,
              { width: progressWidth }
            ]}
          />

          {/* Scrubber dot */}
          <Animated.View
            style={[
              styles.scrubber,
              {
                left: scrubberPosition,
                opacity: isDragging ? 1 : 0.8,
                transform: [{ scale: isDragging ? 1.2 : 1 }]
              }
            ]}
          />
        </View>
      </Animated.View>

      {/* Floating tooltip */}
      {isDragging && (
        <Animated.View
          style={[
            styles.tooltip,
            {
              opacity: tooltipOpacity,
              left: Math.max(20, Math.min(SCREEN_WIDTH - 60, tooltipPosition - 30))
            }
          ]}
        >
          <Text style={styles.tooltipText}>{tooltipTime}</Text>
        </Animated.View>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 60, // Moved up to be above the bottom tab bar (60px + 30px margin)
    left: 20,
    right: 20,
    height: 40, // Larger touch area
    justifyContent: 'center',
    zIndex: 1000,
  },
  progressContainer: {
    height: 40, // Larger touch area
    justifyContent: 'center',
    position: 'relative',
    paddingVertical: 10, // Add padding for easier touch
  },
  progressTrack: {
    height: PROGRESS_HEIGHT,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: PROGRESS_HEIGHT / 2,
  },
  progressFill: {
    position: 'absolute',
    height: PROGRESS_HEIGHT,
    backgroundColor: '#0066FF', // Cobalt blue like TikTok
    borderRadius: PROGRESS_HEIGHT / 2,
    shadowColor: '#0066FF',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 2,
  },
  scrubber: {
    position: 'absolute',
    width: SCRUBBER_SIZE,
    height: SCRUBBER_SIZE,
    backgroundColor: '#FFFFFF',
    borderRadius: SCRUBBER_SIZE / 2,
    top: (40 - SCRUBBER_SIZE) / 2, // Center in 40px container
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
  },
  tooltip: {
    position: 'absolute',
    bottom: 130, // Above progress bar (90px + 40px)
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    zIndex: 1001,
  },
  tooltipText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});
