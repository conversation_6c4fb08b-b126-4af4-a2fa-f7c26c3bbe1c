// Firebase Timestamp Serialization Utilities with Offline Support
import { Timestamp } from "firebase/firestore";
import { Dimensions } from "react-native";

// Get device dimensions for responsive formatting
const { width: SCREEN_WIDTH } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;

// Serialization result interface
export interface SerializationResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  fallbackUsed?: boolean;
}

/**
 * Enhanced timestamp serialization with error handling
 */
export const serializeTimestamp = (timestamp: any): string => {
  try {
    if (!timestamp) {
      return new Date().toISOString();
    }

    // Handle Firebase Timestamp
    if (
      timestamp &&
      typeof timestamp === "object" &&
      "seconds" in timestamp &&
      "nanoseconds" in timestamp
    ) {
      const date = new Date(
        timestamp.seconds * 1000 + timestamp.nanoseconds / 1000000,
      );
      return date.toISOString();
    }

    // Handle Firestore Timestamp
    if (timestamp instanceof Timestamp) {
      return timestamp.toDate().toISOString();
    }

    // Handle Date object
    if (timestamp instanceof Date) {
      return timestamp.toISOString();
    }

    // Handle ISO string (already serialized)
    if (typeof timestamp === "string") {
      // Validate ISO string format
      const parsed = new Date(timestamp);
      if (!isNaN(parsed.getTime())) {
        return timestamp;
      }
    }

    // Handle number (Unix timestamp)
    if (typeof timestamp === "number") {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return date.toISOString();
      }
    }

    // Fallback to current time (removed console.warn)
    return new Date().toISOString();
  } catch (error) {
    // Error in serialization, return current time
    return new Date().toISOString();
  }
};

/**
 * Safe timestamp serialization with result info
 */
export const serializeTimestampSafe = (timestamp: any): SerializationResult<string> => {
  try {
    if (!timestamp) {
      return {
        success: true,
        data: new Date().toISOString(),
        fallbackUsed: true
      };
    }

    // Handle Firebase Timestamp
    if (
      timestamp &&
      typeof timestamp === "object" &&
      "seconds" in timestamp &&
      "nanoseconds" in timestamp
    ) {
      const date = new Date(
        timestamp.seconds * 1000 + timestamp.nanoseconds / 1000000,
      );
      return {
        success: true,
        data: date.toISOString()
      };
    }

    // Handle Firestore Timestamp
    if (timestamp instanceof Timestamp) {
      return {
        success: true,
        data: timestamp.toDate().toISOString()
      };
    }

    // Handle Date object
    if (timestamp instanceof Date) {
      return {
        success: true,
        data: timestamp.toISOString()
      };
    }

    // Handle ISO string (already serialized)
    if (typeof timestamp === "string") {
      const parsed = new Date(timestamp);
      if (!isNaN(parsed.getTime())) {
        return {
          success: true,
          data: timestamp
        };
      }
    }

    // Handle number (Unix timestamp)
    if (typeof timestamp === "number") {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return {
          success: true,
          data: date.toISOString()
        };
      }
    }

    // Unknown format, use fallback
    return {
      success: false,
      data: new Date().toISOString(),
      error: "Unknown timestamp format",
      fallbackUsed: true
    };
  } catch (error) {
    return {
      success: false,
      data: new Date().toISOString(),
      error: error instanceof Error ? error.message : "Serialization failed",
      fallbackUsed: true
    };
  }
};

/**
 * Enhanced timestamp deserialization with error handling
 */
export const deserializeTimestamp = (isoString: string): Date => {
  try {
    if (!isoString || typeof isoString !== 'string') {
      return new Date();
    }

    const date = new Date(isoString);
    if (isNaN(date.getTime())) {
      return new Date();
    }

    return date;
  } catch (error) {
    // Invalid ISO string, return current time (removed console.warn)
    return new Date();
  }
};

/**
 * Safe timestamp deserialization with result info
 */
export const deserializeTimestampSafe = (isoString: string): SerializationResult<Date> => {
  try {
    if (!isoString || typeof isoString !== 'string') {
      return {
        success: false,
        data: new Date(),
        error: "Invalid input: not a string",
        fallbackUsed: true
      };
    }

    const date = new Date(isoString);
    if (isNaN(date.getTime())) {
      return {
        success: false,
        data: new Date(),
        error: "Invalid ISO string format",
        fallbackUsed: true
      };
    }

    return {
      success: true,
      data: date
    };
  } catch (error) {
    return {
      success: false,
      data: new Date(),
      error: error instanceof Error ? error.message : "Deserialization failed",
      fallbackUsed: true
    };
  }
};

/**
 * Enhanced Firestore timestamp conversion with error handling
 */
export const toFirestoreTimestamp = (timestamp: any): Timestamp => {
  try {
    if (!timestamp) {
      return Timestamp.now();
    }

    // Handle Firebase Timestamp (already correct)
    if (timestamp instanceof Timestamp) {
      return timestamp;
    }

    // Handle Date object
    if (timestamp instanceof Date) {
      if (isNaN(timestamp.getTime())) {
        return Timestamp.now();
      }
      return Timestamp.fromDate(timestamp);
    }

    // Handle ISO string
    if (typeof timestamp === "string") {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return Timestamp.now();
      }
      return Timestamp.fromDate(date);
    }

    // Handle number (Unix timestamp)
    if (typeof timestamp === "number") {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return Timestamp.now();
      }
      return Timestamp.fromDate(date);
    }

    // Handle Firebase Timestamp object
    if (
      timestamp &&
      typeof timestamp === "object" &&
      "seconds" in timestamp &&
      "nanoseconds" in timestamp
    ) {
      return new Timestamp(timestamp.seconds, timestamp.nanoseconds);
    }

    // Fallback to current time (removed console.warn)
    return Timestamp.now();
  } catch (error) {
    // Error in conversion, return current time
    return Timestamp.now();
  }
};

/**
 * Safe Firebase timestamp to Date conversion
 */
export const safeTimestampToDate = (timestamp: any): Date => {
  try {
    if (!timestamp) {
      return new Date();
    }

    // Handle Firebase Timestamp with toDate method
    if (timestamp && typeof timestamp === 'object' && timestamp.toDate && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }

    // Handle numeric timestamp (milliseconds)
    if (typeof timestamp === 'number') {
      return new Date(timestamp);
    }

    // Handle Date object
    if (timestamp instanceof Date) {
      return isNaN(timestamp.getTime()) ? new Date() : timestamp;
    }

    // Handle ISO string
    if (typeof timestamp === 'string') {
      const date = new Date(timestamp);
      return isNaN(date.getTime()) ? new Date() : date;
    }

    // Handle Firestore Timestamp-like object with seconds/nanoseconds
    if (timestamp && typeof timestamp === 'object' && 'seconds' in timestamp) {
      return new Date(timestamp.seconds * 1000 + (timestamp.nanoseconds || 0) / 1000000);
    }

    // Fallback to current date
    return new Date();
  } catch (error) {
    console.warn('Error converting timestamp to Date:', error);
    return new Date();
  }
};

/**
 * Safe Firestore timestamp conversion with result info
 */
export const toFirestoreTimestampSafe = (timestamp: any): SerializationResult<Timestamp> => {
  try {
    if (!timestamp) {
      return {
        success: true,
        data: Timestamp.now(),
        fallbackUsed: true
      };
    }

    // Handle Firebase Timestamp (already correct)
    if (timestamp instanceof Timestamp) {
      return {
        success: true,
        data: timestamp
      };
    }

    // Handle Date object
    if (timestamp instanceof Date) {
      if (isNaN(timestamp.getTime())) {
        return {
          success: false,
          data: Timestamp.now(),
          error: "Invalid Date object",
          fallbackUsed: true
        };
      }
      return {
        success: true,
        data: Timestamp.fromDate(timestamp)
      };
    }

    // Handle ISO string
    if (typeof timestamp === "string") {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return {
          success: false,
          data: Timestamp.now(),
          error: "Invalid ISO string",
          fallbackUsed: true
        };
      }
      return {
        success: true,
        data: Timestamp.fromDate(date)
      };
    }

    // Handle number (Unix timestamp)
    if (typeof timestamp === "number") {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) {
        return {
          success: false,
          data: Timestamp.now(),
          error: "Invalid Unix timestamp",
          fallbackUsed: true
        };
      }
      return {
        success: true,
        data: Timestamp.fromDate(date)
      };
    }

    // Handle Firebase Timestamp object
    if (
      timestamp &&
      typeof timestamp === "object" &&
      "seconds" in timestamp &&
      "nanoseconds" in timestamp
    ) {
      return {
        success: true,
        data: new Timestamp(timestamp.seconds, timestamp.nanoseconds)
      };
    }

    // Unknown format
    return {
      success: false,
      data: Timestamp.now(),
      error: "Unknown timestamp format",
      fallbackUsed: true
    };
  } catch (error) {
    return {
      success: false,
      data: Timestamp.now(),
      error: error instanceof Error ? error.message : "Conversion failed",
      fallbackUsed: true
    };
  }
};

/**
 * Enhanced chat serialization for Redux state with error handling
 */
export const serializeChat = (chat: any): any => {
  try {
    if (!chat || typeof chat !== 'object') {
      return {};
    }

    return {
      ...chat,
      lastMessageAt: chat.lastMessageAt
        ? serializeTimestamp(chat.lastMessageAt)
        : undefined,
      timestamp: chat.timestamp
        ? serializeTimestamp(chat.timestamp)
        : serializeTimestamp(new Date()),
      createdAt: chat.createdAt
        ? serializeTimestamp(chat.createdAt)
        : undefined,
      updatedAt: chat.updatedAt
        ? serializeTimestamp(chat.updatedAt)
        : undefined,
    };
  } catch (error) {
    return {
      ...chat,
      timestamp: serializeTimestamp(new Date()),
    };
  }
};

/**
 * Enhanced array serialization with error handling
 */
export const serializeChats = (chats: any[]): any[] => {
  try {
    if (!Array.isArray(chats)) {
      return [];
    }
    return chats.map(serializeChat).filter(chat => chat && typeof chat === 'object');
  } catch (error) {
    return [];
  }
};

/**
 * Enhanced chat deserialization with error handling
 */
export const deserializeChat = (chat: any): any => {
  try {
    if (!chat || typeof chat !== 'object') {
      return {};
    }

    return {
      ...chat,
      lastMessageAt: chat.lastMessageAt
        ? deserializeTimestamp(chat.lastMessageAt)
        : undefined,
      timestamp: chat.timestamp
        ? deserializeTimestamp(chat.timestamp)
        : new Date(),
      createdAt: chat.createdAt
        ? deserializeTimestamp(chat.createdAt)
        : undefined,
      updatedAt: chat.updatedAt
        ? deserializeTimestamp(chat.updatedAt)
        : undefined,
    };
  } catch (error) {
    return {
      ...chat,
      timestamp: new Date(),
    };
  }
};

/**
 * Enhanced human-readable time string with mobile optimization
 */
export const getTimeString = (timestamp: any): string => {
  try {
    let date: Date;

    if (typeof timestamp === "string") {
      date = deserializeTimestamp(timestamp);
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (timestamp && typeof timestamp === "object" && "seconds" in timestamp) {
      date = new Date(timestamp.seconds * 1000);
    } else if (typeof timestamp === "number") {
      date = new Date(timestamp);
    } else {
      date = new Date();
    }

    // Validate date
    if (isNaN(date.getTime())) {
      return isSmallDevice ? "Unknown" : "Unknown time";
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);

    // Use shorter formats for small devices
    if (isSmallDevice) {
      if (diffMinutes < 1) return "now";
      if (diffMinutes < 60) return `${diffMinutes}m`;
      if (diffHours < 24) return `${diffHours}h`;
      if (diffDays < 7) return `${diffDays}d`;
      if (diffWeeks < 4) return `${diffWeeks}w`;
      if (diffMonths < 12) return `${diffMonths}mo`;
      return date.getFullYear().toString();
    }

    // Full format for larger devices
    if (diffMinutes < 1) {
      return "Just now";
    } else if (diffMinutes < 60) {
      return `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else if (diffWeeks < 4) {
      return `${diffWeeks}w ago`;
    } else if (diffMonths < 12) {
      return `${diffMonths}mo ago`;
    } else {
      return date.toLocaleDateString();
    }
  } catch (error) {
    // Error formatting time string (removed console.warn)
    return isSmallDevice ? "Unknown" : "Unknown time";
  }
};

// Offline serialization support
/**
 * Serialize data for offline storage
 */
export const serializeForOffline = (data: any): string => {
  try {
    if (!data) return '{}';

    // Handle different data types
    if (typeof data === 'string') return data;
    if (typeof data === 'number' || typeof data === 'boolean') {
      return JSON.stringify(data);
    }

    // Recursively serialize timestamps in objects
    const serialized = serializeTimestampsInObject(data);
    return JSON.stringify(serialized);
  } catch (error) {
    return '{}';
  }
};

/**
 * Deserialize data from offline storage
 */
export const deserializeFromOffline = (jsonString: string): any => {
  try {
    if (!jsonString || typeof jsonString !== 'string') return {};

    const parsed = JSON.parse(jsonString);
    return deserializeTimestampsInObject(parsed);
  } catch (error) {
    return {};
  }
};

/**
 * Recursively serialize timestamps in an object
 */
const serializeTimestampsInObject = (obj: any): any => {
  if (!obj || typeof obj !== 'object') return obj;

  if (Array.isArray(obj)) {
    return obj.map(serializeTimestampsInObject);
  }

  const result: any = {};
  for (const [key, value] of Object.entries(obj)) {
    if (value instanceof Date ||
        value instanceof Timestamp ||
        (value && typeof value === 'object' && 'seconds' in value)) {
      result[key] = serializeTimestamp(value);
    } else if (value && typeof value === 'object') {
      result[key] = serializeTimestampsInObject(value);
    } else {
      result[key] = value;
    }
  }

  return result;
};

/**
 * Recursively deserialize timestamps in an object
 */
const deserializeTimestampsInObject = (obj: any): any => {
  if (!obj || typeof obj !== 'object') return obj;

  if (Array.isArray(obj)) {
    return obj.map(deserializeTimestampsInObject);
  }

  const result: any = {};
  for (const [key, value] of Object.entries(obj)) {
    // Check if this looks like a timestamp field
    if (typeof value === 'string' &&
        (key.includes('At') || key.includes('Time') || key === 'timestamp') &&
        value.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)) {
      result[key] = deserializeTimestamp(value);
    } else if (value && typeof value === 'object') {
      result[key] = deserializeTimestampsInObject(value);
    } else {
      result[key] = value;
    }
  }

  return result;
};

/**
 * Batch serialize multiple items
 */
export const batchSerialize = (items: any[]): SerializationResult<string[]> => {
  try {
    if (!Array.isArray(items)) {
      return {
        success: false,
        data: [],
        error: "Input is not an array"
      };
    }

    const serialized = items.map(item => serializeForOffline(item));
    return {
      success: true,
      data: serialized
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Batch serialization failed"
    };
  }
};

/**
 * Batch deserialize multiple items
 */
export const batchDeserialize = (jsonStrings: string[]): SerializationResult<any[]> => {
  try {
    if (!Array.isArray(jsonStrings)) {
      return {
        success: false,
        data: [],
        error: "Input is not an array"
      };
    }

    const deserialized = jsonStrings.map(jsonString => deserializeFromOffline(jsonString));
    return {
      success: true,
      data: deserialized
    };
  } catch (error) {
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Batch deserialization failed"
    };
  }
};

export default {
  serializeTimestamp,
  serializeTimestampSafe,
  deserializeTimestamp,
  deserializeTimestampSafe,
  toFirestoreTimestamp,
  toFirestoreTimestampSafe,
  serializeChat,
  serializeChats,
  deserializeChat,
  getTimeString,
  serializeForOffline,
  deserializeFromOffline,
  batchSerialize,
  batchDeserialize,
};
