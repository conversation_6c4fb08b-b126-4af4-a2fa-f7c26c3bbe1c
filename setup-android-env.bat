@echo off
echo Setting up Android environment variables...

REM Set ANDROID_HOME
set ANDROID_HOME=F:\Android\Sdk
echo ANDROID_HOME set to: %ANDROID_HOME%

REM Add Android tools to PATH for this session
set PATH=%PATH%;F:\Android\Sdk\platform-tools;F:\Android\Sdk\cmdline-tools\latest\bin;F:\Android\Sdk\emulator
echo Android tools added to PATH

REM Verify adb is accessible
echo Testing adb...
adb version
if %ERRORLEVEL% EQU 0 (
    echo ✅ ADB is working correctly!
) else (
    echo ❌ ADB is not working. Please check your Android SDK installation.
)

echo.
echo Environment setup complete. You can now run your React Native app.
echo To run on Android: npm run android
echo.
pause
