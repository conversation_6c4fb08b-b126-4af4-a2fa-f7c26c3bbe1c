import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Alert,
  Vibration,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { IRACHAT_COLORS, SHADOWS } from '../../styles/iraChatDesignSystem';
import { ResponsiveScale, ResponsiveTypography, ResponsiveSpacing } from '../../utils/responsiveUtils';
import privacyLockService, { LockType } from '../../services/privacyLockService';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface LockScreenProps {
  onUnlock: () => void;
  onError?: (error: string) => void;
}

const LockScreen: React.FC<LockScreenProps> = ({ onUnlock, onError }) => {
  const [pin, setPin] = useState('');
  const [password, setPassword] = useState('');
  const [lockType, setLockType] = useState<LockType>(LockType.NONE);
  const [isLoading, setIsLoading] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [showPassword, setShowPassword] = useState(false);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    initializeLockScreen();
  }, []);

  const initializeLockScreen = async () => {
    try {
      const config = await privacyLockService.getLockConfig();
      setLockType(config.lockType);

      // Start entrance animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      // If system auth is enabled, try to authenticate immediately
      if (config.lockType === LockType.SYSTEM) {
        setTimeout(() => {
          handleSystemAuth();
        }, 500);
      }
    } catch (error) {
      console.error('Error initializing lock screen:', error);
      onError?.('Failed to initialize lock screen');
    }
  };

  const handleSystemAuth = async () => {
    try {
      setIsLoading(true);

      // Check if system auth is available
      const isAvailable = await privacyLockService.isSystemAuthAvailable();
      if (!isAvailable) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        setAttempts(prev => prev + 1);
        return;
      }

      const success = await privacyLockService.authenticateWithSystem();

      if (success) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        onUnlock();
      } else {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        setAttempts(prev => prev + 1);
      }
    } catch (error) {
      console.error('System authentication error:', error);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      setAttempts(prev => prev + 1);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnlock = async () => {
    if (isLoading) return;

    const credential = lockType === LockType.PIN ? pin : password;
    if (!credential.trim()) {
      shakeAnimation();
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      return;
    }

    try {
      setIsLoading(true);
      const success = await privacyLockService.unlockApp(credential);

      if (success) {
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        
        // Success animation
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.1,
            duration: 150,
            useNativeDriver: true,
          }),
          Animated.timing(fadeAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start(() => {
          onUnlock();
        });
      } else {
        // Failed attempt
        setAttempts(prev => prev + 1);
        setPin('');
        setPassword('');
        shakeAnimation();
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        Vibration.vibrate(200);

        if (attempts >= 4) {
          Alert.alert(
            'Too Many Attempts',
            'Please wait before trying again.',
            [{ text: 'OK' }]
          );
        }
      }
    } catch (error) {
      console.error('Unlock error:', error);
      onError?.('Failed to unlock app');
    } finally {
      setIsLoading(false);
    }
  };

  const shakeAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  };

  const renderPinInput = () => (
    <View style={styles.pinContainer}>
      <Text style={styles.instructionText}>Enter your PIN</Text>
      <View style={styles.pinDotsContainer}>
        {[...Array(6)].map((_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              index < pin.length && styles.pinDotFilled
            ]}
          />
        ))}
      </View>
      <View style={styles.numpadContainer}>
        {[1, 2, 3, 4, 5, 6, 7, 8, 9, '', 0, '⌫'].map((num, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.numpadButton,
              num === '' && styles.numpadButtonEmpty
            ]}
            onPress={() => {
              if (num === '⌫') {
                setPin(prev => prev.slice(0, -1));
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              } else if (typeof num === 'number' && pin.length < 6) {
                setPin(prev => prev + num.toString());
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                
                if (pin.length === 5) {
                  setTimeout(() => handleUnlock(), 100);
                }
              }
            }}
            disabled={num === '' || isLoading}
            activeOpacity={0.7}
          >
            {num !== '' && (
              <Text style={styles.numpadButtonText}>{num}</Text>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderPasswordInput = () => (
    <View style={styles.passwordContainer}>
      <Text style={styles.instructionText}>Enter your password</Text>
      <View style={styles.passwordInputContainer}>
        <TextInput
          style={styles.passwordInput}
          value={password}
          onChangeText={setPassword}
          placeholder="Password"
          placeholderTextColor={IRACHAT_COLORS.textMuted}
          secureTextEntry={!showPassword}
          autoFocus
          onSubmitEditing={handleUnlock}
          editable={!isLoading}
        />
        <TouchableOpacity
          style={styles.passwordToggle}
          onPress={() => setShowPassword(!showPassword)}
          activeOpacity={0.7}
        >
          <Ionicons
            name={showPassword ? 'eye-off' : 'eye'}
            size={20}
            color={IRACHAT_COLORS.textMuted}
          />
        </TouchableOpacity>
      </View>
      <TouchableOpacity
        style={[styles.unlockButton, isLoading && styles.unlockButtonDisabled]}
        onPress={handleUnlock}
        disabled={isLoading || !password.trim()}
        activeOpacity={0.8}
      >
        <LinearGradient
          colors={IRACHAT_COLORS.primaryGradient as any}
          style={styles.unlockButtonGradient}
        >
          <Text style={styles.unlockButtonText}>
            {isLoading ? 'Unlocking...' : 'Unlock'}
          </Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  const renderSystemAuth = () => (
    <View style={styles.systemAuthContainer}>
      <Text style={styles.instructionText}>Use your device authentication</Text>
      <TouchableOpacity
        style={styles.biometricButton}
        onPress={handleSystemAuth}
        disabled={isLoading}
        activeOpacity={0.8}
      >
        <Ionicons name="finger-print" size={60} color={IRACHAT_COLORS.primary} />
        <Text style={styles.biometricText}>
          {isLoading ? 'Authenticating...' : 'Tap to authenticate'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={IRACHAT_COLORS.primary} />
      
      <LinearGradient
        colors={IRACHAT_COLORS.primaryGradient as any}
        style={styles.background}
      >
        <Animated.View
          style={[
            styles.content,
            {
              opacity: fadeAnim,
              transform: [
                { scale: scaleAnim },
                { translateX: shakeAnim }
              ]
            }
          ]}
        >
          {/* App Logo/Title */}
          <View style={styles.header}>
            <Ionicons name="lock-closed" size={60} color="#FFFFFF" />
            <Text style={styles.appTitle}>IraChat</Text>
            <Text style={styles.lockMessage}>App is locked for privacy</Text>
          </View>

          {/* Input based on lock type */}
          {lockType === LockType.PIN && renderPinInput()}
          {lockType === LockType.PASSWORD && renderPasswordInput()}
          {lockType === LockType.SYSTEM && renderSystemAuth()}

          {/* Attempts warning */}
          {attempts > 0 && (
            <Text style={styles.attemptsText}>
              {attempts === 1 ? '1 failed attempt' : `${attempts} failed attempts`}
            </Text>
          )}
        </Animated.View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: ResponsiveSpacing.xl * 2,
  },
  appTitle: {
    fontSize: ResponsiveTypography.fontSize['3xl'],
    lineHeight: ResponsiveTypography.lineHeight.tight,
    color: '#FFFFFF',
    marginTop: ResponsiveSpacing.md,
    fontWeight: 'bold',
  },
  lockMessage: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: ResponsiveSpacing.sm,
    textAlign: 'center',
  },
  instructionText: {
    fontSize: ResponsiveTypography.fontSize.lg,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: ResponsiveSpacing.lg,
  },
  // PIN Input Styles
  pinContainer: {
    alignItems: 'center',
    width: '100%',
  },
  pinDotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: ResponsiveSpacing.xl,
  },
  pinDot: {
    width: ResponsiveScale.spacing(15),
    height: ResponsiveScale.spacing(15),
    borderRadius: ResponsiveScale.borderRadius(7.5),
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: ResponsiveSpacing.sm,
  },
  pinDotFilled: {
    backgroundColor: '#FFFFFF',
    borderColor: '#FFFFFF',
  },
  numpadContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    width: ResponsiveScale.spacing(240),
  },
  numpadButton: {
    width: ResponsiveScale.spacing(70),
    height: ResponsiveScale.spacing(70),
    borderRadius: ResponsiveScale.borderRadius(35),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    margin: ResponsiveSpacing.sm,
    ...SHADOWS.md,
  },
  numpadButtonEmpty: {
    backgroundColor: 'transparent',
  },
  numpadButtonText: {
    fontSize: ResponsiveTypography.fontSize.xl,
    lineHeight: ResponsiveTypography.lineHeight.tight,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  // Password Input Styles
  passwordContainer: {
    width: '100%',
    alignItems: 'center',
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: ResponsiveScale.borderRadius(12),
    paddingHorizontal: ResponsiveSpacing.md,
    marginBottom: ResponsiveSpacing.lg,
    width: '100%',
  },
  passwordInput: {
    flex: 1,
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: '#FFFFFF',
    paddingVertical: ResponsiveSpacing.md,
  },
  passwordToggle: {
    padding: ResponsiveSpacing.sm,
  },
  unlockButton: {
    width: '100%',
    borderRadius: ResponsiveScale.borderRadius(12),
    overflow: 'hidden',
    ...SHADOWS.md,
  },
  unlockButtonDisabled: {
    opacity: 0.6,
  },
  unlockButtonGradient: {
    paddingVertical: ResponsiveSpacing.md,
    alignItems: 'center',
  },
  unlockButtonText: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  // System Auth Styles
  systemAuthContainer: {
    alignItems: 'center',
    width: '100%',
  },
  biometricButton: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: ResponsiveScale.borderRadius(20),
    paddingVertical: ResponsiveSpacing.xl,
    paddingHorizontal: ResponsiveSpacing.lg,
    ...SHADOWS.md,
  },
  biometricText: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: '#FFFFFF',
    marginTop: ResponsiveSpacing.md,
    textAlign: 'center',
  },
  attemptsText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: '#FFB6C1',
    marginTop: ResponsiveSpacing.lg,
    textAlign: 'center',
  },
});

export default LockScreen;
