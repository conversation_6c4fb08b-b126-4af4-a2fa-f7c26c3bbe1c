/**
 * Chat Skeleton Loading Component
 * Provides a smooth skeleton loading experience instead of ugly full-screen loaders
 */

import React from 'react';
import { View, StyleSheet, Animated, Easing } from 'react-native';
import { useEffect, useRef } from 'react';

interface ChatSkeletonProps {
  showHeader?: boolean;
  showMessages?: boolean;
  showInput?: boolean;
}

export const ChatSkeleton: React.FC<ChatSkeletonProps> = ({
  showHeader = true,
  showMessages = true,
  showInput = true,
}) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.timing(shimmerAnim, {
        toValue: 1,
        duration: 1500,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, [shimmerAnim]);

  const shimmerStyle = {
    opacity: shimmerAnim.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: [0.3, 0.7, 0.3],
    }),
  };

  return (
    <View style={styles.container}>
      {/* Header Skeleton */}
      {showHeader && (
        <View style={styles.header}>
          <Animated.View style={[styles.avatar, shimmerStyle]} />
          <View style={styles.headerInfo}>
            <Animated.View style={[styles.nameBar, shimmerStyle]} />
            <Animated.View style={[styles.statusBar, shimmerStyle]} />
          </View>
          <View style={styles.headerActions}>
            <Animated.View style={[styles.actionButton, shimmerStyle]} />
            <Animated.View style={[styles.actionButton, shimmerStyle]} />
          </View>
        </View>
      )}

      {/* Messages Skeleton */}
      {showMessages && (
        <View style={styles.messagesContainer}>
          {/* Received message */}
          <View style={styles.messageLeft}>
            <Animated.View style={[styles.messageAvatar, shimmerStyle]} />
            <View style={styles.messageContent}>
              <Animated.View style={[styles.messageBubbleLeft, shimmerStyle]} />
              <Animated.View style={[styles.messageTimeLeft, shimmerStyle]} />
            </View>
          </View>

          {/* Sent message */}
          <View style={styles.messageRight}>
            <View style={styles.messageContent}>
              <Animated.View style={[styles.messageBubbleRight, shimmerStyle]} />
              <Animated.View style={[styles.messageTimeRight, shimmerStyle]} />
            </View>
          </View>

          {/* Another received message */}
          <View style={styles.messageLeft}>
            <Animated.View style={[styles.messageAvatar, shimmerStyle]} />
            <View style={styles.messageContent}>
              <Animated.View style={[styles.messageBubbleLongLeft, shimmerStyle]} />
              <Animated.View style={[styles.messageTimeLeft, shimmerStyle]} />
            </View>
          </View>

          {/* Another sent message */}
          <View style={styles.messageRight}>
            <View style={styles.messageContent}>
              <Animated.View style={[styles.messageBubbleShortRight, shimmerStyle]} />
              <Animated.View style={[styles.messageTimeRight, shimmerStyle]} />
            </View>
          </View>
        </View>
      )}

      {/* Input Skeleton */}
      {showInput && (
        <View style={styles.inputContainer}>
          <Animated.View style={[styles.inputField, shimmerStyle]} />
          <Animated.View style={[styles.sendButton, shimmerStyle]} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(248, 250, 252, 0.4)',
  },
  
  // Header Skeleton
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(135, 206, 235, 0.95)',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E5E7EB',
  },
  headerInfo: {
    flex: 1,
    marginLeft: 12,
  },
  nameBar: {
    height: 16,
    width: '60%',
    backgroundColor: '#E5E7EB',
    borderRadius: 8,
    marginBottom: 4,
  },
  statusBar: {
    height: 12,
    width: '40%',
    backgroundColor: '#E5E7EB',
    borderRadius: 6,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E5E7EB',
  },

  // Messages Skeleton
  messagesContainer: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  messageLeft: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 16,
  },
  messageRight: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
    marginBottom: 16,
  },
  messageAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#E5E7EB',
    marginRight: 8,
  },
  messageContent: {
    alignItems: 'flex-start',
  },
  messageBubbleLeft: {
    height: 40,
    width: 180,
    backgroundColor: '#E5E7EB',
    borderRadius: 18,
    marginBottom: 4,
  },
  messageBubbleLongLeft: {
    height: 60,
    width: 220,
    backgroundColor: '#E5E7EB',
    borderRadius: 18,
    marginBottom: 4,
  },
  messageBubbleRight: {
    height: 40,
    width: 160,
    backgroundColor: '#DBEAFE',
    borderRadius: 18,
    marginBottom: 4,
  },
  messageBubbleShortRight: {
    height: 40,
    width: 100,
    backgroundColor: '#DBEAFE',
    borderRadius: 18,
    marginBottom: 4,
  },
  messageTimeLeft: {
    height: 10,
    width: 40,
    backgroundColor: '#F3F4F6',
    borderRadius: 5,
  },
  messageTimeRight: {
    height: 10,
    width: 40,
    backgroundColor: '#F3F4F6',
    borderRadius: 5,
    alignSelf: 'flex-end',
  },

  // Input Skeleton
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  inputField: {
    flex: 1,
    height: 40,
    backgroundColor: '#E5E7EB',
    borderRadius: 20,
    marginRight: 8,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E5E7EB',
  },
});

export default ChatSkeleton;
