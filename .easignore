# Dependencies
node_modules/

# Build outputs
android/build/
android/app/build/
ios/build/
.expo/

# Cache directories
.metro-cache/
.gradle/
rn-cache/
npm-cache/
yarn-cache/
temp/

# Development files
*.log
*.tsbuildinfo
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Test files
coverage/
__tests__/
*.test.js
*.test.ts
*.test.tsx

# Documentation
*.md
MEDIA_SYSTEM_DOCUMENTATION.md
NAVIGATION_FIXES_SUMMARY.md
NEW_VALIDATION_RULES_SUMMARY.md
OFFLINE_FUNCTIONALITY_README.md
SECURITY.md

# Scripts and utilities
scripts/
*.py
*.ps1
*.bat
*.sh

# Java installations
java11/
java17/

# Large media files (keep only essential ones)
assets/sounds/
assets/fonts/

# Backup files
*.backup
app.json.backup

# Build scripts
build-*.bat
build-*.ps1
*.msi
*.zip

# Environment setup files
setup-*.bat
setup-*.ps1
install-*.ps1
verify-*.ps1
clean-*.ps1
set-*.bat
deploy-*.sh

# Firebase files (keep only essential)
firebase-setup-instructions.md
firestore-*.rules
firestore-rules-fix.txt
firestore-security-rules.txt

# Test and debug files
test-*.js
debug-*.js
mobile-*.js
