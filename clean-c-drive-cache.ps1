# PowerShell script to clean ALL build cache from C: drive
Write-Host "Cleaning build cache from C: drive..." -ForegroundColor Red
Write-Host "====================================" -ForegroundColor Red

# Stop any running development processes
Write-Host "Stopping development processes..." -ForegroundColor Yellow
try {
    Stop-Process -Name "node" -Force -ErrorAction SilentlyContinue
    Stop-Process -Name "expo" -Force -ErrorAction SilentlyContinue
    Stop-Process -Name "gradle" -Force -ErrorAction SilentlyContinue
    Stop-Process -Name "java" -Force -ErrorAction SilentlyContinue
    Write-Host "✅ Stopped development processes" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Some processes may still be running" -ForegroundColor Yellow
}

# Wait a moment for processes to fully stop
Start-Sleep -Seconds 3

# Cache directories to clean on C: drive
$cDriveCachePaths = @(
    "$env:USERPROFILE\.gradle",
    "$env:USERPROFILE\.android",
    "$env:LOCALAPPDATA\Temp\metro-*",
    "$env:LOCALAPPDATA\Temp\react-*", 
    "$env:LOCALAPPDATA\Temp\expo-*",
    "$env:LOCALAPPDATA\Temp\haste-*",
    "$env:LOCALAPPDATA\Temp\yarn-*",
    "$env:APPDATA\npm-cache",
    "$env:LOCALAPPDATA\npm-cache",
    "C:\Users\<USER>\.expo",
    "C:\Users\<USER>\.npm",
    "C:\temp",
    "C:\tmp",
    "$env:TEMP\metro-*",
    "$env:TEMP\react-*",
    "$env:TEMP\expo-*",
    "$env:TEMP\gradle-*"
)

Write-Host "`nCleaning cache directories..." -ForegroundColor Yellow
$cleanedCount = 0
$failedCount = 0

foreach ($path in $cDriveCachePaths) {
    # Handle wildcard paths
    if ($path -like "*-*") {
        $basePath = Split-Path $path -Parent
        $pattern = Split-Path $path -Leaf
        
        if (Test-Path $basePath) {
            $matchingDirs = Get-ChildItem -Path $basePath -Directory -Name $pattern -ErrorAction SilentlyContinue
            foreach ($dir in $matchingDirs) {
                $fullPath = Join-Path $basePath $dir
                try {
                    Remove-Item $fullPath -Recurse -Force -ErrorAction Stop
                    Write-Host "✅ Cleaned: $fullPath" -ForegroundColor Green
                    $cleanedCount++
                } catch {
                    Write-Host "❌ Failed to clean: $fullPath" -ForegroundColor Red
                    $failedCount++
                }
            }
        }
    } else {
        # Handle regular paths
        if (Test-Path $path) {
            try {
                Remove-Item $path -Recurse -Force -ErrorAction Stop
                Write-Host "✅ Cleaned: $path" -ForegroundColor Green
                $cleanedCount++
            } catch {
                Write-Host "❌ Failed to clean: $path (may be in use)" -ForegroundColor Red
                $failedCount++
            }
        } else {
            Write-Host "✓ Not found: $path" -ForegroundColor Gray
        }
    }
}

Write-Host "`n====================================" -ForegroundColor Red
Write-Host "Cache cleanup summary:" -ForegroundColor White
Write-Host "✅ Cleaned: $cleanedCount directories" -ForegroundColor Green
Write-Host "❌ Failed: $failedCount directories" -ForegroundColor Red
Write-Host "====================================" -ForegroundColor Red

if ($failedCount -gt 0) {
    Write-Host "`nSome cache directories could not be cleaned." -ForegroundColor Yellow
    Write-Host "This is normal if development tools are still running." -ForegroundColor Yellow
    Write-Host "Try running this script again after closing all development tools." -ForegroundColor Yellow
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Run: .\setup-d-drive-cache.ps1" -ForegroundColor White
Write-Host "2. Run: .\set-env.bat" -ForegroundColor White
Write-Host "3. Restart your development environment" -ForegroundColor White
