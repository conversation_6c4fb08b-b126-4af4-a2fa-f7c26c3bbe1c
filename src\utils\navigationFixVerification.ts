/**
 * 🔧 NAVIGATION FIX VERIFICATION
 * Comprehensive test to verify all navigation fixes are working correctly
 */

import { ROUTES } from '../services/navigationService';
import { validateRoute } from './routeValidation';
import { generateNavigationValidationReport } from './navigationValidator';

interface NavigationFixResult {
  issue: string;
  status: 'FIXED' | 'PENDING' | 'FAILED';
  details: string;
}

/**
 * Verify all navigation fixes
 */
export function verifyNavigationFixes(): NavigationFixResult[] {
  const results: NavigationFixResult[] = [];

  // 1. Verify route definition conflicts are resolved
  results.push(verifyRouteDefinitions());

  // 2. Verify registration flow consistency
  results.push(verifyRegistrationFlow());

  // 3. Verify auth navigation paths
  results.push(verifyAuthNavigation());

  // 4. Verify tab navigation
  results.push(verifyTabNavigation());

  // 5. Verify dynamic routes
  results.push(verifyDynamicRoutes());

  return results;
}

/**
 * Verify route definitions are consistent
 */
function verifyRouteDefinitions(): NavigationFixResult {
  try {
    // Check that all route constants are properly defined
    const authRoutes = ROUTES.AUTH;
    const registerRoutes = ROUTES.REGISTER;
    const tabRoutes = ROUTES.TABS;

    // Verify auth routes
    if (!authRoutes.WELCOME || !authRoutes.REGISTER || !authRoutes.PHONE_REGISTER) {
      return {
        issue: 'Route Definition Conflicts',
        status: 'FAILED',
        details: 'Missing required auth routes'
      };
    }

    // Verify register routes
    if (!registerRoutes.INFO || !registerRoutes.MAIN || !registerRoutes.PHONE) {
      return {
        issue: 'Route Definition Conflicts',
        status: 'FAILED',
        details: 'Missing required register routes'
      };
    }

    // Verify tab routes
    if (!tabRoutes.CHATS || !tabRoutes.GROUPS || !tabRoutes.PROFILE) {
      return {
        issue: 'Route Definition Conflicts',
        status: 'FAILED',
        details: 'Missing required tab routes'
      };
    }

    // All route definitions are properly set up - no conflicts to check

    return {
      issue: 'Route Definition Conflicts',
      status: 'FIXED',
      details: 'All route definitions are consistent and conflict-free'
    };
  } catch (error) {
    return {
      issue: 'Route Definition Conflicts',
      status: 'FAILED',
      details: `Error checking route definitions: ${error}`
    };
  }
}

/**
 * Verify registration flow is consistent
 */
function verifyRegistrationFlow(): NavigationFixResult {
  const expectedFlow = [
    '/(auth)/welcome',      // Welcome screen
    '/register-info',            // Info screen (explains phone requirement)
    '/(auth)/phone-register', // Phone registration
    '/(auth)/register',     // Main registration form
    '/(tabs)',              // Main app
  ];

  const flowValidation = expectedFlow.map(route => {
    const validation = validateRoute(route);
    return { route, valid: validation.isValid };
  });

  const invalidRoutes = flowValidation.filter(item => !item.valid);

  if (invalidRoutes.length > 0) {
    return {
      issue: 'Registration Flow Consistency',
      status: 'FAILED',
      details: `Invalid routes in flow: ${invalidRoutes.map(r => r.route).join(', ')}`
    };
  }

  return {
    issue: 'Registration Flow Consistency',
    status: 'FIXED',
    details: 'Registration flow is consistent and all routes are valid'
  };
}

/**
 * Verify auth navigation paths
 */
function verifyAuthNavigation(): NavigationFixResult {
  const authPaths = [
    ROUTES.AUTH.WELCOME,
    ROUTES.AUTH.REGISTER,
    ROUTES.AUTH.PHONE_REGISTER,
    ROUTES.AUTH.INFO,
  ];

  const invalidPaths = authPaths.filter(path => {
    const validation = validateRoute(path);
    return !validation.isValid;
  });

  if (invalidPaths.length > 0) {
    return {
      issue: 'Auth Navigation Paths',
      status: 'FAILED',
      details: `Invalid auth paths: ${invalidPaths.join(', ')}`
    };
  }

  return {
    issue: 'Auth Navigation Paths',
    status: 'FIXED',
    details: 'All auth navigation paths are valid'
  };
}

/**
 * Verify tab navigation
 */
function verifyTabNavigation(): NavigationFixResult {
  const tabPaths = [
    ROUTES.TABS.CHATS,
    ROUTES.TABS.GROUPS,
    ROUTES.TABS.BUSINESS, // Business marketplace tab
    ROUTES.TABS.CALLS,
    ROUTES.TABS.STORIES, // Stories content (video feed)
    ROUTES.TABS.PROFILE,
    ROUTES.TABS.SETTINGS_TAB,
  ];

  const invalidPaths = tabPaths.filter(path => {
    const validation = validateRoute(path);
    return !validation.isValid;
  });

  if (invalidPaths.length > 0) {
    return {
      issue: 'Tab Navigation',
      status: 'FAILED',
      details: `Invalid tab paths: ${invalidPaths.join(', ')}`
    };
  }

  return {
    issue: 'Tab Navigation',
    status: 'FIXED',
    details: 'All tab navigation paths are valid'
  };
}

/**
 * Verify dynamic routes
 */
function verifyDynamicRoutes(): NavigationFixResult {
  // Test dynamic route patterns
  const dynamicTests = [
    { pattern: '/chat/[id]', test: '/chat/123' },
    { pattern: '/update/[id]', test: '/update/456' },
  ];

  const failedTests = dynamicTests.filter(test => {
    const validation = validateRoute(test.test);
    return !validation.isValid;
  });

  if (failedTests.length > 0) {
    return {
      issue: 'Dynamic Routes',
      status: 'FAILED',
      details: `Failed dynamic route tests: ${failedTests.map(t => t.test).join(', ')}`
    };
  }

  return {
    issue: 'Dynamic Routes',
    status: 'FIXED',
    details: 'All dynamic routes are working correctly'
  };
}

/**
 * Generate a comprehensive navigation health report
 */
export function generateNavigationHealthReport(): {
  overall: 'HEALTHY' | 'WARNING' | 'CRITICAL';
  summary: string;
  fixes: NavigationFixResult[];
  validationReport: any;
} {
  const fixes = verifyNavigationFixes();
  const validationReport = generateNavigationValidationReport();

  const failedFixes = fixes.filter(fix => fix.status === 'FAILED');
  const pendingFixes = fixes.filter(fix => fix.status === 'PENDING');

  let overall: 'HEALTHY' | 'WARNING' | 'CRITICAL' = 'HEALTHY';
  let summary = 'All navigation issues have been resolved';

  if (failedFixes.length > 0) {
    overall = 'CRITICAL';
    summary = `${failedFixes.length} critical navigation issues remain`;
  } else if (pendingFixes.length > 0 || validationReport.issues.length > 0) {
    overall = 'WARNING';
    summary = `${pendingFixes.length} navigation issues pending resolution`;
  }

  return {
    overall,
    summary,
    fixes,
    validationReport
  };
}

/**
 * Run navigation fix verification (for development)
 */
export function runNavigationFixVerification(): void {
  if (__DEV__) {
    console.log('🔧 Running Navigation Fix Verification...\n');

    const report = generateNavigationHealthReport();

    console.log(`📊 Navigation Health: ${report.overall}`);
    console.log(`📝 Summary: ${report.summary}\n`);

    console.log('🔧 Fix Results:');
    report.fixes.forEach(fix => {
      const icon = fix.status === 'FIXED' ? '✅' : fix.status === 'FAILED' ? '❌' : '⏳';
      console.log(`${icon} ${fix.issue}: ${fix.status}`);
      console.log(`   ${fix.details}\n`);
    });

    if (report.validationReport.issues.length > 0) {
      console.log('⚠️ Remaining Issues:');
      report.validationReport.issues.forEach((issue: string) => {
        console.log(`  - ${issue}`);
      });
    }

    if (report.overall === 'HEALTHY') {
      console.log('🎉 All navigation fixes have been successfully applied!');
    }
  }
}
