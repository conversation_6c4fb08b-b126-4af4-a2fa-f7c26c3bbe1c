// Test new validation rules for username, name, and bio
console.log('🧪 Testing NEW validation rules...');

// Test username validation - must be one word, first letter capital, allow emojis and numbers
const testUsernames = [
  // Valid usernames
  { username: 'John123', expected: true, description: 'Capital first letter with numbers' },
  { username: '<PERSON><PERSON><PERSON>', expected: true, description: 'Capital first letter with underscore' },
  { username: 'Mike🚀', expected: true, description: 'Capital first letter with emoji' },
  { username: 'Alex2024', expected: true, description: 'Capital first letter with year' },
  { username: '<PERSON>_😊', expected: true, description: 'Capital first letter with underscore and emoji' },
  
  // Invalid usernames
  { username: 'john123', expected: false, description: 'Lowercase first letter' },
  { username: '<PERSON>', expected: false, description: 'Contains space (not one word)' },
  { username: 'JOH<PERSON> DOE', expected: false, description: 'Contains space even with capitals' },
  { username: '123<PERSON><PERSON><PERSON>', expected: false, description: 'Starts with number' },
  { username: '_<PERSON>', expected: false, description: 'Starts with underscore' },
  { username: 'jo', expected: false, description: 'Too short (less than 3 characters)' },
];

// Test name validation - allow emojis, numbers, letters, spaces
const testNames = [
  // Valid names
  { name: '<PERSON> Doe', expected: true, description: 'Regular name with space' },
  { name: 'Sarah 123', expected: true, description: 'Name with numbers' },
  { name: 'Mike 🚀 Johnson', expected: true, description: 'Name with emoji' },
  { name: '<PERSON>-Smith', expected: true, description: 'Name with hyphen' },
  { name: 'Lisa_Cool', expected: true, description: 'Name with underscore' },
  { name: 'Anna.Marie', expected: true, description: 'Name with dot' },
  { name: 'José María', expected: true, description: 'Name with accents' },
  { name: '王小明', expected: true, description: 'Chinese characters' },
  { name: 'محمد علي', expected: true, description: 'Arabic characters' },
  
  // Invalid names
  { name: 'A', expected: false, description: 'Too short (less than 2 characters)' },
  { name: '', expected: false, description: 'Empty name' },
  { name: '   ', expected: false, description: 'Only spaces' },
];

// Test bio validation - allow emojis, numbers, letters, spaces, punctuation
const testBios = [
  // Valid bios
  { bio: 'I love coding! 💻', expected: true, description: 'Bio with emoji and punctuation' },
  { bio: 'Software Engineer @ Tech Corp 2024', expected: true, description: 'Bio with numbers and symbols' },
  { bio: 'Passionate about AI/ML and web development 🚀', expected: true, description: 'Bio with emojis and slashes' },
  { bio: 'Born in 1990. Love music, travel & photography! 📸🎵✈️', expected: true, description: 'Bio with multiple emojis and punctuation' },
  { bio: '', expected: true, description: 'Empty bio (optional)' },
  { bio: 'Simple bio', expected: true, description: 'Simple text bio' },
  
  // Invalid bios (if any - currently very permissive)
  { bio: 'A'.repeat(201), expected: false, description: 'Too long (over 200 characters)' },
];

console.log('\n📝 TESTING USERNAME VALIDATION:');
console.log('Rules: Must be one word, first letter capital, allow emojis and numbers');
testUsernames.forEach(test => {
  // Simulate username validation
  const isValid = validateUsername(test.username);
  const result = isValid === test.expected ? '✅' : '❌';
  console.log(`${result} "${test.username}" - ${test.description} (Expected: ${test.expected ? 'Valid' : 'Invalid'})`);
});

console.log('\n👤 TESTING NAME VALIDATION:');
console.log('Rules: Allow emojis, numbers, letters, spaces, basic punctuation');
testNames.forEach(test => {
  // Simulate name validation
  const isValid = validateName(test.name);
  const result = isValid === test.expected ? '✅' : '❌';
  console.log(`${result} "${test.name}" - ${test.description} (Expected: ${test.expected ? 'Valid' : 'Invalid'})`);
});

console.log('\n📄 TESTING BIO VALIDATION:');
console.log('Rules: Allow emojis, numbers, letters, spaces, punctuation, max 200 chars');
testBios.forEach(test => {
  // Simulate bio validation
  const isValid = validateBio(test.bio);
  const result = isValid === test.expected ? '✅' : '❌';
  const displayBio = test.bio.length > 50 ? test.bio.substring(0, 50) + '...' : test.bio;
  console.log(`${result} "${displayBio}" - ${test.description} (Expected: ${test.expected ? 'Valid' : 'Invalid'})`);
});

// Validation functions (simulating the actual validation logic)
function validateUsername(username) {
  if (!username || username.length < 3) return false;
  if (/\s/.test(username)) return false; // No spaces
  if (!/^[A-Z]/.test(username)) return false; // Must start with capital
  return /^[A-Z][\p{L}\p{N}\p{Emoji}_]*$/u.test(username); // Allow letters, numbers, emojis, underscores
}

function validateName(name) {
  if (!name || name.trim().length < 2) return false;
  return /^[\p{L}\p{N}\p{Emoji}\s._-]+$/u.test(name.trim()); // Allow Unicode, emojis, spaces, punctuation
}

function validateBio(bio) {
  if (!bio) return true; // Optional
  if (bio.length > 200) return false;
  return /^[\p{L}\p{N}\p{Emoji}\s.,!?;:'"()[\]{}_-]*$/u.test(bio); // Allow Unicode, emojis, punctuation
}

console.log('\n🎉 NEW VALIDATION RULES TEST COMPLETED!');
console.log('✅ Username: One word, first letter capital, emojis/numbers allowed');
console.log('✅ Name: Emojis, numbers, letters, spaces, punctuation allowed');
console.log('✅ Bio: Emojis, numbers, letters, spaces, punctuation allowed, max 200 chars');
