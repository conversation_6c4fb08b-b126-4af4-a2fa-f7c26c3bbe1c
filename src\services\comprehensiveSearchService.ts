import { 
  collection, 
  query, 
  where, 
  orderBy, 
  limit, 
  getDocs, 
  Timestamp,
  QueryConstraint 
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { SearchFilters } from '../components/ChatList/EnhancedChatSearch';

export interface SearchResult {
  id: string;
  type: 'chat' | 'message' | 'contact' | 'media';
  title: string;
  subtitle?: string;
  content?: string;
  avatar?: string;
  timestamp?: Date;
  chatId?: string;
  messageId?: string;
  mediaType?: string;
  mediaUrl?: string;
  relevanceScore?: number;
}

export interface SearchOptions {
  query: string;
  filters: SearchFilters;
  userId: string;
  maxResults?: number;
}

class ComprehensiveSearchService {
  private readonly MAX_RESULTS = 50;

  /**
   * Main search function that orchestrates all search types
   */
  async search(options: SearchOptions): Promise<SearchResult[]> {
    const { query, filters, userId, maxResults = this.MAX_RESULTS } = options;
    
    if (!query.trim()) {
      return [];
    }

    try {
      let results: SearchResult[] = [];

      // Search based on filter type
      switch (filters.searchType) {
        case 'chats':
          results = await this.searchChats(query, filters, userId);
          break;
        case 'messages':
          results = await this.searchMessages(query, filters, userId);
          break;
        case 'contacts':
          results = await this.searchContacts(query, filters, userId);
          break;
        case 'media':
          results = await this.searchMedia(query, filters, userId);
          break;
        case 'all':
        default:
          // Search all types and combine results
          const [chats, messages, contacts, media] = await Promise.all([
            this.searchChats(query, filters, userId),
            this.searchMessages(query, filters, userId),
            this.searchContacts(query, filters, userId),
            this.searchMedia(query, filters, userId),
          ]);
          results = [...chats, ...messages, ...contacts, ...media];
          break;
      }

      // Apply time range filter
      results = this.applyTimeRangeFilter(results, filters.timeRange);

      // Sort results
      results = this.sortResults(results, filters.sortBy, query);

      // Limit results
      return results.slice(0, maxResults);
    } catch (error) {
      console.error('❌ Error in comprehensive search:', error);
      return [];
    }
  }

  /**
   * Search chats by name and participants
   */
  private async searchChats(searchQuery: string, filters: SearchFilters, userId: string): Promise<SearchResult[]> {
    try {
      const constraints: QueryConstraint[] = [
        where('participants', 'array-contains', userId)
      ];

      // Apply chat type filter
      if (filters.chatType === 'individual') {
        constraints.push(where('isGroup', '==', false));
      } else if (filters.chatType === 'groups') {
        constraints.push(where('isGroup', '==', true));
      }

      // Apply archived filter
      if (!filters.includeArchived) {
        constraints.push(where('archivedBy', 'not-in', [userId]));
      }

      const q = query(
        collection(db, 'chats'),
        ...constraints,
        orderBy('updatedAt', 'desc'),
        limit(20)
      );

      const snapshot = await getDocs(q);
      const results: SearchResult[] = [];

      snapshot.docs.forEach(doc => {
        const data = doc.data();
        const chatName = data.isGroup ? data.groupName : this.getIndividualChatName(data, userId);
        
        if (chatName && chatName.toLowerCase().includes(searchQuery.toLowerCase())) {
          results.push({
            id: doc.id,
            type: 'chat',
            title: chatName,
            subtitle: data.isGroup ? `${data.participants?.length || 0} members` : 'Individual chat',
            avatar: data.avatar || data.groupAvatar,
            timestamp: data.updatedAt?.toDate(),
            relevanceScore: this.calculateRelevanceScore(chatName, searchQuery),
          });
        }
      });

      return results;
    } catch (error) {
      console.error('❌ Error searching chats:', error);
      return [];
    }
  }

  /**
   * Search messages content
   */
  private async searchMessages(searchQuery: string, filters: SearchFilters, userId: string): Promise<SearchResult[]> {
    try {
      // First get user's chats
      const chatsQuery = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', userId),
        limit(50)
      );

      const chatsSnapshot = await getDocs(chatsQuery);
      const results: SearchResult[] = [];

      // Search messages in each chat
      for (const chatDoc of chatsSnapshot.docs) {
        const chatData = chatDoc.data();
        
        // Skip archived chats if not included
        if (!filters.includeArchived && chatData.archivedBy?.includes(userId)) {
          continue;
        }

        // Skip muted chats if not included
        if (!filters.includeMuted && chatData.mutedBy?.includes(userId)) {
          continue;
        }

        try {
          const messagesQuery = query(
            collection(db, `chats/${chatDoc.id}/messages`),
            orderBy('timestamp', 'desc'),
            limit(100)
          );

          const messagesSnapshot = await getDocs(messagesQuery);

          messagesSnapshot.docs.forEach(messageDoc => {
            const messageData = messageDoc.data();
            
            // Apply message type filter
            if (filters.messageType !== 'all' && messageData.type !== filters.messageType) {
              return;
            }

            // Search in message content
            const content = messageData.text || messageData.content || '';
            if (content.toLowerCase().includes(searchQuery.toLowerCase())) {
              const chatName = chatData.isGroup ? chatData.groupName : this.getIndividualChatName(chatData, userId);
              
              results.push({
                id: messageDoc.id,
                type: 'message',
                title: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
                subtitle: `In ${chatName}`,
                content: content,
                timestamp: messageData.timestamp?.toDate(),
                chatId: chatDoc.id,
                messageId: messageDoc.id,
                relevanceScore: this.calculateRelevanceScore(content, searchQuery),
              });
            }
          });
        } catch (error) {
          console.error(`❌ Error searching messages in chat ${chatDoc.id}:`, error);
        }
      }

      return results;
    } catch (error) {
      console.error('❌ Error searching messages:', error);
      return [];
    }
  }

  /**
   * Search contacts
   */
  private async searchContacts(searchQuery: string, filters: SearchFilters, userId: string): Promise<SearchResult[]> {
    try {
      const q = query(
        collection(db, 'users'),
        orderBy('displayName'),
        limit(20)
      );

      const snapshot = await getDocs(q);
      const results: SearchResult[] = [];

      snapshot.docs.forEach(doc => {
        if (doc.id === userId) return; // Skip current user

        const data = doc.data();
        const name = data.displayName || data.username || '';
        const email = data.email || '';
        const phone = data.phoneNumber || '';

        if (
          name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          phone.includes(searchQuery)
        ) {
          results.push({
            id: doc.id,
            type: 'contact',
            title: name,
            subtitle: email || phone,
            avatar: data.photoURL,
            relevanceScore: this.calculateRelevanceScore(name, searchQuery),
          });
        }
      });

      return results;
    } catch (error) {
      console.error('❌ Error searching contacts:', error);
      return [];
    }
  }

  /**
   * Search media files
   */
  private async searchMedia(searchQuery: string, filters: SearchFilters, userId: string): Promise<SearchResult[]> {
    try {
      // Get user's chats first
      const chatsQuery = query(
        collection(db, 'chats'),
        where('participants', 'array-contains', userId),
        limit(50)
      );

      const chatsSnapshot = await getDocs(chatsQuery);
      const results: SearchResult[] = [];

      // Search media in each chat
      for (const chatDoc of chatsSnapshot.docs) {
        const chatData = chatDoc.data();
        
        if (!filters.includeArchived && chatData.archivedBy?.includes(userId)) {
          continue;
        }

        try {
          const messagesQuery = query(
            collection(db, `chats/${chatDoc.id}/messages`),
            where('type', 'in', ['image', 'video', 'audio', 'document']),
            orderBy('timestamp', 'desc'),
            limit(50)
          );

          const messagesSnapshot = await getDocs(messagesQuery);

          messagesSnapshot.docs.forEach(messageDoc => {
            const messageData = messageDoc.data();
            const fileName = messageData.fileName || '';
            
            if (fileName.toLowerCase().includes(searchQuery.toLowerCase())) {
              const chatName = chatData.isGroup ? chatData.groupName : this.getIndividualChatName(chatData, userId);
              
              results.push({
                id: messageDoc.id,
                type: 'media',
                title: fileName,
                subtitle: `${messageData.type} in ${chatName}`,
                timestamp: messageData.timestamp?.toDate(),
                chatId: chatDoc.id,
                messageId: messageDoc.id,
                mediaType: messageData.type,
                mediaUrl: messageData.fileUrl,
                relevanceScore: this.calculateRelevanceScore(fileName, searchQuery),
              });
            }
          });
        } catch (error) {
          console.error(`❌ Error searching media in chat ${chatDoc.id}:`, error);
        }
      }

      return results;
    } catch (error) {
      console.error('❌ Error searching media:', error);
      return [];
    }
  }

  /**
   * Apply time range filter to results
   */
  private applyTimeRangeFilter(results: SearchResult[], timeRange: string): SearchResult[] {
    if (timeRange === 'all') return results;

    const now = new Date();
    let cutoffDate: Date;

    switch (timeRange) {
      case 'today':
        cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        cutoffDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        cutoffDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        return results;
    }

    return results.filter(result => 
      result.timestamp && result.timestamp >= cutoffDate
    );
  }

  /**
   * Sort search results
   */
  private sortResults(results: SearchResult[], sortBy: string, query: string): SearchResult[] {
    return results.sort((a, b) => {
      switch (sortBy) {
        case 'relevance':
          return (b.relevanceScore || 0) - (a.relevanceScore || 0);
        case 'date':
          if (!a.timestamp && !b.timestamp) return 0;
          if (!a.timestamp) return 1;
          if (!b.timestamp) return -1;
          return b.timestamp.getTime() - a.timestamp.getTime();
        case 'name':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });
  }

  /**
   * Calculate relevance score for search results
   */
  private calculateRelevanceScore(text: string, query: string): number {
    const lowerText = text.toLowerCase();
    const lowerQuery = query.toLowerCase();
    
    // Exact match gets highest score
    if (lowerText === lowerQuery) return 100;
    
    // Starts with query gets high score
    if (lowerText.startsWith(lowerQuery)) return 80;
    
    // Contains query as whole word gets medium score
    if (lowerText.includes(` ${lowerQuery} `) || lowerText.includes(` ${lowerQuery}`)) return 60;
    
    // Contains query gets lower score
    if (lowerText.includes(lowerQuery)) return 40;
    
    // Fuzzy match gets lowest score
    const words = lowerQuery.split(' ');
    const matchedWords = words.filter(word => lowerText.includes(word));
    return (matchedWords.length / words.length) * 20;
  }

  /**
   * Get individual chat name from chat data
   */
  private getIndividualChatName(chatData: any, userId: string): string {
    if (chatData.isGroup) {
      return chatData.groupName || 'Group Chat';
    }
    
    const otherUserId = chatData.participants?.find((id: string) => id !== userId);
    return chatData.participantNames?.[otherUserId] || 'Unknown User';
  }
}

export const comprehensiveSearchService = new ComprehensiveSearchService();
