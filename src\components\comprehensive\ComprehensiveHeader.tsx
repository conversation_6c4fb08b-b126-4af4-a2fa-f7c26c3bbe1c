import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '../../constants/theme';

interface ComprehensiveHeaderProps {
  isOnline: boolean;
  onSearchPress: () => void;
  onNotificationsPress: () => void;
  onProfilePress: () => void;
  onBusinessRegisterPress?: () => void;
  hasBusinessProfile: boolean;
  unreadNotifications: number;
}

export const ComprehensiveHeader: React.FC<ComprehensiveHeaderProps> = ({
  isOnline,
  onSearchPress,
  onNotificationsPress,
  onProfilePress,
  onBusinessRegisterPress,
  hasBusinessProfile,
  unreadNotifications,
}) => {
  return (
    <View style={styles.container}>
      {/* Left side - App title and status */}
      <View style={styles.leftSection}>
        <Text style={styles.title}>Updates</Text>
        <View style={[styles.statusIndicator, { backgroundColor: isOnline ? '#10B981' : '#EF4444' }]}>
          <View style={styles.statusDot} />
          <Text style={styles.statusText}>{isOnline ? 'Online' : 'Offline'}</Text>
        </View>
      </View>

      {/* Right side - Action buttons */}
      <View style={styles.rightSection}>
        {/* Search Button */}
        <TouchableOpacity style={styles.actionButton} onPress={onSearchPress}>
          <Ionicons name="search" size={22} color={COLORS.textSecondary} />
        </TouchableOpacity>

        {/* Business Registration Button (only if no business profile) */}
        {!hasBusinessProfile && onBusinessRegisterPress && (
          <TouchableOpacity style={styles.actionButton} onPress={onBusinessRegisterPress}>
            <Ionicons name="business" size={22} color={COLORS.primary} />
          </TouchableOpacity>
        )}

        {/* Notifications Button */}
        <TouchableOpacity style={styles.actionButton} onPress={onNotificationsPress}>
          <View style={styles.notificationContainer}>
            <Ionicons name="notifications-outline" size={22} color={COLORS.textSecondary} />
            {unreadNotifications > 0 && (
              <View style={styles.notificationBadge}>
                <Text style={styles.notificationCount}>
                  {unreadNotifications > 99 ? '99+' : unreadNotifications.toString()}
                </Text>
              </View>
            )}
          </View>
        </TouchableOpacity>

        {/* Profile Button */}
        <TouchableOpacity style={styles.actionButton} onPress={onProfilePress}>
          <Ionicons name="person-circle-outline" size={22} color={COLORS.textSecondary} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1F2937',
    borderBottomWidth: 1,
    borderBottomColor: '#374151',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    marginRight: 12,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FFFFFF',
    marginRight: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 8,
  },
  notificationContainer: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    backgroundColor: '#EF4444',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationCount: {
    fontSize: 10,
    fontWeight: '700',
    color: '#FFFFFF',
  },
});
