import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';

// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue - IraChat's primary brand color
  primaryDark: '#4682B4',  // Steel Blue - darker variant
  primaryLight: '#B0E0E6', // Powder Blue - lighter variant
  secondary: '#1E90FF',    // Dodger Blue - accent color
  background: '#000000',   // Pure Black
  surface: '#1A1A1A',     // Dark Gray
  surfaceLight: '#2A2A2A', // Lighter Gray
  text: '#FFFFFF',         // White text
  textSecondary: '#B0B0B0', // Gray text
  textMuted: '#808080',    // Muted text
  success: '#00FF7F',      // Spring Green
  warning: '#FFD700',      // Gold
  error: '#FF6B6B',        // Light Red
};

interface CameraCaptureModalProps {
  visible: boolean;
  onClose: () => void;
  onMediaCapture: (uri: string, type: 'image' | 'video') => void;
  isStory?: boolean;
}

export const CameraCaptureModal: React.FC<CameraCaptureModalProps> = ({
  visible,
  onClose,
  onMediaCapture,
  isStory = false,
}) => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [cameraType, setCameraType] = useState<CameraType>('back');
  const [isRecording, setIsRecording] = useState(false);
  const [showNativeCamera, setShowNativeCamera] = useState(false);
  const [cameraMode, setCameraMode] = useState<'picture' | 'video'>('picture');
  const cameraRef = useRef<CameraView>(null);
  const [permission, requestPermission] = useCameraPermissions();

  useEffect(() => {
    if (visible) {
      if (!permission) {
        return;
      }

      if (!permission.granted) {
        requestPermission();
      } else {
        setHasPermission(true);
      }
    } else {
      setShowNativeCamera(false);
      setIsRecording(false);
      setCameraMode('picture');
    }
  }, [visible, permission]);

  useEffect(() => {
    if (permission) {
      setHasPermission(permission.granted);

      if (!permission.granted && visible) {
        Alert.alert(
          'Permission Required',
          'Camera permission is required to take photos and videos',
          [
            { text: 'Cancel', onPress: onClose },
            { text: 'Grant Permission', onPress: requestPermission }
          ]
        );
      }
    }
  }, [permission, visible]);

  const takePicture = async () => {
    if (!cameraRef.current || isCapturing) return;

    try {
      setIsCapturing(true);

      const photo = await cameraRef.current.takePictureAsync({
        quality: 1,
        base64: false,
        skipProcessing: false,
      });

      if (photo?.uri) {
        onMediaCapture(photo.uri, 'image');
        onClose();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take picture. Please try again.');
    } finally {
      setIsCapturing(false);
    }
  };

  const startRecording = async () => {
    if (!cameraRef.current || isRecording) return;

    try {
      setIsRecording(true);

      const video = await cameraRef.current.recordAsync({
        maxDuration: 600, // 10 minutes max for videos
      });

      if (video?.uri) {
        onMediaCapture(video.uri, 'video');
        onClose();
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to record video. Please try again.');
    } finally {
      setIsRecording(false);
    }
  };

  const stopRecording = () => {
    if (!cameraRef.current || !isRecording) return;

    try {
      cameraRef.current.stopRecording();
      setIsRecording(false);
    } catch (error) {
      Alert.alert('Error', 'Failed to stop recording');
      setIsRecording(false);
    }
  };

  const toggleCameraType = () => {
    setCameraType(cameraType === 'back' ? 'front' : 'back');
  };

  const openCameraForPhoto = () => {
    setCameraMode('picture');
    setShowNativeCamera(true);
  };

  const openCameraForVideo = () => {
    setCameraMode('video');
    setShowNativeCamera(true);
  };

  if (!visible) return null;

  // Show native camera interface
  if (showNativeCamera && hasPermission) {
    return (
      <Modal visible={visible} animationType="slide" presentationStyle="fullScreen">
        <View style={styles.cameraContainer}>
          <CameraView
            ref={cameraRef}
            style={styles.camera}
            facing={cameraType}
            mode={cameraMode}
            videoQuality="720p"
          >
            {/* Camera Controls Overlay */}
            <View style={styles.topControls}>
              <TouchableOpacity
                onPress={() => {
                  setShowNativeCamera(false);
                  if (isRecording) {
                    stopRecording();
                  }
                  onClose();
                }}
                style={styles.controlButton}
              >
                <Ionicons name="close" size={26} color="white" />
              </TouchableOpacity>

              <View style={styles.modeIndicator}>
                <Text style={styles.modeText}>
                  {cameraMode === 'picture' ? 'Photo' : 'Video'}
                </Text>
              </View>

              <TouchableOpacity onPress={toggleCameraType} style={styles.controlButton}>
                <Ionicons name="camera-reverse" size={24} color="white" />
              </TouchableOpacity>
            </View>

            {/* Bottom Controls */}
            <View style={styles.bottomControls}>
              <View style={styles.modeSwitcher}>
                <TouchableOpacity
                  onPress={() => setCameraMode('picture')}
                  style={[
                    styles.modeButton,
                    cameraMode === 'picture' && styles.activeModeButton
                  ]}
                >
                  <Text style={styles.modeButtonText}>Photo</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={() => setCameraMode('video')}
                  style={[
                    styles.modeButton,
                    cameraMode === 'video' && styles.activeModeButton
                  ]}
                >
                  <Text style={styles.modeButtonText}>Video</Text>
                </TouchableOpacity>
              </View>

              <Text style={styles.instructionText}>
                {cameraMode === 'picture'
                  ? 'Tap to take a photo'
                  : isRecording
                    ? 'Tap to stop recording'
                    : 'Tap to start recording video'
                }
              </Text>

              <TouchableOpacity
                onPress={cameraMode === 'picture' ? takePicture : (isRecording ? stopRecording : startRecording)}
                disabled={isCapturing}
                style={[
                  styles.captureButton,
                  { 
                    backgroundColor: isRecording ? COLORS.error : '#2A2A2A',
                    borderColor: isRecording ? 'white' : COLORS.primary 
                  }
                ]}
              >
                {isCapturing ? (
                  <ActivityIndicator size="small" color={COLORS.primary} />
                ) : (
                  <Ionicons
                    name={cameraMode === 'picture' ? 'camera' : (isRecording ? 'stop' : 'videocam')}
                    size={32}
                    color={isRecording ? 'white' : COLORS.primary}
                  />
                )}
              </TouchableOpacity>

              {isRecording && (
                <Text style={styles.recordingText}>Recording...</Text>
              )}
            </View>
          </CameraView>
        </View>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {isStory ? 'Create Story' : 'Create Update'}
            </Text>
            <TouchableOpacity onPress={onClose}>
              <Ionicons name="close" size={24} color={COLORS.text} />
            </TouchableOpacity>
          </View>

          {!hasPermission ? (
            <View style={styles.permissionContainer}>
              <Ionicons name="camera-outline" size={48} color={COLORS.textMuted} />
              <Text style={styles.permissionText}>
                Camera permission is required to take photos and videos
              </Text>
              <TouchableOpacity onPress={requestPermission} style={styles.permissionButton}>
                <Text style={styles.permissionButtonText}>Grant Permission</Text>
              </TouchableOpacity>
            </View>
          ) : isCapturing ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={COLORS.primary} />
              <Text style={styles.loadingText}>Opening camera...</Text>
            </View>
          ) : (
            <View>
              <Text style={styles.subtitle}>
                Choose how you want to capture your {isStory ? 'story' : 'update'}
              </Text>

              <TouchableOpacity style={styles.optionButton} onPress={openCameraForPhoto}>
                <Ionicons name="camera" size={24} color={COLORS.primary} />
                <Text style={styles.optionText}>Take Photo</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionButton} onPress={openCameraForVideo}>
                <Ionicons name="videocam" size={24} color={COLORS.primary} />
                <Text style={styles.optionText}>Record Video</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  cameraContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  topControls: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  controlButton: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 20,
    padding: 12,
  },
  modeIndicator: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 15,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  modeText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  modeSwitcher: {
    flexDirection: 'row',
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 20,
    marginBottom: 10,
    padding: 4,
  },
  modeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  activeModeButton: {
    backgroundColor: COLORS.primary,
  },
  modeButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  instructionText: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 20,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingText: {
    color: COLORS.error,
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 10,
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: COLORS.surface,
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxWidth: 400,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    color: COLORS.text,
    fontSize: 18,
    fontWeight: 'bold',
  },
  subtitle: {
    color: COLORS.textSecondary,
    marginBottom: 20,
    textAlign: 'center',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surfaceLight,
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  optionText: {
    color: COLORS.text,
    fontSize: 16,
    marginLeft: 15,
    fontWeight: '500',
  },
  permissionContainer: {
    padding: 40,
    alignItems: 'center',
  },
  permissionText: {
    color: COLORS.textSecondary,
    marginTop: 16,
    textAlign: 'center',
  },
  permissionButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 16,
  },
  permissionButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.textSecondary,
    marginTop: 10,
  },
});
