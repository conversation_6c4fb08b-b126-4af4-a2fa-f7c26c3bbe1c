/**
 * Offline-First Engine for IraChat
 * Integrates all offline functionality into a single, cohesive system
 * This is the main entry point for all offline-first features
 */

import { offlineDatabaseService } from './offlineDatabase';
import { outboxService } from './outboxService';
import { enhancedMediaCacheService } from './enhancedMediaCacheService';
import { avatarVersioningService } from './avatarVersioningService';
import { enhancedContactSyncService } from './enhancedContactSyncService';
import { backgroundSyncWorker } from './backgroundSyncWorker';
import { networkStateManager } from './networkStateManager';
import { iraChatOfflineEngine } from './iraChatOfflineEngine';

export interface OfflineFirstConfig {
  enableBackgroundSync: boolean;
  enableMediaCaching: boolean;
  enableAvatarVersioning: boolean;
  enableContactSync: boolean;
  syncInterval: number;
  maxCacheSize: number;
}

export interface OfflineFirstStats {
  isInitialized: boolean;
  isOnline: boolean;
  database: {
    size: number;
    messageCount: number;
    chatCount: number;
    contactCount: number;
  };
  outbox: {
    pending: number;
    failed: number;
    total: number;
  };
  media: {
    cachedFiles: number;
    totalSize: number;
  };
  avatars: {
    totalCached: number;
    needsCheck: number;
    invalid: number;
  };
  sync: {
    lastSyncTime: number;
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
  };
}

class OfflineFirstEngine {
  private isInitialized = false;
  private config: OfflineFirstConfig = {
    enableBackgroundSync: true,
    enableMediaCaching: true,
    enableAvatarVersioning: true,
    enableContactSync: true,
    syncInterval: 30000, // 30 seconds
    maxCacheSize: 500 * 1024 * 1024, // 500MB
  };

  /**
   * Initialize the complete offline-first system
   */
  async initialize(config?: Partial<OfflineFirstConfig>): Promise<void> {
    if (this.isInitialized) {
      console.log('⚠️ Offline-First Engine already initialized');
      return;
    }

    console.log('🚀 Initializing IraChat Offline-First Engine...');

    // Update config
    if (config) {
      this.config = { ...this.config, ...config };
    }

    try {
      // 1. Initialize core database
      console.log('📊 Initializing offline database...');
      await offlineDatabaseService.initialize();

      // 2. Initialize network state manager
      console.log('🌐 Initializing network state manager...');
      await networkStateManager.initialize();

      // 3. Initialize outbox service
      console.log('📤 Initializing outbox service...');
      await outboxService.initialize();

      // 4. Initialize media caching (if enabled)
      if (this.config.enableMediaCaching) {
        console.log('🎬 Initializing media cache service...');
        await enhancedMediaCacheService.initialize();
      }

      // 5. Initialize avatar versioning (if enabled)
      if (this.config.enableAvatarVersioning) {
        console.log('👤 Initializing avatar versioning service...');
        await avatarVersioningService.initialize();
      }

      // 6. Initialize contact sync (if enabled)
      if (this.config.enableContactSync) {
        console.log('📱 Initializing contact sync service...');
        await enhancedContactSyncService.initialize();
      }

      // 7. Initialize background sync worker (if enabled)
      if (this.config.enableBackgroundSync) {
        console.log('🔄 Initializing background sync worker...');
        await backgroundSyncWorker.initialize({
          enableMessageSync: true,
          enableContactSync: this.config.enableContactSync,
          enableAvatarSync: this.config.enableAvatarVersioning,
          enableMediaSync: this.config.enableMediaCaching,
          syncInterval: this.config.syncInterval,
          maxSyncDuration: 25000,
        });
      }

      // 8. Initialize the original IraChat offline engine
      console.log('💬 Initializing IraChat offline engine...');
      await iraChatOfflineEngine.initialize();

      this.isInitialized = true;
      console.log('✅ IraChat Offline-First Engine initialized successfully!');

      // Log initialization stats
      const stats = await this.getStats();
      console.log('📊 Initialization Stats:', {
        database: stats.database,
        isOnline: stats.isOnline,
      });

    } catch (error) {
      console.error('❌ Failed to initialize Offline-First Engine:', error);
      throw error;
    }
  }

  /**
   * Send a message (works offline)
   */
  async sendMessage(
    chatId: string,
    content: string,
    senderId: string,
    type: 'text' | 'image' | 'video' | 'audio' | 'document' | 'voice' = 'text',
    mediaUrl?: string,
    additionalData?: any
  ): Promise<string | null> {
    try {
      // Send through the original engine first
      const messageType = type === 'voice' ? 'audio' : type === 'document' ? 'file' : type;
      const messageId = await iraChatOfflineEngine.sendMessage(
        chatId,
        content,
        senderId,
        messageType as 'text' | 'image' | 'video' | 'audio' | 'file',
        additionalData
      );

      if (messageId) {
        // Add to outbox for reliable delivery
        await outboxService.addToOutbox({
          messageId,
          chatId,
          senderId,
          content,
          type,
          mediaUrl,
          priority: type === 'text' ? 0 : 1, // Media messages get higher priority
          status: 'pending',
          retryCount: 0,
          maxRetries: 5,
        });
      }

      return messageId;
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      return null;
    }
  }

  /**
   * Get messages for a chat (from local storage)
   */
  async getMessages(chatId: string, limit: number = 50, offset: number = 0): Promise<any[]> {
    return iraChatOfflineEngine.getMessages(chatId, limit, offset);
  }

  /**
   * Get all chats (from local storage)
   */
  async getChats(includeArchived: boolean = false): Promise<any[]> {
    return iraChatOfflineEngine.getChats(includeArchived);
  }

  /**
   * Cache media for offline access
   */
  async cacheMedia(
    messageId: string,
    chatId: string,
    mediaUrl: string,
    mediaType: 'image' | 'video' | 'audio' | 'document',
    fileName?: string
  ): Promise<boolean> {
    if (!this.config.enableMediaCaching) {
      return false;
    }

    try {
      const result = await enhancedMediaCacheService.cacheMedia(
        messageId,
        chatId,
        mediaUrl,
        mediaType,
        fileName
      );
      return result !== null;
    } catch (error) {
      console.error('❌ Failed to cache media:', error);
      return false;
    }
  }

  /**
   * Get cached media
   */
  async getCachedMedia(messageId: string): Promise<any> {
    if (!this.config.enableMediaCaching) {
      return null;
    }

    return enhancedMediaCacheService.getCachedMedia(messageId);
  }

  /**
   * Get avatar with version checking
   */
  async getAvatar(userId: string, size: number = 150): Promise<string | null> {
    if (!this.config.enableAvatarVersioning) {
      return null;
    }

    return avatarVersioningService.getAvatarWithVersionCheck(userId, size);
  }

  /**
   * Sync device contacts
   */
  async syncContacts(currentUserId: string): Promise<any> {
    if (!this.config.enableContactSync) {
      return { totalContacts: 0, iraChatUsers: 0, newContacts: 0, updatedContacts: 0, errors: [] };
    }

    return enhancedContactSyncService.syncDeviceContacts(currentUserId);
  }

  /**
   * Force sync now
   */
  async forceSyncNow(): Promise<void> {
    if (this.config.enableBackgroundSync) {
      await backgroundSyncWorker.forceSyncNow();
    }
  }

  /**
   * Get comprehensive stats
   */
  async getStats(): Promise<OfflineFirstStats> {
    try {
      const [
        databaseStats,
        outboxStats,
        avatarStats,
        backgroundSyncStats
      ] = await Promise.all([
        this.getDatabaseStats(),
        outboxService.getStats(),
        this.config.enableAvatarVersioning ? avatarVersioningService.getSyncStats() : { totalCached: 0, needsCheck: 0, invalid: 0 },
        this.config.enableBackgroundSync ? backgroundSyncWorker.getStats() : { lastSyncTime: 0, totalSyncs: 0, successfulSyncs: 0, failedSyncs: 0 }
      ]);

      return {
        isInitialized: this.isInitialized,
        isOnline: networkStateManager.isOnline(),
        database: databaseStats,
        outbox: outboxStats,
        media: {
          cachedFiles: 0, // Would need to implement this
          totalSize: 0,   // Would need to implement this
        },
        avatars: avatarStats,
        sync: {
          lastSyncTime: backgroundSyncStats.lastSyncTime,
          totalSyncs: backgroundSyncStats.totalSyncs,
          successfulSyncs: backgroundSyncStats.successfulSyncs,
          failedSyncs: backgroundSyncStats.failedSyncs,
        },
      };
    } catch (error) {
      console.error('❌ Failed to get stats:', error);
      return {
        isInitialized: this.isInitialized,
        isOnline: false,
        database: { size: 0, messageCount: 0, chatCount: 0, contactCount: 0 },
        outbox: { pending: 0, failed: 0, total: 0 },
        media: { cachedFiles: 0, totalSize: 0 },
        avatars: { totalCached: 0, needsCheck: 0, invalid: 0 },
        sync: { lastSyncTime: 0, totalSyncs: 0, successfulSyncs: 0, failedSyncs: 0 },
      };
    }
  }

  /**
   * Get database statistics
   */
  private async getDatabaseStats(): Promise<{
    size: number;
    messageCount: number;
    chatCount: number;
    contactCount: number;
  }> {
    try {
      const db = offlineDatabaseService.getDatabase();
      
      const [messageCount, chatCount, contactCount, dbSize] = await Promise.all([
        db.getAllAsync('SELECT COUNT(*) as count FROM messages WHERE isDeleted = 0'),
        db.getAllAsync('SELECT COUNT(*) as count FROM chats WHERE isDeleted = 0'),
        db.getAllAsync('SELECT COUNT(*) as count FROM contacts WHERE isDeleted = 0'),
        offlineDatabaseService.getDatabaseSize()
      ]);

      return {
        size: dbSize,
        messageCount: (messageCount[0] as any)?.count || 0,
        chatCount: (chatCount[0] as any)?.count || 0,
        contactCount: (contactCount[0] as any)?.count || 0,
      };
    } catch (error) {
      console.error('❌ Failed to get database stats:', error);
      return { size: 0, messageCount: 0, chatCount: 0, contactCount: 0 };
    }
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<OfflineFirstConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Update background sync worker config if needed
    if (this.config.enableBackgroundSync && updates.syncInterval) {
      backgroundSyncWorker.updateConfig({
        syncInterval: updates.syncInterval,
      });
    }
  }

  /**
   * Check if system is ready
   */
  isReady(): boolean {
    return this.isInitialized && offlineDatabaseService.isReady();
  }

  /**
   * Check if online
   */
  isOnline(): boolean {
    return networkStateManager.isOnline();
  }

  /**
   * Cleanup and shutdown
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Offline-First Engine...');
    
    if (this.config.enableBackgroundSync) {
      await backgroundSyncWorker.unregister();
    }
    
    if (this.config.enableContactSync) {
      enhancedContactSyncService.stopBackgroundSync();
    }
    
    if (this.config.enableAvatarVersioning) {
      avatarVersioningService.stopBackgroundSync();
    }
    
    outboxService.stopBackgroundProcessing();
    
    console.log('✅ Offline-First Engine shutdown complete');
  }
}

export const offlineFirstEngine = new OfflineFirstEngine();
