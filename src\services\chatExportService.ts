/**
 * Chat Export Service for IraChat
 * Provides functionality to export chat conversations as documents
 * Supports multiple formats and includes media handling
 */

import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as MediaLibrary from 'expo-media-library';
import { Alert, Platform } from 'react-native';
import { offlineDatabaseService } from './offlineDatabase';


export interface ExportOptions {
  format: 'txt' | 'html' | 'json';
  includeMedia: boolean;
  includeSystemMessages: boolean;
  dateRange?: {
    startDate: Date;
    endDate: Date;
  };
  messageTypes?: string[];
}

export interface ExportedMessage {
  id: string;
  content: string;
  type: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  mediaUrl?: string;
  fileName?: string;
  caption?: string;
  isForwarded?: boolean;
  replyTo?: string;
}

export interface ExportResult {
  success: boolean;
  filePath?: string;
  error?: string;
  fileSize?: number;
  messageCount?: number;
}

class ChatExportService {
  private static instance: ChatExportService;

  static getInstance(): ChatExportService {
    if (!ChatExportService.instance) {
      ChatExportService.instance = new ChatExportService();
    }
    return ChatExportService.instance;
  }

  /**
   * Export chat messages from provided messages array (Fixed: For Firebase messages)
   */
  async exportChatFromMessages(
    messages: any[],
    chatName: string,
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      if (messages.length === 0) {
        return { success: false, error: 'No messages found to export' };
      }

      // Convert messages to export format
      const exportMessages: ExportedMessage[] = messages.map(msg => ({
        id: msg.id,
        content: msg.text || '',
        type: msg.type,
        senderId: msg.senderId,
        senderName: msg.senderName || 'Unknown',
        timestamp: msg.timestamp instanceof Date ? msg.timestamp : new Date(msg.timestamp),
        mediaUrl: msg.mediaUrl,
        fileName: msg.fileName,
        caption: msg.text,
        isForwarded: msg.isForwarded || false,
        replyTo: msg.replyTo?.id,
      }));

      // Generate export content based on format
      let content: string;
      let fileName: string;
      let mimeType: string;

      switch (options.format) {
        case 'txt':
          content = await this.generateTextExport(exportMessages, chatName);
          fileName = `${this.sanitizeFileName(chatName)}_chat_export.txt`;
          mimeType = 'text/plain';
          break;
        case 'html':
          content = await this.generateHtmlExport(exportMessages, chatName);
          fileName = `${this.sanitizeFileName(chatName)}_chat_export.html`;
          mimeType = 'text/html';
          break;
        case 'json':
          content = await this.generateJsonExport(exportMessages, chatName);
          fileName = `${this.sanitizeFileName(chatName)}_chat_export.json`;
          mimeType = 'application/json';
          break;
        default:
          return { success: false, error: 'Unsupported export format' };
      }

      // Write file to device storage
      const filePath = `${FileSystem.documentDirectory}${fileName}`;
      await FileSystem.writeAsStringAsync(filePath, content, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      const fileInfo = await FileSystem.getInfoAsync(filePath);

      return {
        success: true,
        filePath,
        fileSize: fileInfo.exists ? (fileInfo as any).size : content.length, // Fixed: Handle size property
        messageCount: exportMessages.length,
      };
    } catch (error) {
      console.error('❌ Export error:', error);
      return { success: false, error: 'Failed to export chat' };
    }
  }

  /**
   * Export chat messages to a document (Original database method)
   */
  async exportChat(
    chatId: string,
    chatName: string,
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      // Get messages from database
      const messages = await this.getMessagesForExport(chatId, options);
      
      if (messages.length === 0) {
        return { success: false, error: 'No messages found to export' };
      }

      // Generate export content based on format
      let content: string;
      let fileName: string;
      let mimeType: string;

      switch (options.format) {
        case 'txt':
          content = await this.generateTextExport(messages, chatName);
          fileName = `${this.sanitizeFileName(chatName)}_chat_export.txt`;
          mimeType = 'text/plain';
          break;
        case 'html':
          content = await this.generateHtmlExport(messages, chatName);
          fileName = `${this.sanitizeFileName(chatName)}_chat_export.html`;
          mimeType = 'text/html';
          break;
        case 'json':
          content = await this.generateJsonExport(messages, chatName);
          fileName = `${this.sanitizeFileName(chatName)}_chat_export.json`;
          mimeType = 'application/json';
          break;
        default:
          return { success: false, error: 'Unsupported export format' };
      }

      console.log(`📄 Exporting as ${options.format} (${mimeType})`);

      // Write file to device storage
      const filePath = `${FileSystem.documentDirectory}exports/${fileName}`;
      
      // Ensure exports directory exists
      const exportsDir = `${FileSystem.documentDirectory}exports/`;
      const dirInfo = await FileSystem.getInfoAsync(exportsDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(exportsDir, { intermediates: true });
      }

      await FileSystem.writeAsStringAsync(filePath, content, {
        encoding: FileSystem.EncodingType.UTF8,
      });

      // Get file size
      const fileInfo = await FileSystem.getInfoAsync(filePath);
      const fileSize = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;

      // Handle media export if requested
      let mediaExportResult;
      if (options.includeMedia) {
        mediaExportResult = await this.exportMediaFiles(messages, chatName);
        console.log(`📁 Media export: ${mediaExportResult.exportedCount} files exported, ${mediaExportResult.skippedCount} skipped`);
      }

      return {
        success: true,
        filePath,
        fileSize,
        messageCount: messages.length,
      };
    } catch (error) {
      console.error('Error exporting chat:', error);
      return { success: false, error: 'Failed to export chat' };
    }
  }

  /**
   * Share exported chat file
   */
  async shareExportedChat(filePath: string): Promise<boolean> {
    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        Alert.alert('Error', 'Sharing is not available on this device');
        return false;
      }

      await Sharing.shareAsync(filePath, {
        mimeType: this.getMimeTypeFromPath(filePath),
        dialogTitle: 'Share Chat Export',
      });

      return true;
    } catch (error) {
      console.error('Error sharing exported chat:', error);
      Alert.alert('Error', 'Failed to share exported chat');
      return false;
    }
  }

  /**
   * Save exported chat to device gallery/downloads
   */
  async saveToDevice(filePath: string): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        // Request permissions
        const { status } = await MediaLibrary.requestPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Please grant permission to save files to your device');
          return false;
        }

        // Save to downloads folder
        const asset = await MediaLibrary.createAssetAsync(filePath);
        const album = await MediaLibrary.getAlbumAsync('Download');
        if (album == null) {
          await MediaLibrary.createAlbumAsync('Download', asset, false);
        } else {
          await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
        }

        Alert.alert('Success', 'Chat export saved to Downloads folder');
        return true;
      } else {
        // iOS - files are already in app documents directory
        Alert.alert('Success', 'Chat export saved to app documents');
        return true;
      }
    } catch (error) {
      console.error('Error saving to device:', error);
      Alert.alert('Error', 'Failed to save chat export to device');
      return false;
    }
  }

  /**
   * Get messages for export with filters
   */
  private async getMessagesForExport(
    chatId: string,
    options: ExportOptions
  ): Promise<ExportedMessage[]> {
    try {
      const db = offlineDatabaseService.getDatabase();
      
      let query = `
        SELECT id, content, type, senderId, senderName, timestamp, 
               mediaUrl, fileName, caption, isForwarded, replyTo
        FROM messages 
        WHERE chatId = ? AND isDeleted = 0
      `;
      
      const params: any[] = [chatId];

      // Add date range filter
      if (options.dateRange) {
        query += ` AND timestamp >= ? AND timestamp <= ?`;
        params.push(options.dateRange.startDate.getTime(), options.dateRange.endDate.getTime());
      }

      // Add message type filter
      if (options.messageTypes && options.messageTypes.length > 0) {
        const placeholders = options.messageTypes.map(() => '?').join(',');
        query += ` AND type IN (${placeholders})`;
        params.push(...options.messageTypes);
      }

      // Exclude system messages if requested
      if (!options.includeSystemMessages) {
        query += ` AND type NOT IN ('system', 'notification')`;
      }

      query += ` ORDER BY timestamp ASC`;

      const results = await db.getAllAsync(query, params);
      
      return results.map((row: any) => ({
        id: row.id,
        content: row.content || '',
        type: row.type,
        senderId: row.senderId,
        senderName: row.senderName || 'Unknown',
        timestamp: new Date(row.timestamp),
        mediaUrl: row.mediaUrl,
        fileName: row.fileName,
        caption: row.caption,
        isForwarded: row.isForwarded === 1,
        replyTo: row.replyTo,
      }));
    } catch (error) {
      console.error('Error getting messages for export:', error);
      return [];
    }
  }

  /**
   * Generate text format export
   */
  private async generateTextExport(messages: ExportedMessage[], chatName: string): Promise<string> {
    let content = `Chat Export: ${chatName}\n`;
    content += `Exported on: ${new Date().toLocaleString()}\n`;
    content += `Total messages: ${messages.length}\n`;
    content += '=' .repeat(50) + '\n\n';

    for (const message of messages) {
      const timestamp = message.timestamp.toLocaleString();
      const sender = message.senderName;
      
      content += `[${timestamp}] ${sender}:\n`;
      
      if (message.type === 'text') {
        content += `${message.content}\n`;
      } else {
        content += `[${message.type.toUpperCase()}]`;
        if (message.fileName) {
          content += ` ${message.fileName}`;
        }
        if (message.caption) {
          content += ` - ${message.caption}`;
        }
        content += '\n';
      }
      
      if (message.isForwarded) {
        content += '(Forwarded message)\n';
      }
      
      content += '\n';
    }

    return content;
  }

  /**
   * Generate HTML format export
   */
  private async generateHtmlExport(messages: ExportedMessage[], chatName: string): Promise<string> {
    let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Chat Export: ${chatName}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .header { background-color: #87CEEB; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .message { background-color: white; margin: 10px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .sender { font-weight: bold; color: #4682B4; }
        .timestamp { color: #666; font-size: 12px; }
        .content { margin: 8px 0; }
        .media { background-color: #f0f0f0; padding: 8px; border-radius: 4px; font-style: italic; }
        .forwarded { color: #888; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Chat Export: ${chatName}</h1>
        <p>Exported on: ${new Date().toLocaleString()}</p>
        <p>Total messages: ${messages.length}</p>
    </div>
`;

    for (const message of messages) {
      html += `
    <div class="message">
        <div class="sender">${message.senderName}</div>
        <div class="timestamp">${message.timestamp.toLocaleString()}</div>
        <div class="content">
`;
      
      if (message.type === 'text') {
        html += `${message.content.replace(/\n/g, '<br>')}`;
      } else {
        // Handle different media types with proper embedding
        switch (message.type) {
          case 'image':
            html += `<div class="media">
              ${message.mediaUrl ? `<img src="${message.mediaUrl}" alt="${message.fileName || 'Image'}" style="max-width: 300px; max-height: 300px; border-radius: 8px;" onclick="window.open('${message.mediaUrl}', '_blank')">` : `[IMAGE: ${message.fileName || 'Unknown'}]`}
              ${message.caption ? `<p style="margin-top: 8px; font-style: normal;">${message.caption}</p>` : ''}
            </div>`;
            break;

          case 'video':
            html += `<div class="media">
              ${message.mediaUrl ? `<video controls style="max-width: 300px; max-height: 300px; border-radius: 8px;">
                <source src="${message.mediaUrl}" type="video/mp4">
                <p>Your browser doesn't support video playback. <a href="${message.mediaUrl}" target="_blank">Download video</a></p>
              </video>` : `[VIDEO: ${message.fileName || 'Unknown'}]`}
              ${message.caption ? `<p style="margin-top: 8px; font-style: normal;">${message.caption}</p>` : ''}
            </div>`;
            break;

          case 'audio':
          case 'voice':
            html += `<div class="media">
              ${message.mediaUrl ? `<audio controls style="width: 300px;">
                <source src="${message.mediaUrl}" type="audio/mpeg">
                <source src="${message.mediaUrl}" type="audio/wav">
                <source src="${message.mediaUrl}" type="audio/ogg">
                <p>Your browser doesn't support audio playback. <a href="${message.mediaUrl}" target="_blank">Download audio</a></p>
              </audio>` : `[${message.type.toUpperCase()}: ${message.fileName || 'Unknown'}]`}
              ${message.caption ? `<p style="margin-top: 8px; font-style: normal;">${message.caption}</p>` : ''}
            </div>`;
            break;

          case 'document':
            html += `<div class="media">
              <div style="display: flex; align-items: center; padding: 12px; background-color: #e8f4f8; border-radius: 8px; border-left: 4px solid #87CEEB;">
                <div style="margin-right: 12px; font-size: 24px;">📄</div>
                <div>
                  <div style="font-weight: bold; color: #333;">${message.fileName || 'Document'}</div>
                  ${message.mediaUrl ? `<a href="${message.mediaUrl}" target="_blank" style="color: #87CEEB; text-decoration: none;">📥 Download</a>` : '<span style="color: #666;">File not available</span>'}
                </div>
              </div>
              ${message.caption ? `<p style="margin-top: 8px; font-style: normal;">${message.caption}</p>` : ''}
            </div>`;
            break;

          default:
            html += `<div class="media">[${message.type.toUpperCase()}]`;
            if (message.fileName) {
              html += ` ${message.fileName}`;
            }
            if (message.mediaUrl) {
              html += ` <a href="${message.mediaUrl}" target="_blank" style="color: #87CEEB;">📥 Download</a>`;
            }
            if (message.caption) {
              html += ` - ${message.caption}`;
            }
            html += '</div>';
            break;
        }
      }
      
      html += `
        </div>
`;
      
      if (message.isForwarded) {
        html += `        <div class="forwarded">(Forwarded message)</div>`;
      }
      
      html += `
    </div>
`;
    }

    html += `
</body>
</html>
`;

    return html;
  }

  /**
   * Generate JSON format export
   */
  private async generateJsonExport(messages: ExportedMessage[], chatName: string): Promise<string> {
    const exportData = {
      chatName,
      exportedAt: new Date().toISOString(),
      messageCount: messages.length,
      messages: messages.map(message => ({
        ...message,
        timestamp: message.timestamp.toISOString(),
      })),
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Export media files associated with messages with size limits and compression
   */
  private async exportMediaFiles(messages: ExportedMessage[], chatName: string): Promise<{
    exportedCount: number;
    skippedCount: number;
    totalSize: number;
    mediaFolder: string;
  }> {
    let exportedCount = 0;
    let skippedCount = 0;
    let totalSize = 0;
    const MAX_EXPORT_SIZE = 1024 * 1024 * 1024; // 1GB limit
    const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB per file

    try {
      const mediaDir = `${FileSystem.documentDirectory}exports/${this.sanitizeFileName(chatName)}_media/`;
      const dirInfo = await FileSystem.getInfoAsync(mediaDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(mediaDir, { intermediates: true });
      }

      // Create media manifest
      const mediaManifest: Array<{
        messageId: string;
        fileName: string;
        originalUrl: string;
        exportedPath: string;
        fileSize: number;
        mediaType: string;
        timestamp: string;
      }> = [];

      for (const message of messages) {
        if (message.mediaUrl && message.fileName) {
          try {
            // Check if we're approaching size limit
            if (totalSize >= MAX_EXPORT_SIZE) {
              console.warn(`📦 Export size limit reached (${MAX_EXPORT_SIZE / 1024 / 1024}MB)`);
              skippedCount++;
              continue;
            }

            // Try to get file info first
            let sourceFile: string;
            let fileSize = 0;

            // Check if media is cached locally first
            const cachedMedia = await this.getCachedMediaPath(message.id);
            if (cachedMedia) {
              sourceFile = cachedMedia;
              const fileInfo = await FileSystem.getInfoAsync(sourceFile);
              fileSize = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;
            } else if (message.mediaUrl.startsWith('http')) {
              // Download from URL if not cached
              const tempPath = `${FileSystem.cacheDirectory}temp_export_${Date.now()}.tmp`;
              const downloadResult = await FileSystem.downloadAsync(message.mediaUrl, tempPath);

              if (downloadResult.status === 200) {
                sourceFile = tempPath;
                const fileInfo = await FileSystem.getInfoAsync(sourceFile);
                fileSize = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;
              } else {
                console.warn(`Failed to download media: ${message.fileName}`);
                skippedCount++;
                continue;
              }
            } else {
              // Local file path
              sourceFile = message.mediaUrl;
              const fileInfo = await FileSystem.getInfoAsync(sourceFile);
              if (!fileInfo.exists) {
                console.warn(`Media file not found: ${message.fileName}`);
                skippedCount++;
                continue;
              }
              fileSize = fileInfo.size || 0;
            }

            // Check individual file size
            if (fileSize > MAX_FILE_SIZE) {
              console.warn(`File too large, skipping: ${message.fileName} (${fileSize / 1024 / 1024}MB)`);
              skippedCount++;
              continue;
            }

            // Check if adding this file would exceed total limit
            if (totalSize + fileSize > MAX_EXPORT_SIZE) {
              console.warn(`Adding file would exceed size limit, skipping: ${message.fileName}`);
              skippedCount++;
              continue;
            }

            // Generate safe filename
            const safeFileName = this.generateSafeFileName(message.fileName, message.type);
            const mediaPath = `${mediaDir}${safeFileName}`;

            // Copy the file
            await FileSystem.copyAsync({
              from: sourceFile,
              to: mediaPath,
            });

            // Add to manifest
            mediaManifest.push({
              messageId: message.id,
              fileName: safeFileName,
              originalUrl: message.mediaUrl,
              exportedPath: mediaPath,
              fileSize,
              mediaType: message.type,
              timestamp: message.timestamp.toISOString(),
            });

            totalSize += fileSize;
            exportedCount++;

            // Clean up temp file if we downloaded it
            if (message.mediaUrl.startsWith('http') && sourceFile.includes('temp_export_')) {
              await FileSystem.deleteAsync(sourceFile, { idempotent: true });
            }

          } catch (error) {
            console.warn(`Failed to export media file: ${message.fileName}`, error);
            skippedCount++;
          }
        }
      }

      // Save media manifest
      const manifestPath = `${mediaDir}media_manifest.json`;
      await FileSystem.writeAsStringAsync(
        manifestPath,
        JSON.stringify({
          exportedAt: new Date().toISOString(),
          chatName,
          totalFiles: exportedCount,
          totalSize,
          files: mediaManifest,
        }, null, 2)
      );

      console.log(`📁 Media export completed: ${exportedCount} files, ${skippedCount} skipped, ${totalSize / 1024 / 1024}MB total`);

      return {
        exportedCount,
        skippedCount,
        totalSize,
        mediaFolder: mediaDir,
      };

    } catch (error) {
      console.error('Error exporting media files:', error);
      return {
        exportedCount: 0,
        skippedCount: messages.filter(m => m.mediaUrl).length,
        totalSize: 0,
        mediaFolder: '',
      };
    }
  }

  /**
   * Get cached media path for a message
   */
  private async getCachedMediaPath(messageId: string): Promise<string | null> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const results = await db.getAllAsync(`
        SELECT localPath FROM cached_media
        WHERE messageId = ? AND isValid = 1 AND localPath IS NOT NULL
      `, [messageId]);

      if (results.length > 0) {
        const localPath = (results[0] as any).localPath;
        const fileInfo = await FileSystem.getInfoAsync(localPath);
        if (fileInfo.exists) {
          return localPath;
        }
      }
      return null;
    } catch (error) {
      console.error('Failed to get cached media path:', error);
      return null;
    }
  }

  /**
   * Generate safe filename with proper extension
   */
  private generateSafeFileName(originalFileName: string, messageType: string): string {
    // Remove unsafe characters
    const safeName = originalFileName.replace(/[^a-zA-Z0-9._-]/g, '_');

    // Ensure proper extension based on message type
    const extension = this.getExtensionForType(messageType);

    // Add timestamp to avoid conflicts
    const timestamp = Date.now();
    const nameWithoutExt = safeName.replace(/\.[^/.]+$/, '');

    return `${nameWithoutExt}_${timestamp}.${extension}`;
  }

  /**
   * Get file extension for message type
   */
  private getExtensionForType(messageType: string): string {
    switch (messageType) {
      case 'image': return 'jpg';
      case 'video': return 'mp4';
      case 'audio': return 'mp3';
      case 'voice': return 'mp3';
      case 'document': return 'pdf';
      default: return 'bin';
    }
  }

  /**
   * Sanitize filename for safe file system usage
   */
  private sanitizeFileName(fileName: string): string {
    return fileName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
  }

  /**
   * Get MIME type from file path
   */
  private getMimeTypeFromPath(filePath: string): string {
    const extension = filePath.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'txt': return 'text/plain';
      case 'html': return 'text/html';
      case 'json': return 'application/json';
      default: return 'application/octet-stream';
    }
  }
}

export const chatExportService = ChatExportService.getInstance();

/**
 * Helper function to get export format options
 */
export const getExportFormatOptions = () => [
  { value: 'txt', label: 'Text File (.txt)', description: 'Simple text format, easy to read' },
  { value: 'html', label: 'HTML File (.html)', description: 'Formatted web page with styling' },
  { value: 'json', label: 'JSON File (.json)', description: 'Structured data format for developers' },
];

/**
 * Helper function to get date range presets for export
 */
export const getExportDateRangePresets = () => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
  const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
  const last3Months = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);

  return [
    { label: 'All Messages', startDate: null, endDate: null },
    { label: 'Today', startDate: today, endDate: now },
    { label: 'Yesterday', startDate: yesterday, endDate: today },
    { label: 'Last 7 days', startDate: lastWeek, endDate: now },
    { label: 'Last 30 days', startDate: lastMonth, endDate: now },
    { label: 'Last 3 months', startDate: last3Months, endDate: now },
  ];
};
