# Install Android SDK completely on D: drive with elevated permissions
# This script downloads and installs Android SDK tools to D:\Android\Sdk

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges. Restarting as administrator..." -ForegroundColor Yellow
    Start-Process PowerShell -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
    exit
}

Write-Host "Installing Android SDK to D: drive..." -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Set environment variables to force D: drive
$env:ANDROID_SDK_ROOT = "D:\Android\Sdk"
$env:ANDROID_HOME = "D:\Android\Sdk"

# Create temp directory on D: drive if it doesn't exist
$tempDir = "D:\temp"
if (-not (Test-Path $tempDir)) {
    New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
}
$env:TEMP = $tempDir
$env:TMP = $tempDir

# Create directories
$androidSdkPath = "D:\Android\Sdk"
$platformToolsPath = "$androidSdkPath\platform-tools"
$cmdlineToolsPath = "$androidSdkPath\cmdline-tools\latest"

Write-Host "Creating Android SDK directories on D: drive..."
try {
    New-Item -ItemType Directory -Path $androidSdkPath -Force -ErrorAction Stop | Out-Null
    New-Item -ItemType Directory -Path "$androidSdkPath\cmdline-tools" -Force -ErrorAction Stop | Out-Null
    New-Item -ItemType Directory -Path $cmdlineToolsPath -Force -ErrorAction Stop | Out-Null
    Write-Host "✅ Directories created successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to create directories: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure you have write permissions to D: drive" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Download Android command line tools
$cmdlineToolsUrl = "https://dl.google.com/android/repository/commandlinetools-win-11076708_latest.zip"
$cmdlineToolsZip = "$tempDir\commandlinetools-win.zip"

Write-Host "Downloading Android command line tools..."
Write-Host "URL: $cmdlineToolsUrl" -ForegroundColor Gray
Write-Host "Destination: $cmdlineToolsZip" -ForegroundColor Gray

try {
    # Remove existing file if it exists
    if (Test-Path $cmdlineToolsZip) {
        Remove-Item $cmdlineToolsZip -Force
    }

    Invoke-WebRequest -Uri $cmdlineToolsUrl -OutFile $cmdlineToolsZip -UseBasicParsing
    Write-Host "✅ Download completed successfully" -ForegroundColor Green
    Write-Host "File size: $((Get-Item $cmdlineToolsZip).Length / 1MB) MB" -ForegroundColor Gray
} catch {
    Write-Host "❌ Failed to download Android command line tools: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Extract command line tools
Write-Host "Extracting command line tools to D: drive..."
try {
    $extractTempPath = "$androidSdkPath\cmdline-tools\temp"

    # Clean up any existing temp extraction folder
    if (Test-Path $extractTempPath) {
        Remove-Item -Path $extractTempPath -Recurse -Force
    }

    Expand-Archive -Path $cmdlineToolsZip -DestinationPath $extractTempPath -Force

    # Move contents to correct location
    $sourcePath = "$extractTempPath\cmdline-tools"
    if (Test-Path $sourcePath) {
        # Ensure destination exists
        if (-not (Test-Path $cmdlineToolsPath)) {
            New-Item -ItemType Directory -Path $cmdlineToolsPath -Force | Out-Null
        }

        Get-ChildItem -Path $sourcePath | Move-Item -Destination $cmdlineToolsPath -Force
        Remove-Item -Path $extractTempPath -Recurse -Force
        Write-Host "✅ Command line tools extraction completed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Extracted files not found in expected location" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Failed to extract command line tools: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Download platform-tools (includes ADB)
$platformToolsUrl = "https://dl.google.com/android/repository/platform-tools-latest-windows.zip"
$platformToolsZip = "$tempDir\platform-tools-latest-windows.zip"

Write-Host "Downloading Android platform tools (ADB)..."
Write-Host "URL: $platformToolsUrl" -ForegroundColor Gray
Write-Host "Destination: $platformToolsZip" -ForegroundColor Gray

try {
    # Remove existing file if it exists
    if (Test-Path $platformToolsZip) {
        Remove-Item $platformToolsZip -Force
    }

    Invoke-WebRequest -Uri $platformToolsUrl -OutFile $platformToolsZip -UseBasicParsing
    Write-Host "✅ Platform tools download completed successfully" -ForegroundColor Green
    Write-Host "File size: $((Get-Item $platformToolsZip).Length / 1MB) MB" -ForegroundColor Gray
} catch {
    Write-Host "❌ Failed to download platform tools: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Extract platform tools
Write-Host "Extracting platform tools to D: drive..."
try {
    Expand-Archive -Path $platformToolsZip -DestinationPath $androidSdkPath -Force
    Write-Host "✅ Platform tools extraction completed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to extract platform tools: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Set up SDK manager and install essential packages
$sdkManagerPath = "$cmdlineToolsPath\bin\sdkmanager.bat"

Write-Host "Setting up SDK Manager and installing essential packages..."
if (Test-Path $sdkManagerPath) {
    Write-Host "✅ SDK Manager found at: $sdkManagerPath" -ForegroundColor Green

    # Accept licenses first
    Write-Host "Accepting Android SDK licenses..."
    try {
        $licenseInput = "y`ny`ny`ny`ny`ny`ny`ny`ny`n"
        $licenseInput | & $sdkManagerPath --licenses --sdk_root=$androidSdkPath
        Write-Host "✅ Licenses accepted" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  License acceptance may have failed, continuing..." -ForegroundColor Yellow
    }

    # Install essential packages
    Write-Host "Installing essential SDK packages..."
    Write-Host "- platform-tools" -ForegroundColor Gray
    Write-Host "- build-tools;34.0.0" -ForegroundColor Gray
    Write-Host "- platforms;android-34" -ForegroundColor Gray

    try {
        & $sdkManagerPath --install "platform-tools" "build-tools;34.0.0" "platforms;android-34" --sdk_root=$androidSdkPath
        Write-Host "✅ Essential SDK packages installed!" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Some packages may not have installed correctly" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ SDK Manager not found at: $sdkManagerPath" -ForegroundColor Red
    Write-Host "Manual installation may be required" -ForegroundColor Yellow
}

# Verify ADB installation
$adbPath = "$androidSdkPath\platform-tools\adb.exe"
Write-Host "Verifying ADB installation..."
if (Test-Path $adbPath) {
    Write-Host "✅ ADB successfully installed at: $adbPath" -ForegroundColor Green
    try {
        $adbVersion = & $adbPath version 2>&1
        Write-Host "ADB Version: $adbVersion" -ForegroundColor Gray
    } catch {
        Write-Host "⚠️  ADB found but version check failed" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ ADB not found at: $adbPath" -ForegroundColor Red
}

# Set environment variables permanently
Write-Host "Setting environment variables..."
try {
    [Environment]::SetEnvironmentVariable("ANDROID_HOME", $androidSdkPath, [EnvironmentVariableTarget]::User)
    [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $androidSdkPath, [EnvironmentVariableTarget]::User)

    # Update PATH
    $currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)
    $pathsToAdd = @(
        "$androidSdkPath\platform-tools",
        "$androidSdkPath\cmdline-tools\latest\bin",
        "$androidSdkPath\emulator"
    )

    foreach ($pathToAdd in $pathsToAdd) {
        if ($currentPath -notlike "*$pathToAdd*") {
            $currentPath = "$currentPath;$pathToAdd"
        }
    }

    [Environment]::SetEnvironmentVariable("PATH", $currentPath, [EnvironmentVariableTarget]::User)
    Write-Host "✅ Environment variables set successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to set environment variables: $($_.Exception.Message)" -ForegroundColor Red
}

# Clean up downloaded files
Write-Host "Cleaning up temporary files..."
Remove-Item -Path $cmdlineToolsZip -Force -ErrorAction SilentlyContinue
Remove-Item -Path $platformToolsZip -Force -ErrorAction SilentlyContinue
Write-Host "✅ Cleanup completed" -ForegroundColor Green

Write-Host ""
Write-Host "=======================================" -ForegroundColor Green
Write-Host "🎉 Android SDK installation completed!" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host "📍 ANDROID_HOME: $androidSdkPath" -ForegroundColor Cyan
Write-Host "📍 ADB Location: $adbPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Close this PowerShell window" -ForegroundColor White
Write-Host "2. Open a new PowerShell/Terminal window" -ForegroundColor White
Write-Host "3. Navigate to your project: cd F:\IraChat" -ForegroundColor White
Write-Host "4. Run: npx expo start" -ForegroundColor White
Write-Host "5. Press 'a' to open Android" -ForegroundColor White
Write-Host ""
Write-Host "🔧 To verify installation:" -ForegroundColor Yellow
Write-Host "   adb version" -ForegroundColor White
Write-Host "   echo `$env:ANDROID_HOME" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  IMPORTANT: Restart your terminal for environment variables to take effect!" -ForegroundColor Red
Write-Host ""

Read-Host "Press Enter to exit"
