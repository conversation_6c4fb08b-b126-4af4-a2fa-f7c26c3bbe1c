/**
 * Offline Chat List Service for IraChat
 * Manages chat list storage and synchronization with Firebase
 * Provides offline-first chat list functionality
 */

import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface OfflineChat {
  id: string;
  name: string;
  avatar?: string;
  partnerId?: string;
  partnerName?: string;
  partnerAvatar?: string;
  isGroup: boolean;
  lastMessage?: string;
  lastMessageTime?: Date;
  lastMessageSender?: string;
  lastMessageType?: string;
  unreadCount: number;
  isOnline: boolean;
  lastSeen?: Date;
  isPinned: boolean;
  isArchived: boolean;
  isMuted: boolean;
  createdAt: Date;
  updatedAt: Date;
}

class OfflineChatListService {
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('💬 Initializing offline chat list service...');
      await offlineDatabaseService.initialize();
      this.isInitialized = true;
      console.log('✅ Offline chat list service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize offline chat list service:', error);
      throw error;
    }
  }

  /**
   * Save or update a chat in offline storage
   */
  async saveChat(chat: OfflineChat): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const now = Date.now();

      await db.runAsync(`
        INSERT OR REPLACE INTO chats (
          id, name, avatar, partnerId, partnerName, partnerAvatar,
          isGroup, lastMessage, lastMessageTime, lastMessageSender, lastMessageType,
          unreadCount, isOnline, lastSeen, isPinned, isArchived, isMuted,
          createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        chat.id,
        chat.name,
        chat.avatar || null,
        chat.partnerId || null,
        chat.partnerName || null,
        chat.partnerAvatar || null,
        chat.isGroup ? 1 : 0,
        chat.lastMessage || null,
        chat.lastMessageTime ? chat.lastMessageTime.getTime() : null,
        chat.lastMessageSender || null,
        chat.lastMessageType || 'text',
        chat.unreadCount,
        chat.isOnline ? 1 : 0,
        chat.lastSeen ? chat.lastSeen.getTime() : null,
        chat.isPinned ? 1 : 0,
        chat.isArchived ? 1 : 0,
        chat.isMuted ? 1 : 0,
        chat.createdAt.getTime(),
        now
      ]);

      console.log('💾 Chat saved offline:', chat.id);
    } catch (error) {
      console.error('❌ Failed to save chat offline:', error);
      throw error;
    }
  }

  /**
   * Get all chats from offline storage
   */
  async getChats(): Promise<OfflineChat[]> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT * FROM chats 
        WHERE isArchived = 0
        ORDER BY isPinned DESC, updatedAt DESC
      `);

      const chats: OfflineChat[] = result.map((row: any) => ({
        id: row.id,
        name: row.name,
        avatar: row.avatar,
        partnerId: row.partnerId,
        partnerName: row.partnerName,
        partnerAvatar: row.partnerAvatar,
        isGroup: row.isGroup === 1,
        lastMessage: row.lastMessage,
        lastMessageTime: row.lastMessageTime ? new Date(row.lastMessageTime) : undefined,
        lastMessageSender: row.lastMessageSender,
        unreadCount: row.unreadCount,
        isOnline: row.isOnline === 1,
        lastSeen: row.lastSeen ? new Date(row.lastSeen) : undefined,
        isPinned: row.isPinned === 1,
        isArchived: row.isArchived === 1,
        isMuted: row.isMuted === 1,
        createdAt: new Date(row.createdAt),
        updatedAt: new Date(row.updatedAt)
      }));

      console.log(`📱 Loaded ${chats.length} chats from offline storage`);
      return chats;
    } catch (error) {
      console.error('❌ Failed to get chats from offline storage:', error);
      return [];
    }
  }

  /**
   * Update chat's last message
   */
  async updateLastMessage(
    chatId: string,
    message: string,
    senderId: string,
    timestamp: Date
  ): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      
      await db.runAsync(`
        UPDATE chats 
        SET lastMessage = ?, lastMessageSender = ?, lastMessageTime = ?, updatedAt = ?
        WHERE id = ?
      `, [message, senderId, timestamp.getTime(), Date.now(), chatId]);

      console.log('💾 Updated last message for chat:', chatId);
    } catch (error) {
      console.error('❌ Failed to update last message:', error);
    }
  }

  /**
   * Update partner's online status
   */
  async updateOnlineStatus(
    partnerId: string,
    isOnline: boolean,
    lastSeen?: Date
  ): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();

      // First check if the lastSeen column exists in chats table
      try {
        const tableInfo = await db.getAllAsync(`PRAGMA table_info(chats);`);
        const hasLastSeenColumn = tableInfo.some((column: any) => column.name === 'lastSeen');

        if (hasLastSeenColumn) {
          await db.runAsync(`
            UPDATE chats
            SET isOnline = ?, lastSeen = ?, updatedAt = ?
            WHERE partnerId = ?
          `, [
            isOnline ? 1 : 0,
            lastSeen ? lastSeen.getTime() : null,
            Date.now(),
            partnerId
          ]);
        } else {
          // Fallback: update without lastSeen column
          await db.runAsync(`
            UPDATE chats
            SET isOnline = ?, updatedAt = ?
            WHERE partnerId = ?
          `, [
            isOnline ? 1 : 0,
            Date.now(),
            partnerId
          ]);
          console.warn('⚠️ Updated online status without lastSeen column (database migration needed)');
        }
      } catch (pragmaError) {
        // If PRAGMA fails, try the basic update without lastSeen
        await db.runAsync(`
          UPDATE chats
          SET isOnline = ?, updatedAt = ?
          WHERE partnerId = ?
        `, [
          isOnline ? 1 : 0,
          Date.now(),
          partnerId
        ]);
        console.warn('⚠️ Updated online status with fallback query');
      }

      console.log(`💾 Updated online status for partner ${partnerId}: ${isOnline}`);
    } catch (error) {
      console.error('❌ Failed to update online status:', error);

      // If this is a "no such column" error, suggest database reinitialization
      if (error instanceof Error && error.message.includes('no such column')) {
        console.error('💡 Database schema issue detected. Consider reinitializing the database.');
      }
    }
  }

  /**
   * Delete a chat from offline storage
   */
  async deleteChat(chatId: string): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync('DELETE FROM chats WHERE id = ?', [chatId]);
      console.log('🗑️ Chat deleted from offline storage:', chatId);
    } catch (error) {
      console.error('❌ Failed to delete chat:', error);
    }
  }

  /**
   * Clear all chats (for logout)
   */
  async clearAllChats(): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync('DELETE FROM chats');
      console.log('🗑️ All chats cleared from offline storage');
    } catch (error) {
      console.error('❌ Failed to clear chats:', error);
    }
  }

  /**
   * Get chat by ID
   */
  async getChatById(chatId: string): Promise<OfflineChat | null> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getFirstAsync(
        'SELECT * FROM chats WHERE id = ?',
        [chatId]
      );

      if (!result) return null;

      const row = result as any;
      return {
        id: row.id,
        name: row.name,
        avatar: row.avatar,
        partnerId: row.partnerId,
        partnerName: row.partnerName,
        partnerAvatar: row.partnerAvatar,
        isGroup: row.isGroup === 1,
        lastMessage: row.lastMessage,
        lastMessageTime: row.lastMessageTime ? new Date(row.lastMessageTime) : undefined,
        lastMessageSender: row.lastMessageSender,
        unreadCount: row.unreadCount,
        isOnline: row.isOnline === 1,
        lastSeen: row.lastSeen ? new Date(row.lastSeen) : undefined,
        isPinned: row.isPinned === 1,
        isArchived: row.isArchived === 1,
        isMuted: row.isMuted === 1,
        createdAt: new Date(row.createdAt),
        updatedAt: new Date(row.updatedAt)
      };
    } catch (error) {
      console.error('❌ Failed to get chat by ID:', error);
      return null;
    }
  }
}

export const offlineChatListService = new OfflineChatListService();
