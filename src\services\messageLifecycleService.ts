import { doc, deleteDoc, updateDoc, getDoc, serverTimestamp, setDoc } from 'firebase/firestore';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';
import { realPrivacyService } from './realPrivacyService';

export interface Message {
  id: string;
  chatId: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: Date;
  disappearAt?: Date;
  isForwardable: boolean;
  forwardedFrom?: string;
  status: 'sent' | 'delivered' | 'read' | 'disappeared' | 'archived';
}

class MessageLifecycleService {
  private disappearingTimers: Map<string, NodeJS.Timeout> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();
      await this.setupDisappearingMessageTimers();
      
      // Listen for network changes to sync when online
      networkStateManager.addListener('messageLifecycle', this.handleNetworkStateChange.bind(this), 3);
      
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize MessageLifecycleService:', error);
      throw error;
    }
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected) {
      this.syncPendingDeletions();
      this.syncPendingArchives();
    }
  }

  /**
   * Apply disappearing message settings to a new message
   */
  async applyDisappearingSettings(message: Message, senderId: string, receiverId: string): Promise<Message> {
    try {
      // Get receiver's privacy settings
      const privacyResult = await realPrivacyService.getPrivacySettings(receiverId);
      if (!privacyResult.success || !privacyResult.settings) {
        return message;
      }

      const settings = privacyResult.settings;
      
      // Check if disappearing messages are enabled
      if (!settings.disappearingMessages) {
        return message;
      }

      // Check if this sender's messages should disappear
      const shouldDisappear = this.shouldMessageDisappear(senderId, settings);
      if (!shouldDisappear) {
        return message;
      }

      // Calculate disappear time
      const disappearAt = this.calculateDisappearTime(settings.disappearingMessagesDuration);
      
      // Update message with disappearing info
      const updatedMessage: Message = {
        ...message,
        disappearAt,
        status: 'sent'
      };

      // Schedule disappearing
      this.scheduleMessageDisappearing(updatedMessage, settings.disappearingMessagesStorage);

      return updatedMessage;
    } catch (error) {
      console.error('Error applying disappearing settings:', error);
      return message;
    }
  }

  private shouldMessageDisappear(senderId: string, settings: any): boolean {
    switch (settings.disappearingMessagesScope) {
      case 'everyone':
        return true;
      case 'nobody':
        return false;
      case 'contacts':
        // Would need to check if sender is in contacts
        return true; // Simplified for now
      case 'custom':
        return settings.disappearingMessagesCustomContacts?.includes(senderId) || false;
      default:
        return false;
    }
  }

  private calculateDisappearTime(duration: string): Date {
    const now = new Date();
    switch (duration) {
      case '1hour':
        return new Date(now.getTime() + 60 * 60 * 1000);
      case '1day':
        return new Date(now.getTime() + 24 * 60 * 60 * 1000);
      case '1week':
        return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      case '1month':
        return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
      case 'never':
        return new Date(now.getTime() + 100 * 365 * 24 * 60 * 60 * 1000); // 100 years
      default:
        return new Date(now.getTime() + 24 * 60 * 60 * 1000); // 1 day default
    }
  }

  private scheduleMessageDisappearing(message: Message, storagePolicy: string): void {
    if (!message.disappearAt) return;

    const timeUntilDisappear = message.disappearAt.getTime() - Date.now();
    if (timeUntilDisappear <= 0) {
      // Message should disappear immediately
      this.executeMessageDisappearing(message.id, storagePolicy);
      return;
    }

    // Schedule disappearing
    const timer = setTimeout(() => {
      this.executeMessageDisappearing(message.id, storagePolicy);
      this.disappearingTimers.delete(message.id);
    }, timeUntilDisappear);

    this.disappearingTimers.set(message.id, timer);
  }

  private async executeMessageDisappearing(messageId: string, storagePolicy: string): Promise<void> {
    try {
      switch (storagePolicy) {
        case 'delete_everywhere':
          await this.deleteMessageEverywhere(messageId);
          break;
        case 'delete_chat_only':
          await this.deleteFromChatOnly(messageId);
          break;
        case 'archive_locally':
          await this.archiveMessageLocally(messageId);
          break;
      }
    } catch (error) {
      console.error('Error executing message disappearing:', error);
    }
  }

  /**
   * Delete message from Firebase, local storage, and chat UI
   */
  private async deleteMessageEverywhere(messageId: string): Promise<void> {
    try {
      if (networkStateManager.isOnline()) {
        // Delete from Firebase
        await deleteDoc(doc(db, 'messages', messageId));
      } else {
        // Queue for deletion when online
        await this.queueForDeletion(messageId, 'delete_everywhere');
      }

      // Delete from local storage
      await this.deleteFromLocalStorage(messageId);
      
      // Update UI status
      await this.updateMessageStatus(messageId, 'disappeared');
    } catch (error) {
      console.error('Error deleting message everywhere:', error);
    }
  }

  /**
   * Remove message from chat UI but keep in Firebase and local storage
   */
  private async deleteFromChatOnly(messageId: string): Promise<void> {
    try {
      // Update message status to disappeared (hides from chat)
      await this.updateMessageStatus(messageId, 'disappeared');
      
      // Keep in Firebase and local storage for potential recovery
    } catch (error) {
      console.error('Error deleting from chat only:', error);
    }
  }

  /**
   * Archive message locally and remove from chat UI
   */
  private async archiveMessageLocally(messageId: string): Promise<void> {
    try {
      if (networkStateManager.isOnline()) {
        // Move to archived collection in Firebase
        const messageDoc = await getDoc(doc(db, 'messages', messageId));
        if (messageDoc.exists()) {
          const messageData = messageDoc.data();
          await setDoc(doc(db, 'archived_messages', messageId), {
            ...messageData,
            archivedAt: serverTimestamp()
          });
          await deleteDoc(doc(db, 'messages', messageId));
        }
      } else {
        // Queue for archiving when online
        await this.queueForArchiving(messageId);
      }

      // Archive locally
      await this.archiveInLocalStorage(messageId);
      
      // Update UI status
      await this.updateMessageStatus(messageId, 'archived');
    } catch (error) {
      console.error('Error archiving message locally:', error);
    }
  }

  private async queueForDeletion(messageId: string, type: string): Promise<void> {
    const localDb = offlineDatabaseService.getDatabase();
    await localDb.runAsync(`
      INSERT OR REPLACE INTO pending_deletions (messageId, type, timestamp)
      VALUES (?, ?, ?)
    `, [messageId, type, Date.now()]);
  }

  private async queueForArchiving(messageId: string): Promise<void> {
    const localDb = offlineDatabaseService.getDatabase();
    await localDb.runAsync(`
      INSERT OR REPLACE INTO pending_archives (messageId, timestamp)
      VALUES (?, ?)
    `, [messageId, Date.now()]);
  }

  private async syncPendingDeletions(): Promise<void> {
    try {
      const localDb = offlineDatabaseService.getDatabase();
      const pendingDeletions = await localDb.getAllAsync('SELECT * FROM pending_deletions') as Array<{messageId: string, type: string, timestamp: number}>;

      for (const deletion of pendingDeletions) {
        try {
          if (deletion.type === 'delete_everywhere') {
            await deleteDoc(doc(db, 'messages', deletion.messageId));
          }

          // Remove from pending queue
          await localDb.runAsync('DELETE FROM pending_deletions WHERE messageId = ?', [deletion.messageId]);
        } catch (error) {
          console.error('Error syncing deletion:', error);
        }
      }
    } catch (error) {
      console.error('Error syncing pending deletions:', error);
    }
  }

  private async syncPendingArchives(): Promise<void> {
    try {
      const localDb = offlineDatabaseService.getDatabase();
      const pendingArchives = await localDb.getAllAsync('SELECT * FROM pending_archives') as Array<{messageId: string, timestamp: number}>;

      for (const archive of pendingArchives) {
        try {
          // Move message to archived collection
          const messageDoc = await getDoc(doc(db, 'messages', archive.messageId));
          if (messageDoc.exists()) {
            const messageData = messageDoc.data();
            await setDoc(doc(db, 'archived_messages', archive.messageId), {
              ...messageData,
              archivedAt: serverTimestamp()
            });
            await deleteDoc(doc(db, 'messages', archive.messageId));
          }

          // Remove from pending queue
          await localDb.runAsync('DELETE FROM pending_archives WHERE messageId = ?', [archive.messageId]);
        } catch (error) {
          console.error('Error syncing archive:', error);
        }
      }
    } catch (error) {
      console.error('Error syncing pending archives:', error);
    }
  }

  private async deleteFromLocalStorage(messageId: string): Promise<void> {
    const localDb = offlineDatabaseService.getDatabase();
    await localDb.runAsync('DELETE FROM messages WHERE id = ?', [messageId]);
  }

  private async archiveInLocalStorage(messageId: string): Promise<void> {
    const localDb = offlineDatabaseService.getDatabase();
    await localDb.runAsync(`
      INSERT OR REPLACE INTO archived_messages
      SELECT * FROM messages WHERE id = ?
    `, [messageId]);
    await localDb.runAsync('DELETE FROM messages WHERE id = ?', [messageId]);
  }

  private async updateMessageStatus(messageId: string, status: string): Promise<void> {
    const localDb = offlineDatabaseService.getDatabase();
    await localDb.runAsync('UPDATE messages SET status = ? WHERE id = ?', [status, messageId]);
  }

  private async setupDisappearingMessageTimers(): Promise<void> {
    try {
      const localDb = offlineDatabaseService.getDatabase();
      const messages = await localDb.getAllAsync(`
        SELECT * FROM messages
        WHERE disappearAt IS NOT NULL
        AND status != 'disappeared'
        AND status != 'archived'
      `) as Array<{
        id: string;
        chatId: string;
        senderId: string;
        receiverId: string;
        content: string;
        timestamp: string;
        disappearAt: string;
        isForwardable: number;
        forwardedFrom?: string;
        status: string;
      }>;

      for (const message of messages) {
        const disappearAt = new Date(message.disappearAt);
        if (disappearAt > new Date()) {
          // Get user's storage policy
          const privacyResult = await realPrivacyService.getPrivacySettings(message.receiverId);
          const storagePolicy = privacyResult.settings?.disappearingMessagesStorage || 'delete_chat_only';

          this.scheduleMessageDisappearing({
            id: message.id,
            chatId: message.chatId,
            senderId: message.senderId,
            receiverId: message.receiverId,
            content: message.content,
            timestamp: new Date(message.timestamp),
            disappearAt,
            isForwardable: Boolean(message.isForwardable),
            forwardedFrom: message.forwardedFrom,
            status: message.status as 'sent' | 'delivered' | 'read' | 'disappeared' | 'archived'
          }, storagePolicy);
        }
      }
    } catch (error) {
      console.error('Error setting up disappearing message timers:', error);
    }
  }

  /**
   * Check if user can forward a message
   */
  async canForwardMessage(forwarderId: string, originalSenderId: string): Promise<boolean> {
    try {
      // Get original sender's privacy settings
      const privacyResult = await realPrivacyService.getPrivacySettings(originalSenderId);
      if (!privacyResult.success || !privacyResult.settings) {
        return false;
      }

      const settings = privacyResult.settings;
      
      // Check if forwarding is enabled
      if (!settings.forwardedMessages) {
        return false;
      }

      // Check forwarding scope
      switch (settings.forwardedMessagesScope) {
        case 'everyone':
          return true;
        case 'nobody':
          return false;
        case 'contacts':
          // Would need to check if forwarder is in sender's contacts
          return true; // Simplified for now
        case 'custom':
          return settings.forwardedMessagesCustomContacts?.includes(forwarderId) || false;
        default:
          return false;
      }
    } catch (error) {
      console.error('Error checking forward permission:', error);
      return false;
    }
  }

  /**
   * Delete message from Firebase (called by sync service)
   */
  async deleteMessageFromFirebase(messageId: string, type: string): Promise<void> {
    try {
      if (type === 'delete_everywhere') {
        await deleteDoc(doc(db, 'messages', messageId));
      }
    } catch (error) {
      console.error('Error deleting message from Firebase:', error);
      throw error;
    }
  }

  /**
   * Archive message in Firebase (called by sync service)
   */
  async archiveMessageInFirebase(messageId: string): Promise<void> {
    try {
      const messageDoc = await getDoc(doc(db, 'messages', messageId));
      if (messageDoc.exists()) {
        const messageData = messageDoc.data();

        // Move to archived collection
        await updateDoc(doc(db, 'archived_messages', messageId), {
          ...messageData,
          archivedAt: serverTimestamp()
        });

        // Delete from messages collection
        await deleteDoc(doc(db, 'messages', messageId));
      }
    } catch (error) {
      console.error('Error archiving message in Firebase:', error);
      throw error;
    }
  }

  cleanup(): void {
    // Clear all timers
    this.disappearingTimers.forEach(timer => clearTimeout(timer));
    this.disappearingTimers.clear();

    // Remove network listener
    networkStateManager.removeListener('messageLifecycle');

    this.isInitialized = false;
  }
}

export const messageLifecycleService = new MessageLifecycleService();
