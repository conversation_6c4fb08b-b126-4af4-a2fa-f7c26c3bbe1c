/**
 * Layout Picker Service for IraChat
 * Provides story layout templates and collage options
 */

export interface LayoutTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  type: 'single' | 'collage' | 'split' | 'grid';
  mediaSlots: number;
  aspectRatio: number; // width/height
  isPopular?: boolean;
  isPremium?: boolean;
}

export interface LayoutCategory {
  id: string;
  name: string;
  templates: LayoutTemplate[];
}

class LayoutPickerService {
  /**
   * Get all layout categories with templates
   */
  getLayoutCategories(): LayoutCategory[] {
    return [
      {
        id: 'popular',
        name: 'Popular',
        templates: [
          {
            id: 'single_portrait',
            name: 'Portrait',
            description: 'Classic single photo/video',
            thumbnail: '📱',
            type: 'single',
            mediaSlots: 1,
            aspectRatio: 9/16,
            isPopular: true,
          },
          {
            id: 'split_vertical',
            name: 'Split Screen',
            description: 'Two photos side by side',
            thumbnail: '⬜⬜',
            type: 'split',
            mediaSlots: 2,
            aspectRatio: 9/16,
            isPopular: true,
          },
          {
            id: 'collage_2x2',
            name: '2x2 Grid',
            description: 'Four photos in a grid',
            thumbnail: '⬜⬜\n⬜⬜',
            type: 'grid',
            mediaSlots: 4,
            aspectRatio: 1,
            isPopular: true,
          },
        ]
      },
      {
        id: 'single',
        name: 'Single',
        templates: [
          {
            id: 'single_portrait',
            name: 'Portrait',
            description: 'Classic 9:16 format',
            thumbnail: '📱',
            type: 'single',
            mediaSlots: 1,
            aspectRatio: 9/16,
          },
          {
            id: 'single_square',
            name: 'Square',
            description: '1:1 aspect ratio',
            thumbnail: '⬜',
            type: 'single',
            mediaSlots: 1,
            aspectRatio: 1,
          },
          {
            id: 'single_landscape',
            name: 'Landscape',
            description: '16:9 format',
            thumbnail: '▬',
            type: 'single',
            mediaSlots: 1,
            aspectRatio: 16/9,
          },
        ]
      },
      {
        id: 'collage',
        name: 'Collage',
        templates: [
          {
            id: 'collage_2_vertical',
            name: 'Dual Vertical',
            description: 'Two photos stacked',
            thumbnail: '⬜\n⬜',
            type: 'collage',
            mediaSlots: 2,
            aspectRatio: 9/16,
          },
          {
            id: 'collage_2_horizontal',
            name: 'Dual Horizontal',
            description: 'Two photos side by side',
            thumbnail: '⬜⬜',
            type: 'collage',
            mediaSlots: 2,
            aspectRatio: 16/9,
          },
          {
            id: 'collage_3_mixed',
            name: 'Triple Mix',
            description: 'Three photos mixed layout',
            thumbnail: '⬜⬜\n ⬜ ',
            type: 'collage',
            mediaSlots: 3,
            aspectRatio: 9/16,
          },
          {
            id: 'collage_4_grid',
            name: '2x2 Grid',
            description: 'Four photos in grid',
            thumbnail: '⬜⬜\n⬜⬜',
            type: 'collage',
            mediaSlots: 4,
            aspectRatio: 1,
          },
          {
            id: 'collage_6_grid',
            name: '3x2 Grid',
            description: 'Six photos in grid',
            thumbnail: '⬜⬜⬜\n⬜⬜⬜',
            type: 'collage',
            mediaSlots: 6,
            aspectRatio: 3/2,
          },
        ]
      },
      {
        id: 'split',
        name: 'Split Screen',
        templates: [
          {
            id: 'split_50_50',
            name: '50/50 Split',
            description: 'Equal split screen',
            thumbnail: '⬜⬜',
            type: 'split',
            mediaSlots: 2,
            aspectRatio: 9/16,
          },
          {
            id: 'split_70_30',
            name: '70/30 Split',
            description: 'Unequal split screen',
            thumbnail: '⬜⬜',
            type: 'split',
            mediaSlots: 2,
            aspectRatio: 9/16,
          },
          {
            id: 'split_diagonal',
            name: 'Diagonal Split',
            description: 'Diagonal division',
            thumbnail: '◢◣',
            type: 'split',
            mediaSlots: 2,
            aspectRatio: 9/16,
            isPremium: true,
          },
        ]
      },
      {
        id: 'creative',
        name: 'Creative',
        templates: [
          {
            id: 'creative_circle',
            name: 'Circle Frame',
            description: 'Circular photo frame',
            thumbnail: '⭕',
            type: 'single',
            mediaSlots: 1,
            aspectRatio: 1,
            isPremium: true,
          },
          {
            id: 'creative_heart',
            name: 'Heart Shape',
            description: 'Heart-shaped frame',
            thumbnail: '💖',
            type: 'single',
            mediaSlots: 1,
            aspectRatio: 1,
            isPremium: true,
          },
          {
            id: 'creative_polaroid',
            name: 'Polaroid Style',
            description: 'Vintage polaroid look',
            thumbnail: '📷',
            type: 'single',
            mediaSlots: 1,
            aspectRatio: 4/5,
            isPremium: true,
          },
        ]
      }
    ];
  }

  /**
   * Get popular templates
   */
  getPopularTemplates(): LayoutTemplate[] {
    const allCategories = this.getLayoutCategories();
    const allTemplates = allCategories.flatMap(category => category.templates);
    return allTemplates.filter(template => template.isPopular);
  }

  /**
   * Get templates by type
   */
  getTemplatesByType(type: LayoutTemplate['type']): LayoutTemplate[] {
    const allCategories = this.getLayoutCategories();
    const allTemplates = allCategories.flatMap(category => category.templates);
    return allTemplates.filter(template => template.type === type);
  }

  /**
   * Search templates
   */
  searchTemplates(query: string): LayoutTemplate[] {
    const allCategories = this.getLayoutCategories();
    const allTemplates = allCategories.flatMap(category => category.templates);
    
    return allTemplates.filter(template => 
      template.name.toLowerCase().includes(query.toLowerCase()) ||
      template.description.toLowerCase().includes(query.toLowerCase()) ||
      template.type.toLowerCase().includes(query.toLowerCase())
    );
  }

  /**
   * Get template by ID
   */
  getTemplateById(id: string): LayoutTemplate | null {
    const allCategories = this.getLayoutCategories();
    const allTemplates = allCategories.flatMap(category => category.templates);
    return allTemplates.find(template => template.id === id) || null;
  }

  /**
   * Check if template is premium
   */
  isPremiumTemplate(templateId: string): boolean {
    const template = this.getTemplateById(templateId);
    return template?.isPremium || false;
  }

  /**
   * Get aspect ratio string for display
   */
  getAspectRatioString(aspectRatio: number): string {
    if (aspectRatio === 9/16) return '9:16';
    if (aspectRatio === 16/9) return '16:9';
    if (aspectRatio === 1) return '1:1';
    if (aspectRatio === 4/5) return '4:5';
    if (aspectRatio === 3/2) return '3:2';
    return `${aspectRatio.toFixed(2)}:1`;
  }

  /**
   * Get layout instructions for template
   */
  getLayoutInstructions(template: LayoutTemplate): string {
    switch (template.type) {
      case 'single':
        return 'Select one photo or video for this layout';
      case 'collage':
        return `Select ${template.mediaSlots} photos for this collage`;
      case 'split':
        return `Select ${template.mediaSlots} photos for split screen`;
      case 'grid':
        return `Select ${template.mediaSlots} photos for grid layout`;
      default:
        return 'Select media for this layout';
    }
  }
}

export const layoutPickerService = new LayoutPickerService();
