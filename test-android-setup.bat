@echo off
echo ========================================
echo Testing Android Development Setup
echo ========================================

echo.
echo 1. Testing ANDROID_HOME...
if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo ✅ ANDROID_HOME is correctly set to: %ANDROID_HOME%
) else (
    echo ❌ ANDROID_HOME not set or invalid: %ANDROID_HOME%
)

echo.
echo 2. Testing Java...
java -version 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Java is installed and accessible
) else (
    echo ❌ Java is not installed or not in PATH
)

echo.
echo 3. Testing JAVA_HOME...
if exist "%JAVA_HOME%\bin\java.exe" (
    echo ✅ JAVA_HOME is correctly set to: %JAVA_HOME%
) else (
    echo ❌ JAVA_HOME not set or invalid: %JAVA_HOME%
)

echo.
echo 4. Testing ADB...
adb version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ ADB is working
    echo Connected devices:
    adb devices
) else (
    echo ❌ ADB is not working
)

echo.
echo ========================================
if exist "%ANDROID_HOME%\platform-tools\adb.exe" if exist "%JAVA_HOME%\bin\java.exe" (
    echo 🎉 Setup looks good! You can try: npm run android
) else (
    echo ⚠️  Setup incomplete. Please install missing components.
)
echo ========================================
pause
