// 📱 REAL UPDATE DETAIL SCREEN
// Complete update detail view with real Firebase and local storage integration

import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  ActivityIndicator,
  Dimensions,
  StyleSheet,
  TextInput,
  Modal,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { Video, ResizeMode } from 'expo-av';
import { useSelector } from 'react-redux';
import { RootState } from '../../src/redux/store';
import { localUpdatesStorage } from '../../src/services/localUpdatesStorage';
import { comprehensiveUpdatesService } from '../../src/services/comprehensiveUpdatesService';
import { Update, UpdateComment } from '../../src/types/Update';
import { formatTimeAgo } from '../../src/utils/dateUtils';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function UpdateDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const currentUser = useSelector((state: RootState) => state.user?.currentUser);

  // Explicitly use React import to fix TypeScript error
  const ReactComponent = React.Fragment;

  // State
  const [update, setUpdate] = useState<Update | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLiking, setIsLiking] = useState(false);
  const [showComments, setShowComments] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [isCommenting, setIsCommenting] = useState(false);
  const [comments, setComments] = useState<UpdateComment[]>([]);

  // Load update details
  const loadUpdate = useCallback(async () => {
    if (!id || typeof id !== 'string') return;

    try {
      setIsLoading(true);

      // 🏠 TRY LOCAL STORAGE FIRST
      console.log('💾 Loading update from local storage:', id);
      const localUpdate = await localUpdatesStorage.getUpdateById(id);

      if (localUpdate) {
        setUpdate(localUpdate);
        setComments(localUpdate.comments || []);
        console.log('✅ Loaded update from local storage');
      }

      // 🔄 SYNC WITH FIREBASE
      try {
        console.log('🔄 Syncing with Firebase...');
        const result = await comprehensiveUpdatesService.getUpdateById(id);

        if (result.success && result.data) {
          setUpdate(result.data);
          setComments(result.data.comments || []);

          // Save to local storage
          await localUpdatesStorage.saveUpdate(result.data);
          console.log('✅ Synced update with Firebase');
        } else if (!localUpdate) {
          // No local data and Firebase failed
          Alert.alert('Error', 'Update not found');
          router.back();
        }
      } catch (firebaseError) {
        console.error('⚠️ Firebase sync failed:', firebaseError);
        if (!localUpdate) {
          Alert.alert('Error', 'Failed to load update');
          router.back();
        }
      }
    } catch (error) {
      console.error('❌ Error loading update:', error);
      Alert.alert('Error', 'Failed to load update');
      router.back();
    } finally {
      setIsLoading(false);
    }
  }, [id, router]);

  // Handle like toggle
  const handleLike = async () => {
    if (!update || !currentUser?.id || isLiking) return;

    try {
      setIsLiking(true);
      const isCurrentlyLiked = update.likes.includes(currentUser.id);

      // Optimistic update
      const updatedLikes = isCurrentlyLiked
        ? update.likes.filter(userId => userId !== currentUser.id)
        : [...update.likes, currentUser.id];

      setUpdate(prev => prev ? {
        ...prev,
        likes: updatedLikes,
        likeCount: updatedLikes.length,
        isLikedByCurrentUser: !isCurrentlyLiked
      } : null);

      // Update Firebase
      const result = await comprehensiveUpdatesService.toggleLike(
        update.id,
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar
      );

      if (!result.success) {
        // Revert on failure
        setUpdate(prev => prev ? {
          ...prev,
          likes: update.likes,
          likeCount: update.likeCount,
          isLikedByCurrentUser: update.isLikedByCurrentUser
        } : null);
        Alert.alert('Error', 'Failed to update like');
      } else {
        // Save updated state to local storage
        const updatedUpdate = {
          ...update,
          likes: updatedLikes,
          likeCount: updatedLikes.length,
          isLikedByCurrentUser: !isCurrentlyLiked
        };
        await localUpdatesStorage.saveUpdate(updatedUpdate);
      }
    } catch (error) {
      console.error('❌ Error toggling like:', error);
      Alert.alert('Error', 'Failed to update like');
    } finally {
      setIsLiking(false);
    }
  };

  // Handle comment submission
  const handleComment = async () => {
    if (!update || !currentUser?.id || !commentText.trim() || isCommenting) return;

    try {
      setIsCommenting(true);

      const result = await comprehensiveUpdatesService.addComment(
        update.id,
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar,
        commentText.trim()
      );

      if (result.success && result.data) {
        const newComment = result.data.comment;

        // Add comment to local state
        setComments(prev => [...prev, newComment]);
        setCommentText('');

        // Update comment count
        setUpdate(prev => prev ? {
          ...prev,
          commentCount: prev.commentCount + 1,
          comments: [...(prev.comments || []), newComment]
        } : null);

        // Save to local storage
        if (update) {
          const updatedUpdate = {
            ...update,
            commentCount: update.commentCount + 1,
            comments: [...(update.comments || []), newComment]
          };
          await localUpdatesStorage.saveUpdate(updatedUpdate);
        }
      } else {
        Alert.alert('Error', 'Failed to add comment');
      }
    } catch (error) {
      console.error('❌ Error adding comment:', error);
      Alert.alert('Error', 'Failed to add comment');
    } finally {
      setIsCommenting(false);
    }
  };

  // Handle share
  const handleShare = async () => {
    if (!update || !currentUser?.id) return;

    try {
      const result = await comprehensiveUpdatesService.shareUpdate(
        update.id,
        currentUser.id,
        currentUser.name || 'Unknown User',
        currentUser.avatar
      );

      if (result.success) {
        Alert.alert('Success', 'Update shared successfully');

        // Update share count
        setUpdate(prev => prev ? {
          ...prev,
          shareCount: prev.shareCount + 1,
          isSharedByCurrentUser: true
        } : null);
      } else {
        Alert.alert('Error', 'Failed to share update');
      }
    } catch (error) {
      console.error('❌ Error sharing update:', error);
      Alert.alert('Error', 'Failed to share update');
    }
  };

  useEffect(() => {
    loadUpdate();
  }, [loadUpdate]);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Update Details</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#667eea" />
          <Text style={styles.loadingText}>Loading update...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!update) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons name="arrow-back" size={24} color="white" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Update Details</Text>
        </View>
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={48} color="#ef4444" />
          <Text style={styles.errorText}>Update not found</Text>
          <TouchableOpacity style={styles.retryButton} onPress={loadUpdate}>
            <Text style={styles.retryButtonText}>Retry</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <Ionicons name="arrow-back" size={24} color="white" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Update Details</Text>
        <ReactComponent>
          <View style={{ width: 24 }} />
        </ReactComponent>
      </View>

      <ScrollView style={styles.content}>
        {/* User Info */}
        <View style={styles.userInfo}>
          {update.userAvatar ? (
            <Image source={{ uri: update.userAvatar }} style={styles.avatar} />
          ) : (
            <View style={[styles.avatar, styles.avatarPlaceholder]}>
              <Ionicons name="person" size={20} color="#9ca3af" />
            </View>
          )}
          <View style={styles.userDetails}>
            <Text style={styles.userName}>{update.userName}</Text>
            <Text style={styles.timestamp}>
              {formatTimeAgo(update.timestamp)}
            </Text>
          </View>
        </View>

        {/* Caption */}
        {update.caption && (
          <Text style={styles.caption}>{update.caption}</Text>
        )}

        {/* Media */}
        {update.media && update.media.length > 0 && (
          <View style={styles.mediaContainer}>
            {update.media[0].type === 'image' ? (
              <Image
                source={{ uri: update.media[0].url }}
                style={styles.mediaImage}
                resizeMode="cover"
              />
            ) : (
              <Video
                source={{ uri: update.media[0].url }}
                style={styles.mediaVideo}
                resizeMode={ResizeMode.COVER}
                shouldPlay={false}
                useNativeControls
              />
            )}
          </View>
        )}

        {/* Stats */}
        <View style={styles.stats}>
          <View style={styles.statItem}>
            <Ionicons name="heart" size={16} color="#ef4444" />
            <Text style={styles.statText}>{update.likeCount} likes</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="chatbubble" size={16} color="#3b82f6" />
            <Text style={styles.statText}>{update.commentCount} comments</Text>
          </View>
          <View style={styles.statItem}>
            <Ionicons name="share" size={16} color="#10b981" />
            <Text style={styles.statText}>{update.shareCount} shares</Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, update.isLikedByCurrentUser && styles.likedButton]}
            onPress={handleLike}
            disabled={isLiking}
          >
            <Ionicons
              name={update.isLikedByCurrentUser ? "heart" : "heart-outline"}
              size={20}
              color={update.isLikedByCurrentUser ? "white" : "#667eea"}
            />
            <Text style={[styles.actionText, update.isLikedByCurrentUser && styles.likedText]}>
              {isLiking ? 'Loading...' : 'Like'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={() => setShowComments(true)}
          >
            <Ionicons name="chatbubble-outline" size={20} color="#667eea" />
            <Text style={styles.actionText}>Comment</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleShare}
          >
            <Ionicons name="share-outline" size={20} color="#667eea" />
            <Text style={styles.actionText}>Share</Text>
          </TouchableOpacity>
        </View>

        {/* Recent Comments */}
        {comments.length > 0 && (
          <View style={styles.commentsSection}>
            <Text style={styles.commentsTitle}>Recent Comments</Text>
            {comments.slice(0, 3).map((comment, index) => (
              <View key={comment.id || index} style={styles.commentItem}>
                <Text style={styles.commentUser}>{comment.userName}</Text>
                <Text style={styles.commentText}>{comment.text}</Text>
                <Text style={styles.commentTime}>
                  {formatTimeAgo(comment.timestamp)}
                </Text>
              </View>
            ))}
            {comments.length > 3 && (
              <TouchableOpacity onPress={() => setShowComments(true)}>
                <Text style={styles.viewAllComments}>
                  View all {comments.length} comments
                </Text>
              </TouchableOpacity>
            )}
          </View>
        )}
      </ScrollView>

      {/* Comments Modal */}
      <Modal
        visible={showComments}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Comments</Text>
            <TouchableOpacity onPress={() => setShowComments(false)}>
              <Ionicons name="close" size={24} color="#374151" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.commentsList}>
            {comments.map((comment, index) => (
              <View key={comment.id || index} style={styles.commentItem}>
                <Text style={styles.commentUser}>{comment.userName}</Text>
                <Text style={styles.commentText}>{comment.text}</Text>
                <Text style={styles.commentTime}>
                  {formatTimeAgo(comment.timestamp)}
                </Text>
              </View>
            ))}
          </ScrollView>

          <View style={styles.commentInput}>
            <TextInput
              style={styles.textInput}
              placeholder="Add a comment..."
              value={commentText}
              onChangeText={setCommentText}
              multiline
            />
            <TouchableOpacity
              style={[styles.sendButton, !commentText.trim() && styles.sendButtonDisabled]}
              onPress={handleComment}
              disabled={!commentText.trim() || isCommenting}
            >
              {isCommenting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Ionicons name="send" size={20} color="white" />
              )}
            </TouchableOpacity>
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#667eea',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6b7280',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 18,
    color: '#374151',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 24,
    backgroundColor: '#667eea',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    backgroundColor: '#f3f4f6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userDetails: {
    marginLeft: 12,
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
  },
  timestamp: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  caption: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginBottom: 16,
  },
  mediaContainer: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  mediaImage: {
    width: SCREEN_WIDTH - 32, // Full width minus padding (16px on each side)
    height: (SCREEN_WIDTH - 32) * 0.75, // Maintain 4:3 aspect ratio
    alignSelf: 'center',
  },
  mediaVideo: {
    width: SCREEN_WIDTH - 32, // Full width minus padding (16px on each side)
    height: (SCREEN_WIDTH - 32) * 0.75, // Maintain 4:3 aspect ratio
    alignSelf: 'center',
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 16,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#6b7280',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#667eea',
    backgroundColor: 'white',
  },
  likedButton: {
    backgroundColor: '#ef4444',
    borderColor: '#ef4444',
  },
  actionText: {
    marginLeft: 8,
    fontSize: 16,
    fontWeight: '600',
    color: '#667eea',
  },
  likedText: {
    color: 'white',
  },
  commentsSection: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  commentsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
  },
  commentItem: {
    marginBottom: 12,
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  commentUser: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  commentText: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 4,
  },
  commentTime: {
    fontSize: 12,
    color: '#9ca3af',
  },
  viewAllComments: {
    fontSize: 14,
    color: '#667eea',
    fontWeight: '600',
    textAlign: 'center',
    marginTop: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'white',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  commentsList: {
    flex: 1,
    padding: 16,
  },
  commentInput: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginRight: 12,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    backgroundColor: '#667eea',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: '#d1d5db',
  },
});
