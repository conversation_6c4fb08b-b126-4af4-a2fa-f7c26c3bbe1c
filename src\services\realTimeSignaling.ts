// 🔥 REAL-TIME SIGNALING SERVICE - <PERSON><PERSON>LETE WEBRTC SIGNALING
// No mockups, no fake data - 100% real WebRTC signaling through Firebase

import {
  doc,
  onSnapshot,
  updateDoc,
  serverTimestamp,
  collection,
  query,
  orderBy,
  Unsubscribe
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { RTCSessionDescription, RTCIceCandidate } from 'react-native-webrtc';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

// Real Signaling Interfaces
interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'call-status';
  data: any;
  timestamp: Date;
  senderId: string;
  receiverId: string;
}

interface CallSignalingState {
  callId: string;
  offer?: RTCSessionDescription;
  answer?: RTCSessionDescription;
  iceCandidates: RTCIceCandidate[];
  status: string;
}

class RealTimeSignalingService {
  private signalingListeners: Map<string, Unsubscribe> = new Map();
  private callStateListeners: Map<string, ((_state: CallSignalingState) => void)[]> = new Map();
  private offlineSignalingQueue: Map<string, any[]> = new Map();
  private isInitialized = false;

  // ==================== INITIALIZATION & OFFLINE SUPPORT ====================

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await offlineDatabaseService.initialize();

      // Set up network state listener for offline sync
      networkStateManager.addListener('realTimeSignaling', (networkState) => {
        if (networkState.isConnected) {
          this.syncOfflineSignalingData();
        }
      });

      this.isInitialized = true;
    } catch (error) {
      throw error;
    }
  }

  private async syncOfflineSignalingData(): Promise<void> {
    try {
      // Sync any queued signaling data when back online
      for (const [callId, queuedData] of this.offlineSignalingQueue.entries()) {
        for (const data of queuedData) {
          try {
            await this.sendSignalingDataToFirebase(callId, data);
          } catch (_error) {
            // Keep in queue for retry
            continue;
          }
        }
        // Clear successfully synced data
        this.offlineSignalingQueue.delete(callId);
      }
    } catch (_error) {
      // Sync failed - will retry on next connection
    }
  }

  private async sendSignalingDataToFirebase(callId: string, data: any): Promise<void> {
    const callDocRef = doc(db, 'calls', callId);
    await updateDoc(callDocRef, {
      ...data,
      updatedAt: serverTimestamp(),
    });
  }

  private async storeSignalingDataOffline(callId: string, data: any): Promise<void> {
    try {
      const database = offlineDatabaseService.getDatabase();
      await database.runAsync(`
        INSERT OR REPLACE INTO signaling_queue (
          callId, data, timestamp, synced
        ) VALUES (?, ?, ?, ?)
      `, [callId, JSON.stringify(data), Date.now(), 0]);

      // Also add to memory queue for immediate retry
      if (!this.offlineSignalingQueue.has(callId)) {
        this.offlineSignalingQueue.set(callId, []);
      }
      this.offlineSignalingQueue.get(callId)!.push(data);
    } catch (_error) {
      // Offline storage failed
    }
  }

  // ==================== REAL SIGNALING SETUP ====================

  /**
   * Set up real-time signaling for a call
   */
  setupCallSignaling(
    callId: string,
    onSignalingMessage: (_message: SignalingMessage) => void
  ): () => void {
    try {
      // Listen to call document changes
      const callDocRef = doc(db, 'calls', callId);
      const unsubscribe = onSnapshot(callDocRef, (snapshot) => {
        if (snapshot.exists()) {
          const callData = snapshot.data();

          // Handle offer
          if (callData.offer && callData.offer !== null) {
            onSignalingMessage({
              type: 'offer',
              data: callData.offer,
              timestamp: new Date(),
              senderId: callData.callerId,
              receiverId: callData.receiverId,
            });
          }

          // Handle answer
          if (callData.answer && callData.answer !== null) {
            onSignalingMessage({
              type: 'answer',
              data: callData.answer,
              timestamp: new Date(),
              senderId: callData.receiverId,
              receiverId: callData.callerId,
            });
          }

          // Handle ICE candidates
          if (callData.iceCandidates && Array.isArray(callData.iceCandidates)) {
            callData.iceCandidates.forEach((candidate: any) => {
              onSignalingMessage({
                type: 'ice-candidate',
                data: candidate,
                timestamp: new Date(),
                senderId: candidate.senderId || callData.callerId,
                receiverId: candidate.receiverId || callData.receiverId,
              });
            });
          }

          // Handle status changes
          if (callData.status) {
            onSignalingMessage({
              type: 'call-status',
              data: { status: callData.status },
              timestamp: new Date(),
              senderId: callData.callerId,
              receiverId: callData.receiverId,
            });
          }
        }
      }, () => {
        // Error handling - signaling connection lost
      });

      this.signalingListeners.set(callId, unsubscribe);

      return () => {
        unsubscribe();
        this.signalingListeners.delete(callId);
      };
    } catch (_error) {
      return () => {};
    }
  }

  // ==================== REAL OFFER/ANSWER EXCHANGE ====================

  /**
   * Send real WebRTC offer
   */
  async sendOffer(callId: string, offer: RTCSessionDescription): Promise<void> {
    const offerData = {
      offer: {
        type: offer.type,
        sdp: offer.sdp,
      },
      status: 'ringing',
    };

    try {
      if (networkStateManager.isOnline()) {
        const callDocRef = doc(db, 'calls', callId);
        await updateDoc(callDocRef, {
          ...offerData,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Store offline for later sync
        await this.storeSignalingDataOffline(callId, offerData);
      }
    } catch (error) {
      // If online send fails, store offline
      await this.storeSignalingDataOffline(callId, offerData);
      throw error;
    }
  }

  /**
   * Send real WebRTC answer
   */
  async sendAnswer(callId: string, answer: RTCSessionDescription): Promise<void> {
    const answerData = {
      answer: {
        type: answer.type,
        sdp: answer.sdp,
      },
      status: 'connecting',
    };

    try {
      if (networkStateManager.isOnline()) {
        const callDocRef = doc(db, 'calls', callId);
        await updateDoc(callDocRef, {
          ...answerData,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Store offline for later sync
        await this.storeSignalingDataOffline(callId, answerData);
      }
    } catch (error) {
      // If online send fails, store offline
      await this.storeSignalingDataOffline(callId, answerData);
      throw error;
    }
  }

  // ==================== REAL ICE CANDIDATE EXCHANGE ====================

  /**
   * Send real ICE candidate
   */
  async sendIceCandidate(
    callId: string,
    candidate: RTCIceCandidate,
    senderId: string,
    receiverId: string
  ): Promise<void> {
    const candidateData = {
      iceCandidates: {
        candidate: candidate.candidate,
        sdpMLineIndex: candidate.sdpMLineIndex,
        sdpMid: candidate.sdpMid,
        senderId,
        receiverId,
        timestamp: Date.now(),
      }
    };

    try {
      if (networkStateManager.isOnline()) {
        const callDocRef = doc(db, 'calls', callId);
        await updateDoc(callDocRef, {
          ...candidateData,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Store offline for later sync
        await this.storeSignalingDataOffline(callId, candidateData);
      }
    } catch (error) {
      // If online send fails, store offline
      await this.storeSignalingDataOffline(callId, candidateData);
      throw error;
    }
  }

  /**
   * Listen for real ICE candidates
   */
  listenForIceCandidates(
    callId: string,
    onIceCandidate: (_candidate: RTCIceCandidate) => void
  ): () => void {
    try {
      const candidatesRef = collection(db, 'calls', callId, 'ice_candidates');
      const candidatesQuery = query(candidatesRef, orderBy('timestamp', 'asc'));

      const unsubscribe = onSnapshot(candidatesQuery, (snapshot) => {
        snapshot.docChanges().forEach((change) => {
          if (change.type === 'added') {
            const candidateData = change.doc.data();

            const candidate = new RTCIceCandidate({
              candidate: candidateData.candidate,
              sdpMLineIndex: candidateData.sdpMLineIndex,
              sdpMid: candidateData.sdpMid,
            });

            onIceCandidate(candidate);
          }
        });
      }, () => {
        // Error listening for ICE candidates
      });

      return () => {
        unsubscribe();
      };
    } catch (_error) {
      return () => {};
    }
  }

  // ==================== REAL CALL STATUS UPDATES ====================

  /**
   * Update real call status
   */
  async updateCallStatus(
    callId: string,
    status: string,
    additionalData?: any
  ): Promise<void> {
    const updateData: any = {
      status,
    };

    if (status === 'connected') {
      updateData.connectedAt = Date.now();
    } else if (status === 'ended' || status === 'declined' || status === 'failed') {
      updateData.endTime = Date.now();
    }

    if (additionalData) {
      Object.assign(updateData, additionalData);
    }

    try {
      if (networkStateManager.isOnline()) {
        const callDocRef = doc(db, 'calls', callId);
        await updateDoc(callDocRef, {
          ...updateData,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Store offline for later sync
        await this.storeSignalingDataOffline(callId, updateData);
      }

      // Notify call state listeners about the status change
      const callState: CallSignalingState = {
        callId,
        status,
        iceCandidates: [], // Will be populated from Firebase if needed
        ...additionalData
      };
      this.notifyCallStateListeners(callId, callState);
    } catch (error) {
      // If online send fails, store offline
      await this.storeSignalingDataOffline(callId, updateData);

      // Still notify listeners for local state
      const callState: CallSignalingState = {
        callId,
        status,
        iceCandidates: [],
        ...additionalData
      };
      this.notifyCallStateListeners(callId, callState);

      throw error;
    }
  }

  /**
   * Listen for real call status changes
   */
  listenForCallStatus(
    callId: string,
    onStatusChange: (_status: string, _callData: any) => void
  ): () => void {
    try {
      const callDocRef = doc(db, 'calls', callId);
      const unsubscribe = onSnapshot(callDocRef, (snapshot) => {
        if (snapshot.exists()) {
          const callData = snapshot.data();

          // Notify call state listeners about the status change
          const callState: CallSignalingState = {
            callId,
            status: callData.status,
            offer: callData.offer,
            answer: callData.answer,
            iceCandidates: callData.iceCandidates || [],
            ...callData
          };
          this.notifyCallStateListeners(callId, callState);

          onStatusChange(callData.status, callData);
        }
      }, () => {
        // Error listening for call status
      });

      return () => {
        unsubscribe();
      };
    } catch (_error) {
      return () => {};
    }
  }

  // ==================== REAL CALL STATE MANAGEMENT ====================

  /**
   * Add call state listener
   */
  addCallStateListener(
    callId: string,
    listener: (_state: CallSignalingState) => void
  ): () => void {
    if (!this.callStateListeners.has(callId)) {
      this.callStateListeners.set(callId, []);
    }

    this.callStateListeners.get(callId)!.push(listener);

    return () => {
      const listeners = this.callStateListeners.get(callId);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
        if (listeners.length === 0) {
          this.callStateListeners.delete(callId);
        }
      }
    };
  }

  /**
   * Notify call state listeners
   */
  private notifyCallStateListeners(callId: string, state: CallSignalingState): void {
    const listeners = this.callStateListeners.get(callId);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(state);
        } catch (_error) {
          // Error in call state listener - continue with other listeners
        }
      });
    }
  }

  // ==================== CLEANUP ====================

  /**
   * Clean up all signaling listeners
   */
  cleanup(): void {
    this.signalingListeners.forEach((unsubscribe) => {
      unsubscribe();
    });

    this.signalingListeners.clear();
    this.callStateListeners.clear();
  }

  /**
   * Clean up signaling for specific call
   */
  cleanupCall(callId: string): void {
    const unsubscribe = this.signalingListeners.get(callId);
    if (unsubscribe) {
      unsubscribe();
      this.signalingListeners.delete(callId);
    }

    this.callStateListeners.delete(callId);
  }
}

// Export singleton instance
export const realTimeSignalingService = new RealTimeSignalingService();
export default realTimeSignalingService;
