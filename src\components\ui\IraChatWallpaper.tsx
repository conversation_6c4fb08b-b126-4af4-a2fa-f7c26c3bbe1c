/**
 * IraChat Advanced Wallpaper System
 * Comprehensive wallpaper component with multiple options, custom uploads, and adaptive features
 * Updated to fix bundling issues
 */

import React, { useEffect, useRef, useState, useMemo } from 'react';
import {
  Animated,
  Dimensions,
  StyleSheet,
  View,
  ImageBackground,
  Image,
  Platform,
  PixelRatio
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Svg, { Circle, Path, Ellipse, Defs, RadialGradient, Stop, Pattern, Rect } from 'react-native-svg';
import { IRACHAT_COLORS, ANIMATIONS } from '../../styles/iraChatDesignSystem';
// import { BlurView } from 'expo-blur'; // Commented out until expo-blur is installed

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const pixelRatio = PixelRatio.get();

// Wallpaper types
export type WallpaperType = 'default' | 'gradient' | 'pattern' | 'custom' | 'solid';
export type WallpaperVariant = 'light' | 'dark' | 'auto';

// Built-in wallpaper options
export const BUILT_IN_WALLPAPERS = {
  gradients: [
    { id: 'sky', colors: ['#87CEEB', '#4682B4', '#1E90FF'], name: 'Sky Blue' },
    { id: 'sunset', colors: ['#FF6B6B', '#FFD93D', '#6BCF7F'], name: 'Sunset' },
    { id: 'ocean', colors: ['#667eea', '#764ba2'], name: 'Ocean' },
    { id: 'forest', colors: ['#134E5E', '#71B280'], name: 'Forest' },
    { id: 'purple', colors: ['#667eea', '#764ba2'], name: 'Purple Dream' },
    { id: 'dark', colors: ['#1A1A1A', '#2D2D2D', '#1A1A1A'], name: 'Dark Mode' },
  ],
  solids: [
    { id: 'white', color: '#FFFFFF', name: 'Pure White' }, // White for light theme
    { id: 'black', color: '#000000', name: 'Pure Black' },
    { id: 'gray', color: '#F5F5F5', name: 'Light Gray' }, // Light grey for light theme
    { id: 'blue', color: '#87CEEB', name: 'IraChat Blue' }, // Original IraChat blue
  ],
  patterns: [
    { id: 'dots', name: 'Subtle Dots' },
    { id: 'waves', name: 'Wave Pattern' },
    { id: 'geometric', name: 'Geometric' },
    { id: 'clouds', name: 'Cloud Pattern' },
  ]
};

interface IraChatWallpaperProps {
  // Basic props
  variant?: WallpaperVariant;
  animated?: boolean;
  opacity?: number;
  children?: React.ReactNode;

  // Advanced wallpaper props
  wallpaperType?: WallpaperType;
  wallpaperConfig?: {
    gradientId?: string;
    solidColor?: string;
    patternId?: string;
    customUri?: string;
    customBase64?: string;
  };

  // Adaptive features
  isDarkMode?: boolean;
  enableParallax?: boolean;
  enableBlurBehindInput?: boolean;
  inputFieldPosition?: { x: number; y: number; width: number; height: number };

  // Performance options
  enableCaching?: boolean;
  compressionQuality?: number;

  // Chat-specific
  chatId?: string;
  isGroupChat?: boolean;
}

export const IraChatWallpaper: React.FC<IraChatWallpaperProps> = ({
  variant = 'auto',
  animated = true,
  opacity = 1,
  children,
  wallpaperType = 'default',
  wallpaperConfig = {},
  isDarkMode = false,
  enableParallax = false,
  enableBlurBehindInput = false,
  inputFieldPosition,
  enableCaching = true,
  compressionQuality = 1,
  chatId,
  isGroupChat = false,
}) => {
  const floatingAnimation = useRef(new Animated.Value(0)).current;
  const pulseAnimation = useRef(new Animated.Value(1)).current;
  const rotateAnimation = useRef(new Animated.Value(0)).current;
  const parallaxAnimation = useRef(new Animated.Value(0)).current;

  // State for wallpaper management
  const [wallpaperLoaded, setWallpaperLoaded] = useState(false);
  const [wallpaperError, setWallpaperError] = useState(false);

  // Determine effective variant based on dark mode
  const effectiveVariant = useMemo(() => {
    if (variant === 'auto') {
      return isDarkMode ? 'dark' : 'light';
    }
    return variant;
  }, [variant, isDarkMode]);

  useEffect(() => {
    if (animated) {
      // Floating animation for clouds
      Animated.loop(
        Animated.sequence([
          Animated.timing(floatingAnimation, {
            toValue: 1,
            duration: ANIMATIONS.slower * 4,
            useNativeDriver: true,
          }),
          Animated.timing(floatingAnimation, {
            toValue: 0,
            duration: ANIMATIONS.slower * 4,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Pulse animation for dots
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnimation, {
            toValue: 1.2,
            duration: ANIMATIONS.slower * 2,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnimation, {
            toValue: 1,
            duration: ANIMATIONS.slower * 2,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Rotation animation for subtle elements
      Animated.loop(
        Animated.timing(rotateAnimation, {
          toValue: 1,
          duration: ANIMATIONS.slower * 8,
          useNativeDriver: true,
        })
      ).start();
    }
  }, [animated, floatingAnimation, pulseAnimation, rotateAnimation]);

  const floatingTranslateY = floatingAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [0, -20],
  });

  const rotateInterpolate = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  // Function to load your actual SVG background
  const loadBackgroundSvg = () => {
    try {
      // For now, we'll use a direct approach with your SVG file
      return require('../../../assets/images/BACKGROUND.svg');
    } catch (error) {
      console.log('Could not load BACKGROUND.svg, using fallback');
      return null;
    }
  };

  // Get wallpaper based on type and configuration
  const getWallpaperContent = (): React.ReactElement | null => {
    console.log('🎨 Getting wallpaper content for type:', wallpaperType, 'with config:', wallpaperConfig);

    switch (wallpaperType) {
      case 'gradient':
        const gradientConfig = BUILT_IN_WALLPAPERS.gradients.find(g => g.id === wallpaperConfig.gradientId)
          || BUILT_IN_WALLPAPERS.gradients[0];
        return (
          <LinearGradient
            colors={gradientConfig.colors as any}
            style={styles.gradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        );

      case 'solid':
        const solidColor = wallpaperConfig.solidColor || BUILT_IN_WALLPAPERS.solids[0].color;
        return <View style={[styles.gradient, { backgroundColor: solidColor }]} />;

      case 'pattern':
        return renderPatternWallpaper();

      case 'custom':
        return renderCustomWallpaper();

      default:
        // Use your original BACKGROUND.svg
        return (
          <ImageBackground
            source={require('../../../assets/images/BACKGROUND.svg')}
            style={styles.gradient}
            resizeMode="cover"
            onLoad={() => setWallpaperLoaded(true)}
            onError={() => setWallpaperError(true)}
          />
        );
    }
  };

  // Render pattern wallpaper
  const renderPatternWallpaper = () => {
    const patternId = wallpaperConfig.patternId || 'dots';

    return (
      <Svg width={screenWidth} height={screenHeight} style={styles.gradient}>
        <Defs>
          <Pattern id={patternId} patternUnits="userSpaceOnUse" width="20" height="20">
            {patternId === 'dots' && (
              <Circle cx="10" cy="10" r="1" fill={effectiveVariant === 'dark' ? '#FFFFFF' : '#87CEEB'} opacity="0.3" />
            )}
            {patternId === 'waves' && (
              <Path d="M0 10c5-5 15 5 20 0v10H0z" fill={effectiveVariant === 'dark' ? '#FFFFFF' : '#87CEEB'} opacity="0.2" />
            )}
            {patternId === 'geometric' && (
              <Rect x="0" y="0" width="10" height="10" fill={effectiveVariant === 'dark' ? '#FFFFFF' : '#87CEEB'} opacity="0.1" />
            )}
          </Pattern>
        </Defs>
        <Rect width="100%" height="100%" fill={effectiveVariant === 'dark' ? '#1A1A1A' : '#FFFFFF'} /> {/* White for light theme, dark for dark theme */}
        <Rect width="100%" height="100%" fill={`url(#${patternId})`} />
      </Svg>
    );
  };

  // Render custom wallpaper
  const renderCustomWallpaper = () => {
    console.log('🎨 Rendering custom wallpaper with config:', wallpaperConfig);

    if (wallpaperConfig.customUri) {
      console.log('🎨 Using custom URI:', wallpaperConfig.customUri);
      return (
        <ImageBackground
          source={{ uri: wallpaperConfig.customUri }}
          style={styles.gradient}
          resizeMode="cover"
          onLoad={() => {
            console.log('🎨 Custom wallpaper loaded successfully');
            setWallpaperLoaded(true);
          }}
          onError={(error) => {
            console.error('🎨 Custom wallpaper failed to load:', error);
            setWallpaperError(true);
          }}
        />
      );
    }

    if (wallpaperConfig.customBase64) {
      console.log('🎨 Using custom base64 image');
      return (
        <ImageBackground
          source={{ uri: `data:image/jpeg;base64,${wallpaperConfig.customBase64}` }}
          style={styles.gradient}
          resizeMode="cover"
          onLoad={() => {
            console.log('🎨 Custom base64 wallpaper loaded successfully');
            setWallpaperLoaded(true);
          }}
          onError={(error) => {
            console.error('🎨 Custom base64 wallpaper failed to load:', error);
            setWallpaperError(true);
          }}
        />
      );
    }

    console.log('🎨 No custom wallpaper found, falling back to default');
    // Fallback to default
    return getWallpaperContent();
  };

  const renderFloatingClouds = () => (
    <Animated.View
      style={[
        styles.floatingElement,
        {
          transform: [{ translateY: floatingTranslateY }],
        },
      ]}
    >
      <Svg width={screenWidth} height={screenHeight} style={styles.svgOverlay}>
        <Defs>
          <RadialGradient id="cloudGradient" cx="50%" cy="50%" r="50%">
            <Stop offset="0%" stopColor={IRACHAT_COLORS.primaryLight} stopOpacity="0.1" />
            <Stop offset="100%" stopColor={IRACHAT_COLORS.primary} stopOpacity="0.05" />
          </RadialGradient>
        </Defs>
        
        {/* Floating clouds */}
        <Ellipse cx={screenWidth * 0.2} cy={screenHeight * 0.15} rx="80" ry="30" fill="url(#cloudGradient)" />
        <Ellipse cx={screenWidth * 0.7} cy={screenHeight * 0.25} rx="100" ry="40" fill="url(#cloudGradient)" />
        <Ellipse cx={screenWidth * 0.4} cy={screenHeight * 0.8} rx="120" ry="35" fill="url(#cloudGradient)" />
        <Ellipse cx={screenWidth * 0.8} cy={screenHeight * 0.7} rx="90" ry="25" fill="url(#cloudGradient)" />
      </Svg>
    </Animated.View>
  );

  const renderPulsingDots = () => (
    <Animated.View
      style={[
        styles.pulsingElement,
        {
          transform: [{ scale: pulseAnimation }],
        },
      ]}
    >
      <Svg width={screenWidth} height={screenHeight} style={styles.svgOverlay}>
        {/* Scattered dots pattern */}
        {Array.from({ length: 20 }).map((_, index) => (
          <Circle
            key={index}
            cx={Math.random() * screenWidth}
            cy={Math.random() * screenHeight}
            r="2"
            fill={IRACHAT_COLORS.primary}
            opacity={0.1 + Math.random() * 0.1}
          />
        ))}
      </Svg>
    </Animated.View>
  );

  const renderWavePattern = () => (
    <Animated.View
      style={[
        styles.rotatingElement,
        {
          transform: [{ rotate: rotateInterpolate }],
        },
      ]}
    >
      <Svg width={screenWidth} height={screenHeight} style={styles.svgOverlay}>
        {/* Subtle wave patterns */}
        <Path
          d={`M0,${screenHeight * 0.3} Q${screenWidth * 0.25},${screenHeight * 0.25} ${screenWidth * 0.5},${screenHeight * 0.3} T${screenWidth},${screenHeight * 0.3} V${screenHeight} H0 Z`}
          fill={IRACHAT_COLORS.primaryLight}
          opacity="0.03"
        />
        <Path
          d={`M0,${screenHeight * 0.6} Q${screenWidth * 0.25},${screenHeight * 0.55} ${screenWidth * 0.5},${screenHeight * 0.6} T${screenWidth},${screenHeight * 0.6} V${screenHeight} H0 Z`}
          fill={IRACHAT_COLORS.primary}
          opacity="0.02"
        />
      </Svg>
    </Animated.View>
  );

  // Safe wallpaper content renderer with fallback
  const renderSafeWallpaperContent = () => {
    try {
      return getWallpaperContent();
    } catch (error) {
      console.warn('⚠️ Wallpaper rendering failed, using solid fallback:', error);
      // Return a simple solid color fallback - use light color instead of black to prevent black screen
      return (
        <View style={[styles.container, { backgroundColor: effectiveVariant === 'dark' ? '#1A1A1A' : '#F5F5F5' }]} />
      );
    }
  };

  return (
    <View style={[styles.container, { opacity }]}>
      {/* Main wallpaper content with error handling */}
      {renderSafeWallpaperContent()}

      {/* Blur effect behind input field if enabled */}
      {enableBlurBehindInput && inputFieldPosition && (
        <View
          style={[
            styles.inputBlur,
            {
              left: inputFieldPosition.x,
              top: inputFieldPosition.y,
              width: inputFieldPosition.width,
              height: inputFieldPosition.height,
              backgroundColor: 'rgba(45, 45, 45, 0.8)', // Changed from white to dark grey
            }
          ]}
        />
      )}

      {/* Removed all animated pattern layers */}

      {/* Content overlay */}
      {children && (
        <View style={styles.contentOverlay}>
          {children}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -10, // Much lower z-index to ensure it stays behind all components
  },
  gradient: {
    flex: 1,
  },
  svgOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  floatingElement: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  pulsingElement: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  rotatingElement: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  contentOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10, // High z-index to ensure components appear above wallpaper
  },
  inputBlur: {
    position: 'absolute',
    borderRadius: 8,
    zIndex: 0,
  },
});

export default IraChatWallpaper;
