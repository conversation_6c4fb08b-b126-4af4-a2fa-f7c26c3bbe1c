// Video Progress Manager for tracking video playback progress
// Handles video player registration, progress tracking, and callbacks

interface ProgressData {
  videoId: string;
  currentTime: number;
  duration: number;
  progress: number;
}

type ProgressCallback = (progressData: ProgressData) => void;

class VideoProgressManager {
  private static instance: VideoProgressManager;
  private players = new Map<string, any>();
  private progressCallbacks = new Map<string, ProgressCallback>();
  private activeVideoId: string | null = null;
  private progressIntervals = new Map<string, NodeJS.Timeout>();
  private lastProgress = new Map<string, number>(); // Track last progress to detect loops

  static getInstance(): VideoProgressManager {
    if (!VideoProgressManager.instance) {
      VideoProgressManager.instance = new VideoProgressManager();
    }
    return VideoProgressManager.instance;
  }

  // Register a video player
  registerPlayer(videoId: string, player: any): void {
    this.players.set(videoId, player);
    console.log(`📹 Registered video player: ${videoId}`);
  }

  // Unregister a video player
  unregisterPlayer(videoId: string): void {
    this.players.delete(videoId);
    this.progressCallbacks.delete(videoId);
    this.lastProgress.delete(videoId);

    // Clear any progress tracking interval
    const interval = this.progressIntervals.get(videoId);
    if (interval) {
      clearInterval(interval);
      this.progressIntervals.delete(videoId);
    }

    console.log(`📹 Unregistered video player: ${videoId}`);
  }

  // Get a specific player
  getPlayer(videoId: string): any | null {
    return this.players.get(videoId) || null;
  }

  // Get all players
  getAllPlayers(): Map<string, any> {
    return this.players;
  }

  // Set the active video for progress tracking
  setActiveVideo(videoId: string | null): void {
    // Clear previous active video tracking
    if (this.activeVideoId) {
      const prevInterval = this.progressIntervals.get(this.activeVideoId);
      if (prevInterval) {
        clearInterval(prevInterval);
        this.progressIntervals.delete(this.activeVideoId);
      }
    }

    this.activeVideoId = videoId;

    // Start tracking the new active video
    if (videoId && this.players.has(videoId)) {
      this.startProgressTracking(videoId);
    }
  }

  // Subscribe to progress updates for a specific video
  subscribeToProgress(videoId: string, callback: ProgressCallback): void {
    this.progressCallbacks.set(videoId, callback);
    
    // If this is the active video, start tracking immediately
    if (videoId === this.activeVideoId) {
      this.startProgressTracking(videoId);
    }
  }

  // Unsubscribe from progress updates
  unsubscribeFromProgress(videoId: string): void {
    this.progressCallbacks.delete(videoId);
    
    // Stop tracking if no callback
    const interval = this.progressIntervals.get(videoId);
    if (interval) {
      clearInterval(interval);
      this.progressIntervals.delete(videoId);
    }
  }

  // Start progress tracking for a video
  private startProgressTracking(videoId: string): void {
    const player = this.players.get(videoId);
    const callback = this.progressCallbacks.get(videoId);
    
    if (!player || !callback) {
      return;
    }

    // Clear any existing interval
    const existingInterval = this.progressIntervals.get(videoId);
    if (existingInterval) {
      clearInterval(existingInterval);
    }

    // Start new progress tracking interval
    const interval = setInterval(() => {
      try {
        if (player && typeof player.currentTime === 'number' && typeof player.duration === 'number') {
          const currentTime = player.currentTime || 0;
          const duration = player.duration || 0;
          const progress = duration > 0 ? currentTime / duration : 0;

          // Detect video loop - if progress jumps from high to low, video has looped
          const lastProgress = this.lastProgress.get(videoId) || 0;
          if (lastProgress > 0.9 && progress < 0.1) {
            console.log(`🔄 Video loop detected for ${videoId} - resetting progress`);
            // Video has looped, ensure progress starts from 0
          }

          // Store current progress for loop detection
          this.lastProgress.set(videoId, progress);

          const progressData: ProgressData = {
            videoId,
            currentTime,
            duration,
            progress: Math.min(Math.max(progress, 0), 1), // Clamp between 0 and 1
          };

          callback(progressData);
        }
      } catch (error) {
        console.warn(`Error tracking progress for video ${videoId}:`, error);
        // Stop tracking on error
        clearInterval(interval);
        this.progressIntervals.delete(videoId);
      }
    }, 100); // Update every 100ms for smooth progress

    this.progressIntervals.set(videoId, interval);
  }

  // Clean up all resources
  cleanup(): void {
    // Clear all intervals
    this.progressIntervals.forEach((interval) => {
      clearInterval(interval);
    });

    // Clear all data
    this.players.clear();
    this.progressCallbacks.clear();
    this.progressIntervals.clear();
    this.lastProgress.clear();
    this.activeVideoId = null;
  }
}

// Export singleton instance
export const videoProgressManager = VideoProgressManager.getInstance();
