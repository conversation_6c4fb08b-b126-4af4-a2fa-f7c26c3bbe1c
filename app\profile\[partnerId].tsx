/**
 * Partner Profile Screen Route
 * Displays partner's profile information
 */

import React from 'react';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { PartnerProfilePage } from '../../src/components/PartnerProfilePage';
import { useSelector } from 'react-redux';
import { RootState } from '../../src/redux/store';

export default function PartnerProfileScreen() {
  const { partnerId } = useLocalSearchParams<{ partnerId: string }>();
  const router = useRouter();
  
  // Get current user from Redux store
  const currentUser = useSelector((state: RootState) => state?.user?.currentUser);

  if (!partnerId || !currentUser?.id) {
    console.warn('⚠️ Missing partnerId or current user, cannot show profile');
    return null;
  }

  const handleStartChat = (partnerUserId: string) => {
    // Navigate to chat with this partner
    router.push(`/chat/${partnerUserId}`);
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <PartnerProfilePage
      partnerId={partnerId}
      currentUserId={currentUser.id}
      onBack={handleBack}
      onStartChat={handleStartChat}
    />
  );
}
