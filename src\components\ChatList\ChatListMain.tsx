import React, { useRef, useCallback, useState } from 'react';
import { FlatList, ActivityIndicator, RefreshControl, View, Text } from 'react-native';
import { IRACHAT_COLORS } from '../../styles/iraChatDesignSystem';
import { ModernChatItem, UnifiedChatItem } from '../ModernChatItem';
import { formatTime, getMessagePreview, getMessageTypeIcon } from '../../utils/chatUtils';
import { keyExtractor, getItemLayout } from '../../utils/chatListUtils';
import { ChatListEmptyState } from './ChatListEmptyState';

interface ChatListMainProps {
  filteredChats: UnifiedChatItem[];
  isLoadingChats: boolean;
  hasLoadedOnce: boolean;
  searchQuery: string;
  selectedChats: string[];
  isSelectionMode: boolean;
  isRefreshing: boolean;
  onChatPress: (chatId: string) => void;
  onLongPress: (chatId: string) => void;
  onDeletePress: (chatId: string) => void;
  onRefresh: () => Promise<void>;
}

export const ChatListMain: React.FC<ChatListMainProps> = ({
  filteredChats,
  isLoadingChats,
  hasLoadedOnce,
  searchQuery,
  selectedChats,
  isSelectionMode,
  isRefreshing,
  onChatPress,
  onLongPress,
  onDeletePress,
  onRefresh,
}) => {
  const flatListRef = useRef<FlatList>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Handle refresh with loading state
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setRefreshing(false);
    }
  }, [onRefresh]);

  // Render chat item
  const renderChatItem = useCallback(({ item }: { item: UnifiedChatItem }) => (
    <ModernChatItem
      item={item}
      onPress={() => onChatPress(item.id)}
      onLongPress={() => onLongPress(item.id)}
      onDeletePress={() => onDeletePress(item.id)}
      isSelected={selectedChats.includes(item.id)}
      isSelectionMode={isSelectionMode}
      formatTime={formatTime}
      getMessagePreview={getMessagePreview}
      getMessageTypeIcon={getMessageTypeIcon}
    />
  ), [onChatPress, onLongPress, onDeletePress, selectedChats, isSelectionMode]);

  // Show loading state
  if (isLoadingChats && !hasLoadedOnce) {
    return (
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center'
      }}>
        <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
        <Text style={{
          marginTop: 16,
          fontSize: 16,
          color: '#6b7280'
        }}>
          Loading chats...
        </Text>
      </View>
    );
  }

  // Show empty state
  if (filteredChats.length === 0) {
    return <ChatListEmptyState searchQuery={searchQuery} />;
  }

  // Show chat list
  return (
    <FlatList
      ref={flatListRef}
      data={filteredChats}
      keyExtractor={keyExtractor}
      renderItem={renderChatItem}
      getItemLayout={getItemLayout}
      refreshControl={
        <RefreshControl
          refreshing={refreshing || isRefreshing}
          onRefresh={handleRefresh}
          colors={[IRACHAT_COLORS.primary]}
          tintColor={IRACHAT_COLORS.primary}
        />
      }
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ paddingBottom: 100 }}
    />
  );
};
