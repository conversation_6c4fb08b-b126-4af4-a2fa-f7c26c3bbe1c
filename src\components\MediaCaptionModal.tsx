// 📝 MEDIA CAPTION MODAL
// Optional captioning before sending media

import React, { useState } from 'react';
import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  StyleSheet,
  Text,
  TextInput,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Video, ResizeMode } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MediaCaptionModalProps {
  visible: boolean;
  mediaUri: string;
  mediaType: 'image' | 'video' | 'audio';
  onSend: (caption?: string) => void;
  onCancel: () => void;
}

export const MediaCaptionModal: React.FC<MediaCaptionModalProps> = ({
  visible,
  mediaUri,
  mediaType,
  onSend,
  onCancel,
}) => {
  const [caption, setCaption] = useState('');

  const handleSend = () => {
    onSend(caption.trim() || undefined);
    setCaption('');
  };

  const handleCancel = () => {
    onCancel();
    setCaption('');
  };

  const renderMedia = () => {
    switch (mediaType) {
      case 'image':
        return (
          <Image
            source={{ uri: mediaUri }}
            style={styles.media}
            resizeMode="contain"
          />
        );
      case 'video':
        return (
          <Video
            source={{ uri: mediaUri }}
            style={styles.media}
            resizeMode={ResizeMode.CONTAIN}
            shouldPlay={false}
            useNativeControls={true}
          />
        );
      case 'audio':
        return (
          <View style={styles.audioPreview}>
            <Ionicons name="musical-notes" size={60} color="#666" />
            <Text style={styles.audioText}>Audio File</Text>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="slide"
      onRequestClose={handleCancel}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={handleCancel} style={styles.headerButton}>
            <Ionicons name="close" size={24} color="#007AFF" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Add Caption</Text>
          <TouchableOpacity onPress={handleSend} style={styles.headerButton}>
            <Text style={styles.sendText}>Send</Text>
          </TouchableOpacity>
        </View>

        {/* Media Preview */}
        <View style={styles.mediaContainer}>
          {renderMedia()}
        </View>

        {/* Caption Input */}
        <View style={styles.captionContainer}>
          <TextInput
            style={styles.captionInput}
            value={caption}
            onChangeText={setCaption}
            placeholder="Add a caption..."
            placeholderTextColor="#999"
            multiline={true}
            maxLength={200}
            textAlignVertical="top"
          />
          <Text style={styles.characterCount}>
            {caption.length}/200
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          <TouchableOpacity
            style={styles.sendButton}
            onPress={handleSend}
          >
            <Ionicons name="send" size={20} color="white" />
            <Text style={styles.sendButtonText}>
              {caption.trim() ? 'Send with Caption' : 'Send'}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerButton: {
    padding: 8,
    minWidth: 60,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  sendText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    textAlign: 'right',
  },
  mediaContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'black',
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  media: {
    width: '100%',
    height: '100%',
  },
  audioPreview: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
  audioText: {
    color: '#666',
    fontSize: 16,
    marginTop: 12,
  },
  captionContainer: {
    backgroundColor: 'white',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  captionInput: {
    fontSize: 16,
    minHeight: 80,
    maxHeight: 120,
    color: '#333',
  },
  characterCount: {
    fontSize: 12,
    color: '#999',
    textAlign: 'right',
    marginTop: 8,
  },
  actionContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  sendButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
  },
  sendButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});

export default MediaCaptionModal;
