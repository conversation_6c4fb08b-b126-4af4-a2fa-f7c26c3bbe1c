/**
 * IraChat Offline Engine - Main Integration Service
 * Provides a unified API for all offline/online functionality
 * Similar to WhatsApp's core messaging engine
 */

import { memoryCacheService } from './memoryCache';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';
import { offlineMessageService } from './offlineMessageService';
import { offlineChatService } from './offlineChatService';
import { Message , Chat } from '../types';

export interface IraChatConfig {
  enableOfflineMode: boolean;
  maxCacheSize: number; // MB
  syncInterval: number; // milliseconds
  retryAttempts: number;
  enableBackgroundSync: boolean;
  enablePushNotifications: boolean;
  autoDownloadMedia: boolean;
  maxMediaCacheSize: number; // MB
  enableEncryption: boolean;
  debugMode: boolean;
}

export interface IraChatStats {
  isOnline: boolean;
  isInitialized: boolean;
  cacheStats: Record<string, any>;
  databaseStats: Record<string, number>;
  networkStats: any;
  messageStats: any;
  chatStats: any;
  totalStorageUsed: number; // bytes
  lastSyncTime: number;
  pendingSyncItems: number;
}

export interface SyncProgress {
  phase: 'messages' | 'chats' | 'contacts' | 'media' | 'settings';
  progress: number; // 0-100
  total: number;
  completed: number;
  errors: number;
  currentItem?: string;
}

export interface ConflictResolution {
  strategy: 'local' | 'remote' | 'merge' | 'manual';
  localData: any;
  remoteData: any;
  resolvedData?: any;
  timestamp: number;
}

class IraChatOfflineEngine {
  private config: IraChatConfig = {
    enableOfflineMode: true,
    maxCacheSize: 200, // 200MB
    syncInterval: 30000, // 30 seconds
    retryAttempts: 3,
    enableBackgroundSync: true,
    enablePushNotifications: true,
    autoDownloadMedia: true,
    maxMediaCacheSize: 500, // 500MB
    enableEncryption: false,
    debugMode: false,
  };

  private isInitialized = false;
  private syncInProgress = false;
  private syncCallbacks: Map<string, (progress: SyncProgress) => void> = new Map();
  private conflictCallbacks: Map<string, (conflict: ConflictResolution) => Promise<ConflictResolution>> = new Map();
  private lastFullSync = 0;
  private backgroundSyncInterval: NodeJS.Timeout | null = null;

  /**
   * Initialize the IraChat offline engine
   */
  async initialize(config?: Partial<IraChatConfig>): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🚀 Initializing IraChat Offline Engine...');

      // Merge config
      this.config = { ...this.config, ...config };

      // Initialize core services in order
      await this.initializeServices();

      // Set up background sync if enabled
      if (this.config.enableBackgroundSync) {
        this.startBackgroundSync();
      }

      this.isInitialized = true;
      console.log('✅ IraChat Offline Engine initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize IraChat Offline Engine:', error);
      throw error;
    }
  }

  private async initializeServices(): Promise<void> {
    try {
      // Initialize in dependency order
      // Initialize memory cache service if available
      if ('initialize' in memoryCacheService && typeof memoryCacheService.initialize === 'function') {
        await memoryCacheService.initialize();
      }

      // Initialize database with retry logic
      await this.initializeDatabaseWithRetry();

      await networkStateManager.initialize();
      await offlineMessageService.initialize();
      await offlineChatService.initialize();

      console.log('✅ All core services initialized');
    } catch (error) {
      console.error('❌ Failed to initialize core services:', error);
      throw error;
    }
  }

  private async initializeDatabaseWithRetry(maxRetries: number = 3): Promise<void> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🗄️ Database initialization attempt ${attempt}/${maxRetries}`);
        await offlineDatabaseService.initialize();
        console.log('✅ Database initialized successfully');
        return;
      } catch (error) {
        lastError = error as Error;
        console.error(`❌ Database initialization attempt ${attempt} failed:`, error);

        if (attempt < maxRetries) {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }

    // If all retries failed, log warning but don't throw to allow app to continue
    console.warn('⚠️ Database initialization failed after all retries. App will continue without offline support.');
    console.warn('Last error:', lastError);

    // Mark as "initialized" even if database failed to prevent repeated attempts
    // The app will continue with Firebase-only functionality
  }

  /**
   * Send a message (works offline)
   */
  async sendMessage(
    chatId: string,
    text: string,
    senderId: string,
    type: Message['type'] = 'text',
    additionalData?: any
  ): Promise<string | null> {
    try {
      // Check if services are properly initialized
      if (!this.isInitialized) {
        console.warn('⚠️ IraChat Offline Engine not initialized, attempting to initialize...');
        await this.initialize();
      }

      const messageId = await offlineMessageService.sendMessage(
        chatId,
        text,
        senderId,
        type,
        additionalData
      );

      // Update chat's last message (optional, don't fail if this fails)
      try {
        await this.updateChatLastMessage(chatId, text, messageId);
      } catch (chatUpdateError) {
        console.warn('⚠️ Failed to update chat last message (non-critical):', chatUpdateError);
      }

      return messageId;
    } catch (error) {
      console.error('❌ Failed to send message to offline storage:', error);
      // Don't throw error - return null to indicate failure but allow app to continue
      return null;
    }
  }

  /**
   * Get messages for a chat
   */
  async getMessages(chatId: string, limit: number = 50, offset: number = 0): Promise<Message[]> {
    return offlineMessageService.getMessages(chatId, limit, offset);
  }

  /**
   * Get all chats
   */
  async getChats(includeArchived: boolean = false): Promise<Chat[]> {
    return offlineChatService.getAllChats(includeArchived);
  }

  /**
   * Create a new chat
   */
  async createChat(
    name: string,
    isGroup: boolean = false,
    participantIds: string[] = [],
    createdBy: string,
    description?: string
  ): Promise<string> {
    return offlineChatService.createChat(name, isGroup, participantIds, createdBy, description);
  }

  /**
   * Update chat information
   */
  async updateChat(chatId: string, updates: Partial<Chat>): Promise<boolean> {
    return offlineChatService.updateChat(chatId, updates);
  }

  /**
   * Delete a chat
   */
  async deleteChat(chatId: string): Promise<boolean> {
    return offlineChatService.deleteChat(chatId);
  }

  /**
   * Search messages across all chats
   */
  async searchMessages(query: string, chatId?: string): Promise<Message[]> {
    const result = await offlineMessageService.searchMessages({
      query,
      chatId,
      limit: 100,
    });
    return result.messages;
  }

  /**
   * Search chats
   */
  async searchChats(query: string): Promise<Chat[]> {
    const result = await offlineChatService.searchChats({
      query,
      limit: 50,
    });
    return result.chats;
  }

  /**
   * Mark chat as read
   */
  async markChatAsRead(chatId: string): Promise<boolean> {
    return offlineChatService.markChatAsRead(chatId);
  }

  /**
   * Pin/unpin chat
   */
  async pinChat(chatId: string, pin: boolean = true): Promise<boolean> {
    return offlineChatService.pinChat(chatId, pin);
  }

  /**
   * Archive/unarchive chat
   */
  async archiveChat(chatId: string, archive: boolean = true): Promise<boolean> {
    return offlineChatService.archiveChat(chatId, archive);
  }

  /**
   * Mute/unmute chat
   */
  async muteChat(chatId: string, mute: boolean = true, duration?: number): Promise<boolean> {
    const mutedUntil = mute && duration ? Date.now() + duration : undefined;
    return offlineChatService.muteChat(chatId, mute, mutedUntil);
  }

  /**
   * Get network status
   */
  getNetworkStatus(): any {
    return networkStateManager.getState();
  }

  /**
   * Check if app is online
   */
  isOnline(): boolean {
    return networkStateManager.isOnline();
  }

  /**
   * Check if app is offline
   */
  isOffline(): boolean {
    return networkStateManager.isOffline();
  }

  /**
   * Force sync with server
   */
  async forceSync(callback?: (progress: SyncProgress) => void): Promise<void> {
    if (this.syncInProgress) {
      console.log('⏳ Sync already in progress');
      return;
    }

    if (!this.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    this.syncInProgress = true;
    const callbackId = callback ? Math.random().toString(36) : '';
    
    if (callback && callbackId) {
      this.syncCallbacks.set(callbackId, callback);
    }

    try {
      console.log('🔄 Starting force sync...');

      // Sync messages
      await this.syncPhase('messages', 0, 25);
      
      // Sync chats
      await this.syncPhase('chats', 25, 50);
      
      // Sync contacts
      await this.syncPhase('contacts', 50, 75);
      
      // Sync media
      await this.syncPhase('media', 75, 90);
      
      // Sync settings
      await this.syncPhase('settings', 90, 100);

      this.lastFullSync = Date.now();
      console.log('✅ Force sync completed');
    } catch (error) {
      console.error('❌ Force sync failed:', error);
      throw error;
    } finally {
      this.syncInProgress = false;
      if (callbackId) {
        this.syncCallbacks.delete(callbackId);
      }
    }
  }

  private async syncPhase(phase: SyncProgress['phase'], startProgress: number, endProgress: number): Promise<void> {
    const progress: SyncProgress = {
      phase,
      progress: startProgress,
      total: 100,
      completed: 0,
      errors: 0,
    };

    this.notifySyncProgress(progress);

    // Simulate sync work (replace with actual sync logic)
    await new Promise(resolve => setTimeout(resolve, 1000));

    progress.progress = endProgress;
    progress.completed = 100;
    this.notifySyncProgress(progress);
  }

  private notifySyncProgress(progress: SyncProgress): void {
    this.syncCallbacks.forEach(callback => {
      try {
        callback(progress);
      } catch (error) {
        console.error('❌ Error in sync progress callback:', error);
      }
    });
  }

  /**
   * Get comprehensive stats
   */
  async getStats(): Promise<IraChatStats> {
    const [cacheStats, databaseStats, databaseSize] = await Promise.all([
      memoryCacheService.getOverallStats(),
      offlineDatabaseService.getTableStats(),
      offlineDatabaseService.getDatabaseSize(),
    ]);

    return {
      isOnline: this.isOnline(),
      isInitialized: this.isInitialized,
      cacheStats,
      databaseStats,
      networkStats: networkStateManager.getStats(),
      messageStats: offlineMessageService.getStats(),
      chatStats: offlineChatService.getStats(),
      totalStorageUsed: databaseSize,
      lastSyncTime: this.lastFullSync,
      pendingSyncItems: this.getPendingSyncCount(),
    };
  }

  private getPendingSyncCount(): number {
    // This would count all pending sync items across all services
    const messageStats = offlineMessageService.getStats();
    const chatStats = offlineChatService.getStats();
    
    return messageStats.pendingMessages + chatStats.pendingSync;
  }

  /**
   * Clear all data (for logout/reset)
   */
  async clearAllData(): Promise<void> {
    try {
      console.log('🧹 Clearing all data...');
      
      // Clear memory cache
      memoryCacheService.clearAll();
      
      // Clear database (this would need to be implemented in offlineDatabaseService)
      // await offlineDatabaseService.clearAllData();
      
      // Reset sync state
      this.lastFullSync = 0;
      
      console.log('✅ All data cleared');
    } catch (error) {
      console.error('❌ Failed to clear data:', error);
      throw error;
    }
  }

  /**
   * Handle memory pressure
   */
  handleMemoryPressure(): void {
    console.log('⚠️ Handling memory pressure...');
    memoryCacheService.handleMemoryPressure();
  }

  /**
   * Cleanup and shutdown
   */
  async cleanup(): Promise<void> {
    try {
      console.log('🧹 Cleaning up IraChat Offline Engine...');

      // Stop background sync
      if (this.backgroundSyncInterval) {
        clearInterval(this.backgroundSyncInterval);
        this.backgroundSyncInterval = null;
      }

      // Cleanup services
      offlineMessageService.cleanup();
      offlineChatService.cleanup();
      networkStateManager.cleanup();
      await offlineDatabaseService.close();

      // Clear callbacks
      this.syncCallbacks.clear();
      this.conflictCallbacks.clear();

      this.isInitialized = false;
      console.log('✅ IraChat Offline Engine cleaned up');
    } catch (error) {
      console.error('❌ Error during cleanup:', error);
    }
  }

  private async updateChatLastMessage(chatId: string, text: string, messageId: string): Promise<void> {
    try {
      await offlineChatService.updateChat(chatId, {
        lastMessageId: messageId,
        lastMessageText: text,
        lastMessageTimestamp: Date.now(),
      });
    } catch (error) {
      console.error('❌ Failed to update chat last message:', error);
    }
  }

  private startBackgroundSync(): void {
    this.backgroundSyncInterval = setInterval(async () => {
      if (this.isOnline() && !this.syncInProgress) {
        try {
          // Perform lightweight background sync
          await this.performBackgroundSync();
        } catch (error) {
          console.error('❌ Background sync failed:', error);
        }
      }
    }, this.config.syncInterval);

    console.log('✅ Background sync started');
  }

  private async performBackgroundSync(): Promise<void> {
    // Implement lightweight background sync
    // This would sync only the most critical data
    console.log('🔄 Performing background sync...');
    
    // For now, just update the last sync time
    this.lastFullSync = Date.now();
  }

  /**
   * Register for sync progress updates
   */
  onSyncProgress(callback: (progress: SyncProgress) => void): string {
    const id = Math.random().toString(36);
    this.syncCallbacks.set(id, callback);
    return id;
  }

  /**
   * Unregister sync progress callback
   */
  offSyncProgress(id: string): void {
    this.syncCallbacks.delete(id);
  }

  /**
   * Register for conflict resolution
   */
  onConflict(callback: (conflict: ConflictResolution) => Promise<ConflictResolution>): string {
    const id = Math.random().toString(36);
    this.conflictCallbacks.set(id, callback);
    return id;
  }

  /**
   * Unregister conflict resolution callback
   */
  offConflict(id: string): void {
    this.conflictCallbacks.delete(id);
  }

  /**
   * Get configuration
   */
  getConfig(): IraChatConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<IraChatConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Apply config changes
    if (updates.enableBackgroundSync !== undefined) {
      if (updates.enableBackgroundSync && !this.backgroundSyncInterval) {
        this.startBackgroundSync();
      } else if (!updates.enableBackgroundSync && this.backgroundSyncInterval) {
        clearInterval(this.backgroundSyncInterval);
        this.backgroundSyncInterval = null;
      }
    }
  }

  /**
   * Export data for backup
   */
  async exportData(): Promise<any> {
    try {
      const stats = await this.getStats();
      return {
        version: '1.0',
        timestamp: Date.now(),
        stats,
        // Add actual data export logic here
      };
    } catch (error) {
      console.error('❌ Failed to export data:', error);
      throw error;
    }
  }

  /**
   * Import data from backup
   */
  async importData(data: any): Promise<void> {
    try {
      console.log('📥 Importing data...', data ? 'with data' : 'no data');
      // Add actual data import logic here
      console.log('✅ Data imported successfully');
    } catch (error) {
      console.error('❌ Failed to import data:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const iraChatOfflineEngine = new IraChatOfflineEngine();
export default iraChatOfflineEngine;
