/**
 * Enhanced Caption Modal Component
 * Advanced caption input with text formatting, hashtag suggestions, mention support, and character limits
 */

import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  TextInput,
  StyleSheet,
  Dimensions,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  FlatList,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface HashtagSuggestion {
  tag: string;
  count: number;
}

interface MentionSuggestion {
  id: string;
  username: string;
  name: string;
  avatar?: string;
}

interface EnhancedCaptionModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (caption: string, hashtags: string[], mentions: string[]) => void;
  mediaUri?: string;
  mediaType?: 'photo' | 'video';
  initialCaption?: string;
  maxLength?: number;
}

export const EnhancedCaptionModal: React.FC<EnhancedCaptionModalProps> = ({
  visible,
  onClose,
  onSubmit,
  mediaUri,
  mediaType = 'photo',
  initialCaption = '',
  maxLength = 500,
}) => {
  const [caption, setCaption] = useState(initialCaption);
  const [showHashtagSuggestions, setShowHashtagSuggestions] = useState(false);
  const [showMentionSuggestions, setShowMentionSuggestions] = useState(false);
  const [hashtagSuggestions, setHashtagSuggestions] = useState<HashtagSuggestion[]>([]);
  const [mentionSuggestions, setMentionSuggestions] = useState<MentionSuggestion[]>([]);
  const [currentHashtag, setCurrentHashtag] = useState('');
  const [currentMention, setCurrentMention] = useState('');
  const [cursorPosition, setCursorPosition] = useState(0);
  const [extractedHashtags, setExtractedHashtags] = useState<string[]>([]);
  const [extractedMentions, setExtractedMentions] = useState<string[]>([]);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;

  // Text input ref
  const textInputRef = useRef<TextInput>(null);

  // Mock data for suggestions
  const popularHashtags: HashtagSuggestion[] = [
    { tag: 'love', count: 1200000 },
    { tag: 'instagood', count: 980000 },
    { tag: 'photooftheday', count: 850000 },
    { tag: 'beautiful', count: 720000 },
    { tag: 'happy', count: 650000 },
    { tag: 'nature', count: 580000 },
    { tag: 'art', count: 520000 },
    { tag: 'photography', count: 480000 },
  ];

  const mockUsers: MentionSuggestion[] = [
    { id: '1', username: 'john_doe', name: 'John Doe' },
    { id: '2', username: 'jane_smith', name: 'Jane Smith' },
    { id: '3', username: 'mike_wilson', name: 'Mike Wilson' },
    { id: '4', username: 'sarah_jones', name: 'Sarah Jones' },
  ];

  useEffect(() => {
    if (visible) {
      // Entrance animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();

      // Focus text input
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 300);
    } else {
      // Reset state when modal closes
      setCaption(initialCaption);
      setShowHashtagSuggestions(false);
      setShowMentionSuggestions(false);
      setCurrentHashtag('');
      setCurrentMention('');
    }
  }, [visible, initialCaption]);

  useEffect(() => {
    // Extract hashtags and mentions from caption
    const hashtags = caption.match(/#\w+/g) || [];
    const mentions = caption.match(/@\w+/g) || [];
    
    setExtractedHashtags(hashtags.map(tag => tag.substring(1)));
    setExtractedMentions(mentions.map(mention => mention.substring(1)));
  }, [caption]);

  const handleTextChange = (text: string) => {
    if (text.length <= maxLength) {
      setCaption(text);
      
      // Check for hashtag or mention typing
      const words = text.split(' ');
      const currentWord = words[words.length - 1];
      
      if (currentWord.startsWith('#') && currentWord.length > 1) {
        setCurrentHashtag(currentWord.substring(1));
        setShowHashtagSuggestions(true);
        setShowMentionSuggestions(false);
        
        // Filter hashtag suggestions
        const filtered = popularHashtags.filter(tag =>
          tag.tag.toLowerCase().includes(currentWord.substring(1).toLowerCase())
        );
        setHashtagSuggestions(filtered);
      } else if (currentWord.startsWith('@') && currentWord.length > 1) {
        setCurrentMention(currentWord.substring(1));
        setShowMentionSuggestions(true);
        setShowHashtagSuggestions(false);
        
        // Filter mention suggestions
        const filtered = mockUsers.filter(user =>
          user.username.toLowerCase().includes(currentWord.substring(1).toLowerCase()) ||
          user.name.toLowerCase().includes(currentWord.substring(1).toLowerCase())
        );
        setMentionSuggestions(filtered);
      } else {
        setShowHashtagSuggestions(false);
        setShowMentionSuggestions(false);
      }
    }
  };

  const handleHashtagSelect = (hashtag: string) => {
    const words = caption.split(' ');
    words[words.length - 1] = `#${hashtag}`;
    setCaption(words.join(' ') + ' ');
    setShowHashtagSuggestions(false);
    textInputRef.current?.focus();
  };

  const handleMentionSelect = (user: MentionSuggestion) => {
    const words = caption.split(' ');
    words[words.length - 1] = `@${user.username}`;
    setCaption(words.join(' ') + ' ');
    setShowMentionSuggestions(false);
    textInputRef.current?.focus();
  };

  const handleSubmit = () => {
    onSubmit(caption.trim(), extractedHashtags, extractedMentions);
    onClose();
  };

  const getCharacterCountColor = () => {
    const remaining = maxLength - caption.length;
    if (remaining < 50) return '#FF3040';
    if (remaining < 100) return '#FF9500';
    return '#666666';
  };

  const renderHashtagSuggestion = ({ item }: { item: HashtagSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleHashtagSelect(item.tag)}
    >
      <View style={styles.hashtagIcon}>
        <Text style={styles.hashtagIconText}>#</Text>
      </View>
      <View style={styles.suggestionContent}>
        <Text style={styles.suggestionText}>{item.tag}</Text>
        <Text style={styles.suggestionCount}>
          {item.count > 1000000 
            ? `${(item.count / 1000000).toFixed(1)}M posts`
            : `${(item.count / 1000).toFixed(0)}K posts`
          }
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderMentionSuggestion = ({ item }: { item: MentionSuggestion }) => (
    <TouchableOpacity
      style={styles.suggestionItem}
      onPress={() => handleMentionSelect(item)}
    >
      <View style={styles.mentionAvatar}>
        <Text style={styles.mentionAvatarText}>
          {item.name.charAt(0).toUpperCase()}
        </Text>
      </View>
      <View style={styles.suggestionContent}>
        <Text style={styles.suggestionText}>{item.name}</Text>
        <Text style={styles.suggestionSubtext}>@{item.username}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <KeyboardAvoidingView
            style={styles.keyboardAvoidingView}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            keyboardVerticalOffset={Platform.OS === 'ios' ? 60 : 80}
          >
            {/* Header */}
            <View style={styles.header}>
              <TouchableOpacity style={styles.headerButton} onPress={onClose}>
                <Ionicons name="close" size={24} color="#000000" />
              </TouchableOpacity>
              <Text style={styles.headerTitle}>Add Caption</Text>
              <TouchableOpacity 
                style={[styles.headerButton, styles.shareButton]} 
                onPress={handleSubmit}
                disabled={caption.trim().length === 0}
              >
                <Text style={[
                  styles.shareButtonText,
                  caption.trim().length === 0 && styles.shareButtonTextDisabled
                ]}>
                  Share
                </Text>
              </TouchableOpacity>
            </View>

            {/* Media Preview */}
            {mediaUri && (
              <View style={styles.mediaPreviewContainer}>
                <Image
                  source={{ uri: mediaUri }}
                  style={styles.mediaPreview}
                  resizeMode="cover"
                />
                <View style={styles.mediaTypeIndicator}>
                  <Ionicons 
                    name={mediaType === 'video' ? 'videocam' : 'image'} 
                    size={16} 
                    color="#FFFFFF" 
                  />
                </View>
              </View>
            )}

            {/* Caption Input */}
            <View style={styles.inputContainer}>
              <TextInput
                ref={textInputRef}
                style={styles.textInput}
                placeholder="Write a caption..."
                placeholderTextColor="#999999"
                value={caption}
                onChangeText={handleTextChange}
                onSelectionChange={(event) => {
                  setCursorPosition(event.nativeEvent.selection.start);
                }}
                multiline
                maxLength={maxLength}
                textAlignVertical="top"
              />
              
              {/* Character Counter */}
              <Text style={[styles.characterCounter, { color: getCharacterCountColor() }]}>
                {caption.length}/{maxLength}
              </Text>
            </View>

            {/* Hashtag/Mention Suggestions */}
            {showHashtagSuggestions && (
              <View style={styles.suggestionsContainer}>
                <FlatList
                  data={hashtagSuggestions}
                  renderItem={renderHashtagSuggestion}
                  keyExtractor={(item, index) => `${index}`}
                  style={styles.suggestionsList}
                  keyboardShouldPersistTaps="handled"
                />
              </View>
            )}
            {showMentionSuggestions && (
              <View style={styles.suggestionsContainer}>
                <FlatList
                  data={mentionSuggestions}
                  renderItem={renderMentionSuggestion}
                  keyExtractor={(item, index) => `${index}`}
                  style={styles.suggestionsList}
                  keyboardShouldPersistTaps="handled"
                />
              </View>
            )}

            {/* Tags Summary */}
            {(extractedHashtags.length > 0 || extractedMentions.length > 0) && (
              <View style={styles.tagsSummary}>
                {extractedHashtags.length > 0 && (
                  <View style={styles.tagsSection}>
                    <Text style={styles.tagsSectionTitle}>Hashtags ({extractedHashtags.length})</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                      {extractedHashtags.map((tag, index) => (
                        <View key={index} style={styles.tagChip}>
                          <Text style={styles.tagChipText}>#{tag}</Text>
                        </View>
                      ))}
                    </ScrollView>
                  </View>
                )}
                
                {extractedMentions.length > 0 && (
                  <View style={styles.tagsSection}>
                    <Text style={styles.tagsSectionTitle}>Mentions ({extractedMentions.length})</Text>
                    <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                      {extractedMentions.map((mention, index) => (
                        <View key={index} style={styles.tagChip}>
                          <Text style={styles.tagChipText}>@{mention}</Text>
                        </View>
                      ))}
                    </ScrollView>
                  </View>
                )}
              </View>
            )}
          </KeyboardAvoidingView>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: screenWidth - 40,
    maxHeight: screenHeight * 0.8,
    overflow: 'hidden',
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  headerButton: {
    padding: 4,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  shareButton: {
    backgroundColor: '#25D366',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  shareButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  shareButtonTextDisabled: {
    color: '#CCCCCC',
  },
  mediaPreviewContainer: {
    height: 120,
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
  },
  mediaPreview: {
    width: '100%',
    height: '100%',
  },
  mediaTypeIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 4,
  },
  inputContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  textInput: {
    fontSize: 16,
    color: '#000000',
    minHeight: 120,
    maxHeight: 200,
    textAlignVertical: 'top',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  characterCounter: {
    fontSize: 12,
    textAlign: 'right',
    marginTop: 8,
    fontWeight: '500',
  },
  suggestionsContainer: {
    maxHeight: 200,
    marginHorizontal: 16,
    marginTop: 8,
    backgroundColor: '#F8F8F8',
    borderRadius: 12,
    overflow: 'hidden',
  },
  suggestionsList: {
    flex: 1,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  hashtagIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#25D366',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  hashtagIconText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  mentionAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#666666',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  mentionAvatarText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  suggestionCount: {
    fontSize: 12,
    color: '#666666',
  },
  suggestionSubtext: {
    fontSize: 14,
    color: '#666666',
  },
  tagsSummary: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  tagsSection: {
    marginBottom: 8,
  },
  tagsSectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
    marginBottom: 6,
  },
  tagChip: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 6,
  },
  tagChipText: {
    fontSize: 12,
    color: '#25D366',
    fontWeight: '500',
  },
});
