/**
 * Enhanced Offline-First Message Service for IraChat
 * Comprehensive message handling with full offline support
 * Similar to <PERSON>s<PERSON><PERSON>'s message system with delivery tracking
 */

import { Message } from '../types';
import { offlineDatabaseService, LocalMessage as DBLocalMessage, SyncStatus } from './offlineDatabase';
import { memoryCacheService } from './memoryCache';
import { networkStateManager } from './networkStateManager';
import { messageSyncService } from './messageSyncService';

// Extended LocalMessage interface with additional properties needed for this service
export interface ExtendedLocalMessage extends DBLocalMessage {
  chatId: string;
  deliveryStatus?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  readBy?: string[];
  deliveredTo?: string[];
  mediaThumbnail?: string;
  mediaType?: string;
  width?: number;
  height?: number;
  replyToMessageId?: string;
  replyToText?: string;
  replyToSenderName?: string;
  replyToType?: string;
  mentions?: string[];
  isStarred?: boolean;
  starredAt?: number;
}

export interface MessageDeliveryStatus {
  messageId: string;
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  timestamp: number;
  error?: string;
}

export interface MessageReadReceipt {
  messageId: string;
  userId: string;
  readAt: number;
}

export interface MessageQueue {
  id: string;
  message: ExtendedLocalMessage;
  priority: number;
  retryCount: number;
  nextRetry: number;
  maxRetries: number;
}

export interface MessageSearchOptions {
  query: string;
  chatId?: string;
  senderId?: string;
  messageType?: Message['type'];
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

export interface MessageSearchResult {
  messages: ExtendedLocalMessage[];
  totalCount: number;
  hasMore: boolean;
}

class OfflineMessageService {
  private messageQueue: Map<string, MessageQueue> = new Map();
  private deliveryCallbacks: Map<string, (status: MessageDeliveryStatus) => void> = new Map();
  private readReceiptCallbacks: Map<string, (receipt: MessageReadReceipt) => void> = new Map();
  private isInitialized = false;
  private syncInterval: NodeJS.Timeout | null = null;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('💬 Initializing offline message service...');

      // Initialize dependencies
      await offlineDatabaseService.initialize();
      
      // Set up network state listener
      networkStateManager.addListener('messageService', this.handleNetworkStateChange.bind(this), 10);

      // Start periodic sync
      this.startPeriodicSync();

      // Load pending messages from database
      await this.loadPendingMessages();

      this.isInitialized = true;
      console.log('✅ Offline message service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize offline message service:', error);
      throw error;
    }
  }

  /**
   * Send a message (works offline)
   */
  async sendMessage(
    chatId: string,
    text: string,
    senderId: string,
    type: Message['type'] = 'text',
    additionalData?: Partial<ExtendedLocalMessage>
  ): Promise<string> {
    const now = Date.now();
    const localId = `msg_${now}_${Math.random().toString(36).substring(2, 9)}`;

    const message: ExtendedLocalMessage = {
      id: localId, // Required by Message interface
      localId,
      chatId,
      text,
      senderId,
      timestamp: new Date(now),
      status: 'sent',
      type,
      syncStatus: {
        status: 'pending',
        retryCount: 0,
      },
      createdAt: now,
      updatedAt: now,
      isDeleted: false,
      deliveryStatus: 'sending',
      readBy: [],
      deliveredTo: [],
      ...additionalData,
    };

    try {
      // Save to database immediately
      await this.saveMessageToDatabase(message);

      // Cache in memory for instant access
      memoryCacheService.setMessage(localId, message);

      // Add to sync queue if online, or queue for later if offline
      if (networkStateManager.isOnline()) {
        await this.queueMessageForSync(message, 1); // High priority
      } else {
        await this.queueMessageForSync(message, 0); // Normal priority
        console.log('📱 Message queued for sync when online:', localId);
      }

      // Notify delivery status
      this.notifyDeliveryStatus({
        messageId: localId,
        status: 'sending',
        timestamp: now,
      });

      return localId;
    } catch (error) {
      console.error('❌ Failed to send message:', error);
      
      // Update message status to failed
      message.deliveryStatus = 'failed';
      message.syncStatus.status = 'failed';
      message.syncStatus.errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      await this.updateMessageInDatabase(message);
      memoryCacheService.setMessage(localId, message);

      this.notifyDeliveryStatus({
        messageId: localId,
        status: 'failed',
        timestamp: now,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      throw error;
    }
  }

  /**
   * Get messages for a chat (with caching)
   */
  async getMessages(chatId: string, limit: number = 50, offset: number = 0): Promise<ExtendedLocalMessage[]> {
    try {
      // Try cache first for recent messages
      if (offset === 0) {
        const cacheKey = `chat_messages_${chatId}`;
        const cachedMessages = memoryCacheService.getSettings(cacheKey) as ExtendedLocalMessage[];
        if (cachedMessages && cachedMessages.length > 0) {
          return cachedMessages.slice(0, limit);
        }
      }

      // Get from database - with safety checks
      if (!offlineDatabaseService.isReady()) {
        console.warn('⚠️ Offline database not ready in getMessages');
        return [];
      }

      const db = offlineDatabaseService.getDatabase();
      if (!db) {
        console.warn('⚠️ Database is null in getMessages');
        return [];
      }

      const result = await db.getAllAsync(`
        SELECT * FROM messages
        WHERE chatId = ? AND isDeleted = 0
        ORDER BY timestamp DESC
        LIMIT ? OFFSET ?
      `, [chatId, limit, offset]);

      const messages = result.map(row => this.rowToMessage(row as any));

      // Cache recent messages
      if (offset === 0 && messages.length > 0) {
        const cacheKey = `chat_messages_${chatId}`;
        memoryCacheService.setSettings(cacheKey, messages);
      }

      return messages;
    } catch (error) {
      console.error('❌ Failed to get messages:', error);
      return [];
    }
  }

  /**
   * Get a specific message
   */
  async getMessage(messageId: string): Promise<ExtendedLocalMessage | null> {
    try {
      // Try cache first
      const cachedMessage = memoryCacheService.getMessage(messageId);
      if (cachedMessage) {
        return cachedMessage as ExtendedLocalMessage;
      }

      // Get from database
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getFirstAsync(`
        SELECT * FROM messages WHERE localId = ? OR id = ?
      `, [messageId, messageId]);

      if (!result) return null;

      const message = this.rowToMessage(result as any);
      
      // Cache the result
      memoryCacheService.setMessage(messageId, message);
      
      return message;
    } catch (error) {
      console.error('❌ Failed to get message:', error);
      return null;
    }
  }

  /**
   * Update message (edit, delete, etc.)
   */
  async updateMessage(messageId: string, updates: Partial<ExtendedLocalMessage>): Promise<boolean> {
    try {
      const existingMessage = await this.getMessage(messageId);
      if (!existingMessage) {
        throw new Error('Message not found');
      }

      const updatedMessage: ExtendedLocalMessage = {
        ...existingMessage,
        ...updates,
        updatedAt: Date.now(),
      };

      // If this is an edit, mark as edited
      if (updates.text && updates.text !== existingMessage.text) {
        updatedMessage.isEdited = true;
        updatedMessage.editedAt = Date.now();
      }

      // Update sync status to pending
      updatedMessage.syncStatus = {
        status: 'pending',
        retryCount: 0,
      };

      await this.updateMessageInDatabase(updatedMessage);
      memoryCacheService.setMessage(messageId, updatedMessage);

      // Queue for sync
      if (networkStateManager.isOnline()) {
        await this.queueMessageForSync(updatedMessage, 1);
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to update message:', error);
      return false;
    }
  }

  /**
   * Delete message
   */
  async deleteMessage(messageId: string, deleteForEveryone: boolean = false): Promise<boolean> {
    try {
      const message = await this.getMessage(messageId);
      if (!message) return false;

      const now = Date.now();
      const updatedMessage: ExtendedLocalMessage = {
        ...message,
        isDeleted: true,
        deletedAt: now,
        updatedAt: now,
        // Add deleteForEveryone flag to the message
        ...(deleteForEveryone && { deletedForEveryone: true }),
        syncStatus: {
          status: 'pending',
          retryCount: 0,
        },
      };

      await this.updateMessageInDatabase(updatedMessage);
      memoryCacheService.deleteMessage(messageId);

      // Queue for sync
      if (networkStateManager.isOnline()) {
        await this.queueMessageForSync(updatedMessage, 1);
      }

      return true;
    } catch (error) {
      console.error('❌ Failed to delete message:', error);
      return false;
    }
  }

  /**
   * Mark message as read
   */
  async markMessageAsRead(messageId: string, userId: string): Promise<void> {
    try {
      const message = await this.getMessage(messageId);
      if (!message) return;

      if (!message.readBy) message.readBy = [];
      if (!message.readBy.includes(userId)) {
        message.readBy.push(userId);
        message.updatedAt = Date.now();
        
        await this.updateMessageInDatabase(message);
        memoryCacheService.setMessage(messageId, message);

        // Notify read receipt
        this.notifyReadReceipt({
          messageId,
          userId,
          readAt: Date.now(),
        });

        // Queue for sync
        if (networkStateManager.isOnline()) {
          await this.queueMessageForSync(message, 0);
        }
      }
    } catch (error) {
      console.error('❌ Failed to mark message as read:', error);
    }
  }

  /**
   * Search messages
   */
  async searchMessages(options: MessageSearchOptions): Promise<MessageSearchResult> {
    try {
      const db = offlineDatabaseService.getDatabase();
      let query = `
        SELECT * FROM messages 
        WHERE isDeleted = 0 AND text LIKE ?
      `;
      const params: any[] = [`%${options.query}%`];

      if (options.chatId) {
        query += ' AND chatId = ?';
        params.push(options.chatId);
      }

      if (options.senderId) {
        query += ' AND senderId = ?';
        params.push(options.senderId);
      }

      if (options.messageType) {
        query += ' AND type = ?';
        params.push(options.messageType);
      }

      if (options.startDate) {
        query += ' AND timestamp >= ?';
        params.push(options.startDate.getTime());
      }

      if (options.endDate) {
        query += ' AND timestamp <= ?';
        params.push(options.endDate.getTime());
      }

      query += ' ORDER BY timestamp DESC';

      if (options.limit) {
        query += ' LIMIT ?';
        params.push(options.limit);
      }

      if (options.offset) {
        query += ' OFFSET ?';
        params.push(options.offset);
      }

      const result = await db.getAllAsync(query, params);
      const messages = result.map(row => this.rowToMessage(row as any));

      // Get total count
      let countQuery = `
        SELECT COUNT(*) as count FROM messages 
        WHERE isDeleted = 0 AND text LIKE ?
      `;
      const countParams = [`%${options.query}%`];

      if (options.chatId) {
        countQuery += ' AND chatId = ?';
        countParams.push(options.chatId);
      }

      const countResult = await db.getFirstAsync<{ count: number }>(countQuery, countParams);
      const totalCount = countResult?.count || 0;

      return {
        messages,
        totalCount,
        hasMore: (options.offset || 0) + messages.length < totalCount,
      };
    } catch (error) {
      console.error('❌ Failed to search messages:', error);
      return { messages: [], totalCount: 0, hasMore: false };
    }
  }

  // Private helper methods
  private async saveMessageToDatabase(message: ExtendedLocalMessage): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    
    await db.runAsync(`
      INSERT OR REPLACE INTO messages (
        localId, id, chatId, senderId, text, type, timestamp, status,
        mediaUrl, mediaThumbnail, mediaType, fileName, fileSize, duration,
        width, height, replyToMessageId, replyToText, replyToSenderName,
        replyToType, reactions, mentions, isEdited, editedAt, isPinned,
        pinnedAt, pinnedBy, isForwarded, forwardedFrom, isStarred, starredAt,
        syncStatus, lastSyncAttempt, retryCount, errorMessage,
        createdAt, updatedAt, isDeleted, deletedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      message.localId,
      message.id || null,
      message.chatId,
      message.senderId,
      message.text || null,
      message.type,
      typeof message.timestamp === 'object' ? message.timestamp.getTime() : message.timestamp,
      message.status,
      message.mediaUrl || null,
      message.mediaThumbnail || null,
      message.mediaType || null,
      message.fileName || null,
      message.fileSize || null,
      message.duration || null,
      message.width || null,
      message.height || null,
      message.replyToMessageId || null,
      message.replyToText || null,
      message.replyToSenderName || null,
      message.replyToType || null,
      message.reactions ? JSON.stringify(message.reactions) : null,
      message.mentions ? JSON.stringify(message.mentions) : null,
      message.isEdited ? 1 : 0,
      message.editedAt || null,
      message.isPinned ? 1 : 0,
      message.pinnedAt || null,
      message.pinnedBy || null,
      message.isForwarded ? 1 : 0,
      message.forwardedFrom || null,
      message.isStarred ? 1 : 0,
      message.starredAt || null,
      message.syncStatus.status,
      message.syncStatus.lastSyncAttempt || null,
      message.syncStatus.retryCount,
      message.syncStatus.errorMessage || null,
      message.createdAt,
      message.updatedAt,
      message.isDeleted ? 1 : 0,
      message.deletedAt || null,
    ]);
  }

  private async updateMessageInDatabase(message: ExtendedLocalMessage): Promise<void> {
    await this.saveMessageToDatabase(message);
  }

  private rowToMessage(row: any): ExtendedLocalMessage {
    return {
      localId: row.localId,
      id: row.id,
      chatId: row.chatId,
      senderId: row.senderId,
      text: row.text,
      type: row.type,
      timestamp: new Date(row.timestamp),
      status: row.status,
      mediaUrl: row.mediaUrl,
      mediaThumbnail: row.mediaThumbnail,
      mediaType: row.mediaType,
      fileName: row.fileName,
      fileSize: row.fileSize,
      duration: row.duration,
      width: row.width,
      height: row.height,
      replyToMessageId: row.replyToMessageId,
      replyToText: row.replyToText,
      replyToSenderName: row.replyToSenderName,
      replyToType: row.replyToType,
      reactions: row.reactions ? JSON.parse(row.reactions) : undefined,
      mentions: row.mentions ? JSON.parse(row.mentions) : undefined,
      isEdited: Boolean(row.isEdited),
      editedAt: row.editedAt,
      isPinned: Boolean(row.isPinned),
      pinnedAt: row.pinnedAt,
      pinnedBy: row.pinnedBy,
      isForwarded: Boolean(row.isForwarded),
      forwardedFrom: row.forwardedFrom,
      isStarred: Boolean(row.isStarred),
      starredAt: row.starredAt,
      syncStatus: {
        status: row.syncStatus,
        lastSyncAttempt: row.lastSyncAttempt,
        retryCount: row.retryCount,
        errorMessage: row.errorMessage,
      },
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
      isDeleted: Boolean(row.isDeleted),
      deletedAt: row.deletedAt,
    };
  }

  private async queueMessageForSync(message: ExtendedLocalMessage, priority: number): Promise<void> {
    const queueItem: MessageQueue = {
      id: message.localId!,
      message,
      priority,
      retryCount: 0,
      nextRetry: Date.now(),
      maxRetries: 3,
    };

    this.messageQueue.set(message.localId!, queueItem);
    
    // Process queue immediately if online
    if (networkStateManager.isOnline()) {
      this.processMessageQueue();
    }
  }

  private async processMessageQueue(): Promise<void> {
    if (!networkStateManager.isOnline() || this.messageQueue.size === 0) return;

    // Sort by priority and retry time
    const queueItems = Array.from(this.messageQueue.values())
      .filter(item => item.nextRetry <= Date.now())
      .sort((a, b) => b.priority - a.priority || a.nextRetry - b.nextRetry);

    for (const item of queueItems.slice(0, 5)) { // Process up to 5 at a time
      try {
        // Use the public sync method instead of private syncSingleMessage
        await messageSyncService.syncPendingMessages();
        this.messageQueue.delete(item.id);

        // Update delivery status
        this.notifyDeliveryStatus({
          messageId: item.id,
          status: 'sent',
          timestamp: Date.now(),
        });
      } catch (error) {
        console.error('❌ Failed to sync message:', error);

        item.retryCount++;
        if (item.retryCount >= item.maxRetries) {
          this.messageQueue.delete(item.id);
          
          // Update message status to failed
          item.message.syncStatus.status = 'failed';
          item.message.syncStatus.errorMessage = error instanceof Error ? error.message : 'Unknown error';
          await this.updateMessageInDatabase(item.message);
          
          this.notifyDeliveryStatus({
            messageId: item.id,
            status: 'failed',
            timestamp: Date.now(),
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        } else {
          // Schedule retry with exponential backoff
          item.nextRetry = Date.now() + (Math.pow(2, item.retryCount) * 1000);
        }
      }
    }
  }

  private async loadPendingMessages(): Promise<void> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT * FROM messages 
        WHERE syncStatus = 'pending' 
        ORDER BY createdAt ASC
      `);

      for (const row of result) {
        const message = this.rowToMessage(row as any);
        await this.queueMessageForSync(message, 0);
      }

      console.log(`📤 Loaded ${result.length} pending messages for sync`);
    } catch (error) {
      console.error('❌ Failed to load pending messages:', error);
    }
  }

  private handleNetworkStateChange(networkState: any): void {
    if (networkState.isConnected) {
      console.log('🔄 Network connected, processing message queue...');
      this.processMessageQueue();
    }
  }

  private startPeriodicSync(): void {
    this.syncInterval = setInterval(() => {
      if (networkStateManager.isOnline()) {
        this.processMessageQueue();
      }
    }, 30000); // Every 30 seconds
  }

  private notifyDeliveryStatus(status: MessageDeliveryStatus): void {
    const callback = this.deliveryCallbacks.get(status.messageId);
    if (callback) {
      callback(status);
    }
  }

  private notifyReadReceipt(receipt: MessageReadReceipt): void {
    const callback = this.readReceiptCallbacks.get(receipt.messageId);
    if (callback) {
      callback(receipt);
    }
  }

  // Public callback registration methods
  onDeliveryStatus(messageId: string, callback: (status: MessageDeliveryStatus) => void): void {
    this.deliveryCallbacks.set(messageId, callback);
  }

  onReadReceipt(messageId: string, callback: (receipt: MessageReadReceipt) => void): void {
    this.readReceiptCallbacks.set(messageId, callback);
  }

  removeDeliveryCallback(messageId: string): void {
    this.deliveryCallbacks.delete(messageId);
  }

  removeReadReceiptCallback(messageId: string): void {
    this.readReceiptCallbacks.delete(messageId);
  }

  // Sync offline messages for a specific chat
  async syncOfflineMessages(chatId: string): Promise<void> {
    try {
      console.log(`🔄 Syncing offline messages for chat: ${chatId}`);

      // Get all pending messages for this chat
      const pendingMessages = Array.from(this.messageQueue.values())
        .filter(item => item.message.chatId === chatId && item.message.syncStatus.status === 'pending');

      if (pendingMessages.length === 0) {
        console.log(`✅ No offline messages to sync for chat: ${chatId}`);
        return;
      }

      console.log(`📤 Syncing ${pendingMessages.length} offline messages for chat: ${chatId}`);

      // Trigger the message queue processing which will handle these messages
      await this.processMessageQueue();

      console.log(`✅ Finished syncing offline messages for chat: ${chatId}`);
    } catch (error) {
      console.error(`❌ Failed to sync offline messages for chat ${chatId}:`, error);
    }
  }

  // Cleanup
  cleanup(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    networkStateManager.removeListener('messageService');
    this.messageQueue.clear();
    this.deliveryCallbacks.clear();
    this.readReceiptCallbacks.clear();

    this.isInitialized = false;
    console.log('🧹 Offline message service cleaned up');
  }

  // Statistics
  getStats(): {
    queueSize: number;
    pendingMessages: number;
    failedMessages: number;
    totalMessages: number;
  } {
    return {
      queueSize: this.messageQueue.size,
      pendingMessages: Array.from(this.messageQueue.values()).filter(item => item.message.syncStatus.status === 'pending').length,
      failedMessages: Array.from(this.messageQueue.values()).filter(item => item.message.syncStatus.status === 'failed').length,
      totalMessages: this.messageQueue.size,
    };
  }
}

// Export singleton instance
export const offlineMessageService = new OfflineMessageService();
export default offlineMessageService;
