/**
 * Pinned Message Bar Component for IraChat
 * Shows pinned messages with auto-cycling for multiple messages
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../contexts/ThemeContext';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface PinnedMessage {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  timestamp: Date;
  pinnedBy: string;
  pinnedAt: Date;
  expiresAt?: Date;
  type: 'text' | 'image' | 'video' | 'audio' | 'document';
}

interface PinnedMessageBarProps {
  pinnedMessages: PinnedMessage[]; // Changed to array for multiple messages
  onPress?: (messageId: string) => void;
  onUnpin?: (messageId: string) => void;
  onNavigateToMessage?: (messageId: string) => void;
  currentUserId?: string;
  isGroupChat?: boolean;
  groupAdmins?: string[];
}

export const PinnedMessageBar: React.FC<PinnedMessageBarProps> = ({
  pinnedMessages,
  onPress,
  onUnpin,
  onNavigateToMessage,
  currentUserId,
  isGroupChat = false,
  groupAdmins = [],
}) => {
  const { colors } = useTheme();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(0));
  const [currentIndex, setCurrentIndex] = useState(0);
  const [timeLeft, setTimeLeft] = useState<string>('');
  const cycleIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const expirationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const currentMessage = pinnedMessages && pinnedMessages.length > 0 ? pinnedMessages[currentIndex] : null;

  // Auto-cycling effect for multiple pinned messages
  useEffect(() => {
    // Clear any existing interval first
    if (cycleIntervalRef.current) {
      clearInterval(cycleIntervalRef.current);
      cycleIntervalRef.current = null;
    }

    if (pinnedMessages && pinnedMessages.length > 1) {
      console.log('🔄 Starting pinned message cycling for', pinnedMessages.length, 'messages');

      cycleIntervalRef.current = setInterval(() => {
        console.log('🔄 Cycling to next pinned message');

        // Slide out current message
        Animated.timing(slideAnim, {
          toValue: -SCREEN_WIDTH,
          duration: 300,
          useNativeDriver: false,
        }).start(() => {
          // Update to next message
          setCurrentIndex((prevIndex) => {
            const nextIndex = (prevIndex + 1) % pinnedMessages.length;
            console.log('🔄 Moving from index', prevIndex, 'to', nextIndex);
            return nextIndex;
          });

          // Reset slide position and slide in new message
          slideAnim.setValue(SCREEN_WIDTH);
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: false,
          }).start();
        });
      }, 4000); // 4 seconds interval
    } else {
      console.log('🔄 Stopping pinned message cycling - only', pinnedMessages?.length || 0, 'messages');
    }

    return () => {
      if (cycleIntervalRef.current) {
        console.log('🔄 Cleaning up pinned message cycling interval');
        clearInterval(cycleIntervalRef.current);
        cycleIntervalRef.current = null;
      }
    };
  }, [pinnedMessages.length]); // Only depend on the length, not the entire array

  // Fade in/out animation and expiration handling
  useEffect(() => {
    if (currentMessage) {
      // Animate in
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: false,
      }).start();

      // Handle expiration timer
      if (currentMessage.expiresAt) {
        expirationIntervalRef.current = setInterval(() => {
          const now = new Date();
          const timeRemaining = currentMessage.expiresAt!.getTime() - now.getTime();

          if (timeRemaining <= 0) {
            setTimeLeft('Expired');
            if (expirationIntervalRef.current) {
              clearInterval(expirationIntervalRef.current);
            }
            // Auto-unpin expired message
            setTimeout(() => onUnpin?.(currentMessage.id), 1000);
          } else {
            const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
            const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

            if (hours > 0) {
              setTimeLeft(`${hours}h ${minutes}m`);
            } else if (minutes > 0) {
              setTimeLeft(`${minutes}m ${seconds}s`);
            } else {
              setTimeLeft(`${seconds}s`);
            }
          }
        }, 1000);

        return () => {
          if (expirationIntervalRef.current) {
            clearInterval(expirationIntervalRef.current);
          }
        };
      }
    } else {
      // Animate out
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  }, [currentMessage]);

  if (!currentMessage) {
    return null;
  }



  const handlePress = () => {
    onPress?.(currentMessage.id);
    onNavigateToMessage?.(currentMessage.id);
  };



  const getMessagePreview = () => {
    if (currentMessage.type === 'text') {
      return currentMessage.content.length > 50
        ? `${currentMessage.content.substring(0, 50)}...`
        : currentMessage.content;
    } else {
      return `📎 ${currentMessage.type.toUpperCase()}`;
    }
  };

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
          opacity: fadeAnim,
        },
      ]}
    >
      <Animated.View style={[styles.slideContainer, { transform: [{ translateX: slideAnim }] }]}>
        <TouchableOpacity
          style={styles.content}
          onPress={handlePress}
          activeOpacity={0.7}
        >
          <View style={styles.pinIcon}>
            <Ionicons name="pin" size={16} color={colors.primary} />
          </View>

          <View style={styles.messageContent}>
            <View style={styles.header}>
              <Text style={[styles.pinnedLabel, { color: colors.primary }]}>
                Pinned Message {pinnedMessages.length > 1 ? `(${currentIndex + 1}/${pinnedMessages.length})` : ''}
              </Text>
              {timeLeft && (
                <Text style={[styles.timeLeft, { color: colors.textSecondary }]}>
                  {timeLeft}
                </Text>
              )}
            </View>

            <Text style={[styles.messageText, { color: colors.text }]} numberOfLines={2}>
              {getMessagePreview()}
            </Text>

            <Text style={[styles.senderInfo, { color: colors.textSecondary }]}>
              by {currentMessage.senderName} • {currentMessage.timestamp.toLocaleTimeString()}
            </Text>
          </View>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

// Pin Duration Options Component
export const PinDurationModal: React.FC<{
  visible: boolean;
  onClose: () => void;
  onSelect: (duration: number | null) => void;
}> = ({ visible, onClose, onSelect }) => {
  const { colors } = useTheme();

  if (!visible) return null;

  const durations = [
    { label: '1 hour', value: 60 * 60 * 1000 },
    { label: '8 hours', value: 8 * 60 * 60 * 1000 },
    { label: '1 day', value: 24 * 60 * 60 * 1000 },
    { label: '7 days', value: 7 * 24 * 60 * 60 * 1000 },
    { label: 'Forever', value: null },
  ];

  return (
    <View style={styles.modalOverlay}>
      <View style={[styles.modal, { backgroundColor: colors.surface }]}>
        <Text style={[styles.modalTitle, { color: colors.text }]}>
          Pin Duration
        </Text>
        
        {durations.map((duration, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.durationOption, { borderBottomColor: colors.border }]}
            onPress={() => {
              onSelect(duration.value);
              onClose();
            }}
          >
            <Text style={[styles.durationText, { color: colors.text }]}>
              {duration.label}
            </Text>
          </TouchableOpacity>
        ))}
        
        <TouchableOpacity
          style={[styles.cancelButton, { backgroundColor: colors.primary }]}
          onPress={onClose}
        >
          <Text style={styles.cancelText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderBottomWidth: 1,
    marginTop: 1, // 1px padding from header bottom border
  },
  slideContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  content: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  pinIcon: {
    marginRight: 8,
  },
  messageContent: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 2,
  },
  pinnedLabel: {
    fontSize: 12,
    fontWeight: '600',
  },
  timeLeft: {
    fontSize: 10,
    fontWeight: '500',
  },
  messageText: {
    fontSize: 14,
    marginBottom: 2,
  },
  senderInfo: {
    fontSize: 11,
  },
  unpinButton: {
    padding: 4,
    marginLeft: 8,
  },
  
  // Modal styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  modal: {
    width: '80%',
    borderRadius: 12,
    padding: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 20,
  },
  durationOption: {
    paddingVertical: 15,
    borderBottomWidth: 1,
  },
  durationText: {
    fontSize: 16,
    textAlign: 'center',
  },
  cancelButton: {
    marginTop: 15,
    paddingVertical: 12,
    borderRadius: 8,
  },
  cancelText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default PinnedMessageBar;
