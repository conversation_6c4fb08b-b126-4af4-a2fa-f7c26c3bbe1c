/**
 * React Hook for IraChat Offline Functionality
 * Provides easy access to offline/online messaging features
 * Similar to WhatsApp's React integration
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { iraChatOfflineEngine, IraChatStats, SyncProgress } from '../services/iraChatOfflineEngine';
import { Message , Chat } from '../types';

export interface UseIraChatOfflineOptions {
  autoInitialize?: boolean;
  enableBackgroundSync?: boolean;
  syncOnAppForeground?: boolean;
  enableDebugLogs?: boolean;
}

export interface UseIraChatOfflineReturn {
  // State
  isInitialized: boolean;
  isOnline: boolean;
  isOffline: boolean;
  isSyncing: boolean;
  syncProgress: SyncProgress | null;
  stats: IraChatStats | null;
  error: string | null;

  // Message operations
  sendMessage: (chatId: string, text: string, senderId: string, type?: Message['type']) => Promise<string>;
  getMessages: (chatId: string, limit?: number, offset?: number) => Promise<Message[]>;
  searchMessages: (query: string, chatId?: string) => Promise<Message[]>;
  markChatAsRead: (chatId: string) => Promise<boolean>;

  // Chat operations
  getChats: (includeArchived?: boolean) => Promise<Chat[]>;
  createChat: (name: string, createdBy: string, isGroup?: boolean, participantIds?: string[]) => Promise<string>;
  updateChat: (chatId: string, updates: Partial<Chat>) => Promise<boolean>;
  deleteChat: (chatId: string) => Promise<boolean>;
  searchChats: (query: string) => Promise<Chat[]>;
  pinChat: (chatId: string, pin?: boolean) => Promise<boolean>;
  archiveChat: (chatId: string, archive?: boolean) => Promise<boolean>;
  muteChat: (chatId: string, mute?: boolean, duration?: number) => Promise<boolean>;

  // Sync operations
  forceSync: () => Promise<void>;
  refreshStats: () => Promise<void>;

  // Utility
  initialize: () => Promise<void>;
  cleanup: () => Promise<void>;
  clearAllData: () => Promise<void>;
  handleMemoryPressure: () => void;
}

export const useIraChatOffline = (options: UseIraChatOfflineOptions = {}): UseIraChatOfflineReturn => {
  const {
    autoInitialize = true,
    enableBackgroundSync = true,
    syncOnAppForeground = true,
    enableDebugLogs = false,
  } = options;

  // State
  const [isInitialized, setIsInitialized] = useState(false);
  const [isOnline, setIsOnline] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncProgress, setSyncProgress] = useState<SyncProgress | null>(null);
  const [stats, setStats] = useState<IraChatStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Refs
  const syncCallbackId = useRef<string | null>(null);
  const appStateSubscription = useRef<any>(null);
  const networkListenerId = useRef<string | null>(null);

  // Initialize the engine
  const initialize = useCallback(async () => {
    try {
      if (enableDebugLogs) {
        console.log('🚀 Initializing IraChat offline hook...');
      }

      await iraChatOfflineEngine.initialize({
        enableBackgroundSync,
        debugMode: enableDebugLogs,
      });

      setIsInitialized(true);
      setError(null);

      // Set up network state listener
      if (!networkListenerId.current) {
        networkListenerId.current = Math.random().toString(36);
        // Note: This would need to be implemented in the network manager
        // networkStateManager.addListener(networkListenerId.current, handleNetworkStateChange);
      }

      // Set up sync progress listener
      if (!syncCallbackId.current) {
        syncCallbackId.current = iraChatOfflineEngine.onSyncProgress(handleSyncProgress);
      }

      // Set up app state listener for foreground sync
      if (syncOnAppForeground && !appStateSubscription.current) {
        appStateSubscription.current = AppState.addEventListener('change', handleAppStateChange);
      }

      // Get initial stats
      await refreshStats();

      if (enableDebugLogs) {
        console.log('✅ IraChat offline hook initialized');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize';
      setError(errorMessage);
      console.error('❌ Failed to initialize IraChat offline hook:', err);
    }
  }, [enableBackgroundSync, enableDebugLogs, syncOnAppForeground]);

  // Cleanup
  const cleanup = useCallback(async () => {
    try {
      if (enableDebugLogs) {
        console.log('🧹 Cleaning up IraChat offline hook...');
      }

      // Remove listeners
      if (syncCallbackId.current) {
        iraChatOfflineEngine.offSyncProgress(syncCallbackId.current);
        syncCallbackId.current = null;
      }

      if (networkListenerId.current) {
        // networkStateManager.removeListener(networkListenerId.current);
        networkListenerId.current = null;
      }

      if (appStateSubscription.current) {
        appStateSubscription.current.remove();
        appStateSubscription.current = null;
      }

      await iraChatOfflineEngine.cleanup();
      
      setIsInitialized(false);
      setIsOnline(false);
      setIsSyncing(false);
      setSyncProgress(null);
      setStats(null);
      setError(null);

      if (enableDebugLogs) {
        console.log('✅ IraChat offline hook cleaned up');
      }
    } catch (err) {
      console.error('❌ Error during cleanup:', err);
    }
  }, [enableDebugLogs]);

  // Handle network state changes
  const handleNetworkStateChange = useCallback((networkState: any) => {
    setIsOnline(networkState.isConnected);
    
    if (enableDebugLogs) {
      console.log('📶 Network state changed:', networkState.isConnected ? 'Online' : 'Offline');
    }
  }, [enableDebugLogs]);

  // Handle sync progress updates
  const handleSyncProgress = useCallback((progress: SyncProgress) => {
    setSyncProgress(progress);
    setIsSyncing(progress.progress < 100);
    
    if (enableDebugLogs) {
      console.log('🔄 Sync progress:', progress);
    }
  }, [enableDebugLogs]);

  // Handle app state changes
  const handleAppStateChange = useCallback(async (nextAppState: AppStateStatus) => {
    if (nextAppState === 'active' && isInitialized && isOnline && !isSyncing) {
      try {
        if (enableDebugLogs) {
          console.log('📱 App became active, triggering sync...');
        }
        await forceSync();
      } catch (err) {
        console.error('❌ Failed to sync on app foreground:', err);
      }
    }
  }, [isInitialized, isOnline, isSyncing, enableDebugLogs]);

  // Message operations
  const sendMessage = useCallback(async (
    chatId: string,
    text: string,
    senderId: string,
    type: Message['type'] = 'text'
  ): Promise<string> => {
    try {
      const messageId = await iraChatOfflineEngine.sendMessage(chatId, text, senderId, type);
      if (messageId === null) {
        throw new Error('Failed to send message - returned null');
      }
      return messageId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const getMessages = useCallback(async (
    chatId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<Message[]> => {
    try {
      return await iraChatOfflineEngine.getMessages(chatId, limit, offset);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get messages';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const searchMessages = useCallback(async (query: string, chatId?: string): Promise<Message[]> => {
    try {
      return await iraChatOfflineEngine.searchMessages(query, chatId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search messages';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const markChatAsRead = useCallback(async (chatId: string): Promise<boolean> => {
    try {
      return await iraChatOfflineEngine.markChatAsRead(chatId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark chat as read';
      setError(errorMessage);
      throw err;
    }
  }, []);

  // Chat operations
  const getChats = useCallback(async (includeArchived: boolean = false): Promise<Chat[]> => {
    try {
      return await iraChatOfflineEngine.getChats(includeArchived);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get chats';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const createChat = useCallback(async (
    name: string,
    createdBy: string,
    isGroup: boolean = false,
    participantIds: string[] = []
  ): Promise<string> => {
    try {
      return await iraChatOfflineEngine.createChat(name, isGroup, participantIds, createdBy);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create chat';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const updateChat = useCallback(async (chatId: string, updates: Partial<Chat>): Promise<boolean> => {
    try {
      return await iraChatOfflineEngine.updateChat(chatId, updates);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update chat';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const deleteChat = useCallback(async (chatId: string): Promise<boolean> => {
    try {
      return await iraChatOfflineEngine.deleteChat(chatId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete chat';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const searchChats = useCallback(async (query: string): Promise<Chat[]> => {
    try {
      return await iraChatOfflineEngine.searchChats(query);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search chats';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const pinChat = useCallback(async (chatId: string, pin: boolean = true): Promise<boolean> => {
    try {
      return await iraChatOfflineEngine.pinChat(chatId, pin);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to pin chat';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const archiveChat = useCallback(async (chatId: string, archive: boolean = true): Promise<boolean> => {
    try {
      return await iraChatOfflineEngine.archiveChat(chatId, archive);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to archive chat';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const muteChat = useCallback(async (chatId: string, mute: boolean = true, duration?: number): Promise<boolean> => {
    try {
      return await iraChatOfflineEngine.muteChat(chatId, mute, duration);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mute chat';
      setError(errorMessage);
      throw err;
    }
  }, []);

  // Sync operations
  const forceSync = useCallback(async (): Promise<void> => {
    try {
      setIsSyncing(true);
      setError(null);
      await iraChatOfflineEngine.forceSync();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to sync';
      setError(errorMessage);
      throw err;
    } finally {
      setIsSyncing(false);
      setSyncProgress(null);
    }
  }, []);

  const refreshStats = useCallback(async (): Promise<void> => {
    try {
      const newStats = await iraChatOfflineEngine.getStats();
      setStats(newStats);
      setIsOnline(newStats.isOnline);
    } catch (err) {
      console.error('❌ Failed to refresh stats:', err);
    }
  }, []);

  // Utility functions
  const clearAllData = useCallback(async (): Promise<void> => {
    try {
      await iraChatOfflineEngine.clearAllData();
      setStats(null);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear data';
      setError(errorMessage);
      throw err;
    }
  }, []);

  const handleMemoryPressure = useCallback((): void => {
    try {
      iraChatOfflineEngine.handleMemoryPressure();
    } catch (err) {
      console.error('❌ Failed to handle memory pressure:', err);
    }
  }, []);

  // Auto-initialize on mount
  useEffect(() => {
    if (autoInitialize) {
      initialize();
    }

    // Cleanup on unmount
    return () => {
      cleanup();
    };
  }, [autoInitialize, initialize, cleanup]);

  // Periodic stats refresh
  useEffect(() => {
    if (!isInitialized) return;

    const interval = setInterval(refreshStats, 30000); // Every 30 seconds
    return () => clearInterval(interval);
  }, [isInitialized, refreshStats]);

  return {
    // State
    isInitialized,
    isOnline,
    isOffline: !isOnline,
    isSyncing,
    syncProgress,
    stats,
    error,

    // Message operations
    sendMessage,
    getMessages,
    searchMessages,
    markChatAsRead,

    // Chat operations
    getChats,
    createChat,
    updateChat,
    deleteChat,
    searchChats,
    pinChat,
    archiveChat,
    muteChat,

    // Sync operations
    forceSync,
    refreshStats,

    // Utility
    initialize,
    cleanup,
    clearAllData,
    handleMemoryPressure,
  };
};

export default useIraChatOffline;
