import React, { useState, useCallback, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  ScrollView,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS } from '../../styles/iraChatDesignSystem';
import { ResponsiveTypography, ResponsiveSpacing } from '../../utils/responsiveUtils';
import { localChatManagementService } from '../../services/localChatManagementService';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export interface SearchFilters {
  searchType: 'all' | 'chats' | 'messages' | 'contacts' | 'media';
  chatType: 'all' | 'individual' | 'groups';
  timeRange: 'all' | 'today' | 'week' | 'month' | 'year';
  messageType: 'all' | 'text' | 'images' | 'videos' | 'documents' | 'audio';
  sortBy: 'relevance' | 'date' | 'name';
  includeArchived: boolean;
  includeMuted: boolean;
  includeHidden: boolean;
  includeDeleted: boolean;
}

interface EnhancedChatSearchProps {
  searchQuery: string;
  onSearchChange: (text: string) => void;
  onClearSearch: () => void;
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onAdvancedSearch?: (query: string, filters: SearchFilters) => void;
  onSearchResults?: (results: any[]) => void;
  placeholder?: string;
  showAdvancedOptions?: boolean;
  enableOfflineSearch?: boolean;
}

export const EnhancedChatSearch: React.FC<EnhancedChatSearchProps> = ({
  searchQuery,
  onSearchChange,
  onClearSearch,
  filters,
  onFiltersChange,
  onAdvancedSearch,
  onSearchResults,
  placeholder = "Search chats, messages, contacts...",
  showAdvancedOptions = true,
  enableOfflineSearch = true,
}) => {
  const [showFilters, setShowFilters] = useState(false);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const slideAnim = useState(new Animated.Value(-300))[0];

  // Local search functionality
  const performLocalSearch = useCallback(async (query: string) => {
    if (!enableOfflineSearch || !query.trim()) {
      if (onSearchResults) onSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const results = await localChatManagementService.searchLocal(query, filters);
      if (onSearchResults) onSearchResults(results);
    } catch (error) {
      console.error('❌ Local search error:', error);
      if (onSearchResults) onSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [enableOfflineSearch, filters, onSearchResults]);

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (enableOfflineSearch) {
        performLocalSearch(searchQuery);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, performLocalSearch, enableOfflineSearch]);

  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: showFilters ? 0 : -300,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [showFilters, slideAnim]);

  const handleFilterChange = useCallback((key: keyof SearchFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    onFiltersChange(newFilters);
  }, [filters, onFiltersChange]);

  const handleAdvancedSearch = useCallback(() => {
    if (onAdvancedSearch) {
      onAdvancedSearch(searchQuery, filters);
    } else if (enableOfflineSearch) {
      performLocalSearch(searchQuery);
    }
    setShowFilters(false);
  }, [searchQuery, filters, onAdvancedSearch, enableOfflineSearch, performLocalSearch]);

  const resetFilters = useCallback(() => {
    const defaultFilters: SearchFilters = {
      searchType: 'all',
      chatType: 'all',
      timeRange: 'all',
      messageType: 'all',
      sortBy: 'relevance',
      includeArchived: false,
      includeMuted: true,
      includeHidden: false,
      includeDeleted: false,
    };
    onFiltersChange(defaultFilters);
  }, [onFiltersChange]);

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchType !== 'all') count++;
    if (filters.chatType !== 'all') count++;
    if (filters.timeRange !== 'all') count++;
    if (filters.messageType !== 'all') count++;
    if (filters.sortBy !== 'relevance') count++;
    if (filters.includeArchived) count++;
    if (!filters.includeMuted) count++;
    if (filters.includeHidden) count++;
    if (filters.includeDeleted) count++;
    return count;
  };

  const renderFilterOption = (
    title: string,
    options: { label: string; value: any }[],
    currentValue: any,
    onSelect: (value: any) => void
  ) => (
    <View style={styles.filterSection}>
      <Text style={styles.filterTitle}>{title}</Text>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterOptions}>
        {options.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.filterOption,
              currentValue === option.value && styles.filterOptionActive,
            ]}
            onPress={() => onSelect(option.value)}
          >
            <Text
              style={[
                styles.filterOptionText,
                currentValue === option.value && styles.filterOptionTextActive,
              ]}
            >
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  );

  const renderToggleOption = (title: string, value: boolean, onToggle: () => void) => (
    <TouchableOpacity style={styles.toggleOption} onPress={onToggle}>
      <Text style={styles.toggleOptionText}>{title}</Text>
      <View style={[styles.toggle, value && styles.toggleActive]}>
        <View style={[styles.toggleThumb, value && styles.toggleThumbActive]} />
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={[styles.searchContainer, isSearchFocused && styles.searchContainerFocused]}>
        <Ionicons name="search" size={20} color={IRACHAT_COLORS.textMuted} />
        <TextInput
          value={searchQuery}
          onChangeText={onSearchChange}
          placeholder={placeholder}
          style={styles.searchInput}
          placeholderTextColor={IRACHAT_COLORS.textMuted}
          onFocus={() => setIsSearchFocused(true)}
          onBlur={() => setIsSearchFocused(false)}
        />
        
        {isSearching && (
          <View style={styles.searchingIndicator}>
            <Text style={styles.searchingText}>🔍</Text>
          </View>
        )}

        {searchQuery.length > 0 && !isSearching && (
          <TouchableOpacity onPress={onClearSearch} style={styles.clearButton}>
            <Ionicons name="close-circle" size={20} color={IRACHAT_COLORS.textMuted} />
          </TouchableOpacity>
        )}
        
        {showAdvancedOptions && (
          <TouchableOpacity
            onPress={() => setShowFilters(!showFilters)}
            style={[styles.filterButton, getActiveFiltersCount() > 0 && styles.filterButtonActive]}
          >
            <Ionicons 
              name="options" 
              size={20} 
              color={getActiveFiltersCount() > 0 ? IRACHAT_COLORS.textOnPrimary : IRACHAT_COLORS.textMuted} 
            />
            {getActiveFiltersCount() > 0 && (
              <View style={styles.filterBadge}>
                <Text style={styles.filterBadgeText}>{getActiveFiltersCount()}</Text>
              </View>
            )}
          </TouchableOpacity>
        )}
      </View>

      {/* Advanced Filters Modal */}
      <Modal
        visible={showFilters}
        transparent
        animationType="none"
        onRequestClose={() => setShowFilters(false)}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity 
            style={styles.modalBackdrop} 
            onPress={() => setShowFilters(false)} 
          />
          <Animated.View 
            style={[
              styles.filtersContainer,
              { transform: [{ translateY: slideAnim }] }
            ]}
          >
            <View style={styles.filtersHeader}>
              <Text style={styles.filtersTitle}>Advanced Search</Text>
              <TouchableOpacity onPress={() => setShowFilters(false)}>
                <Ionicons name="close" size={24} color={IRACHAT_COLORS.text} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.filtersContent} showsVerticalScrollIndicator={false}>
              {renderFilterOption(
                'Search In',
                [
                  { label: 'All', value: 'all' },
                  { label: 'Chats', value: 'chats' },
                  { label: 'Messages', value: 'messages' },
                  { label: 'Contacts', value: 'contacts' },
                  { label: 'Media', value: 'media' },
                ],
                filters.searchType,
                (value) => handleFilterChange('searchType', value)
              )}

              {renderFilterOption(
                'Chat Type',
                [
                  { label: 'All', value: 'all' },
                  { label: 'Individual', value: 'individual' },
                  { label: 'Groups', value: 'groups' },
                ],
                filters.chatType,
                (value) => handleFilterChange('chatType', value)
              )}

              {renderFilterOption(
                'Time Range',
                [
                  { label: 'All Time', value: 'all' },
                  { label: 'Today', value: 'today' },
                  { label: 'This Week', value: 'week' },
                  { label: 'This Month', value: 'month' },
                  { label: 'This Year', value: 'year' },
                ],
                filters.timeRange,
                (value) => handleFilterChange('timeRange', value)
              )}

              {filters.searchType === 'messages' || filters.searchType === 'all' ? (
                renderFilterOption(
                  'Message Type',
                  [
                    { label: 'All', value: 'all' },
                    { label: 'Text', value: 'text' },
                    { label: 'Images', value: 'images' },
                    { label: 'Videos', value: 'videos' },
                    { label: 'Documents', value: 'documents' },
                    { label: 'Audio', value: 'audio' },
                  ],
                  filters.messageType,
                  (value) => handleFilterChange('messageType', value)
                )
              ) : null}

              {renderFilterOption(
                'Sort By',
                [
                  { label: 'Relevance', value: 'relevance' },
                  { label: 'Date', value: 'date' },
                  { label: 'Name', value: 'name' },
                ],
                filters.sortBy,
                (value) => handleFilterChange('sortBy', value)
              )}

              <View style={styles.toggleSection}>
                {renderToggleOption(
                  'Include Archived Chats',
                  filters.includeArchived,
                  () => handleFilterChange('includeArchived', !filters.includeArchived)
                )}
                {renderToggleOption(
                  'Include Muted Chats',
                  filters.includeMuted,
                  () => handleFilterChange('includeMuted', !filters.includeMuted)
                )}
                {renderToggleOption(
                  'Include Hidden Chats',
                  filters.includeHidden,
                  () => handleFilterChange('includeHidden', !filters.includeHidden)
                )}
                {renderToggleOption(
                  'Include Deleted Chats',
                  filters.includeDeleted,
                  () => handleFilterChange('includeDeleted', !filters.includeDeleted)
                )}
              </View>
            </ScrollView>

            <View style={styles.filtersFooter}>
              <TouchableOpacity style={styles.resetButton} onPress={resetFilters}>
                <Text style={styles.resetButtonText}>Reset</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.searchButton} onPress={handleAdvancedSearch}>
                <Text style={styles.searchButtonText}>Search</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: IRACHAT_COLORS.surface,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    margin: ResponsiveSpacing.md,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.borderLight,
  },
  searchContainerFocused: {
    borderColor: IRACHAT_COLORS.primary,
    backgroundColor: IRACHAT_COLORS.surface,
  },
  searchInput: {
    flex: 1,
    marginLeft: ResponsiveSpacing.sm,
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  searchingIndicator: {
    padding: ResponsiveSpacing.xs,
  },
  searchingText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.primary,
  },
  clearButton: {
    padding: ResponsiveSpacing.xs,
  },
  filterButton: {
    padding: ResponsiveSpacing.sm,
    borderRadius: BORDER_RADIUS.md,
    marginLeft: ResponsiveSpacing.sm,
    position: 'relative',
  },
  filterButtonActive: {
    backgroundColor: IRACHAT_COLORS.primary,
  },
  filterBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: IRACHAT_COLORS.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  filterBadgeText: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontSize: 10,
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.overlay,
  },
  modalBackdrop: {
    flex: 1,
  },
  filtersContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomLeftRadius: BORDER_RADIUS.xl,
    borderBottomRightRadius: BORDER_RADIUS.xl,
    maxHeight: '80%',
  },
  filtersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: ResponsiveSpacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  filtersTitle: {
    fontSize: ResponsiveTypography.fontSize.xl,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  filtersContent: {
    padding: ResponsiveSpacing.lg,
  },
  filterSection: {
    marginBottom: ResponsiveSpacing.lg,
  },
  filterTitle: {
    fontSize: ResponsiveTypography.fontSize.base,
    fontWeight: '600',
    color: IRACHAT_COLORS.text,
    marginBottom: ResponsiveSpacing.sm,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  filterOptions: {
    flexDirection: 'row',
  },
  filterOption: {
    paddingHorizontal: ResponsiveSpacing.md,
    paddingVertical: ResponsiveSpacing.sm,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    marginRight: ResponsiveSpacing.sm,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.borderLight,
  },
  filterOptionActive: {
    backgroundColor: IRACHAT_COLORS.primary,
    borderColor: IRACHAT_COLORS.primary,
  },
  filterOptionText: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  filterOptionTextActive: {
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: '600',
  },
  toggleSection: {
    marginTop: ResponsiveSpacing.md,
  },
  toggleOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: ResponsiveSpacing.md,
  },
  toggleOptionText: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  toggle: {
    width: 50,
    height: 30,
    borderRadius: 15,
    backgroundColor: IRACHAT_COLORS.borderLight,
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  toggleActive: {
    backgroundColor: IRACHAT_COLORS.primary,
  },
  toggleThumb: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: IRACHAT_COLORS.surface,
    alignSelf: 'flex-start',
  },
  toggleThumbActive: {
    alignSelf: 'flex-end',
  },
  filtersFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: ResponsiveSpacing.lg,
    borderTopWidth: 1,
    borderTopColor: IRACHAT_COLORS.borderLight,
  },
  resetButton: {
    flex: 1,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    marginRight: ResponsiveSpacing.sm,
    alignItems: 'center',
  },
  resetButtonText: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textSecondary,
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  searchButton: {
    flex: 1,
    paddingVertical: ResponsiveSpacing.md,
    borderRadius: BORDER_RADIUS.lg,
    backgroundColor: IRACHAT_COLORS.primary,
    marginLeft: ResponsiveSpacing.sm,
    alignItems: 'center',
  },
  searchButtonText: {
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: '600',
    fontFamily: TYPOGRAPHY.fontFamily,
  },
});
