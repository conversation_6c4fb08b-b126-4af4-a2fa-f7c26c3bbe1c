// 🚀 CHAT HEADER COMPONENT
// Header with user info, online status, and action buttons

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  StatusBar,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Helper function to generate initials from name
const getInitials = (name: string): string => {
  if (!name || typeof name !== 'string') {
    return '?';
  }

  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

interface ChatUser {
  id: string;
  name: string;
  avatar?: string;
  isOnline: boolean;
  lastSeen?: Date;
}

interface ChatHeaderProps {
  user: ChatUser;
  onBack: () => void;
  onUserPress: () => void;
  onVoiceCall: () => void;
  onVideoCall: () => void;
  onMoreOptions: () => void;
  isSelectionMode: boolean;
  selectedCount: number;
  onCancelSelection: () => void;
  onDeleteSelected: () => void;
  onForwardSelected: () => void;
  isDarkMode?: boolean;
  onToggleTheme?: () => void;
  headerBackgroundColor?: string;
  showSearchBar?: boolean;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  onCloseSearch?: () => void;
  onAvatarPress?: () => void;
}

const COLORS = {
  primary: '#87CEEB',
  text: '#333',
  textMuted: '#666',
  white: '#ffffff',
  background: '#f8f9fa',
};

export const ChatHeader: React.FC<ChatHeaderProps> = ({
  user,
  onBack,
  onUserPress,
  onVoiceCall,
  onVideoCall,
  onMoreOptions,
  isSelectionMode,
  selectedCount,
  onCancelSelection,
  onDeleteSelected,
  onForwardSelected,
  isDarkMode = false,
  onToggleTheme,
  headerBackgroundColor,
  showSearchBar = false,
  searchQuery = '',
  onSearchChange,
  onCloseSearch,
  onAvatarPress,
}) => {
  const insets = useSafeAreaInsets();

  const formatLastSeen = (lastSeen?: Date) => {
    if (!lastSeen) return 'Offline';
    
    const now = new Date();
    const diff = now.getTime() - lastSeen.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Last seen just now';
    if (minutes < 60) return `Last seen ${minutes}m ago`;
    if (hours < 24) return `Last seen ${hours}h ago`;
    if (days < 7) return `Last seen ${days}d ago`;
    return `Last seen ${lastSeen.toLocaleDateString()}`;
  };

  if (isSelectionMode) {
    return (
      <LinearGradient
        colors={['#87CEEB', '#5F9EA0']}
        style={[styles.header, { paddingTop: insets.top }]}
      >
        <StatusBar barStyle="light-content" backgroundColor="#5F9EA0" />
        <View style={styles.selectionHeader}>
          <TouchableOpacity style={styles.backButton} onPress={onCancelSelection}>
            <Ionicons name="close" size={24} color={COLORS.white} />
          </TouchableOpacity>
          
          <Text style={styles.selectionTitle}>
            {selectedCount} selected
          </Text>
          
          <View style={styles.selectionActions}>
            <TouchableOpacity style={styles.actionButton} onPress={onForwardSelected}>
              <Ionicons name="arrow-forward" size={20} color={COLORS.white} />
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionButton} onPress={onDeleteSelected}>
              <Ionicons name="trash" size={20} color={COLORS.white} />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    );
  }

  // Dynamic header colors based on theme and custom background
  const headerColors: [string, string] = headerBackgroundColor
    ? [headerBackgroundColor, headerBackgroundColor]
    : isDarkMode
    ? ['#3C3C3C', '#2D2D2D']
    : ['#FFFFFF', '#FFFFFF']; // White background for light theme

  const statusBarColor = isDarkMode ? '#2D2D2D' : '#FFFFFF'; // White status bar for light theme

  // Dynamic text and icon colors
  const textColor = isDarkMode ? COLORS.white : '#333333';
  const iconColor = isDarkMode ? COLORS.white : '#333333';

  return (
    <LinearGradient
      colors={headerColors}
      style={[styles.header, { paddingTop: insets.top }]}
    >
      <StatusBar
        barStyle={isDarkMode ? "light-content" : "dark-content"} // Dark content for light theme
        backgroundColor={statusBarColor}
      />
      <View style={styles.headerContent}>
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Ionicons name="arrow-back" size={24} color={iconColor} />
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.userInfo} onPress={onUserPress}>
          <TouchableOpacity style={styles.avatarContainer} onPress={onAvatarPress}>
            {user.avatar ? (
              <Image source={{ uri: user.avatar }} style={styles.avatar} />
            ) : (
              <View style={styles.defaultAvatar}>
                <Text style={styles.initialsText}>
                  {getInitials(user.name || 'User')}
                </Text>
              </View>
            )}
            {user.isOnline && <View style={styles.onlineIndicator} />}
          </TouchableOpacity>
          
          <View style={styles.userDetails}>
            <Text style={[styles.userName, { color: textColor }]} numberOfLines={1}>
              {user.name}
            </Text>
            <Text style={[styles.userStatus, { color: textColor, opacity: 0.7 }]} numberOfLines={1}>
              {user.isOnline ? 'Online' : formatLastSeen(user.lastSeen)}
            </Text>
          </View>
        </TouchableOpacity>
        
        <View style={styles.headerActions}>
          {onToggleTheme && (
            <TouchableOpacity style={styles.actionButton} onPress={onToggleTheme}>
              <Ionicons
                name={isDarkMode ? "sunny" : "moon"}
                size={20}
                color={iconColor}
              />
            </TouchableOpacity>
          )}

          <TouchableOpacity style={styles.actionButton} onPress={onMoreOptions}>
            <Ionicons name="ellipsis-vertical" size={20} color={iconColor} />
          </TouchableOpacity>
        </View>
      </View>

      {/* Search Bar */}
      {showSearchBar && (
        <View style={[styles.searchContainer, { backgroundColor: isDarkMode ? '#2D2D2D' : '#FFFFFF' }]}>
          <TextInput
            style={[styles.searchInput, { color: textColor }]}
            placeholder="Search messages, files, media..."
            placeholderTextColor={isDarkMode ? '#AAAAAA' : '#666666'}
            value={searchQuery}
            onChangeText={onSearchChange}
            autoFocus
          />
          <TouchableOpacity onPress={onCloseSearch} style={styles.closeSearchButton}>
            <Ionicons name="close" size={20} color={iconColor} />
          </TouchableOpacity>
        </View>
      )}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  header: {
    paddingBottom: 12,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  selectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  defaultAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  initialsText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  onlineIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: COLORS.white,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  closeSearchButton: {
    marginLeft: 8,
    padding: 8,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  userStatus: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 2,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 8,
  },
  selectionTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.white,
    textAlign: 'center',
  },
  selectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});
