/**
 * Profile Avatar Component for IraChat
 *
 * Enhanced avatar component specifically for profile screens
 * with editing capabilities, offline support, and real-time status
 */

import { Ionicons } from '@expo/vector-icons';
import React, { useState, useEffect, useCallback } from 'react';
import {
  ActionSheetIOS,
  Alert,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { colors, shadows, spacing } from '../styles/designSystem';
import { avatarService, UserProfile } from '../services/avatarService';
import { getAvatarStyles } from '../utils/avatarUtils';
import { networkStateManager } from '../services/networkStateManager';
import { statusService } from '../services/statusService';
import { iraChatOfflineEngine } from '../services/iraChatOfflineEngine';

interface ProfileAvatarProps {
  user: Partial<UserProfile>;
  size?: 'small' | 'medium' | 'large' | 'xlarge' | number;
  editable?: boolean;
  onAvatarUpdate?: (_newAvatarUrl: string) => void;
  style?: ViewStyle;
  showEditIcon?: boolean;
  showOnlineStatus?: boolean;
}

export const ProfileAvatar: React.FC<ProfileAvatarProps> = ({
  user,
  size = 'large',
  editable = false,
  onAvatarUpdate,
  style,
  showEditIcon = true,
  showOnlineStatus = false,
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isOnline, setIsOnline] = useState(false);
  const [cachedAvatarUrl, setCachedAvatarUrl] = useState<string | null>(null);
  const [realTimeOnlineStatus, setRealTimeOnlineStatus] = useState(user.isOnline || false);
  const [hasPendingUpload, setHasPendingUpload] = useState(false);
  
  // Determine avatar size
  let avatarSize: number;
  switch (size) {
    case 'small':
      avatarSize = 48;
      break;
    case 'medium':
      avatarSize = 64;
      break;
    case 'large':
      avatarSize = 96;
      break;
    case 'xlarge':
      avatarSize = 128;
      break;
    default:
      avatarSize = typeof size === 'number' ? size : 96;
  }
  
  // Load cached avatar and setup real-time status monitoring
  useEffect(() => {
    let mounted = true;
    let statusUnsubscribe: (() => void) | null = null;

    const initializeAvatar = async () => {
      try {
        // Initialize network state manager
        if (!networkStateManager.getState().isConnected) {
          await networkStateManager.initialize();
        }

        // Initialize offline engine for proper offline support
        try {
          await iraChatOfflineEngine.initialize();
        } catch (error) {
          console.warn('Offline engine initialization failed, using fallback:', error);
        }

        // Load cached avatar from local storage
        if (user.id) {
          const cacheKey = `avatar_cache_${user.id}_${avatarSize}`;
          try {
            const cached = await AsyncStorage.getItem(cacheKey);
            if (cached && mounted) {
              setCachedAvatarUrl(cached);
            }
          } catch (error) {
            console.warn('Failed to load cached avatar:', error);
          }
        }

        // Monitor network state
        const networkState = networkStateManager.getState();

        // Set up real-time online status monitoring using statusService
        if (showOnlineStatus && user.id && mounted) {
          try {
            // Use real-time status service for accurate status updates
            statusUnsubscribe = statusService.listenToUserStatus(user.id, (status) => {
              if (mounted) {
                setRealTimeOnlineStatus(status.isOnline);
              }
            });
          } catch (error) {
            console.warn('Status service unavailable, using fallback:', error);
            // Fallback to simplified status monitoring
            setRealTimeOnlineStatus(user.isOnline || false);

            // Set up periodic status updates if network is available
            if (networkState.isConnected) {
              const statusInterval = setInterval(() => {
                if (mounted) {
                  // Update status based on network connectivity and user data
                  const currentStatus = user.isOnline || false;
                  setRealTimeOnlineStatus(currentStatus);
                }
              }, 30000); // Check every 30 seconds

              statusUnsubscribe = () => clearInterval(statusInterval);
            }
          }
        }
        if (mounted) {
          setIsOnline(networkState.isConnected);
        }
      } catch (error) {
        console.error('Failed to initialize avatar:', error);
      }
    };

    initializeAvatar();

    return () => {
      mounted = false;
      if (statusUnsubscribe) {
        statusUnsubscribe();
      }
    };
  }, [user.id, avatarSize, showOnlineStatus]);

  // Network monitoring for pending avatar uploads
  useEffect(() => {
    let networkUnsubscribe: (() => void) | null = null;

    if (user.id) {
      // Set up network listener to process pending avatar uploads when online
      networkUnsubscribe = networkStateManager.subscribe(async (isConnected) => {
        if (isConnected) {
          console.log('🌐 Network restored, checking for pending avatar uploads...');
          await processPendingAvatarUploads();
        }
      });
    }

    return () => {
      if (networkUnsubscribe) {
        networkUnsubscribe();
      }
    };
  }, [user.id]);

  // Check for pending uploads on component mount and network changes
  useEffect(() => {
    const checkPendingUploads = async () => {
      if (!user.id) return;

      try {
        const offlineKey = `offline_avatar_${user.id}`;
        const offlineData = await AsyncStorage.getItem(offlineKey);
        setHasPendingUpload(!!offlineData);
      } catch (error) {
        console.warn('Failed to check pending uploads:', error);
      }
    };

    checkPendingUploads();
  }, [user.id, isOnline]);

  // Cache avatar when it changes
  const cacheAvatar = useCallback(async (avatarUrl: string) => {
    if (user.id && avatarUrl) {
      try {
        const cacheKey = `avatar_cache_${user.id}_${avatarSize}`;
        await AsyncStorage.setItem(cacheKey, avatarUrl);
        setCachedAvatarUrl(avatarUrl);
      } catch (error) {
        console.warn('Failed to cache avatar:', error);
      }
    }
  }, [user.id, avatarSize]);

  // Determine which avatar URL to use (cached vs current)
  const effectiveAvatarUrl = isOnline ? (user.avatar || cachedAvatarUrl) : cachedAvatarUrl;

  const { containerStyle, textStyle, initials, hasValidImage } = getAvatarStyles({
    imageUrl: effectiveAvatarUrl,
    name: user.name || 'User',
    size: avatarSize,
  });

  const shouldShowImage = hasValidImage && !imageError;
  
  const handleAvatarPress = () => {
    if (!editable) return;
    
    const options = ['Take Photo', 'Choose from Library', 'Cancel'];
    const cancelButtonIndex = 2;
    
    if (Platform.OS === 'ios') {
      ActionSheetIOS.showActionSheetWithOptions(
        {
          options,
          cancelButtonIndex,
          title: 'Update Profile Photo',
        },
        handleActionSheetResponse
      );
    } else {
      Alert.alert(
        'Update Profile Photo',
        'Choose an option',
        [
          { text: 'Take Photo', onPress: () => handleActionSheetResponse(0) },
          { text: 'Choose from Library', onPress: () => handleActionSheetResponse(1) },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
    }
  };
  
  const handleActionSheetResponse = async (buttonIndex: number) => {
    if (buttonIndex === 2) return; // Cancel

    try {
      setIsUploading(true);
      let imageUri: string | null = null;

      if (buttonIndex === 0) {
        // Take photo
        imageUri = await avatarService.takeAvatarPhoto();
      } else if (buttonIndex === 1) {
        // Choose from library
        imageUri = await avatarService.pickAvatar();
      }

      if (imageUri && user.id) {
        // Check network connectivity with detailed logging
        const networkState = networkStateManager.getState();
        const isOnline = networkStateManager.isOnline();

        console.log('🌐 Network state during avatar upload:', {
          isConnected: networkState.isConnected,
          isInternetReachable: networkState.isInternetReachable,
          connectionType: networkState.connectionType,
          connectionQuality: networkState.connectionQuality,
          isOnline: isOnline
        });

        if (networkState.isConnected && isOnline) {
          // Double-check internet connectivity with a quick test
          try {
            console.log('🔍 Testing actual internet connectivity...');
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000);

            await fetch('https://www.google.com/generate_204', {
              method: 'HEAD',
              signal: controller.signal
            });

            clearTimeout(timeoutId);
            console.log('✅ Internet connectivity confirmed, proceeding with upload');

            // Online: Upload to Firebase immediately
            const avatarUrl = await avatarService.uploadAvatar(user.id, imageUri);

            // Cache the new avatar
            await cacheAvatar(avatarUrl);

            // Update user profile
            if (onAvatarUpdate) {
              onAvatarUpdate(avatarUrl);
            }

            // Clear image error state
            setImageError(false);

            Alert.alert('Success', 'Profile photo updated successfully!');
          } catch (uploadError) {
            console.error('❌ Upload failed, storing for offline sync:', uploadError);

            // Store for offline sync
            await storeForOfflineSync(imageUri);

            Alert.alert(
              'Upload Failed',
              'Photo saved locally and will be uploaded when you\'re back online.'
            );
          }
        } else {
          // Offline: Store for later sync
          await storeForOfflineSync(imageUri);

          Alert.alert(
            'Offline Mode',
            'Photo saved locally and will be uploaded when you\'re back online.'
          );
        }
      }
    } catch (error) {
      console.error('❌ Error updating avatar:', error);
      Alert.alert(
        'Error',
        error instanceof Error ? error.message : 'Failed to update profile photo'
      );
    } finally {
      setIsUploading(false);
    }
  };

  // Store avatar for offline sync using proper offline engine
  const storeForOfflineSync = async (imageUri: string) => {
    if (!user.id) return;

    try {
      const offlineData = {
        imageUri,
        userId: user.id,
        timestamp: Date.now(),
        synced: false,
      };

      // Cache the local image URI temporarily
      await cacheAvatar(imageUri);

      // Store for offline sync using AsyncStorage with proper structure
      try {
        // Store avatar data for offline sync
        const offlineKey = `offline_avatar_${user.id}`;
        await AsyncStorage.setItem(offlineKey, JSON.stringify(offlineData));

        // Add to pending sync queue with proper metadata
        const pendingSync = {
          type: 'avatar_upload',
          data: offlineData,
          timestamp: Date.now(),
          priority: 'normal',
          retryCount: 0,
          maxRetries: 3,
        };

        const pendingKey = `pending_sync_avatar_${user.id}_${Date.now()}`;
        await AsyncStorage.setItem(pendingKey, JSON.stringify(pendingSync));

        // Store in a centralized pending sync index for easier management
        try {
          const pendingSyncIndex = await AsyncStorage.getItem('pending_sync_index');
          const index = pendingSyncIndex ? JSON.parse(pendingSyncIndex) : [];
          index.push({
            key: pendingKey,
            type: 'avatar_upload',
            userId: user.id,
            timestamp: Date.now(),
          });
          await AsyncStorage.setItem('pending_sync_index', JSON.stringify(index));
        } catch (indexError) {
          console.warn('Failed to update sync index:', indexError);
        }

        console.log('✅ Avatar stored for offline sync with proper queue management');

        // Update pending upload state
        setHasPendingUpload(true);

        // Also queue in the main sync service for better integration
        try {
          const { realSyncService } = await import('../services/realSyncService');
          await realSyncService.queueAvatarUpload(user.id, imageUri);
        } catch (syncError) {
          console.warn('Failed to queue in sync service:', syncError);
        }
      } catch (storageError) {
        console.error('❌ Failed to store avatar for offline sync:', storageError);
        throw storageError;
      }

      console.log('✅ Avatar stored for offline sync');
    } catch (error) {
      console.error('❌ Failed to store avatar for offline sync:', error);
      throw error;
    }
  };

  // Process pending avatar uploads when network comes back online
  const processPendingAvatarUploads = async () => {
    if (!user.id) return;

    try {
      console.log('🔄 Processing pending avatar uploads...');

      // Check for pending avatar upload for this user
      const offlineKey = `offline_avatar_${user.id}`;
      const offlineData = await AsyncStorage.getItem(offlineKey);

      if (offlineData) {
        const avatarData = JSON.parse(offlineData);

        if (!avatarData.synced) {
          console.log('📤 Found pending avatar upload, attempting sync...');

          try {
            // Attempt to upload the avatar
            const avatarUrl = await avatarService.uploadAvatar(user.id, avatarData.imageUri);

            // Update user profile
            if (onAvatarUpdate) {
              onAvatarUpdate(avatarUrl);
            }

            // Cache the new avatar
            await cacheAvatar(avatarUrl);

            // Mark as synced and remove from offline storage
            await AsyncStorage.removeItem(offlineKey);

            // Update pending upload state
            setHasPendingUpload(false);

            // Remove from pending sync index
            try {
              const pendingSyncIndex = await AsyncStorage.getItem('pending_sync_index');
              if (pendingSyncIndex) {
                const index = JSON.parse(pendingSyncIndex);
                const filteredIndex = index.filter((item: any) =>
                  !(item.type === 'avatar_upload' && item.userId === user.id)
                );
                await AsyncStorage.setItem('pending_sync_index', JSON.stringify(filteredIndex));
              }
            } catch (indexError) {
              console.warn('Failed to update sync index:', indexError);
            }

            console.log('✅ Pending avatar upload synced successfully');
            Alert.alert('Success', 'Profile photo has been uploaded successfully!');

          } catch (uploadError) {
            console.error('❌ Failed to sync pending avatar upload:', uploadError);
            // Keep in offline storage for next retry
          }
        }
      }
    } catch (error) {
      console.error('❌ Error processing pending avatar uploads:', error);
    }
  };
  
  const editIconSize = Math.floor(avatarSize * 0.25);
  
  return (
    <View style={[{ alignItems: 'center' }, style]}>
      <TouchableOpacity
        onPress={handleAvatarPress}
        disabled={!editable || isUploading}
        style={[
          containerStyle,
          {
            position: 'relative',
            opacity: isUploading ? 0.7 : 1,
          },
          shadows.md,
        ]}
        activeOpacity={editable ? 0.8 : 1}
      >
        {shouldShowImage ? (
          <Image
            source={{ uri: effectiveAvatarUrl! }}
            style={{
              width: containerStyle.width,
              height: containerStyle.height,
              borderRadius: containerStyle.borderRadius,
            }}
            onError={() => setImageError(true)}
            onLoad={() => {
              // Cache the avatar when it loads successfully
              if (effectiveAvatarUrl && isOnline) {
                cacheAvatar(effectiveAvatarUrl);
              }
            }}
            resizeMode="cover"
          />
        ) : (
          <Text style={textStyle}>{initials}</Text>
        )}
        
        {/* Loading overlay */}
        {isUploading && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.5)',
              borderRadius: containerStyle.borderRadius,
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Ionicons name="cloud-upload-outline" size={24} color={colors.white} />
          </View>
        )}
        
        {/* Edit icon */}
        {editable && showEditIcon && !isUploading && (
          <View
            style={{
              position: 'absolute',
              bottom: 0,
              right: 0,
              width: editIconSize + 8,
              height: editIconSize + 8,
              borderRadius: (editIconSize + 8) / 2,
              backgroundColor: colors.primary,
              justifyContent: 'center',
              alignItems: 'center',
              borderWidth: 2,
              borderColor: colors.white,
              ...shadows.sm,
            }}
          >
            <Ionicons
              name="camera"
              size={editIconSize}
              color={colors.white}
            />
          </View>
        )}
        
        {/* Online status indicator */}
        {showOnlineStatus && (
          <View
            style={{
              position: 'absolute',
              bottom: editable && showEditIcon ? editIconSize + 4 : 0,
              right: editable && showEditIcon ? editIconSize + 4 : 0,
              width: Math.floor(avatarSize * 0.2),
              height: Math.floor(avatarSize * 0.2),
              borderRadius: Math.floor(avatarSize * 0.1),
              backgroundColor: realTimeOnlineStatus ? colors.online : colors.offline,
              borderWidth: 2,
              borderColor: colors.white,
            }}
          />
        )}
      </TouchableOpacity>
      
      {/* User name */}
      <Text
        style={{
          fontSize: Math.floor(avatarSize * 0.15),
          fontWeight: '600',
          color: colors.gray900,
          marginTop: spacing.sm,
          textAlign: 'center',
        }}
        numberOfLines={2}
      >
        {user.name || 'Unknown User'}
      </Text>

      {/* Manual sync button for pending uploads */}
      {hasPendingUpload && isOnline && (
        <TouchableOpacity
          style={{
            marginTop: spacing.xs,
            paddingHorizontal: spacing.sm,
            paddingVertical: spacing.xs,
            backgroundColor: colors.primary,
            borderRadius: 12,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onPress={processPendingAvatarUploads}
          disabled={isUploading}
        >
          <Ionicons
            name="cloud-upload-outline"
            size={16}
            color={colors.white}
            style={{ marginRight: 4 }}
          />
          <Text
            style={{
              fontSize: 12,
              fontWeight: '600',
              color: colors.white,
            }}
          >
            Sync Photo
          </Text>
        </TouchableOpacity>
      )}
      
      {/* User status or phone */}
      {(user.status || user.phone) && (
        <Text
          style={{
            fontSize: Math.floor(avatarSize * 0.12),
            color: colors.gray500,
            marginTop: spacing.xs,
            textAlign: 'center',
          }}
          numberOfLines={1}
        >
          {user.status || user.phone}
        </Text>
      )}
    </View>
  );
};

// Preset profile avatar sizes
export const SmallProfileAvatar: React.FC<Omit<ProfileAvatarProps, 'size'>> = (props) => (
  <ProfileAvatar {...props} size="small" />
);

export const MediumProfileAvatar: React.FC<Omit<ProfileAvatarProps, 'size'>> = (props) => (
  <ProfileAvatar {...props} size="medium" />
);

export const LargeProfileAvatar: React.FC<Omit<ProfileAvatarProps, 'size'>> = (props) => (
  <ProfileAvatar {...props} size="large" />
);

export const XLargeProfileAvatar: React.FC<Omit<ProfileAvatarProps, 'size'>> = (props) => (
  <ProfileAvatar {...props} size="xlarge" />
);

export default ProfileAvatar;
