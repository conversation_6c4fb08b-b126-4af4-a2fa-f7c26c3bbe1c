/**
 * Network State Management Service for IraChat
 * Centralized network connectivity monitoring and management
 * Similar to <PERSON>s<PERSON><PERSON>'s network handling with retry mechanisms
 */

import NetInfo, { NetInfoState, NetInfoStateType } from '@react-native-community/netinfo';
import { AppState, AppStateStatus } from 'react-native';

export interface NetworkState {
  isConnected: boolean;
  connectionType: NetInfoStateType | null;
  isInternetReachable: boolean | null;
  connectionQuality: 'poor' | 'good' | 'excellent' | 'unknown';
  lastConnectedAt?: number;
  lastDisconnectedAt?: number;
  reconnectAttempts: number;
  isReconnecting: boolean;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number; // Base delay in milliseconds
  maxDelay: number; // Maximum delay in milliseconds
  backoffFactor: number; // Exponential backoff multiplier
  jitter: boolean; // Add random jitter to prevent thundering herd
}

export interface NetworkListener {
  id: string;
  callback: (state: NetworkState) => void;
  priority: number; // Higher priority listeners are called first
}

export interface ConnectionTest {
  url: string;
  timeout: number;
  expectedStatus?: number;
}

class NetworkStateManager {
  private currentState: NetworkState = {
    isConnected: true, // Assume online until proven otherwise
    connectionType: null,
    isInternetReachable: true, // Assume reachable until proven otherwise
    connectionQuality: 'good',
    reconnectAttempts: 0,
    isReconnecting: false,
  };

  private listeners: Map<string, NetworkListener> = new Map();
  private retryTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private connectionTests: ConnectionTest[] = [
    { url: 'https://www.google.com/generate_204', timeout: 5000, expectedStatus: 204 },
    { url: 'https://www.cloudflare.com/cdn-cgi/trace', timeout: 5000 },
    { url: 'https://httpbin.org/status/200', timeout: 5000, expectedStatus: 200 },
  ];

  private defaultRetryConfig: RetryConfig = {
    maxRetries: 5,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 2,
    jitter: true,
  };

  private netInfoUnsubscribe: (() => void) | null = null;
  private appStateSubscription: any = null;
  private qualityTestInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🌐 Initializing network state manager...');

      // Get initial network state
      const initialState = await NetInfo.fetch();
      this.updateNetworkState(initialState);

      // Set up network state listener
      this.netInfoUnsubscribe = NetInfo.addEventListener(this.handleNetworkStateChange.bind(this));

      // Set up app state listener
      this.setupAppStateListener();

      // Start periodic connection quality tests
      this.startQualityTesting();

      this.isInitialized = true;
      console.log('✅ Network state manager initialized');
    } catch (error) {
      console.error('❌ Failed to initialize network state manager:', error);
      throw error;
    }
  }

  private handleNetworkStateChange(state: NetInfoState): void {
    const wasConnected = this.currentState.isConnected;
    this.updateNetworkState(state);
    const isNowConnected = this.currentState.isConnected;

    // Handle connection state transitions
    if (!wasConnected && isNowConnected) {
      this.handleReconnection();
    } else if (wasConnected && !isNowConnected) {
      this.handleDisconnection();
    }

    // Notify all listeners
    this.notifyListeners();
  }

  private updateNetworkState(state: NetInfoState): void {
    const now = Date.now();
    const wasConnected = this.currentState.isConnected;
    const isConnected = state.isConnected ?? false;

    this.currentState = {
      ...this.currentState,
      isConnected,
      connectionType: state.type,
      isInternetReachable: state.isInternetReachable,
      lastConnectedAt: isConnected && !wasConnected ? now : this.currentState.lastConnectedAt,
      lastDisconnectedAt: !isConnected && wasConnected ? now : this.currentState.lastDisconnectedAt,
    };

    // Update connection quality based on connection type
    this.updateConnectionQuality(state);
  }

  private updateConnectionQuality(state: NetInfoState): void {
    if (!state.isConnected) {
      this.currentState.connectionQuality = 'unknown';
      return;
    }

    switch (state.type) {
      case 'wifi':
        this.currentState.connectionQuality = 'excellent';
        break;
      case 'cellular':
        // Check cellular generation if available
        const details = state.details as any;
        if (details?.cellularGeneration) {
          switch (details.cellularGeneration) {
            case '5g':
              this.currentState.connectionQuality = 'excellent';
              break;
            case '4g':
              this.currentState.connectionQuality = 'good';
              break;
            case '3g':
              this.currentState.connectionQuality = 'poor';
              break;
            default:
              this.currentState.connectionQuality = 'poor';
          }
        } else {
          this.currentState.connectionQuality = 'good'; // Default for cellular
        }
        break;
      case 'ethernet':
        this.currentState.connectionQuality = 'excellent';
        break;
      default:
        this.currentState.connectionQuality = 'unknown';
    }
  }

  private handleReconnection(): void {
    console.log('🔄 Network reconnected');
    this.currentState.isReconnecting = false;
    this.currentState.reconnectAttempts = 0;
    
    // Clear any pending retry timeouts
    this.retryTimeouts.forEach(timeout => clearTimeout(timeout));
    this.retryTimeouts.clear();
  }

  private handleDisconnection(): void {
    console.log('📵 Network disconnected');
    this.currentState.connectionQuality = 'unknown';
  }

  private setupAppStateListener(): void {
    this.appStateSubscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        if (nextAppState === 'active') {
          // App came to foreground, refresh network state
          this.refreshNetworkState();
        }
      }
    );
  }

  private startQualityTesting(): void {
    // Test connection quality every 30 seconds when connected
    this.qualityTestInterval = setInterval(() => {
      if (this.currentState.isConnected) {
        this.testConnectionQuality();
      }
    }, 30000);
  }

  private async testConnectionQuality(): Promise<void> {
    if (!this.currentState.isConnected) return;

    try {
      const startTime = Date.now();
      const testPromises = this.connectionTests.map(test => this.performConnectionTest(test));
      
      // Wait for at least one test to succeed
      await Promise.race(testPromises);
      
      const responseTime = Date.now() - startTime;
      
      // Update quality based on response time
      if (responseTime < 500) {
        this.currentState.connectionQuality = 'excellent';
      } else if (responseTime < 2000) {
        this.currentState.connectionQuality = 'good';
      } else {
        this.currentState.connectionQuality = 'poor';
      }
    } catch (error) {
      console.warn('⚠️ Connection quality test failed:', error);
      this.currentState.connectionQuality = 'poor';
    }
  }

  private async performConnectionTest(test: ConnectionTest): Promise<void> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), test.timeout);

    try {
      const response = await fetch(test.url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache',
      });

      if (test.expectedStatus && response.status !== test.expectedStatus) {
        throw new Error(`Unexpected status: ${response.status}`);
      }
    } finally {
      clearTimeout(timeoutId);
    }
  }

  // Public API methods
  addListener(id: string, callback: (state: NetworkState) => void, priority: number = 0): void {
    this.listeners.set(id, { id, callback, priority });
    
    // Immediately call the callback with current state
    callback(this.getState());
  }

  removeListener(id: string): void {
    this.listeners.delete(id);
  }

  // Subscribe method for easier usage (returns unsubscribe function)
  subscribe(callback: (isConnected: boolean) => void): () => void {
    const id = `subscriber_${Date.now()}_${Math.random()}`;
    this.addListener(id, (state) => callback(state.isConnected));
    return () => this.removeListener(id);
  }

  getState(): NetworkState {
    return { ...this.currentState };
  }

  isOnline(): boolean {
    return this.currentState.isConnected && this.currentState.isInternetReachable !== false;
  }

  isOffline(): boolean {
    return !this.isOnline();
  }

  getConnectionQuality(): NetworkState['connectionQuality'] {
    return this.currentState.connectionQuality;
  }

  async refreshNetworkState(): Promise<void> {
    try {
      const state = await NetInfo.fetch();
      this.handleNetworkStateChange(state);
    } catch (error) {
      console.error('❌ Failed to refresh network state:', error);
    }
  }

  // Retry mechanism with exponential backoff
  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    operationId: string,
    config: Partial<RetryConfig> = {}
  ): Promise<T> {
    const retryConfig = { ...this.defaultRetryConfig, ...config };
    let lastError: Error;

    for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
      try {
        // Clear any existing timeout for this operation
        const existingTimeout = this.retryTimeouts.get(operationId);
        if (existingTimeout) {
          clearTimeout(existingTimeout);
          this.retryTimeouts.delete(operationId);
        }

        const result = await operation();
        return result;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === retryConfig.maxRetries) {
          break; // Don't wait after the last attempt
        }

        // Calculate delay with exponential backoff
        let delay = Math.min(
          retryConfig.baseDelay * Math.pow(retryConfig.backoffFactor, attempt),
          retryConfig.maxDelay
        );

        // Add jitter to prevent thundering herd
        if (retryConfig.jitter) {
          delay += Math.random() * 1000;
        }

        console.log(`⏳ Retry attempt ${attempt + 1}/${retryConfig.maxRetries} for ${operationId} in ${delay}ms`);

        // Wait before retrying
        await new Promise(resolve => {
          const timeoutId = setTimeout(resolve, delay);
          this.retryTimeouts.set(operationId, timeoutId);
        });
      }
    }

    throw lastError!;
  }

  // Wait for connection
  async waitForConnection(timeout: number = 30000): Promise<void> {
    if (this.isOnline()) return;

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.removeListener('waitForConnection');
        reject(new Error('Timeout waiting for connection'));
      }, timeout);

      this.addListener('waitForConnection', (state) => {
        if (state.isConnected && state.isInternetReachable !== false) {
          clearTimeout(timeoutId);
          this.removeListener('waitForConnection');
          resolve();
        }
      });
    });
  }

  private notifyListeners(): void {
    // Sort listeners by priority (higher first)
    const sortedListeners = Array.from(this.listeners.values())
      .sort((a, b) => b.priority - a.priority);

    sortedListeners.forEach(listener => {
      try {
        listener.callback(this.getState());
      } catch (error) {
        console.error(`❌ Error in network listener ${listener.id}:`, error);
      }
    });
  }

  // Cleanup
  cleanup(): void {
    if (this.netInfoUnsubscribe) {
      this.netInfoUnsubscribe();
      this.netInfoUnsubscribe = null;
    }

    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }

    if (this.qualityTestInterval) {
      clearInterval(this.qualityTestInterval);
      this.qualityTestInterval = null;
    }

    this.retryTimeouts.forEach(timeout => clearTimeout(timeout));
    this.retryTimeouts.clear();
    this.listeners.clear();

    this.isInitialized = false;
    console.log('🧹 Network state manager cleaned up');
  }

  // Statistics
  getStats(): {
    listenersCount: number;
    activeRetries: number;
    uptime: number;
    downtime: number;
  } {
    const now = Date.now();
    const uptime = this.currentState.lastConnectedAt ? now - this.currentState.lastConnectedAt : 0;
    const downtime = this.currentState.lastDisconnectedAt ? now - this.currentState.lastDisconnectedAt : 0;

    return {
      listenersCount: this.listeners.size,
      activeRetries: this.retryTimeouts.size,
      uptime: this.currentState.isConnected ? uptime : 0,
      downtime: this.currentState.isConnected ? 0 : downtime,
    };
  }
}

// Export singleton instance
export const networkStateManager = new NetworkStateManager();
export default networkStateManager;
