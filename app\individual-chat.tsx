import { useLocalSearchParams, useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { ActivityIndicator, Text, TouchableOpacity, View } from "react-native";
import { UltimateIndividualChatRoom } from "../src/components/UltimateIndividualChatRoom";
import { auth } from "../src/services/firebase";
import { User } from "../src/types";

export default function IndividualChatScreen() {
  const params = useLocalSearchParams();
  const router = useRouter();
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  useEffect(() => {
    // Quick initialization without blocking
    const initializeChat = async () => {
      try {
        // Validate required params
        if (!params.contactId || !params.contactName) {
          setError('Missing contact information');
          return;
        }

        // Create User object from Firebase auth user
        if (!auth?.currentUser) {
          console.warn("User not authenticated");
          setError('User not authenticated');
          return;
        }

        const firebaseUser = auth.currentUser;

        // Determine auth method and create appropriate user object
        const isEmailAuth = firebaseUser.email && !firebaseUser.phoneNumber;
        const isPhoneAuth = firebaseUser.phoneNumber && !firebaseUser.email;

        let userObject: User;

        if (isEmailAuth) {
          userObject = {
            id: firebaseUser.uid,
            username: firebaseUser.email!.split('@')[0],
            displayName: firebaseUser.displayName || firebaseUser.email!.split('@')[0],
            name: firebaseUser.displayName || firebaseUser.email!.split('@')[0],
            avatar: firebaseUser.photoURL || '',
            photoURL: firebaseUser.photoURL || '',
            followersCount: 0,
            followingCount: 0,
            likesCount: 0,
            isVerified: false,
            isOnline: true,
            email: firebaseUser.email!,
            authMethod: 'email' as const,
            emailVerified: firebaseUser.emailVerified,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };
        } else if (isPhoneAuth) {
          userObject = {
            id: firebaseUser.uid,
            username: firebaseUser.phoneNumber!.replace(/\D/g, ''),
            displayName: firebaseUser.displayName || firebaseUser.phoneNumber!,
            name: firebaseUser.displayName || firebaseUser.phoneNumber!,
            avatar: firebaseUser.photoURL || '',
            photoURL: firebaseUser.photoURL || '',
            followersCount: 0,
            followingCount: 0,
            likesCount: 0,
            isVerified: false,
            isOnline: true,
            phoneNumber: firebaseUser.phoneNumber!,
            authMethod: 'phone' as const,
            phoneVerified: true,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };
        } else {
          // Fallback for mixed or unknown auth
          userObject = {
            id: firebaseUser.uid,
            username: firebaseUser.email?.split('@')[0] || firebaseUser.uid.substring(0, 8),
            displayName: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
            name: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
            avatar: firebaseUser.photoURL || '',
            photoURL: firebaseUser.photoURL || '',
            followersCount: 0,
            followingCount: 0,
            likesCount: 0,
            isVerified: false,
            isOnline: true,
            email: firebaseUser.email || '<EMAIL>',
            authMethod: 'email' as const,
            emailVerified: firebaseUser.emailVerified,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };
        }

        setCurrentUser(userObject);
        setIsReady(true);
        console.log('🔍 Current user set in individual-chat:', userObject);
      } catch (err) {
        console.error('Error initializing chat:', err);
        setError('Failed to initialize chat');
      }
    };

    // Initialize immediately for faster UX
    initializeChat();
  }, [params]);

  // Parse contact data from params with validation
  const contact = {
    id: (params.contactId as string) || '',
    name: (params.contactName as string) || 'Unknown',
    avatar: (params.contactAvatar as string) || '',
    isOnline: params.contactIsOnline === "true",
  };

  // Show error state
  if (error) {
    return (
      <View style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#f8f9fa",
        padding: 20,
      }}>
        <Text style={{
          fontSize: 18,
          color: "#dc3545",
          textAlign: "center",
          marginBottom: 16,
        }}>
          {error}
        </Text>
        <TouchableOpacity
          onPress={() => router.back()}
          style={{
            backgroundColor: "#667eea",
            paddingHorizontal: 20,
            paddingVertical: 10,
            borderRadius: 8,
          }}
        >
          <Text style={{ color: "white", fontWeight: "600" }}>
            Go Back
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Don't render until we have current user
  if (!currentUser) {
    return null; // Or a loading spinner
  }

  // Optimistic UI - show chat immediately with skeleton loading
  // Remove the ugly full-screen loading state

  return (
    <UltimateIndividualChatRoom
      chatId={(params.chatId as string) || `chat_${contact.id}`}
      partnerName={contact.name}
      partnerAvatar={contact.avatar}
      partnerId={contact.id}
      currentUserId={currentUser.id}
    />
  );
}
