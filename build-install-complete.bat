@echo off
echo =====================================================
echo COMPLETE STANDALONE APK BUILD AND INSTALL
echo =====================================================
echo.
echo This will:
echo 1. Set up D: drive cache
echo 2. Build standalone APK
echo 3. Download and install on your phone
echo.
echo Make sure your phone is connected via USB with USB debugging enabled!
echo.
pause

echo.
echo Running complete build process...
echo.

REM Step 1: Build the APK
echo =====================================================
echo STEP 1: BUILDING STANDALONE APK
echo =====================================================
powershell -ExecutionPolicy Bypass -File "build-standalone-apk.ps1"

if %errorlevel% neq 0 (
    echo Build failed. Please check the errors above.
    pause
    exit /b 1
)

echo.
echo =====================================================
echo BUILD SUBMITTED! WAITING FOR COMPLETION...
echo =====================================================
echo.
echo The APK is building in the cloud. This takes 10-20 minutes.
echo.
echo Press any key when you receive notification that build is complete...
pause

REM Step 2: Download and install
echo.
echo =====================================================
echo STEP 2: DOWNLOADING AND INSTALLING APK
echo =====================================================
powershell -ExecutionPolicy Bypass -File "download-and-install-apk.ps1"

echo.
echo =====================================================
echo PROCESS COMPLETE!
echo =====================================================
echo.
echo Your IraChat app should now be installed on your phone!
echo You can open it directly without needing Expo Go.
echo.
pause
