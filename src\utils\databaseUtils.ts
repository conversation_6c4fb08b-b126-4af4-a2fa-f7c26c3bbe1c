/**
 * Database utility functions for handling common database issues
 */

import { offlineDatabaseService } from '../services/offlineDatabase';

/**
 * Handle database initialization with retry logic
 */
export async function ensureDatabaseReady(maxRetries: number = 3): Promise<boolean> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🗄️ Database initialization attempt ${attempt}/${maxRetries}`);
      
      if (!offlineDatabaseService.isReady()) {
        await offlineDatabaseService.initialize();
      }
      
      // Ensure schema is correct
      await offlineDatabaseService.ensureReady();
      
      console.log('✅ Database is ready');
      return true;
    } catch (error) {
      console.error(`❌ Database initialization attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        console.error('💥 All database initialization attempts failed');
        return false;
      }
      
      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  
  return false;
}

/**
 * Handle database schema issues with automatic fixing
 */
export async function handleDatabaseSchemaIssue(error: Error): Promise<boolean> {
  try {
    if (error.message.includes('no such column')) {
      console.log('🔧 Detected missing column, attempting to fix schema...');
      // Database schema is automatically fixed during initialization
      console.log('Database schema check completed');
      return true;
    }
    
    if (error.message.includes('syntax error')) {
      console.log('🔧 Detected SQL syntax error, attempting database reset...');
      await offlineDatabaseService.resetDatabase();
      return true;
    }
    
    return false;
  } catch (fixError) {
    console.error('❌ Failed to fix database schema:', fixError);
    return false;
  }
}

/**
 * Safe database operation wrapper with error handling
 */
export async function safeDatabaseOperation<T>(
  operation: () => Promise<T>,
  fallbackValue: T,
  operationName: string = 'database operation'
): Promise<T> {
  try {
    // Ensure database is ready first
    const isReady = await ensureDatabaseReady();
    if (!isReady) {
      console.warn(`⚠️ Database not ready for ${operationName}, using fallback`);
      return fallbackValue;
    }
    
    return await operation();
  } catch (error) {
    console.error(`❌ ${operationName} failed:`, error);
    
    // Try to fix the issue
    if (error instanceof Error) {
      const fixed = await handleDatabaseSchemaIssue(error);
      if (fixed) {
        try {
          console.log(`🔄 Retrying ${operationName} after fix...`);
          return await operation();
        } catch (retryError) {
          console.error(`❌ ${operationName} failed even after fix:`, retryError);
        }
      }
    }
    
    return fallbackValue;
  }
}

/**
 * Debug database state
 */
export async function debugDatabase(): Promise<void> {
  try {
    console.log('🔍 Starting database debug...');
    await offlineDatabaseService.debugDatabaseSchema();
  } catch (error) {
    console.error('❌ Database debug failed:', error);
  }
}
