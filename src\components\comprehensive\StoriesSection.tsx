import React from 'react';
import { View, Text, TouchableOpacity, FlatList, Image, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Story } from '../../types/Update';
import { COLORS } from '../../constants/theme';

interface StoriesSectionProps {
  stories: Story[];
  myStories: Story[];
  currentUserId?: string;
  onStoryPress: (story: Story, index: number) => void;
  onCreateStoryPress: () => void;
  isLoading?: boolean;
}

export const StoriesSection: React.FC<StoriesSectionProps> = ({
  stories,
  myStories,
  currentUserId,
  onStoryPress,
  onCreateStoryPress,
  isLoading = false,
}) => {
  const allStories = [...myStories, ...stories];

  const renderStoryItem = ({ item, index }: { item: Story; index: number }) => {
    const isMyStory = item.userId === currentUserId;
    const hasUnviewedStory = !item.views.some(view => view.userId === currentUserId);

    return (
      <TouchableOpacity
        style={styles.storyItem}
        onPress={() => onStoryPress(item, index)}
        activeOpacity={0.8}
      >
        <View style={[
          styles.storyImageContainer,
          hasUnviewedStory && styles.unviewedStoryBorder,
          isMyStory && styles.myStoryBorder,
        ]}>
          {item.type === 'image' && item.media[0]?.url ? (
            <Image source={{ uri: item.media[0].url }} style={styles.storyImage} />
          ) : item.type === 'video' && item.media[0]?.url ? (
            <View style={styles.videoStoryContainer}>
              <Image source={{ uri: item.media[0].url }} style={styles.storyImage} />
              <View style={styles.videoPlayIcon}>
                <Ionicons name="play" size={12} color="#FFFFFF" />
              </View>
            </View>
          ) : (
            <View style={[
              styles.textStoryContainer,
              { backgroundColor: item.textOverlays?.[0]?.style?.backgroundColor || COLORS.primary }
            ]}>
              <Text style={styles.textStoryPreview} numberOfLines={3}>
                {item.textOverlays?.[0]?.text || item.caption || 'Story'}
              </Text>
            </View>
          )}

          {/* Story type indicator */}
          <View style={styles.storyTypeIndicator}>
            <Ionicons
              name={
                item.type === 'image' ? 'image' :
                item.type === 'video' ? 'videocam' : 'text'
              }
              size={10}
              color="#FFFFFF"
            />
          </View>

          {/* My story add indicator */}
          {isMyStory && (
            <View style={styles.addStoryIndicator}>
              <Ionicons name="add" size={12} color="#FFFFFF" />
            </View>
          )}
        </View>

        <Text style={styles.storyUserName} numberOfLines={1}>
          {isMyStory ? 'Your Story' : item.userName}
        </Text>

        {/* Story timestamp */}
        <Text style={styles.storyTime}>
          {getTimeAgo(item.timestamp)}
        </Text>
      </TouchableOpacity>
    );
  };

  const renderCreateStoryItem = () => (
    <TouchableOpacity
      style={styles.storyItem}
      onPress={onCreateStoryPress}
      activeOpacity={0.8}
    >
      <View style={styles.createStoryContainer}>
        <View style={styles.createStoryIcon}>
          <Ionicons name="add" size={24} color={COLORS.primary} />
        </View>
        <View style={styles.createStoryPlus}>
          <Ionicons name="add-circle" size={16} color={COLORS.primary} />
        </View>
      </View>
      <Text style={styles.createStoryText}>Create Story</Text>
    </TouchableOpacity>
  );

  const getTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'now';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h`;
    
    return '1d+';
  };

  if (isLoading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading stories...</Text>
        </View>
      </View>
    );
  }

  if (allStories.length === 0 && !currentUserId) {
    return null;
  }

  return (
    <View style={styles.container}>
      <FlatList
        horizontal
        showsHorizontalScrollIndicator={false}
        data={allStories}
        renderItem={renderStoryItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={currentUserId ? renderCreateStoryItem : null}
        contentContainerStyle={styles.storiesContent}
        ItemSeparatorComponent={() => <View style={styles.storySeparator} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1F2937',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#374151',
  },
  loadingContainer: {
    paddingHorizontal: 16,
    paddingVertical: 20,
    alignItems: 'center',
  },
  loadingText: {
    color: COLORS.textSecondary,
    fontSize: 14,
  },
  storiesContent: {
    paddingHorizontal: 16,
  },
  storyItem: {
    alignItems: 'center',
    width: 70,
  },
  storySeparator: {
    width: 12,
  },
  storyImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    overflow: 'hidden',
    position: 'relative',
    borderWidth: 2,
    borderColor: '#374151',
  },
  unviewedStoryBorder: {
    borderColor: COLORS.primary,
    borderWidth: 3,
  },
  myStoryBorder: {
    borderColor: '#10B981',
    borderWidth: 3,
  },
  storyImage: {
    width: '100%',
    height: '100%',
  },
  videoStoryContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  videoPlayIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -6 }, { translateY: -6 }],
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textStoryContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  textStoryPreview: {
    color: '#FFFFFF',
    fontSize: 8,
    fontWeight: '600',
    textAlign: 'center',
  },
  storyTypeIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addStoryIndicator: {
    position: 'absolute',
    top: 2,
    right: 2,
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    width: 16,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  storyUserName: {
    color: COLORS.text,
    fontSize: 11,
    fontWeight: '500',
    marginTop: 6,
    textAlign: 'center',
  },
  storyTime: {
    color: COLORS.textSecondary,
    fontSize: 9,
    marginTop: 2,
    textAlign: 'center',
  },
  createStoryContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#374151',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 2,
    borderColor: '#4B5563',
    borderStyle: 'dashed',
  },
  createStoryIcon: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  createStoryPlus: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: '#1F2937',
    borderRadius: 8,
    padding: 1,
  },
  createStoryText: {
    color: COLORS.primary,
    fontSize: 11,
    fontWeight: '600',
    marginTop: 6,
    textAlign: 'center',
  },
});
