import { useLocalSearchParams , useRouter } from "expo-router";
import { useEffect, useState , useCallback } from "react";
import { ActivityIndicator, Text, TouchableOpacity, View, StyleSheet } from "react-native";
import { StatusBar } from 'expo-status-bar';
import { onSnapshot, doc, DocumentSnapshot } from 'firebase/firestore';
import { UltimateGroupChatRoom } from "../src/components/UltimateGroupChatRoom";
import { realGroupService } from "../src/services/realGroupService";
import { useSelector } from "react-redux";
import { db } from "../src/services/firebaseSimple";
import { navigationService } from "../src/services/navigationService";
import { RootState } from "../src/redux/store";


interface GroupMember {
  id: string;
  name: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member';
  isOnline: boolean;
  lastSeen?: Date;
  joinedAt: Date;
}

export default function GroupChatScreen() {
  const params = useLocalSearchParams();
  const _router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [groupMembers, setGroupMembers] = useState<GroupMember[]>([]);
  const [onlineCount, setOnlineCount] = useState(0);

  // 🔥 REAL FIREBASE REAL-TIME ONLINE STATUS MONITORING
  const setupOnlineStatusListener = (groupId: string) => {
    const unsubscribers: (() => void)[] = [];

    try {
      // Listen to group members' online status in real-time
      groupMembers.forEach(member => {
        const userStatusRef = doc(db, 'userStatus', member.id);

        const unsubscribe = onSnapshot(userStatusRef, (statusDoc: DocumentSnapshot) => {
          if (statusDoc.exists()) {
            const statusData = statusDoc.data();
            const isOnline = statusData?.isOnline &&
                           statusData?.lastSeen &&
                           (Date.now() - statusData.lastSeen.toMillis()) < 300000; // 5 minutes threshold

            // Update specific member's online status
            setGroupMembers(prevMembers =>
              prevMembers.map(m =>
                m.id === member.id
                  ? { ...m, isOnline, lastSeen: statusData?.lastSeen?.toDate() }
                  : m
              )
            );
          }
        }, (error: any) => {
          console.error(`❌ Error listening to user ${member.id} status:`, error);
        });

        unsubscribers.push(unsubscribe);
      });

      // Listen to group's online members count in real-time
      const groupStatusRef = doc(db, 'groupStatus', groupId);
      const groupUnsubscribe = onSnapshot(groupStatusRef, (groupDoc: DocumentSnapshot) => {
        if (groupDoc.exists()) {
          const groupData = groupDoc.data();
          setOnlineCount(groupData?.onlineCount || 0);
        }
      }, (error: any) => {
        console.error('❌ Error listening to group status:', error);
      });

      unsubscribers.push(groupUnsubscribe);

    } catch (error) {
      console.error('❌ Error setting up real-time listeners:', error);
    }

    // Return cleanup function
    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe());
    };
  };

  const loadGroupData = useCallback(async () => {
    try {
      const groupId = params.groupId as string;

      // Load group information from Firebase
      const groupData = await realGroupService.getGroupById(groupId);
      if (!groupData) {
        console.log('⚠️ Group not found, using basic group data');
        // Don't throw error, just use basic data
        return;
      }

      // Convert Firebase group data to GroupMember format with safe fallbacks
      const members: GroupMember[] = (groupData.members || []).map((memberId: string) => ({
        id: memberId,
        name: (groupData.memberNames && groupData.memberNames[memberId]) || 'Unknown User',
        avatar: (groupData.memberAvatars && groupData.memberAvatars[memberId]) || '',
        role: (groupData.memberRoles && groupData.memberRoles[memberId]) || 'member',
        isOnline: false, // Will be updated by online status listener
        joinedAt: (groupData.memberJoinedAt && groupData.memberJoinedAt[memberId]) || new Date(),
      }));

      setGroupMembers(members);

      // Set up real-time online status monitoring
      setupOnlineStatusListener(groupId);

    } catch (error) {
      console.error('Error loading group data:', error);
      // Don't throw error, just log it and continue
      console.log('⚠️ Continuing with basic group functionality');
    }
  }, [params]);

  useEffect(() => {
    const initializeGroupChat = async () => {
      try {
        // Validate required params - only groupId is truly required
        if (!params.groupId) {
          setError('Missing group ID');
          return;
        }

        // Validate user authentication
        if (!currentUser?.id) {
          setError('User not authenticated');
          return;
        }

        // Set ready immediately for better UX - no more loading screen!
        setIsReady(true);

        // Load group data in background (non-blocking)
        loadGroupData().catch(err => {
          console.error('Background group data loading failed:', err);
          // Don't set error state, just log it
        });
      } catch (err) {
        console.error('Error initializing group chat:', err);
        // Still set ready to true to show the chat interface
        setIsReady(true);
      }
    };

    initializeGroupChat();
  }, [params, currentUser, loadGroupData]);



  // Parse group data from params with validation
  const group = {
    id: (params.groupId as string) || '',
    name: (params.groupName as string) || 'Unknown Group',
    avatar: (params.groupAvatar as string) || '',
    isAdmin: params.isAdmin === "true",
  };

  // Show error state
  if (error) {
    return (
      <View style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#f8f9fa",
        padding: 20,
      }}>
        <Text style={{
          fontSize: 18,
          fontWeight: "600",
          color: "#dc3545",
          marginBottom: 16,
          textAlign: "center",
        }}>
          {error}
        </Text>
        <TouchableOpacity
          onPress={() => navigationService.goBack()}
          style={{
            backgroundColor: "#87CEEB",
            paddingHorizontal: 24,
            paddingVertical: 12,
            borderRadius: 8,
          }}
        >
          <Text style={{
            color: "#FFFFFF",
            fontSize: 16,
            fontWeight: "600",
          }}>
            Go Back
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Show loading state
  if (!isReady) {
    return (
      <View style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#f8f9fa",
      }}>
        <ActivityIndicator size="large" color="#87CEEB" />
        <Text style={{
          marginTop: 16,
          fontSize: 16,
          color: "#6c757d",
        }}>
          Loading group chat...
        </Text>
      </View>
    );
  }

  // Render the ULTIMATE GroupChatRoom component with all premium features
  return (
    <UltimateGroupChatRoom
      groupId={group.id}
      groupName={group.name}
      groupAvatar={group.avatar}
      isAdmin={group.isAdmin}
      currentUserId={currentUser?.id || ''}
      currentUserName={currentUser?.displayName || currentUser?.name || 'User'}
      currentUserAvatar={currentUser?.photoURL || currentUser?.avatar}
      onBack={() => {
        console.log('🔙 Navigating back to groups list');
        try {
          _router.back();
        } catch (error) {
          console.error('❌ Router error:', error);
          // Fallback navigation
          _router.replace('/(tabs)/groups');
        }
      }}
    />
  );
}
