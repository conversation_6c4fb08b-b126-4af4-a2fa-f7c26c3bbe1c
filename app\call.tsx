// Active Call Screen Route
import { useEffect, useState } from 'react';
import { BackHandler, View, StyleSheet } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { RealCallScreen } from '../src/components/CallScreen';
import { useRealCallManager } from '../src/hooks/useRealCallManager';
import { realCallService } from '../src/services/realCallService';
import { auth } from '../src/services/firebaseSimple';
import { ResponsiveContainer } from '../src/components/ui/ResponsiveContainer';
import { IRACHAT_COLORS } from '../src/styles/iraChatDesignSystem';
import { DeviceInfo } from '../src/utils/responsiveUtils';

export default function CallScreenPage() {
  const currentUser = auth?.currentUser;
  const {
    callState,
    endCall,
    toggleMute,
    toggleVideo,
    toggleSpeaker,
    switchCamera
  } = useRealCallManager();

  const [streams, setStreams] = useState<{
    localStream: any;
    remoteStream: any;
  }>({ localStream: null, remoteStream: null });

  // Get device info for responsive design - using DeviceInfo object directly
  const isSmallDevice = DeviceInfo.isSmallPhone;
  const isTablet = DeviceInfo.isTablet;

  // Get streams from real calling service
  useEffect(() => {
    const updateStreams = () => {
      const localStream = realCallService.getLocalStream();
      const remoteStream = realCallService.getRemoteStream();
      setStreams({ localStream, remoteStream });
    };

    // Update streams initially
    updateStreams();

    // Update streams periodically during call
    const interval = setInterval(updateStreams, 1000);

    return () => clearInterval(interval);
  }, [callState.currentCall]);

  // Prevent back button during call
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      // Don't allow back button to close call screen
      return true;
    });

    return () => backHandler.remove();
  }, []);

  // Log current user for call analytics
  useEffect(() => {
    if (currentUser && callState.currentCall) {
      console.log(`Call initiated by user: ${currentUser.uid}`);
    }
  }, [currentUser, callState.currentCall]);

  if (!callState.currentCall) {
    return (
      <View style={[styles.emptyContainer, { backgroundColor: IRACHAT_COLORS.background }]}>
        <StatusBar style="light" backgroundColor={IRACHAT_COLORS.background} />
      </View>
    );
  }

  // This screen is for individual calls only (voice/video)
  // Group calls are handled separately

  return (
    <ResponsiveContainer
      wallpaperVariant="minimal"
      backgroundColor={IRACHAT_COLORS.background}
      paddingHorizontal={!isTablet}
      paddingVertical={!isSmallDevice}
    >
      <StatusBar style="light" backgroundColor={IRACHAT_COLORS.background} />
      <RealCallScreen
        call={callState.currentCall}
        localStream={streams.localStream}
        remoteStream={streams.remoteStream}
        onEndCall={endCall}
        onToggleMute={toggleMute}
        onToggleVideo={toggleVideo}
        onToggleSpeaker={toggleSpeaker}
        onToggleCamera={switchCamera}
        isMuted={callState.isMuted}
        isVideoEnabled={callState.isVideoEnabled}
        isSpeakerOn={callState.isSpeakerOn}
        callDuration={callState.callDuration}
        connectionQuality={callState.connectionQuality}
      />
    </ResponsiveContainer>
  );
}

// Styles for the call screen
const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});


