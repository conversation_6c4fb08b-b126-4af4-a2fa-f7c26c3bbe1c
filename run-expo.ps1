# Set Android environment variables
$env:ANDROID_HOME = "F:\Android\Sdk"
$env:PATH = $env:PATH + ";F:\Android\Sdk\platform-tools;F:\Android\Sdk\tools;F:\Android\Sdk\tools\bin"

Write-Host "Android environment set:"
Write-Host "ANDROID_HOME: $env:ANDROID_HOME"

Write-Host "Verifying ADB..."
try {
    & "F:\Android\Sdk\platform-tools\adb.exe" version
    Write-Host "ADB is working!"
} catch {
    Write-Host "ADB test failed: $_"
}

Write-Host "Starting Expo..."
npx expo start
