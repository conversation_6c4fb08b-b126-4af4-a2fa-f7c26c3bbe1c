/**
 * Media Download Service for IraChat
 * Handles downloading and saving media to device gallery
 * Works both offline and online with proper error handling
 */

import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import { Alert, Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { networkStateManager } from './networkStateManager';

export interface DownloadedMedia {
  id: string;
  originalUrl: string;
  localUri: string;
  fileName: string;
  type: 'image' | 'video';
  size: number;
  downloadedAt: number;
  updateId: string;
  userName: string;
  caption?: string;
}

export interface DownloadProgress {
  id: string;
  progress: number;
  status: 'downloading' | 'completed' | 'failed' | 'paused';
}

class MediaDownloadService {
  private readonly DOWNLOADS_KEY = 'downloaded_media';
  private readonly DOWNLOADS_DIR = `${FileSystem.documentDirectory}downloads/`;
  private downloads: Map<string, DownloadedMedia> = new Map();
  private activeDownloads: Map<string, FileSystem.DownloadResumable> = new Map();
  private progressCallbacks: Map<string, (progress: DownloadProgress) => void> = new Map();

  constructor() {
    this.initializeService();
  }

  private async initializeService(): Promise<void> {
    try {
      // Create downloads directory
      const dirInfo = await FileSystem.getInfoAsync(this.DOWNLOADS_DIR);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.DOWNLOADS_DIR, { intermediates: true });
      }

      // Load existing downloads
      await this.loadDownloads();
      
      // Request media library permissions
      await this.requestPermissions();
      
      console.log('✅ Media download service initialized');
    } catch (error) {
      console.error('❌ Error initializing media download service:', error);
    }
  }

  private async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      console.log('📱 Media library permission status:', status);

      if (status !== 'granted') {
        Alert.alert(
          'Permission Required',
          'Please grant media library access to save photos and videos to your gallery.',
          [{ text: 'OK' }]
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error('❌ Error requesting permissions:', error);
      return false;
    }
  }

  /**
   * Check if we have the necessary permissions
   */
  async hasPermissions(): Promise<boolean> {
    try {
      const { status } = await MediaLibrary.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('❌ Error checking permissions:', error);
      return false;
    }
  }

  private async loadDownloads(): Promise<void> {
    try {
      const downloadsData = await AsyncStorage.getItem(this.DOWNLOADS_KEY);
      if (downloadsData) {
        const downloadsArray: DownloadedMedia[] = JSON.parse(downloadsData);
        this.downloads = new Map(downloadsArray.map(item => [item.id, item]));
      }
    } catch (error) {
      console.error('❌ Error loading downloads:', error);
      this.downloads = new Map();
    }
  }

  private async saveDownloads(): Promise<void> {
    try {
      const downloadsArray = Array.from(this.downloads.values());
      await AsyncStorage.setItem(this.DOWNLOADS_KEY, JSON.stringify(downloadsArray));
    } catch (error) {
      console.error('❌ Error saving downloads:', error);
    }
  }

  private generateFileName(url: string, type: 'image' | 'video'): string {
    const timestamp = Date.now();
    const extension = type === 'video' ? 'mp4' : 'jpg';
    return `irachat_${timestamp}.${extension}`;
  }

  private async saveToGallery(localUri: string, type: 'image' | 'video'): Promise<string | null> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) return null;

      const asset = await MediaLibrary.createAssetAsync(localUri);
      
      // Create IraChat album if it doesn't exist
      let album = await MediaLibrary.getAlbumAsync('IraChat');
      if (!album) {
        album = await MediaLibrary.createAlbumAsync('IraChat', asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }

      console.log(`✅ Media saved to gallery: ${asset.uri}`);
      return asset.uri;
    } catch (error) {
      console.error('❌ Error saving to gallery:', error);
      return null;
    }
  }

  /**
   * Download media from URL and save to gallery
   */
  async downloadMedia(
    url: string,
    updateId: string,
    userName: string,
    caption?: string,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<{ success: boolean; downloadedMedia?: DownloadedMedia; error?: string }> {
    try {
      console.log('📥 Starting download process:', { url, updateId, userName });

      // Check permissions first
      const hasPermission = await this.hasPermissions();
      if (!hasPermission) {
        console.log('❌ No media library permissions');
        const granted = await this.requestPermissions();
        if (!granted) {
          return { success: false, error: 'Media library permission denied. Please enable it in Settings.' };
        }
      }

      // Check if already downloaded
      const existingDownload = Array.from(this.downloads.values())
        .find(item => item.originalUrl === url);

      if (existingDownload) {
        console.log('✅ Media already downloaded:', existingDownload.id);
        return { success: true, downloadedMedia: existingDownload };
      }

      // Determine media type
      const type: 'image' | 'video' = url.includes('.mp4') || url.includes('video') ? 'video' : 'image';
      const fileName = this.generateFileName(url, type);
      const downloadId = `download_${Date.now()}`;

      let localUri: string;
      let fileSize: number = 0;

      // Handle local file URLs (file://) vs remote URLs (http/https)
      if (url.startsWith('file://')) {
        // It's already a local file, just copy it to our downloads directory
        localUri = `${this.DOWNLOADS_DIR}${fileName}`;

        // Notify start
        onProgress?.({
          id: downloadId,
          progress: 0,
          status: 'downloading'
        });

        // Copy the file
        await FileSystem.copyAsync({
          from: url,
          to: localUri
        });

        // Get file size
        const fileInfo = await FileSystem.getInfoAsync(localUri);
        fileSize = fileInfo.exists ? fileInfo.size || 0 : 0;

        // Notify progress
        onProgress?.({
          id: downloadId,
          progress: 100,
          status: 'completed'
        });

      } else {
        // It's a remote URL, download it

        // Check network connectivity for remote downloads
        if (!networkStateManager.isOnline()) {
          return { success: false, error: 'No internet connection. Please try again when online.' };
        }

        localUri = `${this.DOWNLOADS_DIR}${fileName}`;

        // Register progress callback
        if (onProgress) {
          this.progressCallbacks.set(downloadId, onProgress);
        }

        // Create download resumable
        const downloadResumable = FileSystem.createDownloadResumable(
          url,
          localUri,
          {},
          (downloadProgress) => {
            const progress = downloadProgress.totalBytesWritten / downloadProgress.totalBytesExpectedToWrite;
            const progressData: DownloadProgress = {
              id: downloadId,
              progress: progress * 100,
              status: 'downloading'
            };

            onProgress?.(progressData);
          }
        );

        this.activeDownloads.set(downloadId, downloadResumable);

        // Start download
        const result = await downloadResumable.downloadAsync();

        if (!result) {
          throw new Error('Download failed');
        }

        // Get file size
        const fileInfo = await FileSystem.getInfoAsync(result.uri);
        fileSize = fileInfo.exists ? fileInfo.size || 0 : 0;
        localUri = result.uri;

        // Cleanup
        this.activeDownloads.delete(downloadId);
        this.progressCallbacks.delete(downloadId);
      }

      // Save to gallery
      const galleryUri = await this.saveToGallery(localUri, type);

      // Create download record
      const downloadedMedia: DownloadedMedia = {
        id: downloadId,
        originalUrl: url,
        localUri,
        fileName,
        type,
        size: fileSize,
        downloadedAt: Date.now(),
        updateId,
        userName,
        caption,
      };

      // Save to storage
      this.downloads.set(downloadId, downloadedMedia);
      await this.saveDownloads();

      console.log(`✅ Media saved successfully: ${fileName}`);
      return { success: true, downloadedMedia };

    } catch (error) {
      console.error('❌ Error saving media:', error);

      // Notify failure
      onProgress?.({
        id: 'error',
        progress: 0,
        status: 'failed'
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save media'
      };
    }
  }

  /**
   * Get all downloaded media
   */
  getDownloadedMedia(): DownloadedMedia[] {
    return Array.from(this.downloads.values()).sort((a, b) => b.downloadedAt - a.downloadedAt);
  }

  /**
   * Get downloaded media by update ID
   */
  getDownloadedMediaByUpdate(updateId: string): DownloadedMedia[] {
    return Array.from(this.downloads.values())
      .filter(item => item.updateId === updateId)
      .sort((a, b) => b.downloadedAt - a.downloadedAt);
  }

  /**
   * Check if media is already downloaded
   */
  isMediaDownloaded(url: string): boolean {
    return Array.from(this.downloads.values()).some(item => item.originalUrl === url);
  }

  /**
   * Delete downloaded media
   */
  async deleteDownloadedMedia(downloadId: string): Promise<boolean> {
    try {
      const download = this.downloads.get(downloadId);
      if (!download) return false;

      // Delete local file
      const fileInfo = await FileSystem.getInfoAsync(download.localUri);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(download.localUri);
      }

      // Remove from storage
      this.downloads.delete(downloadId);
      await this.saveDownloads();

      console.log(`✅ Downloaded media deleted: ${download.fileName}`);
      return true;
    } catch (error) {
      console.error('❌ Error deleting downloaded media:', error);
      return false;
    }
  }

  /**
   * Get total storage used by downloads
   */
  getTotalStorageUsed(): number {
    return Array.from(this.downloads.values())
      .reduce((total, item) => total + item.size, 0);
  }

  /**
   * Clear all downloads
   */
  async clearAllDownloads(): Promise<void> {
    try {
      // Delete all local files
      for (const download of this.downloads.values()) {
        const fileInfo = await FileSystem.getInfoAsync(download.localUri);
        if (fileInfo.exists) {
          await FileSystem.deleteAsync(download.localUri);
        }
      }

      // Clear storage
      this.downloads.clear();
      await this.saveDownloads();

      console.log('🗑️ All downloads cleared');
    } catch (error) {
      console.error('❌ Error clearing downloads:', error);
    }
  }
}

export const mediaDownloadService = new MediaDownloadService();
