import React, { useRef, useEffect } from "react";
import { View, ActivityIndicator, Text, Animated, StyleSheet } from "react-native";
import { useTheme } from "../contexts/ThemeContext";

interface LoadingSpinnerProps {
  message?: string;
  size?: "small" | "large";
  color?: string;
  overlay?: boolean;
}

export default function LoadingSpinner({
  message = "Loading...",
  size = "large",
  color,
  overlay = false,
}: LoadingSpinnerProps) {
  const { colors } = useTheme();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const spinnerColor = color || colors.primary;

  if (overlay) {
    return (
      <Animated.View
        style={[
          styles.overlayContainer,
          {
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            opacity: fadeAnim,
          }
        ]}
      >
        <Animated.View
          style={[
            styles.overlayContent,
            {
              backgroundColor: colors.surface,
              transform: [{ scale: scaleAnim }],
            }
          ]}
        >
          <ActivityIndicator size={size} color={spinnerColor} />
          {message && (
            <Text
              style={[
                styles.overlayText,
                { color: colors.text }
              ]}
            >
              {message}
            </Text>
          )}
        </Animated.View>
      </Animated.View>
    );
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: colors.background,
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        }
      ]}
    >
      <ActivityIndicator size={size} color={spinnerColor} />
      {message && (
        <Text
          style={[
            styles.text,
            { color: colors.textSecondary }
          ]}
        >
          {message}
        </Text>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  overlayContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 50,
  },
  overlayContent: {
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  overlayText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
    fontWeight: '500',
  },
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
});
