/**
 * Enhanced Media Cache Service for IraChat
 * Handles media caching, thumbnails, and offline access
 * Similar to <PERSON>s<PERSON><PERSON>'s media management system
 */

import * as FileSystem from 'expo-file-system';
import * as ImageManipulator from 'expo-image-manipulator';
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface CachedMedia {
  id: string;
  messageId: string;
  chatId: string;
  mediaUrl: string;
  localPath?: string;
  thumbnailPath?: string;
  mediaType: 'image' | 'video' | 'audio' | 'document';
  fileName?: string;
  fileSize?: number;
  width?: number;
  height?: number;
  duration?: number;
  downloadedAt?: number;
  lastAccessed?: number;
  isValid: boolean;
  version?: string;
}

export interface CachedAvatar {
  userId: string;
  url: string;
  localPath?: string;
  timestamp: number;
  size: number;
  version?: string;
  lastChecked?: number;
  downloadedAt?: number;
  fileSize?: number;
  isValid: boolean;
}

class EnhancedMediaCacheService {
  private readonly CACHE_DIR = `${FileSystem.documentDirectory}media_cache/`;
  private readonly AVATAR_CACHE_DIR = `${FileSystem.documentDirectory}avatar_cache/`;
  private readonly THUMBNAIL_CACHE_DIR = `${FileSystem.documentDirectory}thumbnail_cache/`;
  private readonly MAX_CACHE_SIZE = 500 * 1024 * 1024; // 500MB
  private readonly THUMBNAIL_SIZE = 200;

  /**
   * Initialize media cache service
   */
  async initialize(): Promise<void> {
    console.log('🎬 Initializing Enhanced Media Cache Service...');
    
    // Create cache directories
    await this.ensureCacheDirectories();
    
    // Clean up invalid cache entries
    await this.cleanupInvalidCache();
    
    console.log('✅ Enhanced Media Cache Service initialized');
  }

  /**
   * Cache media file
   */
  async cacheMedia(
    messageId: string,
    chatId: string,
    mediaUrl: string,
    mediaType: CachedMedia['mediaType'],
    fileName?: string
  ): Promise<CachedMedia | null> {
    try {
      if (!networkStateManager.isOnline()) {
        console.log('📵 Cannot cache media - offline');
        return null;
      }

      const cacheId = `media_${messageId}_${Date.now()}`;
      const fileExtension = this.getFileExtension(fileName || mediaUrl);
      const localFileName = `${cacheId}.${fileExtension}`;
      const localPath = `${this.CACHE_DIR}${localFileName}`;

      console.log(`📥 Caching media: ${fileName || 'unknown'}`);

      // Download the file
      const downloadResult = await FileSystem.downloadAsync(mediaUrl, localPath);
      
      if (downloadResult.status !== 200) {
        throw new Error(`Download failed with status ${downloadResult.status}`);
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(localPath);
      const fileSize = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;

      // Generate thumbnail for images and videos
      let thumbnailPath: string | undefined;
      let width: number | undefined;
      let height: number | undefined;

      if (mediaType === 'image') {
        const thumbnailResult = await this.generateImageThumbnail(localPath);
        thumbnailPath = thumbnailResult.thumbnailPath;
        width = thumbnailResult.width;
        height = thumbnailResult.height;
      } else if (mediaType === 'video') {
        thumbnailPath = await this.generateVideoThumbnail(localPath);
      }

      // Save to database
      const cachedMedia: CachedMedia = {
        id: cacheId,
        messageId,
        chatId,
        mediaUrl,
        localPath,
        thumbnailPath,
        mediaType,
        fileName,
        fileSize,
        width,
        height,
        downloadedAt: Date.now(),
        lastAccessed: Date.now(),
        isValid: true,
      };

      await this.saveCachedMediaToDatabase(cachedMedia);

      console.log(`✅ Media cached successfully: ${fileName || 'unknown'}`);
      return cachedMedia;
    } catch (error) {
      console.error('❌ Failed to cache media:', error);
      return null;
    }
  }

  /**
   * Get cached media
   */
  async getCachedMedia(messageId: string): Promise<CachedMedia | null> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const results = await db.getAllAsync(`
        SELECT * FROM cached_media WHERE messageId = ? AND isValid = 1
      `, [messageId]);

      if (results.length === 0) {
        return null;
      }

      const media = this.mapRowToCachedMedia(results[0]);
      
      // Verify file still exists
      if (media.localPath) {
        const fileInfo = await FileSystem.getInfoAsync(media.localPath);
        if (!fileInfo.exists) {
          await this.invalidateCachedMedia(media.id);
          return null;
        }
      }

      // Update last accessed time
      await this.updateLastAccessed(media.id);

      return media;
    } catch (error) {
      console.error('❌ Failed to get cached media:', error);
      return null;
    }
  }

  /**
   * Cache avatar with versioning
   */
  async cacheAvatar(
    userId: string,
    avatarUrl: string,
    size: number = 150,
    version?: string
  ): Promise<CachedAvatar | null> {
    try {
      if (!networkStateManager.isOnline()) {
        console.log('📵 Cannot cache avatar - offline');
        return null;
      }

      // Check if we already have this version
      const existingAvatar = await this.getCachedAvatar(userId);
      if (existingAvatar && existingAvatar.version === version && existingAvatar.isValid) {
        console.log(`✅ Avatar already cached with version ${version}`);
        return existingAvatar;
      }

      const fileName = `avatar_${userId}_${size}_${Date.now()}.jpg`;
      const localPath = `${this.AVATAR_CACHE_DIR}${fileName}`;

      console.log(`👤 Caching avatar for user ${userId}`);

      // Download the avatar
      const downloadResult = await FileSystem.downloadAsync(avatarUrl, localPath);
      
      if (downloadResult.status !== 200) {
        throw new Error(`Avatar download failed with status ${downloadResult.status}`);
      }

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(localPath);
      const fileSize = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;

      // Save to database
      const cachedAvatar: CachedAvatar = {
        userId,
        url: avatarUrl,
        localPath,
        timestamp: Date.now(),
        size,
        version,
        lastChecked: Date.now(),
        downloadedAt: Date.now(),
        fileSize,
        isValid: true,
      };

      await this.saveCachedAvatarToDatabase(cachedAvatar);

      // Remove old avatar if exists
      if (existingAvatar && existingAvatar.localPath) {
        await FileSystem.deleteAsync(existingAvatar.localPath, { idempotent: true });
      }

      console.log(`✅ Avatar cached successfully for user ${userId}`);
      return cachedAvatar;
    } catch (error) {
      console.error('❌ Failed to cache avatar:', error);
      return null;
    }
  }

  /**
   * Get cached avatar
   */
  async getCachedAvatar(userId: string): Promise<CachedAvatar | null> {
    try {
      const db = offlineDatabaseService.getDatabase();
      const results = await db.getAllAsync(`
        SELECT * FROM cached_avatars WHERE userId = ? AND isValid = 1
      `, [userId]);

      if (results.length === 0) {
        return null;
      }

      const avatar = this.mapRowToCachedAvatar(results[0]);
      
      // Verify file still exists
      if (avatar.localPath) {
        const fileInfo = await FileSystem.getInfoAsync(avatar.localPath);
        if (!fileInfo.exists) {
          await this.invalidateCachedAvatar(userId);
          return null;
        }
      }

      return avatar;
    } catch (error) {
      console.error('❌ Failed to get cached avatar:', error);
      return null;
    }
  }

  /**
   * Generate image thumbnail
   */
  private async generateImageThumbnail(imagePath: string): Promise<{
    thumbnailPath: string;
    width: number;
    height: number;
  }> {
    try {
      const result = await ImageManipulator.manipulateAsync(
        imagePath,
        [{ resize: { width: this.THUMBNAIL_SIZE } }],
        { compress: 0.7, format: ImageManipulator.SaveFormat.JPEG }
      );

      const thumbnailFileName = `thumb_${Date.now()}.jpg`;
      const thumbnailPath = `${this.THUMBNAIL_CACHE_DIR}${thumbnailFileName}`;
      
      // Move the thumbnail to our cache directory
      await FileSystem.moveAsync({
        from: result.uri,
        to: thumbnailPath,
      });

      return {
        thumbnailPath,
        width: result.width,
        height: result.height,
      };
    } catch (error) {
      console.error('❌ Failed to generate image thumbnail:', error);
      throw error;
    }
  }

  /**
   * Generate video thumbnail (placeholder implementation)
   */
  private async generateVideoThumbnail(videoPath: string): Promise<string> {
    // This would require a video processing library like expo-av
    // For now, return a placeholder
    console.log(`🎥 Video thumbnail generation not implemented yet for: ${videoPath}`);
    return '';
  }

  /**
   * Ensure cache directories exist
   */
  private async ensureCacheDirectories(): Promise<void> {
    const directories = [this.CACHE_DIR, this.AVATAR_CACHE_DIR, this.THUMBNAIL_CACHE_DIR];
    
    for (const dir of directories) {
      const dirInfo = await FileSystem.getInfoAsync(dir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(dir, { intermediates: true });
      }
    }
  }

  /**
   * Clean up invalid cache entries and enforce size limits
   */
  private async cleanupInvalidCache(): Promise<void> {
    try {
      console.log('🧹 Cleaning up invalid cache entries...');

      // Check media cache
      const db = offlineDatabaseService.getDatabase();
      const mediaResults = await db.getAllAsync(`
        SELECT * FROM cached_media WHERE isValid = 1
      `);

      let totalCacheSize = 0;
      const validMedia: any[] = [];

      for (const row of mediaResults) {
        const media = this.mapRowToCachedMedia(row);
        if (media.localPath) {
          const fileInfo = await FileSystem.getInfoAsync(media.localPath);
          if (!fileInfo.exists) {
            await this.invalidateCachedMedia(media.id);
          } else {
            const fileSize = fileInfo.exists && 'size' in fileInfo ? fileInfo.size : 0;
            totalCacheSize += fileSize;
            validMedia.push({ ...media, fileSize });
          }
        }
      }

      // Enforce cache size limit
      if (totalCacheSize > this.MAX_CACHE_SIZE) {
        console.log(`📦 Cache size (${totalCacheSize / 1024 / 1024}MB) exceeds limit, cleaning up...`);

        // Sort by last accessed time (oldest first)
        validMedia.sort((a, b) => (a.lastAccessed || 0) - (b.lastAccessed || 0));

        let sizeToRemove = totalCacheSize - this.MAX_CACHE_SIZE;
        for (const media of validMedia) {
          if (sizeToRemove <= 0) break;

          await this.invalidateCachedMedia(media.id);
          if (media.localPath) {
            await FileSystem.deleteAsync(media.localPath, { idempotent: true });
          }
          sizeToRemove -= media.fileSize || 0;
        }
      }

      // Check avatar cache
      const avatarResults = await db.getAllAsync(`
        SELECT * FROM cached_avatars WHERE isValid = 1
      `);

      for (const row of avatarResults) {
        const avatar = this.mapRowToCachedAvatar(row);
        if (avatar.localPath) {
          const fileInfo = await FileSystem.getInfoAsync(avatar.localPath);
          if (!fileInfo.exists) {
            await this.invalidateCachedAvatar(avatar.userId);
          }
        }
      }

      console.log('✅ Cache cleanup completed');
    } catch (error) {
      console.error('❌ Failed to cleanup cache:', error);
    }
  }

  /**
   * Save cached media to database
   */
  private async saveCachedMediaToDatabase(media: CachedMedia): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      INSERT OR REPLACE INTO cached_media (
        id, messageId, chatId, mediaUrl, localPath, thumbnailPath, mediaType,
        fileName, fileSize, width, height, duration, downloadedAt, lastAccessed,
        isValid, version
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      media.id, media.messageId, media.chatId, media.mediaUrl, media.localPath || null,
      media.thumbnailPath || null, media.mediaType, media.fileName || null, media.fileSize || null,
      media.width || null, media.height || null, media.duration || null, media.downloadedAt || null,
      media.lastAccessed || null, media.isValid ? 1 : 0, media.version || null
    ]);
  }

  /**
   * Save cached avatar to database
   */
  private async saveCachedAvatarToDatabase(avatar: CachedAvatar): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      INSERT OR REPLACE INTO cached_avatars (
        userId, url, localPath, timestamp, size, version, lastChecked,
        downloadedAt, fileSize, isValid
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      avatar.userId, avatar.url, avatar.localPath || null, avatar.timestamp, avatar.size,
      avatar.version || null, avatar.lastChecked || null, avatar.downloadedAt || null, avatar.fileSize || null,
      avatar.isValid ? 1 : 0
    ]);
  }

  /**
   * Invalidate cached media
   */
  private async invalidateCachedMedia(mediaId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE cached_media SET isValid = 0 WHERE id = ?
    `, [mediaId]);
  }

  /**
   * Invalidate cached avatar
   */
  private async invalidateCachedAvatar(userId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE cached_avatars SET isValid = 0 WHERE userId = ?
    `, [userId]);
  }

  /**
   * Update last accessed time
   */
  private async updateLastAccessed(mediaId: string): Promise<void> {
    const db = offlineDatabaseService.getDatabase();
    await db.runAsync(`
      UPDATE cached_media SET lastAccessed = ? WHERE id = ?
    `, [Date.now(), mediaId]);
  }

  /**
   * Get file extension from filename or URL
   */
  private getFileExtension(fileNameOrUrl: string): string {
    const parts = fileNameOrUrl.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : 'bin';
  }

  /**
   * Map database row to CachedMedia
   */
  private mapRowToCachedMedia(row: any): CachedMedia {
    return {
      id: row.id,
      messageId: row.messageId,
      chatId: row.chatId,
      mediaUrl: row.mediaUrl,
      localPath: row.localPath,
      thumbnailPath: row.thumbnailPath,
      mediaType: row.mediaType,
      fileName: row.fileName,
      fileSize: row.fileSize,
      width: row.width,
      height: row.height,
      duration: row.duration,
      downloadedAt: row.downloadedAt,
      lastAccessed: row.lastAccessed,
      isValid: row.isValid === 1,
      version: row.version,
    };
  }

  /**
   * Map database row to CachedAvatar
   */
  private mapRowToCachedAvatar(row: any): CachedAvatar {
    return {
      userId: row.userId,
      url: row.url,
      localPath: row.localPath,
      timestamp: row.timestamp,
      size: row.size,
      version: row.version,
      lastChecked: row.lastChecked,
      downloadedAt: row.downloadedAt,
      fileSize: row.fileSize,
      isValid: row.isValid === 1,
    };
  }
}

export const enhancedMediaCacheService = new EnhancedMediaCacheService();
