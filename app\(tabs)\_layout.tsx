import { Ionicons } from "@expo/vector-icons";
import { Tabs, usePathname, useSegments } from "expo-router";
import React from "react";
import { Keyboard, Platform, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import ErrorBoundary from "../../src/components/ErrorBoundary";

import TabBarBackground from "../../src/components/ui/TabBarBackground";
import {
  headerSizes,
  isVerySmallDevice,
  tabBarSizes,
} from "../../src/utils/responsive";

export default function TabLayout() {
  // Get safe area insets
  const insets = useSafeAreaInsets();

  // Get current pathname and segments to detect profile page
  const pathname = usePathname();
  const segments = useSegments();
  const isProfilePage = pathname.includes('profile') || segments.includes('profile');

  // Debug log to see the actual pathname and segments
  React.useEffect(() => {
    console.log('🔍 Current pathname:', pathname);
    console.log('🔍 Current segments:', segments);
    console.log('🔍 Is profile page:', isProfilePage);
    console.log('🔍 Tab Layout: Rendering 4 tabs - Chats, Groups, Business, Stories (Calls hidden for future versions)');
  }, [pathname, segments, isProfilePage]);

  // Get keyboard visibility for tab bar
  const [shouldHideTabBar, setShouldHideTabBar] = React.useState(false);
  const [shouldHideOnScroll, setShouldHideOnScroll] = React.useState(false);

  // Listen to scroll-based tab bar hiding
  React.useEffect(() => {
    const checkScrollHiding = () => {
      setShouldHideOnScroll((global as any).hideTabBarOnScroll || false);
    };

    const interval = setInterval(checkScrollHiding, 100);
    return () => clearInterval(interval);
  }, []);

  // Listen to keyboard events
  React.useEffect(() => {
    const keyboardWillShow = () => {
      // Don't hide tab bar if business modal is open
      if (!(global as any).businessModalOpen) {
        setShouldHideTabBar(true);
      }
    };
    const keyboardWillHide = () => {
      // Don't show tab bar if business modal is open
      if (!(global as any).businessModalOpen) {
        setShouldHideTabBar(false);
      }
    };
    const keyboardDidShow = () => {
      // Don't hide tab bar if business modal is open
      if (!(global as any).businessModalOpen) {
        setShouldHideTabBar(true);
      }
    };
    const keyboardDidHide = () => {
      // Don't show tab bar if business modal is open
      if (!(global as any).businessModalOpen) {
        setShouldHideTabBar(false);
      }
    };

    const showEvent = Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow';
    const hideEvent = Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide';

    const showListener = Keyboard.addListener(showEvent, keyboardWillShow);
    const hideListener = Keyboard.addListener(hideEvent, keyboardWillHide);
    const didShowListener = Keyboard.addListener('keyboardDidShow', keyboardDidShow);
    const didHideListener = Keyboard.addListener('keyboardDidHide', keyboardDidHide);

    return () => {
      showListener?.remove();
      hideListener?.remove();
      didShowListener?.remove();
      didHideListener?.remove();
    };
  }, []);

  // Get responsive sizes
  const tabHeight = tabBarSizes.height;
  const tabFontSize = tabBarSizes.fontSize;
  const tabIconSize = tabBarSizes.iconSize;
  const headerHeight = headerSizes.height;
  const headerFontSize = headerSizes.fontSize;
  const headerIconSize = headerSizes.iconSize;

  return (
    <ErrorBoundary>
        <View
          style={{ flex: 1 }}
          accessible={true}
          accessibilityRole="tablist"
          accessibilityLabel="Main navigation"
        >
        <Tabs
        screenOptions={{
          // OPTIMIZED FOR PERFORMANCE - Reduced animations and faster loading
          tabBarActiveTintColor: "#1DA1F2", // Blue highlighting for active tab
          tabBarInactiveTintColor: "#000000", // Pure black for inactive tabs
          lazy: false, // Disable lazy loading for faster tab switching
          // animationEnabled is not a valid option for bottom tabs
          tabBarStyle: Platform.select({
            ios: {
              backgroundColor: "#FFFFFF", // White background for black text
              borderTopWidth: 0,
              paddingBottom: Math.max(
                insets.bottom + 8,
                tabBarSizes.paddingBottom + 8,
              ), // Increased padding
              height:
                tabHeight +
                Math.max(insets.bottom + 8, tabBarSizes.paddingBottom + 8),
              position: "absolute",
              // Hide tab bar when keyboard is visible, on profile page, or when scrolling
              display: shouldHideTabBar || isProfilePage || shouldHideOnScroll ? 'none' : 'flex',
            },
            default: {
              backgroundColor: "#FFFFFF", // White background for black text
              borderTopWidth: 1,
              borderTopColor: "#333333",
              paddingBottom: Math.max(
                insets.bottom + 8,
                tabBarSizes.paddingBottom + 8,
              ), // Increased padding
              height: tabHeight + Math.max(insets.bottom + 8, 16), // Increased height
              // Hide tab bar when keyboard is visible, on profile page, or when scrolling
              display: shouldHideTabBar || isProfilePage || shouldHideOnScroll ? 'none' : 'flex',
            },
          }),
          tabBarBackground: () => <TabBarBackground />,
          tabBarLabelStyle: {
            fontSize: tabFontSize,
            fontWeight: "600",
            marginBottom: isVerySmallDevice() ? 2 : 4,
          },
          tabBarIconStyle: {
            marginTop: isVerySmallDevice() ? 2 : 4,
          },
          tabBarItemStyle: {
            borderRadius: 12,
            marginHorizontal: 4,
            paddingVertical: 4,
          },
          // Enhanced animations handled by SwipeableTabWrapper
          headerStyle: {
            backgroundColor: "#000000", // Dark background
            elevation: 4,
            shadowOpacity: 0.3,
            height: headerHeight + insets.top,
            borderBottomWidth: 1,
            borderBottomColor: "rgba(255, 255, 255, 0.1)",
          },
          headerTitleContainerStyle: {
            paddingTop: insets.top, // Move paddingTop here to avoid warning
            justifyContent: "center", // Vertically center the title
            alignItems: "center", // Horizontally center the title
          },
          headerTintColor: "#FFFFFF",
          headerTitleStyle: {
            fontWeight: "bold",
            fontSize: headerFontSize,
            textAlign: "center", // Center text alignment
          },
          headerBackTitleStyle: {
            fontSize: headerIconSize, // Use headerIconSize for back button text
          },
          tabBarAccessibilityLabel: "Main navigation tabs",
          // tabBarAccessibilityRole: 'tablist', // Remove unsupported property
        }}
      >
        {/* Chats Tab */}
        <Tabs.Screen
          name="index"
          options={{
            title: "Chats",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="chatbubbles"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Chats tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Chats tab",
            headerShown: false, // Hide default header since we use custom MainHeader
          }}
        />

        {/* Groups Tab */}
        <Tabs.Screen
          name="groups"
          options={{
            title: "Groups",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="people"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Groups tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Groups tab",
            headerShown: false, // Hide default header since we use custom GroupsHeader
          }}
        />

        {/* Business Tab */}
        <Tabs.Screen
          name="business"
          options={{
            title: "Business",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="storefront"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Business tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Business tab",
            headerShown: false, // Hide default header since business page has its own
          }}
        />

        {/* Calls Tab - Hidden for future app versions */}
        <Tabs.Screen
          name="calls"
          options={{
            href: null, // Hide from tab bar - will be available in future versions
            title: "Calls",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="call"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Calls tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Calls tab",
            headerShown: false, // Hide default header for consistency
          }}
        />

        {/* Stories Tab */}
        <Tabs.Screen
          name="updates"
          options={{
            title: "Stories",
            tabBarIcon: ({ color }) => (
              <Ionicons
                name="radio-button-on"
                size={tabIconSize}
                color={color}
                accessibilityLabel="Stories tab icon"
              />
            ),
            tabBarAccessibilityLabel: "Stories tab",
            headerShown: false, // Hide default header since we use custom MainHeader
          }}
        />



        {/* Profile and Settings are handled in overlay menu - no tabs needed */}
        <Tabs.Screen
          name="profile"
          options={{
            href: null, // Hide from tab bar - accessible via overlay menu
            headerShown: false,
            tabBarStyle: { display: 'none' }, // Hide tab bar on profile page
          }}
        />

        <Tabs.Screen
          name="settings"
          options={{
            href: null, // Hide from tab bar - accessible via overlay menu
            headerShown: false,
            tabBarStyle: { display: 'none' }, // Completely hide tab bar on settings page
          }}
        />
        </Tabs>
        </View>
    </ErrorBoundary>
  );
}
