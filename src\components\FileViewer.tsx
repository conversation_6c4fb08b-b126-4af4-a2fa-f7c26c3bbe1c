// 📄 FILE VIEWER WITH DEVICE READERS
// Opens files with appropriate device applications

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Linking,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

// Optional import - may not be available in all environments
let IntentLauncher: any = null;
try {
  IntentLauncher = require('expo-intent-launcher');
} catch (error) {
  console.warn('expo-intent-launcher not available:', error instanceof Error ? error.message : 'Unknown error');
}

interface FileViewerProps {
  fileUri: string;
  fileName: string;
  fileSize?: number;
  mimeType?: string;
  isOwnMessage: boolean;
  theme: any;
}

export const FileViewer: React.FC<FileViewerProps> = ({
  fileUri,
  fileName,
  fileSize,
  mimeType,
  isOwnMessage,
  theme,
}) => {
  // Format file size
  const formatFileSize = (bytes?: number) => {
    if (!bytes || bytes === 0) return 'Unknown size';

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = bytes / Math.pow(1024, i);

    // Show more precision for smaller files
    const precision = i <= 1 ? 0 : 1; // No decimals for Bytes and KB, 1 decimal for MB+
    return `${size.toFixed(precision)} ${sizes[i]}`;
  };

  // Get file icon based on extension or mime type
  const getFileIcon = () => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    const mime = mimeType?.toLowerCase();

    if (mime?.includes('pdf') || extension === 'pdf') return 'document-text';
    if (mime?.includes('image') || ['jpg', 'jpeg', 'png', 'gif'].includes(extension || '')) return 'image';
    if (mime?.includes('video') || ['mp4', 'avi', 'mov'].includes(extension || '')) return 'videocam';
    if (mime?.includes('audio') || ['mp3', 'wav', 'aac'].includes(extension || '')) return 'musical-notes';
    if (mime?.includes('text') || ['txt', 'doc', 'docx'].includes(extension || '')) return 'document-text';
    if (mime?.includes('spreadsheet') || ['xls', 'xlsx', 'csv'].includes(extension || '')) return 'grid';
    if (mime?.includes('presentation') || ['ppt', 'pptx'].includes(extension || '')) return 'easel';
    if (['zip', 'rar', '7z'].includes(extension || '')) return 'archive';
    
    return 'document';
  };

  // Open file with device application
  const openFile = async () => {
    try {
      // First, try to share the file which will show available apps
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: mimeType,
          dialogTitle: `Open ${fileName}`,
        });
      } else if (Platform.OS === 'android' && mimeType && IntentLauncher) {
        // On Android, use IntentLauncher to open with specific app
        try {
          await IntentLauncher.startActivityAsync('android.intent.action.VIEW', {
            data: fileUri,
            type: mimeType,
            flags: 1, // FLAG_GRANT_READ_URI_PERMISSION
          });
        } catch (intentError) {
          console.warn('IntentLauncher failed, falling back to sharing:', intentError);
          // Fallback to sharing
          await Sharing.shareAsync(fileUri);
        }
      } else {
        // Fallback: try to open with system
        const canOpen = await Linking.canOpenURL(fileUri);
        if (canOpen) {
          await Linking.openURL(fileUri);
        } else {
          Alert.alert(
            'Cannot Open File',
            'No application found to open this file type. The file has been saved to your device.',
            [{ text: 'OK' }]
          );
        }
      }
    } catch (error) {
      console.error('❌ Error opening file:', error);
      Alert.alert(
        'Error',
        'Could not open file. You can try sharing it to another app.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Share', onPress: () => shareFile() },
        ]
      );
    }
  };

  // Share file to other apps
  const shareFile = async () => {
    try {
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: mimeType,
          dialogTitle: `Share ${fileName}`,
        });
      } else {
        Alert.alert('Sharing not available', 'File sharing is not available on this device.');
      }
    } catch (error) {
      console.error('❌ Error sharing file:', error);
      Alert.alert('Error', 'Could not share file.');
    }
  };

  // Download/save file
  const downloadFile = async () => {
    try {
      const downloadDir = FileSystem.documentDirectory + 'Downloads/';
      
      // Ensure downloads directory exists
      const dirInfo = await FileSystem.getInfoAsync(downloadDir);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(downloadDir, { intermediates: true });
      }

      const localUri = downloadDir + fileName;
      
      // Copy file to downloads
      await FileSystem.copyAsync({
        from: fileUri,
        to: localUri,
      });

      Alert.alert(
        'File Downloaded',
        `${fileName} has been saved to Downloads folder.`,
        [
          { text: 'OK' },
          { text: 'Open', onPress: () => openFile() },
        ]
      );
    } catch (error) {
      console.error('❌ Error downloading file:', error);
      Alert.alert('Error', 'Could not download file.');
    }
  };

  return (
    <TouchableOpacity 
      style={[
        styles.fileContainer,
        { backgroundColor: isOwnMessage ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)' }
      ]}
      onPress={openFile}
      activeOpacity={0.7}
    >
      {/* File Icon */}
      <View style={[styles.fileIcon, { backgroundColor: theme.sendButtonActive }]}>
        <Ionicons name={getFileIcon()} size={24} color="white" />
      </View>

      {/* File Info */}
      <View style={styles.fileInfo}>
        <Text 
          style={[
            styles.fileName, 
            { color: isOwnMessage ? theme.ownMessageText : theme.otherMessageText }
          ]}
          numberOfLines={2}
        >
          {fileName}
        </Text>
        <Text style={[styles.fileSize, { color: theme.timeText }]}>
          {formatFileSize(fileSize)}
        </Text>
      </View>

      {/* Action Buttons */}
      <View style={styles.fileActions}>
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={(e) => {
            e.stopPropagation();
            shareFile();
          }}
        >
          <Ionicons name="share-outline" size={20} color={theme.timeText} />
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionButton}
          onPress={(e) => {
            e.stopPropagation();
            downloadFile();
          }}
        >
          <Ionicons name="download-outline" size={20} color={theme.timeText} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  fileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 12,
    marginVertical: 2,
    minWidth: 250,
  },
  fileIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  fileInfo: {
    flex: 1,
    marginRight: 8,
  },
  fileName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  fileSize: {
    fontSize: 12,
  },
  fileActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 8,
    marginLeft: 4,
  },
});

export default FileViewer;
