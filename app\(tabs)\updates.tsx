// 📱 REAL UPDATES TAB - Fully functional social media
// Real camera capture, media upload, story posting, and social interactions

import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from 'expo-image-picker';
// import * as Haptics from 'expo-haptics'; // Removed - no haptic feedback needed
import * as Clipboard from 'expo-clipboard';
// import * as ScreenOrientation from 'expo-screen-orientation'; // Dynamic import used instead
import { useRouter } from "expo-router";
import React, { useCallback, useEffect, useState, useRef } from "react";
import { AppState } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import {
  ActivityIndicator,
  Alert,
  Animated,
  Dimensions,
  FlatList,
  Image,
  KeyboardAvoidingView,
  Modal,
  Platform,
  RefreshControl,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
  Share,
  BackHandler,
} from "react-native";
// Removed gesture handler imports - pure video feed only
import { VideoView, useVideoPlayer } from 'expo-video';
import { useSelector } from "react-redux";
import { RootState } from "../../src/redux/store";
import { realUpdatesService, RealUpdate, UpdateType } from "../../src/services/realUpdatesService";
import { localUpdatesStorage } from "../../src/services/localUpdatesStorage";
import { updatesSyncService } from "../../src/services/updatesSyncService";
import { offlineLikesService } from "../../src/services/offlineLikesService";
import { mediaDownloadService } from "../../src/services/mediaDownloadService";
import { updateInteractionsService } from "../../src/services/updateInteractionsService";
import { TikTokProgressBar } from "../../components/TikTokProgressBar";
import { videoProgressManager } from "../../utils/VideoProgressManager";
import { repostService } from "../../src/services/repostService";
import { Update } from "../../src/types/Update";
import { recentMediaService, RecentMediaItem } from "../../src/services/recentMediaService";

import { LayoutPickerModal } from "../../src/components/LayoutPickerModal";
import { LayoutTemplate } from "../../src/services/layoutPickerService";
import { VoiceRecorderModal } from "../../src/components/VoiceRecorderModal";
import { VoiceRecording } from "../../src/services/voiceRecorderService";
import { EnhancedPhotoCropper } from "../../src/components/media/EnhancedPhotoCropper";
import { EnhancedCaptionModal } from "../../src/components/EnhancedCaptionModal";
import { enhancedFirebaseUploadService, UploadProgress } from "../../src/services/enhancedFirebaseUploadService";
import { errorHandlingService } from "../../src/services/errorHandlingService";
import { formatTimeAgo } from "../../src/utils/dateUtils";
import { UpdatesInteractionPages } from "../../src/components/UpdatesInteractionPages";
import { UpdatesCommentsPage } from "../../src/components/UpdatesCommentsPage";
import NetInfo from '@react-native-community/netinfo';
import { DeviceInfo } from "../../src/utils/responsiveUtils";
import { auth } from "../../src/services/firebaseSimple";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from "../../src/services/firebaseSimple";
import { collection, query, where, orderBy, limit, getDocs } from 'firebase/firestore';
import { EnhancedVideoTrimmer } from "../../src/components/media/EnhancedVideoTrimmer";
import { PhotoCropper } from "../../src/components/media/PhotoCropper";
import { TikTokStyleMediaPicker } from "../../src/components/media/TikTokStyleMediaPicker";
import { offlineCommentsService } from "../../src/services/offlineCommentsService";
import { TikTokStyleCamera } from "../../src/components/media/TikTokStyleCamera";
import { MusicPickerModal } from "../../src/components/modals/MusicPickerModal";
import { LocalMusicTrack, WebMusicTrack } from "../../src/services/musicService";
import { MediaPreviewEditor } from "../../src/components/media/MediaPreviewEditor";
import { TextUpdateCreator } from "../../src/components/media/TextUpdateCreator";
import { AudioCaption, TextOverlay } from "../../src/types/Update";
import { AudioCaptionPlayer } from "../../src/components/media/AudioCaptionPlayer";
import { TextOverlayRenderer } from "../../src/components/media/TextOverlayRenderer";
import { audioCaptionService } from "../../src/services/audioCaptionService";

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// REMOVED: Viewability config - now using scroll-based detection

// GLOBAL VIDEO PLAYER MANAGER - Singleton pattern to prevent re-initialization
class VideoPlayerManager {
  private static instance: VideoPlayerManager;
  private players = new Map<string, any>();
  private playerRefs = new Map<string, React.RefObject<any>>();
  private initializingPlayers = new Set<string>();

  static getInstance(): VideoPlayerManager {
    if (!VideoPlayerManager.instance) {
      VideoPlayerManager.instance = new VideoPlayerManager();
    }
    return VideoPlayerManager.instance;
  }

  getPlayer(videoId: string): any | null {
    return this.players.get(videoId) || null;
  }

  hasPlayer(videoId: string): boolean {
    return this.players.has(videoId);
  }

  isInitializing(videoId: string): boolean {
    return this.initializingPlayers.has(videoId);
  }

  setPlayer(videoId: string, player: any): void {
    // Logging disabled to prevent spam
    this.players.set(videoId, player);
    this.initializingPlayers.delete(videoId);
  }

  markAsInitializing(videoId: string): void {
    this.initializingPlayers.add(videoId);
  }

  removePlayer(videoId: string): void {
    // Logging disabled to prevent spam
    const player = this.players.get(videoId);
    if (player) {
      try {
        if (player && typeof player.pause === 'function') {
          player.pause();
        }
      } catch (error) {
        // Player might already be released
      }
    }
    this.players.delete(videoId);
    this.playerRefs.delete(videoId);
    this.initializingPlayers.delete(videoId);
  }

  pauseAllPlayers(): void {
    const playersToRemove: string[] = [];
    this.players.forEach((player, videoId) => {
      try {
        // Additional validation before pausing
        if (player && typeof player.pause === 'function') {
          // Check if player is still valid by testing a property
          if (typeof player.playing !== 'undefined') {
            player.pause();
          } else {
            playersToRemove.push(videoId);
          }
        } else {
          playersToRemove.push(videoId);
        }
      } catch (error) {
        // Player is likely released, mark for removal
        playersToRemove.push(videoId);
      }
    });

    // Remove invalid players
    playersToRemove.forEach(videoId => {
      this.players.delete(videoId);
      this.playerRefs.delete(videoId);
      this.initializingPlayers.delete(videoId);
    });
  }

  cleanupReleasedPlayers(): void {
    const playersToRemove: string[] = [];
    this.players.forEach((player, videoId) => {
      try {
        if (!player || typeof player.playing === 'undefined') {
          playersToRemove.push(videoId);
        }
      } catch (error) {
        playersToRemove.push(videoId);
      }
    });

    playersToRemove.forEach(videoId => {
      this.players.delete(videoId);
      this.playerRefs.delete(videoId);
      this.initializingPlayers.delete(videoId);
    });
  }

  getAllPlayers(): Map<string, any> {
    return this.players;
  }
}

// Global instance
const videoPlayerManager = VideoPlayerManager.getInstance();

// Stable Video Player Component with proper lifecycle management - MOVED OUTSIDE COMPONENT
interface VideoPlayerProps {
  source: { uri: string };
  style: any;
  isLooping?: boolean;
  videoId: string;
  globalVideoPause: boolean;
  currentlyPlayingVideo: string | null;
  currentVideoMuted: boolean;
  currentUserId?: string;
  onLongPress?: () => void;
  onTap?: () => void;
}

const VideoPlayer = React.memo(function VideoPlayer({
  source,
  style,
  isLooping = true,
  videoId,
  globalVideoPause,
  currentlyPlayingVideo,
  currentVideoMuted,
  currentUserId,
  onLongPress,
  onTap,
}: VideoPlayerProps) {
  const configuredRef = useRef(false);
  const mountedRef = useRef(true);

  // Always call useVideoPlayer hook (React rule)
  const player = useVideoPlayer(source.uri);

  // Configure player only once and register with progress manager - STABLE VERSION
  useEffect(() => {
    if (player && !configuredRef.current && mountedRef.current) {
      try {
        player.loop = isLooping;
        player.muted = false;
        configuredRef.current = true;

        // Register player with high-performance progress manager ONLY ONCE
        videoProgressManager.registerPlayer(videoId, player);

        // Logging disabled to prevent spam
      } catch (error) {
        console.warn('Error configuring player:', error);
      }
    }
  }, [player, isLooping, videoId]); // Include all dependencies

  // Cleanup on unmount - STABLE VERSION
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
      configuredRef.current = false;
      // Unregister from progress manager
      videoProgressManager.unregisterPlayer(videoId);
    };
  }, [videoId]); // Include videoId dependency

  // Add debouncing to prevent infinite loops
  const lastActionRef = useRef<number>(0);
  const lastStateRef = useRef<string>('');

  // STABLE playback control - prevent registration cycles
  useEffect(() => {
    if (!player || !mountedRef.current || !configuredRef.current) return;

    const shouldPlay = !globalVideoPause && currentlyPlayingVideo === videoId;
    const currentState = `${shouldPlay}-${currentVideoMuted}`;

    // If globally paused, pause immediately without debouncing
    if (globalVideoPause) {
      try {
        if (player.playing) {
          player.pause();
          console.log('🛑 Global pause - Video paused:', videoId);
        }
      } catch (error) {
        console.warn('Error pausing video globally:', error);
      }
      return;
    }

    // AGGRESSIVE debouncing to prevent rapid state changes
    const now = Date.now();
    if (now - lastActionRef.current < 500 || lastStateRef.current === currentState) {
      return;
    }

    lastActionRef.current = now;
    lastStateRef.current = currentState;

    try {
      // Validate player is still valid before using
      if (typeof player.play !== 'function' || typeof player.pause !== 'function') {
        console.warn('Invalid player object for:', videoId);
        return;
      }

      if (shouldPlay) {
        // Only set mute if it's different to avoid overriding immediate mute changes
        if (player.muted !== currentVideoMuted) {
          player.muted = currentVideoMuted;
        }
        if (!player.playing) {
          player.play();
          // Logging disabled to prevent spam

          // Track view in Firebase
          if (currentUserId) {
            updateInteractionsService.viewUpdate(videoId, currentUserId, 0);
          }
        }
      } else {
        if (player.playing) {
          player.pause();
          // Logging disabled to prevent spam
        }
      }
    } catch (error) {
      console.warn('Error controlling video playback for', videoId, ':', error);
    }
  }, [player, globalVideoPause, currentlyPlayingVideo, currentVideoMuted, videoId, currentUserId]);

  // Handle tap controls - Show progress bar
  const handleVideoTap = () => {
    console.log('🎯 TikTok Progress: Video tapped - showing progress bar for', videoId);
    onTap?.();
  };

  // Don't render if player is invalid
  if (!player) {
    return (
      <View style={style}>
        <View style={[style, { backgroundColor: '#000', justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={{ color: '#fff' }}>Loading video...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={style}>
      <VideoView
        key={`video-${videoId}-${source.uri}`}
        style={style}
        player={player}
        allowsFullscreen={false}
        allowsPictureInPicture={false}
        contentFit="contain"
        nativeControls={false}
      />

      {/* Invisible tap overlay for better touch detection */}
      <TouchableOpacity
        style={styles.videoTapOverlay}
        onPress={handleVideoTap}
        onLongPress={() => {
          console.log('🔥🔥🔥 VIDEO PLAYER LONG PRESS TRIGGERED:', videoId);
          onLongPress?.();
        }}
        activeOpacity={1}
      />

      {/* Removed pause overlay - using FAB instead */}

      {/* Mute indicator moved to floating header */}
    </View>
  );
}, (prevProps, nextProps) => {
  // Only re-render if essential props change
  return (
    prevProps.videoId === nextProps.videoId &&
    prevProps.source.uri === nextProps.source.uri &&
    prevProps.isLooping === nextProps.isLooping &&
    prevProps.globalVideoPause === nextProps.globalVideoPause &&
    prevProps.currentlyPlayingVideo === nextProps.currentlyPlayingVideo &&
    prevProps.currentVideoMuted === nextProps.currentVideoMuted &&
    prevProps.currentUserId === nextProps.currentUserId &&
    JSON.stringify(prevProps.style) === JSON.stringify(nextProps.style)
  );
});

// Full-Screen Radiating Flowers Animation Component - MOVED OUTSIDE COMPONENT
interface FullScreenRadiatingFlowersProps {
  visible: boolean;
  onComplete: () => void;
}

const FullScreenRadiatingFlowers = React.memo(function FullScreenRadiatingFlowers({
  visible,
  onComplete
}: FullScreenRadiatingFlowersProps) {
  const flowers = useRef(Array.from({ length: 15 }, () => ({
    scale: new Animated.Value(0),
    opacity: new Animated.Value(1),
    translateX: new Animated.Value(0),
    translateY: new Animated.Value(0),
    rotation: new Animated.Value(0),
  }))).current;

  useEffect(() => {
    if (visible) {
      console.log('🌸 STARTING FULL-SCREEN FLOWER ANIMATION');
      // Create full-screen radiating flower animation
      const animations = flowers.map((flower, index) => {
        const angle = (index * 24) * (Math.PI / 180); // 24 degrees apart for more coverage
        const distance = 200 + Math.random() * 300; // Large distance for full screen coverage

        return Animated.parallel([
          Animated.timing(flower.scale, {
            toValue: 2.5 + Math.random() * 1.5, // Much larger flowers (2.5x to 4x size)
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(flower.translateX, {
            toValue: Math.cos(angle) * distance,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(flower.translateY, {
            toValue: Math.sin(angle) * distance,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(flower.rotation, {
            toValue: 360 + Math.random() * 360,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(flower.opacity, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]);
      });

      Animated.parallel(animations).start(() => {
        // Reset values
        flowers.forEach(flower => {
          flower.scale.setValue(0);
          flower.opacity.setValue(1);
          flower.translateX.setValue(0);
          flower.translateY.setValue(0);
          flower.rotation.setValue(0);
        });
        onComplete();
      });
    }
  }, [visible, onComplete, flowers]);

  if (!visible) return null;

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      alignItems: 'center',
      justifyContent: 'center',
      pointerEvents: 'none',
      zIndex: 1000 // Ensure it's above everything
    }}>
      {flowers.map((flower, index) => (
        <Animated.View
          key={index}
          style={{
            position: 'absolute',
            transform: [
              { scale: flower.scale },
              { translateX: flower.translateX },
              { translateY: flower.translateY },
              { rotate: flower.rotation.interpolate({
                inputRange: [0, 360],
                outputRange: ['0deg', '360deg'],
              }) },
            ],
            opacity: flower.opacity,
          }}
        >
          <Text style={{ fontSize: 48 }}>🌸</Text>
        </Animated.View>
      ))}
    </View>
  );
});

// Enhanced Animated Like Button Component - MOVED OUTSIDE COMPONENT
interface LikeButtonProps {
  updateId: string;
  isLiked: boolean;
  likeCount: number;
  onLike: () => void;
  onLongPress: () => void;
  onTriggerFlowerAnimation: () => void;
}

const LikeButton = React.memo(function LikeButton({
  updateId,
  isLiked,
  likeCount,
  onLike,
  onLongPress,
  onTriggerFlowerAnimation
}: LikeButtonProps) {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const [isAnimating, setIsAnimating] = useState(false);

  const handleLikePress = useCallback(() => {
    if (isAnimating) return; // Prevent multiple rapid taps

    // Set animation state immediately to prevent double taps
    setIsAnimating(true);

    // Use requestAnimationFrame to defer state updates properly
    requestAnimationFrame(() => {
      // Call the like handler
      onLike();

      // Trigger flower animation
      console.log('🌸 TRIGGERING GLOBAL FLOWER ANIMATION');
      onTriggerFlowerAnimation();

      // Animate the main heart with proper 1-second duration
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 1.4,
          duration: 500, // 0.5 seconds up
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 500, // 0.5 seconds down = 1 second total
          useNativeDriver: true,
        }),
      ]).start(() => {
        setIsAnimating(false);
      });
    });
  }, [isAnimating, onLike, onTriggerFlowerAnimation, scaleAnim]);

  return (
    <TouchableOpacity
      style={styles.immersiveActionButton}
      onPress={handleLikePress}
      onLongPress={onLongPress}
      activeOpacity={0.7}
    >
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <Ionicons
          name={isLiked ? "heart" : "heart-outline"}
          size={32}
          color={isLiked ? "#FF3040" : "#FFFFFF"}
        />
      </Animated.View>
      <Text style={styles.immersiveActionText}>
        {likeCount > 999 ? `${(likeCount / 1000).toFixed(1)}K` : likeCount}
      </Text>
    </TouchableOpacity>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.updateId === nextProps.updateId &&
    prevProps.isLiked === nextProps.isLiked &&
    prevProps.likeCount === nextProps.likeCount
  );
});

// Global function to safely pause all active video players - UNIFIED VERSION
const pauseAllActiveVideos = () => {
  console.log('🛑 Pausing all active videos');

  // Use VideoProgressManager's getAllPlayers to avoid conflicts
  const allPlayers = videoProgressManager.getAllPlayers();
  const playersToRemove: string[] = [];

  allPlayers.forEach((player: any, videoId: string) => {
    try {
      if (player && typeof player.pause === 'function') {
        // Check if player is still valid by testing a property
        if (typeof player.playing !== 'undefined') {
          player.pause();
          console.log('⏸️ Paused player:', videoId);
        } else {
          playersToRemove.push(videoId);
        }
      } else {
        playersToRemove.push(videoId);
      }
    } catch (error) {
      // Player is likely released, mark for removal
      playersToRemove.push(videoId);
    }
  });

  // Clean up invalid players
  playersToRemove.forEach(videoId => {
    videoProgressManager.unregisterPlayer(videoId);
  });
};

// Convert RealUpdate to Update format for local storage
const convertRealUpdateToUpdate = (realUpdate: RealUpdate): Update => {
  return {
    id: realUpdate.id,
    userId: realUpdate.userId,
    userName: realUpdate.userName,
    userAvatar: realUpdate.userAvatar,
    type: realUpdate.type === 'photo' ? 'image' : realUpdate.type === 'video' ? 'video' : 'text',
    caption: realUpdate.caption || realUpdate.content,
    timestamp: realUpdate.timestamp,
    isStory: !!realUpdate.expiresAt,
    expiresAt: realUpdate.expiresAt,
    privacy: realUpdate.privacy,
    isVisible: true,
    isArchived: false,
    location: undefined, // Location feature disabled
    hashtags: [],
    mentions: [],
    groupTags: [],
    media: realUpdate.mediaUrl ? [{
      id: `${realUpdate.id}_media`,
      url: realUpdate.mediaUrl,
      type: realUpdate.type === 'photo' ? 'image' : 'video',
      width: realUpdate.mediaWidth,
      height: realUpdate.mediaHeight,
      duration: realUpdate.videoDuration,
    }] : [],
    likes: realUpdate.likes || [], // Ensure array exists
    views: (realUpdate.views || []).map(userId => ({
      userId,
      userName: 'Unknown',
      timestamp: new Date(),
      type: 'view' as const
    })),
    shares: (realUpdate.shares || []).map(userId => ({
      userId,
      userName: 'Unknown',
      timestamp: new Date(),
      type: 'share' as const
    })),
    downloads: (realUpdate.downloads || []).map(userId => ({
      userId,
      userName: 'Unknown',
      timestamp: new Date(),
      type: 'download' as const
    })),
    reactions: [],
    comments: (realUpdate.comments || []).map(commentId => ({
      id: commentId,
      updateId: realUpdate.id,
      userId: 'unknown',
      userName: 'Unknown',
      userAvatar: undefined,
      text: '',
      timestamp: new Date(),
      likes: [],
      replies: [],
      mentions: [],
      isEdited: false,
      isVisible: true,
      parentCommentId: undefined,
    })),
    viewCount: (realUpdate.views || []).length,
    likeCount: (realUpdate.likes || []).length,
    commentCount: (realUpdate.comments || []).length,
    shareCount: (realUpdate.shares || []).length,
    downloadCount: (realUpdate.downloads || []).length,
    isLikedByCurrentUser: false,
    isViewedByCurrentUser: false,
    isSharedByCurrentUser: false,
    isDownloadedByCurrentUser: false,
    isReported: false,
    reportCount: 0,
    isFlagged: false,
    isPinned: false,
    isHighlight: false,
    musicTrack: undefined,
  };
};

// Long Press Context Menu Modal Component
interface ContextMenuModalProps {
  visible: boolean;
  update: RealUpdate | null;
  currentUserId: string;
  onClose: () => void;
  onNotInterested: () => void;
  onReport: () => void;
  onAutoScroll: () => void;
  onCopyLink: () => void;
  onAutoDownload: () => void;
  onDelete: () => void;
  onFullScreen: () => void;
  autoScrollEnabled: boolean;
  autoDownloadEnabled: boolean;
}

const ContextMenuModal = React.memo(function ContextMenuModal({
  visible,
  update,
  currentUserId,
  onClose,
  onNotInterested,
  onReport,
  onAutoScroll,
  onCopyLink,
  onAutoDownload,
  onDelete,
  onFullScreen,
  autoScrollEnabled,
  autoDownloadEnabled,
}: ContextMenuModalProps) {
  console.log('🔥🔥🔥 CONTEXT MENU MODAL RENDER:', { visible, updateId: update?.id });

  const slideAnim = useRef(new Animated.Value(300)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 300,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  if (!visible || !update) return null;

  const isOwner = update.userId === currentUserId;

  const menuItems = [
    {
      id: 'not-interested',
      icon: 'eye-off-outline',
      label: 'Not Interested',
      color: '#F59E0B',
      onPress: onNotInterested,
    },
    {
      id: 'report',
      icon: 'flag-outline',
      label: 'Report',
      color: '#EF4444',
      onPress: onReport,
    },
    {
      id: 'auto-scroll',
      icon: autoScrollEnabled ? 'pause-outline' : 'play-outline',
      label: autoScrollEnabled ? 'Disable Auto Scroll' : 'Enable Auto Scroll',
      color: '#8B5CF6',
      onPress: onAutoScroll,
    },
    {
      id: 'copy-link',
      icon: 'link-outline',
      label: 'Copy Link',
      color: '#06B6D4',
      onPress: onCopyLink,
    },
    {
      id: 'auto-download',
      icon: autoDownloadEnabled ? 'cloud-download' : 'cloud-download-outline',
      label: autoDownloadEnabled ? 'Disable Auto Download' : 'Enable Auto Download',
      color: '#3B82F6',
      onPress: onAutoDownload,
    },
    {
      id: 'fullscreen',
      icon: 'expand-outline',
      label: 'Full Screen',
      color: '#6366F1',
      onPress: onFullScreen,
    },
  ];

  // Add delete option for owner
  if (isOwner) {
    menuItems.push({
      id: 'delete',
      icon: 'trash-outline',
      label: 'Delete',
      color: '#DC2626',
      onPress: onDelete,
    });
  }

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={onClose}>
        <Animated.View style={[styles.contextMenuBackdrop, { opacity: backdropOpacity }]}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.contextMenuContainer,
                { transform: [{ translateY: slideAnim }] }
              ]}
            >
              {/* Handle */}
              <View style={styles.contextMenuHandle} />

              {/* Title */}
              <Text style={styles.contextMenuTitle}>Options</Text>

              {/* Menu Items Grid */}
              <View style={styles.contextMenuGrid}>
                {menuItems.map((item, index) => (
                  <TouchableOpacity
                    key={item.id}
                    style={styles.contextMenuItem}
                    onPress={() => {
                      item.onPress();
                      onClose();
                    }}
                  >
                    <View style={[styles.contextMenuIcon, { backgroundColor: item.color }]}>
                      <Ionicons name={item.icon as any} size={24} color="white" />
                    </View>
                    <Text style={styles.contextMenuLabel}>{item.label}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Cancel Button */}
              <TouchableOpacity style={styles.contextMenuCancel} onPress={onClose}>
                <Text style={styles.contextMenuCancelText}>Cancel</Text>
              </TouchableOpacity>
            </Animated.View>
          </TouchableWithoutFeedback>
        </Animated.View>
      </TouchableWithoutFeedback>
    </Modal>
  );
});

// Full Screen Video Modal Component
interface FullScreenVideoModalProps {
  visible: boolean;
  update: RealUpdate | null;
  onClose: () => void;
}

const FullScreenVideoModal = React.memo(function FullScreenVideoModal({
  visible,
  update,
  onClose,
}: FullScreenVideoModalProps) {
  const [isPlaying, setIsPlaying] = useState(true);
  const [showControls, setShowControls] = useState(true);
  const controlsTimeout = useRef<NodeJS.Timeout | null>(null);

  // Create video player - always call hook, handle null case with empty string
  const videoPlayer = useVideoPlayer(update?.mediaUrl || '');

  // Auto-hide controls after 3 seconds
  useEffect(() => {
    if (showControls) {
      controlsTimeout.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }

    return () => {
      if (controlsTimeout.current) {
        clearTimeout(controlsTimeout.current);
      }
    };
  }, [showControls]);

  // Handle screen tap to toggle controls
  const handleScreenTap = () => {
    setShowControls(prev => !prev);
  };

  // Swipe down gesture to exit full screen
  const swipeDownGesture = Gesture.Pan()
    .onUpdate((event) => {
      // Only respond to downward swipes
      if (event.translationY > 0 && event.velocityY > 500) {
        // Swipe down detected with sufficient velocity
        console.log('👆 Swipe down detected, exiting full screen');
        onClose();
      }
    });

  // Handle back button
  useEffect(() => {
    if (!visible) return;

    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      onClose();
      return true;
    });

    return () => backHandler.remove();
  }, [visible, onClose]);

  // Early return after all hooks
  if (!visible || !update || !update.mediaUrl) return null;

  return (
    <Modal
      visible={visible}
      transparent={false}
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <StatusBar hidden={true} />
      <GestureDetector gesture={swipeDownGesture}>
        <View style={styles.fullScreenContainer}>
          <TouchableWithoutFeedback onPress={handleScreenTap}>
            <View style={styles.fullScreenVideoContainer}>
              <VideoView
                style={styles.fullScreenVideo}
                player={videoPlayer}
                contentFit="contain"
                allowsFullscreen={false}
                nativeControls={false}
              />
            </View>
          </TouchableWithoutFeedback>

        {/* Controls Overlay */}
        {showControls && (
          <Animated.View style={styles.fullScreenControls}>
            {/* Top Controls */}
            <View style={styles.fullScreenTopControls}>
              <TouchableOpacity style={styles.fullScreenBackButton} onPress={onClose}>
                <Ionicons name="arrow-back" size={28} color="white" />
              </TouchableOpacity>

              <View style={styles.fullScreenInfo}>
                <Text style={styles.fullScreenUsername}>{update.userName}</Text>
                <Text style={styles.fullScreenCaption} numberOfLines={2}>
                  {update.caption || 'No caption'}
                </Text>
              </View>
            </View>

            {/* Center Play/Pause Button */}
            <TouchableOpacity
              style={styles.fullScreenPlayButton}
              onPress={() => setIsPlaying(prev => !prev)}
            >
              <Ionicons
                name={isPlaying ? 'pause' : 'play'}
                size={60}
                color="white"
              />
            </TouchableOpacity>

            {/* Bottom Controls */}
            <View style={styles.fullScreenBottomControls}>
              <Text style={styles.fullScreenHint}>
                Tap to show/hide controls • Swipe down to exit
              </Text>
            </View>
          </Animated.View>
        )}
        </View>
      </GestureDetector>
    </Modal>
  );
});

const UpdatesScreen: React.FC = () => {
  const router = useRouter();
  const navigation = useNavigation();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);

  const [updates, setUpdates] = useState<RealUpdate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<number>(0);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showCameraOptions, setShowCameraOptions] = useState(false);
  const [showVideoTrimmer, setShowVideoTrimmer] = useState(false);
  const [showPhotoCropper, setShowPhotoCropper] = useState(false);

  // Recent media state for WhatsApp-style modal
  const [recentMedia, setRecentMedia] = useState<RecentMediaItem[]>([]);
  const [isLoadingRecentMedia, setIsLoadingRecentMedia] = useState(false);

  // Music picker state
  const [showMusicPicker, setShowMusicPicker] = useState(false);
  const [selectedMusic, setSelectedMusic] = useState<LocalMusicTrack | WebMusicTrack | null>(null);

  // Layout picker state
  const [showLayoutPicker, setShowLayoutPicker] = useState(false);
  const [selectedLayout, setSelectedLayout] = useState<LayoutTemplate | null>(null);

  // Voice recorder state
  const [showVoiceRecorder, setShowVoiceRecorder] = useState(false);
  const [selectedVoiceRecording, setSelectedVoiceRecording] = useState<VoiceRecording | null>(null);

  // Enhanced upload state
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  // Interaction pages state
  const [showInteractionPages, setShowInteractionPages] = useState(false);
  const [selectedUpdateId, setSelectedUpdateId] = useState<string>('');
  const [selectedInteractionTab, setSelectedInteractionTab] = useState<'likes' | 'views' | 'shares' | 'downloads'>('likes');

  // Removed story management state - pure video feed only

  // Search State
  const [showSearch, setShowSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<RealUpdate[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isOnline, setIsOnline] = useState(true);

  // Video playback control
  const [currentlyPlayingVideo, setCurrentlyPlayingVideo] = useState<string | null>(null);
  const [globalVideoPause, setGlobalVideoPause] = useState(false);
  const [currentVideoMuted, setCurrentVideoMuted] = useState(false);
  const [currentScrollPosition, setCurrentScrollPosition] = useState(0); // Global pause for all videos
  const flatListRef = useRef<FlatList>(null);

  // REMOVED: Cleanup interval - VideoProgressManager handles this automatically



  // Removed tab system - pure TikTok-style video feed only

  // Comments page state
  const [showCommentsPage, setShowCommentsPage] = useState(false);
  const [commentsUpdateId, setCommentsUpdateId] = useState<string>('');

  // Caption input modal state
  const [showCaptionModal, setShowCaptionModal] = useState(false);
  const [captionText, setCaptionText] = useState('');

  // Global full-screen flower animation state
  const [showGlobalFlowerAnimation, setShowGlobalFlowerAnimation] = useState(false);
  const [captionVideoPlaying, setCaptionVideoPlaying] = useState(true);
  const [captionModalData, setCaptionModalData] = useState<{
    mediaUri: string;
    type: 'photo' | 'video';
    isStory: boolean;
  } | null>(null);

  // Download progress state for real-time tracking
  const [downloadingUpdates, setDownloadingUpdates] = useState<Set<string>>(new Set());
  const [downloadProgress, setDownloadProgress] = useState<{ [key: string]: number }>({});

  // High-performance video progress state
  const [videoProgressData, setVideoProgressData] = useState<{
    videoId: string;
    currentTime: number;
    duration: number;
    progress: number;
  } | null>(null);

  // TikTok-style progress bar visibility (per video)
  const [progressBarVisible, setProgressBarVisible] = useState<{[videoId: string]: boolean}>({});

  // Long Press Context Menu State
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuUpdate, setContextMenuUpdate] = useState<RealUpdate | null>(null);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(false);
  const [autoDownloadEnabled, setAutoDownloadEnabled] = useState(false);
  const [hiddenUpdates, setHiddenUpdates] = useState<Set<string>>(new Set());
  const [isFullScreenMode, setIsFullScreenMode] = useState(false);
  const [fullScreenUpdate, setFullScreenUpdate] = useState<RealUpdate | null>(null);

  // Manage active video for progress tracking
  useEffect(() => {
    if (currentlyPlayingVideo) {
      // Set active video in progress manager
      videoProgressManager.setActiveVideo(currentlyPlayingVideo);

      // Subscribe to progress updates
      videoProgressManager.subscribeToProgress(currentlyPlayingVideo, (progressData: {
        videoId: string;
        currentTime: number;
        duration: number;
        progress: number;
      }) => {
        setVideoProgressData({
          videoId: progressData.videoId,
          currentTime: progressData.currentTime,
          duration: progressData.duration,
          progress: progressData.progress,
        });
      });
    } else {
      // No active video
      videoProgressManager.setActiveVideo(null);
      setVideoProgressData(null);
    }

    return () => {
      if (currentlyPlayingVideo) {
        videoProgressManager.unsubscribeFromProgress(currentlyPlayingVideo);
      }
    };
  }, [currentlyPlayingVideo]);

  // TikTok-style media creation states
  const [showTikTokMediaPicker, setShowTikTokMediaPicker] = useState(false);
  const [showTikTokCamera, setShowTikTokCamera] = useState(false);
  const [showMediaPreviewEditor, setShowMediaPreviewEditor] = useState(false);
  const [showTextUpdateCreator, setShowTextUpdateCreator] = useState(false);
  const [selectedMediaForPreview, setSelectedMediaForPreview] = useState<{
    uri: string;
    type: 'photo' | 'video';
  } | null>(null);
  const [isPostingUpdate, setIsPostingUpdate] = useState(false);

  // Network monitoring
  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener(state => {
      setIsOnline(state.isConnected ?? false);
    });

    return unsubscribe;
  }, []);

  // Initialize local storage and load updates on component mount
  useEffect(() => {
    const initializeAndLoad = async () => {
      try {
        // Initialize local storage
        await localUpdatesStorage.initialize();

        // Initialize audio caption service
        await audioCaptionService.initialize();

        // Initialize offline services to ensure data persistence
        await offlineCommentsService.forceInitialize();
        await offlineLikesService.forceInitialize();

        // Clear expired stories
        await localUpdatesStorage.clearExpiredStories();

        // Start background sync service
        try {
          if (updatesSyncService && typeof updatesSyncService.startBackgroundSync === 'function') {
            updatesSyncService.startBackgroundSync();
          }
        } catch (error) {
          console.error('Error starting background sync:', error);
        }
      } catch (error) {
        console.error('❌ Failed to initialize local storage:', error);
      }
    };

    if (currentUser?.id) {
      // Check Firebase authentication status

      // Check if Firebase user matches Redux user
      if (auth?.currentUser?.uid !== currentUser?.id) {


        // If using temporary user, switch to local-only mode
        if (currentUser?.id?.startsWith('temp_')) {
          setIsOnline(false); // Force offline mode for temp users
        }
      }

      initializeAndLoad();
      loadUpdates();

      // Initialize Firebase sync for offline interactions
      updateInteractionsService.syncOfflineInteractions();

      // Initialize and start background sync for offline updates
      updatesSyncService.startBackgroundSync();

      // Subscribe to real-time updates with error handling
      let unsubscribe: (() => void) | null = null;

      try {
        unsubscribe = realUpdatesService.subscribeToUpdatesFeed(
          currentUser.id,
          (newUpdates) => {
            setUpdates(newUpdates);
            setIsLoading(false);

            // Save real-time updates to local storage
            newUpdates.forEach(async (update) => {
              try {
                const convertedUpdate = convertRealUpdateToUpdate(update);
                await localUpdatesStorage.saveUpdate(convertedUpdate);
              } catch (error) {
                console.error('⚠️ Failed to save real-time update locally:', error);
              }
            });
          }
        );
      } catch (firebaseError) {
        console.error('🔥 Firebase subscription failed, using local-only mode:', firebaseError);
        // Continue with local-only mode - updates will still work offline
        setIsLoading(false);
      }

      return () => {
        if (unsubscribe) {
          unsubscribe();
        }
        updatesSyncService.stopBackgroundSync();
        // Stop all video playback on unmount
        setCurrentlyPlayingVideo(null);
      };
    }
  }, [currentUser?.id]);

  // CRITICAL: App State and Navigation Lifecycle Management
  useEffect(() => {
    // App state change listener (background/foreground)
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'background' || nextAppState === 'inactive') {
        pauseAllActiveVideos(); // Immediately pause all videos
        setGlobalVideoPause(true);
        setCurrentlyPlayingVideo(null);
      } else if (nextAppState === 'active') {
        setGlobalVideoPause(false);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, []);

  // Navigation focus listener to resume videos when returning from other screens
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      // Resume videos when returning to this screen
      if (globalVideoPause && !showCommentsPage && !showInteractionPages) {
        console.log('📱 Returning to updates tab - resuming videos');
        setGlobalVideoPause(false);
      }
    });

    return unsubscribe;
  }, [navigation, globalVideoPause, showCommentsPage, showInteractionPages]);

  // Page focus/blur management with immediate video playback
  const hasAutoPlayedRef = useRef(false);

  useFocusEffect(
    useCallback(() => {
      console.log('📱 Stories tab focused - enabling videos');

      // Load updates if we don't have any or if it's been a while
      if (currentUser?.id && (updates.length === 0 || !lastRefreshTime || Date.now() - lastRefreshTime > 300000)) { // 5 minutes
        console.log('🔄 Loading updates on tab focus...');
        loadUpdates();
      }

      // Page focused - enable videos and start playing first video immediately
      setGlobalVideoPause(false);

      // Enhanced video resumption with better timing
      if (updates.length > 0) {
        console.log('🎯 Resuming video playback on focus, scroll position:', currentScrollPosition);

        // Use a small delay to ensure UI is stable before resuming videos
        setTimeout(() => {
          if (!globalVideoPause) {
            // Detect and play visible video based on current scroll position
            const currentIndex = Math.round(currentScrollPosition / SCREEN_HEIGHT);
            const currentUpdate = updates[currentIndex];

            console.log('🎯 Tab focus - Current scroll position:', currentScrollPosition);
            console.log('🎯 Tab focus - Calculated index:', currentIndex);
            console.log('🎯 Tab focus - Current update:', currentUpdate?.id, currentUpdate?.type);

            if (currentUpdate && currentUpdate.type === 'video') {
              console.log('🎯 Resuming video at current position:', currentUpdate.id);
              setCurrentlyPlayingVideo(currentUpdate.id);
            } else {
              // Don't fallback to first video - maintain current state
              console.log('🎯 Current position has no video, keeping current state');
              // Only set to null if we were playing something that's no longer valid
              if (currentlyPlayingVideo && !updates.find(u => u.id === currentlyPlayingVideo)) {
                setCurrentlyPlayingVideo(null);
              }
            }
          }
        }, 200); // Small delay to ensure smooth transition
      }

      return () => {
        console.log('📱 Stories tab unfocused - pausing all videos');
        // CRITICAL: Stop ALL videos when leaving updates tab
        pauseAllActiveVideos(); // Immediately pause all videos
        setGlobalVideoPause(true);
        setCurrentlyPlayingVideo(null);
        hasAutoPlayedRef.current = false; // Reset for next focus
      };
    }, [updates]) // Removed currentlyPlayingVideo from dependencies
  );

  // Modal state management - pause videos when modals/pages open (but preserve playing state for resume)
  useEffect(() => {
    // Comments and downloads pages should only pause, not stop videos
    const pauseOnlyModals = showCommentsPage;

    // Other modals that should completely stop videos (removed showContextMenu)
    const stopVideoModals = showCaptionModal || showCameraOptions || showCreateModal || showSearch ||
                           showInteractionPages || showVideoTrimmer || showPhotoCropper ||
                           showTikTokMediaPicker || showTikTokCamera || showMediaPreviewEditor || showTextUpdateCreator;

    if (pauseOnlyModals) {
      // Only pause videos, don't clear currentlyPlayingVideo so they can resume
      pauseAllActiveVideos();
      setGlobalVideoPause(true);
      // Also pause all audio captions when modals are open
      audioCaptionService.setGlobalAudioPause(true);
    } else if (stopVideoModals) {
      // Completely stop videos for these modals
      pauseAllActiveVideos();
      setGlobalVideoPause(true);
      setCurrentlyPlayingVideo(null);
      // Also pause all audio captions when modals are open
      audioCaptionService.setGlobalAudioPause(true);
    } else {
      setGlobalVideoPause(false);
      // Resume audio captions when modals are closed
      audioCaptionService.setGlobalAudioPause(false);
    }
  }, [showCaptionModal, showCameraOptions, showCreateModal, showSearch, showInteractionPages, showCommentsPage, showVideoTrimmer, showPhotoCropper, showTikTokMediaPicker, showTikTokCamera, showMediaPreviewEditor, showTextUpdateCreator]);

  // Delete update function
  const handleDeleteUpdate = async (updateId: string, userId: string) => {
    // Check if current user is the owner
    if (currentUser?.id !== userId) {
      Alert.alert('Error', 'You can only delete your own updates');
      return;
    }

    Alert.alert(
      'Delete Update',
      'Are you sure you want to delete this update? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // Remove from UI immediately for better UX
              setUpdates(prevUpdates => prevUpdates.filter(u => u.id !== updateId));

              // Remove from local storage (use updateId as localId for local updates)
              try {
                await localUpdatesStorage.deleteUpdate(updateId);
              } catch (localError) {
                // Local delete failed, continuing with Firebase delete
              }

              // Try to delete from Firebase if online and not a temp user
              if (isOnline && !userId.startsWith('temp_')) {
                try {
                  // Note: realUpdatesService.deleteUpdate might need to be implemented
                  // For now, we'll just remove it locally
                  //Firebase delete would be called here
                } catch (error) {
                  // Failed to delete from Firebase, but removed locally
                }
              }

              Alert.alert('Success', 'Update deleted successfully');
            } catch (error) {
              console.error('Failed to delete update:', error);
              Alert.alert('Error', 'Failed to delete update');
              // Re-add the update to UI if deletion failed
              loadUpdates();
            }
          },
        },
      ]
    );
  };

  // TikTok-style scroll-based video detection
  const isSwipingRef = useRef(false);
  const swipeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // REMOVED: Redundant effect that was causing infinite play/pause cycles
  // Video playback is now handled entirely by individual VideoPlayer components

  // Cleanup swipe timeout on unmount
  useEffect(() => {
    return () => {
      if (swipeTimeoutRef.current) {
        clearTimeout(swipeTimeoutRef.current);
      }
    };
  }, []);










  // Load updates (Local-first with Firebase sync)
  const loadUpdates = useCallback(async () => {
    if (!currentUser?.id) return;

    try {
      setIsLoading(true);

      // 🏠 LOAD FROM LOCAL STORAGE FIRST
      const localUpdates = await localUpdatesStorage.getUpdates(50, 0, false);

      // Convert Update[] to RealUpdate[] for display - ROBUST CONVERSION
      const convertedLocalUpdates: RealUpdate[] = localUpdates.map((update: Update) => ({
        id: update.id,
        userId: update.userId,
        userName: update.userName,
        userAvatar: update.userAvatar,
        content: update.caption || '',
        caption: update.caption,
        type: update.type === 'image' ? 'photo' : update.type === 'video' ? 'video' : 'text',
        mediaUrl: update.media && update.media.length > 0 ? update.media[0].url : undefined,
        mediaWidth: update.media && update.media.length > 0 ? update.media[0].width : undefined,
        mediaHeight: update.media && update.media.length > 0 ? update.media[0].height : undefined,
        videoDuration: update.media && update.media.length > 0 ? update.media[0].duration : undefined,
        privacy: update.privacy as 'public' | 'friends' | 'private',
        timestamp: update.timestamp,
        expiresAt: update.expiresAt,
        likes: update.likes || [],
        comments: (update.comments || []).map((c: any) => c.id || c), // Convert comments to string array for RealUpdate
        shares: (update.shares || []).map((s: any) => s.userId || s),
        views: (update.views || []).map((v: any) => v.userId || v),
        downloads: (update.downloads || []).map((d: any) => d.userId || d),
        location: undefined, // Location feature disabled
        tags: update.hashtags || [],
        mentions: update.mentions || [],
        musicTrack: update.musicTrack ? {
          title: update.musicTrack.title,
          artist: update.musicTrack.artist,
          url: update.musicTrack.localUri || ''
        } : undefined,
        musicArtist: update.musicTrack?.artist,
        // Ensure counts are preserved from local storage
        textOverlays: update.textOverlays,
        audioCaption: update.audioCaption,
      }));



      // Set local updates immediately for offline functionality (ensure unique IDs)
      const uniqueLocalUpdates = convertedLocalUpdates.filter((update, index, self) =>
        index === self.findIndex(u => u.id === update.id)
      );

      console.log(`🔍 DEBUG: Total local updates: ${convertedLocalUpdates.length}, Unique: ${uniqueLocalUpdates.length}`);
      if (convertedLocalUpdates.length !== uniqueLocalUpdates.length) {
        console.log(`🚨 FOUND DUPLICATES in local updates!`);
        const duplicateIds = convertedLocalUpdates.map(u => u.id).filter((id, index, arr) => arr.indexOf(id) !== index);
        console.log(`🚨 Duplicate IDs:`, duplicateIds);
      }

      setUpdates(uniqueLocalUpdates);

      // Auto-play first video when updates are loaded (if not globally paused)
      if (convertedLocalUpdates.length > 0 && !globalVideoPause) {
        const firstVideo = convertedLocalUpdates.find(update => update.type === 'video');
        if (firstVideo) {
          // Add a small delay to ensure video players are properly mounted
          setTimeout(() => {
            // Double-check that the video player is still registered
            const allPlayers = videoProgressManager.getAllPlayers();
            if (allPlayers.has(firstVideo.id)) {
              setCurrentlyPlayingVideo(firstVideo.id);
            }
          }, 200);
        }
      }

      // 🔄 SYNC WITH FIREBASE IN BACKGROUND (Skip for temporary users)
      if (!currentUser.id.startsWith('temp_') && isOnline) {
        try {
          const feedUpdates = await realUpdatesService.getUpdatesFeed(currentUser.id);

          // Save Firebase updates to local storage
          for (const update of feedUpdates) {
            try {
              const convertedUpdate = convertRealUpdateToUpdate(update);
              await localUpdatesStorage.saveUpdate(convertedUpdate);
            } catch (error) {
              // Failed to save Firebase update locally
            }
          }

          // Merge Firebase data with local data (prevent duplicates)
          setUpdates(prevUpdates => {
            const firebaseIds = new Set(feedUpdates.map(u => u.id));
            const localOnlyUpdates = prevUpdates.filter(u => !firebaseIds.has(u.id));

            // Create a Map to ensure unique IDs
            const uniqueUpdatesMap = new Map();

            // Add Firebase updates first (they're more up-to-date)
            feedUpdates.forEach(update => {
              uniqueUpdatesMap.set(update.id, update);
            });

            // Add local-only updates (that don't exist in Firebase)
            localOnlyUpdates.forEach(update => {
              if (!uniqueUpdatesMap.has(update.id)) {
                uniqueUpdatesMap.set(update.id, update);
              }
            });

            const mergedUpdates = Array.from(uniqueUpdatesMap.values());

            console.log(`🔍 DEBUG: Firebase updates: ${feedUpdates.length}, Local only: ${localOnlyUpdates.length}, Merged: ${mergedUpdates.length}`);
            console.log(`🔍 DEBUG: Previous updates: ${prevUpdates.length}`);

            // Simple auto-play first video after Firebase sync
            if (mergedUpdates.length > 0 && !currentlyPlayingVideo && !globalVideoPause) {
              const firstVideo = mergedUpdates.find(update => update.type === 'video');
              const allPlayers = videoProgressManager.getAllPlayers();
              if (firstVideo && allPlayers.has(firstVideo.id)) {
                console.log('🎯 Auto-playing first video after sync:', firstVideo.id);
                setCurrentlyPlayingVideo(firstVideo.id);
              }
            }

            return mergedUpdates;
          });
        } catch (firebaseError: any) {
          console.error('🔥 Firebase sync failed:', firebaseError?.message || firebaseError);

          // Check if it's a permission error
          if (firebaseError?.code === 'permission-denied') {

            // Keep using local data - don't show error to user
          } else {

          }

          // Continue with local data only - this is expected behavior
        }
      } else {

      }

    } catch (error) {
      console.error('❌ Error loading updates:', error);
      // Fallback to empty array if both local and Firebase fail
      setUpdates([]);
    } finally {
      setIsLoading(false);
      setLastRefreshTime(Date.now()); // Update last refresh time
    }
  }, [currentUser?.id]);

  // Refresh updates
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadUpdates();
    setIsRefreshing(false);
  }, [loadUpdates]);

  // Handle like toggle with COMPLETE Firebase sync - optimized to prevent video interruption
  const handleLike = useCallback(async (updateId: string) => {
    if (!currentUser?.id) return;

    try {
      // Check if this is an offline update
      if (updateId.startsWith('offline_')) {
        console.log('📱 Skipping Firebase sync for offline update view:', updateId);
        // For offline updates, only update local state
        setUpdates(prev => {
          const updateIndex = prev.findIndex(update => update.id === updateId);
          if (updateIndex === -1) return prev;

          const newUpdates = [...prev];
          const updatedItem = { ...newUpdates[updateIndex] };
          const currentLikeState = updatedItem.likes.includes(currentUser.id);

          if (!currentLikeState) {
            updatedItem.likes = [...updatedItem.likes, currentUser.id];
          } else {
            updatedItem.likes = updatedItem.likes.filter(id => id !== currentUser.id);
          }

          newUpdates[updateIndex] = updatedItem;
          return newUpdates;
        });
        return;
      }

      // Find current update
      const currentUpdate = updates.find(update => update.id === updateId);
      if (!currentUpdate) return;

      const currentLikeState = currentUpdate.likes.includes(currentUser.id);
      const userName = currentUser.displayName || currentUser.email || 'Unknown';
      const userAvatar = currentUser.photoURL;

      // Optimistically update UI first for better UX - moved outside InteractionManager
      setUpdates(prev => {
        const updateIndex = prev.findIndex(update => update.id === updateId);
        if (updateIndex === -1) return prev;

        // Create a shallow copy of the array and update only the specific item
        const newUpdates = [...prev];
        const updatedItem = { ...newUpdates[updateIndex] };

        // Update likes array efficiently
        if (!currentLikeState) {
          // Adding like
          updatedItem.likes = updatedItem.likes.includes(currentUser.id)
            ? updatedItem.likes
            : [...updatedItem.likes, currentUser.id];
        } else {
          // Removing like
          updatedItem.likes = updatedItem.likes.filter(id => id !== currentUser.id);
        }

        newUpdates[updateIndex] = updatedItem;
        return newUpdates;
      });

      let success = false;

      if (currentLikeState) {
        // Unlike the update
        success = await updateInteractionsService.unlikeUpdate(updateId, currentUser.id);
      } else {
        // Like the update
        success = await updateInteractionsService.likeUpdate(updateId, currentUser.id, userName, userAvatar);
      }

      if (!success) {
        // Revert optimistic update on failure
        setUpdates(prev => {
          const updateIndex = prev.findIndex(update => update.id === updateId);
          if (updateIndex === -1) return prev;

          const newUpdates = [...prev];
          const updatedItem = { ...newUpdates[updateIndex] };

          // Revert likes array
          if (!currentLikeState) {
            // Revert adding like
            updatedItem.likes = updatedItem.likes.filter(id => id !== currentUser.id);
          } else {
            // Revert removing like
            updatedItem.likes = updatedItem.likes.includes(currentUser.id)
              ? updatedItem.likes
              : [...updatedItem.likes, currentUser.id];
          }

          newUpdates[updateIndex] = updatedItem;
          return newUpdates;
        });
      } else {
        console.log(`✅ ${currentLikeState ? 'Unlike' : 'Like'} action completed and synced to Firebase`);

        // Save updated state to local storage for persistence
        const updatedUpdate = updates.find(u => u.id === updateId);
        if (updatedUpdate) {
          const convertedUpdate = convertRealUpdateToUpdate(updatedUpdate);
          await localUpdatesStorage.saveUpdate(convertedUpdate);
        }
      }
    } catch (error) {
      console.error('❌ Error toggling like:', error);
    }
  }, [currentUser?.id, currentUser?.displayName, currentUser?.email, currentUser?.photoURL, updates]);

  // Handle comment
  const handleComment = async (updateId: string) => {
    try {
      setCommentsUpdateId(updateId);
      setShowCommentsPage(true);
    } catch (error) {
      console.error('❌ Error opening comments:', error);
    }
  };



  // Handle share
  const handleShare = async (updateId: string) => {
    if (!currentUser?.id) return;

    try {
      // Check if this is an offline story
      if (updateId.startsWith('offline_')) {
        console.log('📱 Sharing offline story:', updateId);

        // For offline stories, use device sharing instead of Firebase
        const update = updates.find(u => u.id === updateId);
        if (update) {
          // Import share service dynamically
          const { shareService } = await import('../../src/services/shareService');

          if (update.mediaUrl) {
            // Share the media file
            const success = await shareService.shareFile(update.mediaUrl, {
              dialogTitle: 'Share Story',
              mimeType: update.type === 'video' ? 'video/mp4' : 'image/jpeg'
            });

            if (success) {
              console.log('✅ Story shared successfully');

              // Update local share count for offline story
              setUpdates(prev => prev.map(u => {
                if (u.id === updateId) {
                  return { ...u, shares: [...u.shares, currentUser.id] };
                }
                return u;
              }));
            }
          } else {
            // Share story text/caption
            const shareText = update.caption || 'Check out this story from IraChat!';
            const success = await shareService.shareText(shareText, {
              dialogTitle: 'Share Story'
            });

            if (success) {
              console.log('✅ Story text shared successfully');
            }
          }
        } else {
          console.error('❌ Story not found for sharing');
        }
        return;
      }

      // For online stories, use Firebase sharing with complete sync
      const userName = currentUser.displayName || currentUser.email || 'Unknown';
      const success = await updateInteractionsService.shareUpdate(updateId, currentUser.id, userName, 'general');

      if (success) {
        console.log('✅ Update shared successfully and synced to Firebase');

        // Update local state
        setUpdates(prev => prev.map(update => {
          if (update.id === updateId) {
            return { ...update, shares: [...update.shares, currentUser.id] };
          }
          return update;
        }));

        // Save updated state to local storage for persistence
        const updatedUpdate = updates.find(u => u.id === updateId);
        if (updatedUpdate) {
          const convertedUpdate = convertRealUpdateToUpdate({
            ...updatedUpdate,
            shares: [...updatedUpdate.shares, currentUser.id]
          });
          await localUpdatesStorage.saveUpdate(convertedUpdate);
        }
      } else {
        console.error('❌ Failed to share update');
      }
    } catch (error) {
      console.error('❌ Error sharing update:', error);
    }
  };

  // Handle repost to stories with real-time Firestore
  const handleRepost = async (item: RealUpdate) => {
    if (!currentUser?.id) {
      Alert.alert('Error', 'Please sign in to repost');
      return;
    }

    console.log('🔄 Starting repost process for:', item.id);
    console.log('📦 Repost service type:', typeof repostService);
    console.log('📊 Item details:', {
      id: item.id,
      mediaUrl: item.mediaUrl,
      type: item.type,
      userName: item.userName,
      caption: item.caption
    });

    try {
      console.log('✅ Repost service available');

      // Check if this is an offline update
      if (item.id.startsWith('offline_')) {
        Alert.alert(
          'Cannot Repost Offline Content',
          'This content is not yet uploaded to the server. Please wait for it to sync before reposting.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Validate item data
      if (!item.mediaUrl || item.mediaUrl.trim() === '') {
        Alert.alert('Error', 'Cannot repost: Media URL is missing');
        return;
      }

      // Check if media URL is a local file path
      if (item.mediaUrl.startsWith('file://')) {
        Alert.alert(
          'Cannot Repost Local Content',
          'This content is stored locally and cannot be reposted. Please wait for it to upload to the server.',
          [{ text: 'OK' }]
        );
        return;
      }

      if (!item.type || !item.userName) {
        Alert.alert('Error', 'Cannot repost: Missing required media information');
        return;
      }

      // Show repost confirmation with caption input immediately
      // (We'll check eligibility inside the prompt callback)
      Alert.prompt(
        'Repost to Stories',
        'Add a caption for your repost (optional):',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Repost',
            onPress: async (caption) => {
              try {
                // Check eligibility first
                console.log('🔍 Checking repost eligibility...');
                const eligibility = await repostService.canUserRepost(currentUser.id);
                console.log('📊 Eligibility result:', eligibility);

                if (!eligibility.canRepost) {
                  Alert.alert('Repost Limit', eligibility.reason || 'Cannot repost at this time');
                  return;
                }

                // Pause videos during repost
                pauseAllActiveVideos();
                setGlobalVideoPause(true);

                console.log('📝 Preparing repost data...');

                // Validate required data
                if (!item.mediaUrl) {
                  Alert.alert('Error', 'Cannot repost - media URL not available');
                  return;
                }

                // Prepare repost data
                const repostData = {
                  originalMediaId: item.id,
                  originalMediaUrl: item.mediaUrl,
                  originalMediaType: item.type === 'video' ? 'video' : 'photo' as 'photo' | 'video',
                  originalAuthor: item.userName,
                  originalCaption: item.caption,
                  newCaption: caption || `Check out this ${item.type} from @${item.userName}! 🔥`,
                  isStory: true,
                  privacy: 'public' as 'public' | 'friends' | 'private'
                };

                console.log('🔄 Repost data prepared:', repostData);

                // Show progress alert
                Alert.alert('Reposting...', 'Creating your story...');

                // Show initial progress
                Alert.alert('Starting Repost...', 'Preparing your story...');

                // Perform repost with detailed progress tracking
                console.log('🚀 Starting repost to stories...');
                const result = await repostService.repostToStories(
                  currentUser.id,
                  currentUser.displayName || currentUser.email || 'User',
                  currentUser.photoURL || undefined,
                  repostData,
                  (progress) => {
                    console.log(`📊 Repost progress: ${progress.stage} - ${progress.percentage}% - ${progress.message}`);

                    // Update user with progress
                    if (progress.stage === 'copying') {
                      // Don't show alert for copying stage to avoid spam
                    } else if (progress.stage === 'uploading') {
                      // Show upload progress for longer operations
                      if (progress.percentage >= 50) {
                        console.log('📤 Upload progress:', progress.percentage + '%');
                      }
                    } else if (progress.stage === 'creating') {
                      console.log('📝 Creating story...');
                    }
                  }
                );

                console.log('✅ Repost result:', result);

                if (result.success) {
                  Alert.alert(
                    'Success! 🎉',
                    'Your story has been posted successfully!',
                    [
                      {
                        text: 'View Stories',
                        onPress: () => {
                          // Refresh updates to show new repost
                          loadUpdates();
                        }
                      },
                      { text: 'OK' }
                    ]
                  );
                } else {
                  console.error('❌ Repost failed with error:', result.error);
                  Alert.alert(
                    'Repost Failed',
                    result.error || 'Failed to repost media. Please check your internet connection and try again.',
                    [
                      {
                        text: 'Retry',
                        onPress: () => handleRepost(item)
                      },
                      { text: 'Cancel', style: 'cancel' }
                    ]
                  );
                }

              } catch (error) {
                console.error('❌ Repost error:', error);
                console.error('❌ Repost error details:', {
                  message: error instanceof Error ? error.message : 'Unknown error',
                  stack: error instanceof Error ? error.stack : undefined,
                  itemId: item.id,
                  mediaUrl: item.mediaUrl,
                  timestamp: new Date().toISOString()
                });

                Alert.alert(
                  'Repost Error',
                  `Failed to repost: ${error instanceof Error ? error.message : 'Unknown error'}. Please check your internet connection and try again.`,
                  [
                    {
                      text: 'Retry',
                      onPress: () => handleRepost(item)
                    },
                    { text: 'Cancel', style: 'cancel' }
                  ]
                );
              } finally {
                // Resume videos
                setGlobalVideoPause(false);
              }
            }
          }
        ],
        'plain-text',
        `Check out this ${item.type} from @${item.userName}! 🔥`
      );

    } catch (error) {
      console.error('❌ Repost initialization error:', error);
      console.error('❌ Initialization error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        userId: currentUser?.id,
        itemId: item.id,
        timestamp: new Date().toISOString()
      });

      Alert.alert(
        'Repost Error',
        `Failed to initialize repost: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`,
        [
          {
            text: 'Retry',
            onPress: () => handleRepost(item)
          },
          { text: 'Cancel', style: 'cancel' }
        ]
      );
    }
  };

  // Smart download/save handler - downloads if needed, then opens downloads page
  const handleDownloadOrOpenPage = async (item: any) => {
    if (!currentUser?.id) return;

    try {
      // Temporarily pause videos for navigation
      pauseAllActiveVideos();
      setGlobalVideoPause(true);

      // Check if media is already downloaded
      if (item.mediaUrl && mediaDownloadService.isMediaDownloaded(item.mediaUrl)) {
        // Already downloaded, directly open downloads page
        console.log('📱 Media already downloaded, opening downloads page');
        router.push('/downloaded-media');
        return;
      }

      // Not downloaded yet, proceed with download
      await handleDownload(item.id);
    } catch (error) {
      console.error('❌ Error in handleDownloadOrOpenPage:', error);
      // Fallback: just open downloads page
      router.push('/downloaded-media');
    }
  };

  // Handle download with offline/online support using mediaDownloadService
  const handleDownload = async (updateId: string, skipNavigation: boolean = false) => {
    if (!currentUser?.id) return;

    try {
      const update = updates.find(u => u.id === updateId);
      if (!update?.mediaUrl) {
        Alert.alert('Error', 'No media to download');
        return;
      }

      // Check if already downloaded
      if (mediaDownloadService.isMediaDownloaded(update.mediaUrl)) {
        Alert.alert('Already Downloaded', 'This media is already saved to your gallery');
        return;
      }

      // Check if already downloading
      if (downloadingUpdates.has(updateId)) {
        Alert.alert('Info', 'Download already in progress');
        return;
      }

      // Start download tracking
      setDownloadingUpdates(prev => new Set(prev).add(updateId));
      setDownloadProgress(prev => ({ ...prev, [updateId]: 0 }));

      // Use mediaDownloadService for robust offline/online handling
      console.log('📥 Starting download:', {
        updateId,
        mediaUrl: update.mediaUrl,
        userName: update.userName,
        type: update.type
      });

      const result = await mediaDownloadService.downloadMedia(
        update.mediaUrl,
        updateId,
        update.userName,
        update.caption,
        (progress) => {
          console.log('📊 Download progress:', updateId, Math.round(progress.progress) + '%');
          setDownloadProgress(prev => ({ ...prev, [updateId]: Math.round(progress.progress) }));
        }
      );

      console.log('📥 Download result:', result);

      if (result.success) {
        // Record download in Firebase with complete sync
        try {
          const userName = currentUser.displayName || currentUser.email || 'Unknown';
          const success = await updateInteractionsService.downloadUpdate(updateId, currentUser.id, userName);
          if (success) {
            // Update local state for real-time UI update
            setUpdates(prev => prev.map(u => {
              if (u.id === updateId) {
                return { ...u, downloads: [...u.downloads, currentUser.id] };
              }
              return u;
            }));

            // Save updated state to local storage for persistence
            const updatedUpdate = updates.find(u => u.id === updateId);
            if (updatedUpdate) {
              const convertedUpdate = convertRealUpdateToUpdate({
                ...updatedUpdate,
                downloads: [...updatedUpdate.downloads, currentUser.id]
              });
              await localUpdatesStorage.saveUpdate(convertedUpdate);
            }
            console.log('✅ Download tracked and synced to Firebase');
          }
        } catch (error) {
          console.warn('⚠️ Failed to record download in Firebase (offline?):', error);
        }

        // Navigate to downloads page after successful download (unless skipped for auto-download)
        if (!skipNavigation) {
          console.log('✅ Download successful, navigating to downloads page');
          // Use setTimeout to ensure video continues playing during download
          // and only pauses when downloads page actually opens
          setTimeout(() => {
            router.push('/downloaded-media');
          }, 100);
        } else {
          console.log('✅ Auto-download successful, media saved to downloads');
        }
      } else {
        console.error('❌ Download failed:', result.error);
        Alert.alert('Download Failed', result.error || 'Failed to download media. Please check your storage permissions and try again.');
      }
    } catch (error: any) {
      console.error('❌ Error downloading media:', error);
      const errorMessage = error?.message || error?.toString() || 'Unknown error occurred';
      Alert.alert('Download Error', `Failed to download media: ${errorMessage}\n\nPlease check:\n• Storage permissions\n• Available storage space\n• Network connection`);
    } finally {
      // Clean up download tracking
      setDownloadingUpdates(prev => {
        const newSet = new Set(prev);
        newSet.delete(updateId);
        return newSet;
      });
      setDownloadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[updateId];
        return newProgress;
      });
    }
  };

  // Handle interaction page opening
  const openInteractionPage = (updateId: string, tab: 'likes' | 'views' | 'shares' | 'downloads') => {
    setSelectedUpdateId(updateId);
    setSelectedInteractionTab(tab);
    setShowInteractionPages(true);
  };

  // Handle user profile navigation
  const handleUserPress = (userId: string) => {
    // Navigate to user profile using router
    router.push(`/profile/${userId}`);
    setShowInteractionPages(false);
  };

  // ==================== REMOTE SEARCH FUNCTIONALITY ====================

  // Search remote updates from backend
  const searchRemoteUpdates = useCallback(async (searchQuery: string): Promise<RealUpdate[]> => {
    try {
      if (!searchQuery.trim() || searchQuery.length < 2) return [];

      console.log('🔍 Searching remote updates for:', searchQuery);

      // Use Firebase query to search updates by caption and username
      const updatesRef = collection(db, 'updates');
      const searchQueries = [
        // Search by caption
        query(
          updatesRef,
          where('privacy', '==', 'public'),
          where('caption', '>=', searchQuery),
          where('caption', '<=', searchQuery + '\uf8ff'),
          orderBy('caption'),
          orderBy('timestamp', 'desc'),
          limit(10)
        ),
        // Search by username
        query(
          updatesRef,
          where('privacy', '==', 'public'),
          where('userName', '>=', searchQuery),
          where('userName', '<=', searchQuery + '\uf8ff'),
          orderBy('userName'),
          orderBy('timestamp', 'desc'),
          limit(10)
        )
      ];

      const searchPromises = searchQueries.map(async (q) => {
        try {
          const snapshot = await getDocs(q);
          return snapshot.docs.map(doc => {
            const data = doc.data();
            return {
              id: doc.id,
              ...data,
              timestamp: data.timestamp?.toDate() || new Date(),
              expiresAt: data.expiresAt?.toDate(),
            } as RealUpdate;
          });
        } catch (error) {
          console.warn('Search query failed:', error);
          return [];
        }
      });

      const results = await Promise.all(searchPromises);
      const allResults = results.flat();

      // Remove duplicates and filter out expired content
      const uniqueResults = allResults.filter((update, index, self) => {
        const isUnique = self.findIndex(u => u.id === update.id) === index;
        const isNotExpired = !update.expiresAt || update.expiresAt > new Date();
        return isUnique && isNotExpired;
      });

      console.log('🔍 Remote search found:', uniqueResults.length, 'unique results');
      return uniqueResults;
    } catch (error) {
      console.error('❌ Remote search error:', error);
      return [];
    }
  }, []);

  // ==================== LONG PRESS CONTEXT MENU HANDLERS ====================

  // Handle video long press
  const handleVideoLongPress = useCallback(async (update: RealUpdate) => {
    console.log('🔥🔥🔥 HANDLE VIDEO LONG PRESS CALLED:', update.id);
    console.log('🔥🔥🔥 Current states:', { showContextMenu, contextMenuUpdate: contextMenuUpdate?.id });

    try {
      // Show context menu without pausing video or haptic feedback
      setContextMenuUpdate(update);
      setShowContextMenu(true);
      console.log('🔥 Context menu state set to true');

      console.log('📱 Long press detected on video:', update.id);
    } catch (error) {
      console.error('❌ Error handling video long press:', error);
    }
  }, [showContextMenu, contextMenuUpdate]);

  // Close context menu
  const closeContextMenu = useCallback(() => {
    setShowContextMenu(false);
    setContextMenuUpdate(null);
    // Don't pause/resume video - let it continue playing
  }, []);

  // Save/Download functionality removed from context menu - available in stacked buttons

  // Not Interested - Hide post
  const handleContextNotInterested = useCallback(async () => {
    if (!contextMenuUpdate) return;

    try {
      // Add to hidden updates
      setHiddenUpdates(prev => new Set(prev).add(contextMenuUpdate.id));

      // Remove from current updates list
      setUpdates(prev => prev.filter(u => u.id !== contextMenuUpdate.id));

      Alert.alert('Hidden', 'Post hidden from your feed');
      console.log('👁️ Post marked as not interested:', contextMenuUpdate.id);
    } catch (error) {
      console.error('❌ Error hiding post:', error);
      Alert.alert('Error', 'Failed to hide post');
    }
  }, [contextMenuUpdate]);

  // Report post
  const handleContextReport = useCallback(async () => {
    if (!contextMenuUpdate) return;

    try {
      Alert.alert(
        'Report Post',
        'Why are you reporting this post?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Inappropriate Content',
            onPress: () => submitReport('inappropriate')
          },
          {
            text: 'Spam',
            onPress: () => submitReport('spam')
          },
          {
            text: 'Harassment',
            onPress: () => submitReport('harassment')
          },
          {
            text: 'Copyright',
            onPress: () => submitReport('copyright')
          },
        ]
      );
    } catch (error) {
      console.error('❌ Error reporting post:', error);
    }
  }, [contextMenuUpdate]);

  // Submit report
  const submitReport = useCallback(async (reason: string) => {
    if (!contextMenuUpdate || !currentUser?.id) return;

    try {
      // Import Firebase functions
      const { getFunctions, httpsCallable } = await import('firebase/functions');
      const functions = getFunctions();
      const handleReportedContent = httpsCallable(functions, 'handleReportedContent');

      // Submit report to Firebase Cloud Function
      await handleReportedContent({
        messageId: contextMenuUpdate.id,
        chatId: 'updates_feed', // Special chat ID for updates
        reason,
        description: `Update reported: ${contextMenuUpdate.caption || 'No caption'}`,
      });

      console.log('🚨 Report submitted successfully:', {
        postId: contextMenuUpdate.id,
        reporterId: currentUser.id,
        reason,
        timestamp: new Date().toISOString(),
      });

      // Also hide the post locally for the reporter
      setHiddenUpdates(prev => new Set(prev).add(contextMenuUpdate.id));
      setUpdates(prev => prev.filter(u => u.id !== contextMenuUpdate.id));

      Alert.alert('Thank You', 'Your report has been submitted and will be reviewed. The post has been hidden from your feed.');
    } catch (error) {
      console.error('❌ Error submitting report:', error);
      Alert.alert('Error', 'Failed to submit report. Please try again.');
    }
  }, [contextMenuUpdate, currentUser]);

  // Toggle auto scroll
  const handleContextAutoScroll = useCallback(async () => {
    try {
      setAutoScrollEnabled(prev => !prev);

      const newState = !autoScrollEnabled;
      Alert.alert(
        'Auto Scroll',
        newState ? 'Auto scroll enabled - videos will automatically advance when finished' : 'Auto scroll disabled'
      );

      console.log('🔄 Auto scroll toggled:', newState);
    } catch (error) {
      console.error('❌ Error toggling auto scroll:', error);
    }
  }, [autoScrollEnabled]);

  // Copy link to clipboard
  const handleContextCopyLink = useCallback(async () => {
    if (!contextMenuUpdate) return;

    try {
      // Generate shareable link
      const shareableLink = `https://irachat.app/update/${contextMenuUpdate.id}`;
      await Clipboard.setStringAsync(shareableLink);

      Alert.alert('Copied', 'Link copied to clipboard');
      console.log('🔗 Link copied:', shareableLink);
    } catch (error) {
      console.error('❌ Error copying link:', error);
      Alert.alert('Error', 'Failed to copy link');
    }
  }, [contextMenuUpdate]);

  // Toggle auto download
  const handleContextAutoDownload = useCallback(async () => {
    try {
      setAutoDownloadEnabled(prev => !prev);

      const newState = !autoDownloadEnabled;
      Alert.alert(
        'Auto Download',
        newState ? 'Auto download enabled - new videos will be automatically saved' : 'Auto download disabled'
      );

      console.log('📥 Auto download toggled:', newState);
    } catch (error) {
      console.error('❌ Error toggling auto download:', error);
    }
  }, [autoDownloadEnabled]);

  // Delete post (owner only)
  const handleContextDelete = useCallback(async () => {
    if (!contextMenuUpdate || !currentUser?.id) return;

    // Verify ownership
    if (contextMenuUpdate.userId !== currentUser.id) {
      Alert.alert('Error', 'You can only delete your own posts');
      return;
    }

    try {
      Alert.alert(
        'Delete Post',
        'Are you sure you want to delete this post? This action cannot be undone.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: async () => {
              await handleDeleteUpdate(contextMenuUpdate.id, contextMenuUpdate.userId);
            }
          }
        ]
      );
    } catch (error) {
      console.error('❌ Error deleting post:', error);
    }
  }, [contextMenuUpdate, currentUser]);

  // Full screen landscape view
  const handleContextFullScreen = useCallback(async () => {
    if (!contextMenuUpdate) return;

    try {
      // Set full screen update and show modal
      setFullScreenUpdate(contextMenuUpdate);
      setIsFullScreenMode(true);

      // Lock to landscape orientation for full screen
      try {
        const ScreenOrientation = await import('expo-screen-orientation');

        // Try different API versions and orientations
        if (ScreenOrientation.lockAsync && ScreenOrientation.OrientationLock) {
          // New API
          try {
            await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE_LEFT);
            console.log('🔄 Locked to landscape orientation (new API)');
          } catch (newApiError) {
            // Try landscape right
            await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.LANDSCAPE_RIGHT);
            console.log('🔄 Locked to landscape right orientation (new API)');
          }
        } else if (ScreenOrientation.lockAsync) {
          // Try with string values (older API)
          await (ScreenOrientation.lockAsync as any)('LANDSCAPE');
          console.log('🔄 Locked to landscape orientation (legacy API)');
        } else {
          console.warn('⚠️ Screen orientation API not available');
        }
      } catch (orientationError) {
        console.warn('⚠️ Screen orientation lock failed:', orientationError);
        // Continue without orientation lock - full screen will still work
      }

      console.log('🖥️ Entering full screen mode for:', contextMenuUpdate.id);
    } catch (error) {
      console.error('❌ Error entering full screen:', error);
      Alert.alert('Error', 'Failed to enter full screen mode');
    }
  }, [contextMenuUpdate]);

  // Exit full screen mode
  const exitFullScreenMode = useCallback(async () => {
    try {
      // Unlock orientation back to portrait
      try {
        const ScreenOrientation = await import('expo-screen-orientation');

        // Try different API versions
        if (ScreenOrientation.unlockAsync) {
          // Try unlock first (preferred)
          await ScreenOrientation.unlockAsync();
          console.log('🔄 Unlocked orientation to default');
        } else if (ScreenOrientation.lockAsync && ScreenOrientation.OrientationLock) {
          // Lock to portrait if unlock not available
          try {
            await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT_UP);
            console.log('🔄 Locked to portrait orientation');
          } catch (portraitError) {
            // Try default portrait
            await ScreenOrientation.lockAsync(ScreenOrientation.OrientationLock.PORTRAIT);
            console.log('🔄 Locked to default portrait orientation');
          }
        } else if (ScreenOrientation.lockAsync) {
          // Legacy API
          await (ScreenOrientation.lockAsync as any)('PORTRAIT');
          console.log('🔄 Locked to portrait orientation (legacy API)');
        } else {
          console.warn('⚠️ Screen orientation API not available for unlock');
        }
      } catch (orientationError) {
        console.warn('⚠️ Screen orientation unlock failed:', orientationError);
        // Continue - app will still work without orientation lock
      }

      setIsFullScreenMode(false);
      setFullScreenUpdate(null);

      console.log('📱 Exiting full screen mode');
    } catch (error) {
      console.error('❌ Error exiting full screen:', error);
    }
  }, []);

  // Handle back button in full screen mode
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (isFullScreenMode) {
        exitFullScreenMode();
        return true; // Prevent default back behavior
      }
      return false; // Allow default back behavior
    });

    return () => backHandler.remove();
  }, [isFullScreenMode, exitFullScreenMode]);

  // Auto-scroll functionality - advance to next video when current one ends
  useEffect(() => {
    if (!autoScrollEnabled || !currentlyPlayingVideo) return;

    const checkVideoProgress = () => {
      const player = videoProgressManager.getPlayer(currentlyPlayingVideo);
      if (player && player.currentTime && player.duration) {
        const progress = player.currentTime / player.duration;

        // If video is 95% complete, auto-scroll to next (balanced timing)
        if (progress >= 0.95) {
          const currentIndex = visibleUpdates.findIndex(u => u.id === currentlyPlayingVideo);
          if (currentIndex >= 0 && currentIndex < visibleUpdates.length - 1) {
            // Scroll to next video
            flatListRef.current?.scrollToIndex({
              index: currentIndex + 1,
              animated: true,
            });
            console.log('🔄 Auto-scrolling to next video');
          }
        }
      }
    };

    // Check progress every second when auto-scroll is enabled
    const interval = setInterval(checkVideoProgress, 1000);
    return () => clearInterval(interval);
  }, [autoScrollEnabled, currentlyPlayingVideo, updates]);

  // Filter updates to exclude hidden ones (Not Interested)
  const visibleUpdates = React.useMemo(() => {
    return updates.filter(update => !hiddenUpdates.has(update.id));
  }, [updates, hiddenUpdates]);

  // Load saved settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const savedAutoScroll = await AsyncStorage.getItem('autoScrollEnabled');
        const savedAutoDownload = await AsyncStorage.getItem('autoDownloadEnabled');
        const savedHiddenUpdates = await AsyncStorage.getItem('hiddenUpdates');

        if (savedAutoScroll !== null) {
          setAutoScrollEnabled(JSON.parse(savedAutoScroll));
        }
        if (savedAutoDownload !== null) {
          setAutoDownloadEnabled(JSON.parse(savedAutoDownload));
        }
        if (savedHiddenUpdates !== null) {
          setHiddenUpdates(new Set(JSON.parse(savedHiddenUpdates)));
        }
      } catch (error) {
        console.warn('⚠️ Error loading settings:', error);
      }
    };

    loadSettings();
  }, []);

  // Save settings when they change
  useEffect(() => {
    AsyncStorage.setItem('autoScrollEnabled', JSON.stringify(autoScrollEnabled));
  }, [autoScrollEnabled]);

  useEffect(() => {
    AsyncStorage.setItem('autoDownloadEnabled', JSON.stringify(autoDownloadEnabled));
  }, [autoDownloadEnabled]);

  useEffect(() => {
    AsyncStorage.setItem('hiddenUpdates', JSON.stringify(Array.from(hiddenUpdates)));
  }, [hiddenUpdates]);

  // Load media with pagination for better performance
  const loadRecentMedia = async (loadMore = false) => {
    if (!loadMore) {
      setIsLoadingRecentMedia(true);
      setRecentMedia([]); // Clear existing data
      recentMediaService.resetPagination(); // Reset pagination
    }

    try {
      const media = loadMore
        ? await recentMediaService.getMoreMedia(20) // Load more with pagination
        : await recentMediaService.getRecentMedia(20); // Initial load

      if (loadMore) {
        setRecentMedia(prev => {
          // Deduplicate media items by ID
          const existingIds = new Set(prev.map(item => item.id));
          const newItems = media.filter(item => !existingIds.has(item.id));
          return [...prev, ...newItems];
        });
      } else {
        setRecentMedia(media);
      }
    } catch (error) {
      console.error('❌ Error loading media:', error);
    } finally {
      setIsLoadingRecentMedia(false);
    }
  };

  // Organize media: balanced grid with videos prioritized in first column
  const organizeMediaForGrid = (media: RecentMediaItem[]) => {
    if (media.length === 0) return [];

    // Separate videos and photos
    const videos = media.filter(item => item.mediaType === 'video');
    const photos = media.filter(item => item.mediaType === 'photo');

    // Create a balanced distribution across 3 columns
    const totalItems = media.length;
    const itemsPerColumn = Math.ceil(totalItems / 3);

    // Create three columns
    const column1: RecentMediaItem[] = [];
    const column2: RecentMediaItem[] = [];
    const column3: RecentMediaItem[] = [];

    // Fill column 1 with videos first, then photos if needed
    let videoIndex = 0;
    let photoIndex = 0;

    // Column 1: Videos first, then photos to fill
    while (column1.length < itemsPerColumn && (videoIndex < videos.length || photoIndex < photos.length)) {
      if (videoIndex < videos.length) {
        column1.push(videos[videoIndex]);
        videoIndex++;
      } else if (photoIndex < photos.length) {
        column1.push(photos[photoIndex]);
        photoIndex++;
      }
    }

    // Column 2: Remaining photos and videos
    while (column2.length < itemsPerColumn && (videoIndex < videos.length || photoIndex < photos.length)) {
      if (photoIndex < photos.length) {
        column2.push(photos[photoIndex]);
        photoIndex++;
      } else if (videoIndex < videos.length) {
        column2.push(videos[videoIndex]);
        videoIndex++;
      }
    }

    // Column 3: Remaining items
    while (column3.length < itemsPerColumn && (videoIndex < videos.length || photoIndex < photos.length)) {
      if (photoIndex < photos.length) {
        column3.push(photos[photoIndex]);
        photoIndex++;
      } else if (videoIndex < videos.length) {
        column3.push(videos[videoIndex]);
        videoIndex++;
      }
    }

    // Create rows from columns
    const rows: (RecentMediaItem | null)[][] = [];
    const maxColumnLength = Math.max(column1.length, column2.length, column3.length);

    for (let i = 0; i < maxColumnLength; i++) {
      const row: (RecentMediaItem | null)[] = [
        column1[i] || null,
        column2[i] || null,
        column3[i] || null,
      ];
      rows.push(row);
    }

    return rows;
  };

  // Render a row of media items
  const renderMediaRow = ({ item: row, index }: { item: (RecentMediaItem | null)[], index: number }) => (
    <View style={styles.whatsappMediaRow}>
      {row.map((item, columnIndex) => (
        <View key={`row_${index}_col_${columnIndex}`} style={styles.whatsappMediaColumn}>
          {item ? (
            <TouchableOpacity
              style={styles.whatsappGalleryItem}
              onPress={() => {
                // Handle media selection - proceed to editing flow
                setShowCameraOptions(false);
                setCaptionModalData({
                  mediaUri: item.uri,
                  type: item.mediaType,
                  isStory: false,
                });

                // Show appropriate editor based on media type
                if (item.mediaType === 'video') {
                  setShowVideoTrimmer(true);
                } else {
                  setShowPhotoCropper(true);
                }
              }}
            >
              <Image
                source={{ uri: item.uri }}
                style={styles.whatsappGalleryImage}
                resizeMode="cover"
              />
              {/* Video duration overlay */}
              {item.mediaType === 'video' && item.duration && (
                <View style={styles.whatsappVideoDurationOverlay}>
                  <Text style={styles.whatsappVideoDurationText}>
                    {recentMediaService.formatDuration(item.duration)}
                  </Text>
                </View>
              )}
              {/* Media type icon */}
              <View style={styles.whatsappMediaTypeIcon}>
                <Ionicons
                  name={item.mediaType === 'video' ? 'videocam' : 'image'}
                  size={12}
                  color="#FFFFFF"
                />
              </View>
            </TouchableOpacity>
          ) : (
            <View style={styles.whatsappGalleryItem} />
          )}
        </View>
      ))}
    </View>
  );

  // Enhanced camera option handler with better error handling and flow
  const handleCameraOption = async (option: 'camera' | 'gallery' | 'video' | 'text') => {
    // CRITICAL: Check authentication before proceeding
    if (!currentUser?.id) {
      console.error('❌ User not authenticated in handleCameraOption');
      Alert.alert('Authentication Error', 'Please sign in again to continue.');
      return;
    }

    setShowCameraOptions(false);

    try {
      switch (option) {
        case 'camera':
          await handleEnhancedCameraCapture('photo');
          break;
        case 'gallery':
          await handleEnhancedGalleryPicker();
          break;
        case 'video':
          await handleEnhancedCameraCapture('video');
          break;
        case 'text':
          // Open text update creator
          setShowTextUpdateCreator(true);
          break;
        default:
          console.warn('⚠️ Unknown camera option:', option);
      }
    } catch (error) {
      console.error('❌ Error handling camera option:', error);
      Alert.alert('Error', 'Something went wrong. Please try again.');
    }
  };

  // Enhanced camera capture with better error handling
  const handleEnhancedCameraCapture = async (type: 'photo' | 'video') => {
    try {
      console.log(`📷 Starting enhanced ${type} capture...`);

      // Request camera permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Camera Permission Required',
          'Please allow camera access to take photos and videos.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {
              Alert.alert('Settings', 'Please enable camera permission in your device settings.');
            }}
          ]
        );
        return;
      }

      console.log('✅ Camera permission granted');

      // Launch camera with enhanced options
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: type === 'video' ? ['videos'] : ['images'],
        allowsEditing: false, // We'll handle editing in our own flow
        quality: type === 'video' ? 0.8 : 1.0,
        videoMaxDuration: type === 'video' ? 60 : undefined, // 60 seconds max for videos
        aspect: [9, 16], // Story aspect ratio
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📷 Camera capture cancelled');
        return;
      }

      const asset = result.assets[0];
      console.log(`✅ ${type} captured:`, asset.uri);

      // Set up caption modal data
      setCaptionModalData({
        mediaUri: asset.uri,
        type: asset.type === 'video' ? 'video' : 'photo',
        isStory: false,
      });

      // Show appropriate editor based on media type
      if (asset.type === 'video') {
        setShowVideoTrimmer(true);
      } else {
        setShowPhotoCropper(true);
      }

    } catch (error) {
      console.error(`❌ Error capturing ${type}:`, error);
      Alert.alert(
        'Camera Error',
        `Failed to capture ${type}. Please check your camera permissions and try again.`,
        [{ text: 'OK' }]
      );
    }
  };

  // Enhanced gallery picker with better error handling
  const handleEnhancedGalleryPicker = async () => {
    try {
      console.log('🖼️ Starting enhanced gallery picker...');

      // Request media library permissions
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Gallery Permission Required',
          'Please allow gallery access to select photos and videos.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => {
              Alert.alert('Settings', 'Please enable gallery permission in your device settings.');
            }}
          ]
        );
        return;
      }

      console.log('✅ Gallery permission granted');

      // Launch gallery with enhanced options
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'],
        allowsEditing: false, // We'll handle editing in our own flow
        quality: 1.0,
        allowsMultipleSelection: false, // For now, single selection
        aspect: [9, 16], // Story aspect ratio
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('🖼️ Gallery selection cancelled');
        return;
      }

      const asset = result.assets[0];
      console.log('✅ Media selected from gallery:', asset.uri);

      // Set up caption modal data
      setCaptionModalData({
        mediaUri: asset.uri,
        type: asset.type === 'video' ? 'video' : 'photo',
        isStory: false,
      });

      // Show appropriate editor based on media type
      if (asset.type === 'video') {
        setShowVideoTrimmer(true);
      } else {
        setShowPhotoCropper(true);
      }

    } catch (error) {
      console.error('❌ Error selecting from gallery:', error);
      Alert.alert(
        'Gallery Error',
        'Failed to select media from gallery. Please check your gallery permissions and try again.',
        [{ text: 'OK' }]
      );
    }
  };

  // TikTok-style media handlers
  const handleTikTokMediaSelected = (uri: string, type: 'photo' | 'video') => {
    setSelectedMediaForPreview({ uri, type });
    setShowTikTokMediaPicker(false);
    setShowMediaPreviewEditor(true);
  };

  const handleTikTokCameraCapture = (uri: string, type: 'photo' | 'video') => {
    setSelectedMediaForPreview({ uri, type });
    setShowTikTokCamera(false);
    setShowMediaPreviewEditor(true);
  };

  const handleTikTokCameraPress = () => {
    setShowTikTokMediaPicker(false);
    setShowTikTokCamera(true);
  };

  const handleTikTokTextPress = () => {
    setShowTikTokMediaPicker(false);
    setShowTextUpdateCreator(true);
  };

  const handleMediaPreviewPost = async (caption: string, audioCaption?: AudioCaption, textOverlays?: TextOverlay[]) => {
    if (!selectedMediaForPreview || isPostingUpdate) return;

    setIsPostingUpdate(true);
    try {
      await createUpdate(selectedMediaForPreview.uri, selectedMediaForPreview.type, caption, audioCaption, textOverlays);

      // Close all modals and reset state
      setShowMediaPreviewEditor(false);
      setSelectedMediaForPreview(null);

      // Resume video playback after successful post
      setTimeout(() => {
        setGlobalVideoPause(false);
        // Resume video at current scroll position instead of first video
        const currentIndex = Math.round(currentScrollPosition / SCREEN_HEIGHT);
        const currentUpdate = updates[currentIndex];

        if (currentUpdate && currentUpdate.type === 'video') {
          console.log('🎯 Resuming video at current position after post:', currentUpdate.id);
          setCurrentlyPlayingVideo(currentUpdate.id);
        } else {
          // If current position doesn't have video, don't force play anything
          console.log('🎯 Current position has no video after post, keeping paused');
        }
      }, 300);

      // Show success message
      Alert.alert('Success!', 'Your update has been posted');
    } catch (error) {
      console.error('Error posting update:', error);
      Alert.alert('Error', 'Failed to post update. Please try again.');
    } finally {
      setIsPostingUpdate(false);
    }
  };

  const handleTextUpdatePost = async (text: string) => {
    if (!text.trim() || isPostingUpdate) return;

    setIsPostingUpdate(true);
    try {
      await createUpdate('', 'text', text);

      // Close modal
      setShowTextUpdateCreator(false);

      // Resume video playback after successful post
      setTimeout(() => {
        setGlobalVideoPause(false);
        // Auto-play the first video if available
        if (updates.length > 0) {
          const firstVideo = updates.find(update => update.type === 'video');
          const allPlayers = videoProgressManager.getAllPlayers();
          if (firstVideo && allPlayers.has(firstVideo.id)) {
            setCurrentlyPlayingVideo(firstVideo.id);
          }
        }
      }, 300);

      // Show success message
      Alert.alert('Success!', 'Your text story has been posted');
    } catch (error) {
      console.error('Error posting text story:', error);
      Alert.alert('Error', 'Failed to post text story. Please try again.');
    } finally {
      setIsPostingUpdate(false);
    }
  };

  const handleCloseMediaPreview = () => {
    if (isPostingUpdate) return;
    setShowMediaPreviewEditor(false);
    setSelectedMediaForPreview(null);
  };

  const handleCloseTextCreator = () => {
    if (isPostingUpdate) return;
    setShowTextUpdateCreator(false);
  };

  // Open camera
  const openCamera = async (type: 'photo' | 'video') => {
    try {
      // CRITICAL: Pause all videos before opening camera

      setGlobalVideoPause(true);
      setCurrentlyPlayingVideo(null);

      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera permission to take photos/videos.');
        setGlobalVideoPause(false); // Resume videos if permission denied
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: type === 'photo' ? ['images'] : ['videos'],
        allowsEditing: false, // Disable automatic cropping
        quality: 1.0, // Full quality to preserve original dimensions
        videoMaxDuration: 600, // 10 minutes max for videos (user preference)
        exif: false, // Don't include EXIF data for privacy
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        // Detect actual media type from the asset
        const actualType = asset.type === 'video' ? 'video' : 'photo';

        // CRITICAL: Prevent navigation issues by ensuring user is still authenticated
        if (!currentUser?.id) {
          console.error('❌ User not authenticated after camera capture');
          Alert.alert('Authentication Error', 'Please sign in again to continue.');
          setGlobalVideoPause(false);
          return;
        }

        showCreateUpdateModal(asset.uri, actualType);
        // Don't resume videos yet - modal is opening
      } else {
        // Camera cancelled - resume videos
        setGlobalVideoPause(false);
      }
    } catch (error) {
      console.error('❌ Error opening camera:', error);
      Alert.alert('Error', 'Failed to open camera');
      // Resume videos on error
      setGlobalVideoPause(false);
    }
  };

  // Open gallery
  const openGallery = async (type: 'photo' | 'video' | 'both') => {
    try {
      // CRITICAL: Pause all videos before opening gallery
      console.log('🖼️ Opening gallery - pausing all videos');
      setGlobalVideoPause(true);
      setCurrentlyPlayingVideo(null);

      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant photo library permission to select media.');
        setGlobalVideoPause(false); // Resume videos if permission denied
        return;
      }

      // Determine media types based on parameter (using new format)
      const mediaTypes = type === 'photo'
        ? ['images']
        : type === 'video'
        ? ['videos']
        : ['images', 'videos']; // 'both' will show all media
      console.log('🖼️ Opening gallery with media types:', mediaTypes, 'for type:', type);

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: mediaTypes as any,
        allowsEditing: false, // Disable automatic cropping
        quality: 1.0, // Full quality to preserve original dimensions
        exif: false, // Don't include EXIF data for privacy
        videoQuality: 1.0, // Best video quality (0.0 to 1.0)
        videoMaxDuration: 600, // 10 minutes max for videos
        allowsMultipleSelection: false, // Single selection only
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        // Detect actual media type from the asset
        const actualType = asset.type === 'video' ? 'video' : 'photo';
        console.log('📱 Gallery asset type:', asset.type, 'Detected as:', actualType);

        // CRITICAL: Prevent navigation issues by ensuring user is still authenticated
        if (!currentUser?.id) {
          console.error('❌ User not authenticated after gallery selection');
          Alert.alert('Authentication Error', 'Please sign in again to continue.');
          setGlobalVideoPause(false);
          return;
        }

        showCreateUpdateModal(asset.uri, actualType);
        // Don't resume videos yet - modal is opening
      } else {
        // Gallery cancelled - resume videos
        console.log('🖼️ Gallery cancelled - resuming videos');
        setGlobalVideoPause(false);
      }
    } catch (error) {
      console.error('❌ Error opening gallery:', error);
      Alert.alert('Error', 'Failed to open gallery');
      // Resume videos on error
      setGlobalVideoPause(false);
    }
  };

  // Show create update modal with editing
  const showCreateUpdateModal = (mediaUri: string, type: 'photo' | 'video', isStory: boolean = false) => {
    // Immediately pause all videos when opening editing modal
    setGlobalVideoPause(true);
    setCurrentlyPlayingVideo(null);

    // Set up caption modal data for later use
    setCaptionModalData({ mediaUri, type, isStory });
    setCaptionText('');

    // Show appropriate editing modal
    if (type === 'video') {
      // Temporarily skip video trimmer to avoid conflicts
      setShowCaptionModal(true);
    } else {
      setShowPhotoCropper(true);
    }
  };

  // Handle video trim completion
  const handleVideoTrimComplete = (_trimmedUri: string, _startTime: number, _endTime: number) => {
    // TODO: Implement actual video trimming with expo-av
    // For now, just proceed to caption modal
    setShowVideoTrimmer(false);
    setShowCaptionModal(true);
  };

  // Handle photo crop completion
  const handlePhotoCropComplete = (croppedUri: string) => {
    // Update the media URI with cropped version
    if (captionModalData) {
      setCaptionModalData({ ...captionModalData, mediaUri: croppedUri });
    }
    setShowPhotoCropper(false);
    setShowCaptionModal(true);
  };

  // Handle editing cancellation
  const handleEditingCancel = () => {
    setShowVideoTrimmer(false);
    setShowPhotoCropper(false);
    setCaptionModalData(null);
    setCaptionText('');
    setTimeout(() => setGlobalVideoPause(false), 100);
  };

  // Handle caption submission
  const handleCaptionSubmit = async () => {
    if (!captionModalData) return;

    const { mediaUri, type } = captionModalData;

    // FIRST: Close modal and reset state to prevent UI issues
    setShowCaptionModal(false);
    setCaptionModalData(null);
    const captionToPost = captionText;
    setCaptionText('');

    // THEN: Create the update (this will add it to the feed)
    try {
      await createUpdate(mediaUri, type, captionToPost);

      // Resume videos after successful post
      setGlobalVideoPause(false);

      // Resume video at current scroll position after posting
      setTimeout(() => {
        const currentIndex = Math.round(currentScrollPosition / SCREEN_HEIGHT);
        const currentUpdate = updates[currentIndex];

        if (currentUpdate && currentUpdate.type === 'video') {
          console.log('🎯 Resuming video at current position after caption post:', currentUpdate.id);
          setCurrentlyPlayingVideo(currentUpdate.id);
        } else {
          // If current position doesn't have video, don't force play anything
          console.log('🎯 Current position has no video after caption post, keeping paused');
          setCurrentlyPlayingVideo(null);
        }
      }, 300); // Longer delay to ensure update list is refreshed
    } catch (error) {
      // If posting fails, still resume videos
      setGlobalVideoPause(false);
    }
  };

  // Create offline update
  const createOfflineUpdate = async (mediaUri: string, type: UpdateType, caption: string) => {
    if (!currentUser?.id) return { success: false, error: 'No user found' };

    try {
      const updateId = `offline_${Date.now()}_${currentUser.id}`;

      // Create offline update object for display
      const offlineUpdate: RealUpdate = {
        id: updateId,
        userId: currentUser.id,
        userName: currentUser.name || 'Unknown',
        userAvatar: currentUser.avatar,
        content: caption,
        caption,
        type,
        mediaUrl: mediaUri, // Store local URI for offline
        privacy: 'public',
        timestamp: new Date(),
        likes: [],
        comments: [],
        shares: [],
        views: [],
        downloads: [],
        location: undefined,
        tags: [],
        mentions: [],
        musicTrack: undefined,
      };

      // Convert to Update format for local storage
      const updateForStorage: Omit<Update, 'id'> & { id?: string } = {
        id: updateId,
        userId: currentUser.id,
        userName: currentUser.name || 'Unknown',
        userAvatar: currentUser.avatar,
        type: type === 'photo' ? 'image' : type,
        caption,
        timestamp: new Date(),
        isStory: false,
        expiresAt: undefined,
        privacy: 'public',
        isVisible: true,
        isArchived: false,
        location: undefined,
        hashtags: [],
        mentions: [],
        groupTags: [],
        media: mediaUri ? [{
          id: `media_${updateId}`,
          url: mediaUri,
          type: type === 'photo' ? 'image' : 'video',
          thumbnailUrl: undefined,
          width: undefined,
          height: undefined,
          duration: undefined,
          size: undefined,
        }] : [],
        likes: [],
        views: [],
        shares: [],
        downloads: [],
        reactions: [],
        comments: [],
        viewCount: 0,
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        downloadCount: 0,
        isLikedByCurrentUser: false,
        isViewedByCurrentUser: false,
        isSharedByCurrentUser: false,
        isDownloadedByCurrentUser: false,
        isReported: false,
        reportCount: 0,
        isFlagged: false,
        isPinned: false,
        isHighlight: false,
      };

      // Store in local storage for immediate display
      await localUpdatesStorage.saveUpdate(updateForStorage);

      // Queue for automatic sync when connection is restored
      await updatesSyncService.queueForSync(updateForStorage as Update);

      // Add to current updates list for immediate UI update - FORCE IMMEDIATE DISPLAY
      setUpdates(prevUpdates => {
        // Make sure we don't duplicate if it already exists
        const filtered = prevUpdates.filter(u => u.id !== updateId);
        return [offlineUpdate, ...filtered];
      });

      console.log('✅ Offline update added to UI and queued for sync:', updateId);
      return { success: true, updateId };
    } catch (error) {
      console.error('❌ Error creating offline update:', error);
      return { success: false, error: 'Failed to save offline' };
    }
  };

  // Enhanced create update with comprehensive upload pipeline
  const createUpdate = async (mediaUri: string, type: UpdateType, caption: string, audioCaption?: AudioCaption, textOverlays?: TextOverlay[]) => {
    if (!currentUser?.id) return;

    try {
      setIsLoading(true);
      setIsUploading(true);

      // Check network status first
      if (!isOnline) {
        // Handle offline creation immediately
        const offlineResult = await createOfflineUpdate(mediaUri, type, caption);
        if (offlineResult.success) {
          Alert.alert('Saved Offline', 'Your story will be posted when you\'re back online');
          setShowCreateModal(false);
          // Don't call loadUpdates() - we already added it to state directly
        } else {
          Alert.alert('Error', 'Failed to save story offline');
        }
        return;
      }

      // For text updates, use the existing service
      if (type === 'text') {
        try {
          const result = await realUpdatesService.createUpdate(
            currentUser.id,
            currentUser.name || 'Unknown',
            currentUser.avatar,
            {
              type,
              caption,
              privacy: 'public',
            }
          );

          if (result.success) {
            console.log('✅ Text update created successfully');
            await loadUpdates();
            setShowCreateModal(false);
          } else {
            Alert.alert('Error', 'Failed to create text update');
          }
        } catch (error) {
          console.error('❌ Error creating text update:', error);
          Alert.alert('Error', 'Failed to create text update');
        }
        return;
      }

      // For media updates, use enhanced Firebase upload service
      if (type === 'photo' || type === 'video') {
        try {
          console.log(`🚀 Starting enhanced ${type} upload...`);

          // Extract hashtags and mentions from caption
          const hashtags = caption.match(/#\w+/g)?.map(tag => tag.substring(1)) || [];
          const mentions = caption.match(/@\w+/g)?.map(mention => mention.substring(1)) || [];

          // Upload with progress tracking
          const postId = await enhancedFirebaseUploadService.uploadMedia(
            mediaUri,
            type,
            currentUser.id,
            caption,
            hashtags,
            mentions,
            {
              compress: true,
              quality: type === 'photo' ? 0.8 : 0.7,
              maxWidth: 1080,
              maxHeight: 1920,
              retryAttempts: 3,
              onProgress: (progress) => {
                setUploadProgress(progress);
                console.log(`📊 Upload progress: ${progress.progress}% (${progress.stage})`);
              },
            }
          );

          console.log('✅ Enhanced upload completed successfully:', postId);

          // Reload updates to show the new post
          await loadUpdates();

          // Show success message
          Alert.alert(
            'Posted Successfully!',
            `Your ${type} has been posted with enhanced quality and features.`,
            [{ text: 'OK' }]
          );

        } catch (error) {
          console.error(`❌ Enhanced ${type} upload failed:`, error);

          // Use comprehensive error handling service
          errorHandlingService.handleError(
            error instanceof Error ? error : new Error(`Failed to upload ${type}`),
            `Enhanced Upload - ${type}`,
            currentUser?.id
          );
        }
        return;
      }

      // Fallback for other types
      console.warn('⚠️ Unknown update type, using fallback service:', type);
      const result = await realUpdatesService.createUpdate(
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar,
        {
          type,
          caption,
          mediaUri: mediaUri,
          privacy: 'public',
        }
      );

      if (result.success) {
        console.log('✅ Fallback update created successfully');
        await loadUpdates();
        setShowCreateModal(false);
      } else {
        Alert.alert('Error', 'Failed to create update');
      }

    } catch (error) {
      console.error('❌ Error creating update:', error);

      // Try offline as fallback
      try {
        const offlineResult = await createOfflineUpdate(mediaUri, type, caption);
        if (offlineResult.success) {
          Alert.alert('Saved Offline', 'Your story will be posted when you\'re back online');
          setShowCreateModal(false);
        } else {
          Alert.alert('Error', 'Failed to create or save story');
        }
      } catch (offlineError) {
        console.error('❌ Offline fallback also failed:', offlineError);
        Alert.alert('Error', 'Failed to create story. Please try again.');
      }
    } finally {
      setIsLoading(false);
      setIsUploading(false);
      setUploadProgress(null);
    }
  };

  // Enhanced Story Management Functions
  // Removed createStory function - pure video feed only

  // Removed all story management functions - pure video feed only

  // Removed tab switching functionality - pure video feed only

  // Enhanced scroll handler with video detection and auto-download
  const handleScroll = (event: any) => {
    const { contentOffset, layoutMeasurement } = event.nativeEvent;
    const { velocity } = event.nativeEvent;

    // Store current scroll position for focus effect
    setCurrentScrollPosition(contentOffset.y);

    // Auto-download functionality - check which videos are in viewport
    if (autoDownloadEnabled) {
      const currentIndex = Math.round(contentOffset.y / SCREEN_HEIGHT);
      const currentUpdate = updates[currentIndex];

      if (currentUpdate && currentUpdate.mediaUrl && !downloadingUpdates.has(currentUpdate.id)) {
        // Check if not already downloaded
        if (!mediaDownloadService.isMediaDownloaded(currentUpdate.mediaUrl)) {
          console.log('📥 Auto-downloading video in viewport:', currentUpdate.id);
          handleDownload(currentUpdate.id, true); // Skip navigation for auto-download
        }
      }
    }

    // Detect very rapid swiping based on scroll velocity (increased threshold)
    const isVeryRapidSwipe = velocity && (Math.abs(velocity.y) > 1.5);

    if (isVeryRapidSwipe) {
      // User is swiping very rapidly - briefly enter swipe mode
      isSwipingRef.current = true;
      console.log('🏃‍♂️ Very rapid swipe detected');

      // Clear any existing timeout
      if (swipeTimeoutRef.current) {
        clearTimeout(swipeTimeoutRef.current);
      }

      // Use setTimeout with proper cleanup to avoid useInsertionEffect warning
      if (swipeTimeoutRef.current) {
        clearTimeout(swipeTimeoutRef.current);
      }
      swipeTimeoutRef.current = setTimeout(() => {
        isSwipingRef.current = false;
        console.log('⏹️ Swipe ended');

        // Detect which video should be playing based on scroll position
        detectVisibleVideo(contentOffset.y, layoutMeasurement.height);
      }, 300); // Increased from 50ms to 300ms for stability
    } else if (!isSwipingRef.current) {
      // Normal scrolling - detect visible video immediately
      detectVisibleVideo(contentOffset.y, layoutMeasurement.height);
    }
  };

  // Function to detect which content is currently visible and handle video playback
  const detectVisibleVideo = (scrollY: number, screenHeight: number) => {
    if (globalVideoPause) return;

    // Calculate which update should be visible based on scroll position
    const updateHeight = screenHeight; // Each update takes full screen height
    const currentUpdateIndex = Math.round(scrollY / updateHeight);

    // Get the current update (could be video, photo, or text)
    if (currentUpdateIndex >= 0 && currentUpdateIndex < updates.length) {
      const currentUpdate = updates[currentUpdateIndex];

      if (currentUpdate.type === 'video') {
        // It's a video - play it
        const videoToPlay = currentUpdate.id;

        if (videoToPlay !== currentlyPlayingVideo) {
          console.log('📺 Scroll-detected video:', videoToPlay, 'at index:', currentUpdateIndex);
          // CRITICAL: Stop all other videos first
          pauseAllActiveVideos();
          setCurrentlyPlayingVideo(videoToPlay);
          // Reset mute state when switching videos
          setCurrentVideoMuted(false);
        }
      } else {
        // It's a photo or text update - IMMEDIATELY pause all videos
        if (currentlyPlayingVideo) {
          console.log('📷 Non-video content in viewport (', currentUpdate.type, ') - IMMEDIATELY pausing all videos');
          pauseAllActiveVideos(); // Force pause all players
          setCurrentlyPlayingVideo(null);
          setCurrentVideoMuted(false);
        }
      }
    } else {
      // No valid update in viewport - IMMEDIATELY pause all videos
      if (currentlyPlayingVideo) {
        console.log('📺 No content in viewport - IMMEDIATELY pausing all videos');
        pauseAllActiveVideos(); // Force pause all players
        setCurrentlyPlayingVideo(null);
        setCurrentVideoMuted(false);
      }
    }
  };

  // Clear search when modal closes
  useEffect(() => {
    if (!showSearch) {
      setSearchQuery('');
      setSearchResults([]);
    }
  }, [showSearch]);

  // Enhanced search functionality with online/offline support
  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      let searchResults: RealUpdate[] = [];

      if (isOnline) {
        // Online search - enhanced search with potential for future remote search
        try {
          // Search in current updates with simple filtering
          searchResults = updates.filter(update => {
            const searchText = query.toLowerCase();
            const matchesCaption = update.caption?.toLowerCase().includes(searchText);
            const matchesUsername = update.userName.toLowerCase().includes(searchText);

            return matchesCaption || matchesUsername;
          });

          // Remote search capability - search backend for more results
          try {
            const remoteResults = await searchRemoteUpdates(query);
            searchResults = [...searchResults, ...remoteResults];
            console.log('🔍 Remote search results:', remoteResults.length);
          } catch (remoteError) {
            console.warn('Remote search failed:', remoteError);
            // Continue with local results only
          }
        } catch (onlineError) {
          console.warn('Online search failed, falling back to basic search:', onlineError);
          // Fallback to basic search if enhanced search fails
          searchResults = updates.filter(update => {
            const searchText = query.toLowerCase();
            const matchesCaption = update.caption?.toLowerCase().includes(searchText);
            const matchesUsername = update.userName.toLowerCase().includes(searchText);
            return matchesCaption || matchesUsername;
          });
        }
      } else {
        // Offline search - search only in locally cached stories
        searchResults = updates.filter(update => {
          const searchText = query.toLowerCase();
          const matchesCaption = update.caption?.toLowerCase().includes(searchText);
          const matchesUsername = update.userName.toLowerCase().includes(searchText);

          return matchesCaption || matchesUsername;
        });
      }

      // Sort results by relevance and recency
      searchResults.sort((a, b) => {
        // Prioritize exact matches in username
        const aUsernameMatch = a.userName.toLowerCase().includes(query.toLowerCase());
        const bUsernameMatch = b.userName.toLowerCase().includes(query.toLowerCase());

        if (aUsernameMatch && !bUsernameMatch) return -1;
        if (!aUsernameMatch && bUsernameMatch) return 1;

        // Then sort by timestamp (most recent first)
        return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
      });

      setSearchResults(searchResults);
    } catch (error) {
      console.error('Search error:', error);
      Alert.alert(
        'Search Error',
        isOnline
          ? 'Failed to search updates. Please try again.'
          : 'Search failed. Limited to cached updates while offline.'
      );
    } finally {
      setIsSearching(false);
    }
  };

  // Render update item - IRACHAT IMMERSIVE STYLE
  const renderUpdateItem = ({ item }: { item: RealUpdate }) => (
    <View style={styles.immersiveContainer}>
      {/* Full screen background - media or text */}
      <View style={styles.tiktokMediaContainer}>
        {item.type === 'text' ? (
          // Text-only update with beautiful background
          <View style={[styles.tiktokMedia, {
            backgroundColor: '#667eea',
            justifyContent: 'center',
            alignItems: 'center'
          }]}>
            {/* Gradient overlay effect */}
            <View style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(118, 75, 162, 0.3)',
            }} />

            <Text style={{
              color: '#FFFFFF',
              fontSize: 28,
              fontWeight: 'bold',
              textAlign: 'center',
              paddingHorizontal: 40,
              lineHeight: 36,
              textShadowColor: 'rgba(0, 0, 0, 0.5)',
              textShadowOffset: { width: 0, height: 2 },
              textShadowRadius: 4,
              zIndex: 1,
            }}>
              {item.caption || 'Text Update'}
            </Text>
          </View>
        ) : item.type === 'photo' && item.mediaUrl ? (
          <View style={[styles.tiktokMedia, { backgroundColor: '#1a1a1a' }]}>
            <Image
              source={{ uri: item.mediaUrl }}
              style={styles.tiktokMedia}
              resizeMode="contain"
              onError={(error) => {
                console.warn('⚠️ Image failed to load:', item.mediaUrl, error.nativeEvent?.error);
              }}
              onLoad={() => {
                console.log('✅ Image loaded successfully:', item.mediaUrl);
              }}
            />
          </View>
        ) : item.type === 'video' && item.mediaUrl ? (
          <View style={[styles.tiktokMedia, { backgroundColor: '#1a1a1a' }]}>
            <VideoPlayer
              key={`video-${item.id}`}
              source={{ uri: item.mediaUrl }}
              style={styles.tiktokMedia}
              isLooping={true}
              videoId={item.id}
              globalVideoPause={globalVideoPause}
              currentlyPlayingVideo={currentlyPlayingVideo}
              currentVideoMuted={currentVideoMuted}
              currentUserId={currentUser?.id}
              onTap={() => {
                console.log('🎯 TikTok Progress: Video tapped - showing progress bar for', item.id);
                setProgressBarVisible(prev => ({ ...prev, [item.id]: true }));
              }}
              onLongPress={() => {
                console.log('🔥 LONG PRESS DETECTED ON VIDEO:', item.id);
                handleVideoLongPress(item);
              }}
            />
          </View>
        ) : item.type === 'video' ? (
          // Video without URL - show loading
          <View style={[styles.tiktokMedia, { backgroundColor: '#1a1a1a', justifyContent: 'center', alignItems: 'center' }]}>
            <Ionicons name="videocam-outline" size={64} color="#666" />
            <Text style={{ color: '#666', fontSize: 14, marginTop: 8 }}>
              Video Loading...
            </Text>
          </View>
        ) : (
          // Fallback for unknown types
          <View style={[styles.tiktokMedia, { backgroundColor: '#2a2a2a', justifyContent: 'center', alignItems: 'center' }]}>
            <Ionicons name="document-text-outline" size={64} color="#666" />
            <Text style={{ color: '#666', fontSize: 14, marginTop: 8 }}>
              Content Loading...
            </Text>
          </View>
        )}

        {/* Gradient overlay for text readability */}
        <View style={styles.tiktokGradientOverlay} />

        {/* Text Overlays */}
        {item.textOverlays && item.textOverlays.length > 0 && (
          <TextOverlayRenderer
            textOverlays={item.textOverlays}
            mediaWidth={SCREEN_WIDTH}
            mediaHeight={SCREEN_HEIGHT}
          />
        )}
      </View>

      {/* Right side actions - TikTok style */}
      <View style={styles.tiktokRightActions}>


        {/* Like button - CLICKABLE with optimized rendering and animation */}
        <LikeButton
          updateId={item.id}
          isLiked={offlineLikesService.getEffectiveLikeState(item.id, item.likes?.includes(currentUser?.id || '') || false)}
          likeCount={item.likes?.length || 0}
          onLike={() => handleLike(item.id)}
          onLongPress={() => openInteractionPage(item.id, 'likes')}
          onTriggerFlowerAnimation={() => setShowGlobalFlowerAnimation(true)}
        />

        {/* Comment button */}
        <TouchableOpacity
          style={styles.immersiveActionButton}
          onPress={() => handleComment(item.id)}
          activeOpacity={0.7}
        >
          <Ionicons name="chatbubble" size={28} color="#FFFFFF" />
          <Text style={styles.immersiveActionText}>
            {(item.comments?.length || 0) + (offlineCommentsService.getOfflineCommentCount(item.id) || 0)}
          </Text>
        </TouchableOpacity>

        {/* Share button - CLICKABLE */}
        <TouchableOpacity
          style={styles.immersiveActionButton}
          onPress={() => handleShare(item.id)}
          onLongPress={() => openInteractionPage(item.id, 'shares')}
          activeOpacity={0.7}
        >
          <Ionicons name="arrow-redo" size={28} color="#FFFFFF" />
          {/* Share count - non-clickable to prevent video pause */}
          <Text style={styles.immersiveActionText}>
            {(item.shares?.length || 0) > 999 ? `${((item.shares?.length || 0) / 1000).toFixed(1)}K` : (item.shares?.length || 0)}
          </Text>
        </TouchableOpacity>

        {/* Repost button - NEW */}
        <TouchableOpacity
          style={styles.immersiveActionButton}
          onPress={() => {
            console.log('🔄 Repost button pressed for item:', item.id);
            handleRepost(item);
          }}
          activeOpacity={0.7}
        >
          <Ionicons name="repeat" size={28} color="#FFFFFF" />
          <Text style={styles.immersiveActionText}>Repost</Text>
        </TouchableOpacity>

        {/* Download/Save button - CLICKABLE with smart navigation */}
        <TouchableOpacity
          style={styles.immersiveActionButton}
          onPress={() => handleDownloadOrOpenPage(item)}
          onLongPress={() => {
            // Long press always opens downloads page
            router.push('/downloaded-media');
          }}
          disabled={downloadingUpdates.has(item.id)}
          activeOpacity={0.7}
        >
          {downloadingUpdates.has(item.id) ? (
            <View style={styles.downloadProgressContainer}>
              <ActivityIndicator size="small" color="#FFFFFF" />
              <Text style={styles.downloadProgressText}>
                {downloadProgress[item.id] || 0}%
              </Text>
            </View>
          ) : (
            <>
              <Ionicons
                name={item.downloads?.includes(currentUser?.id || '') ? "checkmark-circle" : "download"}
                size={28}
                color={item.downloads?.includes(currentUser?.id || '') ? "#87CEEB" : "#FFFFFF"}
              />
              {/* Download count and status */}
              <Text style={styles.immersiveActionText}>
                {(item.downloads?.length || 0) > 0 ? (item.downloads?.length || 0) : 'Save'}
              </Text>
            </>
          )}
        </TouchableOpacity>

        {/* Delete button removed - now available in long press context menu */}
      </View>



      {/* TikTok-Style Progress Bar - DEBUG */}
      {item.type === 'video' && item.mediaUrl && (
        <TikTokProgressBar
          videoId={item.id}
          isVisible={progressBarVisible[item.id] || false}
          currentTime={videoProgressData?.currentTime || 0}
          duration={videoProgressData?.duration || 0}
          onSeek={(time: number) => {
            // Seek the video to the specified time (expo-video uses seconds)
            const player = videoProgressManager.getPlayer(item.id);
            if (player) {
              try {
                // expo-video player.seekTo expects time in seconds
                player.seekTo(time);
                console.log(`🎯 Seeking video ${item.id} to ${time.toFixed(1)}s`);
              } catch (error) {
                console.log(`🚨 Error seeking video ${item.id}:`, error);
              }
            } else {
              console.log(`🚨 No player found for video ${item.id}`);
            }
          }}
          onPause={() => {
            // Pause video when scrubbing starts
            const player = videoProgressManager.getPlayer(item.id);
            if (player && typeof player.pause === 'function') {
              player.pause();
              console.log(`🎯 Paused video ${item.id} for scrubbing`);
            }
          }}
          onResume={() => {
            // Resume video when scrubbing ends
            const player = videoProgressManager.getPlayer(item.id);
            if (player && typeof player.play === 'function') {
              player.play();
              console.log(`🎯 Resumed video ${item.id} after scrubbing`);
            }
          }}
          onHide={() => {
            // Hide progress bar for this specific video
            console.log(`🎯 Hiding progress bar for video ${item.id}`);
            setProgressBarVisible(prev => ({ ...prev, [item.id]: false }));
          }}
        />
      )}

      {/* Bottom content - TikTok style */}
      <View style={styles.tiktokBottomContent}>

        {/* Repost indicator */}
        {item.isRepost && (
          <View style={styles.repostIndicator}>
            <Ionicons name="repeat" size={16} color="#667eea" />
            <Text style={styles.repostText}>
              Reposted from @{item.originalAuthor || 'Unknown'}
            </Text>
          </View>
        )}

        {/* User info */}
        <View style={styles.tiktokUserInfo}>
          <TouchableOpacity onPress={() => {
            // Navigate to user profile using router
            router.push(`/profile/${item.userId}`);
          }}>
            <Text style={styles.tiktokUserName}>@{item.userName}</Text>
          </TouchableOpacity>
          <Text style={styles.tiktokTimestamp}>{formatTimeAgo(item.timestamp)}</Text>
        </View>

        {/* Text Content for text updates OR Caption for media updates */}
        {item.type === 'text' ? (
          <View style={styles.textUpdateContent}>
            <Text style={styles.textUpdateText} numberOfLines={10}>
              {item.content}
            </Text>
          </View>
        ) : item.caption ? (
          <Text style={styles.tiktokCaption} numberOfLines={5}>
            {item.caption}
          </Text>
        ) : null}

        {/* Audio Caption Player for photo/text posts */}
        {item.audioCaption && (item.type === 'photo' || item.type === 'text') && (
          <AudioCaptionPlayer
            audioCaption={item.audioCaption}
            isInViewport={true} // Always consider in viewport for now, can be enhanced later
            autoPlay={true}
            style={{ marginTop: 8 }}
          />
        )}



        {/* View count - CLICKABLE */}
        <TouchableOpacity
          style={styles.immersiveViewCount}
          onPress={() => openInteractionPage(item.id, 'views')}
        >
          <Ionicons name="eye" size={16} color="#FFFFFF" />
          <Text style={styles.immersiveViewText}>
            {item.views.length > 1000
              ? `${(item.views.length / 1000).toFixed(1)}K views`
              : `${item.views.length} views`
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={[styles.emptyContainer, { height: SCREEN_HEIGHT, backgroundColor: '#000000' }]}>
      <Ionicons name="camera-outline" size={64} color="#FFFFFF" />
      <Text style={[styles.emptyTitle, { color: '#FFFFFF' }]}>No Stories Yet</Text>
      <Text style={[styles.emptySubtitle, { color: '#FFFFFF' }]}>
        Share your first photo or video to get started
      </Text>
      <TouchableOpacity
        style={styles.createFirstButton}
        onPress={() => {
          setShowCameraOptions(true);
          loadRecentMedia(); // Load recent media when modal opens
        }}
      >
        <Text style={styles.createFirstButtonText}>Create Story</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Global Full-Screen Flower Animation */}
      <FullScreenRadiatingFlowers
        visible={showGlobalFlowerAnimation}
        onComplete={() => setShowGlobalFlowerAnimation(false)}
      />

      {/* Pure TikTok-Style Video Feed Content - FULL SCREEN */}
      <View style={styles.contentContainer}>
        {isLoading && updates.length === 0 ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#667eea" />
            <Text style={styles.loadingText}>Loading updates...</Text>
          </View>
        ) : (
          <FlatList
            ref={flatListRef}
            data={visibleUpdates}
            renderItem={renderUpdateItem}
            keyExtractor={(item, index) => `tiktok_${item.id}_${item.timestamp || Date.now()}_${index}`}
            style={styles.tiktokList}
            showsVerticalScrollIndicator={false}
            pagingEnabled={true} // TikTok-style pagination
            snapToInterval={SCREEN_HEIGHT} // Snap to full screen height
            snapToAlignment="start"
            decelerationRate="fast"
            onScroll={handleScroll}
            scrollEventThrottle={16}

            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={handleRefresh}
                tintColor="#FFFFFF"
              />
            }
            ListEmptyComponent={renderEmptyState}
          />
        )}
      </View>

      {/* FLOATING OVERLAY HEADER ELEMENTS */}
      <View style={styles.floatingHeader}>
        {/* Floating Title - Removed per user request */}

        {/* Auto-scroll and Auto-download indicators */}
        <View style={styles.autoFeaturesIndicators}>
          {autoScrollEnabled && (
            <View style={styles.autoFeatureIndicator}>
              <Ionicons name="play-outline" size={16} color="#4d10dbff" />
              <Text style={styles.autoFeatureText}>Auto Scroll</Text>
            </View>
          )}
          {autoDownloadEnabled && (
            <View style={styles.autoFeatureIndicator}>
              <Ionicons name="cloud-download" size={16} color="#3B82F6" />
              <Text style={styles.autoFeatureText}>Auto Download</Text>
            </View>
          )}
        </View>

        {/* Floating Actions - Avatar on left, Mute indicator in center, Search on right */}
        <View style={styles.floatingActions}>
          <TouchableOpacity
            style={styles.floatingAvatarContainer}
            onPress={() => {
              setShowCameraOptions(true);
              loadRecentMedia(); // Load recent media when modal opens
            }}
          >
            <Image
              source={{
                uri: currentUser?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(currentUser?.name || 'You')}&background=87CEEB&color=fff`
              }}
              style={styles.floatingAvatar}
            />
            <View style={styles.floatingAddButton}>
              <Ionicons name="add" size={12} color="#FFFFFF" />
            </View>
          </TouchableOpacity>

          {/* Mute indicator - positioned between avatar and search */}
          {currentVideoMuted && currentlyPlayingVideo && (
            <View style={styles.floatingMuteIndicator}>
              <Ionicons name="volume-mute" size={24} color="rgba(255,255,255,0.9)" />
            </View>
          )}

          <TouchableOpacity
            style={styles.floatingSearchButton}
            onPress={() => setShowSearch(true)}
          >
            <Ionicons name="search" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Enhanced Caption Modal */}
      <EnhancedCaptionModal
        visible={showCaptionModal}
        onClose={() => {
          console.log('📱 Caption modal closed - resuming videos');
          setShowCaptionModal(false);
          setCaptionModalData(null);
          setCaptionText('');
          setTimeout(() => setGlobalVideoPause(false), 100);
        }}
        onSubmit={(caption, hashtags, mentions) => {
          console.log('📝 Caption submitted:', { caption, hashtags, mentions });
          setCaptionText(caption);
          handleCaptionSubmit();
        }}
        mediaUri={captionModalData?.mediaUri}
        mediaType={captionModalData?.type}
        initialCaption={captionText}
        maxLength={500}
      />

      {/* Text Update Modal */}
      <Modal
        visible={showCreateModal}
        transparent
        animationType="none"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.cameraOptionsModal}>
            <Text style={styles.modalTitle}>Create Text Story</Text>

            <TouchableOpacity
              style={styles.optionButton}
              onPress={() => {
                setShowCreateModal(false);
                Alert.prompt(
                  'Text Story',
                  'What\'s on your mind?',
                  async (text) => {
                    if (text && text.trim()) {
                      await createUpdate('', 'text', text.trim());
                    }
                  },
                  'plain-text',
                  '',
                  'default'
                );
              }}
            >
              <Ionicons name="text" size={24} color="#667eea" />
              <Text style={styles.optionText}>Write Something</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.cancelButton}
              onPress={() => {
                setShowCreateModal(false);
                setTimeout(() => setGlobalVideoPause(false), 100);
              }}
            >
              <Ionicons name="close" size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* WhatsApp-Style Add Status Modal */}
      <Modal
        visible={showCameraOptions}
        transparent
        animationType="slide"
        onRequestClose={() => setShowCameraOptions(false)}
      >
        <View style={styles.whatsappModalOverlay}>
          <View style={styles.whatsappStatusModal}>
            {/* Header - Fixed */}
            <View style={styles.whatsappModalHeader}>
              <TouchableOpacity
                style={styles.whatsappCloseButton}
                onPress={() => {
                  setShowCameraOptions(false);
                  setTimeout(() => setGlobalVideoPause(false), 100);
                }}
              >
                <Ionicons name="close" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <Text style={styles.whatsappModalTitle}>Create Story</Text>
              <View style={{ width: 24 }} />
            </View>

            {/* Action Buttons - Fixed */}
            <View style={styles.whatsappActionButtons}>
              <TouchableOpacity
                style={styles.whatsappActionButton}
                onPress={() => handleCameraOption('text')}
              >
                <View style={[styles.whatsappActionIcon, { backgroundColor: '#87CEEB' }]}>
                  <Text style={styles.whatsappActionIconText}>T</Text>
                </View>
                <Text style={styles.whatsappActionLabel}>Text</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.whatsappActionButton}
                onPress={() => {
                  setShowCameraOptions(false);
                  setShowMusicPicker(true);
                }}
              >
                <View style={[styles.whatsappActionIcon, { backgroundColor: '#4682B4' }]}>
                  <Ionicons name="musical-notes" size={16} color="#FFFFFF" />
                </View>
                <Text style={styles.whatsappActionLabel}>Music</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.whatsappActionButton}
                onPress={() => {
                  setShowCameraOptions(false);
                  setShowLayoutPicker(true);
                }}
              >
                <View style={[styles.whatsappActionIcon, { backgroundColor: '#1E90FF' }]}>
                  <Ionicons name="grid" size={16} color="#FFFFFF" />
                </View>
                <Text style={styles.whatsappActionLabel}>Layout</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.whatsappActionButton}
                onPress={() => handleCameraOption('camera')}
              >
                <View style={[styles.whatsappActionIcon, { backgroundColor: '#B0E0E6' }]}>
                  <Ionicons name="camera" size={16} color="#FFFFFF" />
                </View>
                <Text style={styles.whatsappActionLabel}>Camera</Text>
              </TouchableOpacity>
            </View>

            {/* Gallery Grid - Scrollable */}
            {isLoadingRecentMedia ? (
              <View style={styles.whatsappLoadingContainer}>
                <ActivityIndicator size="large" color="#87CEEB" />
                <Text style={styles.whatsappLoadingText}>Loading gallery...</Text>
              </View>
            ) : (
              <FlatList
                data={organizeMediaForGrid(recentMedia)}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.whatsappGalleryContainer}
                style={styles.whatsappGalleryList}
                renderItem={renderMediaRow}
                keyExtractor={(row, index) => {
                  // Create unique key based on row content
                  const rowKey = row.map((item, colIndex) =>
                    item ? `${item.id}_${colIndex}` : `empty_${index}_${colIndex}`
                  ).join('_');
                  return `row_${index}_${rowKey}`;
                }}
                // Performance optimizations
                removeClippedSubviews={true}
                maxToRenderPerBatch={5}
                updateCellsBatchingPeriod={50}
                initialNumToRender={10}
                windowSize={10}
                onEndReached={() => loadRecentMedia(true)}
                onEndReachedThreshold={0.5}
              />
            )}
          </View>
        </View>
      </Modal>

      {/* Interaction Pages Modal */}
      <UpdatesInteractionPages
        visible={showInteractionPages}
        onClose={() => {
          console.log('📱 Interaction pages closed - resuming videos');
          setShowInteractionPages(false);
          // Enhanced resume logic - ensure video resumes at current scroll position
          setTimeout(() => {
            setGlobalVideoPause(false);
            // Detect and resume the video that should be playing based on current scroll position
            const currentIndex = Math.round(currentScrollPosition / SCREEN_HEIGHT);
            const currentUpdate = updates[currentIndex];
            if (currentUpdate && currentUpdate.type === 'video') {
              console.log('🎯 Resuming video at current position after interaction close:', currentUpdate.id);
              setCurrentlyPlayingVideo(currentUpdate.id);
            } else {
              console.log('🎯 Current position has no video after interaction close, keeping current state');
            }
          }, 150);
        }}
        updateId={selectedUpdateId}
        initialTab={selectedInteractionTab}
        onUserPress={handleUserPress}
      />

      {/* Comments Page Modal */}
      <UpdatesCommentsPage
        visible={showCommentsPage}
        onClose={() => {
          console.log('📱 Comments page closed - resuming videos');
          setShowCommentsPage(false);
          // Enhanced resume logic - ensure video resumes at current scroll position
          setTimeout(() => {
            setGlobalVideoPause(false);
            // Detect and resume the video that should be playing based on current scroll position
            const currentIndex = Math.round(currentScrollPosition / SCREEN_HEIGHT);
            const currentUpdate = updates[currentIndex];
            if (currentUpdate && currentUpdate.type === 'video') {
              console.log('🎯 Resuming video at current position after comments close:', currentUpdate.id);
              setCurrentlyPlayingVideo(currentUpdate.id);
            } else {
              console.log('🎯 Current position has no video after comments close, keeping current state');
            }
          }, 150);
        }}
        updateId={commentsUpdateId}
        onUserPress={handleUserPress}
      />

      {/* Removed Story Creator Modal - pure video feed only */}


      {/* Search Modal */}
      <Modal
        visible={showSearch}
        animationType="none"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSearch(false)}
      >
        <View style={styles.searchContainer}>
          <View style={styles.searchHeader}>
            <View style={styles.searchHeaderContent}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => {
                  setShowSearch(false);
                  // Clear search content when modal closes
                  setSearchQuery('');
                  setSearchResults([]);
                }}
              >
                <Ionicons name="arrow-back" size={24} color="#F9FAFB" />
              </TouchableOpacity>
              <View style={styles.searchInputContainer}>
                <Ionicons name="search" size={20} color="#D1D5DB" style={styles.searchIcon} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search updates..."
                  placeholderTextColor="#9CA3AF"
                  value={searchQuery}
                  onChangeText={(text: string) => {
                    setSearchQuery(text);
                    performSearch(text);
                  }}
                  autoFocus
                  returnKeyType="search"
                  onSubmitEditing={() => performSearch(searchQuery)}
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity
                    onPress={() => {
                      setSearchQuery('');
                      setSearchResults([]);
                    }}
                  >
                    <Ionicons name="close-circle" size={20} color="#D1D5DB" />
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>

          {isSearching ? (
            <View style={styles.searchLoadingContainer}>
              <ActivityIndicator size="large" color="#667eea" />
              <Text style={styles.searchLoadingText}>Searching...</Text>
            </View>
          ) : searchResults.length > 0 ? (
            <FlatList
              data={searchResults}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.searchResultItem}
                  onPress={() => {
                    setShowSearch(false);
                    // Find index of this update in the main updates list
                    const index = visibleUpdates.findIndex(update => update.id === item.id);
                    if (index !== -1 && flatListRef.current) {
                      // Scroll to this update in the main list
                      setTimeout(() => {
                        flatListRef.current?.scrollToIndex({
                          index: index,
                          animated: true,
                          viewPosition: 0.5, // Center the item in viewport
                        });
                      }, 300); // Small delay to allow search modal to close
                    } else {
                      // If not found in visible updates, add it to the list and scroll
                      console.log('🔍 Search result not in current list, adding:', item.id);
                      setUpdates(prev => {
                        const exists = prev.find(u => u.id === item.id);
                        if (!exists) {
                          return [item, ...prev]; // Add to beginning
                        }
                        return prev;
                      });
                      // Scroll to top after adding
                      setTimeout(() => {
                        flatListRef.current?.scrollToIndex({
                          index: 0,
                          animated: true,
                          viewPosition: 0.5,
                        });
                      }, 300);
                    }
                  }}
                >
                  <Image
                    source={{
                      uri: item.mediaUrl || item.userAvatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(item.userName)}&background=87CEEB&color=fff`
                    }}
                    style={styles.searchResultImage}
                  />
                  <View style={styles.searchResultContent}>
                    <Text style={styles.searchResultUsername}>{item.userName}</Text>
                    <Text style={styles.searchResultCaption} numberOfLines={2}>
                      {item.caption || 'No caption'}
                    </Text>
                    <Text style={styles.searchResultTime}>{formatTimeAgo(item.timestamp)}</Text>
                  </View>
                  <Ionicons name="chevron-forward" size={20} color="#D1D5DB" />
                </TouchableOpacity>
              )}
              keyExtractor={(item, index) => `search_${item.id}_${index}`}
            />
          ) : searchQuery.length > 0 ? (
            <View style={styles.searchEmptyContainer}>
              <Ionicons name="search-outline" size={64} color="#E5E7EB" />
              <Text style={styles.searchEmptyText}>No results found</Text>
              <Text style={styles.searchEmptySubtext}>Try different keywords or filters</Text>
            </View>
          ) : (
            <View style={styles.searchInitialContainer}>
              <Ionicons name="search" size={64} color="#E5E7EB" />
              <Text style={styles.searchInitialText}>Search for stories</Text>
              <Text style={styles.searchInitialSubtext}>Find stories by caption, username, or content</Text>
            </View>
          )}
        </View>
      </Modal>

      {/* Enhanced Video Trimmer Modal */}
      <Modal
        visible={showVideoTrimmer}
        transparent={false}
        animationType="slide"
        onRequestClose={handleEditingCancel}
      >
        {captionModalData && (
          <EnhancedVideoTrimmer
            visible={showVideoTrimmer}
            videoUri={captionModalData.mediaUri}
            onTrimComplete={handleVideoTrimComplete}
            onClose={handleEditingCancel}
            maxDuration={180} // 3 minutes max
          />
        )}
      </Modal>

      {/* Enhanced Photo Cropper Modal */}
      <Modal
        visible={showPhotoCropper}
        transparent={false}
        animationType="slide"
        onRequestClose={handleEditingCancel}
      >
        {captionModalData && (
          <EnhancedPhotoCropper
            imageUri={captionModalData.mediaUri}
            onCropComplete={handlePhotoCropComplete}
            onCancel={handleEditingCancel}
            initialAspectRatio={9/16} // Story aspect ratio
          />
        )}
      </Modal>

      {/* TikTok-Style Media Picker */}
      <TikTokStyleMediaPicker
        visible={showTikTokMediaPicker}
        onClose={() => setShowTikTokMediaPicker(false)}
        onMediaSelected={handleTikTokMediaSelected}
        onCameraPress={handleTikTokCameraPress}
        onTextPress={handleTikTokTextPress}
      />

      {/* TikTok-Style Camera */}
      <TikTokStyleCamera
        visible={showTikTokCamera}
        onClose={() => setShowTikTokCamera(false)}
        onMediaCaptured={handleTikTokCameraCapture}
      />

      {/* Media Preview Editor */}
      {selectedMediaForPreview && (
        <MediaPreviewEditor
          visible={showMediaPreviewEditor}
          mediaUri={selectedMediaForPreview.uri}
          mediaType={selectedMediaForPreview.type}
          onClose={handleCloseMediaPreview}
          onPost={handleMediaPreviewPost}
          isPosting={isPostingUpdate}
        />
      )}

      {/* Text Update Creator */}
      <TextUpdateCreator
        visible={showTextUpdateCreator}
        onClose={handleCloseTextCreator}
        onPost={handleTextUpdatePost}
        isPosting={isPostingUpdate}
      />

      {/* Long Press Context Menu */}
      <ContextMenuModal
        visible={showContextMenu}
        update={contextMenuUpdate}
        currentUserId={currentUser?.id || ''}
        onClose={closeContextMenu}
        onNotInterested={handleContextNotInterested}
        onReport={handleContextReport}
        onAutoScroll={handleContextAutoScroll}
        onCopyLink={handleContextCopyLink}
        onAutoDownload={handleContextAutoDownload}
        onDelete={handleContextDelete}
        onFullScreen={handleContextFullScreen}
        autoScrollEnabled={autoScrollEnabled}
        autoDownloadEnabled={autoDownloadEnabled}
      />

      {/* Full Screen Video Modal */}
      <FullScreenVideoModal
        visible={isFullScreenMode}
        update={fullScreenUpdate}
        onClose={exitFullScreenMode}
      />

      {/* Music Picker Modal */}
      <MusicPickerModal
        visible={showMusicPicker}
        onClose={() => setShowMusicPicker(false)}
        onMusicSelect={(track) => {
          setSelectedMusic(track);
          setShowMusicPicker(false);
          // Show success message
          const artist = 'artist' in track ? track.artist : (track as LocalMusicTrack).artist || 'Unknown Artist';
          Alert.alert(
            'Music Selected',
            `"${track.title}" by ${artist} has been selected for your story.`,
            [{ text: 'OK' }]
          );
        }}
      />

      {/* Layout Picker Modal */}
      <LayoutPickerModal
        visible={showLayoutPicker}
        onClose={() => setShowLayoutPicker(false)}
        onSelectLayout={(template) => {
          setSelectedLayout(template);
          setShowLayoutPicker(false);
          // Show success message with layout instructions
          Alert.alert(
            'Layout Selected',
            `"${template.name}" layout selected. ${template.description}`,
            [{ text: 'OK' }]
          );
        }}
      />

      {/* Voice Recorder Modal */}
      <VoiceRecorderModal
        visible={showVoiceRecorder}
        onClose={() => setShowVoiceRecorder(false)}
        onRecordingComplete={(recording) => {
          setSelectedVoiceRecording(recording);
          setShowVoiceRecorder(false);
          // Show success message
          Alert.alert(
            'Voice Recording Complete',
            `Voice note recorded successfully (${(recording.duration / 1000).toFixed(1)}s). This will be added as narration to your story.`,
            [{ text: 'OK' }]
          );
        }}
      />

      {/* Upload Progress Indicator */}
      {isUploading && uploadProgress && (
        <View style={styles.uploadProgressOverlay}>
          <View style={styles.uploadProgressModal}>
            <Text style={styles.uploadProgressTitle}>
              {uploadProgress.stage === 'compressing' && 'Optimizing media...'}
              {uploadProgress.stage === 'uploading' && 'Uploading...'}
              {uploadProgress.stage === 'saving' && 'Saving post...'}
              {uploadProgress.stage === 'complete' && 'Complete!'}
              {uploadProgress.stage === 'error' && 'Upload failed'}
            </Text>

            <View style={styles.uploadProgressBarContainer}>
              <View
                style={[
                  styles.uploadProgressBar,
                  { width: `${uploadProgress.progress}%` }
                ]}
              />
            </View>

            <Text style={styles.uploadProgressText}>
              {uploadProgress.progress.toFixed(0)}%
              {uploadProgress.totalBytes > 0 && (
                ` • ${(uploadProgress.bytesTransferred / 1024 / 1024).toFixed(1)}MB / ${(uploadProgress.totalBytes / 1024 / 1024).toFixed(1)}MB`
              )}
            </Text>

            {uploadProgress.stage === 'error' && (
              <TouchableOpacity
                style={styles.uploadRetryButton}
                onPress={() => {
                  setIsUploading(false);
                  setUploadProgress(null);
                }}
              >
                <Text style={styles.uploadRetryButtonText}>Dismiss</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000', // TikTok black background
  },
  // FLOATING OVERLAY HEADER STYLES
  floatingHeader: {
    position: 'absolute',
    top: DeviceInfo.statusBarHeight + 30, // Even more spacing from status bar
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    zIndex: 1000,
  },
  floatingTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  floatingActions: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
  },
  floatingSearchButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    // backgroundColor: 'rgba(0, 0, 0, 0.6)', // Removed background color
    justifyContent: 'center',
    alignItems: 'center',
    // Removed shadow since no background
  },
  floatingMuteIndicator: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingAvatarContainer: {
    position: 'relative',
  },
  floatingAvatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    borderWidth: 2,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  floatingAddButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#1DA1F2',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
    zIndex: 1000, // Ensure it appears above avatar overlay
    elevation: 10, // For Android
  },

  // IRACHAT IMMERSIVE STYLE COMPONENTS
  immersiveContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    position: 'relative',
    backgroundColor: '#000000',
  },
  tiktokContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
    position: 'relative',
    backgroundColor: '#000000',
  },
  tiktokMediaContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
    overflow: 'hidden', // Ensure media doesn't go beyond container
  },
  tiktokMedia: {
    width: '100%',
    height: '100%',
    backgroundColor: 'transparent', // Remove black background to prevent flash
  },
  videoPauseOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
    zIndex: 10,
  },
  videoMuteIndicator: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 20,
    padding: 8,
    zIndex: 10,
  },
  likeAnimationOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    marginTop: -30, // Half of icon size
    marginLeft: -30, // Half of icon size
    zIndex: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cooldownMessageOverlay: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 100, // Don't cover the right action buttons
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 20,
    padding: 12,
    zIndex: 15,
    alignItems: 'center',
  },
  cooldownMessageText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  videoTapOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 80, // Don't cover the right action buttons area
    bottom: 0,
    zIndex: 5, // Above video, below pause/mute indicators
  },
  tiktokGradientOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 200,
    backgroundColor: 'rgba(0,0,0,0.4)', // Gradient effect for React Native
    zIndex: 2,
  },
  tiktokRightActions: {
    position: 'absolute',
    right: 12,
    bottom: 90, // Reduced to match bottom content positioning
    zIndex: 3,
    alignItems: 'center',
  },
  tiktokAvatarContainer: {
    position: 'relative',
    marginBottom: 20,
  },
  tiktokAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  tiktokFollowButton: {
    position: 'absolute',
    bottom: -8,
    left: '50%',
    marginLeft: -12,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#1DA1F2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  immersiveActionButton: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 8,
  },

  immersiveActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
    textAlign: 'center',
  },
  tiktokActionButton: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 8,
  },
  tiktokActionText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
    textAlign: 'center',
  },
  tiktokBottomContent: {
    position: 'absolute',
    bottom: 90, // Reduced from 100 to 90 for less space above tab bar
    left: 0,
    right: 80, // Leave space for right actions
    padding: 16,
    zIndex: 3,
  },
  tiktokUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  tiktokUserName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
    marginRight: 12,
  },
  tiktokTimestamp: {
    color: '#FFFFFF',
    fontSize: 12,
    opacity: 0.8,
  },
  tiktokCaption: {
    color: '#FFFFFF',
    fontSize: 15,
    lineHeight: 20,
    marginBottom: 8,
    flexShrink: 1, // Allow caption to shrink and expand upward
    textAlign: 'left',
  },

  tiktokViewCount: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  immersiveViewCount: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 12,
  },
  immersiveViewText: {
    color: '#FFFFFF',
    fontSize: 12,
    marginLeft: 4,
    opacity: 0.9,
    fontWeight: '500',
  },

  tiktokList: {
    flex: 1,
  },

  // Tab Bar Styles
  tabBar: {
    backgroundColor: '#000000',
    paddingTop: 100, // Account for header height
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    // Active tab styling handled by indicator
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#9CA3AF',
    textAlign: 'center',
  },
  activeTabText: {
    color: '#FFFFFF',
  },
  tabIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 16,
    width: SCREEN_WIDTH / 2 - 16,
    height: 3,
    backgroundColor: '#1DA1F2',
    borderRadius: 2,
  },

  // Content Container
  contentContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },

  // Stories Content Styles
  storiesContent: {
    flex: 1,
    backgroundColor: '#000000',
  },
  storiesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#333333',
  },
  storiesTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  addStoryButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1DA1F2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  storiesScrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  storiesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  storyGridItem: {
    width: (SCREEN_WIDTH - 48) / 2,
    height: 200,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: '#1a1a1a',
  },
  storyGridImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  storyGridOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  storyGridTime: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  emptyStoriesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyStoriesText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStoriesSubtext: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  createStoryButton: {
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
  },
  createStoryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    backgroundColor: '#1DA1F2',
    borderBottomWidth: 1,
    borderBottomColor: '#5a67d8',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 45, // Reduced for compact header
    paddingBottom: 15, // Reduced bottom padding
    minHeight: 110, // Match the reduced header height
    flex: 1, // Take full height of header
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
    lineHeight: 28, // Better line height for vertical alignment
  },
  createButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1DA1F2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerAvatarContainer: {
    position: 'relative',
    width: 36,
    height: 36,
    marginTop: 2,
  },
  headerAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  headerAddButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: '#1DA1F2',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#FFFFFF',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  list: {
    flex: 1,
  },
  listContent: {
    paddingVertical: 8,
  },
  updateItem: {
    backgroundColor: '#1A1A1A',
    marginBottom: 8,
    paddingVertical: 16,
  },
  updateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  timestamp: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 2,
  },
  moreButton: {
    padding: 8,
  },
  mediaContainer: {
    width: SCREEN_WIDTH,
    height: SCREEN_WIDTH * 1.2,
    backgroundColor: '#000000',
  },
  media: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  caption: {
    fontSize: 14,
    color: '#374151',
    paddingHorizontal: 16,
    paddingVertical: 12,
    lineHeight: 20,
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 24,
  },
  actionCount: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 4,
    fontWeight: '500',
  },
  viewCount: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
  },
  viewCountText: {
    fontSize: 12,
    color: '#9CA3AF',
    marginLeft: 4,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  createFirstButton: {
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createFirstButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center', // Changed from flex-end to center for better keyboard handling
    paddingHorizontal: 2,
  },
  cameraOptionsModal: {
    backgroundColor: '#1A1A1A',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 2,
    paddingHorizontal: 2,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 2,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    backgroundColor: '#2A2A2A',
  },
  optionText: {
    fontSize: 16,
    color: '#374151',
    marginLeft: 16,
    fontWeight: '500',
  },
  cancelButton: {
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '600',
  },

  // Caption Modal Styles
  captionModal: {
    backgroundColor: '#1A1A1A',
    borderRadius: 20, // Changed to full border radius for centered modal
    paddingVertical: 16,
    paddingHorizontal: 16,
    maxHeight: '80%',
    minHeight: '40%',
    marginHorizontal: 20, // Increased margin for better centering
    width: '90%', // Added width constraint
    alignSelf: 'center', // Center the modal
  },
  mediaPreviewContainer: {
    width: '100%',
    height: 250,
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: 12,
    backgroundColor: '#000000',
  },
  mediaPreview: {
    width: '100%',
    height: '100%',
  },
  captionInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 8,
    fontSize: 16,
    color: '#374151',
    backgroundColor: '#2A2A2A',
    textAlignVertical: 'top',
    minHeight: 120,
    marginBottom: 2,
  },
  characterCounter: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'right',
    marginBottom: 2,
  },
  captionModalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    paddingBottom: 20,
  },
  captionCancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
    flex: 1,
    marginRight: 8,
  },
  captionCancelText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '600',
    textAlign: 'center',
  },
  captionDraftButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: '#FEF3C7',
    borderWidth: 1,
    borderColor: '#F59E0B',
    flex: 1,
    marginHorizontal: 4,
  },
  captionDraftText: {
    fontSize: 16,
    color: '#D97706',
    fontWeight: '600',
    textAlign: 'center',
  },
  captionSubmitButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    backgroundColor: '#1DA1F2',
    flex: 1,
    marginLeft: 8,
  },
  captionSubmitText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
  },


  avatarAddStoryButton: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#1DA1F2',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },


  // Story Viewer Styles
  storyViewerContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },
  storyContent: {
    flex: 1,
    position: 'relative',
  },
  storyMedia: {
    width: '100%',
    height: '100%',
  },
  storyHeader: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    zIndex: 10,
  },
  storyUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  storyUserAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  storyUserName: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginRight: 8,
  },
  storyTime: {
    color: '#FFFFFF',
    fontSize: 12,
    opacity: 0.8,
  },
  storyCloseButton: {
    padding: 8,
  },
  storyCaptionContainer: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
    zIndex: 10,
  },
  storyCaption: {
    color: '#FFFFFF',
    fontSize: 16,
    lineHeight: 22,
    backgroundColor: 'rgba(0,0,0,0.3)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  storyNavigation: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    zIndex: 5,
  },
  storyNavButton: {
    position: 'absolute',
    left: 16,
    top: '50%',
    marginTop: -20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  storyNavButtonRight: {
    left: 'auto',
    right: 16,
  },

  // Draft Styles
  draftContainer: {
    backgroundColor: '#F0F9FF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#BAE6FD',
  },
  draftTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0369A1',
    marginBottom: 4,
  },
  draftInfo: {
    fontSize: 14,
    color: '#0284C7',
    marginBottom: 12,
    lineHeight: 18,
  },
  restoreDraftButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#667eea',
  },
  restoreDraftText: {
    fontSize: 14,
    color: '#667eea',
    fontWeight: '600',
    marginLeft: 4,
  },

  // Download progress styles
  downloadProgressContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 40,
  },
  downloadProgressText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '600',
    marginTop: 2,
    textAlign: 'center',
  },

  // Search Modal Styles
  searchContainer: {
    flex: 1,
    backgroundColor: '#1F2937', // Gray-black background
  },
  searchHeader: {
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 16,
    height: 90,
    backgroundColor: '#374151', // Dark gray header
    borderBottomWidth: 1,
    borderBottomColor: '#4B5563',
    justifyContent: 'flex-end', // Push content to bottom
  },
  searchHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    height: 40, // Fixed height for proper centering
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4B5563',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
    borderWidth: 1,
    borderColor: '#6B7280',
    height: 40,
  },
  searchIcon: {
    marginRight: 4,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#F9FAFB',
    paddingVertical: 0,
    paddingHorizontal: 0,
    textAlignVertical: 'center',
    includeFontPadding: false,
    height: 24,
  },
  searchLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    backgroundColor: '#1F2937',
  },
  searchLoadingText: {
    fontSize: 16,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  searchResultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#374151',
    marginHorizontal: 12,
    marginVertical: 4,
    borderRadius: 12,
    gap: 12,
    borderWidth: 1,
    borderColor: '#4B5563',
  },
  searchResultImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: '#4B5563',
    borderWidth: 1,
    borderColor: '#6B7280',
  },
  searchResultContent: {
    flex: 1,
    gap: 6,
  },
  searchResultUsername: {
    fontSize: 16,
    fontWeight: '600',
    color: '#F9FAFB',
  },
  searchResultCaption: {
    fontSize: 14,
    color: '#D1D5DB',
    lineHeight: 20,
  },
  searchResultTime: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  searchEmptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 32,
    backgroundColor: '#1F2937',
  },
  searchEmptyText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#F9FAFB',
    textAlign: 'center',
  },
  searchEmptySubtext: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 24,
  },
  searchInitialContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
    paddingHorizontal: 32,
    backgroundColor: '#1F2937',
  },
  searchInitialText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#F9FAFB',
    textAlign: 'center',
  },
  searchInitialSubtext: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    lineHeight: 24,
  },

  // Video preview and trimming styles
  videoPreviewContainer: {
    position: 'relative',
  },
  videoControls: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    right: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    padding: 8,
  },
  trimButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  trimButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  playbackControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  playbackButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },

  // TikTok-style Modal Styles
  tiktokOptionsModal: {
    backgroundColor: '#1A1A1A',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingVertical: 20,
    paddingHorizontal: 20,
    minHeight: 280,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  modalHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 20,
  },
  tiktokModalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 24,
  },
  tiktokOptionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  tiktokOptionButton: {
    alignItems: 'center',
    gap: 12,
  },
  tiktokOptionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tiktokOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
  },
  tiktokCancelButton: {
    backgroundColor: '#F3F4F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  tiktokCancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },

  // WhatsApp-style Modal Styles
  whatsappModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  whatsappStatusModal: {
    backgroundColor: '#2A2A2A',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area padding
    maxHeight: '80%',
    flex: 1,
  },
  whatsappModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  whatsappCloseButton: {
    padding: 4,
  },
  whatsappModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  whatsappActionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  whatsappActionButton: {
    alignItems: 'center',
    gap: 8,
  },
  whatsappActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  whatsappActionIconText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  whatsappActionLabel: {
    fontSize: 12,
    color: '#B0B0B0',
    fontWeight: '500',
  },
  whatsappLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  whatsappRecentsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  whatsappRecentsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },

  whatsappLoadingText: {
    color: '#B0B0B0',
    fontSize: 14,
    marginTop: 12,
  },
  whatsappGalleryContainer: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  whatsappGalleryList: {
    flex: 1,
  },
  whatsappMediaRow: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  whatsappMediaColumn: {
    flex: 1,
    marginHorizontal: 2,
  },
  whatsappGalleryGrid: {
    flexDirection: 'row',
    gap: 8,
  },

  whatsappGalleryItem: {
    width: '100%',
    aspectRatio: 1,
    borderRadius: 8,
    overflow: 'hidden',
  },

  whatsappGalleryImage: {
    width: '100%',
    height: '100%',
  },
  whatsappVideoDurationOverlay: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  whatsappVideoDurationText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '500',
  },
  whatsappMediaTypeIcon: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 8,
    padding: 2,
  },


  // Upload Progress Styles
  uploadProgressOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
  },
  uploadProgressModal: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    marginHorizontal: 32,
    minWidth: 280,
    alignItems: 'center',
  },
  uploadProgressTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
    textAlign: 'center',
  },
  uploadProgressBarContainer: {
    width: '100%',
    height: 6,
    backgroundColor: '#F0F0F0',
    borderRadius: 3,
    marginBottom: 12,
    overflow: 'hidden',
  },
  uploadProgressBar: {
    height: '100%',
    backgroundColor: '#25D366',
    borderRadius: 3,
  },
  uploadProgressText: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 8,
  },
  uploadRetryButton: {
    backgroundColor: '#FF3040',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    marginTop: 8,
  },
  uploadRetryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },

  // Text Update Styles
  textUpdateContent: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
    maxWidth: '90%',
  },
  textUpdateText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 24,
  },

  // Repost Indicator Styles
  repostIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(102, 126, 234, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 8,
    alignSelf: 'flex-start',
  },
  repostText: {
    color: '#667eea',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 6,
  },

  // Context Menu Styles
  contextMenuBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  contextMenuContainer: {
    backgroundColor: '#1F2937',
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingVertical: 20,
    paddingHorizontal: 20,
    minHeight: 400,
  },
  contextMenuHandle: {
    width: 40,
    height: 4,
    backgroundColor: '#6B7280',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 20,
  },
  contextMenuTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#F9FAFB',
    textAlign: 'center',
    marginBottom: 24,
  },
  contextMenuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  contextMenuItem: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 20,
  },
  contextMenuIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  contextMenuLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#D1D5DB',
    textAlign: 'center',
  },
  contextMenuCancel: {
    backgroundColor: '#374151',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  contextMenuCancelText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#9CA3AF',
  },

  // Full Screen Video Styles
  fullScreenContainer: {
    flex: 1,
    backgroundColor: '#000000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenVideoContainer: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  fullScreenVideo: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  fullScreenControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  fullScreenTopControls: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 50,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  fullScreenBackButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  fullScreenInfo: {
    flex: 1,
  },
  fullScreenUsername: {
    fontSize: 18,
    fontWeight: '700',
    color: 'white',
    marginBottom: 4,
  },
  fullScreenCaption: {
    fontSize: 14,
    color: '#D1D5DB',
    lineHeight: 20,
  },
  fullScreenPlayButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -30 }, { translateY: -30 }],
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenBottomControls: {
    paddingHorizontal: 20,
    paddingBottom: 50,
    alignItems: 'center',
  },
  fullScreenHint: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
  },

  // Auto Features Indicators Styles - Centered between avatar and search
  autoFeaturesIndicators: {
    position: 'absolute',
    top: 10,
    left: '50%',
    transform: [{ translateX: -50 }], // Center horizontally
    flexDirection: 'column',
    gap: 8,
    alignItems: 'center',
  },
  autoFeatureIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  autoFeatureText: {
    fontSize: 10,
    color: 'white',
    fontWeight: '600',
  },


});

export default UpdatesScreen;

