/**
 * 🔄 IRACHAT OFFLINE FUNCTIONALITY DEMO COMPONENT
 * Demonstrates real offline/online messaging capabilities like WhatsA<PERSON>
 * Updated with proper IraChat Sky Blue branding and enhanced UX
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useIraChatOffline } from '../hooks/useIraChatOffline';
import { Message, Chat } from '../types';


// IraChat Branding Colors
const COLORS = {
  primary: '#87CEEB',      // Sky Blue
  primaryDark: '#4682B4',  // Steel Blue
  primaryLight: '#B0E0E6', // Powder Blue
  secondary: '#1E90FF',    // Dodger Blue
  background: '#F0F8FF',   // Alice Blue
  surface: '#FFFFFF',      // White
  surfaceLight: '#F8FAFC', // Very Light Gray
  text: '#2C3E50',         // Dark Blue Gray
  textSecondary: '#5D6D7E', // Medium Blue Gray
  textMuted: '#85929E',    // Light Blue Gray
  success: '#32CD32',      // Lime Green
  warning: '#FFA500',      // Orange
  error: '#FF6B6B',        // Light Red
  border: '#E5E7EB',       // Light Gray
  shadow: 'rgba(0, 0, 0, 0.1)',
  overlay: 'rgba(0, 0, 0, 0.5)',
};

export const OfflineDemo: React.FC = () => {
  const {
    isInitialized,
    isOnline,
    isSyncing,
    syncProgress,
    stats,
    error,
    sendMessage,
    getMessages,
    getChats,
    createChat,
    searchMessages,
    forceSync,
    markChatAsRead,
    pinChat,
    archiveChat,
  } = useIraChatOffline({
    autoInitialize: true,
    enableBackgroundSync: true,
    syncOnAppForeground: true,
    enableDebugLogs: true,
  });

  const [chats, setChats] = useState<Chat[]>([]);
  const [selectedChatId, setSelectedChatId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [newChatName, setNewChatName] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Message[]>([]);

  // Mock current user ID
  const currentUserId = 'user_123';

  // Load chats on initialization
  useEffect(() => {
    if (isInitialized) {
      loadChats();
    }
  }, [isInitialized]);

  // Load messages when chat is selected
  useEffect(() => {
    if (selectedChatId) {
      loadMessages(selectedChatId);
    }
  }, [selectedChatId]);

  const loadChats = async () => {
    try {
      const chatList = await getChats();
      setChats(chatList);
    } catch (err) {
      console.error('Failed to load chats:', err);
    }
  };

  const loadMessages = async (chatId: string) => {
    try {
      const messageList = await getMessages(chatId, 50, 0);
      setMessages(messageList);
    } catch (err) {
      console.error('Failed to load messages:', err);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedChatId) return;

    try {
      await sendMessage(selectedChatId, newMessage.trim(), currentUserId);
      setNewMessage('');
      
      // Reload messages to show the new one
      await loadMessages(selectedChatId);
      
      // Mark chat as read
      await markChatAsRead(selectedChatId);
    } catch (err) {
      Alert.alert('Error', 'Failed to send message. It will be sent when you\'re back online.');
    }
  };

  const handleCreateChat = async () => {
    if (!newChatName.trim()) return;

    try {
      const chatId = await createChat(newChatName.trim(), currentUserId, false, []);
      setNewChatName('');
      
      // Reload chats and select the new one
      await loadChats();
      setSelectedChatId(chatId);
    } catch (err) {
      Alert.alert('Error', 'Failed to create chat');
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const results = await searchMessages(searchQuery.trim());
      setSearchResults(results);
    } catch (err) {
      console.error('Failed to search messages:', err);
    }
  };

  const handleForceSync = async () => {
    try {
      await forceSync();
      await loadChats();
      if (selectedChatId) {
        await loadMessages(selectedChatId);
      }
      Alert.alert('Success', 'Sync completed successfully');
    } catch (err) {
      Alert.alert('Error', 'Sync failed. Check your internet connection.');
    }
  };

  const handlePinChat = async (chatId: string) => {
    try {
      await pinChat(chatId, true);
      await loadChats();
    } catch (err) {
      Alert.alert('Error', 'Failed to pin chat');
    }
  };

  const handleArchiveChat = async (chatId: string) => {
    try {
      await archiveChat(chatId, true);
      await loadChats();
      if (selectedChatId === chatId) {
        setSelectedChatId(null);
        setMessages([]);
      }
    } catch (err) {
      Alert.alert('Error', 'Failed to archive chat');
    }
  };

  if (!isInitialized) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.primary} />
        <Text style={styles.loadingText}>Initializing IraChat...</Text>
        <Text style={styles.loadingSubtext}>Setting up offline capabilities...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Status Bar */}
      <View style={styles.statusBar}>
        <View style={styles.statusItem}>
          <View style={[styles.statusDot, { backgroundColor: isOnline ? COLORS.success : COLORS.error }]} />
          <Text style={styles.statusText}>
            <Ionicons name={isOnline ? 'wifi' : 'wifi-outline'} size={16} color={isOnline ? COLORS.success : COLORS.error} />
            {' '}{isOnline ? 'Online' : 'Offline'}
          </Text>
        </View>

        {isSyncing && (
          <View style={styles.statusItem}>
            <ActivityIndicator size="small" color={COLORS.primary} />
            <Text style={styles.statusText}>
              Syncing... {syncProgress ? `${Math.round(syncProgress.progress)}%` : ''}
            </Text>
          </View>
        )}

        <TouchableOpacity style={styles.syncButton} onPress={handleForceSync} disabled={isSyncing}>
          <Ionicons name="refresh" size={16} color={COLORS.surface} />
          <Text style={styles.syncButtonText}>Force Sync</Text>
        </TouchableOpacity>
      </View>

      {/* Error Display */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Error: {error}</Text>
        </View>
      )}

      {/* Stats Display */}
      {stats && (
        <View style={styles.statsContainer}>
          <Text style={styles.statsTitle}>Stats</Text>
          <Text style={styles.statsText}>Database Size: {(stats.totalStorageUsed / 1024 / 1024).toFixed(2)} MB</Text>
          <Text style={styles.statsText}>Pending Sync: {stats.pendingSyncItems}</Text>
          <Text style={styles.statsText}>Last Sync: {stats.lastSyncTime ? new Date(stats.lastSyncTime).toLocaleTimeString() : 'Never'}</Text>
        </View>
      )}

      {/* Create Chat */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Create New Chat</Text>
        <View style={styles.inputRow}>
          <TextInput
            style={styles.textInput}
            placeholder="Chat name"
            value={newChatName}
            onChangeText={setNewChatName}
          />
          <TouchableOpacity style={styles.button} onPress={handleCreateChat}>
            <Text style={styles.buttonText}>Create</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Chat List */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Chats ({chats.length})</Text>
        {chats.map((chat) => {
          const chatId = (chat as any).localId || chat.id;
          const isPinned = (chat as any).isPinned || false;
          return (
            <TouchableOpacity
              key={chatId}
              style={[
                styles.chatItem,
                selectedChatId === chatId && styles.selectedChatItem
              ]}
              onPress={() => setSelectedChatId(chatId)}
            >
              <View style={styles.chatInfo}>
                <Text style={styles.chatName}>{chat.name || 'Unknown Chat'}</Text>
                <Text style={styles.chatDetails}>
                  {chat.isGroup ? 'Group' : 'Individual'} •
                  Unread: {chat.unreadCount || 0} •
                  {isPinned ? 'Pinned' : 'Not Pinned'}
                </Text>
              </View>
              <View style={styles.chatActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handlePinChat(chatId)}
                >
                  <Text style={styles.actionButtonText}>📌</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleArchiveChat(chatId)}
                >
                  <Text style={styles.actionButtonText}>📁</Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Messages */}
      {selectedChatId && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Messages ({messages.length})</Text>
          
          {/* Send Message */}
          <View style={styles.inputRow}>
            <TextInput
              style={styles.textInput}
              placeholder="Type a message..."
              value={newMessage}
              onChangeText={setNewMessage}
              multiline
            />
            <TouchableOpacity style={styles.button} onPress={handleSendMessage}>
              <Text style={styles.buttonText}>Send</Text>
            </TouchableOpacity>
          </View>

          {/* Message List */}
          <ScrollView style={styles.messageList}>
            {messages.map((message, index) => {
              const messageId = (message as any).localId || message.id;
              const syncStatus = (message as any).syncStatus;
              return (
                <View
                  key={`main_${messageId}_${index}`}
                  style={[
                    styles.messageItem,
                    message.senderId === currentUserId ? styles.sentMessage : styles.receivedMessage
                  ]}
                >
                  <Text style={styles.messageText}>{message.text}</Text>
                  <Text style={styles.messageTime}>
                    {new Date(message.timestamp).toLocaleTimeString()} • {message.status}
                    {syncStatus?.status === 'pending' && ' (Pending)'}
                    {syncStatus?.status === 'failed' && ' (Failed)'}
                  </Text>
                </View>
              );
            })}
          </ScrollView>
        </View>
      )}

      {/* Search */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Search Messages</Text>
        <View style={styles.inputRow}>
          <TextInput
            style={styles.textInput}
            placeholder="Search messages..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          <TouchableOpacity style={styles.button} onPress={handleSearch}>
            <Text style={styles.buttonText}>Search</Text>
          </TouchableOpacity>
        </View>
        
        {searchResults.length > 0 && (
          <View style={styles.searchResults}>
            <Text style={styles.searchResultsTitle}>Search Results ({searchResults.length})</Text>
            {searchResults.map((message, index) => {
              const messageId = (message as any).localId || message.id;
              const chatId = (message as any).chatId || 'Unknown';
              return (
                <View key={`search_${messageId}_${index}`} style={styles.searchResultItem}>
                  <Text style={styles.messageText}>{message.text}</Text>
                  <Text style={styles.messageTime}>
                    {new Date(message.timestamp).toLocaleTimeString()} • Chat: {chatId}
                  </Text>
                </View>
              );
            })}
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
  },
  loadingSubtext: {
    marginTop: 8,
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  statusBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text,
  },
  syncButton: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  syncButtonText: {
    color: COLORS.surface,
    fontSize: 14,
    fontWeight: '600',
  },
  errorContainer: {
    backgroundColor: COLORS.error + '20',
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: COLORS.error,
  },
  errorText: {
    color: COLORS.error,
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    backgroundColor: COLORS.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statsTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 12,
  },
  statsText: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 6,
  },
  section: {
    backgroundColor: COLORS.surface,
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: COLORS.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 16,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  textInput: {
    flex: 1,
    borderWidth: 2,
    borderColor: COLORS.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: COLORS.surfaceLight,
    color: COLORS.text,
    minHeight: 48,
  },
  button: {
    backgroundColor: COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    minHeight: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonText: {
    color: COLORS.surface,
    fontWeight: '600',
    fontSize: 16,
  },
  chatItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    backgroundColor: COLORS.surface,
  },
  selectedChatItem: {
    backgroundColor: COLORS.primaryLight,
  },
  chatInfo: {
    flex: 1,
  },
  chatName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 4,
  },
  chatDetails: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  chatActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: COLORS.primaryLight,
  },
  actionButtonText: {
    fontSize: 18,
  },
  messageList: {
    maxHeight: 300,
    paddingVertical: 8,
  },
  messageItem: {
    padding: 12,
    marginVertical: 4,
    borderRadius: 12,
    maxWidth: '80%',
  },
  sentMessage: {
    backgroundColor: COLORS.primary,
    alignSelf: 'flex-end',
  },
  receivedMessage: {
    backgroundColor: COLORS.surfaceLight,
    alignSelf: 'flex-start',
  },
  messageText: {
    fontSize: 14,
    color: COLORS.text,
    lineHeight: 20,
  },
  messageTime: {
    fontSize: 10,
    color: COLORS.textMuted,
    marginTop: 4,
  },
  searchResults: {
    marginTop: 16,
  },
  searchResultsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
  },
  searchResultItem: {
    padding: 12,
    backgroundColor: COLORS.surfaceLight,
    borderRadius: 8,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: COLORS.primary,
  },
});

export default OfflineDemo;
