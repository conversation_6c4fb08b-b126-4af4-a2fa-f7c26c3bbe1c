import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Dimensions,
  StyleSheet,
  Animated,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// import { BlurView } from 'expo-blur'; // Optional - using regular View for compatibility

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface TextUpdateCreatorProps {
  visible: boolean;
  onClose: () => void;
  onPost: (text: string) => void;
  isPosting?: boolean;
}

const backgroundColors = [
  '#667eea',
  '#764ba2',
  '#f093fb',
  '#f5576c',
  '#4facfe',
  '#43e97b',
  '#fa709a',
  '#ffecd2',
  '#a8edea',
  '#d299c2',
];

const gradients = [
  ['#667eea', '#764ba2'],
  ['#f093fb', '#f5576c'],
  ['#4facfe', '#00f2fe'],
  ['#43e97b', '#38f9d7'],
  ['#fa709a', '#fee140'],
  ['#ffecd2', '#fcb69f'],
  ['#a8edea', '#fed6e3'],
  ['#d299c2', '#fef9d7'],
  ['#89f7fe', '#66a6ff'],
  ['#fdbb2d', '#22c1c3'],
];

export const TextUpdateCreator: React.FC<TextUpdateCreatorProps> = ({
  visible,
  onClose,
  onPost,
  isPosting = false,
}) => {
  const [text, setText] = useState('');
  const [selectedBackgroundIndex, setSelectedBackgroundIndex] = useState(0);
  const [fontSize, setFontSize] = useState(24);
  const [textAlign, setTextAlign] = useState<'left' | 'center' | 'right'>('center');
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const textInputRef = useRef<TextInput>(null);

  // Keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setIsKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setIsKeyboardVisible(false);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  // Animation effects
  useEffect(() => {
    if (visible) {
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // Focus text input after animation
      setTimeout(() => {
        textInputRef.current?.focus();
      }, 500);
    } else {
      Animated.timing(slideAnim, {
        toValue: SCREEN_HEIGHT,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [visible]);

  // Handle post
  const handlePost = () => {
    if (isPosting || !text.trim()) return;
    onPost(text.trim());
  };

  // Handle back
  const handleBack = () => {
    if (isPosting) return;
    onClose();
  };

  // Cycle through text alignment
  const cycleTextAlign = () => {
    setTextAlign(current => {
      switch (current) {
        case 'left': return 'center';
        case 'center': return 'right';
        case 'right': return 'left';
        default: return 'center';
      }
    });
  };

  // Adjust font size
  const adjustFontSize = (increase: boolean) => {
    setFontSize(current => {
      const newSize = increase ? current + 2 : current - 2;
      return Math.max(16, Math.min(48, newSize));
    });
  };

  // Get current background style
  const getCurrentBackgroundStyle = () => {
    const gradient = gradients[selectedBackgroundIndex];
    return {
      backgroundColor: gradient[0],
      // For now, using solid color. In a real app, you'd use a gradient library
    };
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      onRequestClose={handleBack}
    >
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        {/* Background blur */}
        <View style={[StyleSheet.absoluteFill, { backgroundColor: 'rgba(0, 0, 0, 0.95)' }]} />
        
        <Animated.View
          style={[
            styles.content,
            {
              transform: [{ translateY: slideAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity 
              onPress={handleBack} 
              style={styles.headerButton}
              disabled={isPosting}
            >
              <Ionicons name="close" size={28} color="white" />
            </TouchableOpacity>
            
            <Text style={styles.headerTitle}>Text Update</Text>
            
            <TouchableOpacity 
              onPress={handlePost} 
              style={[
                styles.postButton, 
                (!text.trim() || isPosting) && styles.postButtonDisabled
              ]}
              disabled={!text.trim() || isPosting}
            >
              {isPosting ? (
                <ActivityIndicator size="small" color="white" />
              ) : (
                <Text style={styles.postButtonText}>Post</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* Text Preview */}
          <View style={styles.previewContainer}>
            <View style={[
              styles.textPreview,
              isKeyboardVisible ? getCurrentBackgroundStyle() : getCurrentBackgroundStyle()
            ]}>
              <TextInput
                ref={textInputRef}
                style={[
                  styles.textInput,
                  {
                    fontSize,
                    textAlign,
                    backgroundColor: isKeyboardVisible ? 'rgba(0, 0, 0, 0.3)' : 'transparent',
                    borderRadius: isKeyboardVisible ? 8 : 0,
                  },
                ]}
                placeholder="What's on your mind?"
                placeholderTextColor="rgba(255, 255, 255, 0.7)"
                value={text}
                onChangeText={setText}
                multiline
                maxLength={280}
                editable={!isPosting}
                scrollEnabled={true}
              />
            </View>
          </View>

          {/* Text Controls */}
          <View style={styles.textControls}>
            {/* Font Size Controls */}
            <View style={styles.controlGroup}>
              <Text style={styles.controlLabel}>Size</Text>
              <View style={styles.controlButtons}>
                <TouchableOpacity 
                  style={styles.controlButton} 
                  onPress={() => adjustFontSize(false)}
                  disabled={isPosting}
                >
                  <Ionicons name="remove" size={20} color="white" />
                </TouchableOpacity>
                <Text style={styles.fontSizeText}>{fontSize}</Text>
                <TouchableOpacity 
                  style={styles.controlButton} 
                  onPress={() => adjustFontSize(true)}
                  disabled={isPosting}
                >
                  <Ionicons name="add" size={20} color="white" />
                </TouchableOpacity>
              </View>
            </View>

            {/* Text Alignment */}
            <View style={styles.controlGroup}>
              <Text style={styles.controlLabel}>Align</Text>
              <TouchableOpacity 
                style={styles.controlButton} 
                onPress={cycleTextAlign}
                disabled={isPosting}
              >
                <Ionicons 
                  name={
                    textAlign === 'left' ? 'text-outline' :
                    textAlign === 'center' ? 'text' :
                    'text-outline'
                  } 
                  size={20} 
                  color="white" 
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Background Colors */}
          <View style={styles.backgroundSelector}>
            <Text style={styles.selectorTitle}>Background</Text>
            <View style={styles.colorGrid}>
              {gradients.map((gradient, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.colorOption,
                    { backgroundColor: gradient[0] },
                    selectedBackgroundIndex === index && styles.selectedColorOption,
                  ]}
                  onPress={() => setSelectedBackgroundIndex(index)}
                  disabled={isPosting}
                >
                  {selectedBackgroundIndex === index && (
                    <Ionicons name="checkmark" size={16} color="white" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Character Count */}
          <View style={styles.characterCount}>
            <Text style={styles.characterCountText}>{text.length}/280</Text>
          </View>
        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
  },
  content: {
    flex: 1,
    backgroundColor: 'black',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
  },
  headerButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  postButton: {
    backgroundColor: '#667eea',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 60,
    alignItems: 'center',
  },
  postButtonDisabled: {
    backgroundColor: '#666',
  },
  postButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  previewContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  textPreview: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  textInput: {
    color: 'white',
    fontWeight: '600',
    textAlignVertical: 'top',
    minHeight: 120,
    maxHeight: 200,
    paddingVertical: 10,
    paddingHorizontal: 5,
  },
  textControls: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'space-around',
  },
  controlGroup: {
    alignItems: 'center',
    gap: 8,
  },
  controlLabel: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  controlButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  controlButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fontSizeText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    minWidth: 24,
    textAlign: 'center',
  },
  backgroundSelector: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  selectorTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedColorOption: {
    borderColor: 'white',
  },
  characterCount: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    alignItems: 'flex-end',
  },
  characterCountText: {
    color: '#999',
    fontSize: 12,
  },
});
