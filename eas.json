{"cli": {"version": ">= 12.0.0", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"gradleCommand": ":app:assembleDebug", "buildType": "apk"}, "ios": {"buildConfiguration": "Debug", "simulator": false}, "env": {"APP_VARIANT": "development"}}, "standalone": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "production": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}}, "simple": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "env": {"NODE_ENV": "production"}}}, "submit": {"production": {}}}