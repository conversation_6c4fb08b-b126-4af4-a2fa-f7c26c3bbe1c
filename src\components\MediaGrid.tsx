import { Ionicons } from "@expo/vector-icons";
import React, { useRef, useState, useCallback } from "react";
import {
  Alert,
  Animated,
  Dimensions,
  FlatList,
  Modal,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import * as Sharing from "expo-sharing";
import { useResponsiveDesign } from "../hooks/useResponsiveDesign";
import { colors } from "../theme/colors";
import { MediaPreview } from "./MediaPreview";
import { MediaViewer } from "./MediaViewer";

const { width: SCREEN_WIDTH } = Dimensions.get("window");

interface MediaGridProps {
  media: {
    url: string;
    type: "image" | "video" | "file";
    caption?: string;
    fileType?: string;
    fileSize?: string;
  }[];
  onClose: () => void;
}

export const MediaGrid: React.FC<MediaGridProps> = ({
  media,
  onClose
}) => {
  const [selectedMedia, setSelectedMedia] = useState<(typeof media)[0] | null>(
    null,
  );
  const [showViewer, setShowViewer] = useState(false);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const { dimensions } = useResponsiveDesign();
  const isTablet = dimensions.width > 768;
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef<FlatList>(null);

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleMediaPress = (item: (typeof media)[0]) => {
    setSelectedMedia(item);
    setShowViewer(true);
  };

  const handleCloseViewer = () => {
    setShowViewer(false);
    setSelectedMedia(null);
  };

  const handleShare = async () => {
    if (!selectedMedia) {
      Alert.alert("Error", "No media selected to share");
      return;
    }

    try {
      const isAvailable = await Sharing.isAvailableAsync();
      if (!isAvailable) {
        Alert.alert("Error", "Sharing is not available on this device");
        return;
      }

      await Sharing.shareAsync(selectedMedia.url, {
        mimeType: selectedMedia.type === 'image' ? 'image/*' :
                  selectedMedia.type === 'video' ? 'video/*' : 'application/*',
        dialogTitle: `Share ${selectedMedia.type}`,
      });
    } catch (error) {
      console.error('❌ Error sharing media:', error);
      Alert.alert("Error", "Failed to share media");
    }
  };

  const numColumns = isTablet ? 4 : 3;
  const itemSize = (SCREEN_WIDTH - (numColumns + 1) * 8) / numColumns;

  const renderMediaItem = useCallback(({ item }: { item: (typeof media)[0] }) => (
    <TouchableOpacity
      style={[styles.mediaItem, { width: itemSize, height: itemSize }]}
      onPress={() => handleMediaPress(item)}
      activeOpacity={0.8}
    >
      <MediaPreview
        media={item}
        onPress={() => handleMediaPress(item)}
        style={styles.preview}
      />
    </TouchableOpacity>
  ), [itemSize, handleMediaPress]);

  const scrollToTop = useCallback(() => {
    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
  }, []);

  const getItemLayout = useCallback((_data: any, index: number) => {
    const rowIndex = Math.floor(index / numColumns);
    const itemHeight = itemSize + 8; // item height + margin
    return {
      length: itemHeight,
      offset: itemHeight * rowIndex,
      index,
    };
  }, [itemSize, numColumns]);

  const handleScroll = useCallback((event: any) => {
    const offsetY = event.nativeEvent.contentOffset.y;
    setShowScrollToTop(offsetY > 200); // Show button after scrolling 200px
  }, []);

  return (
    <Modal
      visible={true}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <Animated.View
        style={[
          styles.container,
          {
            opacity: fadeAnim,
          },
        ]}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={onClose}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.shareButton}
            onPress={handleShare}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Ionicons name="share-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        <FlatList
          ref={flatListRef}
          data={media}
          renderItem={renderMediaItem}
          keyExtractor={(item, index) => `${item.url}-${index}`}
          numColumns={numColumns}
          contentContainerStyle={styles.grid}
          showsVerticalScrollIndicator={true}
          showsHorizontalScrollIndicator={false}
          initialNumToRender={12}
          maxToRenderPerBatch={12}
          windowSize={5}
          removeClippedSubviews={true}
          getItemLayout={getItemLayout}
          bounces={true}
          bouncesZoom={false}
          scrollEventThrottle={16}
          decelerationRate="normal"
          onScroll={handleScroll}
        />

        {/* Scroll to Top FAB */}
        {showScrollToTop && (
          <TouchableOpacity
            style={styles.scrollToTopButton}
            onPress={scrollToTop}
            activeOpacity={0.8}
          >
            <Ionicons name="chevron-up" size={24} color={colors.background} />
          </TouchableOpacity>
        )}

        {showViewer && selectedMedia && (
          <MediaViewer
            media={media}
            initialIndex={media.findIndex((m) => m.url === selectedMedia.url)}
            onClose={handleCloseViewer}
          />
        )}
      </Animated.View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    paddingTop: Platform.OS === "ios" ? 48 : 16,
    backgroundColor: colors.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  closeButton: {
    padding: 8,
  },
  shareButton: {
    padding: 8,
  },
  grid: {
    padding: 4,
    paddingBottom: 80, // Add bottom padding for scroll-to-top FAB
  },
  mediaItem: {
    margin: 4,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: colors.mediaPreview.background,
    borderWidth: 1,
    borderColor: colors.mediaPreview.border,
  },
  preview: {
    width: "100%",
    height: "100%",
  },
  scrollToTopButton: {
    position: "absolute",
    bottom: 20,
    right: 20,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    justifyContent: "center",
    alignItems: "center",
    elevation: 8,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
});
