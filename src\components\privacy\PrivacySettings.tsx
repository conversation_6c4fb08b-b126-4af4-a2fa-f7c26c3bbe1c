import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';

import { IRACHAT_COLORS, BORDER_RADIUS, SHADOWS, SPACING } from '../../styles/iraChatDesignSystem';
import { ResponsiveScale, ResponsiveTypography } from '../../utils/responsiveUtils';
import privacyLockService, { LockType, AutoLockDuration, LockConfig } from '../../services/privacyLockService';

interface PrivacySettingsProps {
  onClose: () => void;
  onLockEnabled?: () => void;
}

const PrivacySettings: React.FC<PrivacySettingsProps> = ({ onClose, onLockEnabled }) => {
  const [config, setConfig] = useState<LockConfig>({
    lockType: LockType.NONE,
    autoLockDuration: AutoLockDuration.FIVE_MINUTES,
    useSystemAuth: false,
  });
  const [isSystemAuthAvailable, setIsSystemAuthAvailable] = useState(false);
  const [showPinSetup, setShowPinSetup] = useState(false);
  const [showPasswordSetup, setShowPasswordSetup] = useState(false);
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    loadCurrentConfig();
    checkSystemAuthAvailability();
  }, []);

  const loadCurrentConfig = async () => {
    try {
      const currentConfig = await privacyLockService.getLockConfig();
      setConfig(currentConfig);
    } catch (error) {
      console.error('Error loading privacy config:', error);
    }
  };

  const checkSystemAuthAvailability = async () => {
    const available = await privacyLockService.isSystemAuthAvailable();
    setIsSystemAuthAvailable(available);
  };

  const handleLockTypeChange = async (lockType: LockType) => {
    try {
      if (lockType === LockType.NONE) {
        Alert.alert(
          'Remove Privacy Lock',
          'Are you sure you want to remove the privacy lock? Your app will no longer be protected.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Remove',
              style: 'destructive',
              onPress: async () => {
                await privacyLockService.removeLock();
                setConfig(prev => ({ ...prev, lockType: LockType.NONE }));
                await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              }
            }
          ]
        );
        return;
      }

      if (lockType === LockType.PIN) {
        setShowPinSetup(true);
      } else if (lockType === LockType.PASSWORD) {
        setShowPasswordSetup(true);
      } else if (lockType === LockType.SYSTEM) {
        if (!isSystemAuthAvailable) {
          Alert.alert(
            'System Authentication Unavailable',
            'Please set up biometric authentication or screen lock on your device first.',
            [{ text: 'OK' }]
          );
          return;
        }
        
        const success = await privacyLockService.setupLock(LockType.SYSTEM);
        if (success) {
          setConfig(prev => ({ ...prev, lockType: LockType.SYSTEM }));
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          onLockEnabled?.();
        }
      }
    } catch (error) {
      console.error('Error changing lock type:', error);
      Alert.alert('Error', 'Failed to change lock type. Please try again.');
    }
  };

  const handlePinSetup = async () => {
    if (newPin.length < 4) {
      Alert.alert('Invalid PIN', 'PIN must be at least 4 digits long.');
      return;
    }

    if (newPin !== confirmPin) {
      Alert.alert('PIN Mismatch', 'PINs do not match. Please try again.');
      return;
    }

    try {
      const success = await privacyLockService.setupLock(LockType.PIN, newPin);
      if (success) {
        setConfig(prev => ({ ...prev, lockType: LockType.PIN }));
        setShowPinSetup(false);
        setNewPin('');
        setConfirmPin('');
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        onLockEnabled?.();
        Alert.alert('Success', 'PIN lock has been set up successfully.');
      } else {
        Alert.alert('Error', 'Failed to set up PIN lock. Please try again.');
      }
    } catch (error) {
      console.error('Error setting up PIN:', error);
      Alert.alert('Error', 'Failed to set up PIN lock. Please try again.');
    }
  };

  const handlePasswordSetup = async () => {
    if (newPassword.length < 6) {
      Alert.alert('Invalid Password', 'Password must be at least 6 characters long.');
      return;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('Password Mismatch', 'Passwords do not match. Please try again.');
      return;
    }

    try {
      const success = await privacyLockService.setupLock(LockType.PASSWORD, newPassword);
      if (success) {
        setConfig(prev => ({ ...prev, lockType: LockType.PASSWORD }));
        setShowPasswordSetup(false);
        setNewPassword('');
        setConfirmPassword('');
        await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        onLockEnabled?.();
        Alert.alert('Success', 'Password lock has been set up successfully.');
      } else {
        Alert.alert('Error', 'Failed to set up password lock. Please try again.');
      }
    } catch (error) {
      console.error('Error setting up password:', error);
      Alert.alert('Error', 'Failed to set up password lock. Please try again.');
    }
  };

  const handleAutoLockDurationChange = async (duration: AutoLockDuration) => {
    try {
      await privacyLockService.setAutoLockDuration(duration);
      setConfig(prev => ({ ...prev, autoLockDuration: duration }));
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    } catch (error) {
      console.error('Error setting auto-lock duration:', error);
    }
  };

  const getDurationLabel = (duration: AutoLockDuration): string => {
    switch (duration) {
      case AutoLockDuration.IMMEDIATE: return 'Immediately';
      case AutoLockDuration.THIRTY_SECONDS: return '30 seconds';
      case AutoLockDuration.ONE_MINUTE: return '1 minute';
      case AutoLockDuration.FIVE_MINUTES: return '5 minutes';
      case AutoLockDuration.FIFTEEN_MINUTES: return '15 minutes';
      case AutoLockDuration.THIRTY_MINUTES: return '30 minutes';
      case AutoLockDuration.ONE_HOUR: return '1 hour';
      case AutoLockDuration.NEVER: return 'Never';
      default: return 'Unknown';
    }
  };

  const renderLockTypeOption = (type: LockType, title: string, description: string, icon: string) => (
    <TouchableOpacity
      style={[
        styles.optionItem,
        config.lockType === type && styles.optionItemActive
      ]}
      onPress={() => handleLockTypeChange(type)}
      activeOpacity={0.7}
    >
      <View style={styles.optionIcon}>
        <Ionicons
          name={icon as any}
          size={24}
          color={config.lockType === type ? IRACHAT_COLORS.primary : IRACHAT_COLORS.textMuted}
        />
      </View>
      <View style={styles.optionContent}>
        <Text style={[
          styles.optionTitle,
          config.lockType === type && styles.optionTitleActive
        ]}>
          {title}
        </Text>
        <Text style={styles.optionDescription}>{description}</Text>
      </View>
      {config.lockType === type && (
        <Ionicons name="checkmark-circle" size={20} color={IRACHAT_COLORS.primary} />
      )}
    </TouchableOpacity>
  );

  const renderDurationOption = (duration: AutoLockDuration) => (
    <TouchableOpacity
      key={duration}
      style={[
        styles.durationOption,
        config.autoLockDuration === duration && styles.durationOptionActive
      ]}
      onPress={() => handleAutoLockDurationChange(duration)}
      activeOpacity={0.7}
    >
      <Text style={[
        styles.durationText,
        config.autoLockDuration === duration && styles.durationTextActive
      ]}>
        {getDurationLabel(duration)}
      </Text>
      {config.autoLockDuration === duration && (
        <Ionicons name="checkmark" size={16} color={IRACHAT_COLORS.primary} />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onClose} style={styles.closeButton}>
          <Ionicons name="close" size={24} color={IRACHAT_COLORS.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Privacy Lock</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Lock Type Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Lock Type</Text>
          <Text style={styles.sectionDescription}>
            Choose how you want to protect your app
          </Text>
          
          <View style={styles.optionsList}>
            {renderLockTypeOption(
              LockType.NONE,
              'No Lock',
              'App will not be locked',
              'lock-open'
            )}
            {renderLockTypeOption(
              LockType.PIN,
              'PIN Lock',
              'Use a numeric PIN to lock your app',
              'keypad'
            )}
            {renderLockTypeOption(
              LockType.PASSWORD,
              'Password Lock',
              'Use a custom password to lock your app',
              'key'
            )}
            {isSystemAuthAvailable && renderLockTypeOption(
              LockType.SYSTEM,
              'System Authentication',
              'Use your device\'s biometric or screen lock',
              'finger-print'
            )}
          </View>
        </View>

        {/* Auto-Lock Duration Section */}
        {config.lockType !== LockType.NONE && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Auto-Lock Duration</Text>
            <Text style={styles.sectionDescription}>
              How long to wait before automatically locking the app
            </Text>
            
            <View style={styles.durationList}>
              {Object.values(AutoLockDuration)
                .filter(d => typeof d === 'number')
                .map(duration => renderDurationOption(duration as AutoLockDuration))
              }
            </View>
          </View>
        )}

        {/* Current Status */}
        <View style={styles.statusSection}>
          <View style={styles.statusItem}>
            <Ionicons
              name={config.lockType === LockType.NONE ? 'lock-open' : 'lock-closed'}
              size={20}
              color={config.lockType === LockType.NONE ? IRACHAT_COLORS.warning : IRACHAT_COLORS.success}
            />
            <Text style={styles.statusText}>
              Privacy lock is {config.lockType === LockType.NONE ? 'disabled' : 'enabled'}
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* PIN Setup Modal */}
      <Modal visible={showPinSetup} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Set Up PIN Lock</Text>
            
            <TextInput
              style={styles.input}
              placeholder="Enter PIN (4-6 digits)"
              value={newPin}
              onChangeText={setNewPin}
              keyboardType="numeric"
              maxLength={6}
              secureTextEntry
            />
            
            <TextInput
              style={styles.input}
              placeholder="Confirm PIN"
              value={confirmPin}
              onChangeText={setConfirmPin}
              keyboardType="numeric"
              maxLength={6}
              secureTextEntry
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonSecondary]}
                onPress={() => {
                  setShowPinSetup(false);
                  setNewPin('');
                  setConfirmPin('');
                }}
              >
                <Text style={styles.modalButtonTextSecondary}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonPrimary]}
                onPress={handlePinSetup}
              >
                <Text style={styles.modalButtonTextPrimary}>Set PIN</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Password Setup Modal */}
      <Modal visible={showPasswordSetup} transparent animationType="slide">
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Set Up Password Lock</Text>
            
            <View style={styles.passwordInputContainer}>
              <TextInput
                style={styles.input}
                placeholder="Enter password (min 6 characters)"
                value={newPassword}
                onChangeText={setNewPassword}
                secureTextEntry={!showPassword}
              />
              <TouchableOpacity
                style={styles.passwordToggle}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Ionicons
                  name={showPassword ? 'eye-off' : 'eye'}
                  size={20}
                  color={IRACHAT_COLORS.textMuted}
                />
              </TouchableOpacity>
            </View>
            
            <TextInput
              style={styles.input}
              placeholder="Confirm password"
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry={!showPassword}
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonSecondary]}
                onPress={() => {
                  setShowPasswordSetup(false);
                  setNewPassword('');
                  setConfirmPassword('');
                  setShowPassword(false);
                }}
              >
                <Text style={styles.modalButtonTextSecondary}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.modalButton, styles.modalButtonPrimary]}
                onPress={handlePasswordSetup}
              >
                <Text style={styles.modalButtonTextPrimary}>Set Password</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  closeButton: {
    padding: SPACING.sm,
  },
  headerTitle: {
    fontSize: ResponsiveTypography.fontSize.xl,
    lineHeight: ResponsiveTypography.lineHeight.tight,
    color: IRACHAT_COLORS.text,
    flex: 1,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  headerSpacer: {
    width: ResponsiveScale.spacing(40),
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.md,
  },
  section: {
    marginVertical: SPACING.lg,
  },
  sectionTitle: {
    fontSize: ResponsiveTypography.fontSize.lg,
    lineHeight: ResponsiveTypography.lineHeight.tight,
    color: IRACHAT_COLORS.text,
    fontWeight: 'bold',
    marginBottom: SPACING.sm,
  },
  sectionDescription: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: IRACHAT_COLORS.textMuted,
    marginBottom: SPACING.md,
  },
  optionsList: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  optionItemActive: {
    backgroundColor: `${IRACHAT_COLORS.primary}10`,
  },
  optionIcon: {
    width: ResponsiveScale.spacing(40),
    alignItems: 'center',
  },
  optionContent: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  optionTitle: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: IRACHAT_COLORS.text,
    fontWeight: '600',
  },
  optionTitleActive: {
    color: IRACHAT_COLORS.primary,
  },
  optionDescription: {
    fontSize: ResponsiveTypography.fontSize.sm,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: IRACHAT_COLORS.textMuted,
    marginTop: SPACING.xs,
  },
  durationList: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
  },
  durationOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  durationOptionActive: {
    backgroundColor: `${IRACHAT_COLORS.primary}10`,
  },
  durationText: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: IRACHAT_COLORS.text,
  },
  durationTextActive: {
    color: IRACHAT_COLORS.primary,
    fontWeight: '600',
  },
  statusSection: {
    marginVertical: SPACING.lg,
    padding: SPACING.md,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.md,
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: IRACHAT_COLORS.text,
    marginLeft: SPACING.sm,
  },
  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: IRACHAT_COLORS.background,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    width: '90%',
    maxWidth: ResponsiveScale.spacing(400),
    ...SHADOWS.lg,
  },
  modalTitle: {
    fontSize: ResponsiveTypography.fontSize.xl,
    lineHeight: ResponsiveTypography.lineHeight.tight,
    color: IRACHAT_COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    fontWeight: 'bold',
  },
  input: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: IRACHAT_COLORS.text,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.sm,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    marginBottom: SPACING.md,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.border,
  },
  passwordInputContainer: {
    position: 'relative',
  },
  passwordToggle: {
    position: 'absolute',
    right: SPACING.md,
    top: SPACING.md,
    padding: SPACING.sm,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: SPACING.lg,
  },
  modalButton: {
    flex: 1,
    paddingVertical: SPACING.md,
    borderRadius: BORDER_RADIUS.sm,
    alignItems: 'center',
    marginHorizontal: SPACING.sm,
  },
  modalButtonPrimary: {
    backgroundColor: IRACHAT_COLORS.primary,
  },
  modalButtonSecondary: {
    backgroundColor: IRACHAT_COLORS.surface,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.border,
  },
  modalButtonTextPrimary: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: 'bold',
  },
  modalButtonTextSecondary: {
    fontSize: ResponsiveTypography.fontSize.base,
    lineHeight: ResponsiveTypography.lineHeight.normal,
    color: IRACHAT_COLORS.text,
  },
});

export default PrivacySettings;
