// 🖼️ MEDIA GALLERY WITH VERTICAL & HORIZONTAL SCROLL
// Complete media viewing experience with all media from chat

import React, { useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  Image,
  TouchableOpacity,
  Animated,
  PanResponder,
  Dimensions,
  StatusBar,
  StyleSheet,
  Text,
  FlatList,
  TextInput,
  Alert,
} from 'react-native';
import { Video, ResizeMode } from 'expo-av';
import { Ionicons } from '@expo/vector-icons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface MediaItem {
  id: string;
  type: 'image' | 'video';
  uri: string;
  caption?: string;
}

interface MediaGalleryProps {
  visible: boolean;
  mediaItems: MediaItem[];
  initialIndex: number;
  onClose: () => void;
  onCaptionUpdate?: (id: string, caption: string) => void;
}

export const MediaGallery: React.FC<MediaGalleryProps> = ({
  visible,
  mediaItems,
  initialIndex,
  onClose,
  onCaptionUpdate,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [showCaption, setShowCaption] = useState(false);
  const [captionText, setCaptionText] = useState('');
  const [isZoomed, setIsZoomed] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const scale = useRef(new Animated.Value(1)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;

  // Update caption when current item changes
  useEffect(() => {
    if (mediaItems[currentIndex]) {
      setCaptionText(mediaItems[currentIndex].caption || '');
    }
  }, [currentIndex, mediaItems]);

  // Scroll to initial index when modal opens
  useEffect(() => {
    if (visible && flatListRef.current && mediaItems.length > 0) {
      setTimeout(() => {
        flatListRef.current?.scrollToIndex({ 
          index: initialIndex, 
          animated: false 
        });
      }, 100);
    }
  }, [visible, initialIndex]);

  // Pan responder for zoom gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderMove: (evt, gestureState) => {
        const numberOfTouches = (evt.nativeEvent as any).numberOfTouches;
        
        if (numberOfTouches === 2) {
          // Pinch to zoom (simplified)
          const distance = Math.sqrt(
            Math.pow(evt.nativeEvent.touches[0].pageX - evt.nativeEvent.touches[1].pageX, 2) +
            Math.pow(evt.nativeEvent.touches[0].pageY - evt.nativeEvent.touches[1].pageY, 2)
          );
          
          const newScale = Math.max(0.5, Math.min(3, distance / 200));
          scale.setValue(newScale);
          setIsZoomed(newScale > 1);
        } else if (numberOfTouches === 1 && isZoomed) {
          // Pan when zoomed
          translateX.setValue(gestureState.dx);
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: () => {
        if ((scale as any)._value < 1) {
          Animated.parallel([
            Animated.spring(scale, { toValue: 1, useNativeDriver: true }),
            Animated.spring(translateX, { toValue: 0, useNativeDriver: true }),
            Animated.spring(translateY, { toValue: 0, useNativeDriver: true }),
          ]).start();
          setIsZoomed(false);
        }
      },
    })
  ).current;

  // Double tap to zoom
  const handleDoublePress = () => {
    const newZoom = isZoomed ? 1 : 2;
    
    Animated.parallel([
      Animated.spring(scale, { toValue: newZoom, useNativeDriver: true }),
      Animated.spring(translateX, { toValue: 0, useNativeDriver: true }),
      Animated.spring(translateY, { toValue: 0, useNativeDriver: true }),
    ]).start();
    
    setIsZoomed(newZoom > 1);
  };

  // Save caption
  const saveCaption = () => {
    const currentItem = mediaItems[currentIndex];
    if (currentItem && onCaptionUpdate) {
      onCaptionUpdate(currentItem.id, captionText);
    }
    setShowCaption(false);
  };

  // Render media item
  const renderMediaItem = ({ item, index }: { item: MediaItem; index: number }) => (
    <View style={styles.mediaContainer}>
      <View style={styles.mediaWrapper} {...panResponder.panHandlers}>
        <TouchableOpacity
          activeOpacity={1}
          onPress={handleDoublePress}
          style={styles.mediaTouchable}
        >
          <Animated.View
            style={[
              styles.mediaAnimated,
              {
                transform: [
                  { scale },
                  { translateX },
                  { translateY },
                ],
              },
            ]}
          >
            {item.type === 'image' ? (
              <Image
                source={{ uri: item.uri }}
                style={styles.media}
                resizeMode="contain"
              />
            ) : (
              <Video
                source={{ uri: item.uri }}
                style={styles.media}
                resizeMode={ResizeMode.CONTAIN}
                shouldPlay={index === currentIndex}
                isLooping={true}
                useNativeControls={true}
              />
            )}
          </Animated.View>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <StatusBar backgroundColor="black" barStyle="light-content" />
      <TouchableOpacity 
        style={styles.container}
        activeOpacity={1}
        onPress={onClose}
      >
        {/* Media List */}
        <FlatList
          ref={flatListRef}
          data={mediaItems}
          renderItem={renderMediaItem}
          keyExtractor={(item) => item.id}
          horizontal={true}
          pagingEnabled={true}
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / screenWidth);
            setCurrentIndex(index);
          }}
          getItemLayout={(data, index) => ({
            length: screenWidth,
            offset: screenWidth * index,
            index,
          })}
        />

        {/* Media Counter */}
        <View style={styles.counter}>
          <Text style={styles.counterText}>
            {currentIndex + 1} of {mediaItems.length}
          </Text>
        </View>

        {/* Caption */}
        {mediaItems[currentIndex]?.caption && (
          <View style={styles.captionContainer}>
            <Text style={styles.captionText}>
              {mediaItems[currentIndex].caption}
            </Text>
          </View>
        )}

        {/* Caption Input Modal */}
        {showCaption && (
          <View style={styles.captionInputContainer}>
            <View style={styles.captionInputBox}>
              <TextInput
                style={styles.captionInput}
                value={captionText}
                onChangeText={setCaptionText}
                placeholder="Add a caption..."
                placeholderTextColor="#999"
                multiline={true}
                maxLength={200}
              />
              <View style={styles.captionButtons}>
                <TouchableOpacity
                  style={[styles.captionButton, styles.cancelButton]}
                  onPress={() => setShowCaption(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.captionButton, styles.saveButton]}
                  onPress={saveCaption}
                >
                  <Text style={styles.saveButtonText}>Save</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </TouchableOpacity>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  mediaContainer: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaTouchable: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  mediaAnimated: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  media: {
    width: screenWidth,
    height: screenHeight * 0.8,
  },
  counter: {
    position: 'absolute',
    top: 60,
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  counterText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  captionContainer: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
    alignItems: 'center',
  },
  captionText: {
    color: 'white',
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
  },
  captionInputContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    padding: 20,
  },
  captionInputBox: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
  },
  captionInput: {
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: 16,
  },
  captionButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  captionButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginLeft: 12,
  },
  cancelButton: {
    backgroundColor: '#f0f0f0',
  },
  saveButton: {
    backgroundColor: '#007AFF',
  },
  cancelButtonText: {
    color: '#333',
    fontWeight: '500',
  },
  saveButtonText: {
    color: 'white',
    fontWeight: '500',
  },
});

export default MediaGallery;
