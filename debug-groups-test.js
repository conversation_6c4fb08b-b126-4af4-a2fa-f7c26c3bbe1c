// 🔧 DEBUG GROUPS TIMEOUT TEST SCRIPT
// Run this in your React Native debugger console to test groups functionality

console.log('🧪 Starting Groups Debug Test...');

// Test Firebase connectivity
async function testFirebaseConnection() {
  try {
    console.log('☁️ Testing Firebase connection...');
    
    // Check if Firebase services are available
    if (typeof db !== 'undefined') {
      console.log('✅ Firestore available');
      
      // Test a simple query to check connectivity
      try {
        const testQuery = await db.collection('groups').limit(1).get();
        console.log('✅ Firebase query test successful');
        return true;
      } catch (queryError) {
        console.error('❌ Firebase query test failed:', queryError);
        return false;
      }
    } else {
      console.log('⚠️ Firestore not available in this context');
      return false;
    }
  } catch (error) {
    console.error('❌ Firebase connection test failed:', error);
    return false;
  }
}

// Test network connectivity
async function testNetworkConnectivity() {
  try {
    console.log('🌐 Testing network connectivity...');
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const response = await fetch('https://www.google.com', {
      method: 'HEAD',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (response.ok) {
      console.log('✅ Network connectivity confirmed');
      return true;
    } else {
      console.log('⚠️ Network response not OK:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Network connectivity test failed:', error);
    return false;
  }
}

// Test groups service availability
function testGroupsService() {
  try {
    console.log('🔍 Testing groups service availability...');
    
    // Check if realGroupService is available
    if (typeof realGroupService !== 'undefined') {
      console.log('✅ realGroupService available');
      
      // Check if getUserGroups method exists
      if (typeof realGroupService.getUserGroups === 'function') {
        console.log('✅ getUserGroups method available');
        return true;
      } else {
        console.log('❌ getUserGroups method not found');
        return false;
      }
    } else {
      console.log('⚠️ realGroupService not available in this context');
      return false;
    }
  } catch (error) {
    console.error('❌ Groups service test failed:', error);
    return false;
  }
}

// Test user authentication
function testUserAuth() {
  try {
    console.log('🔐 Testing user authentication...');
    
    // Check if auth is available
    if (typeof auth !== 'undefined' && auth.currentUser) {
      console.log('✅ User authenticated:', auth.currentUser.uid);
      return { authenticated: true, userId: auth.currentUser.uid };
    } else {
      console.log('⚠️ User not authenticated or auth not available');
      return { authenticated: false, userId: null };
    }
  } catch (error) {
    console.error('❌ User auth test failed:', error);
    return { authenticated: false, userId: null };
  }
}

// Test timeout behavior
async function testTimeoutBehavior() {
  try {
    console.log('⏰ Testing timeout behavior...');
    
    // Create a promise that resolves after 2 seconds
    const fastPromise = new Promise(resolve => 
      setTimeout(() => resolve('fast'), 2000)
    );
    
    // Create a promise that times out after 1 second
    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Test timeout')), 1000)
    );
    
    try {
      const result = await Promise.race([fastPromise, timeoutPromise]);
      console.log('❌ Timeout test failed - should have timed out');
      return false;
    } catch (error) {
      if (error.message.includes('timeout')) {
        console.log('✅ Timeout behavior working correctly');
        return true;
      } else {
        console.log('❌ Unexpected error in timeout test:', error);
        return false;
      }
    }
  } catch (error) {
    console.error('❌ Timeout test failed:', error);
    return false;
  }
}

// Test local storage functionality
async function testLocalStorage() {
  try {
    console.log('💾 Testing local storage...');
    
    // Check if AsyncStorage is available
    if (typeof AsyncStorage !== 'undefined') {
      console.log('✅ AsyncStorage available');
      
      // Test write and read
      const testKey = 'groups_test_key';
      const testData = JSON.stringify([{ id: 'test', name: 'Test Group' }]);
      
      await AsyncStorage.setItem(testKey, testData);
      const retrieved = await AsyncStorage.getItem(testKey);
      
      if (retrieved === testData) {
        console.log('✅ Local storage read/write working');
        await AsyncStorage.removeItem(testKey); // Cleanup
        return true;
      } else {
        console.log('❌ Local storage data mismatch');
        return false;
      }
    } else {
      console.log('⚠️ AsyncStorage not available in this context');
      return false;
    }
  } catch (error) {
    console.error('❌ Local storage test failed:', error);
    return false;
  }
}

// Main test function
async function runGroupsDebugTests() {
  console.log('🚀 Running all groups debug tests...');
  
  const results = {
    firebase: await testFirebaseConnection(),
    network: await testNetworkConnectivity(),
    groupsService: testGroupsService(),
    userAuth: testUserAuth(),
    timeout: await testTimeoutBehavior(),
    localStorage: await testLocalStorage()
  };
  
  console.log('📊 Test Results:', results);
  
  // Analyze results
  if (results.firebase && results.network && results.groupsService && results.userAuth.authenticated) {
    console.log('🎉 All core systems working! Groups should load properly.');
  } else {
    console.log('⚠️ Some systems have issues:');
    
    if (!results.firebase) {
      console.log('  - Firebase connection issue');
    }
    if (!results.network) {
      console.log('  - Network connectivity issue');
    }
    if (!results.groupsService) {
      console.log('  - Groups service not available');
    }
    if (!results.userAuth.authenticated) {
      console.log('  - User not authenticated');
    }
  }
  
  return results;
}

// Instructions for use
console.log(`
🔧 GROUPS DEBUG INSTRUCTIONS:

1. Open your React Native app
2. Navigate to the Groups tab
3. Open the debugger console
4. Copy and paste this entire script
5. Run: runGroupsDebugTests()
6. Check the console output for any issues

📝 COMMON TIMEOUT CAUSES:

1. Slow network connection
2. Firebase rules blocking access
3. Too many concurrent queries
4. Large dataset causing slow response
5. Authentication token expired

🐛 IF GROUPS STILL TIMEOUT:

1. Check your internet connection speed
2. Verify Firebase project configuration
3. Check Firestore security rules
4. Try clearing app cache/data
5. Test with a different network

💡 TIMEOUT FIX APPLIED:
- Removed duplicate timeout mechanisms
- Increased timeout to 15 seconds
- Added better fallback to cached data
- Improved error handling and logging
`);

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runGroupsDebugTests,
    testFirebaseConnection,
    testNetworkConnectivity,
    testGroupsService,
    testUserAuth,
    testTimeoutBehavior,
    testLocalStorage
  };
}
