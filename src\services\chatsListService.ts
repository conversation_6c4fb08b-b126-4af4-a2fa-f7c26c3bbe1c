// 💬 WHATSAPP-STYLE CHATS LIST SERVICE
// Manages chat list with real-time updates, offline persistence, and WhatsApp functionality

import {
  collection,
  doc,
  setDoc,
  getDocs,
  query,
  orderBy,
  onSnapshot,
  serverTimestamp,
  limit,
  updateDoc
} from 'firebase/firestore';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { db } from './firebaseSimple';
import { offlineDatabaseService } from './offlineDatabase';

export interface ChatListItem {
  id: string;
  partnerId: string;
  partnerName: string;
  partnerAvatar?: string;
  partnerEmail?: string;
  partnerPhone?: string;
  lastMessage: {
    content: string;
    senderId: string;
    senderName: string;
    timestamp: Date;
    type: 'text' | 'image' | 'video' | 'audio' | 'file';
    isRead: boolean;
  };
  unreadCount: number;
  isPinned: boolean;
  isMuted: boolean;
  isArchived: boolean;
  isOnline: boolean;
  lastSeen?: Date;
  createdAt: Date;
  updatedAt: Date;
}

class ChatsListService {
  private static instance: ChatsListService;
  private readonly STORAGE_KEY = 'irachat_chats_list';
  private listeners: { [userId: string]: () => void } = {};

  static getInstance(): ChatsListService {
    if (!ChatsListService.instance) {
      ChatsListService.instance = new ChatsListService();
    }
    return ChatsListService.instance;
  }

  // Add or update chat in the list
  async addOrUpdateChat(
    currentUserId: string,
    partnerId: string,
    partnerName: string,
    lastMessage: ChatListItem['lastMessage'],
    partnerInfo?: {
      avatar?: string;
      email?: string;
      phone?: string;
      isOnline?: boolean;
      lastSeen?: Date;
    }
  ): Promise<void> {
    try {
      const chatId = this.generateChatId(currentUserId, partnerId);
      
      const chatItem: ChatListItem = {
        id: chatId,
        partnerId,
        partnerName,
        partnerAvatar: partnerInfo?.avatar,
        partnerEmail: partnerInfo?.email,
        partnerPhone: partnerInfo?.phone,
        lastMessage,
        unreadCount: lastMessage.senderId !== currentUserId ? 1 : 0,
        isPinned: false,
        isMuted: false,
        isArchived: false,
        isOnline: partnerInfo?.isOnline || false,
        lastSeen: partnerInfo?.lastSeen,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save to Firestore chats collection
      await this.saveChatToFirestore(currentUserId, chatItem);
      
      // Save to local storage
      await this.saveChatToLocal(currentUserId, chatItem);
      
      // Save to SQLite for offline access
      await this.saveChatToSQLite(currentUserId, chatItem);

      console.log('✅ Chat added/updated in chats list:', chatId);
    } catch (error) {
      console.error('❌ Failed to add/update chat:', error);
      throw error;
    }
  }

  // Get chats list for user
  async getChatsForUser(userId: string): Promise<ChatListItem[]> {
    try {
      // Try Firestore first
      const firestoreChats = await this.getChatsFromFirestore(userId);
      if (firestoreChats.length > 0) {
        // Update local storage
        await this.saveChatsToLocal(userId, firestoreChats);
        return firestoreChats;
      }

      // Fallback to local storage
      const localChats = await this.getChatsFromLocal(userId);
      if (localChats.length > 0) {
        return localChats;
      }

      // Final fallback to SQLite
      return await this.getChatsFromSQLite(userId);
    } catch (error) {
      console.error('❌ Failed to get chats:', error);
      return [];
    }
  }

  // Subscribe to real-time chats updates
  subscribeToChats(
    userId: string,
    callback: (chats: ChatListItem[]) => void
  ): () => void {
    const chatsRef = collection(db, 'userChats', userId, 'chats');
    const q = query(chatsRef, orderBy('updatedAt', 'desc'), limit(100));

    const unsubscribe = onSnapshot(
      q,
      async (snapshot) => {
        const chats: ChatListItem[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          chats.push({
            ...data,
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
            lastMessage: {
              ...data.lastMessage,
              timestamp: data.lastMessage?.timestamp?.toDate() || new Date(),
            },
            lastSeen: data.lastSeen?.toDate(),
          } as ChatListItem);
        });

        // Update local storage
        await this.saveChatsToLocal(userId, chats);
        
        callback(chats);
      },
      (error) => {
        console.error('❌ Chats subscription error:', error);
        // Fallback to local data
        this.getChatsFromLocal(userId).then(callback);
      }
    );

    this.listeners[userId] = unsubscribe;
    return unsubscribe;
  }

  // Update chat's last message
  async updateLastMessage(
    currentUserId: string,
    partnerId: string,
    lastMessage: ChatListItem['lastMessage']
  ): Promise<void> {
    try {
      const chatId = this.generateChatId(currentUserId, partnerId);
      
      // Update in Firestore
      const chatRef = doc(db, 'userChats', currentUserId, 'chats', chatId);
      await updateDoc(chatRef, {
        lastMessage,
        updatedAt: serverTimestamp(),
        ...(lastMessage.senderId !== currentUserId && {
          unreadCount: 1 // Increment unread count for received messages
        })
      });

      console.log('✅ Last message updated for chat:', chatId);
    } catch (error) {
      console.error('❌ Failed to update last message:', error);
    }
  }

  // Mark chat as read
  async markChatAsRead(userId: string, chatId: string): Promise<void> {
    try {
      const chatRef = doc(db, 'userChats', userId, 'chats', chatId);
      await updateDoc(chatRef, {
        unreadCount: 0,
        'lastMessage.isRead': true,
        updatedAt: serverTimestamp(),
      });

      console.log('✅ Chat marked as read:', chatId);
    } catch (error) {
      console.error('❌ Failed to mark chat as read:', error);
    }
  }

  // Pin/unpin chat
  async togglePinChat(userId: string, chatId: string, isPinned: boolean): Promise<void> {
    try {
      const chatRef = doc(db, 'userChats', userId, 'chats', chatId);
      await updateDoc(chatRef, {
        isPinned,
        updatedAt: serverTimestamp(),
      });

      console.log(`✅ Chat ${isPinned ? 'pinned' : 'unpinned'}:`, chatId);
    } catch (error) {
      console.error('❌ Failed to toggle pin chat:', error);
    }
  }

  // Archive/unarchive chat
  async toggleArchiveChat(userId: string, chatId: string, isArchived: boolean): Promise<void> {
    try {
      const chatRef = doc(db, 'userChats', userId, 'chats', chatId);
      await updateDoc(chatRef, {
        isArchived,
        updatedAt: serverTimestamp(),
      });

      console.log(`✅ Chat ${isArchived ? 'archived' : 'unarchived'}:`, chatId);
    } catch (error) {
      console.error('❌ Failed to toggle archive chat:', error);
    }
  }

  // Delete chat
  async deleteChat(userId: string, chatId: string): Promise<void> {
    try {
      // Remove from Firestore
      const chatRef = doc(db, 'userChats', userId, 'chats', chatId);
      await updateDoc(chatRef, {
        isArchived: true,
        updatedAt: serverTimestamp(),
      });

      // Remove from local storage
      const chats = await this.getChatsFromLocal(userId);
      const updatedChats = chats.filter(chat => chat.id !== chatId);
      await this.saveChatsToLocal(userId, updatedChats);

      console.log('✅ Chat deleted:', chatId);
    } catch (error) {
      console.error('❌ Failed to delete chat:', error);
    }
  }

  // Generate consistent chat ID
  private generateChatId(userId1: string, userId2: string): string {
    return [userId1, userId2].sort().join('_');
  }

  // Private methods for data persistence
  private async saveChatToFirestore(userId: string, chat: ChatListItem): Promise<void> {
    const chatRef = doc(db, 'userChats', userId, 'chats', chat.id);

    // Clean data to remove undefined fields
    const cleanChat = {
      id: chat.id,
      partnerId: chat.partnerId,
      partnerName: chat.partnerName,
      ...(chat.partnerAvatar && { partnerAvatar: chat.partnerAvatar }),
      ...(chat.partnerEmail && { partnerEmail: chat.partnerEmail }),
      ...(chat.partnerPhone && { partnerPhone: chat.partnerPhone }),
      lastMessage: {
        content: chat.lastMessage.content,
        senderId: chat.lastMessage.senderId,
        senderName: chat.lastMessage.senderName,
        timestamp: serverTimestamp(),
        type: chat.lastMessage.type,
        isRead: chat.lastMessage.isRead,
      },
      unreadCount: chat.unreadCount,
      isPinned: chat.isPinned,
      isMuted: chat.isMuted,
      isArchived: chat.isArchived,
      isOnline: chat.isOnline,
      ...(chat.lastSeen && { lastSeen: chat.lastSeen }),
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    };

    await setDoc(chatRef, cleanChat, { merge: true });
  }

  private async getChatsFromFirestore(userId: string): Promise<ChatListItem[]> {
    const chatsRef = collection(db, 'userChats', userId, 'chats');
    const q = query(chatsRef, orderBy('updatedAt', 'desc'), limit(100));
    const snapshot = await getDocs(q);
    
    const chats: ChatListItem[] = [];
    snapshot.forEach((doc) => {
      const data = doc.data();
      chats.push({
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
        lastMessage: {
          ...data.lastMessage,
          timestamp: data.lastMessage?.timestamp?.toDate() || new Date(),
        },
        lastSeen: data.lastSeen?.toDate(),
      } as ChatListItem);
    });

    return chats;
  }

  private async saveChatToLocal(userId: string, chat: ChatListItem): Promise<void> {
    const key = `${this.STORAGE_KEY}_${userId}`;
    const existingChats = await this.getChatsFromLocal(userId);
    const updatedChats = existingChats.filter(c => c.id !== chat.id);
    updatedChats.unshift(chat);
    
    await AsyncStorage.setItem(key, JSON.stringify(updatedChats));
  }

  private async saveChatsToLocal(userId: string, chats: ChatListItem[]): Promise<void> {
    const key = `${this.STORAGE_KEY}_${userId}`;
    await AsyncStorage.setItem(key, JSON.stringify(chats));
  }

  private async getChatsFromLocal(userId: string): Promise<ChatListItem[]> {
    try {
      const key = `${this.STORAGE_KEY}_${userId}`;
      const chatsJson = await AsyncStorage.getItem(key);
      if (chatsJson) {
        const chats = JSON.parse(chatsJson);
        return chats.map((chat: any) => ({
          ...chat,
          createdAt: new Date(chat.createdAt),
          updatedAt: new Date(chat.updatedAt),
          lastMessage: {
            ...chat.lastMessage,
            timestamp: new Date(chat.lastMessage.timestamp),
          },
          lastSeen: chat.lastSeen ? new Date(chat.lastSeen) : undefined,
        }));
      }
      return [];
    } catch (error) {
      console.error('❌ Failed to get chats from local storage:', error);
      return [];
    }
  }

  private async saveChatToSQLite(userId: string, chat: ChatListItem): Promise<void> {
    try {
      if (!offlineDatabaseService.isReady()) return;
      
      const db = offlineDatabaseService.getDatabase();
      await db.runAsync(`
        INSERT OR REPLACE INTO chats_list (
          id, userId, partnerId, partnerName, partnerAvatar, partnerEmail, partnerPhone,
          lastMessageContent, lastMessageSenderId, lastMessageTimestamp, lastMessageType,
          unreadCount, isPinned, isMuted, isArchived, isOnline, lastSeen,
          createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        chat.id, userId, chat.partnerId, chat.partnerName || null, chat.partnerAvatar || null,
        chat.partnerEmail || null, chat.partnerPhone || null, chat.lastMessage.content,
        chat.lastMessage.senderId, chat.lastMessage.timestamp.toISOString(),
        chat.lastMessage.type, chat.unreadCount, chat.isPinned ? 1 : 0,
        chat.isMuted ? 1 : 0, chat.isArchived ? 1 : 0, chat.isOnline ? 1 : 0,
        chat.lastSeen?.toISOString() || null, chat.createdAt.toISOString(),
        chat.updatedAt.toISOString()
      ]);
    } catch (error) {
      console.error('❌ Failed to save chat to SQLite:', error);
    }
  }

  private async getChatsFromSQLite(userId: string): Promise<ChatListItem[]> {
    try {
      if (!offlineDatabaseService.isReady()) return [];
      
      const db = offlineDatabaseService.getDatabase();
      const result = await db.getAllAsync(`
        SELECT * FROM chats_list 
        WHERE userId = ? AND isArchived = 0
        ORDER BY updatedAt DESC
        LIMIT 100
      `, [userId]);

      return result.map((row: any) => ({
        id: row.id,
        partnerId: row.partnerId,
        partnerName: row.partnerName,
        partnerAvatar: row.partnerAvatar,
        partnerEmail: row.partnerEmail,
        partnerPhone: row.partnerPhone,
        lastMessage: {
          content: row.lastMessageContent,
          senderId: row.lastMessageSenderId,
          senderName: row.lastMessageSenderId === userId ? 'You' : row.partnerName,
          timestamp: new Date(row.lastMessageTimestamp),
          type: row.lastMessageType,
          isRead: row.unreadCount === 0,
        },
        unreadCount: row.unreadCount,
        isPinned: row.isPinned === 1,
        isMuted: row.isMuted === 1,
        isArchived: row.isArchived === 1,
        isOnline: row.isOnline === 1,
        lastSeen: row.lastSeen ? new Date(row.lastSeen) : undefined,
        createdAt: new Date(row.createdAt),
        updatedAt: new Date(row.updatedAt),
      }));
    } catch (error) {
      console.error('❌ Failed to get chats from SQLite:', error);
      return [];
    }
  }

  // Cleanup
  cleanup(userId: string): void {
    if (this.listeners[userId]) {
      this.listeners[userId]();
      delete this.listeners[userId];
    }
  }
}

export const chatsListService = ChatsListService.getInstance();
export default chatsListService;
