// Enhanced Firebase Testing Utilities with Offline Support
import { createUserWithEmailAndPassword } from "firebase/auth";
import { collection, getDocs, doc, setDoc, deleteDoc, getDoc } from "firebase/firestore";
import { auth, db } from "../services/firebaseSimple";

// Declare global types for React Native environment
declare const navigator: any;
declare const window: any;

// Test result interfaces
export interface FirebaseTestResult {
  success: boolean;
  message: string;
  details?: any;
  error?: string;
  code?: string;
  timestamp: number;
}

export interface ConnectionTestResult extends FirebaseTestResult {
  firestoreConnected: boolean;
  authConnected: boolean;
  latency?: number;
}

export interface AuthTestResult extends FirebaseTestResult {
  userCreated: boolean;
  userDeleted: boolean;
  userId?: string;
}

export interface FirebaseInfo {
  authDomain: string;
  projectId: string;
  isConnected: boolean;
  isOnline: boolean;
  lastTestTime?: number;
  testResults?: {
    connection: boolean;
    auth: boolean;
    firestore: boolean;
  };
}

/**
 * Enhanced Firebase connection test with detailed results
 */
export const testFirebaseConnection = async (): Promise<ConnectionTestResult> => {
  const startTime = Date.now();

  try {
    let firestoreConnected = false;
    let authConnected = false;

    // Test Firestore connection
    try {
      const testCollection = collection(db, "test");
      await getDocs(testCollection);
      firestoreConnected = true;
    } catch (firestoreError) {
      // Firestore connection failed
    }

    // Test Auth connection
    try {
      authConnected = !!auth && !!auth.app;
    } catch (authError) {
      // Auth connection failed
    }

    const latency = Date.now() - startTime;
    const success = firestoreConnected && authConnected;

    return {
      success,
      message: success
        ? "Firebase connection successful"
        : "Firebase connection failed",
      firestoreConnected,
      authConnected,
      latency,
      timestamp: Date.now(),
      details: {
        firestoreStatus: firestoreConnected ? "connected" : "failed",
        authStatus: authConnected ? "connected" : "failed",
        latencyMs: latency
      }
    };
  } catch (error) {
    return {
      success: false,
      message: "Firebase connection test failed",
      firestoreConnected: false,
      authConnected: false,
      timestamp: Date.now(),
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
};

/**
 * Enhanced Firebase Auth configuration test
 */
export const testAuthConfiguration = async (): Promise<AuthTestResult> => {
  try {
    // Check if auth is available
    if (!auth) {
      return {
        success: false,
        message: "Firebase Auth is not available",
        userCreated: false,
        userDeleted: false,
        timestamp: Date.now(),
        error: "Auth service not initialized"
      };
    }

    // Try to create a test user to see if auth is properly configured
    const testEmail = `test-${Date.now()}@example.com`;
    const testPassword = "testpassword123";

    let userCreated = false;
    let userDeleted = false;
    let userId: string | undefined;

    try {
      const userCred = await createUserWithEmailAndPassword(
        auth,
        testEmail,
        testPassword,
      );
      userCreated = true;
      userId = userCred.user.uid;

      // Clean up the test user
      try {
        await userCred.user.delete();
        userDeleted = true;
      } catch (deleteError) {
        // User deletion failed, but creation succeeded
      }

      return {
        success: userCreated,
        message: userDeleted
          ? "Auth configuration is working"
          : "Auth working but cleanup failed",
        userCreated,
        userDeleted,
        userId,
        timestamp: Date.now(),
        details: {
          testEmail,
          userCreationSuccessful: userCreated,
          userDeletionSuccessful: userDeleted
        }
      };
    } catch (authError: any) {
      return {
        success: false,
        message: authError.message || "Auth test failed",
        userCreated: false,
        userDeleted: false,
        timestamp: Date.now(),
        error: authError.message,
        code: authError.code
      };
    }
  } catch (error: any) {
    return {
      success: false,
      message: "Auth configuration test failed",
      userCreated: false,
      userDeleted: false,
      timestamp: Date.now(),
      error: error.message || "Unknown error",
      code: error.code
    };
  }
};

/**
 * Test Firestore read/write operations
 */
export const testFirestoreOperations = async (): Promise<FirebaseTestResult> => {
  try {
    if (!db) {
      return {
        success: false,
        message: "Firestore is not available",
        timestamp: Date.now(),
        error: "Database service not initialized"
      };
    }

    const testDocId = `test-${Date.now()}`;
    const testData = {
      message: "Firebase test document",
      timestamp: new Date(),
      testId: testDocId
    };

    // Test write operation
    const testDocRef = doc(db, "test", testDocId);
    await setDoc(testDocRef, testData);

    // Test read operation
    const docSnap = await getDoc(testDocRef);
    const readSuccess = docSnap.exists();

    // Test delete operation
    await deleteDoc(testDocRef);

    return {
      success: readSuccess,
      message: readSuccess
        ? "Firestore operations successful"
        : "Firestore read operation failed",
      timestamp: Date.now(),
      details: {
        writeSuccessful: true,
        readSuccessful: readSuccess,
        deleteSuccessful: true,
        testDocId
      }
    };
  } catch (error: any) {
    return {
      success: false,
      message: "Firestore operations test failed",
      timestamp: Date.now(),
      error: error.message || "Unknown error",
      code: error.code
    };
  }
};

/**
 * Get Firebase configuration info
 */
export const getFirebaseConfigInfo = async (): Promise<FirebaseInfo> => {
  try {
    const connectionTest = await testFirebaseConnection();

    // Safely check for online status
    const isOnline = typeof window !== 'undefined' &&
                     typeof navigator !== 'undefined' ?
                     navigator.onLine : true;

    return {
      authDomain: (auth as any)?.config?.authDomain || "unknown",
      projectId: (auth as any)?.config?.projectId || "unknown",
      isConnected: connectionTest.success,
      isOnline,
      lastTestTime: Date.now(),
      testResults: {
        connection: connectionTest.success,
        auth: connectionTest.authConnected,
        firestore: connectionTest.firestoreConnected
      }
    };
  } catch (error) {
    return {
      authDomain: "unknown",
      projectId: "unknown",
      isConnected: false,
      isOnline: false,
      lastTestTime: Date.now(),
      testResults: {
        connection: false,
        auth: false,
        firestore: false
      }
    };
  }
};

/**
 * Run comprehensive Firebase test suite
 */
export const runFirebaseTestSuite = async (): Promise<{
  overall: FirebaseTestResult;
  tests: {
    connection: ConnectionTestResult;
    auth: AuthTestResult;
    firestore: FirebaseTestResult;
  };
  info: FirebaseInfo;
}> => {
  try {
    const connectionTest = await testFirebaseConnection();
    const authTest = await testAuthConfiguration();
    const firestoreTest = await testFirestoreOperations();
    const info = await getFirebaseConfigInfo();

    const allTestsPassed = connectionTest.success &&
                          authTest.success &&
                          firestoreTest.success;

    return {
      overall: {
        success: allTestsPassed,
        message: allTestsPassed
          ? "All Firebase tests passed"
          : "Some Firebase tests failed",
        timestamp: Date.now(),
        details: {
          totalTests: 3,
          passedTests: [connectionTest, authTest, firestoreTest]
            .filter(test => test.success).length,
          connectionLatency: connectionTest.latency
        }
      },
      tests: {
        connection: connectionTest,
        auth: authTest,
        firestore: firestoreTest
      },
      info
    };
  } catch (error: any) {
    const info = await getFirebaseConfigInfo();

    return {
      overall: {
        success: false,
        message: "Firebase test suite failed",
        timestamp: Date.now(),
        error: error.message || "Unknown error"
      },
      tests: {
        connection: {
          success: false,
          message: "Not run",
          firestoreConnected: false,
          authConnected: false,
          timestamp: Date.now()
        },
        auth: {
          success: false,
          message: "Not run",
          userCreated: false,
          userDeleted: false,
          timestamp: Date.now()
        },
        firestore: {
          success: false,
          message: "Not run",
          timestamp: Date.now()
        }
      },
      info
    };
  }
};

/**
 * Test Firebase offline capabilities
 */
export const testOfflineCapabilities = async (): Promise<FirebaseTestResult> => {
  try {
    // Test offline persistence and sync capabilities for React Native
    const hasAsyncStorage = typeof require !== 'undefined';
    const hasNetInfo = typeof require !== 'undefined';

    // Check for basic offline storage capabilities
    const isOfflineSupported = hasAsyncStorage && hasNetInfo;

    return {
      success: isOfflineSupported,
      message: isOfflineSupported
        ? "Offline capabilities supported"
        : "Offline capabilities not supported",
      timestamp: Date.now(),
      details: {
        asyncStorageSupported: hasAsyncStorage,
        netInfoSupported: hasNetInfo,
        sqliteSupported: typeof require !== 'undefined', // SQLite for React Native
        firestoreOfflineSupported: true // Firestore has built-in offline support
      }
    };
  } catch (error: any) {
    return {
      success: false,
      message: "Offline capabilities test failed",
      timestamp: Date.now(),
      error: error.message || "Unknown error"
    };
  }
};

// Export Firebase instances for debugging
export const getFirebaseInfo = () => {
  return {
    authDomain: auth?.app?.options?.authDomain || "Unknown",
    projectId: auth?.app?.options?.projectId || "Unknown",
    isConnected: !!db && !!auth,
  };
};
