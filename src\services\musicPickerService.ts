/**
 * Music Picker Service for IraChat
 * Provides background music selection for stories
 */

import { Audio } from 'expo-av';
import { Alert } from 'react-native';

export interface MusicTrack {
  id: string;
  title: string;
  artist: string;
  duration: number;
  uri: string;
  thumbnail?: string;
  genre?: string;
  isPopular?: boolean;
}

export interface MusicCategory {
  id: string;
  name: string;
  tracks: MusicTrack[];
}

class MusicPickerService {
  private sound: Audio.Sound | null = null;
  private isPlaying = false;

  /**
   * Get predefined music categories with sample tracks
   */
  getMusicCategories(): MusicCategory[] {
    return [
      {
        id: 'trending',
        name: 'Trending',
        tracks: [
          {
            id: 'trend1',
            title: 'Upbeat Vibes',
            artist: 'IraChat Music',
            duration: 30,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', // Sample audio
            isPopular: true,
            genre: 'Pop'
          },
          {
            id: 'trend2',
            title: 'Chill Beats',
            artist: 'Ira<PERSON>hat Music',
            duration: 45,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            isPopular: true,
            genre: 'Lo-Fi'
          },
          {
            id: 'trend3',
            title: 'Energy Boost',
            artist: 'Ira<PERSON>hat Music',
            duration: 35,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            isPopular: true,
            genre: 'Electronic'
          }
        ]
      },
      {
        id: 'pop',
        name: 'Pop',
        tracks: [
          {
            id: 'pop1',
            title: 'Summer Nights',
            artist: 'IraChat Music',
            duration: 40,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            genre: 'Pop'
          },
          {
            id: 'pop2',
            title: 'Dance Floor',
            artist: 'IraChat Music',
            duration: 50,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            genre: 'Pop'
          }
        ]
      },
      {
        id: 'chill',
        name: 'Chill',
        tracks: [
          {
            id: 'chill1',
            title: 'Peaceful Mind',
            artist: 'IraChat Music',
            duration: 60,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            genre: 'Ambient'
          },
          {
            id: 'chill2',
            title: 'Sunset Vibes',
            artist: 'IraChat Music',
            duration: 55,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            genre: 'Lo-Fi'
          }
        ]
      },
      {
        id: 'electronic',
        name: 'Electronic',
        tracks: [
          {
            id: 'electronic1',
            title: 'Neon Lights',
            artist: 'IraChat Music',
            duration: 45,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            genre: 'Electronic'
          },
          {
            id: 'electronic2',
            title: 'Digital Dreams',
            artist: 'IraChat Music',
            duration: 38,
            uri: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            genre: 'Synthwave'
          }
        ]
      }
    ];
  }

  /**
   * Search for music tracks
   */
  searchTracks(query: string): MusicTrack[] {
    const allCategories = this.getMusicCategories();
    const allTracks = allCategories.flatMap(category => category.tracks);
    
    return allTracks.filter(track => 
      track.title.toLowerCase().includes(query.toLowerCase()) ||
      track.artist.toLowerCase().includes(query.toLowerCase()) ||
      track.genre?.toLowerCase().includes(query.toLowerCase())
    );
  }

  /**
   * Preview a music track
   */
  async previewTrack(track: MusicTrack): Promise<void> {
    try {
      // Stop current sound if playing
      await this.stopPreview();

      // Load and play new sound
      const { sound } = await Audio.Sound.createAsync(
        { uri: track.uri },
        { shouldPlay: true, volume: 0.5, isLooping: true }
      );

      this.sound = sound;
      this.isPlaying = true;

      console.log(`🎵 Playing preview: ${track.title} by ${track.artist}`);
    } catch (error) {
      console.error('❌ Error playing music preview:', error);
      Alert.alert('Error', 'Failed to play music preview');
    }
  }

  /**
   * Stop music preview
   */
  async stopPreview(): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.unloadAsync();
        this.sound = null;
        this.isPlaying = false;
        console.log('🔇 Music preview stopped');
      }
    } catch (error) {
      console.error('❌ Error stopping music preview:', error);
    }
  }

  /**
   * Check if music is currently playing
   */
  getIsPlaying(): boolean {
    return this.isPlaying;
  }

  /**
   * Set volume for preview
   */
  async setVolume(volume: number): Promise<void> {
    try {
      if (this.sound) {
        await this.sound.setVolumeAsync(Math.max(0, Math.min(1, volume)));
      }
    } catch (error) {
      console.error('❌ Error setting volume:', error);
    }
  }

  /**
   * Get popular/trending tracks
   */
  getPopularTracks(): MusicTrack[] {
    const allCategories = this.getMusicCategories();
    const allTracks = allCategories.flatMap(category => category.tracks);
    return allTracks.filter(track => track.isPopular);
  }

  /**
   * Format duration for display
   */
  formatDuration(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    await this.stopPreview();
  }
}

export const musicPickerService = new MusicPickerService();
