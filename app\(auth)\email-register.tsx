// Email-Based Registration Screen for IraChat
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useState, useCallback } from 'react';
import {
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { emailAuthService } from '../../src/services/emailAuthService';
import { registerUser } from '../../src/services/authService';
import { AuthCredentials } from '../../src/types';
import { UsernameValidator } from '../../src/utils/inputValidation';
import ProfilePicturePicker from '../../src/components/ui/ProfilePicturePicker';

export default function EmailRegisterScreen() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    username: '',
    bio: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordValidation, setPasswordValidation] = useState({
    isValid: false,
    errors: [] as string[],
  });
  const [usernameValidation, setUsernameValidation] = useState({
    isValid: true,
    error: '',
  });
  const [profilePicture, setProfilePicture] = useState('');

  // Validate password in real-time
  const validatePassword = useCallback((password: string) => {
    const validation = emailAuthService.validatePassword(password);
    setPasswordValidation(validation);
  }, []);

  // Validate username in real-time
  const validateUsername = useCallback((username: string) => {
    if (!username.trim()) {
      setUsernameValidation({ isValid: true, error: '' });
      return;
    }

    const validation = UsernameValidator.validate(username.trim());
    setUsernameValidation({
      isValid: validation.isValid,
      error: validation.error || '',
    });
  }, []);

  // Handle form input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    if (field === 'password') {
      validatePassword(value);
    } else if (field === 'username') {
      validateUsername(value);
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    if (!formData.email || !formData.password || !formData.name) {
      Alert.alert('Error', 'Please fill in all required fields');
      return false;
    }

    if (!emailAuthService.validateEmail(formData.email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return false;
    }

    if (!passwordValidation.isValid) {
      Alert.alert('Error', 'Password does not meet requirements:\n' + passwordValidation.errors.join('\n'));
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }

    // Validate username if provided (it's optional for email registration)
    if (formData.username && formData.username.trim()) {
      const usernameValidation = UsernameValidator.validate(formData.username.trim());
      if (!usernameValidation.isValid) {
        Alert.alert('Error', usernameValidation.error || 'Invalid username');
        return false;
      }
    }

    return true;
  };

  // Handle registration
  const handleRegister = async () => {
    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const credentials: AuthCredentials = {
        email: formData.email,
        password: formData.password,
        method: 'email',
      };

      const userData = {
        name: formData.name,
        username: formData.username || formData.email.split('@')[0],
        displayName: formData.name,
        email: formData.email,
        bio: formData.bio,
        avatar: profilePicture,
        photoURL: profilePicture,
      };

      console.log('📧 Starting email registration process...');
      const result = await registerUser(credentials, userData);
      console.log('📧 Registration result:', result.success ? 'SUCCESS' : 'FAILED');

      if (result.success) {
        console.log('✅ Email registration successful, showing success message');
        Alert.alert(
          'Registration Successful!',
          'Please check your email inbox (including spam/junk folder) and click the verification link to complete your registration. The email may take a few minutes to arrive.',
          [
            {
              text: 'OK',
              onPress: () => {
                console.log('📧 User acknowledged registration success, navigating to welcome');
                router.push('/(auth)/welcome');
              },
            },
          ]
        );
      } else {
        console.error('❌ Email registration failed:', result.message);
        Alert.alert('Registration Failed', result.message || 'An error occurred during registration. Please try again.');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
            >
              <Ionicons name="arrow-back" size={24} color="white" />
            </TouchableOpacity>
            <Text style={styles.title}>Create Account</Text>
            <View style={styles.placeholder} />
          </View>

          {/* Form */}
          <View style={styles.form}>
            <Text style={styles.subtitle}>Sign up with email</Text>

            {/* Name Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Full Name *</Text>
              <TextInput
                style={styles.input}
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                placeholder="Enter your full name"
                autoCapitalize="words"
                autoComplete="name"
              />
            </View>

            {/* Profile Picture Section */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Profile Picture (optional)</Text>
              <View style={styles.profilePictureContainer}>
                <ProfilePicturePicker
                  onImageSelect={setProfilePicture}
                  currentImage={profilePicture}
                  size={80}
                />
                <Text style={styles.profilePictureHint}>
                  Add a profile picture to help others recognize you
                </Text>
              </View>
            </View>

            {/* Username Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Username (optional)</Text>
              <TextInput
                style={[
                  styles.input,
                  formData.username && !usernameValidation.isValid && styles.inputError
                ]}
                value={formData.username}
                onChangeText={(value) => handleInputChange('username', value)}
                placeholder="Choose a username (e.g., JohnDoe123)"
                autoCapitalize="none"
                autoComplete="username"
              />
              {formData.username && !usernameValidation.isValid && (
                <Text style={styles.errorText}>{usernameValidation.error}</Text>
              )}
              {formData.username && usernameValidation.isValid && (
                <Text style={styles.successText}>✓ Username is valid</Text>
              )}
            </View>

            {/* Bio Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Bio (optional)</Text>
              <TextInput
                style={[styles.input, styles.bioInput]}
                value={formData.bio}
                onChangeText={(value) => handleInputChange('bio', value)}
                placeholder="Tell us about yourself"
                multiline={true}
                numberOfLines={3}
                maxLength={150}
                textAlignVertical="top"
              />
              <Text style={styles.characterCount}>
                {formData.bio.length}/150 characters
              </Text>
            </View>

            {/* Email Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Email Address *</Text>
              <TextInput
                style={styles.input}
                value={formData.email}
                onChangeText={(value) => handleInputChange('email', value)}
                placeholder="Enter your email"
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            </View>

            {/* Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Password *</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={formData.password}
                  onChangeText={(value) => handleInputChange('password', value)}
                  placeholder="Create a strong password"
                  secureTextEntry={!showPassword}
                  autoComplete="new-password"
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowPassword(!showPassword)}
                >
                  <Ionicons
                    name={showPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#666"
                  />
                </TouchableOpacity>
              </View>
              
              {/* Password Requirements */}
              {formData.password && (
                <View style={styles.passwordRequirements}>
                  {passwordValidation.errors.map((error, index) => (
                    <Text key={index} style={styles.errorText}>
                      • {error}
                    </Text>
                  ))}
                  {passwordValidation.isValid && (
                    <Text style={styles.successText}>✓ Password meets all requirements</Text>
                  )}
                </View>
              )}
            </View>

            {/* Confirm Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.label}>Confirm Password *</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={styles.passwordInput}
                  value={formData.confirmPassword}
                  onChangeText={(value) => handleInputChange('confirmPassword', value)}
                  placeholder="Confirm your password"
                  secureTextEntry={!showConfirmPassword}
                  autoComplete="new-password"
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  <Ionicons
                    name={showConfirmPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#666"
                  />
                </TouchableOpacity>
              </View>
              
              {/* Password Match Indicator */}
              {formData.confirmPassword && (
                <Text
                  style={[
                    styles.matchText,
                    formData.password === formData.confirmPassword
                      ? styles.successText
                      : styles.errorText,
                  ]}
                >
                  {formData.password === formData.confirmPassword
                    ? '✓ Passwords match'
                    : '✗ Passwords do not match'}
                </Text>
              )}
            </View>

            {/* Register Button */}
            <TouchableOpacity
              style={[styles.registerButton, isLoading && styles.disabledButton]}
              onPress={handleRegister}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color="white" />
              ) : (
                <Text style={styles.registerButtonText}>Create Account</Text>
              )}
            </TouchableOpacity>

            {/* Alternative Options */}
            <View style={styles.alternativeContainer}>
              <Text style={styles.alternativeText}>Or register with</Text>
              <TouchableOpacity
                style={styles.phoneButton}
                onPress={() => router.push('/(auth)/phone-register')}
              >
                <Ionicons name="call" size={20} color="#007AFF" />
                <Text style={styles.phoneButtonText}>Phone Number</Text>
              </TouchableOpacity>
            </View>

            {/* Sign In Link */}
            <View style={styles.signInContainer}>
              <Text style={styles.signInText}>Already have an account? </Text>
              <TouchableOpacity onPress={() => router.push('/(auth)/email-sign-in')}>
                <Text style={styles.signInLink}>Sign In</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 5,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
  },
  placeholder: {
    width: 34,
  },
  form: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  subtitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 30,
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: 'white',
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: 'white',
    color: '#333',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'white',
    borderRadius: 10,
    backgroundColor: 'white',
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  eyeButton: {
    paddingHorizontal: 15,
  },
  passwordRequirements: {
    marginTop: 8,
  },
  errorText: {
    fontSize: 12,
    color: '#ff4444',
    marginBottom: 2,
  },
  successText: {
    fontSize: 12,
    color: '#00aa00',
  },
  matchText: {
    fontSize: 12,
    marginTop: 5,
  },
  registerButton: {
    backgroundColor: '#007AFF',
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  disabledButton: {
    opacity: 0.6,
  },
  registerButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  alternativeContainer: {
    alignItems: 'center',
    marginTop: 30,
  },
  alternativeText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
  },
  phoneButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 10,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  phoneButtonText: {
    color: '#007AFF',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  signInContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  signInText: {
    fontSize: 14,
    color: 'white',
  },
  signInLink: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  bioInput: {
    minHeight: 80,
    textAlignVertical: 'top',
    paddingTop: 12,
  },
  characterCount: {
    fontSize: 12,
    color: 'white',
    textAlign: 'right',
    marginTop: 4,
  },
  inputError: {
    borderColor: '#FF3B30',
    borderWidth: 1,
  },
  profilePictureContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  profilePictureHint: {
    fontSize: 12,
    color: 'white',
    textAlign: 'center',
    marginTop: 8,
    maxWidth: 200,
  },
});
