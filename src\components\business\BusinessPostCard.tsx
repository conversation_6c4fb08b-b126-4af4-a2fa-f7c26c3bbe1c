import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  Share,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Video } from 'expo-av';
import { BusinessPost } from '../../types/Business';
import { BusinessContactActions } from './BusinessContactActions';
import { businessService } from '../../services/businessService';

const COLORS = {
  primary: '#1DA1F2',
  background: '#FFFFFF',
  text: '#14171A',
  textSecondary: '#657786',
  border: '#E1E8ED',
  surface: '#F7F9FA',
  success: '#17BF63',
  warning: '#FFAD1F',
  error: '#E0245E',
};

interface BusinessPostCardProps {
  post: BusinessPost;
  currentUserId?: string;
  onPress?: (post: BusinessPost) => void;
  onMediaPress?: (post: BusinessPost, mediaIndex: number) => void;
  onChatPress?: (businessId: string) => void;
}

export const BusinessPostCard: React.FC<BusinessPostCardProps> = React.memo(({
  post,
  currentUserId,
  onPress,
  onMediaPress,
  onChatPress,
}) => {
  const [isLiked, setIsLiked] = useState(post.likes.includes(currentUserId || ''));
  const [likeCount, setLikeCount] = useState(post.likes.length);
  const [showProductMenu, setShowProductMenu] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false); // TODO: Get from user preferences

  // Helper functions for sync status
  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Ionicons name="time-outline" size={12} color="#F59E0B" />;
      case 'syncing': return <Ionicons name="sync-outline" size={12} color="#3B82F6" />;
      case 'failed': return <Ionicons name="warning-outline" size={12} color="#EF4444" />;
      case 'synced': return <Ionicons name="checkmark-circle-outline" size={12} color="#10B981" />;
      default: return null;
    }
  };

  const getSyncStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'syncing': return 'Syncing';
      case 'failed': return 'Failed';
      case 'synced': return 'Synced';
      default: return '';
    }
  };

  const getSyncStatusStyle = (status: string) => {
    switch (status) {
      case 'pending': return { backgroundColor: '#FEF3C7' };
      case 'syncing': return { backgroundColor: '#DBEAFE' };
      case 'failed': return { backgroundColor: '#FEE2E2' };
      case 'synced': return { backgroundColor: '#D1FAE5' };
      default: return {};
    }
  };

  const getSyncStatusTextStyle = (status: string) => {
    switch (status) {
      case 'pending': return { color: '#F59E0B' };
      case 'syncing': return { color: '#3B82F6' };
      case 'failed': return { color: '#EF4444' };
      case 'synced': return { color: '#10B981' };
      default: return {};
    }
  };



  const handleLike = async (event: any) => {
    event.stopPropagation();
    if (!currentUserId) {
      Alert.alert('Login Required', 'Please login to like posts');
      return;
    }

    try {
      const result = await businessService.toggleLike(post.id, currentUserId);
      if (result.success) {
        setIsLiked(!isLiked);
        setLikeCount(prev => isLiked ? prev - 1 : prev + 1);
      }
    } catch (error) {
      console.error('❌ Error toggling like:', error);
    }
  };

  const handleShare = async (event: any) => {
    event.stopPropagation();
    try {
      let priceText = 'Contact for price';
      if (post.price) {
        priceText = `${post.currency} ${post.price.toLocaleString()}`;
        if (post.oldPrice && post.oldPrice !== post.price) {
          priceText += ` (was ${post.currency} ${post.oldPrice.toLocaleString()})`;
        }
      }

      const result = await Share.share({
        message: `Check out this product: ${post.title}\n\n${post.description}\n\nPrice: ${priceText}\n\nShared via IraChat`,
        title: post.title,
      });

      if (result.action === Share.sharedAction) {
        // Increment share count
        await businessService.incrementShares(post.id);
      }
    } catch (error) {
      console.error('❌ Error sharing post:', error);
      Alert.alert('Error', 'Failed to share post');
    }
  };

  const handleChat = (event: any) => {
    event.stopPropagation();
    if (onChatPress) {
      onChatPress(post.businessId);
    } else {
      // Fallback behavior
      Alert.alert('Chat', `Start a conversation with ${post.businessName}`, [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Start Chat', onPress: () => {
          console.log('Navigate to chat with business:', post.businessId);
        }},
      ]);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return COLORS.success;
      case 'out_of_stock': return COLORS.error;
      case 'new': return COLORS.primary;
      case 'refurbished': return COLORS.warning;
      case 'second_hand': return '#8B5CF6';
      case 'limited': return '#F59E0B';
      default: return COLORS.textSecondary;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available': return 'checkmark-circle';
      case 'out_of_stock': return 'close-circle';
      case 'new': return 'sparkles';
      case 'refurbished': return 'construct';
      case 'second_hand': return 'repeat';
      case 'limited': return 'star';
      default: return 'help-circle';
    }
  };

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={() => onPress?.(post)}
      activeOpacity={0.95}
    >
      <View style={styles.content}>
        {/* Business Header */}
        <View style={styles.header}>
          <View style={styles.businessInfo}>
            {/* Business Logo */}
            <View style={styles.businessLogo}>
              {post.businessLogo ? (
                <Image
                  source={{ uri: post.businessLogo }}
                  style={styles.businessLogoImage}
                  onError={() => {
                    console.log('❌ Business logo failed to load for:', post.businessName);
                  }}
                />
              ) : (
                <Ionicons name="business" size={20} color={COLORS.primary} />
              )}
            </View>

            {/* Business Name and Type in Same Line */}
            <View style={styles.businessNameRow}>
              <Text style={styles.businessName}>{post.businessName}</Text>
              {post.isVerified && (
                <Ionicons name="checkmark-circle" size={16} color={COLORS.primary} />
              )}
              <Text style={styles.businessType}>• {post.businessType}</Text>
            </View>
          </View>
          
          {/* Sync Status Indicator */}
          {('_syncStatus' in post) && (
            <View style={[styles.syncStatusBadge, getSyncStatusStyle((post as any)._syncStatus)]}>
              {getSyncStatusIcon((post as any)._syncStatus)}
              <Text style={[styles.syncStatusText, getSyncStatusTextStyle((post as any)._syncStatus)]}>
                {getSyncStatusLabel((post as any)._syncStatus)}
                {(post as any)._syncStatus === 'failed' && ' - Pull to retry'}
              </Text>
            </View>
          )}

          {/* Product Status Badge and Menu */}
          <View style={styles.headerRight}>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(post.status) + '20' }]}>
              <Ionicons
                name={getStatusIcon(post.status) as any}
                size={12}
                color={getStatusColor(post.status)}
              />
              <Text style={[styles.statusText, { color: getStatusColor(post.status) }]}>
                {post.status.replace('_', ' ').toUpperCase()}
              </Text>
            </View>

            {/* Three Dots Menu */}
            <TouchableOpacity
              style={styles.productMenuButton}
              onPress={(event) => {
                event.stopPropagation();
                setShowProductMenu(true);
              }}
            >
              <Ionicons name="ellipsis-vertical" size={20} color={COLORS.textSecondary} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Product Title and Description */}
        <Text style={styles.title}>{post.title}</Text>
        <Text style={styles.description} numberOfLines={3}>
          {post.description}
        </Text>

        {/* Media */}
        {post.media && post.media.length > 0 && (
          <View style={styles.mediaContainer}>
            {post.media.slice(0, 3).map((media, index) => (
              <TouchableOpacity
                key={media.id}
                style={[
                  styles.mediaItem,
                  post.media.length === 1 && styles.singleMedia,
                  post.media.length === 2 && styles.doubleMedia,
                ]}
                onPress={() => onMediaPress?.(post, index)}
              >
                {media.type === 'video' ? (
                  <View style={styles.videoContainer}>
                    <Video
                      source={{ uri: media.url }}
                      style={styles.media}
                      resizeMode={'cover' as any}
                      shouldPlay={false}
                      isLooping={false}
                    />
                    <View style={styles.videoOverlay}>
                      <Ionicons name="play-circle" size={24} color="white" />
                    </View>
                  </View>
                ) : (
                  <Image source={{ uri: media.url }} style={styles.media} resizeMode="cover" />
                )}
                
                {/* Show count for multiple media */}
                {index === 2 && post.media.length > 3 && (
                  <View style={styles.moreMediaOverlay}>
                    <Text style={styles.moreMediaText}>+{post.media.length - 3}</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Price and Location */}
        <View style={styles.priceLocationRow}>
          {post.price && (
            <View style={styles.priceContainer}>
              {post.oldPrice && post.oldPrice !== post.price && (
                <Text style={styles.oldPrice}>
                  {post.currency} {post.oldPrice.toLocaleString()}
                </Text>
              )}
              <Text style={styles.price}>
                {post.currency} {post.price.toLocaleString()}
              </Text>
              {post.isNegotiable && (
                <Text style={styles.negotiable}>Negotiable</Text>
              )}
            </View>
          )}
          
          <View style={styles.locationContainer}>
            <Ionicons name="location-outline" size={14} color={COLORS.textSecondary} />
            <Text style={styles.location} numberOfLines={2}>
              {post.location.address ?
                `${post.location.address}, ${post.location.city}, ${post.location.district}` :
                `${post.location.city}, ${post.location.district}`
              }
            </Text>
          </View>
        </View>

        {/* Additional Locations */}
        {post.additionalLocations && post.additionalLocations.length > 0 && (
          <View style={styles.additionalLocations}>
            <Text style={styles.additionalLocationsLabel}>Also available in:</Text>
            <View style={styles.locationTags}>
              {post.additionalLocations.slice(0, 3).map((location, index) => {
                const colors = [COLORS.primary, COLORS.success, COLORS.warning, COLORS.error];
                const color = colors[index % colors.length];
                return (
                  <View key={index} style={[styles.locationTag, { backgroundColor: color + '20', borderColor: color }]}>
                    <Text style={[styles.locationTagText, { color }]}>
                      {location}
                    </Text>
                  </View>
                );
              })}
              {post.additionalLocations.length > 3 && (
                <Text style={styles.moreLocations}>
                  +{post.additionalLocations.length - 3} more
                </Text>
              )}
            </View>
          </View>
        )}

        {/* Stats and Actions */}
        <View style={styles.footer}>
          <View style={styles.stats}>
            <View style={styles.statItem}>
              <Ionicons name="eye-outline" size={14} color="#9CA3AF" />
              <Text style={styles.statText}>{post.views}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="heart-outline" size={14} color="#9CA3AF" />
              <Text style={styles.statText}>{likeCount}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="share-outline" size={14} color="#9CA3AF" />
              <Text style={styles.statText}>{post.shares}</Text>
            </View>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity style={styles.actionButton} onPress={handleLike}>
              <Ionicons
                name={isLiked ? "heart" : "heart-outline"}
                size={20}
                color={isLiked ? "#FF3040" : "#9CA3AF"}
              />
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleShare}>
              <Ionicons name="share-outline" size={20} color="#9CA3AF" />
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={handleChat}>
              <Ionicons name="chatbubble-outline" size={20} color={COLORS.primary} />
            </TouchableOpacity>

            {/* Contact Actions */}
            <BusinessContactActions
              businessName={post.businessName}
              phone={post.contact?.phone}
              email={post.contact?.email}
              size="small"
              style={{ marginLeft: 8 }}
            />
          </View>
        </View>

        {/* Timestamp */}
        <Text style={styles.timestamp}>
          {post.createdAt.toLocaleDateString()} • {post.createdAt.toLocaleTimeString()}
        </Text>
      </View>

      {/* Product Menu Modal */}
      <Modal
        visible={showProductMenu}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowProductMenu(false)}
      >
        <TouchableOpacity
          style={styles.menuOverlay}
          activeOpacity={1}
          onPress={() => setShowProductMenu(false)}
        >
          <View style={styles.menuContainer}>
            {/* Hide Product */}
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                setShowProductMenu(false);
                Alert.alert(
                  'Hide Product',
                  'This product will be hidden from your feed. You can unhide it from settings.',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Hide', style: 'destructive', onPress: () => {
                      // TODO: Implement hide product functionality
                      console.log('Hide product:', post.id);
                    }}
                  ]
                );
              }}
            >
              <Ionicons name="eye-off-outline" size={20} color={COLORS.textSecondary} />
              <Text style={styles.menuItemText}>Hide Product</Text>
            </TouchableOpacity>

            {/* Follow Business */}
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                setShowProductMenu(false);
                setIsFollowing(!isFollowing);
                Alert.alert(
                  isFollowing ? 'Unfollowed' : 'Following',
                  `You are now ${isFollowing ? 'not following' : 'following'} ${post.businessName}`
                );
                // TODO: Implement follow/unfollow functionality
                console.log(isFollowing ? 'Unfollow' : 'Follow', 'business:', post.businessId);
              }}
            >
              <Ionicons
                name={isFollowing ? "person-remove-outline" : "person-add-outline"}
                size={20}
                color={COLORS.primary}
              />
              <Text style={[styles.menuItemText, { color: COLORS.primary }]}>
                {isFollowing ? 'Unfollow Business' : 'Follow Business'}
              </Text>
            </TouchableOpacity>

            <View style={styles.menuSeparator} />

            {/* Report Product */}
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                setShowProductMenu(false);
                Alert.alert(
                  'Report Product',
                  'Why are you reporting this product?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Inappropriate Content', onPress: () => console.log('Report: Inappropriate') },
                    { text: 'Spam', onPress: () => console.log('Report: Spam') },
                    { text: 'Misleading', onPress: () => console.log('Report: Misleading') },
                  ]
                );
              }}
            >
              <Ionicons name="flag-outline" size={20} color={COLORS.error} />
              <Text style={[styles.menuItemText, { color: COLORS.error }]}>Report Product</Text>
            </TouchableOpacity>

            {/* Block Business */}
            <TouchableOpacity
              style={styles.menuItem}
              onPress={() => {
                setShowProductMenu(false);
                Alert.alert(
                  'Block Business',
                  `Block ${post.businessName}? You won't see their products anymore.`,
                  [
                    { text: 'Cancel', style: 'cancel' },
                    { text: 'Block', style: 'destructive', onPress: () => {
                      // TODO: Implement block business functionality
                      console.log('Block business:', post.businessId);
                    }}
                  ]
                );
              }}
            >
              <Ionicons name="ban-outline" size={20} color={COLORS.error} />
              <Text style={[styles.menuItemText, { color: COLORS.error }]}>Block Business</Text>
            </TouchableOpacity>
          </View>
        </TouchableOpacity>
      </Modal>
    </TouchableOpacity>
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.background,
    marginBottom: 12,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  businessInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  businessLogo: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  businessLogoImage: {
    width: '100%',
    height: '100%',
    borderRadius: 16,
  },
  businessNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    flex: 1,
  },
  businessName: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
  },
  businessType: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontWeight: '500',
    textTransform: 'uppercase',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  title: {
    fontSize: 18,
    fontWeight: '700',
    color: COLORS.primary,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: COLORS.textSecondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  mediaContainer: {
    flexDirection: 'row',
    gap: 4,
    marginBottom: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mediaItem: {
    flex: 1,
    height: 120,
    position: 'relative',
  },
  singleMedia: {
    height: 200,
  },
  doubleMedia: {
    height: 150,
  },
  media: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  videoContainer: {
    position: 'relative',
    width: '100%',
    height: '100%',
  },
  videoOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -12 }, { translateY: -12 }],
  },
  moreMediaOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  moreMediaText: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  priceLocationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  price: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  oldPrice: {
    fontSize: 14,
    color: '#9CA3AF',
    textDecorationLine: 'line-through',
    marginRight: 8,
  },
  negotiable: {
    fontSize: 12,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 4,
    flex: 1,
    maxWidth: '60%',
  },
  location: {
    fontSize: 12,
    color: COLORS.textSecondary,
    flex: 1,
    lineHeight: 16,
  },
  additionalLocations: {
    marginBottom: 12,
  },
  additionalLocationsLabel: {
    fontSize: 12,
    color: COLORS.textSecondary,
    marginBottom: 4,
  },
  locationTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
    alignItems: 'center',
  },
  locationTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    borderWidth: 1,
  },
  locationTagText: {
    fontSize: 10,
    fontWeight: '500',
  },
  moreLocations: {
    fontSize: 10,
    color: COLORS.textSecondary,
    fontStyle: 'italic',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  stats: {
    flexDirection: 'row',
    gap: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  actions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    padding: 8,
  },
  timestamp: {
    fontSize: 11,
    color: COLORS.textSecondary,
    textAlign: 'right',
  },
  // New styles for header layout and menu
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  productMenuButton: {
    padding: 4,
    borderRadius: 12,
  },
  // Product menu styles
  menuOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    backgroundColor: COLORS.background,
    borderRadius: 12,
    paddingVertical: 8,
    minWidth: 200,
    maxWidth: 280,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 5,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  menuItemText: {
    fontSize: 16,
    color: COLORS.text,
    flex: 1,
  },
  menuSeparator: {
    height: 1,
    backgroundColor: COLORS.border,
    marginVertical: 4,
  },
  // Sync status badge styles
  syncStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    gap: 4,
  },
  syncStatusText: {
    fontSize: 10,
    fontWeight: '500',
  },
});
