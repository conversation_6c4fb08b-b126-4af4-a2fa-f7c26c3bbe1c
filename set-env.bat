@echo off
echo Setting environment variables for current session...
echo =====================================================

REM FORCE ALL PATHS TO D: DRIVE - NO C: DRIVE USAGE!
set ANDROID_SDK_ROOT=D:\Android\Sdk
set ANDROID_HOME=D:\Android\Sdk

REM Create Android SDK directory on D: drive if it doesn't exist
if not exist "D:\Android\Sdk" (
    echo Creating Android SDK directory on D: drive...
    mkdir "D:\Android\Sdk"
    mkdir "D:\Android\Sdk\platform-tools"
    mkdir "D:\Android\Sdk\tools"
    mkdir "D:\Android\Sdk\tools\bin"
)

REM Force Java to D: drive (use the Java we have in project)
if exist "D:\java17\jdk-17.0.12+7" (
    set JAVA_HOME=D:\java17\jdk-17.0.12+7
    echo Using Java from D: drive: %JAVA_HOME%
) else if exist "F:\IraChat\java17\jdk-17.0.12+7" (
    set JAVA_HOME=F:\IraChat\java17\jdk-17.0.12+7
    echo Using Java from project: %JAVA_HOME%
) else (
    echo WARNING: Java not found on D: drive! Please move Java to D: drive
    echo Current fallback: Using system Java
    set JAVA_HOME=C:\Program Files\Java\jdk-23
)

REM ULTRA-AGGRESSIVE: Force ALL cache and temp directories to D: drive
set GRADLE_USER_HOME=D:\gradle-cache
set GRADLE_CACHE_DIR=D:\gradle-cache
set GRADLE_BUILD_CACHE_DIR=D:\gradle-build-cache
set TMPDIR=D:\temp
set TEMP=D:\temp
set TMP=D:\temp
set LOCALAPPDATA=D:\AppData\Local
set APPDATA=D:\AppData\Roaming

REM Node.js and NPM cache to D: drive
set NPM_CONFIG_CACHE=D:\npm-cache
set YARN_CACHE_FOLDER=D:\yarn-cache
set NODE_OPTIONS=--max-old-space-size=4096

REM React Native and Metro cache to D: drive
set METRO_CACHE_DIR=D:\metro-cache
set RN_CACHE_DIR=D:\rn-cache
set EXPO_CACHE_DIR=D:\expo-cache

REM Android build cache - let it use project-relative paths to avoid cross-drive issues
REM set ANDROID_BUILD_CACHE_DIR=D:\android-build-cache

REM Create ALL cache directories if they don't exist
if not exist "D:\gradle-cache" mkdir "D:\gradle-cache"
if not exist "D:\gradle-build-cache" mkdir "D:\gradle-build-cache"
if not exist "D:\temp" mkdir "D:\temp"
if not exist "D:\AppData" mkdir "D:\AppData"
if not exist "D:\AppData\Local" mkdir "D:\AppData\Local"
if not exist "D:\AppData\Roaming" mkdir "D:\AppData\Roaming"
if not exist "D:\npm-cache" mkdir "D:\npm-cache"
if not exist "D:\yarn-cache" mkdir "D:\yarn-cache"
if not exist "D:\metro-cache" mkdir "D:\metro-cache"
if not exist "D:\rn-cache" mkdir "D:\rn-cache"
if not exist "D:\expo-cache" mkdir "D:\expo-cache"
REM if not exist "D:\android-build-cache" mkdir "D:\android-build-cache"

REM Set PATH with D: drive Android tools FIRST (prioritize over C: drive)
set PATH=%ANDROID_SDK_ROOT%\platform-tools;%ANDROID_SDK_ROOT%\tools;%ANDROID_SDK_ROOT%\tools\bin;%JAVA_HOME%\bin;%PATH%

echo =====================================================
echo Environment Variables Set:
echo ANDROID_HOME=%ANDROID_HOME%
echo ANDROID_SDK_ROOT=%ANDROID_SDK_ROOT%
echo JAVA_HOME=%JAVA_HOME%
echo GRADLE_USER_HOME=%GRADLE_USER_HOME%
echo GRADLE_CACHE_DIR=%GRADLE_CACHE_DIR%
echo GRADLE_BUILD_CACHE_DIR=%GRADLE_BUILD_CACHE_DIR%
echo TMPDIR=%TMPDIR%
echo TEMP=%TEMP%
echo TMP=%TMP%
echo LOCALAPPDATA=%LOCALAPPDATA%
echo APPDATA=%APPDATA%
echo NPM_CONFIG_CACHE=%NPM_CONFIG_CACHE%
echo YARN_CACHE_FOLDER=%YARN_CACHE_FOLDER%
echo METRO_CACHE_DIR=%METRO_CACHE_DIR%
echo RN_CACHE_DIR=%RN_CACHE_DIR%
echo EXPO_CACHE_DIR=%EXPO_CACHE_DIR%
REM echo ANDROID_BUILD_CACHE_DIR=%ANDROID_BUILD_CACHE_DIR%
echo =====================================================
echo.
echo Testing ADB...
adb version
echo.
echo Environment variables set for current session!
echo All cache and temp files will now go to D: drive
echo You can now run: npx expo run:android --device
