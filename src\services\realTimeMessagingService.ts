// 💬 REAL-TIME MESSAGING SERVICE - Actually works with Firebase!
// This service handles real messaging with live synchronization

import {
  addDoc,
  collection,
  doc,
  getDoc,
  getDocs,
  limit,
  onSnapshot,
  orderBy,
  query,
  serverTimestamp,
  setDoc,
  updateDoc,
  where,
  Timestamp,
  FieldValue,
} from 'firebase/firestore';
import { db } from './firebaseSimple';
import { autoDownloadService } from './autoDownloadService';
import { chatsListService } from './chatsListService';

export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read';

export interface MessageStatusInfo {
  status: MessageStatus;
  timestamp: Date;
  readBy?: { [userId: string]: Date }; // For group chats - who read when
  deliveredTo?: { [userId: string]: Date }; // For group chats - who received when
}

export interface RealMessage {
  id: string;
  chatId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  type: 'text' | 'image' | 'video' | 'audio' | 'file' | 'deleted';
  mediaUrl?: string;
  timestamp: Timestamp | Date | any;
  statusInfo: MessageStatusInfo;
  replyTo?: string; // ID of message being replied to
  reactions?: { [userId: string]: string }; // emoji reactions
  // Deletion fields
  deletedFor?: string[]; // Array of user IDs who deleted this message for themselves
  deletedForEveryone?: boolean; // True if sender deleted for everyone
  deletedAt?: Timestamp | Date | any; // When message was deleted
  // Forwarding fields
  isForwarded?: boolean; // True if this message was forwarded
  originalSender?: string; // Original sender's name (for forwarded messages)
  originalMessageId?: string; // ID of the original message
  // Media fields
  caption?: string; // Caption for media messages
  mediaWidth?: number; // Width of media (for images/videos)
  mediaHeight?: number; // Height of media (for images/videos)
  mediaDuration?: number; // Duration for audio/video (in seconds)
  mediaSize?: number; // File size in bytes
  thumbnailUrl?: string; // Thumbnail URL for videos
  fileName?: string; // Original file name
  mimeType?: string; // MIME type of the file
  // Voice message fields
  waveform?: number[]; // Audio waveform data for voice messages
  // Location fields
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  // Contact fields (for shared contacts)
  contact?: {
    name: string;
    phoneNumber: string;
    avatar?: string;
  };
  // Legacy fields for backward compatibility
  isRead: boolean;
  isDelivered: boolean;
  // Additional fields for compatibility
  isEdited?: boolean; // True if message was edited
  readBy?: string[]; // Array of user IDs who read this message
}

export interface RealChat {
  id: string;
  participants: string[]; // User IDs
  participantNames: { [userId: string]: string };
  participantAvatars: { [userId: string]: string };
  lastMessage?: RealMessage;
  lastMessageTime: Timestamp | Date | any;
  unreadCount: { [userId: string]: number };
  isGroup: boolean;
  groupName?: string;
  groupAvatar?: string;
  createdAt: Timestamp | Date | any;
  updatedAt: Timestamp | Date | any;
  // Archive fields
  archivedBy?: string[]; // Array of user IDs who archived this chat
  archivedAt?: { [userId: string]: Timestamp | Date | any }; // When each user archived the chat
}

class RealTimeMessagingService {
  private messageListeners: { [chatId: string]: () => void } = {};
  private currentUserId: string | null = null;

  /**
   * Initialize the service with current user ID
   */
  async initialize(userId: string): Promise<void> {
    this.currentUserId = userId;
    // Initialize auto download service
    await autoDownloadService.initialize(userId);
  }
  private chatListeners: { [userId: string]: () => void } = {};

  /**
   * Send a real message to Firebase
   */
  async sendMessage(
    chatId: string,
    senderId: string,
    senderName: string,
    content: string,
    type: 'text' | 'image' | 'video' | 'audio' | 'file' = 'text',
    mediaUrl?: string,
    senderAvatar?: string,
    replyTo?: {
      messageId: string;
      content: string;
      senderName: string;
      type: string;
    }
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      const messageData: Omit<RealMessage, 'id'> = {
        chatId,
        senderId,
        senderName,
        ...(senderAvatar && { senderAvatar }), // Only include senderAvatar if it exists
        content,
        type,
        ...(mediaUrl && { mediaUrl }), // Only include mediaUrl if it exists
        ...(replyTo && { replyTo: replyTo.messageId }), // Include reply message ID if replying
        timestamp: serverTimestamp(),
        statusInfo: {
          status: 'sending' as MessageStatus, // Start as sending
          timestamp: new Date(),
          readBy: {},
          deliveredTo: {},
        },
        isRead: false,
        isDelivered: false, // Not delivered until confirmed
      };

      // Add message to Firestore messages collection
      console.log('💾 Saving message to Firestore messages collection:', messageData);
      console.log('🔥 Firebase project:', db.app.options.projectId);
      console.log('📍 Collection path: messages');
      const messagesRef = collection(db, 'messages');
      const docRef = await addDoc(messagesRef, messageData);
      console.log('✅ Message saved to Firestore with ID:', docRef.id);
      console.log('🌐 Full document path:', `projects/${db.app.options.projectId}/databases/(default)/documents/messages/${docRef.id}`);
      console.log('🔍 Query chatId for subscription:', chatId);

      // Update status to 'sent' after successful upload
      await updateDoc(docRef, {
        'statusInfo.status': 'sent',
        isDelivered: true,
      });

      // Update chat's last message
      await this.updateChatLastMessage(chatId, {
        ...messageData,
        id: docRef.id,
        timestamp: new Date(),
      } as RealMessage);

      // Update chats list with new message
      await this.updateChatsListWithMessage(senderId, chatId, {
        content,
        senderId,
        senderName,
        timestamp: new Date(),
        type,
        isRead: false,
      });

      return { success: true, messageId: docRef.id };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  // Mark message as delivered
  async markMessageAsDelivered(messageId: string, userId: string, chatId: string): Promise<void> {
    try {
      const messageRef = doc(db, 'individual_chats', chatId, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (messageDoc.exists()) {
        const messageData = messageDoc.data() as RealMessage;
        const updatedStatusInfo = {
          ...messageData.statusInfo,
          status: 'delivered' as MessageStatus,
          deliveredTo: {
            ...messageData.statusInfo.deliveredTo,
            [userId]: new Date(),
          },
        };

        await updateDoc(messageRef, {
          statusInfo: updatedStatusInfo,
          isDelivered: true,
        });
      }
    } catch (error) {
      // Error handling - could implement retry logic or offline storage
    }
  }

  // Mark message as read
  async markMessageAsRead(messageId: string, userId: string, chatId: string): Promise<void> {
    try {
      const messageRef = doc(db, 'individual_chats', chatId, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (messageDoc.exists()) {
        const messageData = messageDoc.data() as RealMessage;
        const updatedStatusInfo = {
          ...messageData.statusInfo,
          status: 'read' as MessageStatus,
          readBy: {
            ...messageData.statusInfo.readBy,
            [userId]: new Date(),
          },
        };

        await updateDoc(messageRef, {
          statusInfo: updatedStatusInfo,
          isRead: true,
        });
      }
    } catch (error) {
      // Error handling - could implement retry logic or offline storage
    }
  }

  // Mark all messages in a chat as read
  async markChatAsRead(chatId: string, userId: string): Promise<void> {
    try {
      const messagesRef = collection(db, 'individual_chats', chatId, 'messages');
      const q = query(
        messagesRef,
        where('senderId', '!=', userId), // Don't mark own messages as read
        where('isRead', '==', false)
      );

      const snapshot = await getDocs(q);
      const batch = [];

      for (const messageDoc of snapshot.docs) {
        batch.push(this.markMessageAsRead(messageDoc.id, userId, chatId));
      }

      await Promise.all(batch);
    } catch (error) {
      // Error handling - could implement retry logic or offline storage
    }
  }

  // Typing indicator functions
  async setTypingStatus(chatId: string, userId: string, userName: string, isTyping: boolean): Promise<void> {
    try {
      const typingRef = doc(db, 'typing', `${chatId}_${userId}`);

      if (isTyping) {
        await setDoc(typingRef, {
          chatId,
          userId,
          userName,
          isTyping: true,
          timestamp: serverTimestamp(),
        });
      } else {
        // Remove typing indicator when user stops typing
        await updateDoc(typingRef, {
          isTyping: false,
          timestamp: serverTimestamp(),
        });
      }
    } catch (error) {
      // Error handling - could implement retry logic or offline storage
    }
  }

  // Subscribe to typing indicators for a chat
  subscribeToTypingIndicators(
    chatId: string,
    currentUserId: string,
    callback: (_typingUsers: { userId: string; userName: string }[]) => void
  ): () => void {
    const typingRef = collection(db, 'typing');
    const q = query(
      typingRef,
      where('chatId', '==', chatId),
      where('isTyping', '==', true),
      where('userId', '!=', currentUserId) // Don't show own typing
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const typingUsers = snapshot.docs
        .map(doc => {
          const data = doc.data();
          // Only include recent typing indicators (within last 10 seconds)
          const timestamp = data.timestamp?.toDate?.() || new Date(data.timestamp);
          const now = new Date();
          const timeDiff = now.getTime() - timestamp.getTime();

          if (timeDiff < 10000) { // 10 seconds
            return {
              userId: data.userId,
              userName: data.userName,
            };
          }
          return null;
        })
        .filter(Boolean) as { userId: string; userName: string }[];

      callback(typingUsers);
    });

    return unsubscribe;
  }

  // Message reaction functions (removed duplicate - using the more complete version below)

  async removeReaction(messageId: string, userId: string, chatId: string): Promise<void> {
    try {
      const messageRef = doc(db, 'individual_chats', chatId, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (messageDoc.exists()) {
        const messageData = messageDoc.data() as RealMessage;
        const updatedReactions = { ...messageData.reactions };
        delete updatedReactions[userId];

        await updateDoc(messageRef, {
          reactions: updatedReactions,
        });
      }
    } catch (error) {
      // Error handling - could implement retry logic or offline storage
    }
  }

  // Message deletion functions
  async deleteMessageForSelf(messageId: string, userId: string, chatId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (!messageDoc.exists()) {
        return { success: false, error: 'Message not found' };
      }

      const messageData = messageDoc.data() as RealMessage;

      // Add user to deletedFor array
      const deletedFor = messageData.deletedFor || [];
      if (!deletedFor.includes(userId)) {
        deletedFor.push(userId);
      }

      await updateDoc(messageRef, {
        deletedFor: deletedFor,
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete message' };
    }
  }

  async deleteMessageForEveryone(messageId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const messageRef = doc(db, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (!messageDoc.exists()) {
        return { success: false, error: 'Message not found' };
      }

      const messageData = messageDoc.data() as RealMessage;

      // Check if user is the sender
      if (messageData.senderId !== userId) {
        return { success: false, error: 'Only sender can delete for everyone' };
      }

      // Allow deletion of any message for now (remove time limit)
      console.log('🗑️ Deleting message for everyone:', messageId, 'by user:', userId);

      // Delete media files from storage if they exist
      if (messageData.mediaUrl) {
        try {
          const { ref, deleteObject } = await import('firebase/storage');
          const { storage } = await import('./firebaseSimple');
          const mediaRef = ref(storage, messageData.mediaUrl);
          await deleteObject(mediaRef);
        } catch (storageError) {
          // Storage deletion failed - continue with message deletion
        }
      }

      // Delete thumbnail if it exists
      if (messageData.thumbnailUrl) {
        try {
          const { ref, deleteObject } = await import('firebase/storage');
          const { storage } = await import('./firebaseSimple');
          const thumbnailRef = ref(storage, messageData.thumbnailUrl);
          await deleteObject(thumbnailRef);
        } catch (storageError) {
          // Storage deletion failed - continue with message deletion
        }
      }

      // Mark message as deleted for everyone
      await updateDoc(messageRef, {
        deletedForEveryone: true,
        deletedAt: serverTimestamp(),
        content: 'This message was deleted',
        caption: undefined,
        mediaUrl: undefined,
        thumbnailUrl: undefined,
        type: 'deleted',
        // Clear all media-related fields
        mediaWidth: undefined,
        mediaHeight: undefined,
        mediaDuration: undefined,
        mediaSize: undefined,
        fileName: undefined,
        mimeType: undefined,
        waveform: undefined,
        location: undefined,
        contact: undefined,
      });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to delete message' };
    }
  }

  // Forward message to multiple chats
  async forwardMessage(
    originalMessage: RealMessage,
    targetChatIds: string[],
    forwarderId: string,
    forwarderName: string,
    forwarderAvatar?: string
  ): Promise<{ success: boolean; error?: string; forwardedCount?: number }> {
    try {
      let successCount = 0;
      const forwardPromises = targetChatIds.map(async (chatId) => {
        try {
          // Create forwarded message
          const forwardedMessage: Omit<RealMessage, 'id'> = {
            chatId,
            senderId: forwarderId,
            senderName: forwarderName,
            senderAvatar: forwarderAvatar,
            content: originalMessage.content,
            type: originalMessage.type,
            mediaUrl: originalMessage.mediaUrl,
            timestamp: serverTimestamp(),
            statusInfo: {
              status: 'sent',
              timestamp: new Date(),
              readBy: {},
              deliveredTo: {},
            },
            isRead: false,
            isDelivered: true,
            // Mark as forwarded
            isForwarded: true,
            originalSender: originalMessage.senderName,
            originalMessageId: originalMessage.id,
          };

          // Add to messages collection
          const messagesRef = collection(db, 'individual_chats', chatId, 'messages');
          const messageDoc = await addDoc(messagesRef, forwardedMessage);

          // Update chat's last message
          const chatRef = doc(db, 'chats', chatId);
          await updateDoc(chatRef, {
            lastMessage: {
              id: messageDoc.id,
              content: originalMessage.content,
              type: originalMessage.type,
              senderId: forwarderId,
              senderName: forwarderName,
              timestamp: serverTimestamp(),
            },
            lastMessageTime: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });

          successCount++;
        } catch (error) {
          // Error forwarding to this chat - continue with others
        }
      });

      await Promise.all(forwardPromises);

      if (successCount === 0) {
        return { success: false, error: 'Failed to forward message to any chat' };
      } else if (successCount < targetChatIds.length) {
        return {
          success: true,
          forwardedCount: successCount,
          error: `Forwarded to ${successCount} of ${targetChatIds.length} chats`
        };
      } else {
        return { success: true, forwardedCount: successCount };
      }
    } catch (error) {
      return { success: false, error: 'Failed to forward message' };
    }
  }

  // Chat archive functions
  async archiveChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (!chatDoc.exists()) {
        return { success: false, error: 'Chat not found' };
      }

      const chatData = chatDoc.data() as RealChat;
      const archivedBy = chatData.archivedBy || [];

      if (!archivedBy.includes(userId)) {
        archivedBy.push(userId);

        await updateDoc(chatRef, {
          archivedBy: archivedBy,
          [`archivedAt.${userId}`]: serverTimestamp(),
        });
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to archive chat' };
    }
  }

  async unarchiveChat(chatId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (!chatDoc.exists()) {
        return { success: false, error: 'Chat not found' };
      }

      const chatData = chatDoc.data() as RealChat;
      const archivedBy = (chatData.archivedBy || []).filter((id: string) => id !== userId);

      const updateData: any = {
        archivedBy: archivedBy,
      };

      // Remove the user's archive timestamp
      updateData[`archivedAt.${userId}`] = null;

      await updateDoc(chatRef, updateData);

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to unarchive chat' };
    }
  }

  // Get archived chats for a user
  subscribeToArchivedChats(
    userId: string,
    callback: (_chats: RealChat[]) => void
  ): () => void {
    const chatsRef = collection(db, 'chats');
    const q = query(
      chatsRef,
      where('participants', 'array-contains', userId),
      where('archivedBy', 'array-contains', userId),
      orderBy('updatedAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const archivedChats = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      } as RealChat));

      callback(archivedChats);
    });

    return unsubscribe;
  }

  // Media message functions
  async sendMediaMessage(
    chatId: string,
    senderId: string,
    senderName: string,
    senderAvatar: string | undefined,
    mediaUri: string,
    mediaType: 'image' | 'video' | 'audio' | 'file',
    caption?: string,
    replyTo?: string
  ): Promise<{ success: boolean; messageId?: string; error?: string }> {
    try {
      // Upload media to Firebase Storage
      const uploadResult = await this.uploadMediaFile(mediaUri, mediaType);
      if (!uploadResult.success) {
        return { success: false, error: uploadResult.error };
      }

      // Get media metadata
      const metadata = await this.getMediaMetadata(mediaUri, mediaType);

      // Create message
      const message: Omit<RealMessage, 'id'> = {
        chatId,
        senderId,
        senderName,
        senderAvatar,
        content: caption || '',
        caption,
        type: mediaType,
        mediaUrl: uploadResult.url,
        mediaWidth: metadata.width,
        mediaHeight: metadata.height,
        mediaDuration: metadata.duration,
        mediaSize: metadata.size,
        thumbnailUrl: uploadResult.thumbnailUrl,
        timestamp: serverTimestamp(),
        statusInfo: {
          status: 'sent',
          timestamp: new Date(),
          readBy: {},
          deliveredTo: {},
        },
        replyTo,
        reactions: {},
        isRead: false,
        isDelivered: true,
      };

      // Add to messages collection
      const messagesRef = collection(db, 'messages');
      const messageDoc = await addDoc(messagesRef, message);

      // Update chat's last message
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        lastMessage: {
          id: messageDoc.id,
          content: caption || `📎 ${mediaType}`,
          type: mediaType,
          senderId,
          senderName,
          timestamp: serverTimestamp(),
        },
        lastMessageTime: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      return { success: true, messageId: messageDoc.id };
    } catch (error) {
      return { success: false, error: 'Failed to send media message' };
    }
  }

  // Upload media file to Firebase Storage
  private async uploadMediaFile(
    mediaUri: string,
    mediaType: 'image' | 'video' | 'audio' | 'file'
  ): Promise<{ success: boolean; url?: string; thumbnailUrl?: string; error?: string }> {
    try {
      const { ref, uploadBytes, getDownloadURL } = await import('firebase/storage');
      const { storage } = await import('./firebaseSimple');

      // Compress image if it's an image type
      let processedUri = mediaUri;
      if (mediaType === 'image') {
        processedUri = await this.compressImage(mediaUri, 0.8);
      }

      // Convert URI to blob
      const response = await fetch(processedUri);
      const blob = await response.blob();

      // Create storage reference
      const fileExtension = this.getFileExtension(mediaUri, mediaType);
      const fileName = `${Date.now()}_${Math.random().toString(36).substring(7)}.${fileExtension}`;
      const storageRef = ref(storage, `messages/${mediaType}s/${fileName}`);

      // Upload file
      await uploadBytes(storageRef, blob);

      // Get download URL
      const downloadURL = await getDownloadURL(storageRef);

      // Generate thumbnail for videos
      let thumbnailUrl: string | undefined;
      if (mediaType === 'video') {
        thumbnailUrl = await this.generateVideoThumbnail(mediaUri);
      }

      return { success: true, url: downloadURL, thumbnailUrl };
    } catch (error) {
      return { success: false, error: 'Failed to upload media file' };
    }
  }

  // Get media metadata
  private async getMediaMetadata(
    mediaUri: string,
    mediaType: 'image' | 'video' | 'audio' | 'file'
  ): Promise<{
    width?: number;
    height?: number;
    duration?: number;
    size?: number;
  }> {
    try {
      const metadata: any = {};

      // Get file size
      try {
        // Use FileSystem for local files
        const FileSystem = (await import('expo-file-system')).default;
        const fileInfo = await FileSystem.getInfoAsync(mediaUri);

        if (fileInfo.exists && 'size' in fileInfo) {
          metadata.size = fileInfo.size;
        } else {
          // Fallback to fetch for remote files
          const response = await fetch(mediaUri, { method: 'HEAD' });
          const contentLength = response.headers.get('content-length');
          if (contentLength) {
            metadata.size = parseInt(contentLength, 10);
          }
        }
      } catch (error) {
        console.warn('Could not get file size:', error);
        // Continue without file size
      }

      // Get dimensions for images and videos
      if (mediaType === 'image' || mediaType === 'video') {
        try {
          const { Image } = await import('react-native');

          if (mediaType === 'image') {
            const dimensions = await new Promise<{ width: number; height: number }>((resolve, reject) => {
              Image.getSize(
                mediaUri,
                (width, height) => resolve({ width, height }),
                reject
              );
            });
            metadata.width = dimensions.width;
            metadata.height = dimensions.height;
          }
        } catch (error) {
          // Could not get media dimensions - continue without them
        }
      }

      // Get duration for audio and video using expo-av
      if (mediaType === 'audio' || mediaType === 'video') {
        try {
          const { Audio, Video: _Video } = await import('expo-av');

          if (mediaType === 'audio') {
            const { sound } = await Audio.Sound.createAsync({ uri: mediaUri });
            const status = await sound.getStatusAsync();
            if (status.isLoaded && status.durationMillis) {
              metadata.duration = Math.round(status.durationMillis / 1000);
            }
            await sound.unloadAsync();
          } else if (mediaType === 'video') {
            // For video, we can also get duration but it's more complex
            // We'll use a simpler approach for now
            metadata.duration = 0; // Will be updated when video is played
          }
        } catch (error) {
          // Could not get media duration - set to 0
          metadata.duration = 0;
        }
      }

      return metadata;
    } catch (error) {
      return {};
    }
  }

  // Generate video thumbnail using expo-video-thumbnails
  private async generateVideoThumbnail(videoUri: string): Promise<string | undefined> {
    try {
      const { getThumbnailAsync } = await import('expo-video-thumbnails');

      const { uri } = await getThumbnailAsync(videoUri, {
        time: 1000, // Get thumbnail at 1 second
        quality: 0.8,
      });

      return uri;
    } catch (error) {
      // Return a fallback placeholder or the original video URI
      return videoUri;
    }
  }

  // Compress image using expo-image-manipulator
  private async compressImage(imageUri: string, quality: number = 0.8): Promise<string> {
    try {
      const { ImageManipulator } = await import('expo-image-manipulator');

      const result = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          // Resize if image is too large
          { resize: { width: 1920 } } // Max width 1920px, height will be proportional
        ],
        {
          compress: quality,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      return result.uri;
    } catch (error) {
      // Return original URI if compression fails
      return imageUri;
    }
  }

  // Get file extension
  private getFileExtension(uri: string, mediaType: 'image' | 'video' | 'audio' | 'file'): string {
    const uriParts = uri.split('.');
    const extension = uriParts[uriParts.length - 1];

    if (extension && extension.length <= 4) {
      return extension;
    }

    // Default extensions based on media type
    switch (mediaType) {
      case 'image': return 'jpg';
      case 'video': return 'mp4';
      case 'audio': return 'mp3';
      case 'file': return 'bin';
      default: return 'bin';
    }
  }

  /**
   * Listen to real-time messages for a chat
   */
  subscribeToMessages(
    chatId: string,
    callback: (_messages: RealMessage[]) => void
  ): () => void {
    const messagesRef = collection(db, 'messages');
    const q = query(
      messagesRef,
      where('chatId', '==', chatId),
      orderBy('timestamp', 'asc'),
      limit(100)
    );

    const unsubscribe = onSnapshot(
      q,
      async (snapshot) => {
        console.log(`📨 Firestore snapshot received: ${snapshot.size} documents`);
        const messages: RealMessage[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          console.log(`📄 Message document:`, { id: doc.id, chatId: data.chatId, content: data.content });
          messages.push({
            id: doc.id,
            ...data,
            timestamp: data.timestamp?.toDate() || new Date(),
          } as RealMessage);
        });

        console.log(`✅ Processed ${messages.length} messages for chatId: ${chatId}`);

        // Process new media messages for auto download
        await this.processMessagesForAutoDownload(messages, chatId);

        // Keep chronological order (oldest first)
        callback(messages);
      },
      (error) => {
        console.error('❌ Firestore snapshot error:', error);
        console.error('🔍 Error details:', {
          code: error.code,
          message: error.message,
          chatId: chatId
        });
        callback([]);
      }
    );

    // Store listener for cleanup
    this.messageListeners[chatId] = unsubscribe;
    return unsubscribe;
  }

  /**
   * Process messages for auto download
   */
  private async processMessagesForAutoDownload(messages: RealMessage[], chatId: string): Promise<void> {
    try {
      for (const message of messages) {
        // Only process media messages that have URLs
        if (!message.mediaUrl || !['image', 'video', 'audio', 'file'].includes(message.type)) {
          continue;
        }

        // Skip messages from current user (they already have the media)
        if (message.senderId === this.currentUserId) {
          continue;
        }

        // Create download request
        const downloadRequest = {
          id: message.id,
          url: message.mediaUrl,
          type: message.type as 'image' | 'video' | 'audio' | 'document',
          fileName: this.generateFileName(message),
          fileSize: message.mediaSize,
          chatId,
          messageId: message.id,
          senderId: message.senderId,
          priority: 'normal' as const,
          thumbnail: message.thumbnailUrl,
        };

        // Queue for auto download (service will check user preferences and network)
        const wasQueued = await autoDownloadService.queueForAutoDownload(downloadRequest, false);

        if (wasQueued) {
          console.log(`📥 Queued auto download for: ${downloadRequest.fileName}`);
        }
      }
    } catch (error) {
      console.error('❌ Error processing messages for auto download:', error);
    }
  }

  /**
   * Generate appropriate filename for media
   */
  private generateFileName(message: RealMessage): string {
    const timestamp = new Date().getTime();
    const extension = this.getFileExtension(message.mediaUrl || '', message.type as 'image' | 'video' | 'audio' | 'file');
    return `IraChat_${message.type}_${timestamp}.${extension}`;
  }



  /**
   * Create or get a chat between users
   */
  async createOrGetChat(
    currentUserId: string,
    otherUserId: string,
    currentUserName: string,
    otherUserName: string,
    currentUserAvatar?: string,
    otherUserAvatar?: string
  ): Promise<{ success: boolean; chatId?: string; error?: string }> {
    try {
      // Create a consistent chat ID based on user IDs
      const chatId = [currentUserId, otherUserId].sort().join('_');

      // Check if chat already exists
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (chatDoc.exists()) {
        return { success: true, chatId };
      }

      // Create new chat
      const chatData: Omit<RealChat, 'id'> = {
        participants: [currentUserId, otherUserId],
        participantNames: {
          [currentUserId]: currentUserName,
          [otherUserId]: otherUserName,
        },
        participantAvatars: {
          [currentUserId]: currentUserAvatar || '',
          [otherUserId]: otherUserAvatar || '',
        },
        lastMessageTime: serverTimestamp(),
        unreadCount: {
          [currentUserId]: 0,
          [otherUserId]: 0,
        },
        isGroup: false,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      await setDoc(chatRef, chatData);
      return { success: true, chatId };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's chats with real-time updates
   */
  subscribeToUserChats(
    userId: string,
    callback: (_chats: RealChat[]) => void
  ): () => void {
    const chatsRef = collection(db, 'chats');
    const q = query(
      chatsRef,
      where('participants', 'array-contains', userId),
      orderBy('updatedAt', 'desc')
    );

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        const chats: RealChat[] = [];
        snapshot.forEach((doc) => {
          const data = doc.data();
          chats.push({
            id: doc.id,
            ...data,
            lastMessageTime: data.lastMessageTime?.toDate() || data.updatedAt?.toDate() || new Date(),
            createdAt: data.createdAt?.toDate() || new Date(),
            updatedAt: data.updatedAt?.toDate() || new Date(),
          } as RealChat);
        });

        callback(chats);
      },
      () => {
        callback([]);
      }
    );

    this.chatListeners[userId] = unsubscribe;
    return unsubscribe;
  }

  /**
   * Update chat's last message
   */
  private async updateChatLastMessage(chatId: string, message: RealMessage): Promise<void> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        lastMessage: {
          id: message.id,
          content: message.content,
          type: message.type,
          senderId: message.senderId,
          senderName: message.senderName,
          timestamp: message.timestamp,
        },
        lastMessageTime: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      // Error updating chat last message - could implement retry logic
    }
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(chatId: string, userId: string): Promise<void> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      await updateDoc(chatRef, {
        [`unreadCount.${userId}`]: 0,
      });
    } catch (error) {
      // Error marking messages as read - could implement retry logic
    }
  }

  /**
   * Send typing indicator
   */
  async sendTypingIndicator(chatId: string, userId: string, isTyping: boolean): Promise<void> {
    try {
      const typingRef = doc(db, 'chats', chatId, 'typing', userId);
      if (isTyping) {
        await setDoc(typingRef, {
          isTyping: true,
          timestamp: serverTimestamp(),
        });
      } else {
        await setDoc(typingRef, {
          isTyping: false,
          timestamp: serverTimestamp(),
        });
      }
    } catch (error) {
      // Error sending typing indicator - could implement retry logic
    }
  }

  /**
   * Start typing indicator
   */
  async startTyping(chatId: string, userId: string, _userName: string): Promise<void> {
    return this.sendTypingIndicator(chatId, userId, true);
  }

  /**
   * Stop typing indicator
   */
  async stopTyping(chatId: string, userId: string): Promise<void> {
    return this.sendTypingIndicator(chatId, userId, false);
  }

  /**
   * Add reaction to message
   */
  async addReaction(
    chatId: string,
    messageId: string,
    userId: string,
    emoji: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const messageRef = doc(db, 'chats', chatId, 'messages', messageId);
      const messageDoc = await getDoc(messageRef);

      if (!messageDoc.exists()) {
        return { success: false, error: 'Message not found' };
      }

      const messageData = messageDoc.data();
      const reactions = messageData.reactions || {};

      // Toggle reaction
      if (reactions[emoji] && reactions[emoji].includes(userId)) {
        // Remove reaction
        reactions[emoji] = reactions[emoji].filter((id: string) => id !== userId);
        if (reactions[emoji].length === 0) {
          delete reactions[emoji];
        }
      } else {
        // Add reaction
        if (!reactions[emoji]) {
          reactions[emoji] = [];
        }
        reactions[emoji].push(userId);
      }

      await updateDoc(messageRef, { reactions });

      return { success: true };
    } catch (error) {
      return { success: false, error: 'Failed to add reaction' };
    }
  }

  /**
   * Clean up listeners
   */
  cleanup(): void {
    // Clean up message listeners
    Object.values(this.messageListeners).forEach(unsubscribe => unsubscribe());
    this.messageListeners = {};

    // Clean up chat listeners
    Object.values(this.chatListeners).forEach(unsubscribe => unsubscribe());
    this.chatListeners = {};
  }

  /**
   * Get chat by ID
   */
  async getChat(chatId: string): Promise<RealChat | null> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (chatDoc.exists()) {
        const data = chatDoc.data();
        return {
          id: chatDoc.id,
          ...data,
          lastMessageTime: data.lastMessageTime?.toDate() || new Date(),
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as RealChat;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Create a group chat
   */
  async createGroupChat(
    groupId: string,
    groupName: string,
    participants: string[],
    participantNames: { [userId: string]: string },
    participantAvatars: { [userId: string]: string },
    groupAvatar?: string
  ): Promise<{ success: boolean; chatId?: string; error?: string }> {
    try {
      const chatData: Omit<RealChat, 'id'> = {
        participants,
        participantNames,
        participantAvatars,
        lastMessageTime: serverTimestamp(),
        unreadCount: participants.reduce((acc, userId) => {
          acc[userId] = 0;
          return acc;
        }, {} as { [userId: string]: number }),
        isGroup: true,
        groupName,
        groupAvatar,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      const chatRef = doc(db, 'chats', groupId);
      await setDoc(chatRef, chatData);

      return { success: true, chatId: groupId };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Add user to group chat
   */
  async addUserToGroupChat(
    chatId: string,
    userId: string,
    userName: string,
    userAvatar: string
  ): Promise<{ success: boolean; error?: string }> {
    try {

      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (!chatDoc.exists()) {
        return { success: false, error: 'Group chat not found' };
      }

      const chatData = chatDoc.data() as RealChat;

      if (!chatData.isGroup) {
        return { success: false, error: 'Not a group chat' };
      }

      if (chatData.participants.includes(userId)) {
        return { success: false, error: 'User already in group' };
      }

      const updatedParticipants = [...chatData.participants, userId];
      const updatedParticipantNames = { ...chatData.participantNames, [userId]: userName };
      const updatedParticipantAvatars = { ...chatData.participantAvatars, [userId]: userAvatar };
      const updatedUnreadCount = { ...chatData.unreadCount, [userId]: 0 };

      await updateDoc(chatRef, {
        participants: updatedParticipants,
        participantNames: updatedParticipantNames,
        participantAvatars: updatedParticipantAvatars,
        unreadCount: updatedUnreadCount,
        updatedAt: serverTimestamp(),
      });

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Remove user from group chat
   */
  async removeUserFromGroupChat(
    chatId: string,
    userId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {

      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (!chatDoc.exists()) {
        return { success: false, error: 'Group chat not found' };
      }

      const chatData = chatDoc.data() as RealChat;

      if (!chatData.isGroup) {
        return { success: false, error: 'Not a group chat' };
      }

      if (!chatData.participants.includes(userId)) {
        return { success: false, error: 'User not in group' };
      }

      const updatedParticipants = chatData.participants.filter(id => id !== userId);
      const updatedParticipantNames = { ...chatData.participantNames };
      const updatedParticipantAvatars = { ...chatData.participantAvatars };
      const updatedUnreadCount = { ...chatData.unreadCount };

      delete updatedParticipantNames[userId];
      delete updatedParticipantAvatars[userId];
      delete updatedUnreadCount[userId];

      await updateDoc(chatRef, {
        participants: updatedParticipants,
        participantNames: updatedParticipantNames,
        participantAvatars: updatedParticipantAvatars,
        unreadCount: updatedUnreadCount,
        updatedAt: serverTimestamp(),
      });

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Delete group chat
   */
  async deleteGroupChat(chatId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const chatRef = doc(db, 'chats', chatId);
      const chatDoc = await getDoc(chatRef);

      if (!chatDoc.exists()) {
        return { success: false, error: 'Group chat not found' };
      }

      const chatData = chatDoc.data() as RealChat;

      if (!chatData.isGroup) {
        return { success: false, error: 'Not a group chat' };
      }

      // Mark as deleted instead of actually deleting
      await updateDoc(chatRef, {
        deleted: true,
        deletedAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Update message with field value operations
   */
  async updateMessageWithFieldValue(
    chatId: string,
    messageId: string,
    updates: { [key: string]: FieldValue | any }
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const messageRef = doc(db, 'individual_chats', chatId, 'messages', messageId);
      await updateDoc(messageRef, {
        ...updates,
        updatedAt: serverTimestamp(),
      });

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Batch update message timestamps
   */
  async batchUpdateTimestamps(
    chatId: string,
    messageIds: string[]
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const updatePromises = messageIds.map(messageId => {
        const messageRef = doc(db, 'individual_chats', chatId, 'messages', messageId);
        return updateDoc(messageRef, {
          updatedAt: serverTimestamp(),
          lastModified: serverTimestamp(),
        });
      });

      await Promise.all(updatePromises);

      return { success: true };
    } catch (error: any) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Update chats list with new message
   */
  public async updateChatsListWithMessage(
    currentUserId: string,
    chatId: string,
    lastMessage: {
      content: string;
      senderId: string;
      senderName: string;
      timestamp: Date;
      type: 'text' | 'image' | 'video' | 'audio' | 'file';
      isRead: boolean;
    }
  ): Promise<void> {
    try {
      // Extract partner ID from chat ID
      let partnerId = '';
      let partnerName = '';

      if (chatId.includes('_')) {
        // For chat IDs like "user1_user2", extract the other user's ID
        const parts = chatId.split('_');
        partnerId = parts.find(id => id !== currentUserId) || parts[0];
      } else {
        // For simple chat IDs, assume it's the partner ID
        partnerId = chatId;
      }

      // Get proper partner name from the chat context
      // For messages sent by current user, we need to get the partner's name
      if (lastMessage.senderId === currentUserId) {
        // This is our message, so we need the partner's name for the chat list
        partnerName = await this.getPartnerNameFromChatId(chatId, currentUserId);
      } else {
        // This is partner's message, use their sender name
        partnerName = lastMessage.senderName || `User ${partnerId.substring(0, 8)}`;
      }

      // Update chats list for current user
      await chatsListService.addOrUpdateChat(
        currentUserId,
        partnerId,
        partnerName,
        lastMessage
      );

      console.log('✅ Chats list updated with new message');
    } catch (error) {
      console.error('❌ Failed to update chats list:', error);
      // Don't throw error - message sending should still succeed
    }
  }

  /**
   * Get partner name from chat ID
   */
  private async getPartnerNameFromChatId(chatId: string, currentUserId: string): Promise<string> {
    try {
      // Extract partner ID
      let partnerId = '';
      if (chatId.includes('_')) {
        const parts = chatId.split('_');
        partnerId = parts.find(id => id !== currentUserId) || parts[0];
      } else {
        partnerId = chatId;
      }

      // Try to get partner name from user profiles or contacts
      // For now, return a formatted name based on the partner ID
      if (partnerId.startsWith('test_user_')) {
        return `User ${partnerId.split('_')[2] || partnerId.substring(0, 8)}`;
      }

      return `User ${partnerId.substring(0, 8)}`;
    } catch (error) {
      console.error('❌ Failed to get partner name:', error);
      return 'Unknown User';
    }
  }
}

// Export singleton instance
export const realTimeMessagingService = new RealTimeMessagingService();
export default realTimeMessagingService;
