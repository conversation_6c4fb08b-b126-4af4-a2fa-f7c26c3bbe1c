// 🔄 REAL-TIME FIRESTORE REPOST SERVICE
// Complete repost functionality with Firebase integration

import {
  doc,
  setDoc,
  collection,
  addDoc,
  serverTimestamp,
  updateDoc,
  arrayUnion,
  increment,
  onSnapshot,
  query,
  where,
  orderBy,
  limit,
  getDocs,
} from 'firebase/firestore';
import { ref, uploadBytes, getDownloadURL, getBlob } from 'firebase/storage';
import { db, storage } from './firebaseSimple';
import * as FileSystem from 'expo-file-system';
import { Alert } from 'react-native';
import { realUpdatesService } from './realUpdatesService';
import { comprehensiveUpdatesService } from './comprehensiveUpdatesService';

export interface RepostData {
  originalMediaId: string;
  originalMediaUrl: string;
  originalMediaType: 'photo' | 'video';
  originalAuthor: string;
  originalCaption?: string;
  newCaption?: string;
  isStory: boolean;
  privacy: 'public' | 'friends' | 'private';
}

export interface RepostProgress {
  stage: 'copying' | 'uploading' | 'creating' | 'complete';
  percentage: number;
  message: string;
}

class RepostService {
  private activeReposts = new Map<string, boolean>();

  /**
   * Repost media to stories with real-time Firestore integration
   */
  async repostToStories(
    userId: string,
    userName: string,
    userAvatar: string | undefined,
    repostData: RepostData,
    onProgress?: (progress: RepostProgress) => void
  ): Promise<{ success: boolean; updateId?: string; error?: string }> {
    const repostId = `repost_${Date.now()}_${userId}`;
    let tempMediaUri: string | null = null;

    try {
      console.log('🚀 Starting repost process:', {
        repostId,
        userId,
        userName,
        originalMediaId: repostData.originalMediaId,
        originalMediaUrl: repostData.originalMediaUrl,
        originalMediaType: repostData.originalMediaType
      });

      // Validate input parameters
      if (!userId || !userName || !repostData.originalMediaUrl) {
        throw new Error('Missing required parameters for repost');
      }

      // Prevent duplicate reposts
      if (this.activeReposts.has(repostData.originalMediaId)) {
        console.log('⚠️ Repost already in progress for:', repostData.originalMediaId);
        return { success: false, error: 'Repost already in progress' };
      }

      this.activeReposts.set(repostData.originalMediaId, true);
      console.log('✅ Repost process started for:', repostData.originalMediaId);

      onProgress?.({
        stage: 'copying',
        percentage: 10,
        message: 'Copying media file...'
      });

      // Step 1: Copy media to temporary location
      console.log('📋 Step 1: Copying media to temp location...');
      tempMediaUri = await this.copyMediaToTemp(repostData.originalMediaUrl);
      console.log('✅ Step 1 completed: Media copied to', tempMediaUri);

      onProgress?.({
        stage: 'uploading',
        percentage: 30,
        message: 'Uploading to Firebase...'
      });

      // Step 2: Upload to Firebase Storage with repost metadata
      console.log('☁️ Step 2: Uploading to Firebase Storage...');
      const newMediaUrl = await this.uploadRepostMedia(
        tempMediaUri,
        userId,
        repostData,
        (uploadProgress) => {
          onProgress?.({
            stage: 'uploading',
            percentage: 30 + (uploadProgress * 0.5),
            message: `Uploading... ${Math.round(uploadProgress)}%`
          });
        }
      );
      console.log('✅ Step 2 completed: Media uploaded to', newMediaUrl);

      onProgress?.({
        stage: 'creating',
        percentage: 80,
        message: 'Creating story...'
      });

      // Step 3: Create the repost story in Firestore
      console.log('📝 Step 3: Creating repost story in Firestore...');
      const updateId = await this.createRepostStory(
        userId,
        userName,
        userAvatar,
        newMediaUrl,
        repostData
      );
      console.log('✅ Step 3 completed: Story created with ID', updateId);

      onProgress?.({
        stage: 'complete',
        percentage: 100,
        message: 'Repost complete!'
      });

      // Step 4: Track repost analytics
      console.log('📊 Step 4: Tracking analytics...');
      try {
        await this.trackRepostAnalytics(repostData.originalMediaId, userId);
        console.log('✅ Step 4 completed: Analytics tracked');
      } catch (error) {
        console.error('⚠️ Analytics tracking failed (non-critical):', error);
      }

      // Step 5: Send notification
      console.log('🔔 Step 5: Sending notification...');
      try {
        await this.createRepostNotification(
          userId,
          'repost_created',
          updateId,
          `Your repost of @${repostData.originalAuthor}'s ${repostData.originalMediaType} is now live!`
        );
        console.log('✅ Step 5 completed: Notification sent');
      } catch (error) {
        console.error('⚠️ Failed to send repost notification (non-critical):', error);
      }

      // Step 6: Cleanup temp file
      console.log('🧹 Step 6: Cleaning up temp file...');
      if (tempMediaUri) {
        await this.cleanupTempFile(tempMediaUri);
        console.log('✅ Step 6 completed: Temp file cleaned up');
      }

      console.log('🎉 Repost process completed successfully!');
      return { success: true, updateId };

    } catch (error) {
      console.error('❌ Repost failed at some step:', error);
      console.error('❌ Repost error details:', {
        repostId,
        userId,
        originalMediaId: repostData.originalMediaId,
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      });

      // Cleanup temp file on error
      if (tempMediaUri) {
        try {
          await this.cleanupTempFile(tempMediaUri);
          console.log('🧹 Temp file cleaned up after error');
        } catch (cleanupError) {
          console.error('⚠️ Failed to cleanup temp file after error:', cleanupError);
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred during repost'
      };
    } finally {
      this.activeReposts.delete(repostData.originalMediaId);
      console.log('🔓 Repost lock released for:', repostData.originalMediaId);
    }
  }

  /**
   * Copy media file to temporary location
   */
  private async copyMediaToTemp(mediaUrl: string): Promise<string> {
    try {
      console.log('🔄 Starting media copy from:', mediaUrl);

      const timestamp = Date.now();
      const extension = mediaUrl.includes('.mp4') ? '.mp4' : '.jpg';
      const tempFileName = `repost_temp_${timestamp}${extension}`;
      const tempUri = `${FileSystem.documentDirectory}${tempFileName}`;

      console.log('📁 Temp file path:', tempUri);

      // Check if source URL is accessible
      if (!mediaUrl || mediaUrl.trim() === '') {
        throw new Error('Invalid media URL provided');
      }

      // Download the original media with timeout
      console.log('⬇️ Starting download...');
      const downloadResult = await FileSystem.downloadAsync(mediaUrl, tempUri);

      console.log('📊 Download result:', {
        status: downloadResult.status,
        uri: downloadResult.uri,
        headers: downloadResult.headers
      });

      if (downloadResult.status !== 200) {
        throw new Error(`Download failed with status: ${downloadResult.status}`);
      }

      // Verify file was created and has content
      const fileInfo = await FileSystem.getInfoAsync(downloadResult.uri);
      if (!fileInfo.exists) {
        throw new Error('Downloaded file does not exist');
      }

      if (fileInfo.size === 0) {
        throw new Error('Downloaded file is empty');
      }

      console.log('✅ Media copied successfully:', {
        size: fileInfo.size,
        uri: downloadResult.uri
      });

      return downloadResult.uri;
    } catch (error) {
      console.error('❌ Failed to copy media to temp:', error);
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        mediaUrl,
        timestamp: new Date().toISOString()
      });
      throw new Error(`Failed to copy media file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload repost media to Firebase Storage
   */
  private async uploadRepostMedia(
    tempUri: string,
    userId: string,
    repostData: RepostData,
    onProgress?: (progress: number) => void
  ): Promise<string> {
    try {
      console.log('🔄 Starting Firebase upload from:', tempUri);

      // Verify temp file exists before upload
      const fileInfo = await FileSystem.getInfoAsync(tempUri);
      if (!fileInfo.exists) {
        throw new Error('Temporary file does not exist for upload');
      }

      console.log('📊 File info before upload:', {
        size: fileInfo.size,
        exists: fileInfo.exists,
        uri: tempUri
      });

      // Convert file to blob
      console.log('🔄 Converting file to blob...');
      const response = await fetch(tempUri);

      if (!response.ok) {
        throw new Error(`Failed to fetch temp file: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      console.log('📊 Blob created:', {
        size: blob.size,
        type: blob.type
      });

      if (blob.size === 0) {
        throw new Error('Blob is empty - file conversion failed');
      }

      // Create storage reference with repost metadata
      const timestamp = Date.now();
      const extension = repostData.originalMediaType === 'video' ? '.mp4' : '.jpg';
      const fileName = `reposts/${userId}/${timestamp}_repost${extension}`;
      const storageRef = ref(storage, fileName);

      console.log('📁 Upload path:', fileName);

      // Upload with metadata
      const metadata = {
        customMetadata: {
          originalMediaId: repostData.originalMediaId,
          originalAuthor: repostData.originalAuthor,
          repostBy: userId,
          repostAt: new Date().toISOString(),
          isRepost: 'true'
        }
      };

      console.log('⬆️ Starting Firebase upload...');
      const uploadTask = await uploadBytes(storageRef, blob, metadata);
      console.log('✅ Upload completed, getting download URL...');

      const downloadUrl = await getDownloadURL(uploadTask.ref);
      console.log('✅ Download URL obtained:', downloadUrl);

      onProgress?.(100);
      return downloadUrl;

    } catch (error) {
      console.error('❌ Failed to upload repost media:', error);
      console.error('❌ Upload error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        tempUri,
        userId,
        timestamp: new Date().toISOString()
      });
      throw new Error(`Failed to upload media: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Create repost story in Firestore
   */
  private async createRepostStory(
    userId: string,
    userName: string,
    userAvatar: string | undefined,
    mediaUrl: string,
    repostData: RepostData
  ): Promise<string> {
    try {
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000); // 24 hours

      const storyData = {
        userId,
        userName,
        userAvatar: userAvatar || null,
        type: repostData.originalMediaType,
        mediaUrl,
        caption: repostData.newCaption || `Reposted from @${repostData.originalAuthor}`,
        timestamp: serverTimestamp(),
        expiresAt,
        privacy: repostData.privacy,
        isStory: true,
        
        // Repost metadata
        isRepost: true,
        originalMediaId: repostData.originalMediaId,
        originalAuthor: repostData.originalAuthor,
        originalCaption: repostData.originalCaption,
        
        // Interaction arrays
        likes: [],
        views: [],
        shares: [],
        downloads: [],
        comments: [],
        
        // Additional metadata
        mediaWidth: 1080,
        mediaHeight: 1920,
        location: null,
        hashtags: [],
        mentions: [repostData.originalAuthor], // Mention original author
        musicTrack: null,
        textOverlays: [],
        audioCaption: null,
      };

      // Add to Firestore
      const docRef = await addDoc(collection(db, 'updates'), storyData);
      
      console.log('✅ Repost story created:', docRef.id);
      return docRef.id;

    } catch (error) {
      console.error('❌ Failed to create repost story:', error);
      throw new Error('Failed to create story');
    }
  }

  /**
   * Track repost analytics
   */
  private async trackRepostAnalytics(originalMediaId: string, reposterId: string): Promise<void> {
    try {
      // Update original post's repost count
      const originalRef = doc(db, 'updates', originalMediaId);
      await updateDoc(originalRef, {
        shares: arrayUnion(reposterId),
        shareCount: increment(1),
        lastSharedAt: serverTimestamp()
      });

      // Track in analytics collection
      await addDoc(collection(db, 'analytics'), {
        type: 'repost',
        originalMediaId,
        reposterId,
        timestamp: serverTimestamp(),
        platform: 'mobile'
      });

    } catch (error) {
      console.error('⚠️ Failed to track repost analytics:', error);
      // Don't throw - analytics failure shouldn't break repost
    }
  }

  /**
   * Cleanup temporary file
   */
  private async cleanupTempFile(tempUri: string): Promise<void> {
    try {
      const fileInfo = await FileSystem.getInfoAsync(tempUri);
      if (fileInfo.exists) {
        await FileSystem.deleteAsync(tempUri);
        console.log('🧹 Cleaned up temp file:', tempUri);
      }
    } catch (error) {
      console.error('⚠️ Failed to cleanup temp file:', error);
      // Don't throw - cleanup failure is not critical
    }
  }

  /**
   * Get real-time repost feed for user
   */
  subscribeToRepostFeed(
    userId: string,
    onUpdate: (reposts: any[]) => void
  ): () => void {
    const q = query(
      collection(db, 'updates'),
      where('isRepost', '==', true),
      where('userId', '==', userId),
      orderBy('timestamp', 'desc'),
      limit(50)
    );

    return onSnapshot(q, (snapshot) => {
      const reposts = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      onUpdate(reposts);
    });
  }

  /**
   * Check if user can repost (rate limiting)
   */
  async canUserRepost(userId: string): Promise<{ canRepost: boolean; reason?: string }> {
    try {
      console.log('🔍 Checking repost eligibility for user:', userId);

      if (!userId || userId.trim() === '') {
        return {
          canRepost: false,
          reason: 'Invalid user ID'
        };
      }

      // Check recent reposts (last hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      console.log('📅 Checking reposts since:', oneHourAgo.toISOString());

      const q = query(
        collection(db, 'updates'),
        where('userId', '==', userId),
        where('isRepost', '==', true),
        where('timestamp', '>=', oneHourAgo)
      );

      const snapshot = await getDocs(q);
      const recentReposts = snapshot.size;

      console.log('📊 Recent reposts found:', recentReposts);

      if (recentReposts >= 10) {
        console.log('⚠️ Rate limit exceeded');
        return {
          canRepost: false,
          reason: 'Too many reposts in the last hour. Please wait.'
        };
      }

      console.log('✅ User can repost');
      return { canRepost: true };

    } catch (error) {
      console.error('❌ Failed to check repost eligibility:', error);
      console.error('❌ Eligibility check error details:', {
        userId,
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
      // Allow on error to prevent blocking users due to technical issues
      return { canRepost: true };
    }
  }

  /**
   * Create a repost notification
   */
  private async createRepostNotification(
    userId: string,
    type: 'repost_created' | 'repost_liked' | 'repost_viewed',
    repostId: string,
    message: string
  ): Promise<void> {
    try {
      await addDoc(collection(db, 'notifications'), {
        userId,
        type,
        repostId,
        message,
        timestamp: serverTimestamp(),
        read: false,
      });
    } catch (error) {
      console.error('❌ Failed to create repost notification:', error);
    }
  }
}

export const repostService = new RepostService();

// Real-time repost notifications
export const repostNotificationService = {
  /**
   * Subscribe to repost notifications for a user
   */
  subscribeToRepostNotifications(
    userId: string,
    onNotification: (notification: {
      type: 'repost_created' | 'repost_liked' | 'repost_viewed';
      repostId: string;
      message: string;
      timestamp: Date;
    }) => void
  ): () => void {
    const q = query(
      collection(db, 'notifications'),
      where('userId', '==', userId),
      where('type', 'in', ['repost_created', 'repost_liked', 'repost_viewed']),
      orderBy('timestamp', 'desc'),
      limit(10)
    );

    return onSnapshot(q, (snapshot) => {
      snapshot.docChanges().forEach((change) => {
        if (change.type === 'added') {
          const data = change.doc.data();
          onNotification({
            type: data.type,
            repostId: data.repostId,
            message: data.message,
            timestamp: data.timestamp?.toDate() || new Date(),
          });
        }
      });
    });
  },

  /**
   * Create a repost notification
   */
  async createRepostNotification(
    userId: string,
    type: 'repost_created' | 'repost_liked' | 'repost_viewed',
    repostId: string,
    message: string
  ): Promise<void> {
    try {
      await addDoc(collection(db, 'notifications'), {
        userId,
        type,
        repostId,
        message,
        timestamp: serverTimestamp(),
        read: false,
      });
    } catch (error) {
      console.error('❌ Failed to create repost notification:', error);
    }
  },
};
