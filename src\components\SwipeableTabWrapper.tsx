import { useRouter, useSegments } from "expo-router";
import React, { useEffect, useRef } from "react";
import { Animated, Dimensions } from "react-native";
import { Gesture, GestureDetector } from "react-native-gesture-handler";
import { navigationService } from "../services/navigationService";

const { width: screenWidth } = Dimensions.get("window");

interface SwipeableTabWrapperProps {
  children: React.ReactNode;
}

const tabRoutes = [
  "/(tabs)",
  "/(tabs)/groups",
  "/(tabs)/business",
  "/(tabs)/calls",
  "/(tabs)/updates",
];

export default function SwipeableTabWrapper({
  children,
}: SwipeableTabWrapperProps) {
  const _router = useRouter();
  const segments = useSegments();

  // Simple animation values (no bounce)
  const translateX = useRef(new Animated.Value(0)).current;

  // Get current tab index
  const getCurrentTabIndex = () => {
    const currentSegment = segments[segments.length - 1];
    if (currentSegment === "groups") return 1;
    if (currentSegment === "business") return 2;
    if (currentSegment === "calls") return 3;
    if (currentSegment === "updates") return 4; // Stories tab

    return 0; // default to chats
  };

  // Create pan gesture using new Gesture API
  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      const { translationX: tx } = event;
      const currentIndex = getCurrentTabIndex();

      // Completely prevent swiping beyond boundaries
      if (currentIndex === 0 && tx > 0) {
        // At first tab (chats), completely block right swipe
        translateX.setValue(0);
        return;
      }

      if (currentIndex === tabRoutes.length - 1 && tx < 0) {
        // At last tab (calls/settings), completely block left swipe
        translateX.setValue(0);
        return;
      }

      // For middle tabs, allow normal swipe with limits
      const maxTranslation = screenWidth * 0.3;
      translateX.setValue(
        Math.max(-maxTranslation, Math.min(maxTranslation, tx)),
      );
    })
    .onEnd((event) => {
      const { translationX, velocityX } = event;
      const threshold = screenWidth * 0.2; // 20% of screen width
      const currentTabIndex = getCurrentTabIndex();

      let newTabIndex = currentTabIndex;

      // Strict boundary checking for swipe navigation
      if (
        (translationX > threshold || velocityX > 800) &&
        currentTabIndex > 0
      ) {
        // Swipe right - go to previous tab (only if not at first tab)
        newTabIndex = currentTabIndex - 1;
      } else if (
        (translationX < -threshold || velocityX < -800) &&
        currentTabIndex < tabRoutes.length - 1
      ) {
        // Swipe left - go to next tab (only if not at last tab)
        newTabIndex = currentTabIndex + 1;
      }

      // Additional safety check to prevent going beyond boundaries
      if (newTabIndex < 0) newTabIndex = 0;
      if (newTabIndex >= tabRoutes.length) newTabIndex = tabRoutes.length - 1;

      // Reset position immediately without animation
      translateX.setValue(0);

      // Navigate to new tab if changed
      if (newTabIndex !== currentTabIndex) {
        navigationService.navigate(tabRoutes[newTabIndex] as any);
      }
    });

  // No animations - no bounce effect
  useEffect(() => {
    translateX.setValue(0);
  }, [segments]);

  // Configure gesture based on current tab position
  const currentIndex = getCurrentTabIndex();

  // Apply gesture configuration
  const configuredGesture = panGesture
    .activeOffsetX(
      currentIndex === 0
        ? [-20, 0] // Only allow left swipe on first tab
        : currentIndex === tabRoutes.length - 1
          ? [0, 20] // Only allow right swipe on last tab
          : [-20, 20] // Allow both directions on middle tabs
    )
    .failOffsetY([-40, 40])
    .shouldCancelWhenOutside(true)
    .maxPointers(1);

  return (
    <GestureDetector gesture={configuredGesture}>
      <Animated.View
        style={{
          flex: 1,
          transform: [{ translateX }],
        }}
      >
        {children}
      </Animated.View>
    </GestureDetector>
  );
}

// Simple tab transition hook for programmatic navigation
export const useTabTransition = () => {
  const _router = useRouter();
  const segments = useSegments();

  const getCurrentTabIndex = () => {
    const currentSegment = segments[segments.length - 1];
    if (currentSegment === "groups") return 1;
    if (currentSegment === "business") return 2;
    if (currentSegment === "calls") return 3;
    if (currentSegment === "updates") return 4; // Stories tab

    return 0; // default to chats
  };

  const navigateToTab = (direction: "next" | "previous" | number) => {
    const currentIndex = getCurrentTabIndex();
    let targetIndex = currentIndex;

    if (typeof direction === "number") {
      targetIndex = Math.max(0, Math.min(tabRoutes.length - 1, direction));
    } else if (direction === "next") {
      targetIndex = Math.min(tabRoutes.length - 1, currentIndex + 1);
    } else if (direction === "previous") {
      targetIndex = Math.max(0, currentIndex - 1);
    }

    if (targetIndex !== currentIndex) {
      navigationService.navigate(tabRoutes[targetIndex] as any);
    }
  };

  return {
    currentTabIndex: getCurrentTabIndex(),
    navigateToTab,
    canGoNext: getCurrentTabIndex() < tabRoutes.length - 1,
    canGoPrevious: getCurrentTabIndex() > 0,
  };
};
