// 🚀 MESSAGE BUBBLE COMPONENT
// Individual message display with actions and media support

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { RealMessage } from '../../services/realTimeMessagingService';

// Use RealMessage type for consistency
type ChatMessage = RealMessage;

interface MessageBubbleProps {
  message: ChatMessage;
  isOwnMessage: boolean;
  showSenderName: boolean;
  showTimestamp: boolean;
  isProminentTimestamp?: boolean;
  onPress: () => void;
  onLongPress: () => void;
  onReply: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onForward: () => void;
  isDarkMode?: boolean;
  isSelectionMode: boolean;
  isSelected: boolean;
  onSelect: () => void;
}

const COLORS = {
  primary: '#87CEEB',
  background: '#f8f9fa',
  text: '#333',
  textMuted: '#666',
  white: '#ffffff',
  lightGray: '#e1e5e9',
};

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwnMessage,
  showSenderName,
  showTimestamp,
  isProminentTimestamp = true,
  onPress,
  onLongPress,
  onReply,
  onEdit,
  onDelete,
  onForward,
  isSelectionMode,
  isSelected,
  onSelect,
  isDarkMode = false,
}) => {
  const formatTime = (timestamp: any) => {
    if (!timestamp) return '';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);

    // Detect device's time format preference
    const deviceUses24Hour = (() => {
      try {
        const testDate = new Date(2023, 0, 1, 13, 0, 0);
        const timeString = testDate.toLocaleTimeString();
        const has12HourIndicator = /AM|PM|am|pm/i.test(timeString);
        return !has12HourIndicator;
      } catch {
        return false;
      }
    })();

    return date.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      hour12: !deviceUses24Hour
    });
  };

  // Theme-aware colors
  const getThemeColors = () => {
    if (isDarkMode) {
      return {
        ownBubbleBackground: '#2D3748', // Dark gray-blue - easier on eyes
        otherBubbleBackground: '#1A202C', // Very dark gray - comfortable for reading
        ownTextColor: '#E8E8E8',
        otherTextColor: '#E8E8E8',
        timestampColor: '#8E8E93',
        senderNameColor: '#4A90E2',
      };
    } else {
      return {
        ownBubbleBackground: '#2D3748', // Dark gray-blue - easier on eyes
        otherBubbleBackground: '#1A202C', // Very dark gray - comfortable for reading
        ownTextColor: '#E8E8E8',
        otherTextColor: '#E8E8E8',
        timestampColor: '#8E8E93',
        senderNameColor: '#4A90E2',
      };
    }
  };

  const themeColors = getThemeColors();

  const renderMessageContent = () => {
    switch (message.type) {
      case 'image':
        return (
          <View style={styles.mediaContainer}>
            <Image source={{ uri: message.mediaUrl }} style={styles.imageMessage} />
            {message.content && (
              <Text style={[
                styles.messageText,
                { color: isOwnMessage ? themeColors.ownTextColor : themeColors.otherTextColor }
              ]}>
                {message.content}
              </Text>
            )}
          </View>
        );
      
      case 'video':
        return (
          <View style={styles.mediaContainer}>
            <View style={styles.videoContainer}>
              <Image source={{ uri: message.mediaUrl }} style={styles.videoThumbnail} />
              <View style={styles.playButton}>
                <Ionicons name="play" size={24} color="white" />
              </View>
            </View>
            {message.content && (
              <Text style={[
                styles.messageText,
                { color: isOwnMessage ? themeColors.ownTextColor : themeColors.otherTextColor }
              ]}>
                {message.content}
              </Text>
            )}
          </View>
        );
      
      case 'audio':
        return (
          <View style={styles.voiceContainer}>
            <TouchableOpacity style={styles.playButton}>
              <Ionicons name="play" size={16} color="white" />
            </TouchableOpacity>
            <View style={styles.waveform}>
              {[...Array(20)].map((_, i) => (
                <View key={i} style={styles.waveformBar} />
              ))}
            </View>
            <Text style={[styles.voiceDuration, isOwnMessage && styles.ownMessageText]}>
              0:05
            </Text>
          </View>
        );
      
      default:
        return (
          <Text style={[
            styles.messageText,
            { color: isOwnMessage ? themeColors.ownTextColor : themeColors.otherTextColor }
          ]}>
            {message.content}
          </Text>
        );
    }
  };

  const bubbleStyle = [
    styles.messageBubble,
    isOwnMessage ? styles.ownMessageBubble : styles.otherMessageBubble,
    isSelected && styles.selectedBubble,
  ];

  return (
    <View style={[styles.messageContainer, isOwnMessage && styles.ownMessageContainer]}>
      {isSelectionMode && (
        <TouchableOpacity style={styles.selectionButton} onPress={onSelect}>
          <Ionicons 
            name={isSelected ? "checkmark-circle" : "ellipse-outline"} 
            size={24} 
            color={isSelected ? COLORS.primary : COLORS.textMuted} 
          />
        </TouchableOpacity>
      )}
      
      <View style={styles.bubbleWrapper}>
        {showSenderName && !isOwnMessage && (
          <Text style={[styles.senderName, { color: themeColors.senderNameColor }]}>
            {message.senderName}
          </Text>
        )}
        
        <TouchableOpacity
          style={bubbleStyle}
          onPress={onPress}
          onLongPress={onLongPress}
          activeOpacity={0.8}
        >
          {isOwnMessage ? (
            <View style={[styles.gradientBubble, { backgroundColor: themeColors.ownBubbleBackground }]}>
              {renderMessageContent()}
            </View>
          ) : (
            <View style={[styles.regularBubble, { backgroundColor: themeColors.otherBubbleBackground }]}>
              {renderMessageContent()}
            </View>
          )}
        </TouchableOpacity>
        
        {showTimestamp && (
          <View style={[
            styles.timestampContainer,
            isOwnMessage && styles.ownTimestampContainer,
            !isProminentTimestamp && styles.subtleTimestampContainer
          ]}>
            <Text style={[
              styles.timestamp,
              { color: themeColors.timestampColor },
              !isProminentTimestamp && styles.subtleTimestamp
            ]}>
              {formatTime(message.timestamp)}
            </Text>
            {message.isEdited && (
              <Text style={[styles.editedLabel, { color: themeColors.timestampColor }]}>edited</Text>
            )}
            {isOwnMessage && (
              <Ionicons
                name="checkmark-done"
                size={12}
                color={message.readBy?.length ? COLORS.primary : COLORS.textMuted}
              />
            )}
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  messageContainer: {
    flexDirection: 'row',
    marginVertical: 2,
    paddingHorizontal: 4,
  },
  ownMessageContainer: {
    justifyContent: 'flex-end',
  },
  selectionButton: {
    marginRight: 8,
    alignSelf: 'center',
  },
  bubbleWrapper: {
    maxWidth: SCREEN_WIDTH * 0.75,
  },
  senderName: {
    fontSize: 12,
    color: COLORS.textMuted,
    marginBottom: 2,
    marginLeft: 12,
  },
  messageBubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginVertical: 1,
  },
  ownMessageBubble: {
    alignSelf: 'flex-end',
  },
  otherMessageBubble: {
    alignSelf: 'flex-start',
    backgroundColor: 'rgba(13, 125, 99, 0.95)', // Changed from white to dark grey
  },
  selectedBubble: {
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  gradientBubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  regularBubble: {
    borderRadius: 18,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  messageText: {
    fontSize: 16,
    color: COLORS.text,
    lineHeight: 20,
  },
  ownMessageText: {
    color: COLORS.white,
  },
  mediaContainer: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  imageMessage: {
    width: 280,
    height: 280,
    borderRadius: 12,
  },
  videoContainer: {
    position: 'relative',
  },
  videoThumbnail: {
    width: 280,
    height: 210,
    borderRadius: 12,
  },
  playButton: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -12 }, { translateY: -12 }],
    backgroundColor: 'rgba(0,0,0,0.6)',
    borderRadius: 12,
    padding: 8,
  },
  voiceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
  },
  waveform: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 8,
    flex: 1,
  },
  waveformBar: {
    width: 2,
    height: 12,
    backgroundColor: '#B0B0B0', // Changed from white to light grey for visibility
    marginHorizontal: 1,
    borderRadius: 1,
  },
  voiceDuration: {
    fontSize: 12,
    color: COLORS.textMuted,
  },
  timestampContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
    marginLeft: 12,
  },
  ownTimestampContainer: {
    justifyContent: 'flex-end',
    marginRight: 12,
    marginLeft: 0,
  },
  timestamp: {
    fontSize: 11,
    color: COLORS.textMuted,
    marginRight: 4,
  },
  editedLabel: {
    fontSize: 10,
    color: COLORS.textMuted,
    fontStyle: 'italic',
    marginRight: 4,
  },
  subtleTimestampContainer: {
    opacity: 0.6,
    marginTop: 1,
  },
  subtleTimestamp: {
    fontSize: 10,
    opacity: 0.7,
  },
});
