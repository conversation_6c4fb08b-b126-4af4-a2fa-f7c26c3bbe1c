# PowerShell script to build standalone APK for IraChat
Write-Host "Building Standalone APK for IraChat" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

Write-Host "`nThis will create a standalone APK that:" -ForegroundColor Cyan
Write-Host "✅ Runs independently without Expo Go" -ForegroundColor White
Write-Host "✅ Can be installed directly on any Android device" -ForegroundColor White
Write-Host "✅ Includes all your app code and assets" -ForegroundColor White
Write-Host "✅ Works offline after installation" -ForegroundColor White

$continue = Read-Host "`nContinue? (y/n)"
if ($continue -ne 'y' -and $continue -ne 'Y') {
    Write-Host "Build cancelled." -ForegroundColor Yellow
    exit
}

# Step 1: Set up environment
Write-Host "`nStep 1: Setting up build environment..." -ForegroundColor Yellow
Write-Host "=======================================" -ForegroundColor Yellow

# Run the D: drive cache setup
try {
    & ".\setup-d-drive-cache.ps1"
    Write-Host "✅ Environment setup complete" -ForegroundColor Green
} catch {
    Write-Host "⚠️  Environment setup had issues, continuing..." -ForegroundColor Yellow
}

# Step 2: Check prerequisites
Write-Host "`nStep 2: Checking prerequisites..." -ForegroundColor Yellow
Write-Host "==================================" -ForegroundColor Yellow

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js" -ForegroundColor Red
    exit 1
}

# Check npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not found" -ForegroundColor Red
    exit 1
}

# Check/Install EAS CLI
Write-Host "`nChecking EAS CLI..." -ForegroundColor Cyan
try {
    $easVersion = eas --version
    Write-Host "✅ EAS CLI: $easVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠️  EAS CLI not found. Installing..." -ForegroundColor Yellow
    try {
        npm install -g eas-cli
        Write-Host "✅ EAS CLI installed successfully" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to install EAS CLI. Please run: npm install -g eas-cli" -ForegroundColor Red
        exit 1
    }
}

# Check EAS login
Write-Host "`nChecking EAS login status..." -ForegroundColor Cyan
try {
    $whoami = eas whoami 2>$null
    if ($whoami) {
        Write-Host "✅ Logged in as: $whoami" -ForegroundColor Green
    } else {
        throw "Not logged in"
    }
} catch {
    Write-Host "⚠️  Not logged in to EAS. Please login..." -ForegroundColor Yellow
    try {
        eas login
        Write-Host "✅ Login successful" -ForegroundColor Green
    } catch {
        Write-Host "❌ Login failed. Please try again." -ForegroundColor Red
        exit 1
    }
}

# Step 3: Build the APK
Write-Host "`nStep 3: Building standalone APK..." -ForegroundColor Yellow
Write-Host "===================================" -ForegroundColor Yellow

Write-Host "Starting EAS Build with 'standalone' profile..." -ForegroundColor Cyan
Write-Host "This creates a production APK that works without Expo Go" -ForegroundColor White

try {
    # Start the build
    Write-Host "`nRunning: eas build --platform android --profile standalone" -ForegroundColor Gray
    eas build --platform android --profile standalone
    
    Write-Host "`n✅ Build submitted successfully!" -ForegroundColor Green
    
} catch {
    Write-Host "`n❌ Build failed!" -ForegroundColor Red
    Write-Host "Common issues:" -ForegroundColor Yellow
    Write-Host "• Network connection problems" -ForegroundColor White
    Write-Host "• EAS account/billing issues" -ForegroundColor White
    Write-Host "• Configuration errors in app.config.js or eas.json" -ForegroundColor White
    Write-Host "`nTry running manually: eas build --platform android --profile standalone" -ForegroundColor Cyan
    exit 1
}

# Step 4: Provide next steps
Write-Host "`n====================================" -ForegroundColor Green
Write-Host "BUILD SUBMITTED SUCCESSFULLY! 🎉" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

Write-Host "`nYour standalone APK is now building in the cloud." -ForegroundColor Cyan
Write-Host "This typically takes 10-20 minutes." -ForegroundColor White

Write-Host "`nTo monitor build progress:" -ForegroundColor Yellow
Write-Host "  eas build:list" -ForegroundColor White

Write-Host "`nTo download when ready:" -ForegroundColor Yellow
Write-Host "  eas build:download --platform android --profile standalone" -ForegroundColor White

Write-Host "`nINSTALLATION STEPS:" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host "1. Download the APK file when build completes" -ForegroundColor White
Write-Host "2. Transfer to your phone via:" -ForegroundColor White
Write-Host "   • USB cable and file manager" -ForegroundColor Gray
Write-Host "   • Email the APK to yourself" -ForegroundColor Gray
Write-Host "   • Upload to Google Drive/Dropbox" -ForegroundColor Gray
Write-Host "   • ADB: adb install your-app.apk" -ForegroundColor Gray
Write-Host "3. On your phone:" -ForegroundColor White
Write-Host "   • Go to Settings > Security" -ForegroundColor Gray
Write-Host "   • Enable 'Install from unknown sources'" -ForegroundColor Gray
Write-Host "   • Tap the APK file to install" -ForegroundColor Gray
Write-Host "   • Grant permissions when prompted" -ForegroundColor Gray
Write-Host "4. Open 'IraChat' from your app drawer!" -ForegroundColor White

Write-Host "`nThe app will work completely independently! 🚀" -ForegroundColor Green
