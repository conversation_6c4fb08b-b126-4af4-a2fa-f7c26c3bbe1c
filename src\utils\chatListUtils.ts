import { UnifiedChatItem } from '../components/ModernChatItem';
import { Dimensions } from 'react-native';

// Get device dimensions for responsive chat list
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');
const isSmallDevice = SCREEN_WIDTH < 375;
const isTablet = SCREEN_WIDTH >= 768;

// Responsive chat item heights
export const CHAT_ITEM_HEIGHT = isSmallDevice ? 72 : isTablet ? 88 : 80;
export const COMPACT_CHAT_ITEM_HEIGHT = isSmallDevice ? 60 : 68;
export const EXPANDED_CHAT_ITEM_HEIGHT = isSmallDevice ? 96 : isTablet ? 112 : 104;

// Chat list interfaces
export interface ChatListFilter {
  searchQuery?: string;
  showUnreadOnly?: boolean;
  showGroupsOnly?: boolean;
  showIndividualOnly?: boolean;
  showArchivedOnly?: boolean;
  sortBy?: 'lastMessage' | 'name' | 'unreadCount';
  sortOrder?: 'asc' | 'desc';
}

export interface ChatListState {
  chats: UnifiedChatItem[];
  filteredChats: UnifiedChatItem[];
  isLoading: boolean;
  error: string | null;
  hasMore: boolean;
  lastVisible: any;
}

// Key extractor for FlatList with enhanced uniqueness
export const keyExtractor = (item: UnifiedChatItem): string => {
  return `${item.id}_${item.lastMessageAt || 'no_message'}`;
};

// Responsive item layout for FlatList performance
export const getItemLayout = (_data: any, index: number) => ({
  length: CHAT_ITEM_HEIGHT,
  offset: CHAT_ITEM_HEIGHT * index,
  index,
});

// Compact item layout for dense view
export const getCompactItemLayout = (_data: any, index: number) => ({
  length: COMPACT_CHAT_ITEM_HEIGHT,
  offset: COMPACT_CHAT_ITEM_HEIGHT * index,
  index,
});

// Calculate optimal window size for FlatList
export const getOptimalWindowSize = (): number => {
  const itemsPerScreen = Math.ceil(SCREEN_HEIGHT / CHAT_ITEM_HEIGHT);
  return Math.max(itemsPerScreen * 2, 10); // At least 2 screens worth
};

// Calculate initial number of items to render
export const getInitialNumToRender = (): number => {
  const itemsPerScreen = Math.ceil(SCREEN_HEIGHT / CHAT_ITEM_HEIGHT);
  return Math.max(itemsPerScreen + 2, 8); // Slightly more than one screen
};

/**
 * Filter chats based on search query and filters
 */
export const filterChats = (
  chats: UnifiedChatItem[],
  filter: ChatListFilter
): UnifiedChatItem[] => {
  try {
    let filtered = [...chats];

    // Search query filter
    if (filter.searchQuery && filter.searchQuery.trim()) {
      const query = filter.searchQuery.toLowerCase().trim();
      filtered = filtered.filter(chat => {
        const name = (chat.name || '').toLowerCase();
        const lastMessage = (chat.lastMessage || '').toLowerCase();
        return name.includes(query) || lastMessage.includes(query);
      });
    }

    // Unread only filter
    if (filter.showUnreadOnly) {
      filtered = filtered.filter(chat => (chat.unreadCount || 0) > 0);
    }

    // Groups only filter
    if (filter.showGroupsOnly) {
      filtered = filtered.filter(chat => chat.isGroup);
    }

    // Individual only filter
    if (filter.showIndividualOnly) {
      filtered = filtered.filter(chat => !chat.isGroup);
    }

    // Archived only filter
    if (filter.showArchivedOnly) {
      filtered = filtered.filter(chat => chat.isArchived);
    } else {
      // Exclude archived by default
      filtered = filtered.filter(chat => !chat.isArchived);
    }

    return filtered;
  } catch (error) {
    return chats;
  }
};

/**
 * Sort chats based on criteria
 */
export const sortChats = (
  chats: UnifiedChatItem[],
  sortBy: 'lastMessage' | 'name' | 'unreadCount' = 'lastMessage',
  sortOrder: 'asc' | 'desc' = 'desc'
): UnifiedChatItem[] => {
  try {
    const sorted = [...chats].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'lastMessage':
          const aTime = a.lastMessageAt ? new Date(a.lastMessageAt).getTime() : 0;
          const bTime = b.lastMessageAt ? new Date(b.lastMessageAt).getTime() : 0;
          comparison = aTime - bTime;
          break;

        case 'name':
          const aName = (a.name || '').toLowerCase();
          const bName = (b.name || '').toLowerCase();
          comparison = aName.localeCompare(bName);
          break;

        case 'unreadCount':
          const aUnread = a.unreadCount || 0;
          const bUnread = b.unreadCount || 0;
          comparison = aUnread - bUnread;
          break;

        default:
          comparison = 0;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    // Always prioritize pinned chats at the top
    const pinned = sorted.filter(chat => chat.isPinned);
    const unpinned = sorted.filter(chat => !chat.isPinned);

    return [...pinned, ...unpinned];
  } catch (error) {
    return chats;
  }
};

/**
 * Group chats by date for sectioned list
 */
export const groupChatsByDate = (chats: UnifiedChatItem[]): {
  title: string;
  data: UnifiedChatItem[];
}[] => {
  try {
    const groups: { [key: string]: UnifiedChatItem[] } = {};
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    chats.forEach(chat => {
      if (!chat.lastMessageAt) {
        if (!groups['Older']) groups['Older'] = [];
        groups['Older'].push(chat);
        return;
      }

      const messageDate = new Date(chat.lastMessageAt);
      const isToday = messageDate.toDateString() === today.toDateString();
      const isYesterday = messageDate.toDateString() === yesterday.toDateString();

      let groupKey: string;
      if (isToday) {
        groupKey = 'Today';
      } else if (isYesterday) {
        groupKey = 'Yesterday';
      } else {
        const daysDiff = Math.floor((today.getTime() - messageDate.getTime()) / (1000 * 60 * 60 * 24));
        if (daysDiff < 7) {
          groupKey = 'This Week';
        } else if (daysDiff < 30) {
          groupKey = 'This Month';
        } else {
          groupKey = 'Older';
        }
      }

      if (!groups[groupKey]) groups[groupKey] = [];
      groups[groupKey].push(chat);
    });

    // Convert to sectioned format
    const sections = Object.entries(groups).map(([title, data]) => ({
      title,
      data: sortChats(data, 'lastMessage', 'desc'),
    }));

    // Sort sections by priority
    const sectionOrder = ['Today', 'Yesterday', 'This Week', 'This Month', 'Older'];
    return sections.sort((a, b) => {
      const aIndex = sectionOrder.indexOf(a.title);
      const bIndex = sectionOrder.indexOf(b.title);
      return (aIndex === -1 ? 999 : aIndex) - (bIndex === -1 ? 999 : bIndex);
    });
  } catch (error) {
    return [{ title: 'All Chats', data: chats }];
  }
};

/**
 * Calculate unread count for chat list
 */
export const getTotalUnreadCount = (chats: UnifiedChatItem[]): number => {
  try {
    return chats.reduce((total, chat) => {
      if (chat.isArchived || chat.isMuted) return total;
      return total + (chat.unreadCount || 0);
    }, 0);
  } catch (error) {
    return 0;
  }
};

/**
 * Get chat list statistics
 */
export const getChatListStats = (chats: UnifiedChatItem[]): {
  total: number;
  unread: number;
  groups: number;
  individual: number;
  archived: number;
  pinned: number;
  muted: number;
} => {
  try {
    return {
      total: chats.length,
      unread: chats.filter(chat => (chat.unreadCount || 0) > 0).length,
      groups: chats.filter(chat => chat.isGroup).length,
      individual: chats.filter(chat => !chat.isGroup).length,
      archived: chats.filter(chat => chat.isArchived).length,
      pinned: chats.filter(chat => chat.isPinned).length,
      muted: chats.filter(chat => chat.isMuted).length,
    };
  } catch (error) {
    return {
      total: 0,
      unread: 0,
      groups: 0,
      individual: 0,
      archived: 0,
      pinned: 0,
      muted: 0,
    };
  }
};

// Offline chat list management
/**
 * Cache chat list for offline access
 */
export const cacheChatListOffline = async (chats: UnifiedChatItem[]): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    // Clear existing cache
    await database.runAsync('DELETE FROM cached_chat_list');

    // Insert new chat list
    for (const chat of chats) {
      await database.runAsync(`
        INSERT INTO cached_chat_list (
          id, name, isGroup, lastMessage, lastMessageAt, lastMessageType, unreadCount,
          avatar, isArchived, isPinned, isMuted, participants, timestamp
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        chat.id,
        chat.name || '',
        chat.isGroup ? 1 : 0,
        chat.lastMessage || '',
        chat.lastMessageAt ? chat.lastMessageAt.getTime() : null,
        chat.lastMessageType || 'text',
        chat.unreadCount || 0,
        chat.avatar || '',
        chat.isArchived ? 1 : 0,
        chat.isPinned ? 1 : 0,
        chat.isMuted ? 1 : 0,
        JSON.stringify(chat.participants || []),
        Date.now()
      ]);
    }
  } catch (error) {
    // Cache failed - continue without caching
  }
};

/**
 * Get cached chat list from offline storage
 */
export const getCachedChatList = async (): Promise<UnifiedChatItem[]> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const result = await database.getAllAsync(`
      SELECT * FROM cached_chat_list
      ORDER BY lastMessageAt DESC
    `);

    return result.map((row: any) => ({
      id: row.id,
      name: row.name,
      isGroup: row.isGroup === 1,
      lastMessage: row.lastMessage,
      lastMessageAt: new Date(row.lastMessageAt || Date.now()),
      lastMessageTime: new Date(row.lastMessageAt || Date.now()),
      lastMessageType: row.lastMessageType || 'text',
      unreadCount: row.unreadCount || 0,
      avatar: row.avatar,
      isArchived: row.isArchived === 1,
      isPinned: row.isPinned === 1,
      isMuted: row.isMuted === 1,
      participants: JSON.parse(row.participants || '[]'),
      participantNames: {},
      participantAvatars: {},
      messageCount: 0,
      mediaCount: 0,
      isOnline: false,
      lastSeen: new Date(),
      isTyping: false,
      createdAt: new Date(row.createdAt || Date.now()),
      updatedAt: new Date(row.updatedAt || Date.now()),
    }));
  } catch (error) {
    return [];
  }
};

/**
 * Update chat in offline cache
 */
export const updateCachedChat = async (chatId: string, updates: Partial<UnifiedChatItem>): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = Object.values(updates).map(value =>
      typeof value === 'object' ? JSON.stringify(value) : value
    );

    await database.runAsync(`
      UPDATE cached_chat_list
      SET ${setClause}, timestamp = ?
      WHERE id = ?
    `, [...values, Date.now(), chatId]);
  } catch (error) {
    // Update failed - continue
  }
};

/**
 * Remove chat from offline cache
 */
export const removeCachedChat = async (chatId: string): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    await database.runAsync('DELETE FROM cached_chat_list WHERE id = ?', [chatId]);
  } catch (error) {
    // Remove failed - continue
  }
};

/**
 * Search cached chats offline
 */
export const searchCachedChats = async (query: string): Promise<UnifiedChatItem[]> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const searchQuery = `%${query.toLowerCase()}%`;
    const result = await database.getAllAsync(`
      SELECT * FROM cached_chat_list
      WHERE LOWER(name) LIKE ? OR LOWER(lastMessage) LIKE ?
      ORDER BY lastMessageAt DESC
    `, [searchQuery, searchQuery]);

    return result.map((row: any) => ({
      id: row.id,
      name: row.name,
      isGroup: row.isGroup === 1,
      lastMessage: row.lastMessage,
      lastMessageAt: row.lastMessageAt,
      lastMessageTime: new Date(row.lastMessageAt || Date.now()),
      lastMessageType: row.lastMessageType || 'text',
      unreadCount: row.unreadCount || 0,
      avatar: row.avatar,
      isArchived: row.isArchived === 1,
      isPinned: row.isPinned === 1,
      isMuted: row.isMuted === 1,
      participants: JSON.parse(row.participants || '[]'),
      participantNames: JSON.parse(row.participants || '[]').map((p: any) => p.name || ''),
      participantAvatars: JSON.parse(row.participants || '[]').map((p: any) => p.avatar || ''),
      messageCount: 0,
      mediaCount: 0,
      isOnline: false,
      lastSeen: new Date(),
      isTyping: false,
      createdAt: new Date(row.createdAt || Date.now()),
      updatedAt: new Date(row.updatedAt || Date.now()),
    }));
  } catch (error) {
    return [];
  }
};

/**
 * Clean old cached chat data (older than 7 days)
 */
export const cleanOldCachedChats = async (): Promise<void> => {
  try {
    const { offlineDatabaseService } = await import('../services/offlineDatabase');
    const database = offlineDatabaseService.getDatabase();

    const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

    await database.runAsync(`
      DELETE FROM cached_chat_list
      WHERE timestamp < ?
    `, [sevenDaysAgo]);
  } catch (error) {
    // Cleanup failed - continue
  }
};

/**
 * Get chat list with offline fallback
 */
export const getChatListWithOfflineFallback = async (
  onlineChats: UnifiedChatItem[] | null,
  isOnline: boolean
): Promise<UnifiedChatItem[]> => {
  try {
    if (isOnline && onlineChats) {
      // Cache the online data for offline use
      await cacheChatListOffline(onlineChats);
      return onlineChats;
    } else {
      // Use cached data when offline or online data unavailable
      const cachedChats = await getCachedChatList();
      return cachedChats;
    }
  } catch (error) {
    return [];
  }
};
