/**
 * IraChat Wallpaper Picker Component
 * Comprehensive wallpaper selection interface with preview functionality
 */

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Modal,
  Dimensions,
  Alert,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as FileSystem from 'expo-file-system';
import { LinearGradient } from 'expo-linear-gradient';
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS } from '../../styles/iraChatDesignSystem';
import { IraChatWallpaper, BUILT_IN_WALLPAPERS, WallpaperType } from './IraChatWallpaper';
import { WallpaperConfig } from '../../services/wallpaperService';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface WallpaperPickerProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (config: WallpaperConfig) => void;
  currentConfig?: WallpaperConfig;
  chatId?: string;
  isGroupChat?: boolean;
  isDarkMode?: boolean;
}

export const WallpaperPicker: React.FC<WallpaperPickerProps> = ({
  visible,
  onClose,
  onSelect,
  currentConfig,
  chatId,
  isGroupChat = false,
  isDarkMode = false,
}) => {
  const [selectedTab, setSelectedTab] = useState<'default' | 'gradients' | 'solids' | 'patterns' | 'custom'>('default');
  const [previewConfig, setPreviewConfig] = useState<WallpaperConfig | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [uploading, setUploading] = useState(false);

  // Handle wallpaper selection
  const handleWallpaperSelect = (config: WallpaperConfig) => {
    setPreviewConfig(config);
    setShowPreview(true);
  };

  // Confirm wallpaper selection
  const confirmSelection = () => {
    if (previewConfig) {
      onSelect(previewConfig);
      setShowPreview(false);
      onClose();
    }
  };

  // Handle custom image upload
  const handleCustomUpload = async () => {
    try {
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (!permissionResult.granted) {
        Alert.alert('Permission Required', 'Please grant permission to access your photo library.');
        return;
      }

      setUploading(true);
      
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [9, 16], // Phone aspect ratio
        quality: 1,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];
        
        // Convert to base64 for storage
        const base64 = await FileSystem.readAsStringAsync(asset.uri, {
          encoding: FileSystem.EncodingType.Base64,
        });

        const config: WallpaperConfig = {
          type: 'custom',
          customUri: asset.uri,
          customBase64: base64,
        };

        handleWallpaperSelect(config);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to upload image. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Render wallpaper option
  const renderWallpaperOption = (config: WallpaperConfig, name: string, preview: React.ReactNode) => (
    <TouchableOpacity
      style={styles.wallpaperOption}
      onPress={() => handleWallpaperSelect(config)}
    >
      <View style={styles.wallpaperPreview}>
        {preview}
      </View>
      <Text style={styles.wallpaperName}>{name}</Text>
    </TouchableOpacity>
  );

  // Render tab content
  const renderTabContent = () => {
    switch (selectedTab) {
      case 'default':
        return (
          <View style={styles.wallpaperGrid}>
            {renderWallpaperOption(
              { type: 'default' },
              'IraChat Default',
              <View style={[styles.previewBox, { backgroundColor: IRACHAT_COLORS.primary }]}>
                <Ionicons name="chatbubbles" size={24} color="white" />
              </View>
            )}
          </View>
        );

      case 'gradients':
        return (
          <View style={styles.wallpaperGrid}>
            {BUILT_IN_WALLPAPERS.gradients.map((gradient) =>
              renderWallpaperOption(
                { type: 'gradient', gradientId: gradient.id },
                gradient.name,
                <LinearGradient
                  colors={gradient.colors as any}
                  style={styles.previewBox}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                />
              )
            )}
          </View>
        );

      case 'solids':
        return (
          <View style={styles.wallpaperGrid}>
            {BUILT_IN_WALLPAPERS.solids.map((solid) =>
              renderWallpaperOption(
                { type: 'solid', solidColor: solid.color },
                solid.name,
                <View style={[styles.previewBox, { backgroundColor: solid.color }]} />
              )
            )}
          </View>
        );

      case 'patterns':
        return (
          <View style={styles.wallpaperGrid}>
            {BUILT_IN_WALLPAPERS.patterns.map((pattern) =>
              renderWallpaperOption(
                { type: 'pattern', patternId: pattern.id },
                pattern.name,
                <View style={[styles.previewBox, { backgroundColor: '#F5F5F5' }]}>
                  <Text style={styles.patternText}>{pattern.name.charAt(0)}</Text>
                </View>
              )
            )}
          </View>
        );

      case 'custom':
        return (
          <View style={styles.customSection}>
            <TouchableOpacity
              style={styles.uploadButton}
              onPress={handleCustomUpload}
              disabled={uploading}
            >
              {uploading ? (
                <ActivityIndicator color={IRACHAT_COLORS.primary} />
              ) : (
                <>
                  <Ionicons name="cloud-upload" size={32} color={IRACHAT_COLORS.primary} />
                  <Text style={styles.uploadText}>Upload from Gallery</Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        );

      default:
        return null;
    }
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Ionicons name="close" size={24} color={IRACHAT_COLORS.text} />
          </TouchableOpacity>
          <Text style={styles.title}>Choose Wallpaper</Text>
          <View style={styles.placeholder} />
        </View>

        {/* Tabs */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tabContainer}>
          {[
            { key: 'default', label: 'Default', icon: 'home' },
            { key: 'gradients', label: 'Gradients', icon: 'color-palette' },
            { key: 'solids', label: 'Solid Colors', icon: 'square' },
            { key: 'patterns', label: 'Patterns', icon: 'grid' },
            { key: 'custom', label: 'Custom', icon: 'image' },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.key}
              style={[styles.tab, selectedTab === tab.key && styles.activeTab]}
              onPress={() => setSelectedTab(tab.key as any)}
            >
              <Ionicons 
                name={tab.icon as any} 
                size={20} 
                color={selectedTab === tab.key ? IRACHAT_COLORS.primary : IRACHAT_COLORS.textSecondary} 
              />
              <Text style={[
                styles.tabText,
                selectedTab === tab.key && styles.activeTabText
              ]}>
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Content */}
        <ScrollView style={styles.content}>
          {renderTabContent()}
        </ScrollView>

        {/* Preview Modal */}
        <Modal visible={showPreview} animationType="fade" transparent>
          <View style={styles.previewOverlay}>
            <View style={styles.previewContainer}>
              <View style={styles.previewHeader}>
                <Text style={styles.previewTitle}>Preview</Text>
                <TouchableOpacity onPress={() => setShowPreview(false)}>
                  <Ionicons name="close" size={24} color={IRACHAT_COLORS.text} />
                </TouchableOpacity>
              </View>
              
              <View style={styles.previewContent}>
                {previewConfig && (
                  <IraChatWallpaper
                    wallpaperType={previewConfig.type}
                    wallpaperConfig={previewConfig}
                    isDarkMode={isDarkMode}
                    opacity={1}
                  >
                    <View style={styles.sampleChat}>
                      <Text style={styles.sampleMessage}>This is how your wallpaper will look!</Text>
                    </View>
                  </IraChatWallpaper>
                )}
              </View>
              
              <View style={styles.previewActions}>
                <TouchableOpacity style={styles.cancelButton} onPress={() => setShowPreview(false)}>
                  <Text style={styles.cancelText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.confirmButton} onPress={confirmSelection}>
                  <Text style={styles.confirmText}>Apply Wallpaper</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: IRACHAT_COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  closeButton: {
    padding: SPACING.sm,
  },
  title: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: IRACHAT_COLORS.text,
  },
  placeholder: {
    width: 40,
  },
  tabContainer: {
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginHorizontal: SPACING.xs,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: IRACHAT_COLORS.primary,
  },
  tabText: {
    marginLeft: SPACING.xs,
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: IRACHAT_COLORS.textSecondary,
  },
  activeTabText: {
    color: IRACHAT_COLORS.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: SPACING.md,
  },
  wallpaperGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  wallpaperOption: {
    width: '48%',
    marginBottom: SPACING.md,
    alignItems: 'center',
  },
  wallpaperPreview: {
    width: '100%',
    height: 120,
    borderRadius: BORDER_RADIUS.md,
    overflow: 'hidden',
    marginBottom: SPACING.xs,
  },
  previewBox: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wallpaperName: {
    fontSize: TYPOGRAPHY.fontSize.sm,
    color: IRACHAT_COLORS.text,
    textAlign: 'center',
  },
  patternText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: IRACHAT_COLORS.primary,
  },
  customSection: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
  },
  uploadButton: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.xl,
    borderWidth: 2,
    borderColor: IRACHAT_COLORS.primary,
    borderStyle: 'dashed',
    borderRadius: BORDER_RADIUS.lg,
    minHeight: 120,
    minWidth: 200,
  },
  uploadText: {
    marginTop: SPACING.sm,
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.primary,
    fontWeight: '600',
  },
  previewOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  previewContainer: {
    width: screenWidth * 0.9,
    height: screenHeight * 0.8,
    backgroundColor: IRACHAT_COLORS.surface,
    borderRadius: BORDER_RADIUS.lg,
    overflow: 'hidden',
  },
  previewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.border,
  },
  previewTitle: {
    fontSize: TYPOGRAPHY.fontSize.lg,
    fontWeight: 'bold',
    color: IRACHAT_COLORS.text,
  },
  previewContent: {
    flex: 1,
    position: 'relative',
  },
  sampleChat: {
    position: 'absolute',
    bottom: 100,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: SPACING.md,
    borderRadius: BORDER_RADIUS.md,
  },
  sampleMessage: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.text,
    textAlign: 'center',
  },
  previewActions: {
    flexDirection: 'row',
    padding: SPACING.md,
    borderTopWidth: 1,
    borderTopColor: IRACHAT_COLORS.border,
  },
  cancelButton: {
    flex: 1,
    padding: SPACING.md,
    marginRight: SPACING.sm,
    borderWidth: 1,
    borderColor: IRACHAT_COLORS.border,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  confirmButton: {
    flex: 1,
    padding: SPACING.md,
    marginLeft: SPACING.sm,
    backgroundColor: IRACHAT_COLORS.primary,
    borderRadius: BORDER_RADIUS.md,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.text,
  },
  confirmText: {
    fontSize: TYPOGRAPHY.fontSize.base,
    color: IRACHAT_COLORS.textOnPrimary,
    fontWeight: '600',
  },
});

export default WallpaperPicker;
