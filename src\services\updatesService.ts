/**
 * Updates Service - Handle updates/stories functionality with real data
 */

import {
    addDoc,
    collection,
    doc,
    getDoc,
    getDocs,
    limit,
    onSnapshot,
    orderBy,
    query,
    serverTimestamp,
    Timestamp,
    updateDoc,
    where,
} from "firebase/firestore";
import { db, firestore } from "./firebaseSimple";
import { offlineDatabaseService } from './offlineDatabase';
import { networkStateManager } from './networkStateManager';

export interface Update {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  type: "image" | "video";
  mediaUrl: string;
  thumbnailUrl?: string;
  caption?: string;
  duration?: number;
  timestamp: Date;
  expiresAt: Date;
  views: string[];
  likes: string[];
  comments: Comment[];
  isVisible: boolean;
  metadata?: {
    width: number;
    height: number;
    size: number;
  };
}

export interface Comment {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  text: string;
  timestamp: Date;
  likesCount: number;
  isVisible: boolean;
}

// Offline support functions
const cacheUpdateOffline = async (update: Update): Promise<void> => {
  try {
    const database = offlineDatabaseService.getDatabase();
    await database.runAsync(`
      INSERT OR REPLACE INTO cached_updates (
        id, userId, userName, userAvatar, type, mediaUrl, thumbnailUrl,
        caption, duration, timestamp, expiresAt, views, likes, comments,
        isVisible, metadata
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      update.id, update.userId, update.userName, update.userAvatar || '',
      update.type, update.mediaUrl, update.thumbnailUrl || '',
      update.caption || '', update.duration || 0, update.timestamp.getTime(),
      update.expiresAt.getTime(), JSON.stringify(update.views),
      JSON.stringify(update.likes), JSON.stringify(update.comments),
      update.isVisible ? 1 : 0, JSON.stringify(update.metadata)
    ]);
  } catch (error) {
    // Cache failed - continue without caching
  }
};

const getOfflineUpdates = async (limitCount: number = 50): Promise<Update[]> => {
  try {
    const database = offlineDatabaseService.getDatabase();
    const now = Date.now();
    const result = await database.getAllAsync(`
      SELECT * FROM cached_updates
      WHERE expiresAt > ? AND isVisible = 1
      ORDER BY timestamp DESC
      LIMIT ?
    `, [now, limitCount]);

    return result.map((row: any) => ({
      id: row.id,
      userId: row.userId,
      userName: row.userName,
      userAvatar: row.userAvatar || undefined,
      type: row.type,
      mediaUrl: row.mediaUrl,
      thumbnailUrl: row.thumbnailUrl || undefined,
      caption: row.caption || undefined,
      duration: row.duration || undefined,
      timestamp: new Date(row.timestamp),
      expiresAt: new Date(row.expiresAt),
      views: JSON.parse(row.views || '[]'),
      likes: JSON.parse(row.likes || '[]'),
      comments: JSON.parse(row.comments || '[]'),
      isVisible: row.isVisible === 1,
      metadata: row.metadata ? JSON.parse(row.metadata) : undefined,
    }));
  } catch (error) {
    return [];
  }
};

/**
 * Create a new update/story with real media upload
 */
export const createUpdate = async (
  userId: string,
  userName: string,
  file: Blob | File,
  type: "image" | "video",
  caption?: string,
  userAvatar?: string,
  onProgress?: (progress: number) => void,
): Promise<string> => {
  try {
    if (!firestore) throw new Error("Firestore not initialized");

    let mediaResult: any;

    if (networkStateManager.isOnline()) {
      // Upload media to storage
      const { storageService } = await import("./storageService");
      // Convert type to storage service format
      const storageType = type === "image" ? "images" : "videos";
      mediaResult = await storageService.uploadUpdateMedia(
        userId,
        file,
        storageType as "images" | "videos",
        onProgress ? (progress) => onProgress(progress.progress) : undefined,
      );
    } else {
      // Store offline for later upload
      mediaResult = {
        url: `offline_${Date.now()}_${userId}`,
        metadata: { size: file.size || 0 }
      };
    }

    const now = new Date();
    const expiresAt = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

    const update: Omit<Update, "id"> = {
      userId,
      userName,
      userAvatar,
      type,
      mediaUrl: mediaResult.url,
      caption,
      timestamp: now,
      expiresAt: expiresAt,
      views: [],
      likes: [],
      comments: [],
      isVisible: true,
      metadata: {
        width: 1080,
        height: 1920,
        size: mediaResult.metadata.size,
      },
    };

    let docId: string;

    if (networkStateManager.isOnline()) {
      const docRef = await addDoc(collection(firestore, "updates"), {
        ...update,
        timestamp: serverTimestamp(),
        expiresAt: Timestamp.fromDate(expiresAt),
      });
      docId = docRef.id;
    } else {
      // Generate offline ID and store for later sync
      docId = `offline_${Date.now()}_${userId}`;
      // Store in offline queue for later sync
    }

    // Cache update offline
    const fullUpdate = { ...update, id: docId };
    await cacheUpdateOffline(fullUpdate);

    return docId;
  } catch (error) {
    throw error;
  }
};

/**
 * Get all updates (non-expired)
 */
export const getUpdates = async (
  limitCount: number = 50,
): Promise<Update[]> => {
  try {
    if (!firestore) throw new Error("Firestore not initialized");

    if (!networkStateManager.isOnline()) {
      // Return offline cached updates
      return await getOfflineUpdates(limitCount);
    }

    const now = Timestamp.now();
    const q = query(
      collection(firestore, "updates"),
      where("expiresAt", ">", now),
      where("isVisible", "==", true),
      orderBy("expiresAt"),
      orderBy("timestamp", "desc"),
      limit(limitCount),
    );

    const querySnapshot = await getDocs(q);
    const updates: Update[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      const update = {
        id: doc.id,
        ...data,
        timestamp: data.timestamp?.toDate() || new Date(),
        expiresAt: data.expiresAt?.toDate() || new Date(),
      } as Update;
      updates.push(update);
    });

    // Cache updates offline for future use
    for (const update of updates) {
      await cacheUpdateOffline(update);
    }

    return updates;
  } catch (error) {
    // Fallback to offline data
    return await getOfflineUpdates(limitCount);
  }
};

/**
 * Get updates by user
 */
export const getUserUpdates = async (userId: string): Promise<Update[]> => {
  try {
    if (!firestore) throw new Error("Firestore not initialized");

    const now = Timestamp.now();
    const q = query(
      collection(firestore, "updates"),
      where("userId", "==", userId),
      where("expiresAt", ">", now),
      where("isVisible", "==", true),
      orderBy("expiresAt"),
      orderBy("timestamp", "desc"),
    );

    const querySnapshot = await getDocs(q);
    const updates: Update[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      updates.push({
        id: doc.id,
        ...data,
        timestamp: data.timestamp?.toDate() || new Date(),
        expiresAt: data.expiresAt?.toDate() || new Date(),
      } as Update);
    });

    return updates;
  } catch (error) {
    return [];
  }
};

/**
 * Toggle like on an update
 */
export const toggleLike = async (
  updateId: string,
  userId: string,
): Promise<boolean> => {
  try {
    if (!firestore) throw new Error("Firestore not initialized");

    const updateRef = doc(firestore, "updates", updateId);
    const updateSnapshot = await getDoc(updateRef);

    if (updateSnapshot.exists()) {
      const data = updateSnapshot.data();
      const likes = data.likes || [];
      const isLiked = likes.includes(userId);

      const { arrayUnion, arrayRemove } = await import("firebase/firestore");

      if (isLiked) {
        // Remove like
        await updateDoc(updateRef, {
          likes: arrayRemove(userId),
        });
        return false;
      } else {
        // Add like
        await updateDoc(updateRef, {
          likes: arrayUnion(userId),
        });
        return true;
      }
    }
    return false;
  } catch (error) {
    throw error;
  }
};

/**
 * Mark update as viewed
 */
export const markAsViewed = async (
  updateId: string,
  viewerId: string,
): Promise<void> => {
  try {
    if (!firestore) throw new Error("Firestore not initialized");

    const updateRef = doc(firestore, "updates", updateId);
    const { arrayUnion } = await import("firebase/firestore");

    await updateDoc(updateRef, {
      views: arrayUnion(viewerId),
    });

  } catch (error) {
    // Error marking as viewed - continue silently
  }
};

/**
 * Add comment to update
 */
export const addComment = async (
  updateId: string,
  comment: Omit<Comment, "id" | "timestamp">,
): Promise<string> => {
  try {
    const commentData = {
      ...comment,
      timestamp: serverTimestamp(),
      likesCount: 0,
      isVisible: true,
    };

    const docRef = await addDoc(
      collection(db, "updates", updateId, "comments"),
      commentData,
    );

    // Update comment count
    const updateRef = doc(db, "updates", updateId);
    const updateSnapshot = await getDoc(updateRef);
    if (updateSnapshot.exists()) {
      const currentComments = updateSnapshot.data().commentCount || 0;
      await updateDoc(updateRef, {
        commentCount: currentComments + 1,
      });
    }

    return docRef.id;
  } catch (error) {
    throw error;
  }
};

/**
 * Get comments for an update
 */
export const getUpdateComments = async (
  updateId: string,
): Promise<Comment[]> => {
  try {
    const q = query(
      collection(db, "updates", updateId, "comments"),
      where("isVisible", "==", true),
      orderBy("timestamp", "desc"),
    );

    const querySnapshot = await getDocs(q);
    const comments: Comment[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();
      comments.push({
        id: doc.id,
        ...data,
        timestamp: data.timestamp?.toDate() || new Date(),
      } as Comment);
    });

    return comments;
  } catch (error) {
    return [];
  }
};

/**
 * Delete an update
 */
export const deleteUpdate = async (updateId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, "updates", updateId), {
      isVisible: false,
    });
  } catch (error) {
    throw error;
  }
};

/**
 * Subscribe to updates in real-time
 */
export const subscribeToUpdates = (callback: (updates: Update[]) => void) => {
  try {
    if (!firestore) {
      console.warn("⚠️ Firestore not available for updates subscription");
      return () => {};
    }

    const now = Timestamp.now();
    const q = query(
      collection(firestore, "updates"),
      where("expiresAt", ">", now),
      where("isVisible", "==", true),
      orderBy("expiresAt"),
      orderBy("timestamp", "desc"),
      limit(50),
    );

    return onSnapshot(q, (querySnapshot) => {
      const updates: Update[] = [];
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        updates.push({
          id: doc.id,
          ...data,
          timestamp: data.timestamp?.toDate() || new Date(),
          expiresAt: data.expiresAt?.toDate() || new Date(),
        } as Update);
      });
      callback(updates);
    }, (error) => {
      console.error("❌ Error in updates subscription:", error);
      // Return empty array on error to prevent crashes
      callback([]);
    });
  } catch (error) {
    console.error("❌ Error setting up updates listener:", error);
    return () => {};
  }
};

/**
 * Clean up expired updates (should be run periodically)
 */
export const cleanupExpiredUpdates = async (): Promise<void> => {
  try {
    if (!firestore) throw new Error("Firestore not initialized");

    const now = Timestamp.now();
    const q = query(
      collection(firestore, "updates"),
      where("expiresAt", "<=", now),
    );

    const querySnapshot = await getDocs(q);
    const updatePromises: Promise<void>[] = [];

    querySnapshot.forEach((doc) => {
      // Mark as invisible instead of deleting
      updatePromises.push(updateDoc(doc.ref, { isVisible: false }));
    });

    await Promise.all(updatePromises);
  } catch (error) {
    // Error cleaning up - continue silently
  }
};

// Additional exports for UpdatesScreen compatibility
export const deleteMedia = async (
  updateId: string,
  mediaId: string,
): Promise<void> => {
  try {
    if (!firestore) throw new Error("Firestore not initialized");



    // Get the update document
    const updateRef = doc(firestore, "updates", updateId);
    const updateSnapshot = await getDoc(updateRef);

    if (!updateSnapshot.exists()) {
      throw new Error("Update not found");
    }

    const updateData = updateSnapshot.data();
    const media = updateData.media || [];

    // Remove the specific media item
    const updatedMedia = media.filter((m: any) => m.id !== mediaId);

    // Update the document
    await updateDoc(updateRef, {
      media: updatedMedia,
      updatedAt: serverTimestamp(),
    });

  } catch (error) {
    throw error;
  }
};

export const updateUpdateMedia = async (
  updateId: string,
  mediaUpdates: any,
): Promise<void> => {
  try {
    if (!firestore) throw new Error("Firestore not initialized");



    const updateRef = doc(firestore, "updates", updateId);
    await updateDoc(updateRef, {
      ...mediaUpdates,
      updatedAt: serverTimestamp(),
    });

  } catch (error) {
    throw error;
  }
};

// Legacy compatibility - keep the old likeUpdate function
export const likeUpdate = async (
  updateId: string,
  userId: string,
): Promise<void> => {
  await toggleLike(updateId, userId);
};


