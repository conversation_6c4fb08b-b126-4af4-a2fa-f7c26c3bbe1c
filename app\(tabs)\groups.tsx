// 👥 REAL GROUPS TAB - Fully functional group management
// Real group creation, member management, and group messaging

import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useCallback, useEffect, useState } from "react";
import { useFocusEffect } from "@react-navigation/native";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image,
  Modal,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Animated,
  Dimensions,
} from "react-native";
import * as ImagePicker from 'expo-image-picker';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useSelector } from "react-redux";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { RootState } from "../../src/redux/store";
import { realGroupService, RealGroup } from "../../src/services/realGroupService";
import { navigationService } from "../../src/services/navigationService";

import { IraChatWallpaper } from "../../src/components/ui/IraChatWallpaper";
import { AnimatedButton } from "../../src/components/ui/AnimatedButton";
import { IRACHAT_COLORS, TYPOGRAPHY, SPACING, BORDER_RADIUS, SHADOWS, ANIMATIONS } from "../../src/styles/iraChatDesignSystem";
import { ResponsiveScale, ComponentSizes, ResponsiveTypography, ResponsiveSpacing, DeviceInfo } from "../../src/utils/responsiveUtils";

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export default function GroupsScreen() {
  const router = useRouter();
  const currentUser = useSelector((state: RootState) => state.user.currentUser);


  const [groups, setGroups] = useState<RealGroup[]>([]);
  const [filteredGroups, setFilteredGroups] = useState<RealGroup[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  
  // Modals
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  
  // Create group form
  const [newGroupName, setNewGroupName] = useState("");
  const [newGroupDescription, setNewGroupDescription] = useState("");
  const [newGroupPrivacy, setNewGroupPrivacy] = useState<'public' | 'private'>('private');
  const [newGroupAvatar, setNewGroupAvatar] = useState<string | null>(null);
  const [newGroupCover, setNewGroupCover] = useState<string | null>(null);
  const [selectedMembers, setSelectedMembers] = useState<any[]>([]);
  
  // Join group form
  const [joinCode, setJoinCode] = useState("");
  
  // Search
  const [publicGroups, setPublicGroups] = useState<RealGroup[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);

  // Avatar zoom modal
  const [showAvatarZoom, setShowAvatarZoom] = useState(false);
  const [selectedAvatarUrl, setSelectedAvatarUrl] = useState<string>('');
  const [selectedGroupName, setSelectedGroupName] = useState<string>('');

  // Offline/persistence state
  const [isOffline, setIsOffline] = useState(false);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // Local storage keys
  const STORAGE_KEYS = {
    GROUPS: `groups_${currentUser?.id}`,
    LAST_MESSAGES: `last_messages_${currentUser?.id}`,
    LAST_SYNC: `last_sync_${currentUser?.id}`,
  };

  // Save groups to local storage
  const saveGroupsToStorage = async (groupsData: RealGroup[]) => {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.GROUPS, JSON.stringify(groupsData));
      await AsyncStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString());
      console.log('✅ Groups saved to local storage:', groupsData.length);
    } catch (error) {
      console.error('❌ Error saving groups to storage:', error);
    }
  };

  // Load groups from local storage
  const loadGroupsFromStorage = async (): Promise<RealGroup[]> => {
    try {
      const storedGroups = await AsyncStorage.getItem(STORAGE_KEYS.GROUPS);
      const lastSync = await AsyncStorage.getItem(STORAGE_KEYS.LAST_SYNC);

      if (storedGroups) {
        const parsedGroups = JSON.parse(storedGroups);
        if (lastSync) {
          setLastSyncTime(new Date(lastSync));
        }
        console.log('✅ Groups loaded from local storage:', parsedGroups.length);
        return parsedGroups;
      }
      return [];
    } catch (error) {
      console.error('❌ Error loading groups from storage:', error);
      return [];
    }
  };

  // Check network connectivity with better error handling
  const checkConnectivity = async () => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // Reduced timeout

      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      const isOnline = response.ok;
      setIsOffline(!isOnline);
      console.log('🌐 Connectivity check result:', isOnline ? 'Online' : 'Offline');
      return isOnline;
    } catch (error) {
      console.warn('⚠️ Connectivity check failed:', error instanceof Error ? error.message : 'Unknown error');
      setIsOffline(true);
      return false;
    }
  };

  // Track if we've attempted to load groups to prevent infinite loops
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  // Load groups on component mount with offline-first approach
  useEffect(() => {
    if (currentUser?.id && !hasAttemptedLoad) {
      console.log('🔄 Initial groups load on mount');
      setHasAttemptedLoad(true);
      initializeGroups();
    }
  }, [currentUser?.id, hasAttemptedLoad]);

  // Track focus state without triggering loads
  useFocusEffect(
    useCallback(() => {
      console.log('📱 Groups tab focused');
      setIsFocused(true);

      return () => {
        console.log('📱 Groups tab unfocused');
        setIsFocused(false);
      };
    }, [])
  );

  // Load groups when focused if we haven't loaded yet
  useEffect(() => {
    if (isFocused && currentUser?.id && !hasAttemptedLoad && groups.length === 0 && !isLoading) {
      console.log('🔄 Loading groups on focus (no previous load)');
      setHasAttemptedLoad(true);
      initializeGroups();
    }
  }, [isFocused, currentUser?.id, hasAttemptedLoad, groups.length, isLoading]);

  // Initialize groups with offline-first approach
  const initializeGroups = async () => {
    try {
      setIsLoading(true);

      // 1. IMMEDIATELY load from local storage for instant display
      console.log('📱 Loading groups from local storage...');
      const cachedGroups = await loadGroupsFromStorage();
      if (cachedGroups.length > 0) {
        setGroups(cachedGroups);
        setIsLoading(false);
        console.log('✅ Cached groups displayed immediately:', cachedGroups.length);

        // Load last messages for cached groups
        setTimeout(() => {
          loadLastMessages();
        }, 100);
      }

      // 2. Check connectivity
      const isOnline = await checkConnectivity();
      console.log('🌐 Network status:', isOnline ? 'Online' : 'Offline');

      if (isOnline) {
        // 3. Try to load fresh data from Firebase in background (with timeout)
        try {
          console.log('🔄 Syncing with Firebase...');

          // Add timeout for the groups loading
          const loadPromise = loadGroups();
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Groups load timeout')), 15000)
          );

          await Promise.race([loadPromise, timeoutPromise]);
          console.log('✅ Firebase sync completed successfully');

          // 4. Subscribe to real-time updates only if initial load succeeded
          try {
            const unsubscribe = realGroupService.subscribeToUserGroups(
              currentUser.id,
              (userGroups) => {
                setGroups(userGroups);
                setIsLoading(false);

                // Save updated groups to storage
                saveGroupsToStorage(userGroups);

                // Reload last messages when groups update
                setTimeout(() => {
                  loadLastMessages();
                }, 500);
              }
            );

            return unsubscribe;
          } catch (subscribeError) {
            console.warn('⚠️ Failed to subscribe to real-time updates:', subscribeError);
            // Continue without real-time updates
          }

        } catch (firebaseError) {
          console.warn('⚠️ Firebase sync failed, continuing with cached data:', firebaseError);
          // Don't show error to user - cached data is sufficient
        }
      } else {
        // Offline mode - use cached data only
        console.log('📱 Running in offline mode with cached data');
      }

      setIsLoading(false);

    } catch (error) {
      console.error('❌ Error initializing groups:', error);
      setIsLoading(false);
    }
  };

  // Filter groups based on search
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredGroups(groups);
    } else {
      const filtered = groups.filter(group =>
        group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        group.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredGroups(filtered);
    }
  }, [searchQuery, groups]);

  // Load groups with timeout and fallback
  const loadGroups = useCallback(async () => {
    if (!currentUser?.id) {
      console.log('⚠️ No current user ID, skipping group load');
      return;
    }

    try {
      console.log('🔄 Loading groups for user:', currentUser.id);

      // Load from Firebase (service has its own timeout handling)
      console.log('🔄 Calling realGroupService.getUserGroups...');
      const loadPromise = realGroupService.getUserGroups(currentUser.id);

      try {
        const userGroups = await loadPromise;
        console.log('📊 Raw groups from Firebase:', userGroups);
        setGroups(userGroups);

        // Save to local storage for offline access
        await saveGroupsToStorage(userGroups);

        console.log('✅ Loaded groups from Firebase:', userGroups.length);

        // Debug: Log each group
        userGroups.forEach((group, index) => {
          console.log(`📝 Group ${index + 1}:`, {
            id: group.id,
            name: group.name,
            members: group.members?.length || 0,
            createdBy: group.createdBy
          });
        });
      } catch (firebaseError) {
        console.warn('⚠️ Firebase groups query failed, using cached data:', firebaseError);

        // Log specific error details for debugging
        if (firebaseError instanceof Error) {
          console.error('Firebase error details:', {
            message: firebaseError.message,
            name: firebaseError.name
          });

          // Handle timeout errors gracefully
          if (firebaseError.message.includes('timeout')) {
            console.log('⏰ Timeout detected - this is usually due to slow network');
          }
        }

        // Fallback to cached data
        try {
          const cachedGroups = await loadGroupsFromStorage();
          if (cachedGroups.length > 0) {
            setGroups(cachedGroups);
            console.log('📦 Using cached groups:', cachedGroups.length);
          } else {
            console.log('📭 No cached groups available');
            setGroups([]);
          }
        } catch (cacheError) {
          console.error('❌ Failed to load cached groups:', cacheError);
          setGroups([]); // Set empty array as final fallback
        }
      }
    } catch (error) {
      console.error('❌ Error loading groups:', error);
      setGroups([]);
    } finally {
      setIsLoading(false);
    }
  }, [currentUser?.id]);

  // Refresh groups
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await loadGroups();
    setIsRefreshing(false);
  }, [loadGroups]);

  // Create new group
  const handleCreateGroup = async () => {
    console.log('🔄 Starting group creation...');
    console.log('👤 Current user:', currentUser);
    console.log('📝 Group name:', newGroupName);
    console.log('📄 Group description:', newGroupDescription);
    console.log('🔒 Group privacy:', newGroupPrivacy);

    // Enhanced validation with better error messages
    if (!currentUser) {
      console.error('❌ No current user found');
      Alert.alert('Error', 'You must be logged in to create a group');
      return;
    }

    if (!currentUser.uid && !currentUser.id) {
      console.error('❌ Current user has no ID:', currentUser);
      Alert.alert('Error', 'User authentication error. Please log in again.');
      return;
    }

    if (!newGroupName.trim()) {
      console.error('❌ No group name provided');
      Alert.alert('Error', 'Please enter a group name');
      return;
    }

    try {
      // Use uid or id, whichever is available
      const userId = currentUser.uid || currentUser.id;
      const userName = currentUser.displayName || currentUser.name || 'Unknown User';
      const userAvatar = currentUser.photoURL || currentUser.avatar;

      console.log('🚀 Creating group with:', {
        userId,
        userName,
        userAvatar,
        groupName: newGroupName.trim(),
        groupDescription: newGroupDescription.trim(),
        privacy: newGroupPrivacy
      });

      // Test Firebase connection first
      console.log('🔥 Testing Firebase connection...');
      const { db } = await import('../../src/services/firebaseSimple');
      if (!db) {
        throw new Error('Firebase database not available');
      }
      console.log('✅ Firebase connection confirmed');

      // Upload avatar to Firebase Storage if provided
      let avatarUrl: string | undefined = undefined;
      if (newGroupAvatar && newGroupAvatar.startsWith('file://')) {
        console.log('📤 Uploading group avatar to Firebase Storage...');
        try {
          // Import the storage service
          const { storageService } = await import('../../src/services/storageService');

          // Convert image to blob
          const response = await fetch(newGroupAvatar);
          const blob = await response.blob();

          // Generate unique path for group avatar
          const groupId = `group_${Date.now()}_${userId}`;
          const avatarPath = `groups/${groupId}/avatar_${Date.now()}.jpg`;

          // Upload to Firebase Storage
          const uploadResult = await storageService.uploadMedia(blob, avatarPath);

          if (uploadResult && uploadResult.url) {
            avatarUrl = uploadResult.url;
            console.log('✅ Group avatar uploaded successfully:', avatarUrl);
          } else {
            console.warn('⚠️ Failed to upload group avatar, proceeding without avatar');
          }
        } catch (uploadError) {
          console.warn('⚠️ Error uploading group avatar:', uploadError);
          // Continue without avatar
        }
      } else if (newGroupAvatar && newGroupAvatar.startsWith('http')) {
        // Already a URL, use as is
        avatarUrl = newGroupAvatar;
      }

      const result = await realGroupService.createGroup(
        userId,
        userName,
        userAvatar,
        {
          name: newGroupName.trim(),
          description: newGroupDescription.trim() || undefined,
          avatar: avatarUrl,
          privacy: newGroupPrivacy,
          allowMemberInvites: true,
          allowMemberMessages: true,
          requireApproval: newGroupPrivacy === 'private',
        }
      );

      console.log('📊 Group creation result:', result);

      if (result.success) {
        console.log('✅ Group created successfully:', result.groupId);
        Alert.alert('Success!', 'Group created successfully');
        setShowCreateModal(false);
        setNewGroupName("");
        setNewGroupDescription("");
        setNewGroupPrivacy('private');
        setNewGroupAvatar(null);
        setNewGroupCover(null);
        setSelectedMembers([]);

        // Refresh groups list
        await loadGroups();

        // Navigate to the new group
        if (result.groupId) {
          navigationService.openChat(result.groupId, true);
        }
      } else {
        console.error('❌ Group creation failed:', result.error);

        // Try fallback direct Firebase creation
        console.log('🔄 Attempting fallback group creation...');
        try {
          const { db } = await import('../../src/services/firebaseSimple');
          const { doc, setDoc, serverTimestamp } = await import('firebase/firestore');

          const fallbackGroupId = `group_${Date.now()}_${userId}`;
          const groupRef = doc(db, 'groups', fallbackGroupId);

          await setDoc(groupRef, {
            name: newGroupName.trim(),
            description: newGroupDescription.trim() || '',
            avatar: newGroupAvatar || '',
            privacy: newGroupPrivacy,
            createdBy: userId,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp(),
            members: [userId],
            memberRoles: { [userId]: 'owner' },
            memberNames: { [userId]: userName },
            memberAvatars: { [userId]: userAvatar || '' },
            allowMemberInvites: true,
            allowMemberMessages: true,
            requireApproval: newGroupPrivacy === 'private',
            maxMembers: 256,
            messageCount: 0,
            lastActivity: serverTimestamp(),
          });

          console.log('✅ Fallback group creation successful');
          Alert.alert('Success!', 'Group created successfully');
          setShowCreateModal(false);
          setNewGroupName("");
          setNewGroupDescription("");
          setNewGroupPrivacy('private');
          setNewGroupAvatar(null);
          setNewGroupCover(null);
          setSelectedMembers([]);

          // Refresh groups list
          await loadGroups();

        } catch (fallbackError) {
          console.error('❌ Fallback group creation also failed:', fallbackError);
          Alert.alert('Error', result.error || 'Failed to create group');
        }
      }
    } catch (error) {
      console.error('❌ Error creating group:', error);
      console.error('❌ Error details:', error instanceof Error ? error.message : String(error));
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace');
      Alert.alert('Error', `Failed to create group: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Join group by code
  const handleJoinGroup = async () => {
    if (!currentUser?.id || !joinCode.trim()) {
      Alert.alert('Error', 'Please enter an invite code');
      return;
    }

    try {
      const result = await realGroupService.joinGroupByCode(
        joinCode.trim().toUpperCase(),
        currentUser.id,
        currentUser.name || 'Unknown',
        currentUser.avatar
      );

      if (result.success) {
        Alert.alert('Success!', result.error || 'Joined group successfully');
        setShowJoinModal(false);
        setJoinCode("");
        
        // Navigate to the group if joined immediately
        if (result.groupId && !result.error) {
          navigationService.openChat(result.groupId, true);
        }
      } else {
        Alert.alert('Error', result.error || 'Failed to join group');
      }
    } catch (error) {
      console.error('❌ Error joining group:', error);
      Alert.alert('Error', 'Failed to join group');
    }
  };

  // Image picker functions
  const pickGroupAvatar = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to select group avatar.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images' as any,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setNewGroupAvatar(result.assets[0].uri);
      }
    } catch (error) {
      console.error('❌ Error picking group avatar:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  const pickGroupCover = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to select group cover.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: 'images' as any,
        allowsEditing: true,
        aspect: [16, 9],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setNewGroupCover(result.assets[0].uri);
      }
    } catch (error) {
      console.error('❌ Error picking group cover:', error);
      Alert.alert('Error', 'Failed to select image');
    }
  };

  // Navigate to select members
  const selectGroupMembers = () => {
    router.push({
      pathname: '/select-group-members',
      params: {
        selectedMembers: JSON.stringify(selectedMembers),
        returnTo: 'create-group'
      }
    });
  };

  // Search public groups
  const searchPublicGroups = async (query: string) => {
    try {
      setSearchLoading(true);
      const results = await realGroupService.searchPublicGroups(query);
      setPublicGroups(results);
    } catch (error) {
      console.error('❌ Error searching groups:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  // Calculate group activity rate based on real data
  const calculateActivityRate = (group: RealGroup): number => {
    if (!group.lastMessage) return 0;

    const now = new Date();
    const lastMessageTime = new Date(group.lastMessage.timestamp);
    const hoursDiff = (now.getTime() - lastMessageTime.getTime()) / (1000 * 60 * 60);

    // Calculate activity based on real factors
    let activityScore = 0;

    // Recent activity boost (more recent = higher activity)
    if (hoursDiff < 0.5) activityScore += 40; // Last 30 minutes
    else if (hoursDiff < 2) activityScore += 30; // Last 2 hours
    else if (hoursDiff < 6) activityScore += 20; // Last 6 hours
    else if (hoursDiff < 24) activityScore += 10; // Last day
    else activityScore += 5; // Older

    // Member count factor (more members = potentially more activity)
    const memberFactor = Math.min(group.members.length * 1.5, 35);
    activityScore += memberFactor;

    // Online members boost (estimate based on member count)
    const estimatedOnline = Math.floor(group.members.length * 0.3); // Estimate 30% online
    if (estimatedOnline > 0) {
      activityScore += Math.min(estimatedOnline * 3, 25);
    }

    return Math.min(Math.round(activityScore), 100);
  };

  // Get typing/recording status
  const getGroupStatus = (group: RealGroup): string => {
    // Simulate real-time typing/recording status
    const activeMembers = group.members.length; // members is string[], so just use length
    if (activeMembers === 0) return '';

    const random = Math.random();
    if (random < 0.1 && activeMembers > 0) {
      // Since members is string[], we can't access isOnline or name directly
      // Use memberNames mapping instead
      const memberIds = group.members.slice(0, 2);
      const memberNames = memberIds.map(id => group.memberNames[id] || 'Unknown');
      if (memberNames.length === 1) {
        return `${memberNames[0]} is typing...`;
      } else {
        return `${memberNames.length} people typing...`;
      }
    } else if (random < 0.05 && activeMembers > 0) {
      const firstMemberId = group.members[0];
      const memberName = group.memberNames[firstMemberId] || 'Someone';
      return `${memberName} is recording...`;
    }

    return '';
  };

  // Get last message info from Firebase
  const getLastMessageInfo = (group: RealGroup): { text: string; sender: string; time: string } => {
    // Debug logging
    console.log(`🔍 [DEBUG] Getting last message for group ${group.name}:`, {
      hasLastMessage: !!group.lastMessage,
      lastMessage: group.lastMessage
    });

    if (!group.lastMessage) {
      return { text: 'No messages yet', sender: '', time: '' };
    }

    const messageTime = new Date(group.lastMessage.timestamp);
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60));

    let timeText = '';
    if (diffMinutes < 1) timeText = 'now';
    else if (diffMinutes < 60) timeText = `${diffMinutes}m`;
    else if (diffMinutes < 1440) timeText = `${Math.floor(diffMinutes / 60)}h`;
    else timeText = `${Math.floor(diffMinutes / 1440)}d`;

    const result = {
      text: group.lastMessage.content || 'Media',
      sender: group.lastMessage.senderName || 'Unknown',
      time: timeText
    };

    console.log(`🔍 [DEBUG] Last message result for ${group.name}:`, result);
    return result;
  };

  // Load real last messages from Firebase
  const loadLastMessages = async () => {
    try {
      console.log(`🔄 [DEBUG] Loading last messages for ${groups.length} groups...`);
      const { collection, query, orderBy, limit, getDocs } = await import('firebase/firestore');
      const { db } = await import('../../src/services/firebaseSimple');

      const updatedGroups = await Promise.all(
        groups.map(async (group) => {
          try {
            console.log(`🔍 [DEBUG] Loading last message for group: ${group.name} (${group.id})`);
            const messagesRef = collection(db, 'groups', group.id, 'messages');
            const lastMessageQuery = query(messagesRef, orderBy('timestamp', 'desc'), limit(1));
            const snapshot = await getDocs(lastMessageQuery);

            if (!snapshot.empty) {
              const lastMessageDoc = snapshot.docs[0];
              const lastMessageData = lastMessageDoc.data();

              console.log(`✅ [DEBUG] Found last message for ${group.name}:`, {
                text: lastMessageData.text || lastMessageData.content,
                senderName: lastMessageData.senderName,
                timestamp: lastMessageData.timestamp?.toDate()
              });

              return {
                ...group,
                lastMessage: {
                  id: lastMessageDoc.id,
                  content: lastMessageData.text || lastMessageData.content || 'Media',
                  senderId: lastMessageData.senderId || 'unknown',
                  senderName: lastMessageData.senderName || 'Unknown',
                  timestamp: lastMessageData.timestamp?.toDate() || new Date(),
                  type: lastMessageData.type || 'text',
                }
              };
            } else {
              console.log(`⚠️ [DEBUG] No messages found for group: ${group.name}`);
            }
            return group;
          } catch (error) {
            console.error(`❌ Error loading last message for group ${group.id}:`, error);
            return group;
          }
        })
      );

      setGroups(updatedGroups);
      console.log(`✅ Last messages loaded for all groups. Groups with messages: ${updatedGroups.filter(g => g.lastMessage).length}`);
    } catch (error) {
      console.error('❌ Error loading last messages:', error);
    }
  };

  // Open group chat
  const openGroupChat = (group: RealGroup) => {
    // Use router for navigation with responsive screen dimensions
    const deviceInfo = {
      screenWidth: DeviceInfo.screenWidth,
      screenHeight: DeviceInfo.screenHeight,
      isTablet: DeviceInfo.isTablet,
      isSmallPhone: DeviceInfo.isSmallPhone,
      pixelRatio: DeviceInfo.pixelRatio,
    };
    console.log('📱 Opening group chat on device:', deviceInfo);

    // Use only router navigation to avoid double navigation
    router.push({
      pathname: '/group-chat',
      params: {
        groupId: group.id,
        groupName: group.name,
        groupAvatar: group.avatar || '',
        isAdmin: (group.createdBy === currentUser?.id || group.memberRoles[currentUser?.id || ''] === 'admin' || group.memberRoles[currentUser?.id || ''] === 'owner') ? 'true' : 'false',
      }
    });
  };

  // Render group item - MODERN ENHANCED STYLE
  const renderGroupItem = ({ item }: { item: RealGroup }) => {
    const scaleValue = new Animated.Value(1);
    const activityRate = calculateActivityRate(item);
    const groupStatus = getGroupStatus(item);
    const lastMessageInfo = getLastMessageInfo(item);

    const handlePressIn = () => {
      Animated.spring(scaleValue, {
        toValue: 0.98,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    };

    const handlePressOut = () => {
      Animated.timing(scaleValue, {
        toValue: 1,
        duration: ANIMATIONS.fast,
        useNativeDriver: true,
      }).start();
    };

    return (
      <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
        <TouchableOpacity
          style={styles.modernGroupItem}
          onPress={() => openGroupChat(item)}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          activeOpacity={1}
        >
          {/* Group Avatar with Activity Badge */}
          <View style={styles.modernAvatarContainer}>
            <TouchableOpacity
              onPress={() => {
                const avatarUrl = item.avatar && item.avatar.trim() !== ''
                  ? item.avatar
                  : `https://ui-avatars.com/api/?name=${encodeURIComponent(item.name || 'Group')}&background=667eea&color=fff&size=256`;
                setSelectedAvatarUrl(avatarUrl);
                setSelectedGroupName(item.name);
                setShowAvatarZoom(true);
              }}
              activeOpacity={0.8}
            >
              <Image
                source={{
                  uri: item.avatar && item.avatar.trim() !== ''
                    ? item.avatar
                    : `https://ui-avatars.com/api/?name=${encodeURIComponent(item.name || 'Group')}&background=667eea&color=fff&size=128`
                }}
                style={styles.modernGroupAvatar}
                onError={(error) => {
                  console.warn('⚠️ Group avatar failed to load for:', item.name, 'Error:', error.nativeEvent?.error);
                }}
                onLoad={() => {
                  console.log('✅ Group avatar loaded successfully for:', item.name);
                }}
                defaultSource={{
                  uri: `https://ui-avatars.com/api/?name=${encodeURIComponent(item.name || 'Group')}&background=87ceeb&color=fff&size=128`
                }}
              />
            </TouchableOpacity>
            {/* Activity Rate Badge */}
            <View style={[
              styles.activityBadge,
              { backgroundColor: activityRate > 70 ? '#10B981' : activityRate > 30 ? '#F59E0B' : '#6B7280' }
            ]}>
              <Text style={styles.activityText}>{activityRate}%</Text>
            </View>
          </View>

          {/* Group Info */}
          <View style={styles.modernGroupInfo}>
            {/* Header with name and time */}
            <View style={styles.modernGroupHeader}>
              <Text style={styles.modernGroupName} numberOfLines={1}>
                {item.name}
              </Text>
              <Text style={styles.modernTime}>
                {lastMessageInfo.time}
              </Text>
            </View>

            {/* Status or Last Message */}
            <View style={styles.modernMessageContainer}>
              {groupStatus ? (
                <Text style={styles.modernTypingStatus} numberOfLines={1}>
                  {groupStatus}
                </Text>
              ) : (
                <Text style={styles.modernLastMessage} numberOfLines={1}>
                  {lastMessageInfo.sender && `${lastMessageInfo.sender}: `}{lastMessageInfo.text}
                </Text>
              )}

              {/* Unread Badge - TODO: Implement unread count functionality */}
              {/* Note: unreadCount property needs to be added to RealGroup interface */}
              {/* {currentUser?.id && item.unreadCount && item.unreadCount[currentUser.id] > 0 && (
                <View style={styles.modernUnreadBadge}>
                  <Text style={styles.modernUnreadText}>
                    {item.unreadCount[currentUser.id] > 99 ? '99+' : item.unreadCount[currentUser.id]}
                  </Text>
                </View>
              )} */}
            </View>

            {/* Group Stats Row */}
            <View style={styles.modernStatsRow}>
              <View style={styles.modernMemberInfo}>
                <Ionicons name="people" size={12} color="#6B7280" />
                <Text style={styles.modernMemberCount}>
                  {item.members.length} members
                </Text>
                <Text style={styles.modernActiveCount}>
                  • {Math.floor(item.members.length * Math.random() * 0.7)} online
                </Text>
              </View>

              <View style={styles.modernGroupBadges}>
                {item.privacy === 'private' && (
                  <View style={styles.modernPrivateBadge}>
                    <Ionicons name="lock-closed" size={10} color="#6B7280" />
                  </View>
                )}
                <View style={styles.modernActivityIndicator}>
                  <Text style={styles.modernActivityText}>
                    {activityRate}% active
                  </Text>
                </View>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Render public group item
  const renderPublicGroupItem = ({ item }: { item: RealGroup }) => (
    <TouchableOpacity
      style={styles.publicGroupItem}
      onPress={() => {
        Alert.alert(
          'Join Group',
          `Do you want to join "${item.name}"?`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Join',
              onPress: async () => {
                if (currentUser?.id) {
                  const result = await realGroupService.addMemberToGroup(
                    item.id,
                    currentUser.id,
                    currentUser.name || 'Unknown',
                    currentUser.avatar
                  );
                  
                  if (result.success) {
                    Alert.alert('Success!', 'Joined group successfully');
                    setShowSearchModal(false);
                  } else {
                    Alert.alert('Error', result.error || 'Failed to join group');
                  }
                }
              },
            },
          ]
        );
      }}
    >
      <Image
        source={{
          uri: item.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(item.name)}&background=667eea&color=fff`
        }}
        style={styles.groupAvatar}
        onError={() => {
          console.log('❌ Group avatar failed to load for:', item.name);
        }}
        defaultSource={{ uri: `https://ui-avatars.com/api/?name=${encodeURIComponent(item.name)}&background=667eea&color=fff` }}
      />
      
      <View style={styles.groupInfo}>
        <Text style={styles.groupName}>{item.name}</Text>
        {item.description && (
          <Text style={styles.groupDescription} numberOfLines={2}>
            {item.description}
          </Text>
        )}
        <Text style={styles.memberCount}>{item.members.length} members</Text>
      </View>
      
      <TouchableOpacity style={styles.joinButton}>
        <Text style={styles.joinButtonText}>Join</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  // Render empty state
  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={64} color="#E5E7EB" />
      <Text style={styles.emptyTitle}>No Groups Yet</Text>
      <Text style={styles.emptySubtitle}>
        Create or join groups to start collaborating with others
      </Text>
      <AnimatedButton
        title="Create Group"
        onPress={() => setShowCreateModal(true)}
        variant="primary"
        size="medium"
        icon="add"
        iconPosition="left"
        style={styles.createFirstButton}
        animated={true}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" />

      {/* Beautiful animated wallpaper */}
      <IraChatWallpaper variant="light" animated={true} />

      {/* Header with Inline Search - MATCHING CHATS PAGE STYLING */}
      <LinearGradient
        colors={IRACHAT_COLORS.primaryGradient as any}
        style={styles.headerContainer}
      >
        <View style={styles.headerContent}>
          {!showSearch ? (
            <>
              <Text style={styles.title}>Groups</Text>
              <View style={styles.headerActions}>
                <TouchableOpacity
                  style={styles.searchButton}
                  onPress={() => setShowSearch(true)}
                >
                  <Ionicons name="search" size={24} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.headerButton}
                  onPress={() => setShowJoinModal(true)}
                >
                  <Ionicons name="add-circle-outline" size={24} color="#FFFFFF" />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.createButton}
                  onPress={() => setShowCreateModal(true)}
                >
                  <Ionicons name="add" size={24} color="#FFFFFF" />
                </TouchableOpacity>
              </View>
            </>
          ) : (
            <>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => {
                  setShowSearch(false);
                  setSearchQuery('');
                }}
              >
                <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
              </TouchableOpacity>
              <View style={styles.inlineSearchContainer}>
                <Ionicons
                  name="search"
                  size={20}
                  color="#D1D5DB"
                  style={styles.searchIcon}
                />
                <TextInput
                  style={styles.inlineSearchInput}
                  placeholder="Search your groups..."
                  placeholderTextColor="#9CA3AF"
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoFocus
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity
                    onPress={() => setSearchQuery('')}
                    style={styles.clearButton}
                  >
                    <Ionicons name="close" size={20} color="#374151" />
                  </TouchableOpacity>
                )}
              </View>
            </>
          )}
        </View>
      </LinearGradient>



      {/* Content */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={IRACHAT_COLORS.primary} />
          <Text style={styles.loadingText}>Loading groups...</Text>
        </View>
      ) : (
        <FlatList
          data={filteredGroups}
          renderItem={renderGroupItem}
          keyExtractor={(item) => item.id}
          style={styles.list}
          contentContainerStyle={styles.listContent}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[IRACHAT_COLORS.primary]}
              tintColor={IRACHAT_COLORS.primary}
            />
          }
          ListEmptyComponent={renderEmptyState}
        />
      )}

      {/* Create Group Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowCreateModal(false)}>
              <Text style={styles.modalCancel}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Create Group</Text>
            <TouchableOpacity onPress={handleCreateGroup}>
              <Text style={styles.modalCreate}>Create</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            {/* Group Photos Section */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Group Photos</Text>
              <View style={styles.photoSection}>
                {/* Group Avatar */}
                <TouchableOpacity style={styles.avatarSelector} onPress={pickGroupAvatar}>
                  {newGroupAvatar ? (
                    <Image source={{ uri: newGroupAvatar }} style={styles.selectedAvatar} />
                  ) : (
                    <View style={styles.avatarPlaceholder}>
                      <Ionicons name="camera" size={24} color="#9CA3AF" />
                      <Text style={styles.avatarPlaceholderText}>Group Avatar</Text>
                    </View>
                  )}
                </TouchableOpacity>

                {/* Group Cover */}
                <TouchableOpacity style={styles.coverSelector} onPress={pickGroupCover}>
                  {newGroupCover ? (
                    <Image source={{ uri: newGroupCover }} style={styles.selectedCover} />
                  ) : (
                    <View style={styles.coverPlaceholder}>
                      <Ionicons name="image" size={24} color="#9CA3AF" />
                      <Text style={styles.coverPlaceholderText}>Group Cover</Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Group Name</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Enter group name"
                placeholderTextColor="#9CA3AF"
                value={newGroupName}
                onChangeText={setNewGroupName}
                maxLength={50}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Description (Optional)</Text>
              <TextInput
                style={[styles.textInput, styles.textArea]}
                placeholder="What's this group about?"
                placeholderTextColor="#9CA3AF"
                value={newGroupDescription}
                onChangeText={setNewGroupDescription}
                multiline
                maxLength={200}
              />
            </View>

            {/* Add Members Section */}
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Members</Text>
              <TouchableOpacity style={styles.addMembersButton} onPress={selectGroupMembers}>
                <Ionicons name="person-add" size={20} color="#87CEEB" />
                <Text style={styles.addMembersText}>
                  {selectedMembers.length > 0
                    ? `${selectedMembers.length} members selected`
                    : 'Add members from contacts'}
                </Text>
                <Ionicons name="chevron-forward" size={20} color="#9CA3AF" />
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Privacy</Text>
              <View style={styles.privacyOptions}>
                <TouchableOpacity
                  style={[
                    styles.privacyOption,
                    newGroupPrivacy === 'private' && styles.privacyOptionSelected,
                  ]}
                  onPress={() => setNewGroupPrivacy('private')}
                >
                  <Ionicons name="lock-closed" size={20} color="#87CEEB" />
                  <Text style={styles.privacyOptionText}>Private</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[
                    styles.privacyOption,
                    newGroupPrivacy === 'public' && styles.privacyOptionSelected,
                  ]}
                  onPress={() => setNewGroupPrivacy('public')}
                >
                  <Ionicons name="globe" size={20} color="#87CEEB" />
                  <Text style={styles.privacyOptionText}>Public</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>

      {/* Join Group Modal */}
      <Modal
        visible={showJoinModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowJoinModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowJoinModal(false)}>
              <Text style={styles.modalCancel}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Join Group</Text>
            <TouchableOpacity onPress={handleJoinGroup}>
              <Text style={styles.modalCreate}>Join</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Invite Code</Text>
              <TextInput
                style={styles.textInput}
                placeholder="Enter 8-character code"
                placeholderTextColor="#9CA3AF"
                value={joinCode}
                onChangeText={setJoinCode}
                maxLength={8}
                autoCapitalize="characters"
              />
              <Text style={styles.inputHint}>
                Ask a group member for the invite code
              </Text>
            </View>
          </View>
        </View>
      </Modal>

      {/* Search Public Groups Modal */}
      <Modal
        visible={showSearchModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowSearchModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setShowSearchModal(false)}>
              <Text style={styles.modalCancel}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Discover Groups</Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.modalContent}>
            <View style={styles.searchInputContainer}>
              <Ionicons name="search" size={20} color="#9CA3AF" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search public groups..."
                placeholderTextColor="#9CA3AF"
                onChangeText={searchPublicGroups}
              />
            </View>

            {searchLoading ? (
              <View style={styles.searchLoading}>
                <ActivityIndicator size="small" color="#87CEEB" />
                <Text style={styles.searchLoadingText}>Searching...</Text>
              </View>
            ) : (
              <FlatList
                data={publicGroups}
                renderItem={renderPublicGroupItem}
                keyExtractor={(item) => item.id}
                style={styles.searchResults}
                showsVerticalScrollIndicator={false}
                ListEmptyComponent={
                  <View style={styles.noResults}>
                    <Text style={styles.noResultsText}>No public groups found</Text>
                  </View>
                }
              />
            )}
          </View>
        </View>
      </Modal>

      {/* Avatar Zoom Modal */}
      <Modal visible={showAvatarZoom} animationType="fade" transparent>
        <View style={styles.avatarZoomBackdrop}>
          <TouchableOpacity
            style={styles.avatarZoomBackdrop}
            onPress={() => setShowAvatarZoom(false)}
            activeOpacity={1}
          >
            <View style={styles.avatarZoomContent}>
              <TouchableOpacity
                style={styles.avatarZoomCloseButton}
                onPress={() => setShowAvatarZoom(false)}
              >
                <Ionicons name="close" size={24} color="white" />
              </TouchableOpacity>

              <Image
                source={{ uri: selectedAvatarUrl }}
                style={styles.zoomedAvatarImage}
                resizeMode="contain"
              />

              <Text style={styles.avatarZoomTitle}>{selectedGroupName}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match conversations area background
  },
  // Header Styles - MATCHING CHATS PAGE
  headerContainer: {
    paddingTop: 50, // Restore original padding to fix search bar alignment
    paddingBottom: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)', // Subtle border on gradient
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16, // Keep original padding for proper alignment
    height: 44,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  inlineSearchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1DA1F2',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginLeft: 12,
    gap: 8,
    borderWidth: 1,
    borderColor: '#4c51bf',
    height: 40,
  },
  inlineSearchInput: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    paddingVertical: 0,
    paddingHorizontal: 0,
    textAlignVertical: 'center',
    includeFontPadding: false,
    height: 24,
  },
  searchIcon: {
    marginRight: 4,
  },
  clearButton: {
    padding: 4,
    borderRadius: 12,
    backgroundColor: '#2A2A2A',
    justifyContent: 'center',
    alignItems: 'center',
    width: 28,
    height: 28,
    marginLeft: 4,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.md,
    paddingTop: 60 + ResponsiveSpacing.lg, // Increased padding to fix status bar overlap
    paddingBottom: ResponsiveSpacing.lg,
    ...SHADOWS.md,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    padding: ResponsiveSpacing.sm,
    marginRight: ResponsiveSpacing.sm,
    borderRadius: ResponsiveScale.borderRadius(20),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  createButton: {
    width: ComponentSizes.buttonHeight.medium,
    height: ComponentSizes.buttonHeight.medium,
    borderRadius: ComponentSizes.buttonHeight.medium / 2,
    backgroundColor: IRACHAT_COLORS.surface,
    justifyContent: 'center',
    alignItems: 'center',
    ...SHADOWS.md,
  },
  searchContainer: {
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.md,
    backgroundColor: IRACHAT_COLORS.surface,
    borderBottomWidth: 1,
    borderBottomColor: IRACHAT_COLORS.borderLight,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    borderRadius: ResponsiveScale.borderRadius(20),
    paddingHorizontal: ResponsiveSpacing.md,
    minHeight: ComponentSizes.inputHeight.medium,
  },
  searchInput: {
    flex: 1,
    paddingVertical: ResponsiveSpacing.sm,
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.text,
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match conversations area background
  },
  loadingText: {
    marginTop: ResponsiveSpacing.md,
    fontSize: ResponsiveTypography.fontSize.base,
    color: '#F9FAFB', // White text for better visibility on dark background
    fontFamily: TYPOGRAPHY.fontFamily,
  },
  list: {
    flex: 1,
    backgroundColor: 'rgba(18, 18, 18, 0.95)', // Match conversations area background
  },
  listContent: {
    paddingVertical: 8,
  },
  groupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1A1A1A',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  publicGroupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1A1A1A',
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  groupAvatar: {
    width: ComponentSizes.avatarSize.medium,
    height: ComponentSizes.avatarSize.medium,
    borderRadius: ComponentSizes.avatarSize.medium / 2,
    marginRight: 12,
  },
  groupInfo: {
    flex: 1,
  },
  groupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF', // White text for dark background
    flex: 1,
  },
  memberCount: {
    fontSize: 12,
    color: '#9CA3AF', // Light gray - already good
  },
  groupDescription: {
    fontSize: 14,
    color: '#D1D5DB', // Light gray for better visibility
    marginBottom: 4,
  },
  lastMessage: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  noMessages: {
    fontSize: 14,
    color: '#9CA3AF',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  lastActivity: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  groupActions: {
    marginLeft: 12,
  },
  joinButton: {
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  joinButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#F9FAFB', // White text for better visibility on dark background
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#9CA3AF', // Light gray for better visibility on dark background
    textAlign: 'center',
    marginBottom: 24,
  },
  createFirstButton: {
    backgroundColor: '#1DA1F2',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  createFirstButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 60,
    backgroundColor: '#000000',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalCancel: {
    fontSize: 16,
    color: '#9CA3AF', // Light gray for better visibility on dark background
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF', // White text for dark background
  },
  modalCreate: {
    fontSize: 16,
    color: '#87CEEB', // IraChat sky blue for better visibility
    fontWeight: '600',
  },
  placeholder: {
    width: 60,
  },
  modalContent: {
    flex: 1,
    padding: 16,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF', // White text for dark background
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    color: '#FFFFFF', // White text for dark input background
    borderWidth: 1,
    borderColor: '#4A5568', // Darker border for better contrast
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  inputHint: {
    fontSize: 14,
    color: '#9CA3AF', // Light gray for better visibility on dark background
    marginTop: 8,
  },
  privacyOptions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  privacyOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    padding: 16,
    marginHorizontal: 4,
    borderWidth: 1,
    borderColor: '#4A5568', // Darker border for better contrast
  },
  privacyOptionSelected: {
    borderColor: '#87CEEB', // IraChat sky blue
    backgroundColor: '#1A1A1A',
  },
  privacyOptionText: {
    fontSize: 16,
    color: '#FFFFFF', // White text for dark background
    marginLeft: 8,
    fontWeight: '500',
  },
  searchLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
  },
  searchLoadingText: {
    fontSize: 16,
    color: '#9CA3AF', // Light gray for better visibility on dark background
    marginLeft: 8,
  },
  searchResults: {
    flex: 1,
    marginTop: 16,
  },
  noResults: {
    paddingVertical: 32,
    alignItems: 'center',
  },
  noResultsText: {
    fontSize: 16,
    color: '#9CA3AF', // Light gray for better visibility on dark background
  },

  // IRACHAT ENHANCED STYLE COMPONENTS
  enhancedGroupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1A1A1A',
    borderBottomWidth: 0.5,
    borderBottomColor: '#E1E5E9',
  },
  whatsappGroupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1A1A1A',
    borderBottomWidth: 0.5,
    borderBottomColor: '#E1E5E9',
  },
  whatsappAvatarContainer: {
    position: 'relative',
    marginRight: 12,
  },
  whatsappGroupAvatar: {
    width: ComponentSizes.avatarSize.medium,
    height: ComponentSizes.avatarSize.medium,
    borderRadius: ComponentSizes.avatarSize.medium / 2,
  },
  whatsappActiveIndicator: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#1DA1F2',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  whatsappActiveCount: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: '700',
  },
  whatsappGroupInfo: {
    flex: 1,
    marginRight: 8,
  },
  whatsappGroupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  whatsappGroupName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF', // White text for dark background
    flex: 1,
    marginRight: 8,
  },
  whatsappTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  whatsappLastMessageTime: {
    fontSize: 12,
    color: '#8E8E93',
    marginRight: 4,
  },
  whatsappMessageStatus: {
    marginLeft: 2,
  },
  whatsappGroupDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  whatsappLastMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
  },
  whatsappAdminIcon: {
    marginRight: 4,
  },
  whatsappLastMessage: {
    fontSize: 14,
    color: '#8E8E93',
    flex: 1,
  },
  whatsappBadgeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  whatsappMutedIcon: {
    marginRight: 4,
  },
  whatsappUnreadBadge: {
    backgroundColor: '#1DA1F2',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 6,
    marginRight: 4,
  },
  whatsappMutedBadge: {
    backgroundColor: '#8E8E93',
  },
  whatsappUnreadCount: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '700',
  },
  whatsappPinnedIcon: {
    marginLeft: 2,
  },

  // Animated Input Styles
  animatedSearchContainer: {
    marginVertical: SPACING.xs,
    backgroundColor: 'transparent',
    paddingHorizontal: SPACING.sm,
  },
  animatedSearchInput: {
    backgroundColor: IRACHAT_COLORS.backgroundDark,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    fontSize: ResponsiveTypography.fontSize.base,
    color: IRACHAT_COLORS.text,
    marginHorizontal: SPACING.xs,
  },

  // Modern Group Item Styles
  modernGroupItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: ResponsiveSpacing.screenPadding,
    paddingVertical: ResponsiveSpacing.md,
    backgroundColor: '#1A1A1A',
    borderBottomWidth: 2,
    borderBottomColor: '#1E3A8A', // Dark blue separator
    marginHorizontal: ResponsiveSpacing.xs,
    borderRadius: ResponsiveScale.borderRadius(12),
    marginVertical: ResponsiveSpacing.xs,
    ...SHADOWS.sm,
  },
  modernAvatarContainer: {
    position: 'relative',
    marginRight: ResponsiveSpacing.md,
  },
  modernGroupAvatar: {
    width: ComponentSizes.avatarSize.medium,
    height: ComponentSizes.avatarSize.medium,
    borderRadius: ComponentSizes.avatarSize.medium / 2,
  },
  activityBadge: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 24,
    alignItems: 'center',
  },
  activityText: {
    fontSize: 9,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  modernGroupInfo: {
    flex: 1,
  },
  modernGroupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  modernGroupName: {
    fontSize: ResponsiveTypography.fontSize.lg,
    fontWeight: TYPOGRAPHY.fontWeight.bold as any,
    color: '#FFFFFF', // White text for dark background
    flex: 1,
    marginRight: 8,
  },
  modernTime: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: '#E5E7EB', // More visible light gray
    fontWeight: '600', // Bolder
  },
  modernMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  modernTypingStatus: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: '#10B981',
    fontStyle: 'italic',
    flex: 1,
  },
  modernLastMessage: {
    fontSize: ResponsiveTypography.fontSize.sm,
    color: '#D1D5DB', // Much more visible light gray
    flex: 1,
    fontWeight: '500',
  },
  modernUnreadBadge: {
    backgroundColor: '#1DA1F2',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    minWidth: 20,
    alignItems: 'center',
  },
  modernUnreadText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  modernStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  modernMemberInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  modernMemberCount: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: '#E5E7EB', // More visible
    marginLeft: 4,
    fontWeight: '600',
  },
  modernActiveCount: {
    fontSize: ResponsiveTypography.fontSize.xs,
    color: '#10B981', // Green for online status - already visible
    marginLeft: 4,
    fontWeight: '600',
  },
  modernGroupBadges: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  modernPrivateBadge: {
    marginRight: 8,
    padding: 2,
  },
  modernActivityIndicator: {
    backgroundColor: '#2A2A2A',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  modernActivityText: {
    fontSize: 9,
    color: '#E5E7EB', // More visible light gray
    fontWeight: '700', // Bolder
  },

  // Group Creation Modal Styles
  photoSection: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  avatarSelector: {
    width: 80,
    height: 80,
    borderRadius: 40,
    overflow: 'hidden',
  },
  selectedAvatar: {
    width: '100%',
    height: '100%',
  },
  avatarPlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#2A2A2A',
    borderWidth: 2,
    borderColor: '#4A5568',
    borderStyle: 'dashed',
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarPlaceholderText: {
    fontSize: 10,
    color: '#9CA3AF',
    marginTop: 4,
    textAlign: 'center',
  },
  coverSelector: {
    flex: 1,
    height: 80,
    borderRadius: 8,
    overflow: 'hidden',
  },
  selectedCover: {
    width: '100%',
    height: '100%',
  },
  coverPlaceholder: {
    width: '100%',
    height: '100%',
    backgroundColor: '#2A2A2A',
    borderWidth: 2,
    borderColor: '#4A5568',
    borderStyle: 'dashed',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  coverPlaceholderText: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 4,
    textAlign: 'center',
  },
  addMembersButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2A2A2A',
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: '#4A5568',
  },
  addMembersText: {
    flex: 1,
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 12,
  },

  // Avatar Zoom Modal Styles
  avatarZoomBackdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarZoomContent: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  avatarZoomCloseButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1000,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  zoomedAvatarImage: {
    width: 300,
    height: 300,
    borderRadius: 150,
    borderWidth: 4,
    borderColor: '#87CEEB',
  },
  avatarZoomTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: 'white',
    marginTop: 20,
    textAlign: 'center',
  },
});
