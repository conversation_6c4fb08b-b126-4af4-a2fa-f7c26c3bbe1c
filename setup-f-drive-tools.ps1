# Simple F: Drive Tools Environment Setup
Write-Host "🚀 Setting up F: Drive Development Tools" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

# Get current user PATH
$currentPath = [Environment]::GetEnvironmentVariable("PATH", [EnvironmentVariableTarget]::User)
$pathsAdded = @()

# 1. JAVA SETUP
Write-Host "`n☕ Setting up Java JDK 17..." -ForegroundColor Cyan
$javaPath = "F:\IraChat\java17\jdk-17.0.12+7"
if (Test-Path $javaPath) {
    [Environment]::SetEnvironmentVariable("JAVA_HOME", $javaPath, [EnvironmentVariableTarget]::User)
    Write-Host "✅ Set JAVA_HOME = $javaPath" -ForegroundColor Green
    
    $javaBinPath = "$javaPath\bin"
    if (Test-Path $javaBinPath) {
        if ($currentPath -notlike "*$javaBinPath*") {
            $currentPath = "$currentPath;$javaBinPath"
            $pathsAdded += "Java JDK 17 ($javaBinPath)"
            Write-Host "✅ Added Java to PATH" -ForegroundColor Green
        } else {
            Write-Host "✓ Java already in PATH" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "❌ Java 17 not found at: $javaPath" -ForegroundColor Red
}

# 2. ANDROID SDK SETUP
Write-Host "`n🤖 Setting up Android SDK..." -ForegroundColor Cyan
$possibleSdkPaths = @(
    "F:\Android\Sdk",
    "F:\android\sdk",
    "F:\AndroidSDK",
    "F:\android-sdk",
    "F:\SDK\Android",
    "F:\Tools\Android\Sdk",
    "F:\Development\Android\Sdk"
)

$sdkPath = $null
foreach ($path in $possibleSdkPaths) {
    if (Test-Path $path) {
        $sdkPath = $path
        Write-Host "✅ Found Android SDK at: $sdkPath" -ForegroundColor Green
        break
    }
}

if ($sdkPath) {
    [Environment]::SetEnvironmentVariable("ANDROID_HOME", $sdkPath, [EnvironmentVariableTarget]::User)
    [Environment]::SetEnvironmentVariable("ANDROID_SDK_ROOT", $sdkPath, [EnvironmentVariableTarget]::User)
    Write-Host "✅ Set ANDROID_HOME = $sdkPath" -ForegroundColor Green
    
    # Add Android tools to PATH
    $androidPaths = @(
        "$sdkPath\platform-tools",
        "$sdkPath\cmdline-tools\latest\bin",
        "$sdkPath\tools",
        "$sdkPath\tools\bin",
        "$sdkPath\emulator"
    )
    
    foreach ($androidPath in $androidPaths) {
        if (Test-Path $androidPath) {
            if ($currentPath -notlike "*$androidPath*") {
                $currentPath = "$currentPath;$androidPath"
                $pathsAdded += "Android Tools ($androidPath)"
                Write-Host "✅ Added Android tool to PATH: $androidPath" -ForegroundColor Green
            }
        }
    }
} else {
    Write-Host "❌ Android SDK not found on F: drive" -ForegroundColor Red
}

# 3. NODE.JS SETUP
Write-Host "`n📦 Checking Node.js..." -ForegroundColor Cyan
$possibleNodePaths = @(
    "F:\nodejs",
    "F:\Node",
    "F:\Tools\nodejs",
    "F:\Development\nodejs",
    "F:\node"
)

$nodePath = $null
foreach ($path in $possibleNodePaths) {
    if (Test-Path "$path\node.exe") {
        $nodePath = $path
        Write-Host "✅ Found Node.js at: $nodePath" -ForegroundColor Green
        if ($currentPath -notlike "*$nodePath*") {
            $currentPath = "$currentPath;$nodePath"
            $pathsAdded += "Node.js ($nodePath)"
        }
        break
    }
}

if (-not $nodePath) {
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Host "✅ Node.js already available: $nodeVersion" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ Node.js not found. Please install Node.js." -ForegroundColor Red
    }
}

# 4. ADDITIONAL TOOLS
Write-Host "`n🛠️  Setting up additional tools..." -ForegroundColor Cyan
$possibleToolPaths = @(
    "F:\Tools",
    "F:\Tools\bin",
    "F:\Development\Tools",
    "F:\SDK",
    "F:\bin"
)

foreach ($toolPath in $possibleToolPaths) {
    if (Test-Path $toolPath) {
        if ($currentPath -notlike "*$toolPath*") {
            $currentPath = "$currentPath;$toolPath"
            $pathsAdded += "Tools ($toolPath)"
            Write-Host "✅ Added tools directory: $toolPath" -ForegroundColor Green
        }
    }
}

# Update PATH if changes were made
if ($pathsAdded.Count -gt 0) {
    [Environment]::SetEnvironmentVariable("PATH", $currentPath, [EnvironmentVariableTarget]::User)
    Write-Host "`n✅ Updated user PATH with $($pathsAdded.Count) new entries" -ForegroundColor Green
} else {
    Write-Host "`n✓ No PATH updates needed" -ForegroundColor Gray
}

# VERIFICATION
Write-Host "`n🔍 Verifying setup..." -ForegroundColor Cyan

# Test Java
try {
    $javaVersion = java -version 2>&1 | Select-Object -First 1
    Write-Host "✅ Java: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java not working" -ForegroundColor Red
}

# Test Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not working" -ForegroundColor Red
}

# Test npm
try {
    $npmVersion = npm --version
    Write-Host "✅ npm: v$npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not working" -ForegroundColor Red
}

Write-Host "`n🎉 Setup completed!" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

Write-Host "`n📋 Next Steps:" -ForegroundColor Yellow
Write-Host "1. Close this terminal and open a new one" -ForegroundColor White
Write-Host "2. Navigate to project: cd F:\IraChat" -ForegroundColor White
Write-Host "3. Run: npm install" -ForegroundColor White
Write-Host "4. Run: npx expo start" -ForegroundColor White

Write-Host "`n⚠️  RESTART YOUR TERMINAL for changes to take effect!" -ForegroundColor Red

Read-Host "`nPress Enter to exit"
